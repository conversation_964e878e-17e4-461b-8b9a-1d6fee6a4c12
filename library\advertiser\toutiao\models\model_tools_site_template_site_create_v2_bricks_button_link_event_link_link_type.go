/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType
type ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType string

// List of tools_site_template_site_create_v2_bricks_button_link_event_link_link_type
const (
	QUICK_APP_ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType = "QUICK_APP"
	SCHEME_ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType    ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType = "SCHEME"
	URL_ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType       ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType = "URL"
)

// Ptr returns reference to tools_site_template_site_create_v2_bricks_button_link_event_link_link_type value
func (v ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType) Ptr() *ToolsSiteTemplateSiteCreateV2BricksButtonLinkEventLinkLinkType {
	return &v
}
