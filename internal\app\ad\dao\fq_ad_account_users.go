// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-16 11:16:21
// 生成路径: internal/app/ad/dao/fq_ad_account_users.go
// 生成人：gfast
// desc:番茄账号权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdAccountUsersDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdAccountUsersDao struct {
	*internal.FqAdAccountUsersDao
}

var (
	// FqAdAccountUsers is globally public accessible object for table tools_gen_table operations.
	FqAdAccountUsers = fqAdAccountUsersDao{
		internal.NewFqAdAccountUsersDao(),
	}
)

// Fill with you ideas below.
