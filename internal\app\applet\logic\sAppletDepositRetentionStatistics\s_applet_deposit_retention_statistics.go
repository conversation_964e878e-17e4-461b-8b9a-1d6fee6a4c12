// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-08-28 15:16:41
// 生成路径: internal/app/applet/logic/s_applet_deposit_retention_statistics.go
// 生成人：cyao
// desc:小程序维度用户/充值留存数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	memberDao "github.com/tiger1103/gfast/v3/internal/app/member/dao"
	orderService "github.com/tiger1103/gfast/v3/internal/app/order/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
)

func init() {
	service.RegisterSAppletDepositRetentionStatistics(New())
}

func New() service.ISAppletDepositRetentionStatistics {
	return &sSAppletDepositRetentionStatistics{}
}

type sSAppletDepositRetentionStatistics struct{}

func (s *sSAppletDepositRetentionStatistics) List(ctx context.Context, req *model.SAppletDepositRetentionStatisticsSearchReq) (listRes *model.SAppletDepositRetentionStatisticsSearchRes, err error) {
	listRes = new(model.SAppletDepositRetentionStatisticsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SAppletDepositRetentionStatistics.Ctx(ctx).WithAll()
		if req.AppId != "" {
			m = m.Where(dao.SAppletDepositRetentionStatistics.Columns().AppId, req.AppId)
		} else if len(req.AppIds) > 0 {
			m = m.WhereIn(dao.SAppletDepositRetentionStatistics.Columns().AppId, req.AppIds)
		}
		if req.StartTime != "" {
			m = m.Where(dao.SAppletDepositRetentionStatistics.Columns().CreateTime+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where(dao.SAppletDepositRetentionStatistics.Columns().CreateTime+" <= ?", req.EndTime)
		}
		if req.AppType != "" {
			m = m.Where(dao.SAppletDepositRetentionStatistics.Columns().AppType+" = ?", req.AppType)
		}
		if req.Merge == 0 {
			m = m.Group(dao.SAppletDepositRetentionStatistics.Columns().CreateTime)
		}
		if req.AppletOption == 1 {
			m = m.Group(dao.SAppletDepositRetentionStatistics.Columns().AppId)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "create_time desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SAppletDepositRetentionStatisticsListRes
		m = m.FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NewUserNums, "newUserNums").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NewUserAmount, "newUserAmount").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().ActiveUserNums, "activeUserNums").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().RechargeNums, "rechargeNums").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NewUserRechargeNums, "newUserRechargeNums").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().TotalAmount, "totalAmount").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().UserRetention, "userRetention").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NextDayPayment, "nextDayPayment").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().UserRetention3Day, "userRetention3Day").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NextDayPayment3Day, "nextDayPayment3Day").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().UserRetention7Day, "userRetention7Day").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NextDayPayment7Day, "nextDayPayment7Day").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().UserRetention30Day, "userRetention30Day").
			FieldSum(dao.SAppletDepositRetentionStatistics.Columns().NextDayPayment30Day, "nextDayPayment30Day")
		if req.Merge == 1 {
			if req.AppletOption == 1 {
				order = "totalAmount desc"
				m = m.Fields("any_value(app_id) as appId ,any_value(app_name) as appName, any_value(app_type) as appType ").
					FieldMax(dao.SAppletDepositRetentionStatistics.Columns().TotalNumberUsers, "totalNumberUsers")
			} else {
				//m = m.FieldSum(dao.SAppletDepositRetentionStatistics.Columns().TotalNumberUsers, "totalNumberUsers")
			}
		} else {
			if req.AppletOption == 1 {
				m = m.Fields("any_value(app_id) as appId ,any_value(app_name) as appName ,any_value(app_type) as appType, any_value(create_time) as createTime").
					FieldMax(dao.SAppletDepositRetentionStatistics.Columns().TotalNumberUsers, "totalNumberUsers")
			} else {
				m = m.Fields("create_time as createTime")
				//FieldSum(dao.SAppletDepositRetentionStatistics.Columns().TotalNumberUsers, "totalNumberUsers")
			}
		}
		var totalNumberUsers []model.TotalNumberUsers
		if req.AppletOption != 1 {
			if req.StartTime == "" && req.EndTime == "" {
				_, end, _ := libUtils.GetDayStartEnd(libUtils.GetNowDate())
				err = memberDao.LoginAccountInfoAnalytic.Ctx(ctx).
					Where(memberDao.LoginAccountInfo.Columns().CreateTime+" <= ?", end).
					Fields("count(*) as totalNumberUsers").Scan(&totalNumberUsers)
			} else {
				//平台用户
				_, end, _ := libUtils.GetDayStartEnd(req.EndTime)
				err = memberDao.LoginAccountInfoAnalytic.Ctx(ctx).
					Where(memberDao.LoginAccountInfo.Columns().CreateTime+" <= ?", end).
					Fields("count(*) as totalNumberUsers").Scan(&totalNumberUsers)
			}
		}

		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		for k, stat := range res {
			//用户增长率：新增用户/平台用户*100%
			if req.AppletOption != 1 && len(totalNumberUsers) > 0 {
				stat.TotalNumberUsers = totalNumberUsers[0].TotalNumberUsers
			}
			if stat.TotalNumberUsers > 0 {
				res[k].UserGrowthRate = libUtils.DivideAndRound(float64(stat.NewUserNums)*100, float64(stat.TotalNumberUsers), 2, libUtils.RoundHalfEven)
			}

			//当天付费率：付费人数/活跃人数*100%
			if stat.ActiveUserNums > 0 {
				res[k].SameDayRate = libUtils.DivideAndRound(float64(stat.RechargeNums)*100, float64(stat.ActiveUserNums), 2, libUtils.RoundHalfEven)
			}
			//新增付费率：新增付费人数/新增用户*100%
			if stat.NewUserNums > 0 {
				res[k].IncrementalFeeRate = libUtils.DivideAndRound(float64(stat.NewUserRechargeNums)*100, float64(stat.NewUserNums), 2, libUtils.RoundHalfEven)
				res[k].RetentionRate = libUtils.DivideAndRound(stat.UserRetention*100, float64(stat.NewUserNums), 2, libUtils.RoundHalfEven)
				res[k].RetentionRate3Day = libUtils.DivideAndRound(stat.UserRetention3Day*100, float64(stat.NewUserNums), 2, libUtils.RoundHalfEven)
				res[k].RetentionRate7Day = libUtils.DivideAndRound(stat.UserRetention7Day*100, float64(stat.NewUserNums), 2, libUtils.RoundHalfEven)
				res[k].RetentionRate30Day = libUtils.DivideAndRound(stat.UserRetention30Day*100, float64(stat.NewUserNums), 2, libUtils.RoundHalfEven)
			}

		}
		listRes.List = res
	})
	return
}

func (s *sSAppletDepositRetentionStatistics) GetById(ctx context.Context, id int64) (res *model.SAppletDepositRetentionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SAppletDepositRetentionStatistics.Ctx(ctx).WithAll().Where(dao.SAppletDepositRetentionStatistics.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSAppletDepositRetentionStatistics) Add(ctx context.Context, req *model.SAppletDepositRetentionStatisticsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SAppletDepositRetentionStatistics.Ctx(ctx).Insert(do.SAppletDepositRetentionStatistics{
			CreateTime:          req.CreateTime,
			AppId:               req.AppId,
			AppName:             req.AppName,
			TotalNumberUsers:    req.TotalNumberUsers,
			NewUserNums:         req.NewUserNums,
			UserGrowthRate:      req.UserGrowthRate,
			ActiveUserNums:      req.ActiveUserNums,
			RechargeNums:        req.RechargeNums,
			NewUserRechargeNums: req.NewUserRechargeNums,
			TotalAmount:         req.TotalAmount,
			NewUserAmount:       req.NewUserAmount,
			SameDayRate:         req.SameDayRate,
			IncrementalFeeRate:  req.IncrementalFeeRate,
			UserRetention:       req.UserRetention,
			RetentionRate:       req.RetentionRate,
			NextDayPayment:      req.NextDayPayment,
			UserRetention3Day:   req.UserRetention3Day,
			RetentionRate3Day:   req.RetentionRate3Day,
			NextDayPayment3Day:  req.NextDayPayment3Day,
			UserRetention7Day:   req.UserRetention7Day,
			RetentionRate7Day:   req.RetentionRate7Day,
			NextDayPayment7Day:  req.NextDayPayment7Day,
			UserRetention30Day:  req.UserRetention30Day,
			RetentionRate30Day:  req.RetentionRate30Day,
			NextDayPayment30Day: req.NextDayPayment30Day,
			AppType:             req.AppType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSAppletDepositRetentionStatistics) Edit(ctx context.Context, req *model.SAppletDepositRetentionStatisticsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SAppletDepositRetentionStatistics.Ctx(ctx).WherePri(req.Id).Update(do.SAppletDepositRetentionStatistics{
			CreateTime:          req.CreateTime,
			AppId:               req.AppId,
			AppName:             req.AppName,
			TotalNumberUsers:    req.TotalNumberUsers,
			NewUserNums:         req.NewUserNums,
			UserGrowthRate:      req.UserGrowthRate,
			ActiveUserNums:      req.ActiveUserNums,
			RechargeNums:        req.RechargeNums,
			NewUserRechargeNums: req.NewUserRechargeNums,
			TotalAmount:         req.TotalAmount,
			NewUserAmount:       req.NewUserAmount,
			SameDayRate:         req.SameDayRate,
			IncrementalFeeRate:  req.IncrementalFeeRate,
			UserRetention:       req.UserRetention,
			RetentionRate:       req.RetentionRate,
			NextDayPayment:      req.NextDayPayment,
			UserRetention3Day:   req.UserRetention3Day,
			RetentionRate3Day:   req.RetentionRate3Day,
			NextDayPayment3Day:  req.NextDayPayment3Day,
			UserRetention7Day:   req.UserRetention7Day,
			RetentionRate7Day:   req.RetentionRate7Day,
			NextDayPayment7Day:  req.NextDayPayment7Day,
			UserRetention30Day:  req.UserRetention30Day,
			RetentionRate30Day:  req.RetentionRate30Day,
			NextDayPayment30Day: req.NextDayPayment30Day,
			AppType:             req.AppType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSAppletDepositRetentionStatistics) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SAppletDepositRetentionStatistics.Ctx(ctx).Delete(dao.SAppletDepositRetentionStatistics.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sSAppletDepositRetentionStatistics) Statistics(ctx context.Context, statDate, startTime string, beforeDay bool) (err error) {
	statDate = strings.ReplaceAll(statDate, "'", "")
	if beforeDay { // 刷当天之前的所有数据 （只做插入操作不做update操作）适用于init数据
		if libUtils.CompareDateString(statDate, "2023-01-01") >= 0 {
			for startDate := startTime; startDate != libUtils.StringTimeAddDay(statDate, 1); startDate = libUtils.StringTimeAddDay(startDate, 1) {
				err = orderService.OrderTask().CreateAppletDepositRetentionStat(ctx, startDate)
			}
			return err
		}
		return errors.New("请输入2023-01-01 之后的时间进行操作")
	} else { // 只刷当天的数据（如果数据存在的话会更新而非走插入操作）
		err = orderService.OrderTask().CreateAppletDepositRetentionStat(ctx, statDate)
		return err
	}
}
