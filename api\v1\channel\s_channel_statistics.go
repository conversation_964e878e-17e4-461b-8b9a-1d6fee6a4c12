// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-02-23 11:44:07
// 生成路径: api/v1/channel/s_channel_statistics.go
// 生成人：lx
// desc:渠道统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelStatisticsSearchReq 分页请求参数
type SChannelStatisticsSearchReq struct {
	g.Meta `path:"/channelDetail" tags:"渠道统计" method:"post" summary:"渠道明细"`
	commonApi.Author
	model.SChannelStatisticsSearchReq
}

// SChannelStatisticsSearchRes 列表返回结果
type SChannelStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelStatisticsSearchRes
}

// SChannelStatisticsAddReq 添加操作请求参数
type SChannelStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"渠道统计" method:"post" summary:"渠道统计添加"`
	commonApi.Author
	*model.SChannelStatisticsAddReq
}

// SChannelStatisticsAddRes 添加操作返回结果
type SChannelStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SChannelStatisticsEditReq 修改操作请求参数
type SChannelStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"渠道统计" method:"put" summary:"渠道统计修改"`
	commonApi.Author
	*model.SChannelStatisticsEditReq
}

// SChannelStatisticsEditRes 修改操作返回结果
type SChannelStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SChannelStatisticsGetReq 获取一条数据请求
type SChannelStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"渠道统计" method:"get" summary:"获取渠道统计信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelStatisticsGetRes 获取一条数据结果
type SChannelStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelStatisticsInfoRes
}

// SChannelStatisticsDeleteReq 删除数据请求
type SChannelStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道统计" method:"post" summary:"删除渠道统计"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelStatisticsDeleteRes 删除数据返回
type SChannelStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
