/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30Request struct for YuntuAudienceLabelCreateV30Request
type YuntuAudienceLabelCreateV30Request struct {
	CalculateRule YuntuAudienceLabelCreateV30RequestCalculateRule `json:"calculate_rule"`
	DataSource    YuntuAudienceLabelCreateV30DataSource           `json:"data_source"`
	// 标签描述。非必填，若选择传入，则字符数上限为100。
	Description *string `json:"description,omitempty"`
	// 行业ID，需传入当前用户在当前品牌下拥有权限的、数值最小的行业ID。行业信息及行业ID可通过“品牌元信息”接口获取。
	IndustryId int64 `json:"industry_id"`
	// 标签名称，需要创建的人群标签名称。标签名称不能为纯数字，字符长度上限为20，名称前后不能存在空格。
	Name string `json:"name"`
	// 服务商id
	ServiceProviderId int64 `json:"service_provider_id"`
	// 当前用户名称，可通过“品牌元信息”接口获取。
	UserDisplayName string                              `json:"user_display_name"`
	ValidDay        YuntuAudienceLabelCreateV30ValidDay `json:"valid_day"`
	// 品牌id
	YuntuBrandId int64 `json:"yuntu_brand_id"`
}
