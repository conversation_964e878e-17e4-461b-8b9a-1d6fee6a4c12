# 点众原生链接爬取定时任务

## 功能概述

该定时任务用于自动调用 `DzNativeLinkCrawl` 方法进行点众原生链接的数据爬取，支持智能调度，根据不同时间段采用不同的执行频率。

## 调度规则

### 高频时间段（每5分钟执行一次）
- **10:00-10:30**
- **14:00-14:30** 
- **16:00-16:30**

### 低频时间段（每30分钟执行一次）
- **其他所有时间**

## 技术实现

### 1. 任务函数
- **文件位置**: `task/task.go`
- **函数名**: `DzNativeLinkCrawlTask`
- **调度函数**: `shouldExecuteDzNativeLinkCrawl`

### 2. 任务注册
- **文件位置**: `task/bind_function.go`
- **任务名**: `dzNativeLinkCrawlTask`

### 3. 数据库配置
- **表名**: `sys_job`
- **任务标识**: `DzNativeLinkCrawlTask`
- **Cron表达式**: `0 * * * * ?` (每分钟执行一次)
- **执行策略**: 通过代码逻辑控制实际执行频率

## 部署步骤

### 1. 代码部署
代码已经添加到项目中，包含：
- 任务执行函数
- 智能调度逻辑
- 任务注册

### 2. 数据库配置
执行SQL脚本添加定时任务配置：
```sql
-- 执行 sql/add_dz_native_link_crawl_job.sql
```

### 3. 启动任务
1. 重启应用程序
2. 在管理后台的定时任务管理中确认任务状态为"启用"
3. 可以手动执行一次测试任务是否正常

## 监控和日志

### 执行日志
- 所有执行记录都会写入 `sys_job_log` 表
- 包含执行时间、执行结果、错误信息等

### 日志类型
1. **正常执行**: 显示执行成功和执行时间
2. **跳过执行**: 显示跳过原因和当前时间
3. **执行失败**: 显示详细错误信息

### 示例日志
```
DzNativeLinkCrawlTask执行成功，执行时间：10:05:00
DzNativeLinkCrawlTask跳过执行，当前时间：10:03:00
DzNativeLinkCrawlTask失败，Err：网络连接超时
```

## 调度逻辑说明

### 高频时间段判断
```go
isHighFrequencyPeriod := (hour == 10 && minute <= 30) ||
    (hour == 14 && minute <= 30) ||
    (hour == 16 && minute <= 30)
```

### 执行频率控制
- **高频时段**: `minute % 5 == 0` (每5分钟)
- **低频时段**: `minute == 0 || minute == 30` (每30分钟)

## 注意事项

1. **时间精度**: 任务基于分钟级别的时间判断，秒级误差可忽略
2. **时区**: 使用服务器本地时间
3. **重启影响**: 应用重启后任务会自动恢复
4. **性能考虑**: 每分钟的检查开销很小，不会影响系统性能

## 故障排查

### 常见问题
1. **任务未执行**: 检查数据库中任务状态是否为启用(status=0)
2. **执行频率异常**: 检查服务器时间是否正确
3. **爬取失败**: 查看具体错误日志，可能是网络或认证问题

### 调试方法
1. 查看 `sys_job_log` 表的最新记录
2. 检查应用程序日志
3. 手动执行任务测试功能是否正常

## 维护建议

1. **定期检查**: 每周检查任务执行日志，确保正常运行
2. **性能监控**: 关注爬取任务的执行时间，避免超时
3. **数据验证**: 定期验证爬取的数据完整性和准确性
