// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-08-28 15:47:10
// 生成路径: internal/app/applet/dao/internal/product_detail.go
// 生成人：cq
// desc:商品详情表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ProductDetailDao is the manager for logic model data accessing and custom defined data operations functions management.
type ProductDetailDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns ProductDetailColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// ProductDetailColumns defines and stores column names for table product_detail.
type ProductDetailColumns struct {
	Id            string //
	AppId         string // 小程序ID，多个用,隔开
	ImgUrl        string // 封面
	Title         string // 商品标题
	Detail        string // 商品详情
	Price         string // 商品价格
	OriginalPrice string // 商品原价
	Type          string // 商品类型：1：乘车券
	Specification string // 商品规格
	GiftType      string // 赠品类型：1：无赠品 2：金币 3：VIP 4：观影券
	GiftNums      string // 赠品数量
	VipType       string // VIP类型 1：月卡  2：季卡  3：年卡  4：半年卡  5：3天卡  6：1天卡  7：剧vip  8：周卡
	Sort          string // 排序
	Status        string // 状态 0：下架 1：上架
	Remark        string // 备注
	CreateTime    string // 创建时间
	UpdateTime    string // 更新时间
	Deleted       string // 是否删除 0：否 1：是
}

var productDetailColumns = ProductDetailColumns{
	Id:            "id",
	AppId:         "app_id",
	ImgUrl:        "img_url",
	Title:         "title",
	Detail:        "detail",
	Price:         "price",
	OriginalPrice: "original_price",
	Type:          "type",
	Specification: "specification",
	GiftType:      "gift_type",
	GiftNums:      "gift_nums",
	VipType:       "vip_type",
	Sort:          "sort",
	Status:        "status",
	Remark:        "remark",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	Deleted:       "deleted",
}

// NewProductDetailDao creates and returns a new DAO object for table data access.
func NewProductDetailDao() *ProductDetailDao {
	return &ProductDetailDao{
		group:   "default",
		table:   "product_detail",
		columns: productDetailColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ProductDetailDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ProductDetailDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ProductDetailDao) Columns() ProductDetailColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ProductDetailDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ProductDetailDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *ProductDetailDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
