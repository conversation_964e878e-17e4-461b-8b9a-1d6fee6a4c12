/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameListV30DataListAgeLimit
type ToolsWechatGameListV30DataListAgeLimit string

// List of tools_wechat_game_list_v3.0_data_list_age_limit
const (
	EIGHTEEN_PLUS_ToolsWechatGameListV30DataListAgeLimit ToolsWechatGameListV30DataListAgeLimit = "EIGHTEEN_PLUS"
	EIGHT_PLUS_ToolsWechatGameListV30DataListAgeLimit    ToolsWechatGameListV30DataListAgeLimit = "EIGHT_PLUS"
	FOUR_PLUS_ToolsWechatGameListV30DataListAgeLimit     ToolsWechatGameListV30DataListAgeLimit = "FOUR_PLUS"
	SIXTEEN_PLUS_ToolsWechatGameListV30DataListAgeLimit  ToolsWechatGameListV30DataListAgeLimit = "SIXTEEN_PLUS"
	TWELVE_PLUS_ToolsWechatGameListV30DataListAgeLimit   ToolsWechatGameListV30DataListAgeLimit = "TWELVE_PLUS"
)

// Ptr returns reference to tools_wechat_game_list_v3.0_data_list_age_limit value
func (v ToolsWechatGameListV30DataListAgeLimit) Ptr() *ToolsWechatGameListV30DataListAgeLimit {
	return &v
}
