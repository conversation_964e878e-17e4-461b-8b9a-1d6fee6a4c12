/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletUpdateV30MaxPaymentTierRange
type ToolsWechatAppletUpdateV30MaxPaymentTierRange string

// List of tools_wechat_applet_update_v3.0_max_payment_tier_range
const (
	ABOVE_1000_ToolsWechatAppletUpdateV30MaxPaymentTierRange       ToolsWechatAppletUpdateV30MaxPaymentTierRange = "ABOVE_1000"
	BELOW_500_ToolsWechatAppletUpdateV30MaxPaymentTierRange        ToolsWechatAppletUpdateV30MaxPaymentTierRange = "BELOW_500"
	FROM_500_TO_1000_ToolsWechatAppletUpdateV30MaxPaymentTierRange ToolsWechatAppletUpdateV30MaxPaymentTierRange = "FROM_500_TO_1000"
)

// Ptr returns reference to tools_wechat_applet_update_v3.0_max_payment_tier_range value
func (v ToolsWechatAppletUpdateV30MaxPaymentTierRange) Ptr() *ToolsWechatAppletUpdateV30MaxPaymentTierRange {
	return &v
}
