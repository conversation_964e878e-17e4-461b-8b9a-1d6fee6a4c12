// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: api/v1/ad/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdEcpmSearchReq 分页请求参数
type DzAdEcpmSearchReq struct {
	g.Meta `path:"/list" tags:"广告ECPM信息表" method:"get" summary:"广告ECPM信息表列表"`
	commonApi.Author
	model.DzAdEcpmSearchReq
}

// DzAdEcpmSearchRes 列表返回结果
type DzAdEcpmSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdEcpmSearchRes
}

// DzAdEcpmExportReq 导出请求
type DzAdEcpmExportReq struct {
	g.Meta `path:"/export" tags:"广告ECPM信息表" method:"get" summary:"广告ECPM信息表导出"`
	commonApi.Author
	model.DzAdEcpmSearchReq
}

// DzAdEcpmExportRes 导出响应
type DzAdEcpmExportRes struct {
	commonApi.EmptyRes
}

// DzAdEcpmAddReq 添加操作请求参数
type DzAdEcpmAddReq struct {
	g.Meta `path:"/add" tags:"广告ECPM信息表" method:"post" summary:"广告ECPM信息表添加"`
	commonApi.Author
	*model.DzAdEcpmAddReq
}

// DzAdEcpmAddRes 添加操作返回结果
type DzAdEcpmAddRes struct {
	commonApi.EmptyRes
}

// DzAdEcpmEditReq 修改操作请求参数
type DzAdEcpmEditReq struct {
	g.Meta `path:"/edit" tags:"广告ECPM信息表" method:"put" summary:"广告ECPM信息表修改"`
	commonApi.Author
	*model.DzAdEcpmEditReq
}

// DzAdEcpmEditRes 修改操作返回结果
type DzAdEcpmEditRes struct {
	commonApi.EmptyRes
}

// DzAdEcpmGetReq 获取一条数据请求
type DzAdEcpmGetReq struct {
	g.Meta `path:"/get" tags:"广告ECPM信息表" method:"get" summary:"获取广告ECPM信息表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// DzAdEcpmGetRes 获取一条数据结果
type DzAdEcpmGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdEcpmInfoRes
}

// DzAdEcpmDeleteReq 删除数据请求
type DzAdEcpmDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告ECPM信息表" method:"delete" summary:"删除广告ECPM信息表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// DzAdEcpmDeleteRes 删除数据返回
type DzAdEcpmDeleteRes struct {
	commonApi.EmptyRes
}
