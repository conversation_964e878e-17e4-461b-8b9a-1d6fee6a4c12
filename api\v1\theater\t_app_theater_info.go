// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-12 10:01:00
// 生成路径: api/v1/theater/t_app_theater_info.go
// 生成人：cq
// desc:短剧10+1表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TAppTheaterInfoSearchReq 分页请求参数
type TAppTheaterInfoSearchReq struct {
	g.Meta `path:"/list" tags:"短剧10+1表信息" method:"get" summary:"获取短剧10+1列表"`
	commonApi.Author
	model.TAppTheaterInfoSearchReq
}

// TAppTheaterInfoSearchRes 列表返回结果
type TAppTheaterInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TAppTheaterInfoSearchRes
}

// TAppTheaterInfoAddReq 添加操作请求参数
type TAppTheaterInfoAddReq struct {
	g.Meta `path:"/add" tags:"短剧10+1表信息" method:"post" summary:"添加短剧10+1"`
	commonApi.Author
	*model.TAppTheaterInfoAddReq
}

// TAppTheaterInfoAddRes 添加操作返回结果
type TAppTheaterInfoAddRes struct {
	commonApi.EmptyRes
}

// TAppTheaterInfoEditReq 修改操作请求参数
type TAppTheaterInfoEditReq struct {
	g.Meta `path:"/edit" tags:"短剧10+1表信息" method:"put" summary:"短剧10+1表信息修改"`
	commonApi.Author
	*model.TAppTheaterInfoEditReq
}

// TAppTheaterInfoEditRes 修改操作返回结果
type TAppTheaterInfoEditRes struct {
	commonApi.EmptyRes
}

// TAppTheaterInfoGetReq 获取一条数据请求
type TAppTheaterInfoGetReq struct {
	g.Meta `path:"/get" tags:"短剧10+1表信息" method:"get" summary:"获取短剧10+1表信息信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// TAppTheaterInfoGetRes 获取一条数据结果
type TAppTheaterInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TAppTheaterInfoInfoRes
}

// TAppTheaterInfoDeleteReq 删除数据请求
type TAppTheaterInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧10+1表信息" method:"post" summary:"删除短剧10+1表信息"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// TAppTheaterInfoDeleteRes 删除数据返回
type TAppTheaterInfoDeleteRes struct {
	commonApi.EmptyRes
}
