// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: internal/app/ad/dao/internal/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdXtTaskSettleDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdXtTaskSettleDao struct {
	table   string                // Table is the underlying table name of the DAO.
	group   string                // Group is the database configuration group name of current DAO.
	columns AdXtTaskSettleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdXtTaskSettleColumns defines and stores column names for table ad_xt_task_settle.
type AdXtTaskSettleColumns struct {
	Id                   string // ID
	AdvertiserId         string // 账户ID 对应 star_id
	TaskId               string // 任务ID
	ItemId               string // 视频唯一ID
	AuthorId             string // 作者ID
	Uid                  string // 用户平台ID
	ProviderId           string // 内容提供方ID
	Title                string // 视频标题
	Link                 string // 视频分享链接
	AuthorNickname       string // 作者昵称
	ReleaseTime          string // 发布时间(unix时间戳)
	AndroidActivateCnt   string // 安卓端激活数
	IosActivateCnt       string // iOS端激活数
	CommentCnt           string // 评论数
	LikeCnt              string // 点赞数
	ValidLikeCnt         string // 有效点赞数
	PlayVv               string // 播放次数
	ValidPlayVv          string // 有效播放次数
	ShareCnt             string // 分享次数
	PromoteCnt           string // 推广次数
	ComponentClickCnt    string // 组件点击次数
	EstAdCost            string // 预估广告收入(JSON格式)
	EstSales             string // 预估销售额(JSON格式)
	Play                 string // 播放数据(JSON格式)
	SettleAdShare        string // 广告分成结算(JSON格式)
	SettleCps            string // CPS结算(JSON格式)
	IaaCostHour          string // 小时级IAA消耗
	IapCostHour          string // 小时级IAP消耗
	RewardAmount         string // 打赏金额
	RelevanceAuditResult string // 相关性审核结果
	RewardLevel          string // 打赏等级
	ItemInfoDailyList    string // 每日数据明细(JSON数组)
}

var adXtTaskSettleColumns = AdXtTaskSettleColumns{
	Id:                   "id",
	AdvertiserId:         "advertiser_id",
	TaskId:               "task_id",
	ItemId:               "item_id",
	AuthorId:             "author_id",
	Uid:                  "uid",
	ProviderId:           "provider_id",
	Title:                "title",
	Link:                 "link",
	AuthorNickname:       "author_nickname",
	ReleaseTime:          "release_time",
	AndroidActivateCnt:   "android_activate_cnt",
	IosActivateCnt:       "ios_activate_cnt",
	CommentCnt:           "comment_cnt",
	LikeCnt:              "like_cnt",
	ValidLikeCnt:         "valid_like_cnt",
	PlayVv:               "play_vv",
	ValidPlayVv:          "valid_play_vv",
	ShareCnt:             "share_cnt",
	PromoteCnt:           "promote_cnt",
	ComponentClickCnt:    "component_click_cnt",
	EstAdCost:            "est_ad_cost",
	EstSales:             "est_sales",
	Play:                 "play",
	SettleAdShare:        "settle_ad_share",
	SettleCps:            "settle_cps",
	IaaCostHour:          "iaa_cost_hour",
	IapCostHour:          "iap_cost_hour",
	RewardAmount:         "reward_amount",
	RelevanceAuditResult: "relevance_audit_result",
	RewardLevel:          "reward_level",
	ItemInfoDailyList:    "item_info_daily_list",
}

// NewAdXtTaskSettleDao creates and returns a new DAO object for table data access.
func NewAdXtTaskSettleDao() *AdXtTaskSettleDao {
	return &AdXtTaskSettleDao{
		group:   "default",
		table:   "ad_xt_task_settle",
		columns: adXtTaskSettleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdXtTaskSettleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdXtTaskSettleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdXtTaskSettleDao) Columns() AdXtTaskSettleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdXtTaskSettleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdXtTaskSettleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdXtTaskSettleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
