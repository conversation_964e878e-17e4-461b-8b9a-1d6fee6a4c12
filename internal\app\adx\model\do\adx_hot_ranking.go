// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/model/entity/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxHotRanking is the golang structure for table adx_hot_ranking.
type AdxHotRanking struct {
	gmeta.Meta `orm:"table:adx_hot_ranking, do:true"`
	PlayletId  interface{} `orm:"playlet_id,primary" json:"playletId"` // 短剧ID
	//business_date
	BusinessDate        interface{} `orm:"business_date" json:"businessDate"`
	PlayletName         interface{} `orm:"playlet_name" json:"playletName"`            // 短剧名称
	Ranking             interface{} `orm:"ranking" json:"ranking"`                     // 排名
	ConsumeNum          interface{} `orm:"consume_num" json:"consumeNum"`              // 当日热力值
	TotalConsumeNum     interface{} `orm:"total_consume_num" json:"totalConsumeNum"`   // 累计热力值
	NewFlag             interface{} `orm:"new_flag" json:"newFlag"`                    // 是否有new标识
	PlayletTags         interface{} `orm:"playlet_tags" json:"playletTags"`            // 短剧标签
	ContractorList      interface{} `orm:"contractor_list" json:"contractorList"`      // 承制方列表
	RelatedPartyList    interface{} `orm:"related_party_list" json:"relatedPartyList"` // 关联方列表
	CopyrightHolderList interface{} `orm:"copyright_holder_list" json:"copyrightHolderList" dc:"版权方列表"`
}
