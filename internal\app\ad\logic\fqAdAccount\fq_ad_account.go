// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/logic/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdAccount(New())
}

func New() service.IFqAdAccount {
	return &sFqAdAccount{}
}

type sFqAdAccount struct{}

func (s *sFqAdAccount) List(ctx context.Context, req *model.FqAdAccountSearchReq) (listRes *model.FqAdAccountSearchRes, err error) {
	listRes = new(model.FqAdAccountSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.FqAdAccount.Ctx(ctx).WithAll().As("a").InnerJoin(dao.FqAdAccountChannel.Table(), "b", " a.distributor_id = b.distributor_id")
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " b.channel_distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " b.channel_distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}
		if req.DistributorId != "" {
			m = m.Where("a."+dao.FqAdAccount.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.SecretKey != "" {
			m = m.Where("a."+dao.FqAdAccount.Columns().SecretKey+" = ?", req.SecretKey)
		}
		if req.AccountName != "" {
			m = m.Where("a."+dao.FqAdAccount.Columns().AccountName+" like ?", "%"+req.AccountName+"%")
		}
		if len(req.AccountNames) > 0 {
			m = m.WhereIn("a."+dao.FqAdAccount.Columns().AccountName, req.AccountNames)
		}
		if req.DramaType != "" {
			m = m.Where("a."+dao.FqAdAccount.Columns().DramaType+" = ?", req.DramaType)
		}
		if len(req.DramaTypes) > 0 {
			m = m.WhereIn("a."+dao.FqAdAccount.Columns().DramaType, req.DramaTypes)
		}
		if len(req.DateRange) != 0 {
			m = m.Where("a."+dao.FqAdAccount.Columns().CreatedAt+" >=? AND "+dao.FqAdAccount.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "a.distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.FqAdAccountListRes
		err = m.Fields("DISTINCT a.*").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdAccountListRes, len(res))
		disIds := make([]int64, 0)
		for k, v := range res {
			disIds = append(disIds, v.DistributorId)
			listRes.List[k] = &model.FqAdAccountListRes{
				DistributorId: v.DistributorId,
				SecretKey:     v.SecretKey,
				AccountName:   v.AccountName,
				DramaType:     v.DramaType,
				CreatedAt:     v.CreatedAt,
			}
		}
		uList, _ := service.FqAdAccountUsers().List(ctx, &model.FqAdAccountUsersSearchReq{
			DistributorIds: disIds,
		})
		if uList != nil && len(uList.List) > 0 {
			for _, usersListRes := range uList.List {
				for _, accountListRes := range listRes.List {
					if usersListRes.DistributorId == accountListRes.DistributorId {
						if accountListRes.UserList == nil {
							accountListRes.UserList = make([]int64, 0)
						}
						accountListRes.UserList = append(accountListRes.UserList, gconv.Int64(usersListRes.SpecifyUserId))
					}
				}
			}
		}
		deptList, _ := service.FqAdAccountDepts().List(ctx, &model.FqAdAccountDeptsSearchReq{
			DistributorIds: disIds,
		})
		if uList != nil && len(deptList.List) > 0 {
			for _, usersListRes := range deptList.List {
				for _, accountListRes := range listRes.List {
					if usersListRes.DistributorId == accountListRes.DistributorId {
						if accountListRes.DeptList == nil {
							accountListRes.DeptList = make([]int64, 0)
						}
						accountListRes.DeptList = append(accountListRes.DeptList, gconv.Int64(usersListRes.DetpId))
					}
				}
			}
		}
	})
	return
}

func (s *sFqAdAccount) List2(ctx context.Context) (listRes *model.FqAdAccountSearchRes, err error) {
	listRes = new(model.FqAdAccountSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.FqAdAccount.Ctx(ctx).WithAll().As("a").InnerJoin(dao.FqAdAccountChannel.Table(), "b", " a.distributor_id = b.distributor_id")
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " b.channel_distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " b.channel_distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
			//WhereOr("d.detp_id = ?", userInfo.DeptId).
			//WhereOr("u.specify_user_id = ?", userInfo.Id)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		var res []*model.FqAdAccountListRes
		err = m.Fields("DISTINCT a.distributor_id,a.secret_key,a.account_name,a.drama_type,a.created_at").Order("a.distributor_id asc").Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdAccountListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.FqAdAccountListRes{
				DistributorId: v.DistributorId,
				SecretKey:     v.SecretKey,
				AccountName:   v.AccountName,
				DramaType:     v.DramaType,
				CreatedAt:     v.CreatedAt,
			}
		}
	})
	return
}

func (s *sFqAdAccount) GetList(ctx context.Context, req *model.FqAdAccountSearchReq) (listRes []*model.FqAdAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccount.Ctx(ctx).WithAll().Page(req.PageNum, req.PageSize).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

// AddAuth
func (s *sFqAdAccount) AddAuth(ctx context.Context, req *model.FqAdAddAuthReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.DistributorIds) > 1 {
			for _, id := range req.DistributorIds {
				req.DistributorId = id
				//dao.FqAdAccountDepts.Ctx(ctx).Delete(dao.FqAdAccountDepts.Columns().DistributorId, req.DistributorId)
				//dao.FqAdAccountUsers.Ctx(ctx).Delete(dao.FqAdAccountUsers.Columns().DistributorId, req.DistributorId)
				if len(req.DeptIds) > 0 {
					list := make([]do.FqAdAccountDepts, 0)
					for _, id := range req.DeptIds {
						list = append(list, do.FqAdAccountDepts{
							DistributorId: req.DistributorId,
							DetpId:        id,
						})
					}
					dao.FqAdAccountDepts.Ctx(ctx).Save(list)
				}
				if len(req.SpecifyUserIds) > 0 {
					list := make([]do.FqAdAccountUsers, 0)
					for _, id := range req.SpecifyUserIds {
						list = append(list, do.FqAdAccountUsers{
							DistributorId: req.DistributorId,
							SpecifyUserId: id,
						})
					}
					dao.FqAdAccountUsers.Ctx(ctx).Save(list)
				}
			}
		} else {
			if len(req.DistributorIds) == 1 {
				req.DistributorId = req.DistributorIds[0]
			}
			dao.FqAdAccountDepts.Ctx(ctx).Delete(dao.FqAdAccountDepts.Columns().DistributorId, req.DistributorId)
			dao.FqAdAccountUsers.Ctx(ctx).Delete(dao.FqAdAccountUsers.Columns().DistributorId, req.DistributorId)
			if len(req.DeptIds) > 0 {
				list := make([]do.FqAdAccountDepts, 0)
				for _, id := range req.DeptIds {
					list = append(list, do.FqAdAccountDepts{
						DistributorId: req.DistributorId,
						DetpId:        id,
					})
				}
				dao.FqAdAccountDepts.Ctx(ctx).Save(list)
			}
			if len(req.SpecifyUserIds) > 0 {
				list := make([]do.FqAdAccountUsers, 0)
				for _, id := range req.SpecifyUserIds {
					list = append(list, do.FqAdAccountUsers{
						DistributorId: req.DistributorId,
						SpecifyUserId: id,
					})
				}
				dao.FqAdAccountUsers.Ctx(ctx).Save(list)
			}
		}
	})
	return
}

func (s *sFqAdAccount) GetExportData(ctx context.Context, req *model.FqAdAccountSearchReq) (listRes []*model.FqAdAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAccount.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdAccount.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.SecretKey != "" {
			m = m.Where(dao.FqAdAccount.Columns().SecretKey+" = ?", req.SecretKey)
		}
		if req.AccountName != "" {
			m = m.Where(dao.FqAdAccount.Columns().AccountName+" like ?", "%"+req.AccountName+"%")
		}
		if len(req.AccountNames) > 0 {
			m = m.WhereIn(dao.FqAdAccount.Columns().AccountName, req.AccountNames)
		}
		if req.DramaType != "" {
			m = m.Where(dao.FqAdAccount.Columns().DramaType+" = ?", req.DramaType)
		}
		if len(req.DramaTypes) > 0 {
			m = m.WhereIn(dao.FqAdAccount.Columns().DramaType, req.DramaTypes)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.FqAdAccount.Columns().CreatedAt+" >=? AND "+dao.FqAdAccount.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdAccount) GetByDistributorId(ctx context.Context, distributorId int64) (res *model.FqAdAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccount.Ctx(ctx).WithAll().Where(dao.FqAdAccount.Columns().DistributorId, distributorId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccount) GetByDistributorIds(ctx context.Context, distributorIds []string) (res []*model.FqAdAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(distributorIds) == 0 {
			return
		}
		err = dao.FqAdAccount.Ctx(ctx).WithAll().WhereIn(dao.FqAdAccount.Columns().DistributorId, distributorIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccount) Add(ctx context.Context, req *model.FqAdAccountAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccount.Ctx(ctx).Insert(do.FqAdAccount{
			DistributorId: req.DistributorId,
			SecretKey:     req.SecretKey,
			AccountName:   req.AccountName,
			DramaType:     req.DramaType,
		})
		// todo 拉取番茄渠道账号
		err = service.FqAdAccountChannel().SyncFqAdAccountChannel(ctx)
		if err != nil {
			liberr.ErrIsNil(ctx, err, err.Error())
		}
	})
	return
}

func (s *sFqAdAccount) Edit(ctx context.Context, req *model.FqAdAccountEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.BeforeDistributorId > 0 {
			_, err = dao.FqAdAccount.Ctx(ctx).WherePri(req.DistributorId).Update(do.FqAdAccount{
				DistributorId: req.DistributorId,
				SecretKey:     req.SecretKey,
				AccountName:   req.AccountName,
				DramaType:     req.DramaType,
			})
			liberr.ErrIsNil(ctx, err, "修改失败")
			_, err = dao.FqAdAccountChannel.Ctx(ctx).WhereIn(dao.FqAdAccountChannel.Columns().DistributorId, req.BeforeDistributorId).Update(do.FqAdAccountChannel{
				DistributorId: req.DistributorId,
			})
			return
		}
		_, err = dao.FqAdAccount.Ctx(ctx).WherePri(req.DistributorId).Update(do.FqAdAccount{
			SecretKey:   req.SecretKey,
			AccountName: req.AccountName,
			DramaType:   req.DramaType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdAccount) Delete(ctx context.Context, distributorIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccount.Ctx(ctx).Delete(dao.FqAdAccount.Columns().DistributorId+" in (?)", distributorIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
