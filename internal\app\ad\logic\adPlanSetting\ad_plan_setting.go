// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: internal/app/ad/logic/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/google/uuid"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"

	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	oceanModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	oceanengineModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	oceanService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/rabbitmqlib"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"github.com/tiger1103/gfast/v3/library/notify"
	"log"
	"path/filepath"
	"strings"
	"text/template"
	"time"
	"unicode/utf8"
)

func init() {
	service.RegisterAdPlanSetting(New())
}

func New() service.IAdPlanSetting {
	return &sAdPlanSetting{}
}

type sAdPlanSetting struct{}

func (s *sAdPlanSetting) List(ctx context.Context, req *model.AdPlanSettingSearchReq) (listRes *model.AdPlanSettingSearchRes, err error) {
	listRes = new(model.AdPlanSettingSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdPlanSetting.Ctx(ctx).As("ad").
			LeftJoin("sys_user u", "ad.user_id = u.id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdPlanSetting.Columns().UserId, userIds)
		}
		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u.dept_id", req.DeptIds)
		}
		if req.RuleId != 0 {
			m = m.Where("ad."+dao.AdPlanSetting.Columns().Id+" = ?", req.RuleId)
		}
		if req.RuleName != "" {
			m = m.Where("ad."+dao.AdPlanSetting.Columns().RuleName+" like ?", "%"+req.RuleName+"%")
		}
		if req.Status != "" {
			m = m.Where("ad."+dao.AdPlanSetting.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.StartTime != "" && req.EndTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.WhereGTE("ad."+dao.AdPlanSetting.Columns().CreatedAt, dayStartTime)
			m = m.WhereLTE("ad."+dao.AdPlanSetting.Columns().CreatedAt, dayEndTime)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "ad.id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPlanSettingListRes
		err = m.Fields("ad.id as ruleId").
			Fields("ad.rule_name as ruleName").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ad.media_type as mediaType").
			Fields("ad.object_type as objectType").
			Fields("ad.scope_type as scopeType").
			Fields("ad.scope_object_type as scopeObjectType").
			Fields("adjust_channel as adjustChannel").
			Fields("ad.scope_entity_ids as scopeEntityIds").
			Fields("ad.only_message as onlyMessage").
			Fields("ad.run_frequency as runFrequency").
			Fields("ad.effective_time_type as effectiveTimeType").
			Fields("ad.effective_start_time as effectiveStartTime").
			Fields("ad.effective_end_time as effectiveEndTime").
			Fields("ad.mode_of_notification as modeOfNotification").
			Fields("ad.phone_no as phoneNo").
			Fields("ad.email as email").
			Fields("ad.status as status").
			Fields("ad.created_at as createdAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPlanSettingListRes, len(res))
		for k, v := range res {
			adPlanSetting := &model.AdPlanSettingListRes{
				RuleId:             v.RuleId,
				RuleName:           v.RuleName,
				UserId:             v.UserId,
				UserName:           v.UserName,
				MediaType:          v.MediaType,
				ObjectType:         v.ObjectType,
				ScopeType:          v.ScopeType,
				ScopeObjectType:    v.ScopeObjectType,
				ScopeEntityIds:     v.ScopeEntityIds,
				AdjustChannel:      v.AdjustChannel,
				OnlyMessage:        v.OnlyMessage,
				RunFrequency:       v.RunFrequency,
				EffectiveTimeType:  v.EffectiveTimeType,
				EffectiveStartTime: v.EffectiveStartTime,
				EffectiveEndTime:   v.EffectiveEndTime,
				ModeOfNotification: v.ModeOfNotification,
				PhoneNo:            v.PhoneNo,
				Email:              v.Email,
				Status:             v.Status,
				CreatedAt:          v.CreatedAt,
			}
			adPlanSetting.ScopeEntityNums = len(strings.Split(adPlanSetting.ScopeEntityIds, commonConsts.Delimiter))
			listRes.List[k] = adPlanSetting
		}
	})
	return
}

func (s *sAdPlanSetting) BeforeGetById(ctx context.Context, id int) (res *model.AdPlanSettingInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanSetting.Ctx(ctx).WithAll().Where(dao.AdPlanSetting.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanSetting) GetById(ctx context.Context, id int) (res *model.AdPlanSettingListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPlanSetting.Ctx(ctx).Where(dao.AdPlanSetting.Columns().Id, id)
		err = m.Fields("id as ruleId").
			Fields("rule_name as ruleName").
			Fields("user_id as userId").
			Fields("media_type as mediaType").
			Fields("object_type as objectType").
			Fields("scope_type as scopeType").
			Fields("scope_object_type as scopeObjectType").
			Fields("scope_entity_ids as scopeEntityIds").
			Fields("adjust_channel as adjustChannel").
			Fields("only_message as onlyMessage").
			Fields("run_frequency as runFrequency").
			Fields("effective_time_type as effectiveTimeType").
			Fields("effective_start_time as effectiveStartTime").
			Fields("effective_end_time as effectiveEndTime").
			Fields("mode_of_notification as modeOfNotification").
			Fields("phone_no as phoneNo").
			Fields("email as email").
			Fields("status as status").
			Fields("created_at as createdAt").
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		// 查询ad_plan_rule表数据
		adPlanRuleList, _ := service.AdPlanRule().GetByPlanId(ctx, res.RuleId)
		res.AdPlanRuleList = adPlanRuleList
		// 查询ad_plan_execute表数据
		adPlanExecute, _ := service.AdPlanExecute().GetByPlanId(ctx, res.RuleId)
		if adPlanExecute != nil {
			res.ExecuteType = adPlanExecute.ExecuteType
			res.AdjustmentType = adPlanExecute.AdjustmentType
			res.ValueType = adPlanExecute.ValueType
			res.ExecuteValue = adPlanExecute.ExecuteValue
			res.LimitValue = adPlanExecute.LimitValue
			res.TimeDimension = adPlanExecute.TimeDimension
			res.ExecuteTimes = adPlanExecute.ExecuteTimes
		}
		// 查询ad_plan_channel_execute表数据
		adPlanChannelExecute, _ := service.AdPlanChannelExecute().GetByPlanId(ctx, res.RuleId)
		if adPlanChannelExecute != nil {
			res.ChannelList = adPlanChannelExecute.ChannelList
			res.TemplateId = adPlanChannelExecute.TemplateId
			res.SinglePrice = adPlanChannelExecute.SinglePrice
			res.LockNum = adPlanChannelExecute.LockNum
			if adPlanChannelExecute.AdSettingId != 0 {
				adSetting, _ := channelService.AdSetting().GetById(ctx, adPlanChannelExecute.AdSettingId)
				if res.AdSetting == nil {
					res.AdSetting = &channelModel.AdSettingListRes{}
				}
				err = gconv.Struct(adSetting, res.AdSetting)
				liberr.ErrIsNil(ctx, err, "struct转换失败")
			}
			if adPlanChannelExecute.RewardId != 0 {
				adRewardSetting, _ := channelService.AdRewardSetting().GetById(ctx, adPlanChannelExecute.RewardId)
				if res.AdSetting == nil {
					res.AdSetting = &channelModel.AdSettingListRes{}
				}
				res.AdSetting.RewardId = adRewardSetting.Id
				err = gconv.Struct(adRewardSetting, res.AdSetting)
				res.AdSetting.Pv = adRewardSetting.Ipu
				res.AdSetting.PvFlag = adRewardSetting.IpuFlag
				liberr.ErrIsNil(ctx, err, "struct转换失败")
			}
			channelInfos := make([]*model.ScopeEntityInfo, 0)
			for _, v := range strings.Split(res.ChannelList, commonConsts.Delimiter) {
				channelInfos = append(channelInfos, &model.ScopeEntityInfo{
					Label: v,
					Value: v,
				})
			}
			res.ChannelInfos = channelInfos
		}
		// 返回前端需要的数据结构
		managedObjectList, err1 := s.GetManagedObjectList(ctx, &model.GetManagedObjectListReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: 10000,
			},
			RuleId: res.RuleId,
		})
		liberr.ErrIsNil(ctx, err1, "获取托管对象列表失败")
		scopeEntityInfos := make([]*model.ScopeEntityInfo, 0)
		for _, v := range managedObjectList.List {
			scopeEntityInfos = append(scopeEntityInfos, &model.ScopeEntityInfo{
				Label: v.Name,
				Value: v.Id,
			})
		}
		res.ScopeEntityInfos = scopeEntityInfos
	})
	return
}

func (s *sAdPlanSetting) Add(ctx context.Context, req *model.AdPlanSettingAddReq) (err error) {
	// 校验价格区间是否合法
	if req.AdSetting != nil {
		err = channelService.AdSetting().CheckPriceRange(req.AdSetting)
		if err != nil {
			return
		}
	}
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		err = g.Try(ctx, func(ctx context.Context) {
			user := sysService.Context().GetLoginUser(ctx)
			// 1. 添加ad_plan_setting表数据
			adPlainSetting := do.AdPlanSetting{
				RuleName:           req.RuleName,
				UserId:             user.Id,
				MediaType:          req.MediaType,
				ObjectType:         req.ObjectType,
				ScopeType:          req.ScopeType,
				ScopeObjectType:    req.ScopeObjectType,
				ScopeEntityIds:     req.ScopeEntityIds,
				AdjustChannel:      req.AdjustChannel,
				OnlyMessage:        req.OnlyMessage,
				RunFrequency:       req.RunFrequency,
				EffectiveTimeType:  req.EffectiveTimeType,
				EffectiveStartTime: req.EffectiveStartTime,
				EffectiveEndTime:   req.EffectiveEndTime,
				ModeOfNotification: req.ModeOfNotification,
				PhoneNo:            req.PhoneNo,
				Email:              req.Email,
				Status:             req.Status,
			}
			planId, err1 := dao.AdPlanSetting.Ctx(ctx).InsertAndGetId(adPlainSetting)
			liberr.ErrIsNil(ctx, err1, "添加托管设置失败")

			// 2. 添加ad_plan_rule表数据
			for _, addReq := range req.AdPlanRuleList {
				addReq.PlanId = gconv.Int(planId)
			}
			err2 := service.AdPlanRule().BatchAdd(ctx, req.AdPlanRuleList)
			liberr.ErrIsNil(ctx, err2, "添加托管规则失败")

			// 3. 添加ad_plan_execute表数据
			err3 := service.AdPlanExecute().Add(ctx, &model.AdPlanExecuteAddReq{
				PlanId:         gconv.Int(planId),
				ExecuteType:    req.ExecuteType,
				ExecuteTimes:   req.ExecuteTimes,
				ExecuteValue:   req.ExecuteValue,
				LimitValue:     req.LimitValue,
				TimeDimension:  req.TimeDimension,
				AdjustmentType: req.AdjustmentType,
				ValueType:      req.ValueType,
			})
			liberr.ErrIsNil(ctx, err3, "添加托管执行失败")
			s.AddChannelExecute(ctx, req, gconv.Int(planId))
			err = s.PushToPlanQueue(ctx, &model.AdPlanSettingInfoRes{
				Id:           gconv.Int(planId),
				RunFrequency: req.RunFrequency,
			})
			liberr.ErrIsNil(ctx, err, "添加到托管队列失败")
		})
		return err
	})
	return
}

func (s *sAdPlanSetting) AddChannelExecute(ctx context.Context, req *model.AdPlanSettingAddReq, planId int) {
	if req.AdSetting == nil {
		return
	}
	// 1. 添加ad_reward_setting表数据
	rewardId, err4 := channelService.AdRewardSetting().Add(ctx, &channelModel.AdRewardSettingAddReq{
		Up:                        req.AdSetting.Up,
		Ipu:                       req.AdSetting.Pv,
		Ecpm:                      req.AdSetting.Ecpm,
		UpFlag:                    req.AdSetting.UpFlag,
		IpuFlag:                   req.AdSetting.PvFlag,
		EcpmFlag:                  req.AdSetting.EcpmFlag,
		AdCallbackConfig:          req.AdSetting.AdCallbackConfig,
		CallbackNum:               req.AdSetting.CallbackNum,
		UnCallbackNum:             req.AdSetting.UnCallbackNum,
		OldUserCallbackFlag:       req.AdSetting.OldUserCallbackFlag,
		OldUserAdCallbackConfig:   req.AdSetting.OldUserAdCallbackConfig,
		Dimension:                 req.AdSetting.Dimension,
		ProtectionSetting:         req.AdSetting.ProtectionSetting,
		DailyNonCallbackQuantity:  req.AdSetting.DailyNonCallbackQuantity,
		SingleNonCallbackQuantity: req.AdSetting.SingleNonCallbackQuantity,
	})
	liberr.ErrIsNil(ctx, err4, "添加激励配置失败")

	// 2. 添加ad_setting表数据
	adSettingId, err5 := channelService.AdSetting().Add(ctx, &channelModel.AdSettingEditReq{
		UserActionSetAppName:       req.AdSetting.UserActionSetAppName,
		BaiduToken:                 req.AdSetting.BaiduToken,
		SubChannel:                 uuid.NewString(),
		OrderReturnRateType:        req.AdSetting.OrderReturnRateType,
		AdActive:                   req.AdSetting.AdActive,
		ReturnRate:                 req.AdSetting.ReturnRate,
		MinReturnAmount:            req.AdSetting.MinReturnAmount,
		MaxReturnAmount:            req.AdSetting.MaxReturnAmount,
		ChannelFrom:                req.AdSetting.ChannelFrom,
		OriginalAccount:            req.AdSetting.OriginalAccount,
		IsDefault:                  0,
		CustomReportFlag:           req.AdSetting.CustomReportFlag,
		AdAction:                   req.AdSetting.AdAction,
		OpenFlag:                   req.AdSetting.OpenFlag,
		AdvertiserId:               req.AdSetting.AdvertiserId,
		SourceId:                   req.AdSetting.SourceId,
		LowOrderReturnRateTrue:     req.AdSetting.LowOrderReturnRateTrue,
		AppId:                      req.AdSetting.AppId,
		LowOrderReturnRateFalse:    req.AdSetting.LowOrderReturnRateFalse,
		MiddleOrderReturnRateTrue:  req.AdSetting.MiddleOrderReturnRateTrue,
		MiddleOrderReturnRateFalse: req.AdSetting.MiddleOrderReturnRateFalse,
		HighOrderReturnRateTrue:    req.AdSetting.HighOrderReturnRateTrue,
		HighOrderReturnRateFalse:   req.AdSetting.HighOrderReturnRateFalse,
		LowMinReturnAmount:         req.AdSetting.LowMinReturnAmount,
		LowMaxReturnAmount:         req.AdSetting.LowMaxReturnAmount,
		MiddleMinReturnAmount:      req.AdSetting.MiddleMinReturnAmount,
		MiddleMaxReturnAmount:      req.AdSetting.MiddleMaxReturnAmount,
		HighMinReturnAmount:        req.AdSetting.HighMinReturnAmount,
		HighMaxReturnAmount:        req.AdSetting.HighMaxReturnAmount,
		LowOpenFlag:                req.AdSetting.LowOpenFlag,
		MiddleOpenFlag:             req.AdSetting.MiddleOpenFlag,
		HighOpenFlag:               req.AdSetting.HighOpenFlag,
		TopMinReturnAmount:         req.AdSetting.TopMinReturnAmount,
		TopMaxReturnAmount:         req.AdSetting.TopMaxReturnAmount,
		ReturnType:                 req.AdSetting.ReturnType,
		TopOrderReturnRateTrue:     req.AdSetting.TopOrderReturnRateTrue,
		TopOrderReturnRateFalse:    req.AdSetting.TopOrderReturnRateFalse,
		AdvertiserIds:              req.AdSetting.AdvertiserIds,
		OldCustomReportFlag:        req.AdSetting.OldCustomReportFlag,
		RewardOpenFlag:             req.AdSetting.RewardOpenFlag,
	})
	liberr.ErrIsNil(ctx, err5, "添加广告配置失败")

	// 3. 添加ad_plan_channel_execute表数据
	err6 := service.AdPlanChannelExecute().Add(ctx, &model.AdPlanChannelExecuteAddReq{
		PlanId:      planId,
		ChannelList: req.ChannelList,
		TemplateId:  req.TemplateId,
		SinglePrice: req.SinglePrice,
		LockNum:     req.LockNum,
		RewardId:    gconv.Int(rewardId),
		AdSettingId: gconv.Int(adSettingId),
	})
	liberr.ErrIsNil(ctx, err6, "添加渠道执行失败")
}

// PushToPlanQueue 推送配置到队列中
func (s *sAdPlanSetting) PushToPlanQueue(ctx context.Context, req *model.AdPlanSettingInfoRes) (err error) {
	data := fmt.Sprintf("%d", req.Id)
	// 生成唯一的id
	correlationId := libUtils.GenerateID()
	libUtils.SafeGo(func() {
		_ = sysService.SysMqTask().Add(ctx, &systemModel.SysMqTaskAddReq{
			CorrelationId: correlationId,
			TaskData:      data,
			JobId:         req.Id,
			TaskType:      consts.AdPlanTask,
			RetryCount:    0,
			Status:        1,
		})
	})
	//
	err = commonService.GetRabbitMqClient().PublishDelayMsg(rabbitmqlib.AD_PLAN_EXCHANGE, rabbitmqlib.AD_PLAN_KEY, correlationId, []byte(data), s.GetDelayTime(req))
	return err
}

// GetDelayTime 根据配置获取当前push 应当设置的执行间隔时间
func (s *sAdPlanSetting) GetDelayTime(req *model.AdPlanSettingInfoRes) (expireTime int64) {
	// 1. 获取当前时间
	if req.RunFrequency > 0 {
		return int64(req.RunFrequency) * 60 * 1000
	} else {
		return 0
	}
}

func (s *sAdPlanSetting) ExecutePlanByPlanId(ctx context.Context, id string) (err error) {
	// 获取当前配置
	adPlanSettingInfo, err := service.AdPlanSetting().BeforeGetById(ctx, gconv.Int(id))
	// 根据当前配置判断当前配置是否在运行中
	s.Execute(ctx, adPlanSettingInfo)
	if CheckPlanNeedToQueue(adPlanSettingInfo) {
		err = s.PushToPlanQueue(ctx, adPlanSettingInfo)
	}
	return
}

// Execute 执行计划
func (s *sAdPlanSetting) Execute(ctx context.Context, adPlanSettingInfo *model.AdPlanSettingInfoRes) {
	// 获取当前配置
	// 根据当前配置判断当前配置是否在运行中
	if adPlanSettingInfo != nil && CheckPlanNeedExecuted(adPlanSettingInfo) {
		// 判断当前计划是否需要执行下一步
		need, metricsList := s.IsConditionMet(ctx, adPlanSettingInfo)
		if need {
			// 获取当前执行计划
			planExecuteInfo, _ := service.AdPlanExecute().GetByPlanId(ctx, adPlanSettingInfo.Id)
			var bidOrBudgetMap map[int64]float64
			var bidOrBudgetMap2 map[string]float64
			if planExecuteInfo.ExecuteType == 2 && adPlanSettingInfo.ObjectType == 3 && planExecuteInfo.AdjustmentType != 1 {
				ids := make([]int64, 0)
				for _, item := range metricsList {
					ids = append(ids, libUtils.StringToInt64(item.ObjectId))
				}
				bidOrBudgetMap, _ = oceanengineService.AdPromotion().GetAdPromotionCpaBid(ctx, ids)
			}

			if planExecuteInfo.ExecuteType == 1 && adPlanSettingInfo.ObjectType == 3 && planExecuteInfo.AdjustmentType != 1 {
				ids := make([]int64, 0)
				for _, item := range metricsList {
					ids = append(ids, libUtils.StringToInt64(item.ObjectId))
				}
				bidOrBudgetMap, _ = oceanengineService.AdPromotion().GetAdPromotionBudget(ctx, ids)
			}

			if planExecuteInfo.ExecuteType == 1 && adPlanSettingInfo.ObjectType == 2 && planExecuteInfo.AdjustmentType != 1 {
				ids := make([]string, 0)
				for _, item := range metricsList {
					ids = append(ids, item.ObjectId)
				}
				bidOrBudgetMap2, _ = oceanengineService.AdProject().GetProjectBudget(ctx, ids)
			}

			if planExecuteInfo.ExecuteType == 1 && adPlanSettingInfo.ObjectType == 1 && planExecuteInfo.AdjustmentType != 1 {
				ids := make([]string, 0)
				for _, item := range metricsList {
					ids = append(ids, item.ObjectId)
				}
				bidOrBudgetMap2, _ = oceanengineService.AdAdvertiserAccount().GetAdAccountBudget(ctx, ids)
			}

			for _, item := range metricsList {
				if item.CompareResult {
					// 需要执行当前计划
					if adPlanSettingInfo.OnlyMessage != 1 {
						// 判断当前执行计划是否已经执行了设置的频率
						if planExecuteInfo.ExecuteTimes > 0 {
							// 当前计划已经执行成功了规定次数直接跳过执行 	// 判断当天的执行是否已经满足了
							if service.AdPlanLog().GetDayExecuteCount(ctx, adPlanSettingInfo.Id, libUtils.StringParsInt(item.ObjectId), planExecuteInfo.ExecuteTimes) >= planExecuteInfo.ExecuteTimes {
								continue
							}
							contentStr := ""
							// 判断当前是否打开AdjustChannel
							if adPlanSettingInfo.AdjustChannel == 1 {
								operation, _ := service.AdPlanChannelExecute().AdPlanExecute(ctx, adPlanSettingInfo.Id)
								if len(operation) > 0 {
									contentStr += operation + "\n"
								}
							}
							//  仅仅只发送通知
							sendErr := s.SendNotify(ctx, adPlanSettingInfo.Id)
							if sendErr != nil {
								g.Log().Errorf(ctx, "-- SendNotify error: %v -- ", sendErr)
								contentStr += "发送通知：失败" + "\n"
							} else {
								contentStr += "发送通知：成功" + "\n"
							}
							// 开始执行调整计划
							switch planExecuteInfo.ExecuteType {
							case 1: // 更新预算
								if adPlanSettingInfo.ObjectType == 3 {
									bid := float64(0)
									if planExecuteInfo.AdjustmentType == 1 {
										bid = ComputeBid(0, planExecuteInfo)
									} else {
										nowBid := bidOrBudgetMap[libUtils.StringToInt64(item.ObjectId)]
										bid = ComputeBid(nowBid, planExecuteInfo)
									}

									contentStr += fmt.Sprintf("更新广告预算至%v", bid) + "\n"
									_, err := PromotionBudgetUpdate(item, bid)
									if err != nil {
										g.Log().Errorf(ctx, "-- PromotionStatusUpdate error: %v -- ", err)
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 1)
										//})
									} else {
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 2)
										//})
									}
								} else if adPlanSettingInfo.ObjectType == 2 {
									bid := float64(0)
									if planExecuteInfo.AdjustmentType == 1 {
										bid = ComputeBid(0, planExecuteInfo)
									} else {
										nowBid := bidOrBudgetMap2[item.ObjectId]
										bid = ComputeBid(nowBid, planExecuteInfo)
									}
									contentStr += fmt.Sprintf("更新项目预算至%v", bid) + "\n"
									_, err := ProjectBudgetUpdate(item, bid)
									if err != nil {
										g.Log().Errorf(ctx, "-- PromotionStatusUpdate error: %v -- ", err)
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 1)
										//})
									} else {
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 2)
										//})
									}
								} else if adPlanSettingInfo.ObjectType == 1 {
									bid := float64(0)
									if planExecuteInfo.AdjustmentType == 1 {
										bid = ComputeBid(0, planExecuteInfo)
									} else {
										nowBid := bidOrBudgetMap2[item.ObjectId]
										bid = ComputeBid(nowBid, planExecuteInfo)
									}
									contentStr += fmt.Sprintf("更新账户预算至%v", bid) + "\n"
									_, err := AccountBudgetUpdate(item, bid)
									if err != nil {
										g.Log().Errorf(ctx, "-- PromotionStatusUpdate error: %v -- ", err)

										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 1)
										//})
									} else {
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 2)
										//})
									}

								}
							case 2: // 更新出价
								if adPlanSettingInfo.ObjectType == 3 {
									bid := float64(0)
									if planExecuteInfo.AdjustmentType == 1 {
										bid = ComputeBid(0, planExecuteInfo)
									} else {
										nowBid := bidOrBudgetMap[libUtils.StringToInt64(item.ObjectId)]
										bid = ComputeBid(nowBid, planExecuteInfo)
									}
									contentStr += fmt.Sprintf("更新广告出价至%v", bid) + "\n"
									// 更新出价
									_, err := PromotionBidUpdate(item, bid)
									if err != nil {
										g.Log().Errorf(ctx, "-- PromotionStatusUpdate error: %v -- ", err)
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 1)
										//})
									} else {
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 2)
										//})
									}
								}
							case 3:
								// 执行暂停广告计划
								if adPlanSettingInfo.ObjectType == 3 {
									contentStr += "执行暂停广告计划" + "\n"
									_, err := PromotionStatusUpdate(item)
									if err != nil {
										g.Log().Errorf(ctx, "-- PromotionStatusUpdate error: %v -- ", err)
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 1)
										//})
									} else {
										//libUtils.SafeGo(func() {
										AddPlanLog(ctx, adPlanSettingInfo, planExecuteInfo, item, contentStr, 2)
										//})
									}
								}
							}
						}
					} else {
						//  仅仅只发送通知
						sendErr := s.SendNotify(ctx, adPlanSettingInfo.Id)
						if sendErr != nil {
							g.Log().Errorf(ctx, "-- SendNotify error: %v -- ", sendErr)
						}
					}
				}
			}
		}
	}
	return
}

func AddPlanLog(ctx context.Context, adPlanSettingInfo *model.AdPlanSettingInfoRes, planExecuteInfo *model.AdPlanExecuteInfoRes, item *oceanengineModel.MetricsByPlanTaskRes, contentStr string, success int) {
	_ = service.AdPlanLog().Add(ctx, &model.AdPlanLogAddReq{
		PlanId:         adPlanSettingInfo.Id,
		RuleExecuteId:  planExecuteInfo.Id,
		RuleName:       adPlanSettingInfo.RuleName,
		MediaType:      adPlanSettingInfo.MediaType,
		ObjectType:     adPlanSettingInfo.ObjectType,
		ScopeType:      adPlanSettingInfo.ScopeType,
		ScopeEntityId:  libUtils.StringToInt64(item.ObjectId),
		Conditions:     buildConditions(item),
		Content:        contentStr,
		ExecutionState: success,
	})
}

// 根据metric 指标key value 来拼接满足条件
func buildConditions(metrics *oceanengineModel.MetricsByPlanTaskRes) string {
	returnStr := ""
	for _, item := range metrics.AdPlanRule {
		if item.TimeScope == 1 {
			returnStr = "当天" + metrics.KeyNameMap[item.MetricsName] + item.Operator + fmt.Sprintf("%v", item.TargetValue) + item.Unit + "\n"
		} else {
			returnStr = fmt.Sprintf("过去%v天", item.TimeScope) + metrics.KeyNameMap[item.MetricsName] + item.Operator + fmt.Sprintf("%v", item.TargetValue) + item.Unit + "\n"
		}
	}
	return returnStr
}

// ComputeBid 根据当前的出价来调整最终价格
func ComputeBid(nowBid float64, execute *model.AdPlanExecuteInfoRes) float64 {
	//1 目标值 2 增加，3 减少
	switch execute.AdjustmentType {
	case 1:
		return float64(execute.ExecuteValue)
	case 2:
		// yuan
		if execute.ValueType == 1 {
			nowBid += float64(execute.ExecuteValue)
		} else if execute.ValueType == 2 { // 百分比
			nowBid += nowBid * float64(execute.ExecuteValue/100)
		}
	case 3:
		if execute.ValueType == 1 {
			nowBid -= float64(execute.ExecuteValue)
		} else if execute.ValueType == 2 { // 百分比
			nowBid -= nowBid * float64(execute.ExecuteValue/100)
		}
	}
	return nowBid
}

// AccountBudgetUpdate 更新账户预算
func AccountBudgetUpdate(item *oceanModel.MetricsByPlanTaskRes, budget float64) (bool, error) {
	resp, err := advertiser.GetToutiaoApiClient().AdvertiserBudgetUpdateV2ApiService.AdvertiserUpdateBudgetV2Request(toutiaoModels.AdvertiserUpdateBudgetV2Request{
		AdvertiserId: libUtils.StringToInt64(item.AdId),
		//ProjectId: gconv.Int64(item.ObjectId),
		Budget: &budget,
	}).AccessToken(item.AccessToken).Do()
	if err != nil {
		err = fmt.Errorf("更新广告出价失败: %v", err)
		return false, err
	}
	if *resp.Code == 0 {
		return true, nil
	} else {
		return false, errors.New(*resp.Message)
	}
}

// ProjectBudgetUpdate 更新项目预算
func ProjectBudgetUpdate(item *oceanModel.MetricsByPlanTaskRes, budget float64) (bool, error) {
	resp, err := advertiser.GetToutiaoApiClient().ProjectBudgetUpdateV3ApiService.ProjectBudgetUpdateV30Request(toutiaoModels.ProjectBudgetUpdateV30Request{
		AdvertiserId: libUtils.StringToInt64(item.AdId),
		Data: []*toutiaoModels.ProjectBudgetUpdateV30RequestDataInner{
			{
				ProjectId: gconv.Int64(item.ObjectId),
				Budget:    &budget,
			},
		},
	}).AccessToken(item.AccessToken).Do()
	if err != nil {
		err = fmt.Errorf("更新广告出价失败: %v", err)
		return false, err
	}
	if *resp.Code == 0 {
		return true, nil
	} else {
		return false, errors.New(*resp.Message)
	}

}

// PromotionBudgetUpdate 更新 广告预算
func PromotionBudgetUpdate(item *oceanModel.MetricsByPlanTaskRes, budget float64) (bool, error) {
	resp, err := advertiser.GetToutiaoApiClient().PromotionBudgetUpdateV3ApiService.PromotionBudgetUpdateV30Request(toutiaoModels.PromotionBudgetUpdateV30Request{
		AdvertiserId: libUtils.StringToInt64(item.AdId),
		Data: []*toutiaoModels.PromotionBudgetUpdateV30RequestDataInner{
			{
				PromotionId: gconv.Int64(item.ObjectId),
				Budget:      budget,
			},
		},
	}).AccessToken(item.AccessToken).Do()
	if err != nil {
		err = fmt.Errorf("更新广告出价失败: %v", err)
		return false, err
	}
	if *resp.Code == 0 {
		return true, nil
	} else {
		return false, errors.New(*resp.Message)
	}

}

// PromotionBidUpdate 更新广告出价
func PromotionBidUpdate(item *oceanModel.MetricsByPlanTaskRes, bid float64) (bool, error) {
	// 执行暂停广告计划
	resp, err := advertiser.GetToutiaoApiClient().PromotionBidUpdateV3ApiService.PromotionBidUpdateV30Request(toutiaoModels.PromotionBidUpdateV30Request{
		AdvertiserId: libUtils.StringToInt64(item.AdId),
		Data: []*toutiaoModels.PromotionBidUpdateV30RequestDataInner{
			{
				PromotionId: gconv.Int64(item.ObjectId),
				Bid:         bid,
			},
		},
	}).AccessToken(item.AccessToken).Do()
	if err != nil {
		err = fmt.Errorf("更新广告出价失败: %v", err)
		return false, err
	}
	if *resp.Code == 0 {
		return true, nil
	} else {
		return false, errors.New(*resp.Message)
	}

}

// PromotionStatusUpdate 执行暂停广告计划
func PromotionStatusUpdate(item *oceanModel.MetricsByPlanTaskRes) (bool, error) {
	// 执行暂停广告计划
	resp, err := advertiser.GetToutiaoApiClient().PromotionStatusUpdateV3ApiService.PromotionStatusUpdateV30Request(toutiaoModels.PromotionStatusUpdateV30Request{
		AdvertiserId: libUtils.StringToInt64(item.AdId),
		Data: []*toutiaoModels.PromotionStatusUpdateV30RequestDataInner{
			{
				PromotionId: gconv.Int64(item.ObjectId),
				OptStatus:   toutiaoModels.DISABLE_PromotionStatusUpdateV30DataOptStatus,
			},
		},
	}).AccessToken(item.AccessToken).Do()
	if err != nil {
		err = fmt.Errorf("更新广告状态失败: %v", err)
		return false, err
	}
	if *resp.Code == 0 {
		return true, nil
	} else {
		return false, errors.New(*resp.Message)
	}
}

// IsConditionMet 判断当前计划是否符合执行条件
func (s *sAdPlanSetting) IsConditionMet(ctx context.Context, adPlanSettingInfo *model.AdPlanSettingInfoRes) (bool, []*oceanengineModel.MetricsByPlanTaskRes) {
	switch adPlanSettingInfo.ObjectType {
	case 1:
		return ConditionAccount(ctx, adPlanSettingInfo)
	case 2:
		return ConditionProject(ctx, adPlanSettingInfo)
	case 3:
		return ConditionPromotion(ctx, adPlanSettingInfo)
	}
	return false, nil
}

// MergeMetrics 将同一个objectId 的所有指标数据进行合并
func MergeMetrics(metricsList, innerList []*oceanengineModel.MetricsByPlanTaskRes) []*oceanengineModel.MetricsByPlanTaskRes {
	for _, inner := range innerList {
		have := false
		for _, res := range metricsList {
			if res.ObjectId == inner.ObjectId {
				have = true
				for key, val := range res.KeyValuePare {
					res.KeyValuePare[key] = val
				}
			}
		}
		if !have {
			metricsList = append(metricsList, inner)
		}
	}
	return metricsList
}

func ConditionAccount(ctx context.Context, adPlanSettingInfo *model.AdPlanSettingInfoRes) (bool, []*oceanengineModel.MetricsByPlanTaskRes) {
	var metricsList = make([]*oceanengineModel.MetricsByPlanTaskRes, 0)
	// 根据当前创建者获取Account 数据
	// 当前用户下的所有数据
	taskList := make([]*oceanengineModel.AdAdvertiserAccountTaskListRes, 0)
	if adPlanSettingInfo.ScopeType == 1 {
		returnList, err := oceanengineService.AdAdvertiserAccount().GetTaskList(ctx, &oceanengineModel.AdAdvertiserTaskSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: consts.MaxPageSize,
			},
			UserId: adPlanSettingInfo.UserId,
			//AdStatus: "1",
		})
		if err != nil {
			g.Log().Errorf(ctx, "ConditionAccount -> GetTaskList获取账户数据失败%+v", err)
			return false, nil
		}
		if len(returnList.List) == 0 {
			return false, nil
		}
		taskList = returnList.List

	} else if adPlanSettingInfo.ScopeType == 2 {
		ids := strings.Split(adPlanSettingInfo.ScopeEntityIds, "|")
		returnList, err := oceanengineService.AdAdvertiserAccount().GetTaskList(ctx, &oceanengineModel.AdAdvertiserTaskSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: consts.MaxPageSize,
			},
			Ids: ids,
			//AdStatus: "1",
		})
		if err != nil {
			g.Log().Errorf(ctx, "ConditionAccount -> GetTaskList获取账户数据失败%+v", err)
			return false, nil
		}
		if len(returnList.List) == 0 {
			return false, nil
		}
		taskList = returnList.List
	}
	var adIds = make([]string, 0)
	for _, item := range taskList {
		adIds = append(adIds, item.AdvertiserId)
	}
	// BatchAddAdvertiser 更新账户下的预算和信息
	startTime := time.Now() // 记录开始时间
	_ = oceanengineService.AdMajordomoAdvertiserAccount().TaskPlanAsync(ctx, taskList)
	elapsedTime := time.Since(startTime) // 计算耗时
	log.Printf("TaskPlanAsync耗时: %v", elapsedTime)

	// 同步当天的指标数据
	startTime = time.Now() // 重新记录开始时间
	_ = oceanengineService.AdAdvertiserAccountMetricsData().AdAdvertiserAccountReportSubTask2(ctx, taskList, libUtils.GetNowDate())
	elapsedTime = time.Since(startTime) // 计算耗时
	log.Printf("AdAdvertiserAccountReportSubTask2耗时: %v", elapsedTime)
	// 根据配置查询相关指标数据 判断是否需要执行下一步

	// 根据plan_id 获取 ad_plan_rule 表格的list
	adPlanRuleList, err := service.AdPlanRule().List(ctx, &model.AdPlanRuleSearchReq{
		PlanId: fmt.Sprintf("%d", adPlanSettingInfo.Id),
		PageReq: comModel.PageReq{
			PageNum:  1,
			PageSize: consts.MaxPageSize,
		},
	})
	if err != nil {
		g.Log().Errorf(ctx, "ConditionAccount -> AdPlanRule.List获取规则数据失败%+v", err)
	}

	// 构造执行参数
	searchList := buildMetricsByPlanTaskReq(adPlanRuleList)
	for _, item := range searchList {
		item.ObjectIds = adIds
	}

	// 单个任务单个任务进行数据汇总
	for _, item := range searchList {
		innerList := oceanengineService.AdAdvertiserAccountMetricsData().GetAccountMetricsByPlanTask(ctx, item.ObjectIds, item.MetricsName, item.TimeScope)
		metricsList = MergeMetrics(metricsList, innerList)
	}
	//判断所有指标是否都满足条件
	CompareAllMetrics(adIds, metricsList, adPlanRuleList)
	for _, taskItem := range taskList {
		for _, item := range metricsList {
			if taskItem.AdvertiserId == item.AdId {
				item.AccessToken = taskItem.AccessToken
			}
		}
	}
	for _, item := range metricsList {
		if item.CompareResult {
			return true, metricsList
		}
	}
	return false, metricsList
}

func ConditionProject(ctx context.Context, adPlanSettingInfo *model.AdPlanSettingInfoRes) (bool, []*oceanengineModel.MetricsByPlanTaskRes) {
	var metricsList = make([]*oceanengineModel.MetricsByPlanTaskRes, 0)
	// 根据当前创建者获取Account 数据
	// 当前用户下的所有数据
	taskList := make([]*oceanengineModel.AdAdvertiserTaskListRes, 0)
	if adPlanSettingInfo.ScopeType == 1 {
		returnList, err := oceanengineService.AdProject().GetTaskList(ctx, &oceanengineModel.AdAdvertiserTaskSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: consts.MaxPageSize,
			},
			UserId:   adPlanSettingInfo.UserId,
			AdStatus: "PROJECT_STATUS_ENABLE",
		})
		if err != nil {
			g.Log().Errorf(ctx, "ConditionProject -> GetTaskList获取账户数据失败%+v", err)
			return false, nil
		}
		if len(returnList.List) == 0 {
			return false, nil
		}
		taskList = returnList.List

	} else if adPlanSettingInfo.ScopeType == 2 {
		ids := strings.Split(adPlanSettingInfo.ScopeEntityIds, "|")
		returnList, err := oceanengineService.AdProject().GetTaskList(ctx, &oceanengineModel.AdAdvertiserTaskSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: consts.MaxPageSize,
			},
			Ids:      ids,
			AdStatus: "PROJECT_STATUS_ENABLE",
		})
		if err != nil {
			g.Log().Errorf(ctx, "ConditionAccount -> GetTaskList获取账户数据失败%+v", err)
			return false, nil
		}
		if len(returnList.List) == 0 {
			return false, nil
		}
		taskList = returnList.List
	}
	var ids = make([]string, 0)
	for _, item := range taskList {
		ids = append(ids, item.ObjectIds...)
	}
	// BatchAddAdvertiser 更新账户下的预算和信息
	startTime := time.Now() // 记录开始时间
	for _, item := range taskList {
		tokenMap, _ := oceanengineService.AdProject().SyncAdProject(ctx, "", []string{gconv.String(item.AdvertiserId)}, libUtils.StringArrayToInt64Array(item.ObjectIds))
		for _, taskItem := range taskList {
			for key, val := range tokenMap {
				if taskItem.AdvertiserId == key {
					taskItem.AccessToken = val
				}
			}
		}
	}
	elapsedTime := time.Since(startTime) // 计算耗时
	log.Printf("oceanengineService.AdProject().SyncAdProject耗时: %v", elapsedTime)

	// 同步当天的指标数据
	startTime = time.Now() // 重新记录开始时间
	for _, item := range taskList {
		_ = oceanengineService.AdProjectMetricsData().UpdateMetricsByPIds(ctx, item.ObjectIds, libUtils.GetNowDate(), false)
	}
	elapsedTime = time.Since(startTime) // 计算耗时
	log.Printf("UpdateMetricsByPIds耗时: %v", elapsedTime)

	// 根据plan_id 获取 ad_plan_rule 表格的list
	adPlanRuleList, err := service.AdPlanRule().List(ctx, &model.AdPlanRuleSearchReq{
		PlanId: fmt.Sprintf("%d", adPlanSettingInfo.Id),
		PageReq: comModel.PageReq{
			PageNum:  1,
			PageSize: consts.MaxPageSize,
		},
	})
	if err != nil {
		g.Log().Errorf(ctx, "ConditionAccount -> AdPlanRule.List获取规则数据失败%+v", err)
	}
	// 构造执行参数
	searchList := buildMetricsByPlanTaskReq(adPlanRuleList)
	for _, item := range searchList {
		item.ObjectIds = ids
	}

	// 单个任务单个任务进行数据汇总
	for _, item := range searchList {
		innerList := oceanengineService.AdProjectMetricsData().GetProjectMetricsByPlanTask(ctx, item.ObjectIds, item.MetricsName, item.TimeScope)
		metricsList = MergeMetrics(metricsList, innerList)
	}
	//判断所有指标是否都满足条件
	CompareAllMetrics(ids, metricsList, adPlanRuleList)
	for _, item := range metricsList {
		if item.CompareResult {
			return true, metricsList
		}
	}
	return false, metricsList
}

func ConditionPromotion(ctx context.Context, adPlanSettingInfo *model.AdPlanSettingInfoRes) (bool, []*oceanengineModel.MetricsByPlanTaskRes) {
	var metricsList = make([]*oceanengineModel.MetricsByPlanTaskRes, 0)
	// 根据当前创建者获取Account 数据
	// 当前用户下的所有数据
	taskList := make([]*oceanengineModel.AdAdvertiserTaskListRes, 0)
	if adPlanSettingInfo.ScopeType == 1 {
		returnList, err := oceanengineService.AdPromotion().GetTaskList(ctx, &oceanengineModel.AdAdvertiserTaskSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: consts.MaxPageSize,
			},
			UserId:   adPlanSettingInfo.UserId,
			AdStatus: "OK",
		})
		if err != nil {
			g.Log().Errorf(ctx, "ConditionPromotion -> GetTaskList获取账户数据失败%+v", err)
			return false, nil
		}
		if len(returnList.List) == 0 {
			return false, nil
		}
		taskList = returnList.List

	} else if adPlanSettingInfo.ScopeType == 2 {
		ids := strings.Split(adPlanSettingInfo.ScopeEntityIds, "|")
		returnList, err := oceanengineService.AdPromotion().GetTaskList(ctx, &oceanengineModel.AdAdvertiserTaskSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: consts.MaxPageSize,
			},
			Ids:      ids,
			AdStatus: "OK",
		})
		if err != nil {
			g.Log().Errorf(ctx, "ConditionAccount -> GetTaskList获取账户数据失败%+v", err)
			return false, nil
		}
		if len(returnList.List) == 0 {
			return false, nil
		}
		taskList = returnList.List
	}
	var ids = make([]string, 0)
	for _, item := range taskList {
		ids = append(ids, item.ObjectIds...)
	}
	// BatchAddAdvertiser 更新账户下的预算和信息
	startTime := time.Now() // 记录开始时间
	for _, item := range taskList {
		tokenMap, _ := oceanengineService.AdPromotion().SyncAdPromotion(ctx, "", []string{gconv.String(item.AdvertiserId)}, libUtils.StringArrayToInt64Array(item.ObjectIds))
		for _, taskItem := range taskList {
			for key, val := range tokenMap {
				if taskItem.AdvertiserId == key {
					taskItem.AccessToken = val
				}
			}
		}

	}
	elapsedTime := time.Since(startTime) // 计算耗时
	log.Printf("oceanengineService.AdPromotion().SyncAdPromotion耗时: %v", elapsedTime)

	// 同步当天的指标数据
	startTime = time.Now() // 重新记录开始时间
	for _, item := range taskList {
		_ = oceanengineService.AdPromotionMetricsData().UpdateMetricsByPIds(ctx, item.ObjectIds, libUtils.GetNowDate(), false)
	}
	elapsedTime = time.Since(startTime) // 计算耗时
	log.Printf("oceanengineService.AdPromotionMetricsData().UpdateMetricsByPIds耗时: %v", elapsedTime)

	// 根据plan_id 获取 ad_plan_rule 表格的list
	adPlanRuleList, err := service.AdPlanRule().List(ctx, &model.AdPlanRuleSearchReq{
		PlanId: fmt.Sprintf("%d", adPlanSettingInfo.Id),
		PageReq: comModel.PageReq{
			PageNum:  1,
			PageSize: consts.MaxPageSize,
		},
	})
	if err != nil {
		g.Log().Errorf(ctx, "ConditionAccount -> AdPlanRule.List获取规则数据失败%+v", err)
	}
	// 构造执行参数
	searchList := buildMetricsByPlanTaskReq(adPlanRuleList)
	for _, item := range searchList {
		item.ObjectIds = ids
	}

	// 单个任务单个任务进行数据汇总
	for _, item := range searchList {
		innerList := oceanengineService.AdPromotionMetricsData().GetProjectMetricsByPlanTask(ctx, item.ObjectIds, item.MetricsName, item.TimeScope)
		MergeMetrics(metricsList, innerList)
	}
	//判断所有指标是否都满足条件
	CompareAllMetrics(ids, metricsList, adPlanRuleList)
	for _, item := range metricsList {
		if item.CompareResult {
			return true, metricsList
		}
	}
	return false, metricsList
}

// CompareAllMetrics 判断所有指标是否都满足条件
func CompareAllMetrics(adIds []string, metricsList []*oceanengineModel.MetricsByPlanTaskRes, adPlanRuleList *model.AdPlanRuleSearchRes) {
	for _, adId := range adIds {
		for _, metrics := range metricsList {
			for i, rule := range adPlanRuleList.List {
				if metrics.ObjectId == adId {
					if !metrics.CompareResult && i > 0 {
						break
					}
					for key, val := range metrics.KeyValuePare {
						if key == libUtils.SnakeToCamel(rule.MetricsName) {
							metrics.CompareResult = libUtils.Compare(val, rule.TargetValue, rule.Operator)
							if len(metrics.AdPlanRule) > 0 {
								metrics.AdPlanRule = append(metrics.AdPlanRule, rule)
							} else {
								metrics.AdPlanRule = []*model.AdPlanRuleListRes{rule}
							}
						}
					}
				}

			}
		}
	}
}

// buildMetricsByPlanTaskReq 根据不同统计天数合并指标
func buildMetricsByPlanTaskReq(req *model.AdPlanRuleSearchRes) []*oceanengineModel.MetricsByPlanTaskReq {
	var reqList = make([]*oceanengineModel.MetricsByPlanTaskReq, 0)
	if len(req.List) > 0 {
		for _, res := range req.List {
			have := false
			for _, task := range reqList {
				if task.TimeScope == res.TimeScope {
					have = true
					task.MetricsName = append(task.MetricsName, res.MetricsName)
					break
				}
			}
			if !have {
				reqList = append(reqList, &oceanengineModel.MetricsByPlanTaskReq{
					MetricsName: []string{res.MetricsName},
					TimeScope:   res.TimeScope,
				})
			}
		}
	}
	return reqList
}

// CheckPlanNeedExecuted 判断当前的计划是否需要被执行
func CheckPlanNeedExecuted(adPlanSettingInfo *model.AdPlanSettingInfoRes) (need bool) {
	if adPlanSettingInfo.EffectiveTimeType == 1 {
		return true
	}
	// 1. 判断当前时间是否在计划执行时间范围内
	now := gtime.Now()
	if now.After(adPlanSettingInfo.EffectiveStartTime) || now.Before(adPlanSettingInfo.EffectiveEndTime) {
		return true
	} else {
		return false
	}
}

// CheckPlanNeedToQueue 判断当前的计划是否需要被加入队列
func CheckPlanNeedToQueue(adPlanSettingInfo *model.AdPlanSettingInfoRes) (need bool) {
	if adPlanSettingInfo.EffectiveTimeType == 1 {
		return true
	}
	now := gtime.Now()
	if now.After(adPlanSettingInfo.EffectiveEndTime) {
		return false
	} else {
		return true
	}
}

func (s *sAdPlanSetting) Edit(ctx context.Context, req *model.AdPlanSettingEditReq) (err error) {
	// 校验价格区间是否合法
	if req.AdSetting != nil {
		err = channelService.AdSetting().CheckPriceRange(req.AdSetting)
		if err != nil {
			return
		}
	}
	var planId = req.RuleId
	adPlanRules, _ := service.AdPlanRule().GetByPlanId(ctx, planId)
	adPlanExecute, _ := service.AdPlanExecute().GetByPlanId(ctx, planId)
	adPlanChannelExecute, _ := service.AdPlanChannelExecute().GetByPlanId(ctx, planId)
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		err = g.Try(ctx, func(ctx context.Context) {
			// 1. 修改ad_plan_setting表数据
			_, err = dao.AdPlanSetting.Ctx(ctx).WherePri(planId).Update(do.AdPlanSetting{
				RuleName:           req.RuleName,
				MediaType:          req.MediaType,
				ObjectType:         req.ObjectType,
				ScopeType:          req.ScopeType,
				ScopeObjectType:    req.ScopeObjectType,
				ScopeEntityIds:     req.ScopeEntityIds,
				AdjustChannel:      req.AdjustChannel,
				OnlyMessage:        req.OnlyMessage,
				RunFrequency:       req.RunFrequency,
				EffectiveTimeType:  req.EffectiveTimeType,
				EffectiveStartTime: req.EffectiveStartTime,
				EffectiveEndTime:   req.EffectiveEndTime,
				ModeOfNotification: req.ModeOfNotification,
				PhoneNo:            req.PhoneNo,
				Email:              req.Email,
				Status:             req.Status,
			})
			liberr.ErrIsNil(ctx, err, "修改托管设置失败")

			// 2. 修改ad_plan_rule表数据
			batchAddPlanRule := make([]*model.AdPlanRuleAddReq, 0)
			batchEditPlanRule := make([]*model.AdPlanRuleEditReq, 0)
			editIds := make([]int, 0)
			// 新增，id=0的数据
			for _, adPlanRule := range req.AdPlanRuleList {
				if adPlanRule.Id == 0 {
					batchAddPlanRule = append(batchAddPlanRule, &model.AdPlanRuleAddReq{
						PlanId:      planId,
						TimeScope:   adPlanRule.TimeScope,
						MetricsName: adPlanRule.MetricsName,
						Operator:    adPlanRule.Operator,
						Unit:        adPlanRule.Unit,
						TargetValue: adPlanRule.TargetValue,
					})
				} else {
					batchEditPlanRule = append(batchEditPlanRule, &model.AdPlanRuleEditReq{
						Id:          adPlanRule.Id,
						PlanId:      planId,
						TimeScope:   adPlanRule.TimeScope,
						MetricsName: adPlanRule.MetricsName,
						Operator:    adPlanRule.Operator,
						Unit:        adPlanRule.Unit,
						TargetValue: adPlanRule.TargetValue,
					})
					editIds = append(editIds, adPlanRule.Id)
				}
			}
			err = service.AdPlanRule().BatchAdd(ctx, batchAddPlanRule)
			liberr.ErrIsNil(ctx, err, "添加托管规则条件失败")
			// 编辑，id!=0的数据
			for _, editReq := range batchEditPlanRule {
				err = service.AdPlanRule().Edit(ctx, editReq)
				liberr.ErrIsNil(ctx, err, "修改托管规则条件失败")
			}
			// 删除，id在adPlanRules并且不在req.AdPlanRuleList里面的数据
			deleteIds := make([]int, 0)
			for _, adPlanRule := range adPlanRules {
				if !libUtils.FindTargetInt(editIds, adPlanRule.Id) {
					deleteIds = append(deleteIds, adPlanRule.Id)
				}
			}
			err = service.AdPlanRule().Delete(ctx, deleteIds)
			liberr.ErrIsNil(ctx, err, "删除托管规则失败")

			// 3. 修改ad_plan_execute表数据
			err = service.AdPlanExecute().Edit(ctx, &model.AdPlanExecuteEditReq{
				Id:             adPlanExecute.Id,
				PlanId:         planId,
				ExecuteType:    req.ExecuteType,
				AdjustmentType: req.AdjustmentType,
				ValueType:      req.ValueType,
				ExecuteValue:   req.ExecuteValue,
				LimitValue:     req.LimitValue,
				TimeDimension:  req.TimeDimension,
				ExecuteTimes:   req.ExecuteTimes,
			})
			liberr.ErrIsNil(ctx, err, "修改托管执行失败")

			// 4. 修改ad_reward_setting和ad_setting表数据
			if adPlanChannelExecute != nil {
				var rewardId = adPlanChannelExecute.RewardId
				var adSettingId = adPlanChannelExecute.AdSettingId
				if req.AdSetting != nil {
					err = channelService.AdSetting().Edit(ctx, &channelModel.AdSettingEditReq{
						Id:                           adPlanChannelExecute.AdSettingId,
						UserActionSetAppName:         req.AdSetting.UserActionSetAppName,
						OrderReturnRateType:          req.AdSetting.OrderReturnRateType,
						AdActive:                     req.AdSetting.AdActive,
						ReturnRate:                   req.AdSetting.ReturnRate,
						MinReturnAmount:              req.AdSetting.MinReturnAmount,
						MaxReturnAmount:              req.AdSetting.MaxReturnAmount,
						CustomReportFlag:             req.AdSetting.CustomReportFlag,
						AdAction:                     req.AdSetting.AdAction,
						OpenFlag:                     req.AdSetting.OpenFlag,
						LowOrderReturnRateTrue:       req.AdSetting.LowOrderReturnRateTrue,
						LowOrderReturnRateFalse:      req.AdSetting.LowOrderReturnRateFalse,
						MiddleOrderReturnRateTrue:    req.AdSetting.MiddleOrderReturnRateTrue,
						MiddleOrderReturnRateFalse:   req.AdSetting.MiddleOrderReturnRateFalse,
						HighOrderReturnRateTrue:      req.AdSetting.HighOrderReturnRateTrue,
						HighOrderReturnRateFalse:     req.AdSetting.HighOrderReturnRateFalse,
						LowMinReturnAmount:           req.AdSetting.LowMinReturnAmount,
						LowMaxReturnAmount:           req.AdSetting.LowMaxReturnAmount,
						MiddleMinReturnAmount:        req.AdSetting.MiddleMinReturnAmount,
						MiddleMaxReturnAmount:        req.AdSetting.MiddleMaxReturnAmount,
						HighMinReturnAmount:          req.AdSetting.HighMinReturnAmount,
						HighMaxReturnAmount:          req.AdSetting.HighMaxReturnAmount,
						LowOpenFlag:                  req.AdSetting.LowOpenFlag,
						MiddleOpenFlag:               req.AdSetting.MiddleOpenFlag,
						HighOpenFlag:                 req.AdSetting.HighOpenFlag,
						TopMinReturnAmount:           req.AdSetting.TopMinReturnAmount,
						TopMaxReturnAmount:           req.AdSetting.TopMaxReturnAmount,
						ReturnType:                   req.AdSetting.ReturnType,
						TopOrderReturnRateTrue:       req.AdSetting.TopOrderReturnRateTrue,
						TopOrderReturnRateFalse:      req.AdSetting.TopOrderReturnRateFalse,
						OldCustomReportFlag:          req.AdSetting.OldCustomReportFlag,
						RewardOpenFlag:               req.AdSetting.RewardOpenFlag,
						RewardId:                     adPlanChannelExecute.RewardId,
						Up:                           req.AdSetting.Up,
						Pv:                           req.AdSetting.Pv,
						Ecpm:                         req.AdSetting.Ecpm,
						UpFlag:                       req.AdSetting.UpFlag,
						PvFlag:                       req.AdSetting.PvFlag,
						EcpmFlag:                     req.AdSetting.EcpmFlag,
						AdCallbackConfig:             req.AdSetting.AdCallbackConfig,
						CallbackNum:                  req.AdSetting.CallbackNum,
						UnCallbackNum:                req.AdSetting.UnCallbackNum,
						OldUserCallbackFlag:          req.AdSetting.OldUserCallbackFlag,
						OldUserAdCallbackConfig:      req.AdSetting.OldUserAdCallbackConfig,
						Dimension:                    req.AdSetting.Dimension,
						ProtectionSetting:            req.AdSetting.ProtectionSetting,
						DailyNonCallbackQuantity:     req.AdSetting.DailyNonCallbackQuantity,
						SingleNonCallbackQuantity:    req.AdSetting.SingleNonCallbackQuantity,
						IaaDimension:                 req.AdSetting.IaaDimension,
						IaaProtectionSetting:         req.AdSetting.IaaProtectionSetting,
						IaaDailyNonCallbackQuantity:  req.AdSetting.IaaDailyNonCallbackQuantity,
						IaaSingleNonCallbackQuantity: req.AdSetting.IaaSingleNonCallbackQuantity,
					})
					liberr.ErrIsNil(ctx, err, "修改广告配置失败")
				} else {
					rewardId = 0
					adSettingId = 0
				}
				// 5. 修改ad_plan_channel_execute表数据
				err = service.AdPlanChannelExecute().Edit(ctx, &model.AdPlanChannelExecuteEditReq{
					Id:          adPlanChannelExecute.Id,
					PlanId:      planId,
					ChannelList: req.ChannelList,
					TemplateId:  req.TemplateId,
					SinglePrice: req.SinglePrice,
					LockNum:     req.LockNum,
					RewardId:    rewardId,
					AdSettingId: adSettingId,
				})
				liberr.ErrIsNil(ctx, err, "修改渠道托管执行失败")
			} else {
				// 之前没有打开调整渠道开关，编辑打开了，需要新增
				if req.AdSetting != nil {
					addReq := &model.AdPlanSettingAddReq{}
					err = gconv.Struct(req.AdSetting, addReq)
					liberr.ErrIsNil(ctx, err, "结构体转换失败")
					s.AddChannelExecute(ctx, addReq, planId)
				}
			}
		})
		if err == nil {
			err = s.PushToPlanQueue(ctx, &model.AdPlanSettingInfoRes{
				Id:           gconv.Int(planId),
				RunFrequency: req.RunFrequency,
			})
			g.Log().Errorf(ctx, "添加到托管队列失败err:%v", err)
		}
		return err
	})
	return
}

func (s *sAdPlanSetting) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanSetting.Ctx(ctx).Delete(dao.AdPlanSetting.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdPlanSetting) UpdateStatus(ctx context.Context, ids []int, status int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanSetting.Ctx(ctx).WhereIn(dao.AdPlanSetting.Columns().Id, ids).
			Update(do.AdPlanSetting{
				Status: status,
			})
		liberr.ErrIsNil(ctx, err, "更新状态失败")
	})
	return
}

func (s *sAdPlanSetting) GetManagedObjectList(ctx context.Context, req *model.GetManagedObjectListReq) (listRes *model.GetManagedObjectListRes, err error) {
	listRes = new(model.GetManagedObjectListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		var planSetting *model.AdPlanSettingInfoRes
		err = dao.AdPlanSetting.Ctx(ctx).Where(dao.AdPlanSetting.Columns().Id, req.RuleId).Scan(&planSetting)
		liberr.ErrIsNil(ctx, err, "获取规则配置失败")
		listRes.List = make([]*model.GetManagedObjectInfo, 0)
		scopeEntityIds := gstr.Split(planSetting.ScopeEntityIds, commonConsts.Delimiter)
		listRes.CurrentPage = req.PageNum
		listRes.Total = len(scopeEntityIds)
		pageIds := make([]string, 0)
		linq.From(scopeEntityIds).Skip((req.PageNum - 1) * req.PageSize).Take(req.PageSize).ToSlice(&pageIds)
		if planSetting.ScopeType == 2 {
			switch planSetting.ScopeObjectType {
			case 1:
				advertiserRes, err1 := oceanService.AdAdvertiserAccount().List(ctx, &oceanModel.AdAdvertiserAccountSearchReq{
					PageReq: comModel.PageReq{
						PageNum:  req.PageNum,
						PageSize: req.PageSize,
					},
					AdvertiserIds: pageIds,
				})
				liberr.ErrIsNil(ctx, err1, "获取广告主列表失败")
				for _, res := range advertiserRes.List {
					listRes.List = append(listRes.List, &model.GetManagedObjectInfo{
						Id:   res.AdvertiserId,
						Name: res.AdvertiserNick,
					})
				}
				break
			case 2:
				projectRes, err1 := oceanService.AdProject().List(ctx, &oceanModel.AdProjectSearchReq{
					PageReq: comModel.PageReq{
						PageNum:  req.PageNum,
						PageSize: req.PageSize,
					},
					Ids: pageIds,
				})
				liberr.ErrIsNil(ctx, err1, "获取项目列表失败")
				for _, res := range projectRes.List {
					listRes.List = append(listRes.List, &model.GetManagedObjectInfo{
						Id:   res.ProjectId,
						Name: res.Name,
					})
				}
				break
			case 3:
				promotionRes, err1 := oceanService.AdPromotion().List(ctx, &oceanModel.AdPromotionSearchReq{
					PageReq: comModel.PageReq{
						PageNum:  req.PageNum,
						PageSize: req.PageSize,
					},
					Ids: pageIds,
				})
				liberr.ErrIsNil(ctx, err1, "获取广告列表失败")
				for _, res := range promotionRes.List {
					listRes.List = append(listRes.List, &model.GetManagedObjectInfo{
						Id:   res.PromotionId,
						Name: res.PromotionName,
					})
				}
				break
			}
		}
	})
	return
}

func (s *sAdPlanSetting) PushNotifyTest(ctx context.Context, req *model.PushNotifyTestReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		signName := g.Cfg().MustGet(ctx, "sms.signName").String()
		templateCode := g.Cfg().MustGet(ctx, "sms.adTemplateCode").String()
		message := notify.Message{
			UserIds:  []int{req.UserId},
			PhoneNos: strings.Split(req.PhoneNo, commonConsts.Delimiter),
			Emails:   strings.Split(req.Email, commonConsts.Delimiter),
			Title:    commonConsts.AdNotifyTitle,
			Content:  commonConsts.AdNotifyTitle,
			TemplateParam: map[string]string{
				"ruleName":   "",
				"targetName": commonConsts.ObjectTypeMapping[1],
				"condition":  "",
			},
			SignName:     signName,
			TemplateCode: templateCode,
		}
		err = notify.SendMsgByType(ctx, req.NotifyType, message)
		liberr.ErrIsNil(ctx, err, "发送通知失败")
	})
	return
}

// SendNotify 发送通知
func (s *sAdPlanSetting) SendNotify(ctx context.Context, ruleId int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var adPlanSetting *model.AdPlanSettingInfoRes
		err = dao.AdPlanSetting.Ctx(ctx).Where(dao.AdPlanSetting.Columns().Id, ruleId).Scan(&adPlanSetting)
		liberr.ErrIsNil(ctx, err, "获取规则配置失败")
		planRules, err1 := service.AdPlanRule().GetByPlanId(ctx, ruleId)
		liberr.ErrIsNil(ctx, err1, "获取规则条件失败")
		msgContent, err1 := s.GetAdMsgContent(ctx, adPlanSetting, planRules)
		liberr.ErrIsNil(ctx, err1, "获取通知内容失败")
		notifyTypes := strings.Split(adPlanSetting.ModeOfNotification, commonConsts.Delimiter)
		signName := g.Cfg().MustGet(ctx, "sms.signName").String()
		templateCode := g.Cfg().MustGet(ctx, "sms.adTemplateCode").String()
		for _, notifyType := range notifyTypes {
			message := notify.Message{
				UserIds:       []int{adPlanSetting.UserId},
				PhoneNos:      strings.Split(adPlanSetting.PhoneNo, commonConsts.Delimiter),
				Emails:        strings.Split(adPlanSetting.Email, commonConsts.Delimiter),
				Title:         msgContent.Title,
				Content:       msgContent.Content,
				TemplateParam: msgContent.TemplateParam,
				SignName:      signName,
				TemplateCode:  templateCode,
			}
			err = notify.SendMsgByType(ctx, notify.NotifyType(gconv.Int(notifyType)), message)
		}
	})
	return
}

func (s *sAdPlanSetting) GetAdMsgContent(ctx context.Context, adPlanSetting *model.AdPlanSettingInfoRes, planRules []*model.AdPlanRuleInfoRes) (res *model.MsgContent, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.MsgContent)
		data := &model.MsgTemplate{
			RuleName:   adPlanSetting.RuleName,
			TargetName: commonConsts.ObjectTypeMapping[adPlanSetting.ObjectType],
		}
		var condition string
		for index, planRule := range planRules {
			var format = "%s%s%s%.2f%s"
			if index > 0 {
				format = "且%s%s%s%.2f%s"
			}
			condition += fmt.Sprintf(format,
				commonConsts.TimeScopeMapping[planRule.TimeScope],
				commonConsts.MetricsNameMapping[planRule.MetricsName],
				planRule.Operator, planRule.TargetValue, planRule.Unit)
		}
		data.Condition = condition
		// 获取当前目录
		dir, _ := filepath.Abs(filepath.Dir("."))
		filePath := filepath.Join(dir, "resource/template/system/ad_plan_notify.template")
		// 解析模板
		tmpl, err2 := template.ParseFiles(filePath)
		liberr.ErrIsNil(ctx, err2, "解析模板失败")
		var buf bytes.Buffer
		err2 = tmpl.Execute(&buf, data)
		liberr.ErrIsNil(ctx, err2, "执行模板失败")
		res.Content = buf.String()
		res.Title = commonConsts.AdNotifyTitle
		// 阿里短信变量长度不能超过35个字符
		var msgCondition = data.Condition
		if utf8.RuneCountInString(msgCondition) > 35 {
			msgCondition = gstr.SubStr(msgCondition, 0, 32) + "..."
		}
		res.TemplateParam = map[string]string{
			"ruleName":   data.RuleName,
			"targetName": data.TargetName,
			"condition":  msgCondition,
		}
	})
	return
}
