// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-16 10:20:02
// 生成路径: api/v1/channel/m_member_ad_callback_statistics.go
// 生成人：cq
// desc:用户广告回传统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdCallbackStatisticsSearchReq 分页请求参数
type MMemberAdCallbackStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"用户广告回传统计" method:"post" summary:"渠道广告统计列表"`
	commonApi.Author
	model.MMemberAdCallbackStatisticsSearchReq
}

// MMemberAdCallbackStatisticsSearchRes 列表返回结果
type MMemberAdCallbackStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackStatisticsSearchRes
}

// MMemberAdCallbackStatisticsExportReq 导出请求
type MMemberAdCallbackStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"用户广告回传统计" method:"post" summary:"渠道广告统计导出"`
	commonApi.Author
	model.MMemberAdCallbackStatisticsSearchReq
}

// MMemberAdCallbackStatisticsExportRes 导出响应
type MMemberAdCallbackStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackStatisticsTaskReq 用户广告回传统计请求参数
type MMemberAdCallbackStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"用户广告回传统计" method:"post" summary:"用户广告回传统计任务"`
	commonApi.Author
	model.MMemberAdCallbackStatisticsSearchReq
}

// MMemberAdCallbackStatisticsTaskRes 用户广告回传统计返回结果
type MMemberAdCallbackStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}

// MMemberAdCallbackPitcherStatisticsReq 投手广告统计请求参数
type MMemberAdCallbackPitcherStatisticsReq struct {
	g.Meta `path:"/pitcher/stat" tags:"用户广告回传统计" method:"post" summary:"投手广告统计列表"`
	commonApi.Author
	model.MMemberAdCallbackPitcherStatisticsReq
}

// MMemberAdCallbackPitcherStatisticsRes 投手广告统计返回结果
type MMemberAdCallbackPitcherStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackPitcherStatisticsRes
}

// MMemberAdCallbackPitcherStatisticsExportReq 投手广告统计导出请求
type MMemberAdCallbackPitcherStatisticsExportReq struct {
	g.Meta `path:"/pitcher/export" tags:"用户广告回传统计" method:"post" summary:"投手广告统计导出"`
	commonApi.Author
	model.MMemberAdCallbackPitcherStatisticsReq
}

// MMemberAdCallbackPitcherStatisticsExportRes 投手广告统计导出响应
type MMemberAdCallbackPitcherStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackDistributorStatisticsReq 分销广告统计请求参数
type MMemberAdCallbackDistributorStatisticsReq struct {
	g.Meta `path:"/distributor/stat" tags:"用户广告回传统计" method:"post" summary:"分销广告统计列表"`
	commonApi.Author
	model.MMemberAdCallbackDistributorStatisticsReq
}

// MMemberAdCallbackDistributorStatisticsRes 分销广告统计返回结果
type MMemberAdCallbackDistributorStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackDistributorStatisticsRes
}

// MMemberAdCallbackDistributorStatisticsExportReq 分销广告统计导出请求
type MMemberAdCallbackDistributorStatisticsExportReq struct {
	g.Meta `path:"/distributor/export" tags:"用户广告回传统计" method:"post" summary:"分销广告统计导出"`
	commonApi.Author
	model.MMemberAdCallbackDistributorStatisticsReq
}

// MMemberAdCallbackDistributorStatisticsExportRes 分销广告统计导出响应
type MMemberAdCallbackDistributorStatisticsExportRes struct {
	commonApi.EmptyRes
}
