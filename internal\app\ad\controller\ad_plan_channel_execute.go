// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-27 11:19:17
// 生成路径: internal/app/ad/controller/ad_plan_channel_execute.go
// 生成人：cq
// desc:广告渠道执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adPlanChannelExecuteController struct {
	systemController.BaseController
}

var AdPlanChannelExecute = new(adPlanChannelExecuteController)

// List 列表
func (c *adPlanChannelExecuteController) List(ctx context.Context, req *ad.AdPlanChannelExecuteSearchReq) (res *ad.AdPlanChannelExecuteSearchRes, err error) {
	res = new(ad.AdPlanChannelExecuteSearchRes)
	res.AdPlanChannelExecuteSearchRes, err = service.AdPlanChannelExecute().List(ctx, &req.AdPlanChannelExecuteSearchReq)
	return
}

// Get 获取广告渠道执行配置
func (c *adPlanChannelExecuteController) Get(ctx context.Context, req *ad.AdPlanChannelExecuteGetReq) (res *ad.AdPlanChannelExecuteGetRes, err error) {
	res = new(ad.AdPlanChannelExecuteGetRes)
	res.AdPlanChannelExecuteInfoRes, err = service.AdPlanChannelExecute().GetById(ctx, req.Id)
	return
}

// Add 添加广告渠道执行配置
func (c *adPlanChannelExecuteController) Add(ctx context.Context, req *ad.AdPlanChannelExecuteAddReq) (res *ad.AdPlanChannelExecuteAddRes, err error) {
	err = service.AdPlanChannelExecute().Add(ctx, req.AdPlanChannelExecuteAddReq)
	return
}

// Edit 修改广告渠道执行配置
func (c *adPlanChannelExecuteController) Edit(ctx context.Context, req *ad.AdPlanChannelExecuteEditReq) (res *ad.AdPlanChannelExecuteEditRes, err error) {
	err = service.AdPlanChannelExecute().Edit(ctx, req.AdPlanChannelExecuteEditReq)
	return
}

// Delete 删除广告渠道执行配置
func (c *adPlanChannelExecuteController) Delete(ctx context.Context, req *ad.AdPlanChannelExecuteDeleteReq) (res *ad.AdPlanChannelExecuteDeleteRes, err error) {
	err = service.AdPlanChannelExecute().Delete(ctx, req.Ids)
	return
}

// AdPlanExecute 调整渠道模板/单集价格/付费集数/回传配置
func (c *adPlanChannelExecuteController) AdPlanExecute(ctx context.Context, req *ad.AdPlanChannelExecuteReq) (res *ad.AdPlanChannelExecuteRes, err error) {
	_, err = service.AdPlanChannelExecute().AdPlanExecute(ctx, req.Id)
	return
}
