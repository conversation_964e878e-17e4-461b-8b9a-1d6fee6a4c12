/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsStarTaskSettlementConfigV2StarTaskExternalAction
type ToolsStarTaskSettlementConfigV2StarTaskExternalAction string

// List of tools_star_task_settlement_config_v2_star_task_external_action
const (
	AD_CONVERT_TYPE_ACTIVE_ToolsStarTaskSettlementConfigV2StarTaskExternalAction          ToolsStarTaskSettlementConfigV2StarTaskExternalAction = "AD_CONVERT_TYPE_ACTIVE"
	AD_CONVERT_TYPE_ACTIVE_REGISTER_ToolsStarTaskSettlementConfigV2StarTaskExternalAction ToolsStarTaskSettlementConfigV2StarTaskExternalAction = "AD_CONVERT_TYPE_ACTIVE_REGISTER"
	AD_CONVERT_TYPE_PAY_ToolsStarTaskSettlementConfigV2StarTaskExternalAction             ToolsStarTaskSettlementConfigV2StarTaskExternalAction = "AD_CONVERT_TYPE_PAY"
)

// Ptr returns reference to tools_star_task_settlement_config_v2_star_task_external_action value
func (v ToolsStarTaskSettlementConfigV2StarTaskExternalAction) Ptr() *ToolsStarTaskSettlementConfigV2StarTaskExternalAction {
	return &v
}
