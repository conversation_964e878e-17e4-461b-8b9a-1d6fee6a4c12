// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: api/v1/adx/adx_material.go
// 生成人：cq
// desc:ADX素材信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxMaterialSearchReq 分页请求参数
type AdxMaterialSearchReq struct {
	g.Meta `path:"/list" tags:"ADX素材信息表" method:"post" summary:"ADX素材信息表列表"`
	commonApi.Author
	model.AdxMaterialSearchReq
}

// AdxMaterialSearchRes 列表返回结果
type AdxMaterialSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxMaterialSearchRes
}

// AdxMaterialExportReq 导出请求
type AdxMaterialExportReq struct {
	g.Meta `path:"/export" tags:"ADX素材信息表" method:"post" summary:"ADX素材信息表导出"`
	commonApi.Author
	model.AdxMaterialSearchReq
}

// AdxMaterialExportRes 导出响应
type AdxMaterialExportRes struct {
	commonApi.EmptyRes
}

// AdxMaterialAddReq 添加操作请求参数
type AdxMaterialAddReq struct {
	g.Meta `path:"/add" tags:"ADX素材信息表" method:"post" summary:"ADX素材信息表添加"`
	commonApi.Author
	*model.AdxMaterialAddReq
}

// AdxMaterialAddRes 添加操作返回结果
type AdxMaterialAddRes struct {
	commonApi.EmptyRes
}

// AdxMaterialEditReq 修改操作请求参数
type AdxMaterialEditReq struct {
	g.Meta `path:"/edit" tags:"ADX素材信息表" method:"put" summary:"ADX素材信息表修改"`
	commonApi.Author
	*model.AdxMaterialEditReq
}

// AdxMaterialEditRes 修改操作返回结果
type AdxMaterialEditRes struct {
	commonApi.EmptyRes
}

// AdxMaterialGetReq 获取一条数据请求
type AdxMaterialGetReq struct {
	g.Meta `path:"/get" tags:"ADX素材信息表" method:"get" summary:"获取ADX素材信息表信息"`
	commonApi.Author
	MaterialId uint64 `p:"materialId" v:"required#主键必须"` //通过主键获取
}

// AdxMaterialGetRes 获取一条数据结果
type AdxMaterialGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxMaterialInfoRes
}

// AdxMaterialDeleteReq 删除数据请求
type AdxMaterialDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX素材信息表" method:"post" summary:"删除ADX素材信息表"`
	commonApi.Author
	MaterialIds []uint64 `p:"materialIds" v:"required#主键必须"` //通过主键删除
}

// AdxMaterialDeleteRes 删除数据返回
type AdxMaterialDeleteRes struct {
	commonApi.EmptyRes
}

// AdxMaterialSyncReq 同步ADX素材请求参数
type AdxMaterialSyncReq struct {
	g.Meta `path:"/sync" tags:"ADX素材信息表" method:"post" summary:"同步ADX素材"`
	commonApi.Author
	model.AdxMaterialSearchReq
}

// AdxMaterialSyncRes 同步ADX素材返回结果
type AdxMaterialSyncRes struct {
	g.Meta `mime:"application/json"`
}
