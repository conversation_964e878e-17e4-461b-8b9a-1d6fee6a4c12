// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-25 10:56:08
// 生成路径: api/v1/channel/s_channel_recharge_statistics.go
// 生成人：len
// desc:渠道充值统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelRechargeStatisticsSearchReq 分页请求参数
type SChannelRechargeStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"渠道充值统计" method:"get" summary:"渠道充值统计列表"`
	commonApi.Author
	model.SChannelRechargeStatisticsSearchReq
}

// SChannelRechargeStatisticsSearchRes 列表返回结果
type SChannelRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelRechargeStatisticsSearchRes
}

// SChannelRechargeStatisticsExportReq 导出请求
type SChannelRechargeStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"渠道充值统计" method:"get" summary:"渠道充值统计导出"`
	commonApi.Author
	model.SChannelRechargeStatisticsSearchReq
}

// SChannelRechargeStatisticsExportRes 导出响应
type SChannelRechargeStatisticsExportRes struct {
	commonApi.EmptyRes
}

// SChannelRechargeStatisticsAddReq 添加操作请求参数
type SChannelRechargeStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"渠道充值统计" method:"post" summary:"渠道充值统计添加"`
	commonApi.Author
	*model.SChannelRechargeStatisticsAddReq
}

// SChannelRechargeStatisticsAddRes 添加操作返回结果
type SChannelRechargeStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SChannelRechargeStatisticsEditReq 修改操作请求参数
type SChannelRechargeStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"渠道充值统计" method:"put" summary:"渠道充值统计修改"`
	commonApi.Author
	*model.SChannelRechargeStatisticsEditReq
}

// SChannelRechargeStatisticsEditRes 修改操作返回结果
type SChannelRechargeStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SChannelRechargeStatisticsGetReq 获取一条数据请求
type SChannelRechargeStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"渠道充值统计" method:"get" summary:"获取渠道充值统计信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelRechargeStatisticsGetRes 获取一条数据结果
type SChannelRechargeStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelRechargeStatisticsInfoRes
}

// SChannelRechargeStatisticsDeleteReq 删除数据请求
type SChannelRechargeStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道充值统计" method:"post" summary:"删除渠道充值统计"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelRechargeStatisticsDeleteRes 删除数据返回
type SChannelRechargeStatisticsDeleteRes struct {
	commonApi.EmptyRes
}

type PitcherRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.PitcherRechargeStatisticsSearchRes
}

// PitcherRechargeStatisticsSearchReq 投手充值统计查询
type PitcherRechargeStatisticsSearchReq struct {
	g.Meta `path:"/pitcherStat" tags:"渠道充值统计" method:"post" summary:"投手充值统计"`
	commonApi.Author
	model.PitcherRechargeStatisticsSearchReq
}
type PitcherRechargeStatisticsExportReq struct {
	g.Meta `path:"/pitcherExport" tags:"渠道充值统计" method:"get" summary:"投手充值导出"`
	commonApi.Author
	model.PitcherRechargeStatisticsSearchReq
}

// SChannelRechargeStatisticsReq   渠道充值统计
type SChannelRechargeStatisticsReq struct {
	g.Meta `path:"/channelStat" tags:"分销统计" method:"post" summary:"渠道充值统计"`
	commonApi.Author
	model.ChannelRechargeStatisticsReq
}

// SChannelRechargeStatisticsRes  列表返回结果
type SChannelRechargeStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelRechargeStatisticsRes
}
type SChannelRechargeExportReq struct {
	g.Meta `path:"/channelExport" tags:"渠道充值统计" method:"get" summary:"渠道充值导出"`
	commonApi.Author
	model.ChannelRechargeStatisticsReq
}

// RechargeStatisticsSearchReq 投手充值统计查询
type RechargeStatisticsSearchReq struct {
	g.Meta `path:"/rechargeStatics" tags:"充值统计" method:"post" summary:"充值统计"`
	commonApi.Author
	model.RechargeStatisticsSearchReq
}
type RechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.RechargeStatisticsSearchRes
}

// TheaterPitcherRechargeReq 剧充值统计查询
type TheaterPitcherRechargeReq struct {
	g.Meta `path:"/getTheaterPitcherRecharge" tags:"充值统计" method:"post" summary:"查询剧充值统计"`
	commonApi.Author
	model.TheaterPitcherRechargeReq
}

// TheaterPitcherRechargeExportReq 剧充值导出
type TheaterPitcherRechargeExportReq struct {
	g.Meta `path:"/exportTheaterPitcherRecharge" tags:"充值统计" method:"post" summary:"导出剧充值统计"`
	commonApi.Author
	model.TheaterPitcherRechargeReq
}

type OceanCallbackValidReq struct {
	g.Meta `path:"/ocean/callback" tags:"充值统计" method:"get" summary:"巨量引擎回调验证"`
	commonApi.Author
	*model.OceanCallbackValidReq
}

type OceanCallbackValidRes struct {
	commonApi.EmptyRes
	*model.OceanCallbackValidRes
}

type OceanCallbackReq struct {
	g.Meta `path:"/ocean/callback" tags:"充值统计" method:"post" summary:"巨量引擎消耗回调"`
	commonApi.Author
}

type OceanCallbackRes struct {
	commonApi.EmptyRes
	*model.OceanCallbackValidRes
}

type DistributorReconciliationStatReq struct {
	g.Meta `path:"/distributor/reconciliation" tags:"财务对账" method:"get" summary:"分销对账"`
	commonApi.Author
	model.DistributorReconciliationStatReq
}

// DistributorReconciliationStatExportReq 导出请求
type DistributorReconciliationStatExportReq struct {
	g.Meta `path:"/distributor/reconciliation/export" tags:"财务对账" method:"get" summary:"财务对账分销对账导出"`
	commonApi.Author
	model.DistributorReconciliationStatReq
}

type DistributorReconciliationStatRes struct {
	g.Meta `mime:"application/json"`
	*model.DistributorReconciliationStatRes
}

type UpdateAdvertiserRecordReq struct {
	g.Meta `path:"/update/advertiser/record" tags:"充值统计" method:"post" summary:"更新渠道广告主记录"`
	commonApi.Author
	StatDate string `p:"statDate" v:"required#开始时间必须"`
	Channel  string `p:"channel"`
}

type UpdateAdvertiserRecordRes struct {
	commonApi.EmptyRes
}
