// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-22 18:08:01
// 生成路径: api/v1/system/member_view_records.go
// 生成人：cyao
// desc:用户观看记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MemberViewRecordsSearchReq 分页请求参数
type MemberViewRecordsSearchReq struct {
	g.Meta `path:"/list" tags:"用户观看记录" method:"get" summary:"用户观看记录列表"`
	commonApi.Author
	model.MemberViewRecordsSearchReq
}

// MemberViewRecordsSearchRes 列表返回结果
type MemberViewRecordsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MemberViewRecordsSearchRes
}

// MemberViewRecordsExportReq 导出请求
type MemberViewRecordsExportReq struct {
	g.Meta `path:"/export" tags:"用户观看记录" method:"get" summary:"用户观看记录导出"`
	commonApi.Author
	model.MemberViewRecordsSearchReq
}

// MemberViewRecordsExportRes 导出响应
type MemberViewRecordsExportRes struct {
	commonApi.EmptyRes
}
type MemberViewRecordsExcelTemplateReq struct {
	g.Meta `path:"/excelTemplate" tags:"用户观看记录" method:"get" summary:"导出模板文件"`
	commonApi.Author
}
type MemberViewRecordsExcelTemplateRes struct {
	commonApi.EmptyRes
}
type MemberViewRecordsImportReq struct {
	g.Meta `path:"/import" tags:"用户观看记录" method:"post" summary:"用户观看记录导入"`
	commonApi.Author
	File *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}
type MemberViewRecordsImportRes struct {
	commonApi.EmptyRes
}

// MemberViewRecordsAddReq 添加操作请求参数
type MemberViewRecordsAddReq struct {
	g.Meta `path:"/add" tags:"用户观看记录" method:"post" summary:"用户观看记录添加"`
	commonApi.Author
	*model.MemberViewRecordsAddReq
}

// MemberViewRecordsAddRes 添加操作返回结果
type MemberViewRecordsAddRes struct {
	commonApi.EmptyRes
}

// MemberViewRecordsEditReq 修改操作请求参数
type MemberViewRecordsEditReq struct {
	g.Meta `path:"/edit" tags:"用户观看记录" method:"put" summary:"用户观看记录修改"`
	commonApi.Author
	*model.MemberViewRecordsEditReq
}

// MemberViewRecordsEditRes 修改操作返回结果
type MemberViewRecordsEditRes struct {
	commonApi.EmptyRes
}

// MemberViewRecordsGetReq 获取一条数据请求
type MemberViewRecordsGetReq struct {
	g.Meta `path:"/get" tags:"用户观看记录" method:"get" summary:"获取用户观看记录信息"`
	commonApi.Author
	SaasId string `p:"saasId" v:"required#主键必须"` //通过主键获取
}

// MemberViewRecordsGetRes 获取一条数据结果
type MemberViewRecordsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MemberViewRecordsInfoRes
}

// MemberViewRecordsDeleteReq 删除数据请求
type MemberViewRecordsDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户观看记录" method:"post" summary:"删除用户观看记录"`
	commonApi.Author
	SaasIds []string `p:"saasIds" v:"required#主键必须"` //通过主键删除
}

// MemberViewRecordsDeleteRes 删除数据返回
type MemberViewRecordsDeleteRes struct {
	commonApi.EmptyRes
}
