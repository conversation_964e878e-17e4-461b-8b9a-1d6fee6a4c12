package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/productDetail"
	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sAdvertiserConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sAppletRechargeStatistics"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sCouponCode"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sCustomerQrCodeConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sExchangeConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sTaskConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sysBanner"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/wxProductConfig"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sysActivePopUpWindow"

	_ "github.com/tiger1103/gfast/v3/internal/app/applet/logic/sAppletDepositRetentionStatistics"
)
