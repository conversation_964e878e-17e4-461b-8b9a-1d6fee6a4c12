// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-07-16 14:26:31
// 生成路径: internal/app/applet/controller/s_task_config.go
// 生成人：cq
// desc:任务配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sTaskConfigController struct {
	systemController.BaseController
}

var STaskConfig = new(sTaskConfigController)

// List 列表
func (c *sTaskConfigController) List(ctx context.Context, req *applet.STaskConfigSearchReq) (res *applet.STaskConfigSearchRes, err error) {
	res = new(applet.STaskConfigSearchRes)
	res.STaskConfigSearchRes, err = service.STaskConfig().List(ctx, &req.STaskConfigSearchReq)
	return
}

// Get 获取任务配置表
func (c *sTaskConfigController) Get(ctx context.Context, req *applet.STaskConfigGetReq) (res *applet.STaskConfigGetRes, err error) {
	res = new(applet.STaskConfigGetRes)
	res.STaskConfigInfoRes, err = service.STaskConfig().GetById(ctx, req.Id)
	return
}

// Add 添加任务配置表
func (c *sTaskConfigController) Add(ctx context.Context, req *applet.STaskConfigAddReq) (res *applet.STaskConfigAddRes, err error) {
	err = service.STaskConfig().Add(ctx, req.STaskConfigAddReq)
	return
}

// Edit 修改任务配置表
func (c *sTaskConfigController) Edit(ctx context.Context, req *applet.STaskConfigEditReq) (res *applet.STaskConfigEditRes, err error) {
	err = service.STaskConfig().Edit(ctx, req.STaskConfigEditReq)
	return
}

// Delete 删除任务配置表
func (c *sTaskConfigController) Delete(ctx context.Context, req *applet.STaskConfigDeleteReq) (res *applet.STaskConfigDeleteRes, err error) {
	err = service.STaskConfig().Delete(ctx, req.Ids)
	return
}
