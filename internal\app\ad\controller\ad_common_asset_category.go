// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: internal/app/ad/controller/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adCommonAssetCategoryController struct {
	systemController.BaseController
}

var AdCommonAssetCategory = new(adCommonAssetCategoryController)

// List 列表
func (c *adCommonAssetCategoryController) List(ctx context.Context, req *ad.AdCommonAssetCategorySearchReq) (res *ad.AdCommonAssetCategorySearchRes, err error) {
	res = new(ad.AdCommonAssetCategorySearchRes)
	res.AdCommonAssetCategorySearchRes, err = service.AdCommonAssetCategory().List(ctx, &req.AdCommonAssetCategorySearchReq)
	return
}

// Get 获取通用资产-标题分类
func (c *adCommonAssetCategoryController) Get(ctx context.Context, req *ad.AdCommonAssetCategoryGetReq) (res *ad.AdCommonAssetCategoryGetRes, err error) {
	res = new(ad.AdCommonAssetCategoryGetRes)
	res.AdCommonAssetCategoryInfoRes, err = service.AdCommonAssetCategory().GetById(ctx, req.Id)
	return
}

// Add 添加通用资产-标题分类
func (c *adCommonAssetCategoryController) Add(ctx context.Context, req *ad.AdCommonAssetCategoryAddReq) (res *ad.AdCommonAssetCategoryAddRes, err error) {
	err = service.AdCommonAssetCategory().Add(ctx, req.AdCommonAssetCategoryAddReq)
	return
}

// Edit 修改通用资产-标题分类
func (c *adCommonAssetCategoryController) Edit(ctx context.Context, req *ad.AdCommonAssetCategoryEditReq) (res *ad.AdCommonAssetCategoryEditRes, err error) {
	err = service.AdCommonAssetCategory().Edit(ctx, req.AdCommonAssetCategoryEditReq)
	return
}

// Delete 删除通用资产-标题分类
func (c *adCommonAssetCategoryController) Delete(ctx context.Context, req *ad.AdCommonAssetCategoryDeleteReq) (res *ad.AdCommonAssetCategoryDeleteRes, err error) {
	err = service.AdCommonAssetCategory().Delete(ctx, req.Ids)
	return
}
