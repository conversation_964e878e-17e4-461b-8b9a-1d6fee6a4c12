// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-04-03 11:14:03
// 生成路径: internal/app/applet/dao/sys_banner.go
// 生成人：cyao
// desc:新banner表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sysBannerDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sysBannerDao struct {
	*internal.SysBannerDao
}

var (
	// SysBanner is globally public accessible object for table tools_gen_table operations.
	SysBanner = sysBannerDao{
		internal.NewSysBannerDao(),
	}
)

// Fill with you ideas below.
