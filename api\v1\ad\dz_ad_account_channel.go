// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: api/v1/ad/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdAccountChannelSearchReq 分页请求参数
type DzAdAccountChannelSearchReq struct {
	g.Meta `path:"/list" tags:"点众渠道" method:"get" summary:"点众渠道列表"`
	commonApi.Author
	model.DzAdAccountChannelSearchReq
}

// DzAdAccountChannelSearchRes 列表返回结果
type DzAdAccountChannelSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountChannelSearchRes
}

// DzAdAccountChannelExportReq 导出请求
type DzAdAccountChannelExportReq struct {
	g.Meta `path:"/export" tags:"点众渠道" method:"get" summary:"点众渠道导出"`
	commonApi.Author
	model.DzAdAccountChannelSearchReq
}

// DzAdAccountChannelExportRes 导出响应
type DzAdAccountChannelExportRes struct {
	commonApi.EmptyRes
}

// DzAdAccountChannelAddReq 添加操作请求参数
type DzAdAccountChannelAddReq struct {
	g.Meta `path:"/add" tags:"点众渠道" method:"post" summary:"点众渠道添加"`
	commonApi.Author
	*model.DzAdAccountChannelAddReq
}
type DzAdAccountChannelSetAppIdReq struct {
	g.Meta `path:"/set/appId" tags:"点众渠道" method:"post" summary:"设置小程序ID"`
	commonApi.Author
	*model.DzAdAccountChannelSetAppIdReq
}

// DzAdAccountChannelAddRes 添加操作返回结果
type DzAdAccountChannelAddRes struct {
	commonApi.EmptyRes
}

type DzAdAccountChannelSetAppIdRes struct {
	commonApi.EmptyRes
}

// DzAdAccountChannelEditReq 修改操作请求参数
type DzAdAccountChannelEditReq struct {
	g.Meta `path:"/edit" tags:"点众渠道" method:"put" summary:"点众渠道修改"`
	commonApi.Author
	*model.DzAdAccountChannelEditReq
}

// DzAdAccountChannelEditRes 修改操作返回结果
type DzAdAccountChannelEditRes struct {
	commonApi.EmptyRes
}

// DzAdAccountChannelGetReq 获取一条数据请求
type DzAdAccountChannelGetReq struct {
	g.Meta `path:"/get" tags:"点众渠道" method:"get" summary:"获取点众渠道信息"`
	commonApi.Author
	ChannelId int64 `p:"channelId" v:"required#主键必须"` //通过主键获取
}

// DzAdAccountChannelGetRes 获取一条数据结果
type DzAdAccountChannelGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountChannelInfoRes
}

// DzAdAccountChannelDeleteReq 删除数据请求
type DzAdAccountChannelDeleteReq struct {
	g.Meta `path:"/delete" tags:"点众渠道" method:"delete" summary:"删除点众渠道"`
	commonApi.Author
	ChannelIds []int64 `p:"channelIds" v:"required#主键必须"` //通过主键删除
}

// DzAdAccountChannelDeleteRes 删除数据返回
type DzAdAccountChannelDeleteRes struct {
	commonApi.EmptyRes
}

// SyncDZAdAccountChannelReq 同步番茄渠道请求
type SyncDZAdAccountChannelReq struct {
	g.Meta `path:"/sync" tags:"点众渠道" method:"post" summary:"同步点众渠道"`
	commonApi.Author
}

// SyncDZAdAccountChannelRes 同步番茄渠道数据返回
type SyncDZAdAccountChannelRes struct {
	commonApi.EmptyRes
}

// AddDZAuthReq 点众渠道添加授权请求
type AddDZAuthReq struct {
	g.Meta `path:"/add/auth" tags:"点众渠道" method:"post" summary:"dz渠道添加授权"`
	commonApi.Author
	*model.DZAdAddAuthReq
}

// AddDZAuthRes 添加操作返回结果
type AddDZAuthRes struct {
	commonApi.EmptyRes
}
