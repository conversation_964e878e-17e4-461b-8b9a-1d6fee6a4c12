// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-22 11:52:02
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_campaign.go
// 生成人：cq
// desc:快手策略组-广告计划
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyCampaign(New())
}

func New() service.IKsAdvertiserStrategyCampaign {
	return &sKsAdvertiserStrategyCampaign{}
}

type sKsAdvertiserStrategyCampaign struct{}

func (s *sKsAdvertiserStrategyCampaign) List(ctx context.Context, req *model.KsAdvertiserStrategyCampaignSearchReq) (listRes *model.KsAdvertiserStrategyCampaignSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyCampaignSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyCampaign.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().Id+" = ?", req.Id)
		}
		if req.StrategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().StrategyId+" = ?", req.StrategyId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.CampaignType != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().CampaignType+" = ?", gconv.Int(req.CampaignType))
		}
		if req.AdType != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().AdType+" = ?", gconv.Int(req.AdType))
		}
		if req.AutoAdjust != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().AutoAdjust+" = ?", gconv.Int(req.AutoAdjust))
		}
		if req.AutoBuild != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().AutoBuild+" = ?", gconv.Int(req.AutoBuild))
		}
		if req.UnitNameRule != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().UnitNameRule+" = ?", req.UnitNameRule)
		}
		if req.CreativeNameRule != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().CreativeNameRule+" = ?", req.CreativeNameRule)
		}
		if req.AutoManage != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().AutoManage+" = ?", gconv.Int(req.AutoManage))
		}
		if req.BidType != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().BidType+" = ?", gconv.Int(req.BidType))
		}
		if req.CampaignName != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().CampaignName+" like ?", "%"+req.CampaignName+"%")
		}
		if req.AdUnitLimit != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().AdUnitLimit+" = ?", gconv.Int(req.AdUnitLimit))
		}
		if req.PutStatus != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().PutStatus+" = ?", gconv.Int(req.PutStatus))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyCampaign.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyCampaignListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyCampaignListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyCampaignListRes{
				Id:               v.Id,
				StrategyId:       v.StrategyId,
				TaskId:           v.TaskId,
				CampaignType:     v.CampaignType,
				AdType:           v.AdType,
				AutoAdjust:       v.AutoAdjust,
				AutoBuild:        v.AutoBuild,
				UnitNameRule:     v.UnitNameRule,
				CreativeNameRule: v.CreativeNameRule,
				AutoManage:       v.AutoManage,
				BidType:          v.BidType,
				DayBudget:        v.DayBudget,
				CampaignName:     v.CampaignName,
				AdUnitLimit:      v.AdUnitLimit,
				PutStatus:        v.PutStatus,
				CreatedAt:        v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyCampaign) GetById(ctx context.Context, id uint64) (res *model.KsAdvertiserStrategyCampaignInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyCampaign.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyCampaign.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCampaign) GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyCampaignInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyCampaign.Ctx(ctx).WithAll()
		if strategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().StrategyId+" = ?", strategyId)
		}
		if taskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyCampaign.Columns().TaskId+" = ?", taskId)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCampaign) Add(ctx context.Context, req *model.KsAdvertiserStrategyCampaignAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyCampaign.Ctx(ctx).Insert(do.KsAdvertiserStrategyCampaign{
			StrategyId:       req.StrategyId,
			TaskId:           req.TaskId,
			CampaignType:     req.CampaignType,
			AdType:           req.AdType,
			AutoAdjust:       req.AutoAdjust,
			AutoBuild:        req.AutoBuild,
			UnitNameRule:     req.UnitNameRule,
			CreativeNameRule: req.CreativeNameRule,
			AutoManage:       req.AutoManage,
			BidType:          req.BidType,
			DayBudget:        req.DayBudget,
			CampaignName:     req.CampaignName,
			AdUnitLimit:      req.AdUnitLimit,
			PutStatus:        req.PutStatus,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCampaign) Edit(ctx context.Context, req *model.KsAdvertiserStrategyCampaignEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyCampaign.Ctx(ctx).
			Where(dao.KsAdvertiserStrategyCampaign.Columns().StrategyId, req.StrategyId).
			Update(do.KsAdvertiserStrategyCampaign{
				TaskId:           req.TaskId,
				CampaignType:     req.CampaignType,
				AdType:           req.AdType,
				AutoAdjust:       req.AutoAdjust,
				AutoBuild:        req.AutoBuild,
				UnitNameRule:     req.UnitNameRule,
				CreativeNameRule: req.CreativeNameRule,
				AutoManage:       req.AutoManage,
				BidType:          req.BidType,
				DayBudget:        req.DayBudget,
				CampaignName:     req.CampaignName,
				AdUnitLimit:      req.AdUnitLimit,
				PutStatus:        req.PutStatus,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyCampaign) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyCampaign.Ctx(ctx).Delete(dao.KsAdvertiserStrategyCampaign.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
