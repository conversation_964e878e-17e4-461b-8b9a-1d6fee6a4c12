// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: api/v1/ad/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdXtTaskSettleSearchReq 分页请求参数
type AdXtTaskSettleSearchReq struct {
	g.Meta `path:"/list" tags:"星图任务结算数据" method:"post" summary:"星图任务结算数据列表"`
	commonApi.Author
	model.AdXtTaskSettleSearchReq
}

// AdXtTaskSettleSearchRes 列表返回结果
type AdXtTaskSettleSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtTaskSettleSearchRes
}

// AdXtTaskSettlePullReq 分页请求参数
type AdXtTaskSettlePullReq struct {
	g.Meta `path:"/pull" tags:"星图任务结算数据" method:"get" summary:"pull"`
	commonApi.Author
	AdvertiserId string `p:"advertiserId" v:"required#主键必须"` //通过主键获取
}

type AdXtTaskSettlePullDetailReq struct {
	g.Meta `path:"/pull/detail" tags:"星图任务结算数据" method:"get" summary:"pullDetail"`
	commonApi.Author
	AdvertiserId string `p:"advertiserId"` //通过主键获取
}

// AdXtTaskSettlePullDetailRes
type AdXtTaskSettlePullDetailRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettlePullRes
type AdXtTaskSettlePullRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// AdXtTaskSettleExportReq 导出请求
type AdXtTaskSettleExportReq struct {
	g.Meta `path:"/export" tags:"星图任务结算数据" method:"get" summary:"星图任务结算数据导出"`
	commonApi.Author
	model.AdXtTaskSettleSearchReq
}

// AdXtTaskSettleExportRes 导出响应
type AdXtTaskSettleExportRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettleAddReq 添加操作请求参数
type AdXtTaskSettleAddReq struct {
	g.Meta `path:"/add" tags:"星图任务结算数据" method:"post" summary:"星图任务结算数据添加"`
	commonApi.Author
	*model.AdXtTaskSettleAddReq
}

// AdXtTaskSettleAddRes 添加操作返回结果
type AdXtTaskSettleAddRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettleEditReq 修改操作请求参数
type AdXtTaskSettleEditReq struct {
	g.Meta `path:"/edit" tags:"星图任务结算数据" method:"put" summary:"星图任务结算数据修改"`
	commonApi.Author
	*model.AdXtTaskSettleEditReq
}

// AdXtTaskSettleEditRes 修改操作返回结果
type AdXtTaskSettleEditRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettleGetReq 获取一条数据请求
type AdXtTaskSettleGetReq struct {
	g.Meta `path:"/get" tags:"星图任务结算数据" method:"get" summary:"获取星图任务结算数据信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdXtTaskSettleGetRes 获取一条数据结果
type AdXtTaskSettleGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtTaskSettleInfoRes
}

// AdXtTaskSettleDeleteReq 删除数据请求
type AdXtTaskSettleDeleteReq struct {
	g.Meta `path:"/delete" tags:"星图任务结算数据" method:"delete" summary:"删除星图任务结算数据"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdXtTaskSettleDeleteRes 删除数据返回
type AdXtTaskSettleDeleteRes struct {
	commonApi.EmptyRes
}
