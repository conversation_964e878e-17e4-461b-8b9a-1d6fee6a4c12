// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: internal/app/adx/router/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/adx/controller"
)

func (router *Router) BindAdxTaskItemController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adxTaskItem", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdxTaskItem,
		)
	})
}
