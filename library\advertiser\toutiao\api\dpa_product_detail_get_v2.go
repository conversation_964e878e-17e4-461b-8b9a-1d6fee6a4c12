/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DpaProductDetailGetV2ApiService DpaProductDetailGetV2Api service
type DpaProductDetailGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DpaProductDetailGetV2Request
}

type DpaProductDetailGetV2Request struct {
	AdvertiserId *int64
	PlatformId   *int64
	Filtering    *models.DpaProductDetailGetV2Filtering
	Page         *int32
	PageSize     *int32
}

func (r *DpaProductDetailGetV2ApiService) SetCfg(cfg *conf.Configuration) *DpaProductDetailGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DpaProductDetailGetV2ApiService) DpaProductDetailGetV2Request(dpaProductDetailGetV2Request DpaProductDetailGetV2Request) *DpaProductDetailGetV2ApiService {
	r.Request = &dpaProductDetailGetV2Request
	return r
}

func (r *DpaProductDetailGetV2ApiService) AccessToken(accessToken string) *DpaProductDetailGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DpaProductDetailGetV2ApiService) Do() (data *models.DpaProductDetailGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dpa/product/detail/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.PlatformId == nil {
		return nil, errors.New("platformId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "platform_id", r.Request.PlatformId)
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.Request.Filtering)
	}
	if r.Request.Page != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.DpaProductDetailGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DpaProductDetailGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dpa/product/detail/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
