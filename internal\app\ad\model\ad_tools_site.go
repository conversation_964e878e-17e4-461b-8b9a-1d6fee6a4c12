// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/model/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdToolsSiteInfoRes is the golang structure for table ad_tools_site.
type AdToolsSiteInfoRes struct {
	gmeta.Meta   `orm:"table:ad_tools_site"`
	Id           int64  `orm:"id,primary" json:"id" dc:"ID"`                    // ID
	AdvertiserId int64  `orm:"advertiser_id" json:"advertiserId" dc:"广告主ID"` // 广告主ID
	SiteId       string `orm:"site_id" json:"siteId" dc:"站点ID"`               // 站点ID
	SiteName     string `orm:"site_name" json:"siteName" dc:"站点名称"`         // 站点名称
	Status       string `orm:"status" json:"status" dc:"状态"`                  // 状态
	SiteType     string `orm:"site_type" json:"siteType" dc:"站点类型"`         // 站点类型
	FunctionType string `orm:"function_type" json:"functionType" dc:"功能类型"` // 功能类型
	Thumbnail    string `orm:"thumbnail" json:"thumbnail" dc:"缩略图"`          // 缩略图
	MainUserId   int    `orm:"main_user_id" json:"mainUserId" dc:"主用户ID"`    // 主用户ID

	CreateTime *gtime.Time `orm:"create_time" json:"createTime" dc:"创建时间"` // 创建时间
	UpdateTime *gtime.Time `orm:"update_time" json:"updateTime" dc:"更新时间"` // 更新时间
}

type AdToolsSiteListRes struct {
	Id             int64       `json:"id" dc:"ID"`
	AdvertiserId   int64       `json:"advertiserId" dc:"广告主ID"`
	AdvertiserName string      `json:"advertiserName" dc:"广告主Name 媒体账户"`
	SiteId         string      `json:"siteId" dc:"站点ID"`
	SiteName       string      `json:"siteName" dc:"站点名称"`
	Status         string      `json:"status" dc:"状态"`
	SiteType       string      `json:"siteType" dc:"站点类型"`
	FunctionType   string      `json:"functionType" dc:"功能类型"`
	Thumbnail      string      `json:"thumbnail" dc:"缩略图"`
	MainUserId     int         `json:"mainUserId" dc:"主用户ID"`
	SiteUrl        string      `json:"siteUrl" dc:"url"`
	CreateTime     *gtime.Time `json:"createTime" dc:"创建时间"`
	UpdateTime     *gtime.Time `json:"updateTime" dc:"更新时间"`
}

type SynchronizeSiteReq struct {
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#媒体账户ID不能为空" dc:"媒体账户ID"`
}

type ToolsSiteHandselReq struct {
	// 广告主id，范围：1 <= advertiser_id <= MAX_INT64
	AdvertiserId int64 `json:"advertiserId"`
	// 站点id列表, min_size=1, max_size=20
	SiteIds []string `json:"siteIds"`
	// 目标广告主id列表, min_size=1, max_size=20
	TargetAdvertiserIds []string `json:"targetAdvertiserIds"`
}

type ToolsSiteHandselSuccess struct {
	OriginSiteID       string `json:"originSiteId"`
	TargetAdvertiserID string `json:"targetAdvertiserId"`
	SiteID             string `json:"siteId"`
}

type ToolsSiteHandselError struct {
	OriginSiteID       string `json:"originSiteId"`
	TargetAdvertiserID string `json:"targetAdvertiserId"`
	ErrorReason        string `json:"errorReason"`
}

type ToolsSiteHandselRes struct {
	SuccessList []ToolsSiteHandselSuccess `json:"successList"`
	ErrorList   []ToolsSiteHandselError   `json:"errorList"`
	RequestID   string                    `json:"requestId"`
}

// AdToolsSiteSearchReq 分页请求参数
type AdToolsSiteSearchReq struct {
	comModel.PageReq
	Id           string `p:"id" dc:"ID"`                                                                           //ID
	AdvertiserId string `p:"advertiserId" v:"advertiserId@integer#广告主ID需为整数" dc:"广告主ID"`                 //广告主ID
	SiteId       string `p:"siteId" dc:"站点ID"`                                                                   //站点ID
	SiteName     string `p:"siteName" dc:"站点名称"`                                                               //站点名称
	Status       string `p:"status"  dc:"状态"`                                                                    //状态
	SiteType     string `p:"siteType" dc:"站点类型"`                                                               //站点类型
	FunctionType string `p:"functionType" dc:"功能类型"`                                                           //功能类型
	Thumbnail    string `p:"thumbnail" dc:"缩略图"`                                                                //缩略图
	MainUserId   string `p:"mainUserId" v:"mainUserId@integer#主用户ID需为整数" dc:"主用户ID"`                     //主用户ID
	CreateTime   string `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	UpdateTime   string `p:"updateTime" v:"updateTime@datetime#更新时间需为YYYY-MM-DD hh:mm:ss格式" dc:"更新时间"` //更新时间
}

// AdToolsSiteSearchRes 列表返回结果
type AdToolsSiteSearchRes struct {
	comModel.ListRes
	List []*AdToolsSiteListRes `json:"list"`
}

type GetPreviewUrlReq struct {
	//advertiser_id
	AdvertiserId int64 `p:"advertiserId" v:"required#广告主ID不能为空" dc:"广告主ID"`
	//site_id
	SiteId string `p:"siteId" v:"required#站点ID不能为空" dc:"站点ID"`
}

type UpdateStatusReq struct {
	//advertiser_id
	AdvertiserId int64 `p:"advertiserId" v:"required#广告主ID不能为空" dc:"广告主ID"`
	//site_ids
	SiteIds []string `p:"siteIds" v:"required#站点ID不能为空" dc:"站点ID"`
	//published 发布  unpublished  下线 delete 删除  undeleted  恢复删除
	Status string `p:"status" v:"required#状态不能为空" dc:"状态"`
}
type UpdateStatusFail struct {
	Message string `json:"message"`
	SiteID  string `json:"site_id"`
}

type UpdateStatusRes struct {
	// 失败的list
	Fail []UpdateStatusFail `json:"fail"`
	// 成功的站点ID
	Success []string `json:"success"`
}

// AdToolsSiteAddReq 添加操作请求参数
type AdToolsSiteAddReq struct {
	AdvertiserId int64  `p:"advertiserId" v:"required#广告主ID不能为空" dc:"广告主ID"`
	SiteId       string `p:"siteId" v:"required#站点ID不能为空" dc:"站点ID"`
	SiteName     string `p:"siteName" v:"required#站点名称不能为空" dc:"站点名称"`
	Status       string `p:"status" v:"required#状态不能为空" dc:"状态"`
	SiteType     string `p:"siteType" v:"required#站点类型不能为空" dc:"站点类型"`
	FunctionType string `p:"functionType" v:"required#功能类型不能为空" dc:"功能类型"`
	Thumbnail    string `p:"thumbnail" v:"required#缩略图不能为空" dc:"缩略图"`
	MainUserId   int    `p:"mainUserId" v:"required#主用户ID不能为空" dc:"主用户ID"`

	CreateTime *gtime.Time `p:"createTime" v:"required#创建时间不能为空" dc:"创建时间"`
	UpdateTime *gtime.Time `p:"updateTime"  dc:"更新时间"`
}

// AdToolsSiteEditReq 修改操作请求参数
type AdToolsSiteEditReq struct {
	Id           int64       `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AdvertiserId int64       `p:"advertiserId" v:"required#广告主ID不能为空" dc:"广告主ID"`
	SiteId       string      `p:"siteId" v:"required#站点ID不能为空" dc:"站点ID"`
	SiteName     string      `p:"siteName" v:"required#站点名称不能为空" dc:"站点名称"`
	Status       string      `p:"status" v:"required#状态不能为空" dc:"状态"`
	SiteType     string      `p:"siteType" v:"required#站点类型不能为空" dc:"站点类型"`
	FunctionType string      `p:"functionType" v:"required#功能类型不能为空" dc:"功能类型"`
	Thumbnail    string      `p:"thumbnail" v:"required#缩略图不能为空" dc:"缩略图"`
	MainUserId   int         `p:"mainUserId" v:"required#主用户ID不能为空" dc:"主用户ID"`
	CreateTime   *gtime.Time `p:"createTime" v:"required#创建时间不能为空" dc:"创建时间"`
	UpdateTime   *gtime.Time `p:"updateTime"  dc:"更新时间"`
}
