// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: internal/app/ad/controller/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type ksAdSalerCopyRightController struct {
	systemController.BaseController
}

var KsAdSalerCopyRight = new(ksAdSalerCopyRightController)

// List 列表
func (c *ksAdSalerCopyRightController) List(ctx context.Context, req *ad.KsAdSalerCopyRightSearchReq) (res *ad.KsAdSalerCopyRightSearchRes, err error) {
	res = new(ad.KsAdSalerCopyRightSearchRes)
	res.KsAdSalerCopyRightSearchRes, err = service.KsAdSalerCopyRight().List(ctx, &req.KsAdSalerCopyRightSearchReq)
	return
}

// Export 导出excel
func (c *ksAdSalerCopyRightController) Export(ctx context.Context, req *ad.KsAdSalerCopyRightExportReq) (res *ad.KsAdSalerCopyRightExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.KsAdSalerCopyRightListRes
		//表头
		tableHead = []interface{}{"日期", "快手账号", "快手账号id", "分销商机构主体名称", "短剧名称", "分成比例", "付费金额", "版权方分成金额", "分销商分成金额"}
		excelData [][]interface{}
		//字典选项处理
	)
	if req.SeriesType == 1 {
		tableHead = []interface{}{"日期", "快手账号", "快手账号id", "分销商机构主体名称", "短剧名称", "分成比例", "IAA广告含返货LTV(元)", "IAA广告不含返货LTV(元)", "版权方分成LTV(元)", "分销方分成LTV"}
	}
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.KsAdSalerCopyRight().GetExportData(ctx, &req.KsAdSalerCopyRightSearchReq)
		if err != nil {
			return
		}
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			var dt []interface{}
			if req.SeriesType == 1 {
				dt = []interface{}{
					v.ReportDate,
					v.AdvertiserName,
					v.AdvertiserId,
					v.SalerEntityName,
					v.CopyrightSeriesName,
					v.SalerRateStr,
					v.MiniGameIaaPurchaseAmount,
					v.MiniGameIaaPurchaseAmountDivided,
					v.CopyrightMiniGameIaaPurchaseAmountDivided,
					v.DistributorPurchaseAmountDivided,
				}
			} else {
				dt = []interface{}{
					v.ReportDate,
					v.AdvertiserName,
					v.AdvertiserId,
					v.SalerEntityName,
					v.CopyrightSeriesName,
					v.SalerRateStr,
					v.PayAmt,
					v.CopyrightEventPayPurchaseAmount,
					v.DistributorPurchaseAmount,
				}
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("快手版权商短剧分销数据")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取快手版权商短剧分销数据
func (c *ksAdSalerCopyRightController) Get(ctx context.Context, req *ad.KsAdSalerCopyRightGetReq) (res *ad.KsAdSalerCopyRightGetRes, err error) {
	res = new(ad.KsAdSalerCopyRightGetRes)
	res.KsAdSalerCopyRightInfoRes, err = service.KsAdSalerCopyRight().GetById(ctx, req.Id)
	return
}

// Add 添加快手版权商短剧分销数据
func (c *ksAdSalerCopyRightController) Add(ctx context.Context, req *ad.KsAdSalerCopyRightAddReq) (res *ad.KsAdSalerCopyRightAddRes, err error) {
	err = service.KsAdSalerCopyRight().Add(ctx, req.KsAdSalerCopyRightAddReq)
	return
}

// Edit 修改快手版权商短剧分销数据
func (c *ksAdSalerCopyRightController) Edit(ctx context.Context, req *ad.KsAdSalerCopyRightEditReq) (res *ad.KsAdSalerCopyRightEditRes, err error) {
	err = service.KsAdSalerCopyRight().Edit(ctx, req.KsAdSalerCopyRightEditReq)
	return
}

// Delete 删除快手版权商短剧分销数据
func (c *ksAdSalerCopyRightController) Delete(ctx context.Context, req *ad.KsAdSalerCopyRightDeleteReq) (res *ad.KsAdSalerCopyRightDeleteRes, err error) {
	err = service.KsAdSalerCopyRight().Delete(ctx, req.Ids)
	return
}

// RunCalcAdSalerCopyRightStat 版权商短剧分销数据任务
func (c *ksAdSalerCopyRightController) RunCalcAdSalerCopyRightStat(ctx context.Context, req *ad.KsAdSalerCopyRightTaskReq) (res *ad.KsAdSalerCopyRightTaskRes, err error) {
	err = service.KsAdSalerCopyRight().RunCalcAdSalerCopyRightStat(ctx, &req.KsAdSalerCopyRightSearchReq)
	return
}
