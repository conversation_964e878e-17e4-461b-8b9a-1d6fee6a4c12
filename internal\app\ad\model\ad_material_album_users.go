// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-13 15:31:10
// 生成路径: internal/app/ad/model/ad_material_album_users.go
// 生成人：cyao
// desc:广告素材专辑和用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdMaterialAlbumUsersInfoRes is the golang structure for table ad_material_album_users.
type AdMaterialAlbumUsersInfoRes struct {
	gmeta.Meta    `orm:"table:ad_material_album_users"`
	AlbumId       int `orm:"album_id,primary" json:"albumId" dc:"专辑ID"`              // 专辑ID
	SpecifyUserId int `orm:"specify_user_id,primary" json:"specifyUserId" dc:"用户ID"` // 用户ID
}

type AdMaterialAlbumUsersListRes struct {
	AlbumId       int `json:"albumId" dc:"专辑ID"`
	SpecifyUserId int `json:"specifyUserId" dc:"用户ID"`
}

// AdMaterialAlbumUsersSearchReq 分页请求参数
type AdMaterialAlbumUsersSearchReq struct {
	comModel.PageReq
	AlbumId       string `p:"albumId" dc:"专辑ID"`       //专辑ID
	SpecifyUserId string `p:"specifyUserId" dc:"用户ID"` //用户ID
}

// AdMaterialAlbumUsersSearchRes 列表返回结果
type AdMaterialAlbumUsersSearchRes struct {
	comModel.ListRes
	List []*AdMaterialAlbumUsersListRes `json:"list"`
}

// AdMaterialAlbumUsersAddReq 添加操作请求参数
type AdMaterialAlbumUsersAddReq struct {
	AlbumId int `p:"albumId" v:"required#主键ID不能为空" dc:"专辑ID"`
}

// AdMaterialAlbumUsersEditReq 修改操作请求参数
type AdMaterialAlbumUsersEditReq struct {
	AlbumId int `p:"albumId" v:"required#主键ID不能为空" dc:"专辑ID"`
}
