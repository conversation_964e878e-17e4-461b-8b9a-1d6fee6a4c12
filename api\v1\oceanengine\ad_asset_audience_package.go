// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-17 11:29:53
// 生成路径: api/v1/oceanengine/ad_asset_audience_package.go
// 生成人：cq
// desc:资产-定向包相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetAudiencePackageSearchReq 分页请求参数
type AdAssetAudiencePackageSearchReq struct {
	g.Meta `path:"/list" tags:"资产-定向包" method:"post" summary:"资产-定向包列表"`
	commonApi.Author
	model.AdAssetAudiencePackageSearchReq
}

// AdAssetAudiencePackageSearchRes 列表返回结果
type AdAssetAudiencePackageSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetAudiencePackageSearchRes
}

// AdAssetAudiencePackageAddReq 添加操作请求参数
type AdAssetAudiencePackageAddReq struct {
	g.Meta `path:"/add" tags:"资产-定向包" method:"post" summary:"资产-定向包添加"`
	commonApi.Author
	*model.AdAssetAudiencePackageAddReq
}

// AdAssetAudiencePackageAddRes 添加操作返回结果
type AdAssetAudiencePackageAddRes struct {
	commonApi.EmptyRes
}

// AdAssetAudiencePackageSyncReq 同步定向包请求参数
type AdAssetAudiencePackageSyncReq struct {
	g.Meta `path:"/sync" tags:"资产-定向包" method:"post" summary:"资产-同步定向包"`
	commonApi.Author
	AdvertiserId string `p:"advertiserId" dc:"广告主ID"`
}

// AdAssetAudiencePackageSyncRes 同步定向包返回结果
type AdAssetAudiencePackageSyncRes struct {
	commonApi.EmptyRes
}

// AdAssetAudiencePackageEditReq 修改操作请求参数
type AdAssetAudiencePackageEditReq struct {
	g.Meta `path:"/edit" tags:"资产-定向包" method:"put" summary:"资产-定向包修改"`
	commonApi.Author
	*model.AdAssetAudiencePackageEditReq
}

// AdAssetAudiencePackageEditRes 修改操作返回结果
type AdAssetAudiencePackageEditRes struct {
	commonApi.EmptyRes
}

// AdAssetAudiencePackageGetReq 获取一条数据请求
type AdAssetAudiencePackageGetReq struct {
	g.Meta `path:"/get" tags:"资产-定向包" method:"get" summary:"获取资产-定向包信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetAudiencePackageGetRes 获取一条数据结果
type AdAssetAudiencePackageGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetAudiencePackageInfoRes
}

// AdAssetAudiencePackageDeleteReq 删除数据请求
type AdAssetAudiencePackageDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-定向包" method:"post" summary:"删除资产-定向包"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetAudiencePackageDeleteRes 删除数据返回
type AdAssetAudiencePackageDeleteRes struct {
	commonApi.EmptyRes
}

// AdAssetAudiencePackageSharedReq 删除数据请求
type AdAssetAudiencePackageSharedReq struct {
	g.Meta `path:"/shared" tags:"资产-定向包" method:"post" summary:"资产-定向包共享"`
	commonApi.Author
	Id      int   `p:"id" v:"required#主键必须"`
	UserIds []int `p:"userIds" v:"required#用户id必须"`
}

// AdAssetAudiencePackageSharedRes 删除数据返回
type AdAssetAudiencePackageSharedRes struct {
	commonApi.EmptyRes
}

// AdAssetEstimateAudienceReq 查询受众预估结果请求
type AdAssetEstimateAudienceReq struct {
	g.Meta `path:"/estimateAudience" tags:"资产-定向包" method:"post" summary:"查询受众预估结果"`
	commonApi.Author
	model.EstimateAudienceReq
}

// AdAssetEstimateAudienceRes 查询受众预估结果返回
type AdAssetEstimateAudienceRes struct {
	commonApi.EmptyRes
	*model.EstimateAudienceRes
}

// AdAssetGetDistrictsReq 获取行政信息请求
type AdAssetGetDistrictsReq struct {
	g.Meta `path:"/getDistricts" tags:"资产-定向包" method:"post" summary:"获取行政信息"`
	commonApi.Author
}

// AdAssetGetDistrictsRes 获取行政信息返回
type AdAssetGetDistrictsRes struct {
	commonApi.EmptyRes
	*model.GetDistrictsRes
}

// AdAssetGetActionCategoryReq 获取行为类目请求
type AdAssetGetActionCategoryReq struct {
	g.Meta `path:"/getActionCategory" tags:"资产-定向包" method:"post" summary:"获取行为类目"`
	commonApi.Author
	model.GetActionCategoryReq
}

// AdAssetGetActionCategoryRes 获取行为类目返回
type AdAssetGetActionCategoryRes struct {
	commonApi.EmptyRes
	*model.GetActionCategoryRes
}

// AdAssetGetActionKeywordReq 获取行为关键词请求
type AdAssetGetActionKeywordReq struct {
	g.Meta `path:"/getActionKeyword" tags:"资产-定向包" method:"post" summary:"获取行为关键词"`
	commonApi.Author
	model.GetActionKeywordReq
}

// AdAssetGetActionKeywordRes 获取行为关键词返回
type AdAssetGetActionKeywordRes struct {
	commonApi.EmptyRes
	*model.GetActionKeywordRes
}

// AdAssetGetInterestCategoryReq 获取兴趣类目请求
type AdAssetGetInterestCategoryReq struct {
	g.Meta `path:"/getInterestCategory" tags:"资产-定向包" method:"post" summary:"获取兴趣类目"`
	commonApi.Author
}

// AdAssetGetInterestCategoryRes 获取行为类目返回
type AdAssetGetInterestCategoryRes struct {
	commonApi.EmptyRes
	*model.GetInterestCategoryRes
}

// AdAssetGetInterestKeywordReq 获取兴趣关键词请求
type AdAssetGetInterestKeywordReq struct {
	g.Meta `path:"/getInterestKeyword" tags:"资产-定向包" method:"post" summary:"获取兴趣关键词"`
	commonApi.Author
	model.GetInterestKeywordReq
}

// AdAssetGetInterestKeywordRes 获取兴趣关键词返回
type AdAssetGetInterestKeywordRes struct {
	commonApi.EmptyRes
	*model.GetInterestKeywordRes
}

// AdAssetActionId2WordReq 兴趣行为类目关键词id转词请求
type AdAssetActionId2WordReq struct {
	g.Meta `path:"/actionId2Word" tags:"资产-定向包" method:"post" summary:"兴趣行为类目关键词id转词"`
	commonApi.Author
	model.ActionId2WordReq
}

// AdAssetActionId2WordRes 兴趣行为类目关键词id转词返回
type AdAssetActionId2WordRes struct {
	commonApi.EmptyRes
	*model.ActionId2WordRes
}

// AdAssetGetKeywordSuggestReq 获取行为兴趣推荐关键词请求
type AdAssetGetKeywordSuggestReq struct {
	g.Meta `path:"/getKeywordSuggest" tags:"资产-定向包" method:"post" summary:"获取行为兴趣推荐关键词"`
	commonApi.Author
	model.GetKeywordSuggestReq
}

// AdAssetGetKeywordSuggestRes 获取行为兴趣推荐关键词返回
type AdAssetGetKeywordSuggestRes struct {
	commonApi.EmptyRes
	*model.GetKeywordSuggestRes
}

// AdAssetGetAwemeCategoryReq 获取抖音类目请求
type AdAssetGetAwemeCategoryReq struct {
	g.Meta `path:"/getAwemeCategory" tags:"资产-定向包" method:"post" summary:"获取抖音类目"`
	commonApi.Author
	model.GetAwemeCategoryReq
}

// AdAssetGetAwemeCategoryRes 获取抖音类目返回
type AdAssetGetAwemeCategoryRes struct {
	commonApi.EmptyRes
	*model.GetAwemeCategoryRes
}

// AdAssetGetAwemeAuthorReq 获取抖音类目下的推荐达人请求
type AdAssetGetAwemeAuthorReq struct {
	g.Meta `path:"/getAwemeAuthor" tags:"资产-定向包" method:"post" summary:"获取抖音类目下的推荐达人"`
	commonApi.Author
	model.GetAwemeAuthorReq
}

// AdAssetGetAwemeAuthorRes 获取抖音类目下的推荐达人返回
type AdAssetGetAwemeAuthorRes struct {
	commonApi.EmptyRes
	*model.GetAwemeAuthorRes
}

// AdAssetGetAwemeSimilarAuthorReq 获取抖音类似帐号请求
type AdAssetGetAwemeSimilarAuthorReq struct {
	g.Meta `path:"/getAwemeSimilarAuthor" tags:"资产-定向包" method:"post" summary:"获取抖音类似帐号"`
	commonApi.Author
	model.GetAwemeSimilarAuthorReq
}

// AdAssetGetAwemeSimilarAuthorRes 获取抖音类似帐号返回
type AdAssetGetAwemeSimilarAuthorRes struct {
	commonApi.EmptyRes
	*model.GetAwemeSimilarAuthorRes
}

// AdAssetAwemeInfoSearchReq 查询抖音帐号和类目信息请求
type AdAssetAwemeInfoSearchReq struct {
	g.Meta `path:"/awemeInfoSearch" tags:"资产-定向包" method:"post" summary:"查询抖音帐号和类目信息"`
	commonApi.Author
	model.AwemeInfoSearchReq
}

// AdAssetAwemeInfoSearchRes 查询抖音帐号和类目信息返回
type AdAssetAwemeInfoSearchRes struct {
	commonApi.EmptyRes
	*model.AwemeInfoSearchRes
}
