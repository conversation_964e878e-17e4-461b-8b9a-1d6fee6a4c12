/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletUpdateV30MinRechargeTier
type ToolsWechatAppletUpdateV30MinRechargeTier string

// List of tools_wechat_applet_update_v3.0_min_recharge_tier
const (
	FROM_100_TO_200_ToolsWechatAppletUpdateV30MinRechargeTier       ToolsWechatAppletUpdateV30MinRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletUpdateV30MinRechargeTier ToolsWechatAppletUpdateV30MinRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletUpdateV30MinRechargeTier     ToolsWechatAppletUpdateV30MinRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_update_v3.0_min_recharge_tier value
func (v ToolsWechatAppletUpdateV30MinRechargeTier) Ptr() *ToolsWechatAppletUpdateV30MinRechargeTier {
	return &v
}
