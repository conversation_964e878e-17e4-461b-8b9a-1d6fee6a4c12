/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AudiencePackageGetV3ApiService AudiencePackageGetV3Api service
type AudiencePackageGetV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *AudiencePackageGetV3Request
}

type AudiencePackageGetV3Request struct {
	AdvertiserId *int64
	Filtering    *models.AudiencePackageGetV30Filtering
	Page         *int32
	PageSize     *int32
}

func (r *AudiencePackageGetV3ApiService) SetCfg(cfg *conf.Configuration) *AudiencePackageGetV3ApiService {
	r.cfg = cfg
	return r
}

func (r *AudiencePackageGetV3ApiService) AudiencePackageGetV3Request(audiencePackageGetV3Request AudiencePackageGetV3Request) *AudiencePackageGetV3ApiService {
	r.Request = &audiencePackageGetV3Request
	return r
}

func (r *AudiencePackageGetV3ApiService) AccessToken(accessToken string) *AudiencePackageGetV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AudiencePackageGetV3ApiService) Do() (data *models.AudiencePackageGetV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/audience_package/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.Request.Filtering)
	}
	if r.Request.Page != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AudiencePackageGetV30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AudiencePackageGetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/audience_package/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
