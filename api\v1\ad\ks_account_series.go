// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: api/v1/ad/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

type PullKsAccountSeriesReq struct {
	g.Meta `path:"/pull/series" tags:"短剧信息列表" method:"get" summary:"获取短剧广告报表明细表信息"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiserId" v:"required#主键必须"` //通过主键获取
	InDb         bool  `p:"inDb" `                          // 是否需要入库
}

type PullKsAccountSeriesRes struct {
	g.Meta `mime:"application/json"`
	List   []api.SeriesInfoSnake
}

// KsAccountSeriesSearchReq 分页请求参数
type KsAccountSeriesSearchReq struct {
	g.Meta `path:"/list" tags:"短剧信息列表" method:"get" summary:"短剧信息列表列表"`
	commonApi.Author
	model.KsAccountSeriesSearchReq
}

// KsAccountSeriesSearchRes 列表返回结果
type KsAccountSeriesSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAccountSeriesSearchRes
}

// KsAccountSeriesExportReq 导出请求
type KsAccountSeriesExportReq struct {
	g.Meta `path:"/export" tags:"短剧信息列表" method:"get" summary:"短剧信息列表导出"`
	commonApi.Author
	model.KsAccountSeriesSearchReq
}

// KsAccountSeriesExportRes 导出响应
type KsAccountSeriesExportRes struct {
	commonApi.EmptyRes
}

// KsAccountSeriesAddReq 添加操作请求参数
type KsAccountSeriesAddReq struct {
	g.Meta `path:"/add" tags:"短剧信息列表" method:"post" summary:"短剧信息列表添加"`
	commonApi.Author
	*model.KsAccountSeriesAddReq
}

// KsAccountSeriesAddRes 添加操作返回结果
type KsAccountSeriesAddRes struct {
	commonApi.EmptyRes
}

// KsAccountSeriesEditReq 修改操作请求参数
type KsAccountSeriesEditReq struct {
	g.Meta `path:"/edit" tags:"短剧信息列表" method:"put" summary:"短剧信息列表修改"`
	commonApi.Author
	*model.KsAccountSeriesEditReq
}

// KsAccountSeriesEditRes 修改操作返回结果
type KsAccountSeriesEditRes struct {
	commonApi.EmptyRes
}

// KsAccountSeriesGetReq 获取一条数据请求
type KsAccountSeriesGetReq struct {
	g.Meta `path:"/get" tags:"短剧信息列表" method:"get" summary:"获取短剧信息列表信息"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiserId" v:"required#主键必须"` //通过主键获取
	SId          int64 `p:"sid" v:"required#主键必须"`          // 剧集id
}

// KsAccountSeriesGetRes 获取一条数据结果
type KsAccountSeriesGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAccountSeriesInfoRes
}

// KsAccountSeriesDeleteReq 删除数据请求
type KsAccountSeriesDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧信息列表" method:"delete" summary:"删除短剧信息列表"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#主键必须"` //通过主键删除
}

// KsAccountSeriesDeleteRes 删除数据返回
type KsAccountSeriesDeleteRes struct {
	commonApi.EmptyRes
}
