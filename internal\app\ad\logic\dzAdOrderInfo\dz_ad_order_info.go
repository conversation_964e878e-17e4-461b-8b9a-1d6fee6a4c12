// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:38
// 生成路径: internal/app/ad/logic/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	channelEntity "github.com/tiger1103/gfast/v3/internal/app/channel/model/entity"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	orderModel "github.com/tiger1103/gfast/v3/internal/app/order/model"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterModel "github.com/tiger1103/gfast/v3/internal/app/theater/model"
	dzApi "github.com/tiger1103/gfast/v3/library/advertiser/dz/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzAdOrderInfo(New())
}

func New() service.IDzAdOrderInfo {
	return &sDzAdOrderInfo{}
}

type sDzAdOrderInfo struct{}

func (s *sDzAdOrderInfo) List(ctx context.Context, req *model.DzAdOrderInfoSearchReq) (listRes *model.DzAdOrderInfoSearchRes, err error) {
	listRes = new(model.DzAdOrderInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdOrderInfo.Ctx(ctx).WithAll().As("dzdata")
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin {
			m = m.LeftJoin(dao.DzAdAccountDepts.Table(), "d", " dzdata.channel_id = d.channel_id").
				LeftJoin(dao.DzAdAccountUsers.Table(), "u", " dzdata.channel_id = u.channel_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if len(req.AccountIds) > 0 {
			list, _ := service.DzAdAccountChannel().GetByAccountIds(ctx, gconv.SliceInt64(req.AccountIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("dzdata."+dao.DzAdOrderInfo.Columns().ChannelId, ids)
				}
			}

		}

		if len(req.ChannelIds) > 0 {
			m = m.WhereIn("dzdata."+dao.DzAdOrderInfo.Columns().ChannelId, req.ChannelIds)
		}
		if req.StartTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Ctime+" >= ?", libUtils.GetSecByDateTime(req.StartTime))
		}
		if req.EndTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Ctime+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.EndTime, 1)))
		}

		if req.PayStartTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().FinishTime+" >= ?", libUtils.GetSecByDateTime(req.PayStartTime))
		}
		if req.PayEndTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().FinishTime+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.PayEndTime, 1)))
		}
		if req.RegisterStartTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().RegisterDate+" >= ?", libUtils.GetSecByDateTime(req.RegisterStartTime))
		}
		if req.RegisterEndTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().RegisterDate+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.RegisterEndTime, 1)))
		}
		if req.DyeStartTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().DyeTime+" >= ?", libUtils.GetSecByDateTime(req.DyeStartTime))
		}
		if req.DyeEndTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().DyeTime+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.DyeEndTime, 1)))
		}

		if req.PitcherId > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().DzReferralId+" = dzdata.referral_id ").InnerJoin(systemDao.SysUser.Table(), "su", fmt.Sprintf("su.id = sc.user_id and su.id = %v", req.PitcherId))
		} else {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().DzReferralId+" = dzdata.referral_id ").LeftJoin(systemDao.SysUser.Table(), "su", "su.id = sc.user_id ")
		}

		if req.DistributorId > 0 {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
		} else {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
		}

		if len(req.DeptIds) > 0 {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id")
			m = m.Where(fmt.Sprintf("de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds)))
		}

		//if req.Id != "" {
		//	m = m.Where(dao.DzAdOrderInfo.Columns().Id+" = ?", req.Id)
		//}
		//if req.Ver != "" {
		//	m = m.Where(dao.DzAdOrderInfo.Columns().Ver+" = ?", req.Ver)
		//}
		if req.OutTradeNo != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().OutTradeNo+" = ?", req.OutTradeNo)
		}
		if req.Type != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Type+" = ?", gconv.Int(req.Type))
		}
		if req.StatusNotify != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().StatusNotify+" = ?", gconv.Int(req.StatusNotify))
		}
		if req.Ctime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Ctime+" = ?", gconv.Int64(req.Ctime))
		}
		if req.FinishTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().FinishTime+" = ?", gconv.Int64(req.FinishTime))
		}
		if req.UserId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().UserId+" = ?", gconv.Int64(req.UserId))
		}
		if req.ChannelId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.Domain != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Domain+" = ?", gconv.Int(req.Domain))
		}
		if req.SourceInfo != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().SourceInfo+" = ?", req.SourceInfo)
		}
		if req.ChapterId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().ChapterId+" = ?", req.ChapterId)
		}
		if req.SourceDesc != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().SourceDesc+" = ?", req.SourceDesc)
		}

		if req.OpenId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.Os != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Os+" = ?", req.Os)
		}
		if req.ReferralId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().ReferralId+" = ?", req.ReferralId)
		}
		if req.Adid != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Adid+" = ?", req.Adid)
		}
		if req.FromDrId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().FromDrId+" = ?", req.FromDrId)
		}
		if req.Platform != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Platform+" = ?", req.Platform)
		}
		if req.Scene != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Scene+" = ?", req.Scene)
		}
		if req.ThirdCorpId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().ThirdCorpId+" = ?", req.ThirdCorpId)
		}
		if req.ThirdWxId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().ThirdWxId+" = ?", req.ThirdWxId)
		}
		if req.KdrId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().KdrId+" = ?", req.KdrId)
		}
		if req.SelfReturn != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().SelfReturn+" = ?", req.SelfReturn)
		}
		if req.ProjectId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.PromotionId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.SchannelTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().SchannelTime+" = ?", req.SchannelTime)
		}
		if req.DyeTime != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().DyeTime+" = ?", req.DyeTime)
		}
		if req.XuniPay != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().XuniPay+" = ?", gconv.Int(req.XuniPay))
		}
		if req.MoneyBenefit != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().MoneyBenefit+" = ?", req.MoneyBenefit)
		}
		if req.Mid1 != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Mid1+" = ?", req.Mid1)
		}
		if req.Mid2 != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Mid2+" = ?", req.Mid2)
		}
		if req.Mid3 != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().Mid3+" = ?", req.Mid3)
		}
		if req.UnionId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().UnionId+" = ?", req.UnionId)
		}
		if req.From != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().From+" = ?", req.From)
		}
		if req.OrderSubType != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().OrderSubType+" = ?", req.OrderSubType)
		}
		if req.WxFinderId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().WxFinderId+" = ?", req.WxFinderId)
		}
		if req.WxExportId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().WxExportId+" = ?", req.WxExportId)
		}
		if req.WxPromotionId != "" {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().WxPromotionId+" = ?", req.WxPromotionId)
		}
		if len(req.DateRange) != 0 {
			m = m.Where("dzdata."+dao.DzAdOrderInfo.Columns().CreatedAt+" >=? AND "+"dzdata."+dao.DzAdOrderInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		// 支付成功的总金额
		var summary = new(model.DzAdOrderInfoSummary)
		err = m.FieldSum("dzdata."+dao.DzAdOrderInfo.Columns().Discount, "discount").Where("dzdata.status_notify", 1).
			Scan(&summary)
		summary.Discount = libUtils.ToRound(summary.Discount, 2, libUtils.RoundHalfEven)
		listRes.Summary = summary

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := " out_trade_no asc"
		if req.OrderBy != "" {
			//order = req.OrderBy
			order = "dzdata." + libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
		}
		var res []*model.DzAdOrderInfoListRes
		err = m.Fields("dzdata.*, su.user_name as userName , de.leader as distributorName , channel_code").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		cIds := make([]int64, 0)
		listRes.List = make([]*model.DzAdOrderInfoListRes, len(res))
		for k, v := range res {
			cIds = append(cIds, gconv.Int64(v.ChannelId))
			listRes.List[k] = &model.DzAdOrderInfoListRes{
				CreateTime: libUtils.GetDateBySec2(v.Ctime),
				PayTime:    libUtils.GetDateBySec2(v.FinishTime),
				//AppType:         libUtils.GetAppTypeByOpenId(v.OpenId),
				DistributorName: v.DistributorName,
				UserName:        v.UserName,
				DyeTime:         libUtils.GetDateBySec2(gconv.Int64(v.DyeTime)),
				OutTradeNo:      v.OutTradeNo,
				Discount:        v.Discount,
				Type:            v.Type,
				StatusNotify:    v.StatusNotify,
				Ctime:           v.Ctime,
				FinishTime:      v.FinishTime,
				UserId:          v.UserId,
				ChannelId:       v.ChannelId,
				MoneyBenefit:    v.MoneyBenefit,
				SourceDesc:      v.SourceDesc,
				RegisterDate:    libUtils.GetDateBySec2(gconv.Int64(v.RegisterDate)),
				OpenId:          v.OpenId,
				Os:              v.Os,
				ReferralId:      v.ReferralId,
				FromDrId:        v.FromDrId,
				From:            v.From,

				//Id:              v.Id,
				//Ver:             v.Ver,
				//Domain:          v.Domain,
				//SourceInfo:      v.SourceInfo,
				//ChapterId:       v.ChapterId,
				//Adid:            v.Adid,
				//Platform:        v.Platform,
				//Scene:           v.Scene,
				//ThirdCorpId:     v.ThirdCorpId,
				//ThirdWxId:       v.ThirdWxId,
				//KdrId:           v.KdrId,
				//SelfReturn:      v.SelfReturn,
				//ProjectId:       v.ProjectId,
				//PromotionId:     v.PromotionId,
				//SchannelTime:    v.SchannelTime,
				//XuniPay:         v.XuniPay,
				//Mid1:            v.Mid1,
				//Mid2:            v.Mid2,
				//Mid3:            v.Mid3,
				//UnionId:         v.UnionId,
				//OrderSubType:    v.OrderSubType,
				//WxFinderId:      v.WxFinderId,
				//WxExportId:      v.WxExportId,
				//WxPromotionId:   v.WxPromotionId,
				//CreatedAt:       v.CreatedAt,
				//ChannelCode:     v.ChannelCode,
			}
		}

		var accountInfo = make([]model.DzAdAccountInfoRes, 0)
		channelList, _ := service.DzAdAccountChannel().GetByChannelIds(ctx, cIds)
		accountIds := make([]int64, 0)
		for _, item := range channelList {
			accountIds = append(accountIds, item.AccountId)
		}
		err = dao.DzAdAccount.Ctx(ctx).WhereIn("account_id", accountIds).Scan(&accountInfo)

		for _, item := range listRes.List {
			for _, channelInfo := range channelList {
				if gconv.Int64(item.ChannelId) == channelInfo.ChannelId {
					item.DzChannel = channelInfo.NickName
					for _, accountInfoRes := range accountInfo {
						if gconv.String(channelInfo.AccountId) == accountInfoRes.AccountId {
							item.DzAccount = accountInfoRes.AccountName
							break
						}
					}
					break
				}
			}
		}

	})
	return
}

func (s *sDzAdOrderInfo) GetExportData(ctx context.Context, req *model.DzAdOrderInfoSearchReq) (listRes []*model.DzAdOrderInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdOrderInfo.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Id+" = ?", req.Id)
		}
		if req.Ver != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Ver+" = ?", req.Ver)
		}
		if req.OutTradeNo != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().OutTradeNo+" = ?", req.OutTradeNo)
		}
		if req.Type != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Type+" = ?", gconv.Int(req.Type))
		}
		if req.StatusNotify != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().StatusNotify+" = ?", gconv.Int(req.StatusNotify))
		}
		if req.Ctime != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Ctime+" = ?", gconv.Int64(req.Ctime))
		}
		if req.FinishTime != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().FinishTime+" = ?", gconv.Int64(req.FinishTime))
		}
		if req.UserId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().UserId+" = ?", gconv.Int64(req.UserId))
		}
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.Domain != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Domain+" = ?", gconv.Int(req.Domain))
		}
		if req.SourceInfo != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().SourceInfo+" = ?", req.SourceInfo)
		}
		if req.ChapterId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().ChapterId+" = ?", req.ChapterId)
		}
		if req.SourceDesc != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().SourceDesc+" = ?", req.SourceDesc)
		}
		if req.OpenId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.Os != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Os+" = ?", req.Os)
		}
		if req.ReferralId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().ReferralId+" = ?", req.ReferralId)
		}
		if req.Adid != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Adid+" = ?", req.Adid)
		}
		if req.FromDrId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().FromDrId+" = ?", req.FromDrId)
		}
		if req.Platform != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Platform+" = ?", req.Platform)
		}
		if req.Scene != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Scene+" = ?", req.Scene)
		}
		if req.ThirdCorpId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().ThirdCorpId+" = ?", req.ThirdCorpId)
		}
		if req.ThirdWxId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().ThirdWxId+" = ?", req.ThirdWxId)
		}
		if req.KdrId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().KdrId+" = ?", req.KdrId)
		}
		if req.SelfReturn != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().SelfReturn+" = ?", req.SelfReturn)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.SchannelTime != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().SchannelTime+" = ?", req.SchannelTime)
		}
		if req.DyeTime != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().DyeTime+" = ?", req.DyeTime)
		}
		if req.XuniPay != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().XuniPay+" = ?", gconv.Int(req.XuniPay))
		}
		if req.MoneyBenefit != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().MoneyBenefit+" = ?", req.MoneyBenefit)
		}
		if req.Mid1 != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Mid1+" = ?", req.Mid1)
		}
		if req.Mid2 != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Mid2+" = ?", req.Mid2)
		}
		if req.Mid3 != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().Mid3+" = ?", req.Mid3)
		}
		if req.UnionId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().UnionId+" = ?", req.UnionId)
		}
		if req.From != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().From+" = ?", req.From)
		}
		if req.OrderSubType != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().OrderSubType+" = ?", req.OrderSubType)
		}
		if req.WxFinderId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().WxFinderId+" = ?", req.WxFinderId)
		}
		if req.WxExportId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().WxExportId+" = ?", req.WxExportId)
		}
		if req.WxPromotionId != "" {
			m = m.Where(dao.DzAdOrderInfo.Columns().WxPromotionId+" = ?", req.WxPromotionId)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.DzAdOrderInfo.Columns().CreatedAt+" >=? AND "+dao.DzAdOrderInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sDzAdOrderInfo) GetById(ctx context.Context, id uint64) (res *model.DzAdOrderInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdOrderInfo.Ctx(ctx).WithAll().Where(dao.DzAdOrderInfo.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdOrderInfo) Add(ctx context.Context, req *model.DzAdOrderInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdOrderInfo.Ctx(ctx).Insert(do.DzAdOrderInfo{
			Id:            req.Id,
			Ver:           req.Ver,
			OutTradeNo:    req.OutTradeNo,
			Discount:      req.Discount,
			Type:          req.Type,
			StatusNotify:  req.StatusNotify,
			Ctime:         req.Ctime,
			FinishTime:    req.FinishTime,
			UserId:        req.UserId,
			ChannelId:     req.ChannelId,
			Domain:        req.Domain,
			SourceInfo:    req.SourceInfo,
			ChapterId:     req.ChapterId,
			SourceDesc:    req.SourceDesc,
			RegisterDate:  req.RegisterDate,
			OpenId:        req.OpenId,
			Os:            req.Os,
			ReferralId:    req.ReferralId,
			Adid:          req.Adid,
			FromDrId:      req.FromDrId,
			Platform:      req.Platform,
			Scene:         req.Scene,
			ThirdCorpId:   req.ThirdCorpId,
			ThirdWxId:     req.ThirdWxId,
			KdrId:         req.KdrId,
			SelfReturn:    req.SelfReturn,
			ProjectId:     req.ProjectId,
			PromotionId:   req.PromotionId,
			SchannelTime:  req.SchannelTime,
			DyeTime:       req.DyeTime,
			XuniPay:       req.XuniPay,
			MoneyBenefit:  req.MoneyBenefit,
			Mid1:          req.Mid1,
			Mid2:          req.Mid2,
			Mid3:          req.Mid3,
			UnionId:       req.UnionId,
			From:          req.From,
			OrderSubType:  req.OrderSubType,
			WxFinderId:    req.WxFinderId,
			WxExportId:    req.WxExportId,
			WxPromotionId: req.WxPromotionId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzAdOrderInfo) Edit(ctx context.Context, req *model.DzAdOrderInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdOrderInfo.Ctx(ctx).WherePri(req.Id).Update(do.DzAdOrderInfo{
			Ver:           req.Ver,
			OutTradeNo:    req.OutTradeNo,
			Discount:      req.Discount,
			Type:          req.Type,
			StatusNotify:  req.StatusNotify,
			Ctime:         req.Ctime,
			FinishTime:    req.FinishTime,
			UserId:        req.UserId,
			ChannelId:     req.ChannelId,
			Domain:        req.Domain,
			SourceInfo:    req.SourceInfo,
			ChapterId:     req.ChapterId,
			SourceDesc:    req.SourceDesc,
			RegisterDate:  req.RegisterDate,
			OpenId:        req.OpenId,
			Os:            req.Os,
			ReferralId:    req.ReferralId,
			Adid:          req.Adid,
			FromDrId:      req.FromDrId,
			Platform:      req.Platform,
			Scene:         req.Scene,
			ThirdCorpId:   req.ThirdCorpId,
			ThirdWxId:     req.ThirdWxId,
			KdrId:         req.KdrId,
			SelfReturn:    req.SelfReturn,
			ProjectId:     req.ProjectId,
			PromotionId:   req.PromotionId,
			SchannelTime:  req.SchannelTime,
			DyeTime:       req.DyeTime,
			XuniPay:       req.XuniPay,
			MoneyBenefit:  req.MoneyBenefit,
			Mid1:          req.Mid1,
			Mid2:          req.Mid2,
			Mid3:          req.Mid3,
			UnionId:       req.UnionId,
			From:          req.From,
			OrderSubType:  req.OrderSubType,
			WxFinderId:    req.WxFinderId,
			WxExportId:    req.WxExportId,
			WxPromotionId: req.WxPromotionId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

// BatchAdd
func (s *sDzAdOrderInfo) BatchAdd(ctx context.Context, req []*model.DzAdOrderInfoInfoRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdOrderInfo.Ctx(ctx).InsertIgnore(req)
		liberr.ErrIsNil(ctx, err, "批量添加失败")
	})
	return
}

// 回调
func (s *sDzAdOrderInfo) Callback(ctx context.Context, req *model.DzAdOrderInfoCallbackReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Info(ctx, fmt.Sprintf("-------------  执行点众回调:Xurl:%v TaskId :%v  -------------------", req.Xurl, req.TaskId))
		if req.Xurl == "-1" {
			return
		}
		resp, err := http.Get(req.Xurl)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		defer resp.Body.Close()
		// 创建按行读取器
		scanner := bufio.NewScanner(resp.Body)
		lineNum := 1
		var listRes []*model.DzAdOrderInfoInfoRes
		for scanner.Scan() {
			line := scanner.Text()
			var p model.DzAdOrderInfoInfoRes
			err := json.Unmarshal([]byte(line), &p)
			if err != nil {
				g.Log().Error(ctx, err, fmt.Sprintf("第 %d 行 JSON 解析失败: %v\n", lineNum, err))
				continue
			}
			// 成功解析
			g.Log().Info(ctx, fmt.Sprintf("第 %d 行解析成功: %+v\n", lineNum, p))
			listRes = append(listRes, &p)
			if len(listRes) == 500 {
				err = s.BatchAdd(ctx, listRes)
				liberr.ErrIsNil(ctx, err, "批量添加失败")
				listRes = listRes[:0]
			}
			lineNum++
		}

		if len(listRes) > 0 {
			err = s.BatchAdd(ctx, listRes)
			liberr.ErrIsNil(ctx, err, "批量添加失败")
		}
		if err := scanner.Err(); err != nil {
			g.Log().Error(ctx, fmt.Sprintf("读取远程文件出错: %v\n", err))
		}
		// todo 修改任务状态
		var task *model.DzTaskInfoRes
		_ = dao.DzTask.Ctx(ctx).Where(dao.DzTask.Columns().TaskId, req.TaskId).Scan(&task)
		if task != nil && len(task.TaskId) > 0 {
			task.Status = 2
			task.Xurl = req.Xurl
			dao.DzTask.Ctx(ctx).Save(task)
		}

	})
	return
}

func (s *sDzAdOrderInfo) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdOrderInfo.Ctx(ctx).Delete(dao.DzAdOrderInfo.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// TimedPull 每隔五分钟拉取一次订单数据
func (s *sDzAdOrderInfo) TimedPull(ctx context.Context) (count int, err error) {
	sDate, eDate, _, _ := libUtils.GetTwoStepsBackFiveMinuteWindow()
	channelRechargeStatKey := model.DzOrderDataTask + ":" + gconv.String(eDate)
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName : sDzAdOrderInfo TimedPull ")
		return 0, err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = s.Pull(innerCtx, sDate, eDate)
	if err != nil {
		g.Log().Error(ctx, "Pull err：", err)
	}
	return

}

// 拉取当天订单数据
func (s *sDzAdOrderInfo) PullOrderData(ctx context.Context, statDate string) (err error) {
	channelRechargeStatKey := model.DzOrderDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName : PullOrderData TimedPull ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	err = g.Try(ctx, func(ctx context.Context) {
		sDate, eDate, _, _, _ := libUtils.GetDayTimestamps2(statDate)
		err = s.Pull(ctx, sDate, eDate)
	})
	if err != nil {
		g.Log().Error(ctx, "PullOrderData err：", err)
	}
	return
}

func (s *sDzAdOrderInfo) Pull(ctx context.Context, sDate, eDate int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 100
		if err != nil {
			return
		}
		for {
			accounts, _ := service.DzAdAccount().List2(ctx, &model.DzAdAccountSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})
			if len(accounts.List) == 0 {
				break
			}
			for _, adAccount := range accounts.List {
				req := dzApi.QueryOrderRequest{
					ClientId: adAccount.AccountId,
					SDate:    gconv.String(sDate),
					EDate:    gconv.String(eDate),
				}
				queryJson := ""
				orderResponse, err1 := dzApi.GetAdDZIClient2().QueryOrderService.
					SetToken(adAccount.Token).
					SetReq(req).Do()
				if err1 != nil {
					//记录日志
					g.Log().Error(ctx, "查询订单失败："+adAccount.AccountId+"sDate :"+gconv.String(sDate)+"eDate :"+gconv.String(eDate)+"---------err :"+err.Error())
					liberr.ErrIsNil(ctx, err1)
				}
				if len(orderResponse.Data) > 0 {
					// 添加 dz_task 数据
					reqByte, _ := json.Marshal(req)
					if len(reqByte) > 0 {
						queryJson = string(reqByte)
					}
					service.DzTask().Add(ctx, &model.DzTaskAddReq{
						QueryJson: queryJson,
						QueryUrl:  "订单查询",
						TaskId:    orderResponse.Data,
						Status:    1,
					})
				}
			}
			pageNo++
		}
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzTotalAmount(ctx context.Context, statDate string) (totalAmountStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).As("dz").
			InnerJoin("s_channel s", "s.dz_referral_id = dz.referral_id").
			Fields("IFNULL(sum(dz.discount), 0.00) as totalAmount").
			Fields("count(distinct dz.user_id) as rechargeNums").
			Fields("count(*) as totalRechargeTimes").
			Fields("s.channel_code as account").
			WhereGTE("dz."+dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE("dz."+dao.DzAdOrderInfo.Columns().Ctime, endTime).
			Where("dz."+dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("s.channel_code").
			Scan(&totalAmountStat)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzNewUserAmount(ctx context.Context, statDate string) (newUserAmountStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).As("dz").
			InnerJoin("s_channel s", "s.dz_referral_id = dz.referral_id").
			Fields("IFNULL(sum(dz.discount), 0.00) as newUserAmount").
			Fields("count(distinct dz.user_id) as newUserRechargeNums").
			Fields("s.channel_code as account").
			WhereGTE("dz."+dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE("dz."+dao.DzAdOrderInfo.Columns().Ctime, endTime).
			WhereGTE("dz."+dao.DzAdOrderInfo.Columns().RegisterDate, startTime).
			WhereLTE("dz."+dao.DzAdOrderInfo.Columns().RegisterDate, endTime).
			Where("dz."+dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("s.channel_code").
			Scan(&newUserAmountStat)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzAmountByDomainAndOs(ctx context.Context, statDate string) (detailAmountStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).As("dz").
			InnerJoin("s_channel s", "s.dz_referral_id = dz.referral_id").
			Fields("IFNULL(sum(dz.discount), 0.00) as totalAmount").
			Fields("s.channel_code as account").
			Fields("dz.domain as dzDomain").
			Fields("dz.os as dzOs").
			WhereGTE("dz."+dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE("dz."+dao.DzAdOrderInfo.Columns().Ctime, endTime).
			Where("dz."+dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("s.channel_code, dz.domain, dz.os").
			Scan(&detailAmountStat)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzDistributorRechargeAmount(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).
			Fields("count(distinct user_id) as rechargeNums").
			Fields("count(*) as totalRechargeTimes").
			Fields("IFNULL(sum(discount), 0.00) as dayTotalAmount").
			Fields("channel_id as dzChannelId").
			WhereGTE(dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE(dao.DzAdOrderInfo.Columns().Ctime, endTime).
			Where(dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("channel_id").
			Scan(&res)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzDistributorAmountByDomainAndOs(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).
			Fields("IFNULL(sum(discount), 0.00) as dayTotalAmount").
			Fields("channel_id as dzChannelId").
			Fields("domain as dzDomain").
			Fields("os as dzOs").
			WhereGTE(dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE(dao.DzAdOrderInfo.Columns().Ctime, endTime).
			Where(dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("channel_id, domain, os").
			Scan(&res)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzVideoTotalAmount(ctx context.Context, statDate string) (totalAmountStat []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).
			Fields("referral_id as dzReferralId").
			Fields("source_info as videoId").
			Fields("ANY_VALUE(source_desc) as videoName").
			Fields("ANY_VALUE(channel_id) as dzChannelId").
			Fields("IFNULL(sum(discount), 0.00) as totalAmount").
			Fields("count(distinct user_id) as rechargeNums").
			Fields("count(*) as totalRechargeTimes").
			Fields("ROUND(IFNULL(sum(discount), 0.00) / IFNULL(count(distinct user_id), 0.00), 2) as customerPrice").
			Fields("ROUND(IFNULL(count(*), 0.00) / IFNULL(count(distinct user_id), 0.00), 2) as avgRechargeTimes").
			WhereGTE(dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE(dao.DzAdOrderInfo.Columns().Ctime, endTime).
			Where(dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("referral_id, source_info").
			Scan(&totalAmountStat)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzVideoNewUserAmount(ctx context.Context, statDate string) (newUserAmountStat []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).
			Fields("referral_id as dzReferralId").
			Fields("source_info as videoId").
			Fields("ANY_VALUE(source_desc) as videoName").
			Fields("ANY_VALUE(channel_id) as dzChannelId").
			Fields("IFNULL(sum(discount), 0.00) as newUserAmount").
			WhereGTE(dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE(dao.DzAdOrderInfo.Columns().Ctime, endTime).
			WhereGTE(dao.DzAdOrderInfo.Columns().RegisterDate, startTime).
			WhereLTE(dao.DzAdOrderInfo.Columns().RegisterDate, endTime).
			Where(dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("referral_id, source_info").
			Scan(&newUserAmountStat)
	})
	return
}

func (s *sDzAdOrderInfo) CalcDzVideoAmountByDomainAndOs(ctx context.Context, statDate string) (res []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdOrderInfoAnalytic.Ctx(ctx).
			Fields("IFNULL(sum(discount), 0.00) as totalAmount").
			Fields("referral_id as dzReferralId").
			Fields("source_info as videoId").
			Fields("domain as dzDomain").
			Fields("os as dzOs").
			WhereGTE(dao.DzAdOrderInfo.Columns().Ctime, startTime).
			WhereLTE(dao.DzAdOrderInfo.Columns().Ctime, endTime).
			Where(dao.DzAdOrderInfo.Columns().StatusNotify, 1).
			Group("referral_id, source_info, domain, os").
			Scan(&res)
	})
	return
}
