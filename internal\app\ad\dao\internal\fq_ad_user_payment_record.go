// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: internal/app/ad/dao/internal/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdUserPaymentRecordDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdUserPaymentRecordDao struct {
	table   string                       // Table is the underlying table name of the DAO.
	group   string                       // Group is the database configuration group name of current DAO.
	columns FqAdUserPaymentRecordColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdUserPaymentRecordColumns defines and stores column names for table fq_ad_user_payment_record.
type FqAdUserPaymentRecordColumns struct {
	TradeNo          string // 常读订单id
	DistributorId    string // 快应用/公众号distributor_id
	AppId            string // 公众号/快应用id（分销平台id）
	AppName          string // 快应用/公众号名称
	PromotionId      string // 付费推广链id（用户染色归属推广链）
	OutTradeNo       string // 第三方订单ID
	DeviceId         string // 设备ID
	OpenId           string // 用户openid（H5书城、微信小程序、抖音小程序）
	WxOaOpenId       string // 微信公众号 open_id（仅适用网文微小复访的公众号用户）
	WxOaName         string // 公众号名称（微小的小程序维度返回）
	PayAmount        string // 付费金额，单位分
	Ip               string // 用户最近一次点击推广链时的IP
	UserAgent        string // 用户最近一次点击推广链时的UA
	Oaid             string // 付费时用户OAID（仅支持快应用）
	AndroidId        string // 付费时用户android_id（仅支持快应用）
	RegisterTime     string // 用户染色时间戳
	WxPlatformAppKey string // 微信开发者id(仅支持微信H5)【v1.2】
	BookId           string // 染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）
	BookName         string // 染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称
	BookGender       string // 染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别
	BookCategory     string // 染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型
	Activity         string // 是否是充值活动(仅支持微信H5)【v1.2】
	RecentReadBookId string // H5书城用户订单最近阅读书籍（小程序不返回）
	ExternalId       string // 企微用户企微id（公众号返回）
	OrderType        string // 订单类型：1 拟支付，2 非虚拟支付
	AdvertiserId     string // 腾讯广告主id
	AdgroupId        string // 腾讯广告id
	AdId             string // 腾讯广告创意id
	UnionId          string // 用户在微信/抖音开放平台下的唯一id
	WxVideoId        string // 视频ID（仅视频号场景）
	WxVcSourceType   string // 视频号订单类型（仅视频号场景）1.自然流量 2.加热流量
	WxPromotionId    string // 视频号加热订单ID（仅视频号场景）
	WxSourceType     string // 场景参数，用于区分分销自挂载和CPS达人模式
	WxVideoChannelId string // 视频号ID（仅视频号场景）
	Status           string // 0-已支付-1-未支付
	PayWay           string // 1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成
	PayTimestamp     string // 付费时间戳
	CreateTime       string // 订单创建时间
}

var fqAdUserPaymentRecordColumns = FqAdUserPaymentRecordColumns{
	TradeNo:          "trade_no",
	DistributorId:    "distributor_id",
	AppId:            "app_id",
	AppName:          "app_name",
	PromotionId:      "promotion_id",
	OutTradeNo:       "out_trade_no",
	DeviceId:         "device_id",
	OpenId:           "open_id",
	WxOaOpenId:       "wx_oa_open_id",
	WxOaName:         "wx_oa_name",
	PayAmount:        "pay_amount",
	Ip:               "ip",
	UserAgent:        "user_agent",
	Oaid:             "oaid",
	AndroidId:        "android_id",
	RegisterTime:     "register_time",
	WxPlatformAppKey: "wx_platform_app_key",
	BookId:           "book_id",
	BookName:         "book_name",
	BookGender:       "book_gender",
	BookCategory:     "book_category",
	Activity:         "activity",
	RecentReadBookId: "recent_read_book_id",
	ExternalId:       "external_id",
	OrderType:        "order_type",
	AdvertiserId:     "advertiser_id",
	AdgroupId:        "adgroup_id",
	AdId:             "ad_id",
	UnionId:          "union_id",
	WxVideoId:        "wx_video_id",
	WxVcSourceType:   "wx_vc_source_type",
	WxPromotionId:    "wx_promotion_id",
	WxSourceType:     "wx_source_type",
	WxVideoChannelId: "wx_video_channel_id",
	Status:           "status",
	PayWay:           "pay_way",
	PayTimestamp:     "pay_timestamp",
	CreateTime:       "create_time",
}

// NewFqAdUserPaymentRecordDao creates and returns a new DAO object for table data access.
func NewFqAdUserPaymentRecordDao() *FqAdUserPaymentRecordDao {
	return &FqAdUserPaymentRecordDao{
		group:   "default",
		table:   "fq_ad_user_payment_record",
		columns: fqAdUserPaymentRecordColumns,
	}
}

func NewFqAdUserPaymentRecordAnalyticDao() *FqAdUserPaymentRecordDao {
	return &FqAdUserPaymentRecordDao{
		group:   "analyticDB",
		table:   "fq_ad_user_payment_record",
		columns: fqAdUserPaymentRecordColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdUserPaymentRecordDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdUserPaymentRecordDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdUserPaymentRecordDao) Columns() FqAdUserPaymentRecordColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdUserPaymentRecordDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdUserPaymentRecordDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdUserPaymentRecordDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
