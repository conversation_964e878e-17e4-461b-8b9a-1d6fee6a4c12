// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-10 14:40:38
// 生成路径: api/v1/ad/ks_series_report_core_data.go
// 生成人：cyao
// desc:短剧核心总览数据报表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

// GetKsReportCoreDataReq 通过api获取数据条件包含短剧的时候查询实时接口
type GetKsReportCoreDataReq struct {
	g.Meta `path:"/get/api" tags:"短剧广告报表明细表" method:"post" summary:"通过api获取数据条件包含短剧的时候查询实时接口"`
	commonApi.Author
	api.QueryCoreDataReq
}

type GetKsReportCoreDataRes struct {
	g.Meta `mime:"application/json"`
	*api.SeriesCorePageDataSnake
}

type PullKsSeriesReportCoreDataReq struct {
	g.Meta `path:"/get/report/stats" tags:"短剧广告报表明细表" method:"get" summary:"获取短剧广告报表明细表信息"`
	commonApi.Author
	AdvertiserId int64  `p:"advertiserId" v:"required#主键必须"` //通过主键获取
	AppId        int64  `p:"appId" v:"required#主键必须"`
	StatTime     string `p:"statTime" v:"required#日期必须"`
}

type PullKsSeriesReportCoreDataRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"`
}

// KsSeriesReportCoreDataSearchReq 分页请求参数
type KsSeriesReportCoreDataSearchReq struct {
	g.Meta `path:"/list" tags:"短剧核心总览数据报表" method:"post" summary:"短剧核心总览数据报表列表"`
	commonApi.Author
	model.KsSeriesReportCoreDataSearchReq
}

// KsSeriesReportCoreDataSearchRes 列表返回结果
type KsSeriesReportCoreDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsSeriesReportCoreDataSearchRes
}

// KsSeriesReportCoreDataExportReq 导出请求
type KsSeriesReportCoreDataExportReq struct {
	g.Meta `path:"/export" tags:"短剧核心总览数据报表" method:"get" summary:"短剧核心总览数据报表导出"`
	commonApi.Author
	model.KsSeriesReportCoreDataSearchReq
}

// KsSeriesReportCoreDataExportRes 导出响应
type KsSeriesReportCoreDataExportRes struct {
	commonApi.EmptyRes
}

// KsSeriesReportCoreDataAddReq 添加操作请求参数
type KsSeriesReportCoreDataAddReq struct {
	g.Meta `path:"/add" tags:"短剧核心总览数据报表" method:"post" summary:"短剧核心总览数据报表添加"`
	commonApi.Author
	*model.KsSeriesReportCoreDataAddReq
}

// KsSeriesReportCoreDataAddRes 添加操作返回结果
type KsSeriesReportCoreDataAddRes struct {
	commonApi.EmptyRes
}

// KsSeriesReportCoreDataEditReq 修改操作请求参数
type KsSeriesReportCoreDataEditReq struct {
	g.Meta `path:"/edit" tags:"短剧核心总览数据报表" method:"put" summary:"短剧核心总览数据报表修改"`
	commonApi.Author
	*model.KsSeriesReportCoreDataEditReq
}

// KsSeriesReportCoreDataEditRes 修改操作返回结果
type KsSeriesReportCoreDataEditRes struct {
	commonApi.EmptyRes
}

// KsSeriesReportCoreDataGetReq 获取一条数据请求
type KsSeriesReportCoreDataGetReq struct {
	g.Meta `path:"/get" tags:"短剧核心总览数据报表" method:"get" summary:"获取短剧核心总览数据报表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsSeriesReportCoreDataGetRes 获取一条数据结果
type KsSeriesReportCoreDataGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsSeriesReportCoreDataInfoRes
}

// KsSeriesReportCoreDataDeleteReq 删除数据请求
type KsSeriesReportCoreDataDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧核心总览数据报表" method:"delete" summary:"删除短剧核心总览数据报表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsSeriesReportCoreDataDeleteRes 删除数据返回
type KsSeriesReportCoreDataDeleteRes struct {
	commonApi.EmptyRes
}
