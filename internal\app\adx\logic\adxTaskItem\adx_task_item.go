// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: internal/app/adx/logic/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdxTaskItem(New())
}

func New() service.IAdxTaskItem {
	return &sAdxTaskItem{}
}

type sAdxTaskItem struct{}

func (s *sAdxTaskItem) List(ctx context.Context, req *model.AdxTaskItemSearchReq) (listRes *model.AdxTaskItemSearchRes, err error) {
	listRes = new(model.AdxTaskItemSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxTaskItem.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxTaskItem.Columns().Id+" = ?", req.Id)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdxTaskItem.Columns().TaskId+" = ?", gconv.Int64(req.TaskId))
		}
		if req.MaterialUrl != "" {
			m = m.Where(dao.AdxTaskItem.Columns().MaterialUrl+" = ?", req.MaterialUrl)
		}
		if req.MaterialType != "" {
			m = m.Where(dao.AdxTaskItem.Columns().MaterialType+" = ?", req.MaterialType)
		}
		if req.MaterialSize != "" {
			m = m.Where(dao.AdxTaskItem.Columns().MaterialSize+" = ?", req.MaterialSize)
		}
		if req.RelatedScript != "" {
			m = m.Where(dao.AdxTaskItem.Columns().RelatedScript+" = ?", req.RelatedScript)
		}
		if req.OperationTime != "" {
			m = m.Where(dao.AdxTaskItem.Columns().OperationTime+" = ?", gconv.Time(req.OperationTime))
		}
		if req.Result != "" {
			m = m.Where(dao.AdxTaskItem.Columns().Result+" = ?", req.Result)
		}
		if req.FailReason != "" {
			m = m.Where(dao.AdxTaskItem.Columns().FailReason+" = ?", req.FailReason)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdxTaskItem.Columns().CreatedAt+" >=? AND "+dao.AdxTaskItem.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxTaskItemListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxTaskItemListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxTaskItemListRes{
				Id:            v.Id,
				TaskId:        v.TaskId,
				MaterialUrl:   v.MaterialUrl,
				ImgUrl:        v.ImgUrl,
				MaterialType:  v.MaterialType,
				MaterialSize:  v.MaterialSize,
				RelatedScript: v.RelatedScript,
				OperationTime: v.OperationTime,
				Result:        v.Result,
				FailReason:    v.FailReason,
				CreatedAt:     v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdxTaskItem) GetExportData(ctx context.Context, req *model.AdxTaskItemSearchReq) (listRes []*model.AdxTaskItemInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxTaskItem.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxTaskItem.Columns().Id+" = ?", req.Id)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdxTaskItem.Columns().TaskId+" = ?", gconv.Int64(req.TaskId))
		}
		if req.MaterialUrl != "" {
			m = m.Where(dao.AdxTaskItem.Columns().MaterialUrl+" = ?", req.MaterialUrl)
		}
		if req.MaterialType != "" {
			m = m.Where(dao.AdxTaskItem.Columns().MaterialType+" = ?", req.MaterialType)
		}
		if req.MaterialSize != "" {
			m = m.Where(dao.AdxTaskItem.Columns().MaterialSize+" = ?", req.MaterialSize)
		}
		if req.RelatedScript != "" {
			m = m.Where(dao.AdxTaskItem.Columns().RelatedScript+" = ?", req.RelatedScript)
		}
		if req.OperationTime != "" {
			m = m.Where(dao.AdxTaskItem.Columns().OperationTime+" = ?", gconv.Time(req.OperationTime))
		}
		if req.Result != "" {
			m = m.Where(dao.AdxTaskItem.Columns().Result+" = ?", req.Result)
		}
		if req.FailReason != "" {
			m = m.Where(dao.AdxTaskItem.Columns().FailReason+" = ?", req.FailReason)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdxTaskItem.Columns().CreatedAt+" >=? AND "+dao.AdxTaskItem.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxTaskItem) GetById(ctx context.Context, id int64) (res *model.AdxTaskItemInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxTaskItem.Ctx(ctx).WithAll().Where(dao.AdxTaskItem.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxTaskItem) GetByTaskId(ctx context.Context, id int64) (res []*model.AdxTaskItemInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = []*model.AdxTaskItemInfoRes{}
		err = dao.AdxTaskItem.Ctx(ctx).WithAll().Where(dao.AdxTaskItem.Columns().TaskId, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxTaskItem) Add(ctx context.Context, req *model.AdxTaskItemAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTaskItem.Ctx(ctx).Insert(do.AdxTaskItem{
			TaskId:        req.TaskId,
			MaterialUrl:   req.MaterialUrl,
			MaterialType:  req.MaterialType,
			MaterialSize:  req.MaterialSize,
			RelatedScript: req.RelatedScript,
			OperationTime: req.OperationTime,
			Result:        req.Result,
			FailReason:    req.FailReason,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// AddBatch 批量添加
func (s *sAdxTaskItem) AddBatch(ctx context.Context, req []*model.AdxTaskItemAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTaskItem.Ctx(ctx).Insert(req)
		liberr.ErrIsNil(ctx, err, "批量添加失败")
	})
	return
}

func (s *sAdxTaskItem) Edit(ctx context.Context, req *model.AdxTaskItemEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTaskItem.Ctx(ctx).WherePri(req.Id).Update(do.AdxTaskItem{
			TaskId:        req.TaskId,
			MaterialUrl:   req.MaterialUrl,
			MaterialType:  req.MaterialType,
			MaterialSize:  req.MaterialSize,
			RelatedScript: req.RelatedScript,
			OperationTime: req.OperationTime,
			Result:        req.Result,
			FailReason:    req.FailReason,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

// DownloadStatus(ctx context.Context, id int64, status string, reason string) error
func (s *sAdxTaskItem) DownloadStatus(ctx context.Context, id int64, status string, reason string) error {
	return g.Try(ctx, func(ctx context.Context) {
		_, err := dao.AdxTaskItem.Ctx(ctx).Where(dao.AdxTaskItem.Columns().Id, id).Update(do.AdxTaskItem{
			Result:     status,
			FailReason: reason,
			UpdatedAt:  gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
		item, err := s.GetById(ctx, id)
		//if item.Result == "执行完成" {
		//	return
		//}
		// 判断当前任务的所有的item是否都完成
		taskItemList, err := service.AdxTaskItem().GetByTaskId(ctx, item.TaskId)
		if len(taskItemList) > 0 {
			for i, res := range taskItemList {
				if res.Result != "执行完成" {
					break
				}
				if len(taskItemList) == i+1 {
					// 更新task
					task, _ := service.AdxTask().GetById(ctx, item.TaskId)
					_ = service.AdxTask().Edit(ctx, &model.AdxTaskEditReq{
						Id:             item.TaskId,
						TaskName:       task.TaskName,
						OperationType:  task.OperationType,
						OperationCount: task.OperationCount,
						OperationTime:  task.OperationTime,
						Status:         "执行完成",
						Operator:       task.Operator,
						DownloadUrl:    task.DownloadUrl,
						FailLog:        task.FailLog,
						TotalFileSize:  task.TotalFileSize,
					})
				}
			}
		}

	})
}

// 根据taskId 获取 urls
func (s *sAdxTaskItem) GetUrlsByTaskId(ctx context.Context, taskId int64) (urls []string, err error) {
	arrayList, err := dao.AdxTaskItem.Ctx(ctx).Array(dao.AdxTaskItem.Columns().MaterialUrl, dao.AdxTaskItem.Columns().TaskId, taskId)
	urls = make([]string, 0)
	for _, value := range arrayList {
		urls = append(urls, value.String())
	}
	return
}

func (s *sAdxTaskItem) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTaskItem.Ctx(ctx).Delete(dao.AdxTaskItem.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
