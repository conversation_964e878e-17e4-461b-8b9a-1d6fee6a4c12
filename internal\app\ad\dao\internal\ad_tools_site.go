// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/dao/internal/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdToolsSiteDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdToolsSiteDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns AdToolsSiteColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdToolsSiteColumns defines and stores column names for table ad_tools_site.
type AdToolsSiteColumns struct {
	Id           string // ID
	AdvertiserId string // 广告主ID
	SiteId       string // 站点ID
	SiteName     string // 站点名称
	Status       string // 状态
	SiteType     string // 站点类型
	FunctionType string // 功能类型
	Thumbnail    string // 缩略图
	MainUserId   string // 主用户ID

	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

var adToolsSiteColumns = AdToolsSiteColumns{
	Id:           "id",
	AdvertiserId: "advertiser_id",
	SiteId:       "site_id",
	SiteName:     "site_name",
	Status:       "status",
	SiteType:     "site_type",
	FunctionType: "function_type",
	Thumbnail:    "thumbnail",
	MainUserId:   "main_user_id",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
}

// NewAdToolsSiteDao creates and returns a new DAO object for table data access.
func NewAdToolsSiteDao() *AdToolsSiteDao {
	return &AdToolsSiteDao{
		group:   "default",
		table:   "ad_tools_site",
		columns: adToolsSiteColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdToolsSiteDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdToolsSiteDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdToolsSiteDao) Columns() AdToolsSiteColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdToolsSiteDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdToolsSiteDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdToolsSiteDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
