// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-07 11:44:22
// 生成路径: internal/app/ad/logic/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdAccountInfo(New())
}

func New() service.IKsAdAccountInfo {
	return &sKsAdAccountInfo{}
}

type sKsAdAccountInfo struct{}

func (s *sKsAdAccountInfo) List(ctx context.Context, req *model.KsAdAccountInfoSearchReq) (listRes *model.KsAdAccountInfoSearchRes, err error) {
	listRes = new(model.KsAdAccountInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdAccountInfo.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.Id != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().Id+" = ?", req.Id)
		}
		if req.PrimaryIndustryName != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().PrimaryIndustryName+" like ?", "%"+req.PrimaryIndustryName+"%")
		}
		if req.AppId != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().AppId+" = ?", gconv.Int(req.AppId))
		}
		if req.IndustryId != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().IndustryId+" = ?", gconv.Int64(req.IndustryId))
		}
		if req.AccountId != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().AccountId+" = ?", gconv.Int64(req.AccountId))
		}
		if req.IndustryName != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().IndustryName+" like ?", "%"+req.IndustryName+"%")
		}
		if req.AccountName != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().AccountName+" like ?", "%"+req.AccountName+"%")
		}
		if req.DeliveryType != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().DeliveryType+" = ?", gconv.Int(req.DeliveryType))
		}
		if req.PrimaryIndustryId != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().PrimaryIndustryId+" = ?", gconv.Int64(req.PrimaryIndustryId))
		}
		if req.EffectFirst != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().EffectFirst+" = ?", gconv.Int(req.EffectFirst))
		}
		if req.CorporationName != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().CorporationName+" like ?", "%"+req.CorporationName+"%")
		}
		if req.ProductName != "" {
			m = m.Where(dao.KsAdAccountInfo.Columns().ProductName+" like ?", "%"+req.ProductName+"%")
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdAccountInfo.Columns().CreatedAt+" >=? AND "+dao.KsAdAccountInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "advertiser_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdAccountInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdAccountInfoListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdAccountInfoListRes{
				AdvertiserId:        v.AdvertiserId,
				Id:                  v.Id,
				PrimaryIndustryName: v.PrimaryIndustryName,
				AppId:               v.AppId,
				IndustryId:          v.IndustryId,
				AccountId:           v.AccountId,
				IndustryName:        v.IndustryName,
				AccountName:         v.AccountName,
				DeliveryType:        v.DeliveryType,
				PrimaryIndustryId:   v.PrimaryIndustryId,
				EffectFirst:         v.EffectFirst,
				CorporationName:     v.CorporationName,
				ProductName:         v.ProductName,
				DirectRebate:        v.DirectRebate,
				ContractRebate:      v.ContractRebate,
				RechargeBalance:     v.RechargeBalance,
				Balance:             v.Balance,
				CreatedAt:           v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdAccountInfo) GetById(ctx context.Context, id int64) (res *model.KsAdAccountInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdAccountInfo.Ctx(ctx).WithAll().Where(dao.KsAdAccountInfo.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdAccountInfo) Add(ctx context.Context, req *model.KsAdAccountInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdAccountInfo.Ctx(ctx).Insert(do.KsAdAccountInfo{
			AdvertiserId:        req.AdvertiserId,
			PrimaryIndustryName: req.PrimaryIndustryName,
			AppId:               req.AppId,
			IndustryId:          req.IndustryId,
			AccountId:           req.AccountId,
			IndustryName:        req.IndustryName,
			AccountName:         req.AccountName,
			DeliveryType:        req.DeliveryType,
			PrimaryIndustryId:   req.PrimaryIndustryId,
			EffectFirst:         req.EffectFirst,
			CorporationName:     req.CorporationName,
			ProductName:         req.ProductName,
			DirectRebate:        req.DirectRebate,
			ContractRebate:      req.ContractRebate,
			RechargeBalance:     req.RechargeBalance,
			Balance:             req.Balance,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// BatchAdd 批量添加
func (s *sKsAdAccountInfo) BatchAdd(ctx context.Context, reqList []*model.KsAdAccountInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		list := make([]do.KsAdAccountInfo, 0)
		for _, req := range reqList {
			list = append(list, do.KsAdAccountInfo{
				AdvertiserId:        req.AdvertiserId,
				PrimaryIndustryName: req.PrimaryIndustryName,
				AppId:               req.AppId,
				IndustryId:          req.IndustryId,
				AccountId:           req.AccountId,
				IndustryName:        req.IndustryName,
				AccountName:         req.AccountName,
				DeliveryType:        req.DeliveryType,
				PrimaryIndustryId:   req.PrimaryIndustryId,
				EffectFirst:         req.EffectFirst,
				CorporationName:     req.CorporationName,
				ProductName:         req.ProductName,
				DirectRebate:        req.DirectRebate,
				ContractRebate:      req.ContractRebate,
				RechargeBalance:     req.RechargeBalance,
				Balance:             req.Balance,
			})
		}
		_, err = dao.KsAdAccountInfo.Ctx(ctx).Save(list)
	})
	return
}

func (s *sKsAdAccountInfo) Edit(ctx context.Context, req *model.KsAdAccountInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdAccountInfo.Ctx(ctx).WherePri(req.Id).Update(do.KsAdAccountInfo{
			AdvertiserId:        req.AdvertiserId,
			PrimaryIndustryName: req.PrimaryIndustryName,
			AppId:               req.AppId,
			IndustryId:          req.IndustryId,
			AccountId:           req.AccountId,
			IndustryName:        req.IndustryName,
			AccountName:         req.AccountName,
			DeliveryType:        req.DeliveryType,
			PrimaryIndustryId:   req.PrimaryIndustryId,
			EffectFirst:         req.EffectFirst,
			CorporationName:     req.CorporationName,
			ProductName:         req.ProductName,
			DirectRebate:        req.DirectRebate,
			ContractRebate:      req.ContractRebate,
			RechargeBalance:     req.RechargeBalance,
			Balance:             req.Balance,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdAccountInfo) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdAccountInfo.Ctx(ctx).Delete(dao.KsAdAccountInfo.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetKsAdAccountList 拉取快手账户列表
func (s *sKsAdAccountInfo) GetKsAdAccountList(ctx context.Context, accessToken string, aid, appId int64) (res []*model.KsAdAccountInfoAddReq, err error) {
	res = make([]*model.KsAdAccountInfoAddReq, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		if aid == 0 || appId == 0 {
			err = errors.New("aid appid 不能为空")
			return
		}
		if len(accessToken) == 0 {
			accessToken = api.GetAccessTokenByAppIdCache(aid)
		}
		accountRes, err := api.GetKSApiClient().QueryAccountInfoService.SetReq(api.QueryAccountInfoReq{
			AdvertiserID: aid,
		}).AccessToken(accessToken).Do()
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- QueryAccountInfoService err：%v --------------------", err))
		} else {
			// 每次插入500条记录
			batchSize := 500
			var batch []*model.KsAdAccountInfoAddReq
			for _, item := range accountRes.Data.AccountList {
				req := &model.KsAdAccountInfoAddReq{
					AdvertiserId: aid,
					AppId:        int(appId),
					AccountId:    gconv.Int64(item.AccountId),
					AccountName:  item.AccountName,
				}
				batch = append(batch, req)
				// 当达到500条时，执行一次批量插入，并重置批次
				if len(batch) >= batchSize {
					if err = service.KsAdAccountInfo().BatchAdd(ctx, batch); err != nil {
						return
					}
					// 清空当前批次，准备下一批
					batch = batch[:0]
				}
			}

			// 如果还有剩余不足500条的记录，也执行一次插入
			if len(batch) > 0 {
				if err = service.KsAdAccountInfo().BatchAdd(ctx, batch); err != nil {
					return
				}
			}

		}

	})
	return
}

func (s *sKsAdAccountInfo) GetAllAdvertiserIds(ctx context.Context) (res []*model.KsAdAccountAIdsRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdAccountInfo.Ctx(ctx).
			Fields(dao.KsAdAccountInfo.Columns().AdvertiserId).
			Group(dao.KsAdAccountInfo.Columns().AdvertiserId).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}
