// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: api/v1/ad/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdThirdMiniProgramConfigSearchReq 分页请求参数
type AdThirdMiniProgramConfigSearchReq struct {
	g.Meta `path:"/list" tags:"第三方小程序配置" method:"get" summary:"第三方小程序配置列表"`
	commonApi.Author
	model.AdThirdMiniProgramConfigSearchReq
}

// AdThirdMiniProgramConfigSearchRes 列表返回结果
type AdThirdMiniProgramConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdThirdMiniProgramConfigSearchRes
}

// AdThirdMiniProgramConfigAddReq 添加操作请求参数
type AdThirdMiniProgramConfigAddReq struct {
	g.Meta `path:"/add" tags:"第三方小程序配置" method:"post" summary:"第三方小程序配置添加"`
	commonApi.Author
	*model.AdThirdMiniProgramConfigAddReq
}

// AdThirdMiniProgramConfigAddRes 添加操作返回结果
type AdThirdMiniProgramConfigAddRes struct {
	commonApi.EmptyRes
}

// AdThirdMiniProgramConfigEditReq 修改操作请求参数
type AdThirdMiniProgramConfigEditReq struct {
	g.Meta `path:"/edit" tags:"第三方小程序配置" method:"put" summary:"第三方小程序配置修改"`
	commonApi.Author
	*model.AdThirdMiniProgramConfigEditReq
}

// AdThirdMiniProgramConfigEditRes 修改操作返回结果
type AdThirdMiniProgramConfigEditRes struct {
	commonApi.EmptyRes
}

// AdThirdMiniProgramConfigGetReq 获取一条数据请求
type AdThirdMiniProgramConfigGetReq struct {
	g.Meta `path:"/get" tags:"第三方小程序配置" method:"get" summary:"获取第三方小程序配置信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdThirdMiniProgramConfigGetRes 获取一条数据结果
type AdThirdMiniProgramConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdThirdMiniProgramConfigInfoRes
}

// AdThirdMiniProgramConfigDeleteReq 删除数据请求
type AdThirdMiniProgramConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"第三方小程序配置" method:"delete" summary:"删除第三方小程序配置"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdThirdMiniProgramConfigDeleteRes 删除数据返回
type AdThirdMiniProgramConfigDeleteRes struct {
	commonApi.EmptyRes
}
