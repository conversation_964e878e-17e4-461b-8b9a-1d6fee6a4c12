// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:33
// 生成路径: internal/app/ad/controller/dz_ad_account_users.go
// 生成人：cyao
// desc:权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type dzAdAccountUsersController struct {
	systemController.BaseController
}

var DzAdAccountUsers = new(dzAdAccountUsersController)

// List 列表
func (c *dzAdAccountUsersController) List(ctx context.Context, req *ad.DzAdAccountUsersSearchReq) (res *ad.DzAdAccountUsersSearchRes, err error) {
	res = new(ad.DzAdAccountUsersSearchRes)
	res.DzAdAccountUsersSearchRes, err = service.DzAdAccountUsers().List(ctx, &req.DzAdAccountUsersSearchReq)
	return
}

// Get 获取权限用户关联
func (c *dzAdAccountUsersController) Get(ctx context.Context, req *ad.DzAdAccountUsersGetReq) (res *ad.DzAdAccountUsersGetRes, err error) {
	res = new(ad.DzAdAccountUsersGetRes)
	res.DzAdAccountUsersInfoRes, err = service.DzAdAccountUsers().GetByChannelId(ctx, req.ChannelId)
	return
}

// Add 添加权限用户关联
func (c *dzAdAccountUsersController) Add(ctx context.Context, req *ad.DzAdAccountUsersAddReq) (res *ad.DzAdAccountUsersAddRes, err error) {
	err = service.DzAdAccountUsers().Add(ctx, req.DzAdAccountUsersAddReq)
	return
}

// Edit 修改权限用户关联
func (c *dzAdAccountUsersController) Edit(ctx context.Context, req *ad.DzAdAccountUsersEditReq) (res *ad.DzAdAccountUsersEditRes, err error) {
	err = service.DzAdAccountUsers().Edit(ctx, req.DzAdAccountUsersEditReq)
	return
}

// Delete 删除权限用户关联
func (c *dzAdAccountUsersController) Delete(ctx context.Context, req *ad.DzAdAccountUsersDeleteReq) (res *ad.DzAdAccountUsersDeleteRes, err error) {
	err = service.DzAdAccountUsers().Delete(ctx, req.ChannelIds)
	return
}
