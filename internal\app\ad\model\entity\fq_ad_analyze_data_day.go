// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: internal/app/ad/model/entity/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdAnalyzeDataDay is the golang structure for table fq_ad_analyze_data_day.
type FqAdAnalyzeDataDay struct {
	gmeta.Meta     `orm:"table:fq_ad_analyze_data_day"`
	Id             int         `orm:"id,primary" json:"id"`                 // id
	DistributorId  int64       `orm:"distributor_id" json:"distributorId"`  // 渠道ID
	PromotionId    string      `orm:"promotion_id" json:"promotionId"`      // 推广链id
	StatDate       string      `orm:"stat_date" json:"statDate"`            // 统计日期
	AddDesktopNum  int         `orm:"add_desktop_num" json:"addDesktopNum"` // 新增桌面用户数
	RechargeNum    int         `orm:"recharge_num" json:"rechargeNum"`      // 充值用户数
	UserNum        int         `orm:"user_num" json:"userNum"`              // 用户总数
	RoiDetail      string      `orm:"roi_detail" json:"roiDetail"`          // ROI 明细，JSON 格式存储
	RechargeAmount int64       `orm:"recharge_amount" json:"rechargeAmount"   dc:"充值金额"`
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"` // 删除时间
}
