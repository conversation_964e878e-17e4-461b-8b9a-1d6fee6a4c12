// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-10-14 11:57:06
// 生成路径: api/v1/theater/theater_hour_times_stat.go
// 生成人：cq
// desc:短剧小时多维度次数统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterHourTimesStatSearchReq 分页请求参数
type TheaterHourTimesStatSearchReq struct {
	g.Meta `path:"/list" tags:"短剧小时多维度次数统计" method:"get" summary:"短剧小时多维度次数统计列表"`
	commonApi.Author
	model.TheaterHourTimesStatSearchReq
}

// TheaterHourTimesStatSearchRes 列表返回结果
type TheaterHourTimesStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterHourTimesStatSearchRes
}

// TheaterHourTimesStatSearchReq 短剧小时多维度次数统计任务
type TheaterHourTimesStatTaskReq struct {
	g.Meta `path:"/task" tags:"短剧小时多维度次数统计" method:"get" summary:"短剧小时多维度次数统计任务"`
	commonApi.Author
	model.TheaterHourTimesStatTaskReq
}

// TheaterHourTimesStatSearchRes 列表返回结果
type TheaterHourTimesStatTaskRes struct {
	g.Meta `mime:"application/json"`
}
