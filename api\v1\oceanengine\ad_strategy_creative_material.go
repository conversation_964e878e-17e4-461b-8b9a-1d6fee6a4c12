// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:28
// 生成路径: api/v1/oceanengine/ad_strategy_creative_material.go
// 生成人：gfast
// desc:巨量广告搭建-创意素材相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyCreativeMaterialSearchReq 分页请求参数
type AdStrategyCreativeMaterialSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-创意素材" method:"get" summary:"巨量广告搭建-创意素材列表"`
	commonApi.Author
	model.AdStrategyCreativeMaterialSearchReq
}

// AdStrategyCreativeMaterialSearchRes 列表返回结果
type AdStrategyCreativeMaterialSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyCreativeMaterialSearchRes
}

// AdStrategyCreativeMaterialAddReq 添加操作请求参数
type AdStrategyCreativeMaterialAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-创意素材" method:"post" summary:"巨量广告搭建-创意素材添加"`
	commonApi.Author
	*model.AdStrategyCreativeMaterialAddReq
}

// AdStrategyCreativeMaterialAddRes 添加操作返回结果
type AdStrategyCreativeMaterialAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyCreativeMaterialEditReq 修改操作请求参数
type AdStrategyCreativeMaterialEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-创意素材" method:"put" summary:"巨量广告搭建-创意素材修改"`
	commonApi.Author
	*model.AdStrategyCreativeMaterialEditReq
}

// AdStrategyCreativeMaterialEditRes 修改操作返回结果
type AdStrategyCreativeMaterialEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyCreativeMaterialGetReq 获取一条数据请求
type AdStrategyCreativeMaterialGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-创意素材" method:"get" summary:"获取巨量广告搭建-创意素材信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyCreativeMaterialGetRes 获取一条数据结果
type AdStrategyCreativeMaterialGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyCreativeMaterialInfoRes
}

// AdStrategyCreativeMaterialDeleteReq 删除数据请求
type AdStrategyCreativeMaterialDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-创意素材" method:"delete" summary:"删除巨量广告搭建-创意素材"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyCreativeMaterialDeleteRes 删除数据返回
type AdStrategyCreativeMaterialDeleteRes struct {
	commonApi.EmptyRes
}
