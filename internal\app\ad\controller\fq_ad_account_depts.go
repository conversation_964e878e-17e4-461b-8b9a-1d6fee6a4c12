// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-16 11:16:20
// 生成路径: internal/app/ad/controller/fq_ad_account_depts.go
// 生成人：gfast
// desc:番茄账号权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdAccountDeptsController struct {
	systemController.BaseController
}

var FqAdAccountDepts = new(fqAdAccountDeptsController)

// List 列表
func (c *fqAdAccountDeptsController) List(ctx context.Context, req *ad.FqAdAccountDeptsSearchReq) (res *ad.FqAdAccountDeptsSearchRes, err error) {
	res = new(ad.FqAdAccountDeptsSearchRes)
	res.FqAdAccountDeptsSearchRes, err = service.FqAdAccountDepts().List(ctx, &req.FqAdAccountDeptsSearchReq)
	return
}

// Export 导出excel
func (c *fqAdAccountDeptsController) Export(ctx context.Context, req *ad.FqAdAccountDeptsExportReq) (res *ad.FqAdAccountDeptsExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdAccountDeptsInfoRes
		//表头
		tableHead = []interface{}{"渠道ID", "部门ID"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.FqAdAccountDepts().GetExportData(ctx, &req.FqAdAccountDeptsSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.DistributorId,
				v.DetpId,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("番茄账号权限和部门关联")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取番茄账号权限和部门关联
func (c *fqAdAccountDeptsController) Get(ctx context.Context, req *ad.FqAdAccountDeptsGetReq) (res *ad.FqAdAccountDeptsGetRes, err error) {
	res = new(ad.FqAdAccountDeptsGetRes)
	res.FqAdAccountDeptsInfoRes, err = service.FqAdAccountDepts().GetByDetpId(ctx, req.DistributorId)
	return
}

// Add 添加番茄账号权限和部门关联
func (c *fqAdAccountDeptsController) Add(ctx context.Context, req *ad.FqAdAccountDeptsAddReq) (res *ad.FqAdAccountDeptsAddRes, err error) {
	err = service.FqAdAccountDepts().Add(ctx, req.FqAdAccountDeptsAddReq)
	return
}

// Edit 修改番茄账号权限和部门关联
func (c *fqAdAccountDeptsController) Edit(ctx context.Context, req *ad.FqAdAccountDeptsEditReq) (res *ad.FqAdAccountDeptsEditRes, err error) {
	err = service.FqAdAccountDepts().Edit(ctx, req.FqAdAccountDeptsEditReq)
	return
}

// Delete 删除番茄账号权限和部门关联
func (c *fqAdAccountDeptsController) Delete(ctx context.Context, req *ad.FqAdAccountDeptsDeleteReq) (res *ad.FqAdAccountDeptsDeleteRes, err error) {
	err = service.FqAdAccountDepts().Delete(ctx, req.DistributorIds)
	return
}
