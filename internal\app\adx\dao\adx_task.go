// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: internal/app/adx/dao/adx_task.go
// 生成人：cyao
// desc:ADX任务主表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao/internal"
)

// adxTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adxTaskDao struct {
	*internal.AdxTaskDao
}

var (
	// AdxTask is globally public accessible object for table tools_gen_table operations.
	AdxTask = adxTaskDao{
		internal.NewAdxTaskDao(),
	}
)

// Fill with you ideas below.
