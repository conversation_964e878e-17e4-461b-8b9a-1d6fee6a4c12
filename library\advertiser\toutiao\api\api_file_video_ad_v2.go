/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// FileVideoAdV2ApiService AdvertiserInfoV2Api service  上传广告视频
type FileVideoAdV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2FileVideoAdPostRequest
}

type ApiOpenApi2FileVideoAdPostRequest struct {
	AdvertiserId   int64
	Filename       string
	IsAigc         bool
	IsGuideVideo   bool
	Labels         []string
	UploadType     models.FileVideoAdV2UploadType
	VideoFile      *models.FormFileInfo
	VideoSignature string
	VideoUrl       string
}

func (r *FileVideoAdV2ApiService) SetCfg(cfg *conf.Configuration) *FileVideoAdV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileVideoAdV2ApiService) SetRequest(req ApiOpenApi2FileVideoAdPostRequest) *FileVideoAdV2ApiService {
	r.Request = &req
	return r
}

func (r *FileVideoAdV2ApiService) AccessToken(accessToken string) *FileVideoAdV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *FileVideoAdV2ApiService) Do() (data *models.FileVideoAdV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/video/ad/"
	localVarQueryParams := map[string]string{}
	if r.Request == nil {
		return nil, errors.New("请求参数不能为空")
	}
	if r.Request.AdvertiserId == 0 {
		return nil, errors.New("请求参数AdvertiserId不能为空")
	}
	if r.Request.UploadType == "" {
		// 默认 file 但是我们默认走url
		r.Request.UploadType = models.UPLOAD_BY_FILE_FileVideoAdV2UploadType
	}
	if r.Request.UploadType == models.UPLOAD_BY_FILE_FileVideoAdV2UploadType && r.Request.VideoFile == nil {
		return nil, errors.New("upload_type为UPLOAD_BY_FILE,ImageFile必填")
	}
	if r.Request.UploadType == models.UPLOAD_BY_URL_FileVideoAdV2UploadType && r.Request.VideoUrl == "" {
		return nil, errors.New("upload_type为UPLOAD_BY_URL,ImageUrl 必须填写")
	}
	if r.Request.AdvertiserId > 0 {
		localVarQueryParams["advertiser_id"] = fmt.Sprintf("%d", r.Request.AdvertiserId)
		//libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	}
	if len(r.Request.UploadType) > 0 {
		localVarQueryParams["upload_type"] = string(r.Request.UploadType)
		//libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "upload_type", r.Request.UploadType)
	}

	if len(r.Request.VideoSignature) > 0 {
		localVarQueryParams["image_signature"] = r.Request.VideoSignature
		//libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_signature", r.Request.VideoSignature)
	}
	if len(r.Request.VideoUrl) > 0 {
		localVarQueryParams["image_url"] = r.Request.VideoUrl
		//libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_url", r.Request.VideoUrl)
	}
	request := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "multipart/form-data").
		SetHeader("Access-Token", r.token).
		SetMultipartFormData(localVarQueryParams).
		SetResult(&models.FileVideoAdV2Response{})
	if r.Request.VideoFile != nil {
		var formFiles = make(map[string]*models.FormFileInfo)
		formFiles["video_file"] = r.Request.VideoFile
		request.SetFileReader("video_file", r.Request.VideoFile.FileName, bytes.NewReader(r.Request.VideoFile.FileBytes))
	}
	response, err := request.Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.FileVideoAdV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/video/ad/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
