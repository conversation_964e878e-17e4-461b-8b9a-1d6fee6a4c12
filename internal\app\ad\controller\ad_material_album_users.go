// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-13 15:31:10
// 生成路径: internal/app/ad/controller/ad_material_album_users.go
// 生成人：cyao
// desc:广告素材专辑和用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adMaterialAlbumUsersController struct {
	systemController.BaseController
}

var AdMaterialAlbumUsers = new(adMaterialAlbumUsersController)

// List 列表
func (c *adMaterialAlbumUsersController) List(ctx context.Context, req *ad.AdMaterialAlbumUsersSearchReq) (res *ad.AdMaterialAlbumUsersSearchRes, err error) {
	res = new(ad.AdMaterialAlbumUsersSearchRes)
	res.AdMaterialAlbumUsersSearchRes, err = service.AdMaterialAlbumUsers().List(ctx, &req.AdMaterialAlbumUsersSearchReq)
	return
}

// Get 获取广告素材专辑和用户关联
func (c *adMaterialAlbumUsersController) Get(ctx context.Context, req *ad.AdMaterialAlbumUsersGetReq) (res *ad.AdMaterialAlbumUsersGetRes, err error) {
	res = new(ad.AdMaterialAlbumUsersGetRes)
	res.AdMaterialAlbumUsersInfoRes, err = service.AdMaterialAlbumUsers().GetByAlbumId(ctx, req.AlbumId)
	return
}

// Add 添加广告素材专辑和用户关联
func (c *adMaterialAlbumUsersController) Add(ctx context.Context, req *ad.AdMaterialAlbumUsersAddReq) (res *ad.AdMaterialAlbumUsersAddRes, err error) {
	err = service.AdMaterialAlbumUsers().Add(ctx, req.AdMaterialAlbumUsersAddReq)
	return
}

// Edit 修改广告素材专辑和用户关联
func (c *adMaterialAlbumUsersController) Edit(ctx context.Context, req *ad.AdMaterialAlbumUsersEditReq) (res *ad.AdMaterialAlbumUsersEditRes, err error) {
	err = service.AdMaterialAlbumUsers().Edit(ctx, req.AdMaterialAlbumUsersEditReq)
	return
}

// Delete 删除广告素材专辑和用户关联
func (c *adMaterialAlbumUsersController) Delete(ctx context.Context, req *ad.AdMaterialAlbumUsersDeleteReq) (res *ad.AdMaterialAlbumUsersDeleteRes, err error) {
	err = service.AdMaterialAlbumUsers().Delete(ctx, req.AlbumIds)
	return
}
