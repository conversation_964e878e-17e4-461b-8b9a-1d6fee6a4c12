// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:33:58
// 生成路径: api/v1/adx/adx_product.go
// 生成人：cq
// desc:ADX产品信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxProductSearchReq 分页请求参数
type AdxProductSearchReq struct {
	g.Meta `path:"/list" tags:"ADX产品信息表" method:"post" summary:"ADX产品信息表列表"`
	commonApi.Author
	model.AdxProductSearchReq
}

// AdxProductSearchRes 列表返回结果
type AdxProductSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxProductSearchRes
}

// AdxProductExportReq 导出请求
type AdxProductExportReq struct {
	g.Meta `path:"/export" tags:"ADX产品信息表" method:"post" summary:"ADX产品信息表导出"`
	commonApi.Author
	model.AdxProductSearchReq
}

// AdxProductExportRes 导出响应
type AdxProductExportRes struct {
	commonApi.EmptyRes
}

// AdxProductAddReq 添加操作请求参数
type AdxProductAddReq struct {
	g.Meta `path:"/add" tags:"ADX产品信息表" method:"post" summary:"ADX产品信息表添加"`
	commonApi.Author
	*model.AdxProductAddReq
}

// AdxProductAddRes 添加操作返回结果
type AdxProductAddRes struct {
	commonApi.EmptyRes
}

// AdxProductEditReq 修改操作请求参数
type AdxProductEditReq struct {
	g.Meta `path:"/edit" tags:"ADX产品信息表" method:"put" summary:"ADX产品信息表修改"`
	commonApi.Author
	*model.AdxProductEditReq
}

// AdxProductEditRes 修改操作返回结果
type AdxProductEditRes struct {
	commonApi.EmptyRes
}

// AdxProductGetReq 获取一条数据请求
type AdxProductGetReq struct {
	g.Meta `path:"/get" tags:"ADX产品信息表" method:"get" summary:"获取ADX产品信息表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdxProductGetRes 获取一条数据结果
type AdxProductGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxProductInfoRes
}

// AdxProductDeleteReq 删除数据请求
type AdxProductDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX产品信息表" method:"post" summary:"删除ADX产品信息表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdxProductDeleteRes 删除数据返回
type AdxProductDeleteRes struct {
	commonApi.EmptyRes
}

// AdxMaterialSyncReq 同步ADX产品请求参数
type AdxProductSyncReq struct {
	g.Meta `path:"/sync" tags:"ADX产品信息表" method:"post" summary:"同步ADX产品"`
	commonApi.Author
	model.AdxProductSearchReq
}

// AdxProductSyncRes 同步ADX产品返回结果
type AdxProductSyncRes struct {
	g.Meta `mime:"application/json"`
}
