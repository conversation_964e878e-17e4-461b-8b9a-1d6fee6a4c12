// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-13 15:31:08
// 生成路径: internal/app/ad/model/entity/ad_material_album_depts.go
// 生成人：cyao
// desc:广告素材专辑和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterialAlbumDepts is the golang structure for table ad_material_album_depts.
type AdMaterialAlbumDepts struct {
	gmeta.Meta `orm:"table:ad_material_album_depts"`
	AlbumId    int `orm:"album_id,primary" json:"albumId"` // 专辑ID
	DetpId     int `orm:"detp_id,primary" json:"detpId"`   // 部门ID
}
