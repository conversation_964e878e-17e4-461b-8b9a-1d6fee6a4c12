// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:31
// 生成路径: internal/app/ad/controller/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type dzAdAccountDeptsController struct {
	systemController.BaseController
}

var DzAdAccountDepts = new(dzAdAccountDeptsController)

// List 列表
func (c *dzAdAccountDeptsController) List(ctx context.Context, req *ad.DzAdAccountDeptsSearchReq) (res *ad.DzAdAccountDeptsSearchRes, err error) {
	res = new(ad.DzAdAccountDeptsSearchRes)
	res.DzAdAccountDeptsSearchRes, err = service.DzAdAccountDepts().List(ctx, &req.DzAdAccountDeptsSearchReq)
	return
}

// Export 导出excel
func (c *dzAdAccountDeptsController) Export(ctx context.Context, req *ad.DzAdAccountDeptsExportReq) (res *ad.DzAdAccountDeptsExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.DzAdAccountDeptsInfoRes
		//表头
		tableHead = []interface{}{"渠道id", "部门ID"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.DzAdAccountDepts().GetExportData(ctx, &req.DzAdAccountDeptsSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.ChannelId,
				v.DetpId,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("权限和部门关联")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取权限和部门关联
func (c *dzAdAccountDeptsController) Get(ctx context.Context, req *ad.DzAdAccountDeptsGetReq) (res *ad.DzAdAccountDeptsGetRes, err error) {
	res = new(ad.DzAdAccountDeptsGetRes)
	res.DzAdAccountDeptsInfoRes, err = service.DzAdAccountDepts().GetByChannelId(ctx, req.ChannelId)
	return
}

// Add 添加权限和部门关联
func (c *dzAdAccountDeptsController) Add(ctx context.Context, req *ad.DzAdAccountDeptsAddReq) (res *ad.DzAdAccountDeptsAddRes, err error) {
	err = service.DzAdAccountDepts().Add(ctx, req.DzAdAccountDeptsAddReq)
	return
}

// Edit 修改权限和部门关联
func (c *dzAdAccountDeptsController) Edit(ctx context.Context, req *ad.DzAdAccountDeptsEditReq) (res *ad.DzAdAccountDeptsEditRes, err error) {
	err = service.DzAdAccountDepts().Edit(ctx, req.DzAdAccountDeptsEditReq)
	return
}

// Delete 删除权限和部门关联
func (c *dzAdAccountDeptsController) Delete(ctx context.Context, req *ad.DzAdAccountDeptsDeleteReq) (res *ad.DzAdAccountDeptsDeleteRes, err error) {
	err = service.DzAdAccountDepts().Delete(ctx, req.ChannelIds)
	return
}
