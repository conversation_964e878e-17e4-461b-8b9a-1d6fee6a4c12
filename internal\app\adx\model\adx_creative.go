// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:33:12
// 生成路径: internal/app/adx/model/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxCreativeInfoRes is the golang structure for table adx_creative.
type AdxCreativeInfoRes struct {
	gmeta.Meta    `orm:"table:adx_creative"`
	CreativeId    uint64           `orm:"creative_id,primary" json:"creativeId" dc:"计划ID"` // 计划ID
	FirstSeen     string           `orm:"first_seen" json:"firstSeen" dc:"首次出现日期"`         // 首次出现日期
	LastSeen      string           `orm:"last_seen" json:"lastSeen" dc:"最后出现日期"`           // 最后出现日期
	MaterialId    uint64           `orm:"material_id" json:"materialId" dc:"素材ID"`         // 素材ID
	MediaId       uint             `orm:"media_id" json:"mediaId" dc:"媒体ID"`               // 媒体ID
	MobileType    uint             `orm:"mobile_type" json:"mobileType" dc:"投放平台1.安卓2.苹果"` // 投放平台1.安卓2.苹果
	NumDate       map[string]int64 `orm:"num_date" json:"numDate" dc:"投放日期及次数"`            // 投放日期及次数
	PositionId    uint             `orm:"position_id" json:"positionId" dc:"广告位ID"`        // 广告位ID
	ProductId     uint64           `orm:"product_id" json:"productId" dc:"投放产品ID"`         // 投放产品ID
	PublisherId   uint64           `orm:"publisher_id" json:"publisherId" dc:"公司ID"`       // 公司ID
	TalentAccount string           `orm:"talent_account" json:"talentAccount" dc:"达人账号"`   // 达人账号
	TalentName    string           `orm:"talent_name" json:"talentName" dc:"达人名称"`         // 达人名称
	TargetUrl     string           `orm:"target_url" json:"targetUrl" dc:"落地页"`            // 落地页
	TargetUrlId   uint64           `orm:"target_url_id" json:"targetUrlId" dc:"落地页ID"`     // 落地页ID
	Title1        string           `orm:"title1" json:"title1" dc:"使用的标题"`                 // 使用的标题
	Title2        string           `orm:"title2" json:"title2" dc:"使用的文案"`                 // 使用的文案
}

type AdxCreativeListRes struct {
	CreativeId    uint64           `json:"creativeId" dc:"计划ID"`
	FirstSeen     string           `json:"firstSeen" dc:"首次出现日期"`
	LastSeen      string           `json:"lastSeen" dc:"最后出现日期"`
	MaterialId    uint64           `json:"materialId" dc:"素材ID"`
	MediaId       uint             `json:"mediaId" dc:"媒体ID"`
	MobileType    uint             `json:"mobileType" dc:"投放平台1.安卓2.苹果"`
	NumDate       map[string]int64 `json:"numDate" dc:"投放日期及次数"`
	PositionId    uint             `json:"positionId" dc:"广告位ID"`
	ProductId     uint64           `json:"productId" dc:"投放产品ID"`
	PublisherId   uint64           `json:"publisherId" dc:"公司ID"`
	TalentAccount string           `json:"talentAccount" dc:"达人账号"`
	TalentName    string           `json:"talentName" dc:"达人名称"`
	TargetUrl     string           `json:"targetUrl" dc:"落地页"`
	TargetUrlId   uint64           `json:"targetUrlId" dc:"落地页ID"`
	Title1        string           `json:"title1" dc:"使用的标题"`
	Title2        string           `json:"title2" dc:"使用的文案"`
}

// AdxCreativeSearchReq 分页请求参数
type AdxCreativeSearchReq struct {
	comModel.PageReq
	CreativeId    string           `p:"creativeId" dc:"计划ID"`                                                 //计划ID
	FirstSeen     string           `p:"firstSeen" dc:"首次出现日期"`                                                //首次出现日期
	LastSeen      string           `p:"lastSeen" dc:"最后出现日期"`                                                 //最后出现日期
	MaterialId    string           `p:"materialId" v:"materialId@integer#素材ID需为整数" dc:"素材ID"`                 //素材ID
	MediaId       string           `p:"mediaId" v:"mediaId@integer#媒体ID需为整数" dc:"媒体ID"`                       //媒体ID
	MobileType    string           `p:"mobileType" v:"mobileType@integer#投放平台1.安卓2.苹果需为整数" dc:"投放平台1.安卓2.苹果"` //投放平台1.安卓2.苹果
	NumDate       map[string]int64 `p:"numDate" dc:"投放日期及次数"`                                                 //投放日期及次数
	PositionId    string           `p:"positionId" v:"positionId@integer#广告位ID需为整数" dc:"广告位ID"`               //广告位ID
	ProductId     string           `p:"productId" v:"productId@integer#投放产品ID需为整数" dc:"投放产品ID"`               //投放产品ID
	PublisherId   string           `p:"publisherId" v:"publisherId@integer#公司ID需为整数" dc:"公司ID"`               //公司ID
	TalentAccount string           `p:"talentAccount" dc:"达人账号"`                                              //达人账号
	TalentName    string           `p:"talentName" dc:"达人名称"`                                                 //达人名称
	TargetUrl     string           `p:"targetUrl" dc:"落地页"`                                                   //落地页
	TargetUrlId   string           `p:"targetUrlId" v:"targetUrlId@integer#落地页ID需为整数" dc:"落地页ID"`             //落地页ID
	Title1        string           `p:"title1" dc:"使用的标题"`                                                    //使用的标题
	Title2        string           `p:"title2" dc:"使用的文案"`                                                    //使用的文案
	StartTime     string           `p:"startTime" dc:"开始时间"`
	EndTime       string           `p:"endTime" dc:"结束时间"`
}

// AdxCreativeSearchRes 列表返回结果
type AdxCreativeSearchRes struct {
	comModel.ListRes
	List []*AdxCreativeListRes `json:"list"`
}

// AdxCreativeAddReq 添加操作请求参数
type AdxCreativeAddReq struct {
	CreativeId    uint64           `p:"creativeId" v:"required#主键ID不能为空" dc:"计划ID"`
	FirstSeen     string           `p:"firstSeen"  dc:"首次出现日期"`
	LastSeen      string           `p:"lastSeen"  dc:"最后出现日期"`
	MaterialId    uint64           `p:"materialId"  dc:"素材ID"`
	MediaId       uint             `p:"mediaId"  dc:"媒体ID"`
	MobileType    uint             `p:"mobileType"  dc:"投放平台1.安卓2.苹果"`
	NumDate       map[string]int64 `p:"numDate" v:"required#投放日期及次数不能为空" dc:"投放日期及次数"`
	PositionId    uint             `p:"positionId"  dc:"广告位ID"`
	ProductId     uint64           `p:"productId"  dc:"投放产品ID"`
	PublisherId   uint64           `p:"publisherId"  dc:"公司ID"`
	TalentAccount string           `p:"talentAccount"  dc:"达人账号"`
	TalentName    string           `p:"talentName" v:"required#达人名称不能为空" dc:"达人名称"`
	TargetUrl     string           `p:"targetUrl"  dc:"落地页"`
	TargetUrlId   uint64           `p:"targetUrlId"  dc:"落地页ID"`
	Title1        string           `p:"title1"  dc:"使用的标题"`
	Title2        string           `p:"title2"  dc:"使用的文案"`
}

// AdxCreativeEditReq 修改操作请求参数
type AdxCreativeEditReq struct {
	CreativeId    uint64           `p:"creativeId" v:"required#主键ID不能为空" dc:"计划ID"`
	FirstSeen     string           `p:"firstSeen"  dc:"首次出现日期"`
	LastSeen      string           `p:"lastSeen"  dc:"最后出现日期"`
	MaterialId    uint64           `p:"materialId"  dc:"素材ID"`
	MediaId       uint             `p:"mediaId"  dc:"媒体ID"`
	MobileType    uint             `p:"mobileType"  dc:"投放平台1.安卓2.苹果"`
	NumDate       map[string]int64 `p:"numDate" v:"required#投放日期及次数不能为空" dc:"投放日期及次数"`
	PositionId    uint             `p:"positionId"  dc:"广告位ID"`
	ProductId     uint64           `p:"productId"  dc:"投放产品ID"`
	PublisherId   uint64           `p:"publisherId"  dc:"公司ID"`
	TalentAccount string           `p:"talentAccount"  dc:"达人账号"`
	TalentName    string           `p:"talentName" v:"required#达人名称不能为空" dc:"达人名称"`
	TargetUrl     string           `p:"targetUrl"  dc:"落地页"`
	TargetUrlId   uint64           `p:"targetUrlId"  dc:"落地页ID"`
	Title1        string           `p:"title1"  dc:"使用的标题"`
	Title2        string           `p:"title2"  dc:"使用的文案"`
}
