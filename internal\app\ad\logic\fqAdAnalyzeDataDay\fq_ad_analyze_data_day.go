// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: internal/app/ad/logic/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdAnalyzeDataDay(New())
}

func New() service.IFqAdAnalyzeDataDay {
	return &sFqAdAnalyzeDataDay{}
}

type sFqAdAnalyzeDataDay struct{}

func (s *sFqAdAnalyzeDataDay) List(ctx context.Context, req *model.FqAdAnalyzeDataDaySearchReq) (listRes *model.FqAdAnalyzeDataDaySearchRes, err error) {
	listRes = new(model.FqAdAnalyzeDataDaySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAnalyzeDataDay.Ctx(ctx).WithAll().As("fqdata")
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " fqdata.distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " fqdata.distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
			//WhereOr("d.detp_id = ?", userInfo.DeptId).
			//WhereOr("u.specify_user_id = ?", userInfo.Id)
		}

		if req.Id != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().Id+" = ?", req.Id)
		}
		//if req.FqDistributorId != "" {
		//	m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().DistributorId+" = ?", gconv.Int64(req.FqDistributorId))
		//}
		//if len(req.FqDistributorIds) > 0 {
		//	m = m.WhereIn("fqdata."+dao.FqAdAnalyzeDataDay.Columns().DistributorId, req.FqDistributorIds)
		//}

		if req.FqDistributorId != "" {
			list, _ := service.FqAdAccountChannel().GetByDistributorId(ctx, gconv.Int64(req.FqDistributorId))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdAnalyzeDataDay.Columns().DistributorId, ids)
				}
			}
		}
		if len(req.FqDistributorIds) > 0 {
			list, _ := service.FqAdAccountChannel().GetByDistributorIds(ctx, gconv.SliceInt64(req.FqChannelDistributorIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId, ids)
				}
			}

		}

		if len(req.FqChannelDistributorId) > 0 {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId+" = ?", gconv.Int64(req.FqChannelDistributorId))
		}
		if len(req.FqChannelDistributorIds) > 0 {
			m = m.WhereIn("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId, req.FqChannelDistributorIds)
		}

		if req.PromotionId != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().PromotionId+" = ?", gconv.Int64(req.PromotionId))
		}
		if req.StatDate != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().StatDate+" = ?", req.StatDate)
		}
		if req.AddDesktopNum != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().AddDesktopNum+" = ?", gconv.Int(req.AddDesktopNum))
		}
		if req.RechargeNum != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().RechargeNum+" = ?", gconv.Int(req.RechargeNum))
		}
		if req.UserNum != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().UserNum+" = ?", gconv.Int(req.UserNum))
		}
		if req.StartTime != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().StatDate+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeDataDay.Columns().StatDate+" <= ?", req.EndTime)
		}

		if len(req.ChannelCode) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code = "+fmt.Sprintf("'%s'", req.ChannelCode))
		} else if len(req.ChannelCodes) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code in "+fmt.Sprintf("(%s)", libUtils.BuildSqlInStr(req.ChannelCodes)))
		} else {
			m = m.LeftJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id ")
		}

		if req.PitcherId > 0 {
			m = m.InnerJoin(systemDao.SysUser.Table(), "su", fmt.Sprintf("su.id = sc.user_id and su.id = %v", req.PitcherId))
		} else {
			m = m.LeftJoin(systemDao.SysUser.Table(), "su", "su.id = sc.user_id")
		}
		if req.DistributorId > 0 {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
		} else {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			if req.OrderBy == "paidUserNum" {
				req.OrderType = "rechargeNum"
			}
			if req.OrderType == "uv" {
				req.OrderType = "userNum"
			}
			if req.OrderType == "addDesktopUserNum" {
				req.OrderType = "addDesktopNum"
			}
			order = "fqdata." + libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
		}

		var summary = new(model.FqAdAnalyzeDataSummary2)
		err = m.FieldSum("fqdata."+dao.FqAdAnalyzeDataDay.Columns().AddDesktopNum, "addDesktopNum").
			FieldSum("fqdata."+dao.FqAdAnalyzeDataDay.Columns().UserNum, "userNum").
			FieldSum("fqdata."+dao.FqAdAnalyzeDataDay.Columns().RechargeNum, "rechargeNum").
			FieldSum("fqdata."+dao.FqAdAnalyzeDataDay.Columns().RechargeAmount, "rechargeAmount").
			Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取统计数据失败")
		listRes.Summary = *summary
		listRes.Summary.RechargeAmount = gconv.Float64(summary.RechargeAmount) / 100

		var res []*model.FqAdAnalyzeDataDayListRes
		err = m.Fields("fqdata.*, su.user_name as userName , su.id as userId ,de.leader as distributorName, fq_promotion_id, channel_code").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdAnalyzeDataDayListRes, len(res))
		disIds := make([]int64, 0)
		for k, v := range res {
			disIds = append(disIds, v.DistributorId)
			listRes.List[k] = &model.FqAdAnalyzeDataDayListRes{
				Id:              v.Id,
				DistributorId:   v.DistributorId,
				PromotionId:     v.PromotionId,
				StatDate:        v.StatDate,
				AddDesktopNum:   v.AddDesktopNum,
				RechargeNum:     v.RechargeNum,
				UserNum:         v.UserNum,
				RoiDetail:       v.RoiDetail,
				CreatedAt:       v.CreatedAt,
				ChannelCode:     v.ChannelCode,
				UserId:          v.UserId,
				UserName:        v.UserName,
				DistributorName: v.DistributorName,
				RechargeAmount:  gconv.Float64(v.RechargeAmount) / 100,
			}
		}

		// 根据分销id获取分销信息
		var distributorInfo = make([]model.FqAdAccountInfoRes, 0)
		channelList, _ := service.FqAdAccountChannel().GetByChannelDistributorIds(ctx, disIds)
		disIds = make([]int64, 0)
		for _, item := range channelList {
			disIds = append(disIds, item.DistributorId)
		}
		err = dao.FqAdAccount.Ctx(ctx).WhereIn("distributor_id", disIds).Scan(&distributorInfo)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if len(distributorInfo) > 0 {
			for i, dataListRes := range listRes.List {
				for _, infoRes := range channelList {
					if dataListRes.DistributorId == infoRes.ChannelDistributorId {
						listRes.List[i].FqChannelDistributorId = gconv.String(infoRes.ChannelDistributorId)
						listRes.List[i].FqChannel = infoRes.NickName
						listRes.List[i].AppId = gconv.String(infoRes.AppId)
						listRes.List[i].AppName = infoRes.AppName
						for _, item := range distributorInfo {
							if item.DistributorId == infoRes.DistributorId {
								listRes.List[i].FqAccount = item.AccountName
							}
						}
					}
				}
			}
		}

	})
	return
}

func (s *sFqAdAnalyzeDataDay) GetExportData(ctx context.Context, req *model.FqAdAnalyzeDataDaySearchReq) (listRes []*model.FqAdAnalyzeDataDayInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAnalyzeDataDay.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().Id+" = ?", req.Id)
		}
		if req.FqDistributorId != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().DistributorId+" = ?", gconv.Int64(req.FqDistributorId))
		}
		if req.PromotionId != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().PromotionId+" = ?", gconv.Int64(req.PromotionId))
		}
		if req.StatDate != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().StatDate+" = ?", req.StatDate)
		}
		if req.AddDesktopNum != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().AddDesktopNum+" = ?", gconv.Int(req.AddDesktopNum))
		}
		if req.RechargeNum != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().RechargeNum+" = ?", gconv.Int(req.RechargeNum))
		}
		if req.UserNum != "" {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().UserNum+" = ?", gconv.Int(req.UserNum))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.FqAdAnalyzeDataDay.Columns().CreatedAt+" >=? AND "+dao.FqAdAnalyzeDataDay.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdAnalyzeDataDay) GetById(ctx context.Context, id int) (res *model.FqAdAnalyzeDataDayInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAnalyzeDataDay.Ctx(ctx).WithAll().Where(dao.FqAdAnalyzeDataDay.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAnalyzeDataDay) Add(ctx context.Context, req *model.FqAdAnalyzeDataDayAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAnalyzeDataDay.Ctx(ctx).Insert(do.FqAdAnalyzeDataDay{
			DistributorId:  req.DistributorId,
			PromotionId:    req.PromotionId,
			StatDate:       req.StatDate,
			AddDesktopNum:  req.AddDesktopNum,
			RechargeNum:    req.RechargeNum,
			UserNum:        req.UserNum,
			RechargeAmount: req.RechargeAmount,
			RoiDetail:      req.RoiDetail,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// BatchAdd
func (s *sFqAdAnalyzeDataDay) BatchAdd(ctx context.Context, req []*model.FqAdAnalyzeDataDayAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		list := make([]do.FqAdAnalyzeDataDay, len(req))
		for k, v := range req {
			list[k] = do.FqAdAnalyzeDataDay{
				DistributorId:  v.DistributorId,
				PromotionId:    v.PromotionId,
				StatDate:       v.StatDate,
				AddDesktopNum:  v.AddDesktopNum,
				RechargeNum:    v.RechargeNum,
				UserNum:        v.UserNum,
				RoiDetail:      v.RoiDetail,
				RechargeAmount: v.RechargeAmount,
				CreatedAt:      gtime.Now(),
			}
		}
		_, err = dao.FqAdAnalyzeDataDay.Ctx(ctx).Save(list)
	})
	return
}

func (s *sFqAdAnalyzeDataDay) Edit(ctx context.Context, req *model.FqAdAnalyzeDataDayEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAnalyzeDataDay.Ctx(ctx).WherePri(req.Id).Update(do.FqAdAnalyzeDataDay{
			DistributorId:  req.DistributorId,
			PromotionId:    req.PromotionId,
			StatDate:       req.StatDate,
			AddDesktopNum:  req.AddDesktopNum,
			RechargeNum:    req.RechargeNum,
			UserNum:        req.UserNum,
			RechargeAmount: req.RechargeAmount,
			RoiDetail:      req.RoiDetail,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdAnalyzeDataDay) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAnalyzeDataDay.Ctx(ctx).Delete(dao.FqAdAnalyzeDataDay.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
