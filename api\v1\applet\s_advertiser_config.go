// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-06 17:53:48
// 生成路径: api/v1/applet/s_advertiser_config.go
// 生成人：cq
// desc:广告配置表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SAdvertiserConfigSearchReq 分页请求参数
type SAdvertiserConfigSearchReq struct {
	g.Meta `path:"/list" tags:"小程序设置 - 流量主管理" method:"get" summary:"流量主管理列表"`
	commonApi.Author
	model.SAdvertiserConfigSearchReq
}

// SAdvertiserConfigSearchRes 列表返回结果
type SAdvertiserConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SAdvertiserConfigSearchRes
}

// SAdvertiserConfigAddReq 添加操作请求参数
type SAdvertiserConfigAddReq struct {
	g.Meta `path:"/add" tags:"小程序设置 - 流量主管理" method:"post" summary:"流量主添加"`
	commonApi.Author
	*model.SAdvertiserConfigAddReq
}

// SAdvertiserConfigAddRes 添加操作返回结果
type SAdvertiserConfigAddRes struct {
	commonApi.EmptyRes
}

// SAdvertiserConfigEditReq 修改操作请求参数
type SAdvertiserConfigEditReq struct {
	g.Meta `path:"/edit" tags:"小程序设置 - 流量主管理" method:"put" summary:"流量主修改"`
	commonApi.Author
	*model.SAdvertiserConfigEditReq
}

// SAdvertiserConfigEditRes 修改操作返回结果
type SAdvertiserConfigEditRes struct {
	commonApi.EmptyRes
}

// SAdvertiserConfigGetReq 获取一条数据请求
type SAdvertiserConfigGetReq struct {
	g.Meta `path:"/get" tags:"小程序设置 - 流量主管理" method:"get" summary:"获取流量主信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SAdvertiserConfigGetRes 获取一条数据结果
type SAdvertiserConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SAdvertiserConfigInfoRes
}

// SAdvertiserConfigDeleteReq 删除数据请求
type SAdvertiserConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"小程序设置 - 流量主管理" method:"post" summary:"删除流量主"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SAdvertiserConfigDeleteRes 删除数据返回
type SAdvertiserConfigDeleteRes struct {
	commonApi.EmptyRes
}
