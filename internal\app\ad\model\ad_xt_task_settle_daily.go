// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-21 14:30:52
// 生成路径: internal/app/ad/model/ad_xt_task_settle_daily.go
// 生成人：cyao
// desc:星图结算数据分天
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdXtTaskSettleDailyInfoRes is the golang structure for table ad_xt_task_settle_daily.
type AdXtTaskSettleDailyInfoRes struct {
	gmeta.Meta    `orm:"table:ad_xt_task_settle_daily"`
	Id            int64       `orm:"id,primary" json:"id" dc:"ID"`                            // ID
	AdvertiserId  string      `orm:"advertiser_id" json:"advertiserId" dc:"账户ID对应star_id"`    // 账户ID对应star_id
	TaskId        string      `orm:"task_id" json:"taskId" dc:"任务ID"`                         // 任务ID
	ItemId        int64       `orm:"item_id" json:"itemId" dc:"视频唯一ID"`                       // 视频唯一ID
	AuthorId      int64       `orm:"author_id" json:"authorId" dc:"作者ID"`                     // 作者ID
	Uid           int64       `orm:"uid" json:"uid" dc:"用户平台ID"`                              // 用户平台ID
	ProviderId    int64       `orm:"provider_id" json:"providerId" dc:"内容提供方ID"`              // 内容提供方ID
	PDate         *gtime.Time `orm:"p_date" json:"pDate" dc:"日期"`                             // 日期
	EstSales      int64       `orm:"est_sales" json:"estSales" dc:"当天产生的预估付费流水金额"`            // 当天产生的预估付费流水金额
	SettleCps     int64       `orm:"settle_cps" json:"settleCps" dc:"当天发放的达人付费分佣金额"`          // 当天发放的达人付费分佣金额
	EstAdCost     int64       `orm:"est_ad_cost" json:"estAdCost" dc:"当天产生的预估广告消耗金额"`         // 当天产生的预估广告消耗金额
	SettleAdShare int64       `orm:"settle_ad_share" json:"settleAdShare" dc:"当天发放的达人广告分成金额"` // 当天发放的达人广告分成金额
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                   // 创建时间
}

type AdXtTaskSettleDailyListRes struct {
	Id             int64       `json:"id" dc:"ID"`
	AdvertiserId   string      `json:"advertiserId" dc:"账户ID对应star_id"`
	TaskId         string      `json:"taskId" dc:"任务ID"`
	TaskName       string      `json:"TaskName" dc:"任务ID"`
	ItemId         string      `json:"itemId" dc:"视频唯一ID"`
	Title          string      `json:"title" dc:"视频标题"`
	AuthorId       string      `json:"authorId" dc:"作者ID"`
	AuthorNickname string      `json:"authorNickname" dc:"达人名称"` // 达人名称
	AdvertiserNick string      `json:"advertiserNick" dc:"账户名称"` // 账户名称
	Uid            int64       `json:"uid" dc:"用户平台ID"`
	ProviderId     int64       `json:"providerId" dc:"内容提供方ID"`
	PDate          *gtime.Time `json:"pDate" dc:"日期"`
	EstSales       int64       `json:"estSales" dc:"当天产生的预估付费流水金额"`
	SettleCps      int64       `json:"settleCps" dc:"当天发放的达人付费分佣金额"`
	EstAdCost      int64       `json:"estAdCost" dc:"当天产生的预估广告消耗金额"`
	SettleAdShare  int64       `json:"settleAdShare" dc:"当天发放的达人广告分成金额"`
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdXtTaskSettleDailySearchReq 分页请求参数
type AdXtTaskSettleDailySearchReq struct {
	comModel.PageReq
	Id            string   `p:"id" dc:"ID"`                                                             //ID
	AdvertiserId  string   `p:"advertiserId" dc:"账户ID对应star_id"`                                        //账户ID对应star_id
	TaskId        string   `p:"taskId" dc:"任务ID"`                                                       //任务ID
	TaskIds       []string `p:"taskIds" dc:"任务ID"`                                                      //任务ID
	ItemId        string   `p:"itemId" v:"itemId@integer#视频唯一ID需为整数" dc:"视频唯一ID"`                       //视频唯一ID
	AuthorId      string   `p:"authorId" v:"authorId@integer#作者ID需为整数" dc:"作者ID"`                       //作者ID
	Uid           string   `p:"uid" v:"uid@integer#用户平台ID需为整数" dc:"用户平台ID"`                             //用户平台ID
	ProviderId    string   `p:"providerId" v:"providerId@integer#内容提供方ID需为整数" dc:"内容提供方ID"`             //内容提供方ID
	PDate         string   `p:"pDate" v:"pDate@datetime#日期需为YYYY-MM-DD hh:mm:ss格式" dc:"日期"`             //日期
	EstSales      int64    `p:"estSales" dc:"当天产生的预估付费流水金额"`                                            //当天产生的预估付费流水金额
	SettleCps     int64    `p:"settleCps" dc:"当天发放的达人付费分佣金额"`                                           //当天发放的达人付费分佣金额
	EstAdCost     int64    `p:"estAdCost" v:"estAdCost@float#当天产生的预估广告消耗金额需为浮点数" dc:"当天产生的预估广告消耗金额"`    //当天产生的预估广告消耗金额
	SettleAdShare int64    `p:"settleAdShare" dc:"当天发放的达人广告分成金额"`                                       //当天发放的达人广告分成金额
	CreatedAt     string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	StartDate     string   `p:"startDate" dc:"开始时间"`
	EndDate       string   `p:"endDate" dc:"结束时间"`
}

// AdXtTaskSettleDailySearchRes 列表返回结果
type AdXtTaskSettleDailySearchRes struct {
	comModel.ListRes
	List    []*AdXtTaskSettleDailyListRes `json:"list"`
	Summary *AdXtTaskSettleSummary        `json:"summary"`
}

// AdXtTaskSettleDailyAddReq 添加操作请求参数
type AdXtTaskSettleDailyAddReq struct {
	AdvertiserId  string      `p:"advertiserId" v:"required#账户ID对应star_id不能为空" dc:"账户ID对应star_id"`
	TaskId        string      `p:"taskId" v:"required#任务ID不能为空" dc:"任务ID"`
	ItemId        int64       `p:"itemId"  dc:"视频唯一ID"`
	AuthorId      int64       `p:"authorId"  dc:"作者ID"`
	Uid           int64       `p:"uid"  dc:"用户平台ID"`
	ProviderId    int64       `p:"providerId"  dc:"内容提供方ID"`
	PDate         *gtime.Time `p:"pDate"  dc:"日期"`
	EstSales      int64       `p:"estSales"  dc:"当天产生的预估付费流水金额"`
	SettleCps     int64       `p:"settleCps"  dc:"当天发放的达人付费分佣金额"`
	EstAdCost     int64       `p:"estAdCost"  dc:"当天产生的预估广告消耗金额"`
	SettleAdShare int64       `p:"settleAdShare"  dc:"当天发放的达人广告分成金额"`
}

// AdXtTaskSettleDailyEditReq 修改操作请求参数
type AdXtTaskSettleDailyEditReq struct {
	Id            int64       `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AdvertiserId  string      `p:"advertiserId" v:"required#账户ID对应star_id不能为空" dc:"账户ID对应star_id"`
	TaskId        string      `p:"taskId" v:"required#任务ID不能为空" dc:"任务ID"`
	ItemId        int64       `p:"itemId"  dc:"视频唯一ID"`
	AuthorId      int64       `p:"authorId"  dc:"作者ID"`
	Uid           int64       `p:"uid"  dc:"用户平台ID"`
	ProviderId    int64       `p:"providerId"  dc:"内容提供方ID"`
	PDate         *gtime.Time `p:"pDate"  dc:"日期"`
	EstSales      int64       `p:"estSales"  dc:"当天产生的预估付费流水金额"`
	SettleCps     int64       `p:"settleCps"  dc:"当天发放的达人付费分佣金额"`
	EstAdCost     int64       `p:"estAdCost"  dc:"当天产生的预估广告消耗金额"`
	SettleAdShare int64       `p:"settleAdShare"  dc:"当天发放的达人广告分成金额"`
}
