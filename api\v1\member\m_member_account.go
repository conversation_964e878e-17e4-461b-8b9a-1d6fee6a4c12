// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-09 08:30:36
// 生成路径: api/v1/member/m_member_account.go
// 生成人：lx
// desc:用户金币表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberAccountSearchReq 分页请求参数
type MMemberAccountSearchReq struct {
	g.Meta `path:"/list" tags:"用户金币表" method:"get" summary:"用户金币表列表"`
	commonApi.Author
	model.MMemberAccountSearchReq
}

// MMemberAccountSearchRes 列表返回结果
type MMemberAccountSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAccountSearchRes
}

// MMemberAccountAddReq 添加操作请求参数
type MMemberAccountAddReq struct {
	g.Meta `path:"/add" tags:"用户金币表" method:"post" summary:"用户金币表添加"`
	commonApi.Author
	*model.MMemberAccountAddReq
}

// MMemberAccountAddRes 添加操作返回结果
type MMemberAccountAddRes struct {
	commonApi.EmptyRes
}

// MMemberAccountEditReq 修改操作请求参数
type MMemberAccountEditReq struct {
	g.Meta `path:"/edit" tags:"用户金币表" method:"post" summary:"用户金币表修改"`
	commonApi.Author
	*model.MMemberAccountEditReq
}

// MMemberAccountEditRes 修改操作返回结果
type MMemberAccountEditRes struct {
	commonApi.EmptyRes
}

// MMemberAccountGetReq 获取一条数据请求
type MMemberAccountGetReq struct {
	g.Meta `path:"/get" tags:"用户金币表" method:"post" summary:"获取用户金币表信息"`
	commonApi.Author
	SaasId string `p:"saasId" v:"required#主键必须"` //通过主键获取
}

// MMemberAccountGetRes 获取一条数据结果
type MMemberAccountGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAccountInfoRes
}

// MMemberAccountDeleteReq 删除数据请求
type MMemberAccountDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户金币表" method:"delete" summary:"删除用户金币表"`
	commonApi.Author
	SaasIds []string `p:"saasIds" v:"required#主键必须"` //通过主键删除
}

// MMemberAccountDeleteRes 删除数据返回
type MMemberAccountDeleteRes struct {
	commonApi.EmptyRes
}
