// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: internal/app/ad/model/entity/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdPlanLog is the golang structure for table ad_plan_log.
type AdPlanLog struct {
	gmeta.Meta      `orm:"table:ad_plan_log"`
	Id              int         `orm:"id,primary" json:"id"`                     // ID
	PlanId          int         `orm:"plan_id" json:"planId"`                    // 计划ID
	RuleName        string      `orm:"rule_name" json:"ruleName"`                // 计划规则名称
	MediaType       int         `orm:"media_type" json:"mediaType"`              // 媒体类型，1 巨量，2
	ObjectType      int         `orm:"object_type" json:"objectType"`            // 对象类型，1 账户，2 项目，3 广告
	ScopeType       int         `orm:"scope_type" json:"scopeType"`              // 范围，1 所有，2 指定范围
	ScopeObjectType int         `orm:"scope_object_type" json:"scopeObjectType"` // 指定范围类型，仅当 scope_type 为 2 时才不为空，1 账户，2 项目，3 广告
	ScopeEntityId   int64       `orm:"scope_entity_id" json:"scopeEntityId"`     // （和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空
	Conditions      string      `orm:"conditions" json:"conditions"`             // 满足条件
	Content         string      `orm:"content" json:"content"`                   // 执行操作的内容
	ExecutionState  int         `orm:"execution_state" json:"executionState"`    // 执行状态，1 成功，2 失败
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt"`              // 创建时间
	UpdatedAt       *gtime.Time `orm:"updated_at" json:"updatedAt"`              // 更新时间
}
