// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-22 11:52:52
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_unit.go
// 生成人：cq
// desc:快手策略组-广告组
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyUnit(New())
}

func New() service.IKsAdvertiserStrategyUnit {
	return &sKsAdvertiserStrategyUnit{}
}

type sKsAdvertiserStrategyUnit struct{}

func (s *sKsAdvertiserStrategyUnit) List(ctx context.Context, req *model.KsAdvertiserStrategyUnitSearchReq) (listRes *model.KsAdvertiserStrategyUnitSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyUnitSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyUnit.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().Id+" = ?", req.Id)
		}
		if req.StrategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().StrategyId+" = ?", req.StrategyId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.SceneCategory != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().SceneCategory+" = ?", req.SceneCategory)
		}
		if req.KsUserId != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().KsUserId+" = ?", gconv.Int64(req.KsUserId))
		}
		if req.ShortPlayAllocationMethod != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().ShortPlayAllocationMethod+" = ?", req.ShortPlayAllocationMethod)
		}
		if req.ProductAllocationMethod != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().ProductAllocationMethod+" = ?", req.ProductAllocationMethod)
		}
		if req.QuickSearch != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().QuickSearch+" = ?", gconv.Int(req.QuickSearch))
		}
		if req.TargetExplore != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().TargetExplore+" = ?", gconv.Int(req.TargetExplore))
		}
		if req.BeginTime != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().BeginTime+" = ?", req.BeginTime)
		}
		if req.EndTime != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().EndTime+" = ?", req.EndTime)
		}
		if req.ScheduleTime != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().ScheduleTime+" = ?", req.ScheduleTime)
		}
		if req.OcpxActionType != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().OcpxActionType+" = ?", gconv.Int(req.OcpxActionType))
		}
		if req.BidType != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().BidType+" = ?", gconv.Int(req.BidType))
		}
		if req.BidAllocationMethod != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().BidAllocationMethod+" = ?", req.BidAllocationMethod)
		}
		if req.UnitName != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().UnitName+" like ?", "%"+req.UnitName+"%")
		}
		if req.PutStatus != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().PutStatus+" = ?", gconv.Int(req.PutStatus))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyUnit.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyUnitListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyUnitListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyUnitListRes{
				Id:                        v.Id,
				StrategyId:                v.StrategyId,
				TaskId:                    v.TaskId,
				SceneCategory:             v.SceneCategory,
				SceneId:                   v.SceneId,
				KsUserId:                  v.KsUserId,
				ShortPlayAllocationMethod: v.ShortPlayAllocationMethod,
				ShortPlayData:             v.ShortPlayData,
				ProductAllocationMethod:   v.ProductAllocationMethod,
				ProductData:               v.ProductData,
				QuickSearch:               v.QuickSearch,
				TargetExplore:             v.TargetExplore,
				NegativeWordParam:         v.NegativeWordParam,
				BeginTime:                 v.BeginTime,
				EndTime:                   v.EndTime,
				ScheduleTime:              v.ScheduleTime,
				DayBudget:                 v.DayBudget,
				OcpxActionType:            v.OcpxActionType,
				BidType:                   v.BidType,
				BidAllocationMethod:       v.BidAllocationMethod,
				BidData:                   v.BidData,
				UnitName:                  v.UnitName,
				PutStatus:                 v.PutStatus,
				CreatedAt:                 v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyUnit) GetById(ctx context.Context, id uint64) (res *model.KsAdvertiserStrategyUnitInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyUnit.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyUnit.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyUnit) GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyUnitInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyUnit.Ctx(ctx).WithAll()
		if strategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().StrategyId+" = ?", strategyId)
		}
		if taskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyUnit.Columns().TaskId+" = ?", taskId)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyUnit) Add(ctx context.Context, req *model.KsAdvertiserStrategyUnitAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyUnit.Ctx(ctx).Insert(do.KsAdvertiserStrategyUnit{
			StrategyId:                req.StrategyId,
			TaskId:                    req.TaskId,
			SceneCategory:             req.SceneCategory,
			SceneId:                   req.SceneId,
			KsUserId:                  req.KsUserId,
			ShortPlayAllocationMethod: req.ShortPlayAllocationMethod,
			ShortPlayData:             req.ShortPlayData,
			ProductAllocationMethod:   req.ProductAllocationMethod,
			ProductData:               req.ProductData,
			QuickSearch:               req.QuickSearch,
			TargetExplore:             req.TargetExplore,
			NegativeWordParam:         req.NegativeWordParam,
			BeginTime:                 req.BeginTime,
			EndTime:                   req.EndTime,
			ScheduleTime:              req.ScheduleTime,
			DayBudget:                 req.DayBudget,
			OcpxActionType:            req.OcpxActionType,
			BidType:                   req.BidType,
			BidAllocationMethod:       req.BidAllocationMethod,
			BidData:                   req.BidData,
			UnitName:                  req.UnitName,
			PutStatus:                 req.PutStatus,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyUnit) Edit(ctx context.Context, req *model.KsAdvertiserStrategyUnitEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyUnit.Ctx(ctx).
			Where(dao.KsAdvertiserStrategyUnit.Columns().StrategyId, req.StrategyId).
			Update(do.KsAdvertiserStrategyUnit{
				TaskId:                    req.TaskId,
				SceneCategory:             req.SceneCategory,
				SceneId:                   req.SceneId,
				KsUserId:                  req.KsUserId,
				ShortPlayAllocationMethod: req.ShortPlayAllocationMethod,
				ShortPlayData:             req.ShortPlayData,
				ProductAllocationMethod:   req.ProductAllocationMethod,
				ProductData:               req.ProductData,
				QuickSearch:               req.QuickSearch,
				TargetExplore:             req.TargetExplore,
				NegativeWordParam:         req.NegativeWordParam,
				BeginTime:                 req.BeginTime,
				EndTime:                   req.EndTime,
				ScheduleTime:              req.ScheduleTime,
				DayBudget:                 req.DayBudget,
				OcpxActionType:            req.OcpxActionType,
				BidType:                   req.BidType,
				BidAllocationMethod:       req.BidAllocationMethod,
				BidData:                   req.BidData,
				UnitName:                  req.UnitName,
				PutStatus:                 req.PutStatus,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyUnit) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyUnit.Ctx(ctx).Delete(dao.KsAdvertiserStrategyUnit.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
