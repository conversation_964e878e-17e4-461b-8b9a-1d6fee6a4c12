// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: internal/app/ad/controller/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adThirdMiniProgramConfigController struct {
	systemController.BaseController
}

var AdThirdMiniProgramConfig = new(adThirdMiniProgramConfigController)

// List 列表
func (c *adThirdMiniProgramConfigController) List(ctx context.Context, req *ad.AdThirdMiniProgramConfigSearchReq) (res *ad.AdThirdMiniProgramConfigSearchRes, err error) {
	res = new(ad.AdThirdMiniProgramConfigSearchRes)
	res.AdThirdMiniProgramConfigSearchRes, err = service.AdThirdMiniProgramConfig().List(ctx, &req.AdThirdMiniProgramConfigSearchReq)
	return
}

// Get 获取第三方小程序配置
func (c *adThirdMiniProgramConfigController) Get(ctx context.Context, req *ad.AdThirdMiniProgramConfigGetReq) (res *ad.AdThirdMiniProgramConfigGetRes, err error) {
	res = new(ad.AdThirdMiniProgramConfigGetRes)
	res.AdThirdMiniProgramConfigInfoRes, err = service.AdThirdMiniProgramConfig().GetById(ctx, req.Id)
	return
}

// Add 添加第三方小程序配置
func (c *adThirdMiniProgramConfigController) Add(ctx context.Context, req *ad.AdThirdMiniProgramConfigAddReq) (res *ad.AdThirdMiniProgramConfigAddRes, err error) {
	err = service.AdThirdMiniProgramConfig().Add(ctx, req.AdThirdMiniProgramConfigAddReq)
	return
}

// Edit 修改第三方小程序配置
func (c *adThirdMiniProgramConfigController) Edit(ctx context.Context, req *ad.AdThirdMiniProgramConfigEditReq) (res *ad.AdThirdMiniProgramConfigEditRes, err error) {
	err = service.AdThirdMiniProgramConfig().Edit(ctx, req.AdThirdMiniProgramConfigEditReq)
	return
}

// Delete 删除第三方小程序配置
func (c *adThirdMiniProgramConfigController) Delete(ctx context.Context, req *ad.AdThirdMiniProgramConfigDeleteReq) (res *ad.AdThirdMiniProgramConfigDeleteRes, err error) {
	err = service.AdThirdMiniProgramConfig().Delete(ctx, req.Ids)
	return
}
