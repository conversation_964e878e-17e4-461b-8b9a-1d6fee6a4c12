// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:34:01
// 生成路径: internal/app/adx/model/entity/adx_publisher.go
// 生成人：cq
// desc:ADX公司信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxPublisher is the golang structure for table adx_publisher.
type AdxPublisher struct {
	gmeta.Meta    `orm:"table:adx_publisher, do:true"`
	Id            interface{} `orm:"id,primary" json:"id"`                // 公司ID
	PublisherName interface{} `orm:"publisher_name" json:"publisherName"` // 公司名称
}
