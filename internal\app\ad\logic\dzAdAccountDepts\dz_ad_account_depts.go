// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:31
// 生成路径: internal/app/ad/logic/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzAdAccountDepts(New())
}

func New() service.IDzAdAccountDepts {
	return &sDzAdAccountDepts{}
}

type sDzAdAccountDepts struct{}

func (s *sDzAdAccountDepts) List(ctx context.Context, req *model.DzAdAccountDeptsSearchReq) (listRes *model.DzAdAccountDeptsSearchRes, err error) {
	listRes = new(model.DzAdAccountDeptsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdAccountDepts.Ctx(ctx).WithAll()
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdAccountDepts.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if len(req.ChannelIds) > 0 {
			m = m.WhereIn(dao.DzAdAccountDepts.Columns().ChannelId, req.ChannelIds)
		}
		if req.DetpId != "" {
			m = m.Where(dao.DzAdAccountDepts.Columns().DetpId+" = ?", req.DetpId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "channel_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzAdAccountDeptsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdAccountDeptsListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzAdAccountDeptsListRes{
				ChannelId: v.ChannelId,
				DetpId:    v.DetpId,
			}
		}
	})
	return
}

func (s *sDzAdAccountDepts) GetExportData(ctx context.Context, req *model.DzAdAccountDeptsSearchReq) (listRes []*model.DzAdAccountDeptsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdAccountDepts.Ctx(ctx).WithAll()
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdAccountDepts.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.DetpId != "" {
			m = m.Where(dao.DzAdAccountDepts.Columns().DetpId+" = ?", req.DetpId)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "channel_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sDzAdAccountDepts) GetByChannelId(ctx context.Context, ChannelId int64) (res *model.DzAdAccountDeptsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdAccountDepts.Ctx(ctx).WithAll().Where(dao.DzAdAccountDepts.Columns().ChannelId, ChannelId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdAccountDepts) Add(ctx context.Context, req *model.DzAdAccountDeptsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountDepts.Ctx(ctx).Insert(do.DzAdAccountDepts{
			ChannelId: req.ChannelId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzAdAccountDepts) Edit(ctx context.Context, req *model.DzAdAccountDeptsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountDepts.Ctx(ctx).WherePri(req.ChannelId).Update(do.DzAdAccountDepts{})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzAdAccountDepts) Delete(ctx context.Context, channelIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountDepts.Ctx(ctx).Delete(dao.DzAdAccountDepts.Columns().DetpId+" in (?)", channelIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
