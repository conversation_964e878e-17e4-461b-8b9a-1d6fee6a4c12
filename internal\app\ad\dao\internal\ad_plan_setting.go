// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: internal/app/ad/dao/internal/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdPlanSettingDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdPlanSettingDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns AdPlanSettingColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdPlanSettingColumns defines and stores column names for table ad_plan_setting.
type AdPlanSettingColumns struct {
	Id                 string // ID
	RuleName           string // 计划规则名称
	UserId             string // 用户id创建计划人id
	MediaType          string // 媒体类型 1 巨量 2
	ObjectType         string // 对象类型 1 账户 2 项目 3 广告
	ScopeType          string // 范围 1.所有 2 指定范围
	ScopeObjectType    string // 指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告
	ScopeEntityIds     string // (和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空
	AdjustChannel      string // 调整渠道开关，0 关，1 开
	OnlyMessage        string // 1 仅发送通知 2 为需要执行计划
	RunFrequency       string // 运行频率，单位分钟
	EffectiveTimeType  string // 生效时间类型，1 长期，2 时间范围
	EffectiveStartTime string // 生效开始时间，仅当 effective_time_type 为 2 时有值
	EffectiveEndTime   string // 生效结束时间，仅当 effective_time_type 为 2 时有值
	ModeOfNotification string // 通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔
	PhoneNo            string // 手机号，多个以 | 分隔
	Email              string // 邮箱，以 | 分隔
	Status             string // 1启用 2 未启用
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
	DeletedAt          string // 删除时间
}

var adPlanSettingColumns = AdPlanSettingColumns{
	Id:                 "id",
	RuleName:           "rule_name",
	UserId:             "user_id",
	MediaType:          "media_type",
	ObjectType:         "object_type",
	ScopeType:          "scope_type",
	ScopeObjectType:    "scope_object_type",
	ScopeEntityIds:     "scope_entity_ids",
	AdjustChannel:      "adjust_channel",
	OnlyMessage:        "only_message",
	RunFrequency:       "run_frequency",
	EffectiveTimeType:  "effective_time_type",
	EffectiveStartTime: "effective_start_time",
	EffectiveEndTime:   "effective_end_time",
	ModeOfNotification: "mode_of_notification",
	PhoneNo:            "phone_no",
	Email:              "email",
	Status:             "status",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
	DeletedAt:          "deleted_at",
}

// NewAdPlanSettingDao creates and returns a new DAO object for table data access.
func NewAdPlanSettingDao() *AdPlanSettingDao {
	return &AdPlanSettingDao{
		group:   "default",
		table:   "ad_plan_setting",
		columns: adPlanSettingColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdPlanSettingDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdPlanSettingDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdPlanSettingDao) Columns() AdPlanSettingColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdPlanSettingDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdPlanSettingDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdPlanSettingDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
