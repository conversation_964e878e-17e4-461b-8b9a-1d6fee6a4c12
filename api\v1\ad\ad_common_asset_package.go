// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 13:50:44
// 生成路径: api/v1/ad/ad_common_asset_package.go
// 生成人：cq
// desc:通用资产-标题包相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdCommonAssetPackageSearchReq 分页请求参数
type AdCommonAssetPackageSearchReq struct {
	g.Meta `path:"/list" tags:"通用资产-标题包" method:"post" summary:"通用资产-标题包列表"`
	commonApi.Author
	model.AdCommonAssetPackageSearchReq
}

// AdCommonAssetPackageSearchRes 列表返回结果
type AdCommonAssetPackageSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetPackageSearchRes
}

// AdCommonAssetPackageExportReq 导出请求
type AdCommonAssetPackageExportReq struct {
	g.Meta `path:"/export" tags:"通用资产-标题包" method:"post" summary:"通用资产-标题包导出"`
	commonApi.Author
	model.AdCommonAssetPackageSearchReq
}

// AdCommonAssetPackageExportRes 导出响应
type AdCommonAssetPackageExportRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetPackageAddReq 添加操作请求参数
type AdCommonAssetPackageAddReq struct {
	g.Meta `path:"/add" tags:"通用资产-标题包" method:"post" summary:"通用资产-标题包添加"`
	commonApi.Author
	*model.AdCommonAssetPackageAddReq
}

// AdCommonAssetPackageAddRes 添加操作返回结果
type AdCommonAssetPackageAddRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetPackageEditReq 修改操作请求参数
type AdCommonAssetPackageEditReq struct {
	g.Meta `path:"/edit" tags:"通用资产-标题包" method:"put" summary:"通用资产-标题包修改"`
	commonApi.Author
	*model.AdCommonAssetPackageEditReq
}

// AdCommonAssetPackageEditRes 修改操作返回结果
type AdCommonAssetPackageEditRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetPackageGetReq 获取一条数据请求
type AdCommonAssetPackageGetReq struct {
	g.Meta `path:"/get" tags:"通用资产-标题包" method:"get" summary:"获取通用资产-标题包信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdCommonAssetPackageGetRes 获取一条数据结果
type AdCommonAssetPackageGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetPackageInfoRes
}

// AdCommonAssetPackageDeleteReq 删除数据请求
type AdCommonAssetPackageDeleteReq struct {
	g.Meta `path:"/delete" tags:"通用资产-标题包" method:"post" summary:"删除通用资产-标题包"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdCommonAssetPackageDeleteRes 删除数据返回
type AdCommonAssetPackageDeleteRes struct {
	commonApi.EmptyRes
}
