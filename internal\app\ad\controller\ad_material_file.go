// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: internal/app/ad/controller/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adMaterialFileController struct {
	systemController.BaseController
}

var AdMaterialFile = new(adMaterialFileController)

// List 列表
func (c *adMaterialFileController) List(ctx context.Context, req *ad.AdMaterialFileSearchReq) (res *ad.AdMaterialFileSearchRes, err error) {
	res = new(ad.AdMaterialFileSearchRes)
	res.AdMaterialFileSearchRes, err = service.AdMaterialFile().List(ctx, &req.AdMaterialFileSearchReq)
	return
}

func (c *adMaterialFileController) ChildList(ctx context.Context, req *ad.AdMaterialFileChildListReq) (res *ad.AdMaterialFileChildListRes, err error) {
	res = new(ad.AdMaterialFileChildListRes)
	res.AdMaterialFileChildListRes, err = service.AdMaterialFile().ChildList(ctx, &req.AdMaterialFileChildListReq)
	return
}

// Get 获取广告素材文件夹
func (c *adMaterialFileController) Get(ctx context.Context, req *ad.AdMaterialFileGetReq) (res *ad.AdMaterialFileGetRes, err error) {
	res = new(ad.AdMaterialFileGetRes)
	res.AdMaterialFileInfoRes, err = service.AdMaterialFile().GetByFileId(ctx, req.FileId)
	return
}

// Add 添加广告素材文件夹
func (c *adMaterialFileController) Add(ctx context.Context, req *ad.AdMaterialFileAddReq) (res *ad.AdMaterialFileAddRes, err error) {
	err = service.AdMaterialFile().Add(ctx, req.AdMaterialFileAddReq)
	return
}

// Edit 修改广告素材文件夹
func (c *adMaterialFileController) Edit(ctx context.Context, req *ad.AdMaterialFileEditReq) (res *ad.AdMaterialFileEditRes, err error) {
	err = service.AdMaterialFile().Edit(ctx, req.AdMaterialFileEditReq)
	return
}

// Delete 删除广告素材文件夹
func (c *adMaterialFileController) Delete(ctx context.Context, req *ad.AdMaterialFileDeleteReq) (res *ad.AdMaterialFileDeleteRes, err error) {
	err = service.AdMaterialFile().Delete(ctx, req.FileIds)
	return
}
