// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-15 16:29:14
// 生成路径: api/v1/oceanengine/ad_advertiser_account_transactions.go
// 生成人：gfast
// desc:广告账户的流水数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAdvertiserAccountTransactionsSearchReq 分页请求参数
type AdAdvertiserAccountTransactionsSearchReq struct {
	g.Meta `path:"/list" tags:"广告账户的流水数据" method:"get" summary:"广告账户的流水数据列表"`
	commonApi.Author
	model.AdAdvertiserAccountTransactionsSearchReq
}

// AdAdvertiserAccountTransactionsSearchRes 列表返回结果
type AdAdvertiserAccountTransactionsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountTransactionsSearchRes
}

// AdAdvertiserAccountTransactionsAddReq 添加操作请求参数
type AdAdvertiserAccountTransactionsAddReq struct {
	g.Meta `path:"/add" tags:"广告账户的流水数据" method:"post" summary:"广告账户的流水数据添加"`
	commonApi.Author
	*model.AdAdvertiserAccountTransactionsAddReq
}

// AdAdvertiserAccountTransactionsAddRes 添加操作返回结果
type AdAdvertiserAccountTransactionsAddRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountTransactionsEditReq 修改操作请求参数
type AdAdvertiserAccountTransactionsEditReq struct {
	g.Meta `path:"/edit" tags:"广告账户的流水数据" method:"put" summary:"广告账户的流水数据修改"`
	commonApi.Author
	*model.AdAdvertiserAccountTransactionsEditReq
}

// AdAdvertiserAccountTransactionsEditRes 修改操作返回结果
type AdAdvertiserAccountTransactionsEditRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountTransactionsGetReq 获取一条数据请求
type AdAdvertiserAccountTransactionsGetReq struct {
	g.Meta `path:"/get" tags:"广告账户的流水数据" method:"get" summary:"获取广告账户的流水数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAdvertiserAccountTransactionsGetRes 获取一条数据结果
type AdAdvertiserAccountTransactionsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountTransactionsInfoRes
}

// AdAdvertiserAccountTransactionsDeleteReq 删除数据请求
type AdAdvertiserAccountTransactionsDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告账户的流水数据" method:"delete" summary:"删除广告账户的流水数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAdvertiserAccountTransactionsDeleteRes 删除数据返回
type AdAdvertiserAccountTransactionsDeleteRes struct {
	commonApi.EmptyRes
}
