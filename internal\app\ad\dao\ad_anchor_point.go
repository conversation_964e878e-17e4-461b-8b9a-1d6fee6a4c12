// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/dao/ad_anchor_point.go
// 生成人：cyao
// desc:锚点表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adAnchorPointDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adAnchorPointDao struct {
	*internal.AdAnchorPointDao
}

var (
	// AdAnchorPoint is globally public accessible object for table tools_gen_table operations.
	AdAnchorPoint = adAnchorPointDao{
		internal.NewAdAnchorPointDao(),
	}
)

// Fill with you ideas below.
