// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-08-02 18:25:26
// 生成路径: internal/app/applet/dao/internal/s_applet_rechare_statistics.go
// 生成人：len
// desc:小程序充值统计
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SAppletRechareStatisticsD<PERSON> is the manager for logic model data accessing and custom defined data operations functions management.
type SAppletRechargeStatisticsDao struct {
	table   string                           // Table is the underlying table name of the DAO.
	group   string                           // Group is the database configuration group name of current DAO.
	columns SAppletRechargeStatisticsColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SAppletRechargeStatisticsColumns defines and stores column names for table s_applet_rechare_statistics.
type SAppletRechargeStatisticsColumns struct {
	Id                          string //
	AppId                       string // 小程序id
	AppName                     string //
	AppType                     string //
	NewUserAmount               string // 新用户充值金额
	TotalAmount                 string // 总充值金额
	WechatAndroidRechargeAmount string // 微信充值金额
	WechatIosRechargeAmount     string // 微信ios充值
	DyIosRechargeAmount         string // 逗小ios充值
	DyAndroidRechargeAmount     string // 逗小安卓充值
	CreateDate                  string //
	CreateTime                  string //
	TotalAdUp                   string // 总广告收入
	PitcherId                   string // 总广告收入
}

var sAppletRechargeStatisticsColumns = SAppletRechargeStatisticsColumns{
	Id:                          "id",
	AppId:                       "app_id",
	AppName:                     "app_name",
	AppType:                     "app_type",
	NewUserAmount:               "new_user_amount",
	TotalAmount:                 "total_amount",
	WechatAndroidRechargeAmount: "wechat_android_recharge_amount",
	WechatIosRechargeAmount:     "wechat_ios_recharge_amount",
	DyIosRechargeAmount:         "dy_ios_recharge_amount",
	DyAndroidRechargeAmount:     "dy_android_recharge_amount",
	CreateDate:                  "create_date",
	CreateTime:                  "create_time",
	TotalAdUp:                   "total_ad_up",
	PitcherId:                   "pitcher_id",
}

// NewSAppletRechargeStatisticsDao creates and returns a new DAO object for table data access.
func NewSAppletRechargeStatisticsDao() *SAppletRechargeStatisticsDao {
	return &SAppletRechargeStatisticsDao{
		group:   "default",
		table:   "s_applet_recharge_statistics",
		columns: sAppletRechargeStatisticsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SAppletRechargeStatisticsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SAppletRechargeStatisticsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SAppletRechargeStatisticsDao) Columns() SAppletRechargeStatisticsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SAppletRechargeStatisticsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SAppletRechargeStatisticsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SAppletRechargeStatisticsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
