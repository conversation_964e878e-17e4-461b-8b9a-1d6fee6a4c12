// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:33:37
// 生成路径: internal/app/adx/model/adx_playlet.go
// 生成人：cq
// desc:ADX短剧基本信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxPlayletInfoRes is the golang structure for table adx_playlet.
type AdxPlayletInfoRes struct {
	gmeta.Meta          `orm:"table:adx_playlet"`
	PlayletId           uint64   `orm:"playlet_id,primary" json:"playletId" dc:"短剧ID"`               // 短剧ID
	PlayletName         string   `orm:"playlet_name" json:"playletName" dc:"短剧名称"`                   // 短剧名称
	CoverOss            string   `orm:"cover_oss" json:"coverOss" dc:"封面OSS地址"`                      // 封面OSS地址
	Description         string   `orm:"description" json:"description" dc:"短剧描述"`                    // 短剧描述
	ReleaseStartDate    string   `orm:"release_start_date" json:"releaseStartDate" dc:"首次投放日期"`      // 首次投放日期
	TotalEpisode        uint     `orm:"total_episode" json:"totalEpisode" dc:"总集数"`                  // 总集数
	ContractorList      []string `orm:"contractor_list" json:"contractorList" dc:"承制方列表"`            // 承制方列表
	CopyrightHolderList []string `orm:"copyright_holder_list" json:"copyrightHolderList" dc:"版权方列表"` // 版权方列表
	RelatedPartyList    []string `orm:"related_party_list" json:"relatedPartyList" dc:"关联方列表"`       // 关联方列表
	Tags                []string `orm:"tags" json:"tags" dc:"短剧标签"`                                  // 短剧标签
}

type AdxPlayletDetialRes struct {
	gmeta.Meta          `orm:"table:adx_playlet"`
	PlayletId           uint64   `orm:"playlet_id,primary" json:"playletId" dc:"短剧ID"`               // 短剧ID
	PlayletName         string   `orm:"playlet_name" json:"playletName" dc:"短剧名称"`                   // 短剧名称
	CoverOss            string   `orm:"cover_oss" json:"coverOss" dc:"封面OSS地址"`                      // 封面OSS地址
	Description         string   `orm:"description" json:"description" dc:"短剧描述"`                    // 短剧描述
	ReleaseStartDate    string   `orm:"release_start_date" json:"releaseStartDate" dc:"首次投放日期"`      // 首次投放日期
	TotalEpisode        uint     `orm:"total_episode" json:"totalEpisode" dc:"总集数"`                  // 总集数
	ContractorList      []string `orm:"contractor_list" json:"contractorList" dc:"承制方列表"`            // 承制方列表
	CopyrightHolderList []string `orm:"copyright_holder_list" json:"copyrightHolderList" dc:"版权方列表"` // 版权方列表
	RelatedPartyList    []string `orm:"related_party_list" json:"relatedPartyList" dc:"关联方列表"`       // 关联方列表
	Tags                []string `orm:"tags" json:"tags" dc:"短剧标签"`                                  // 短剧标签
	// 投放天数
	Days uint ` json:"days" dc:"投放天数"`
	// 计划数量
	PlanNum uint ` json:"planNum" dc:"计划数量"`
	// 素材数量
	MaterialNum uint `  json:"materialNum" dc:"素材数量"`
	// 累计热力值
	HeatValue uint `  json:"heatValue" dc:"累计热力值"`
}

type AdxPlayletListRes struct {
	PlayletId           uint64   `json:"playletId" dc:"短剧ID"`
	PlayletName         string   `json:"playletName" dc:"短剧名称"`
	CoverOss            string   `json:"coverOss" dc:"封面OSS地址"`
	Description         string   `json:"description" dc:"短剧描述"`
	ReleaseStartDate    string   `json:"releaseStartDate" dc:"首次投放日期"`
	TotalEpisode        uint     `json:"totalEpisode" dc:"总集数"`
	ContractorList      []string `json:"contractorList" dc:"承制方列表"`
	CopyrightHolderList []string `json:"copyrightHolderList" dc:"版权方列表"`
	RelatedPartyList    []string `json:"relatedPartyList" dc:"关联方列表"`
	Tags                []string `json:"tags" dc:"短剧标签"`
}

// AdxPlayletSearchReq 分页请求参数
type AdxPlayletSearchReq struct {
	comModel.PageReq
	PlayletId           string   `p:"playletId" dc:"短剧ID"`                                    //短剧ID
	PlayletName         string   `p:"playletName" dc:"短剧名称"`                                  //短剧名称
	CoverOss            string   `p:"coverOss" dc:"封面OSS地址"`                                  //封面OSS地址
	Description         string   `p:"description" dc:"短剧描述"`                                  //短剧描述
	ReleaseStartDate    string   `p:"releaseStartDate" dc:"首次投放日期"`                           //首次投放日期
	TotalEpisode        string   `p:"totalEpisode" v:"totalEpisode@integer#总集数需为整数" dc:"总集数"` //总集数
	ContractorList      []string `p:"contractorList" dc:"承制方列表"`                              //承制方列表
	CopyrightHolderList []string `p:"copyrightHolderList" dc:"版权方列表"`                         //版权方列表
	RelatedPartyList    []string `p:"relatedPartyList" dc:"关联方列表"`                            //关联方列表
	Tags                []string `p:"tags" dc:"短剧标签"`                                         //短剧标签
}

// AdxPlayletSearchRes 列表返回结果
type AdxPlayletSearchRes struct {
	comModel.ListRes
	List []*AdxPlayletListRes `json:"list"`
}

// AdxPlayletAddReq 添加操作请求参数
type AdxPlayletAddReq struct {
	PlayletId           uint64   `p:"playletId" v:"required#主键ID不能为空" dc:"短剧ID"`
	PlayletName         string   `p:"playletName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	CoverOss            string   `p:"coverOss"  dc:"封面OSS地址"`
	Description         string   `p:"description"  dc:"短剧描述"`
	ReleaseStartDate    string   `p:"releaseStartDate"  dc:"首次投放日期"`
	TotalEpisode        uint     `p:"totalEpisode"  dc:"总集数"`
	ContractorList      []string `p:"contractorList" v:"required#承制方列表不能为空" dc:"承制方列表"`
	CopyrightHolderList []string `p:"copyrightHolderList" v:"required#版权方列表不能为空" dc:"版权方列表"`
	RelatedPartyList    []string `p:"relatedPartyList" v:"required#关联方列表不能为空" dc:"关联方列表"`
	Tags                []string `p:"tags" v:"required#短剧标签不能为空" dc:"短剧标签"`
}

// AdxPlayletEditReq 修改操作请求参数
type AdxPlayletEditReq struct {
	PlayletId           uint64   `p:"playletId" v:"required#主键ID不能为空" dc:"短剧ID"`
	PlayletName         string   `p:"playletName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	CoverOss            string   `p:"coverOss"  dc:"封面OSS地址"`
	Description         string   `p:"description"  dc:"短剧描述"`
	ReleaseStartDate    string   `p:"releaseStartDate"  dc:"首次投放日期"`
	TotalEpisode        uint     `p:"totalEpisode"  dc:"总集数"`
	ContractorList      []string `p:"contractorList" v:"required#承制方列表不能为空" dc:"承制方列表"`
	CopyrightHolderList []string `p:"copyrightHolderList" v:"required#版权方列表不能为空" dc:"版权方列表"`
	RelatedPartyList    []string `p:"relatedPartyList" v:"required#关联方列表不能为空" dc:"关联方列表"`
	Tags                []string `p:"tags" v:"required#短剧标签不能为空" dc:"短剧标签"`
}

type AdxPlayLetData struct {
	PlanNum      int64  `json:"planNum"`
	MinFirstSeen string `json:"minFirstSeen"`
	MaxLastSeen  string `json:"maxLastSeen"`
}
