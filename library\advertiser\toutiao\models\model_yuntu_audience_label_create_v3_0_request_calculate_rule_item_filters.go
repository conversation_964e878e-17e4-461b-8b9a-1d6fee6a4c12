/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30RequestCalculateRuleItemFilters 内容条件。
type YuntuAudienceLabelCreateV30RequestCalculateRuleItemFilters struct {
	// 内容类别列表。创建商品人群标签时必传，其他人群标签勿传。其中商品人群标签下场景为不限时，需传入 3、4、5 三个元素。含义详见枚举包。
	ContentTypeList []*YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList `json:"content_type_list,omitempty"`
	// 商品类目列表。字符串数组，每个元素为由英文逗号分隔拼接的、一级类目ID至四级类目ID字符串。
	EcomCategories []string                                                     `json:"ecom_categories,omitempty"`
	MatchType      YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType `json:"match_type"`
	// 商品价格最大值。区间为 [0.02, 9999999.00]。min_price < max_price。
	MaxPrice *float64 `json:"max_price,omitempty"`
	// 商品价格最小值。区间为 [0.01, 9999999.00)
	MinPrice      *float64                                                          `json:"min_price,omitempty"`
	OnlySelfBrand *YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand `json:"only_self_brand,omitempty"`
}
