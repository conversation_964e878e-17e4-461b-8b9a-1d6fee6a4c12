/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)


// StarDemandOmGetChallengeItemsDataV2ApiService  查询任务详情
type StarDemandOmGetChallengeV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2StarDemandOmGetChallengeGetRequest
}

func (r *StarDemandOmGetChallengeV2ApiService) SetCfg(cfg *conf.Configuration) *StarDemandOmGetChallengeV2ApiService {
	r.cfg = cfg
	return r
}

func (r *StarDemandOmGetChallengeV2ApiService) SetRequest(accountFundGetV3Request ApiOpenApi2StarDemandOmGetChallengeGetRequest) *StarDemandOmGetChallengeV2ApiService {
	r.Request = &accountFundGetV3Request
	return r
}

func (r *StarDemandOmGetChallengeV2ApiService) AccessToken(accessToken string) *StarDemandOmGetChallengeV2ApiService {
	r.token = accessToken
	return r
}

type ApiOpenApi2StarDemandOmGetChallengeGetRequest struct {
	//ctx             context.Context
	//ApiService      *StarDemandOmGetChallengeV2ApiService
	StarId          int64
	ChallengeTaskId int64
	DeveloperId     int64
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *StarDemandOmGetChallengeV2ApiService) Do() (data *models.StarDemandOmGetChallengeV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/star/demand/om_get_challenge/"
	localVarQueryParams := map[string]string{}

	if r.Request.StarId == 0 {
		return nil, errors.New("starId is required and must be specified")
	}
	if r.Request.ChallengeTaskId == 0 {
		return nil, errors.New("challengeTaskId is required and must be specified")
	}

	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "star_id", r.Request.StarId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "challenge_task_id", r.Request.ChallengeTaskId)
	if r.Request.DeveloperId > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "developer_id", r.Request.DeveloperId)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.StarDemandOmGetChallengeV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.StarDemandOmGetChallengeV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/star/demand/om_get_challenge/解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}

