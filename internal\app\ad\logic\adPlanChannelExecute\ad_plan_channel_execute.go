// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-27 11:19:17
// 生成路径: internal/app/ad/logic/ad_plan_channel_execute.go
// 生成人：cq
// desc:广告渠道执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdPlanChannelExecute(New())
}

func New() service.IAdPlanChannelExecute {
	return &sAdPlanChannelExecute{}
}

type sAdPlanChannelExecute struct{}

func (s *sAdPlanChannelExecute) List(ctx context.Context, req *model.AdPlanChannelExecuteSearchReq) (listRes *model.AdPlanChannelExecuteSearchRes, err error) {
	listRes = new(model.AdPlanChannelExecuteSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPlanChannelExecute.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().Id+" = ?", req.Id)
		}
		if req.PlanId != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().PlanId+" = ?", gconv.Int(req.PlanId))
		}
		if req.ChannelList != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().ChannelList+" = ?", req.ChannelList)
		}
		if req.TemplateId != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().TemplateId+" = ?", gconv.Int(req.TemplateId))
		}
		if req.LockNum != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().LockNum+" = ?", gconv.Int(req.LockNum))
		}
		if req.RewardId != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().RewardId+" = ?", gconv.Int(req.RewardId))
		}
		if req.AdSettingId != "" {
			m = m.Where(dao.AdPlanChannelExecute.Columns().AdSettingId+" = ?", gconv.Int(req.AdSettingId))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdPlanChannelExecute.Columns().CreatedAt+" >=? AND "+dao.AdPlanChannelExecute.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPlanChannelExecuteListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPlanChannelExecuteListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdPlanChannelExecuteListRes{
				Id:          v.Id,
				PlanId:      v.PlanId,
				ChannelList: v.ChannelList,
				TemplateId:  v.TemplateId,
				SinglePrice: v.SinglePrice,
				LockNum:     v.LockNum,
				RewardId:    v.RewardId,
				AdSettingId: v.AdSettingId,
				CreatedAt:   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdPlanChannelExecute) GetById(ctx context.Context, id int) (res *model.AdPlanChannelExecuteInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanChannelExecute.Ctx(ctx).WithAll().Where(dao.AdPlanChannelExecute.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanChannelExecute) GetByPlanId(ctx context.Context, planId int) (res *model.AdPlanChannelExecuteInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanChannelExecute.Ctx(ctx).WithAll().Where(dao.AdPlanChannelExecute.Columns().PlanId, planId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanChannelExecute) GetByPlanIds(ctx context.Context, planIds []int) (res []*model.AdPlanChannelExecuteInfoRes, err error) {
	res = make([]*model.AdPlanChannelExecuteInfoRes, 0)
	if planIds == nil || len(planIds) == 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanChannelExecute.Ctx(ctx).WithAll().WhereIn(dao.AdPlanChannelExecute.Columns().PlanId, planIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanChannelExecute) Add(ctx context.Context, req *model.AdPlanChannelExecuteAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanChannelExecute.Ctx(ctx).Insert(do.AdPlanChannelExecute{
			PlanId:      req.PlanId,
			ChannelList: req.ChannelList,
			TemplateId:  req.TemplateId,
			SinglePrice: req.SinglePrice,
			LockNum:     req.LockNum,
			RewardId:    req.RewardId,
			AdSettingId: req.AdSettingId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPlanChannelExecute) Edit(ctx context.Context, req *model.AdPlanChannelExecuteEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanChannelExecute.Ctx(ctx).WherePri(req.Id).Update(do.AdPlanChannelExecute{
			PlanId:      req.PlanId,
			ChannelList: req.ChannelList,
			TemplateId:  req.TemplateId,
			SinglePrice: req.SinglePrice,
			LockNum:     req.LockNum,
			RewardId:    req.RewardId,
			AdSettingId: req.AdSettingId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdPlanChannelExecute) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanChannelExecute.Ctx(ctx).Delete(dao.AdPlanChannelExecute.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// AdPlanExecute 调整渠道模板/单集价格/付费集数/回传配置
func (s *sAdPlanChannelExecute) AdPlanExecute(ctx context.Context, planId int) (operation string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		channelExecute, err1 := s.GetByPlanId(ctx, planId)
		liberr.ErrIsNil(ctx, err1, "获取渠道执行规则失败")
		if channelExecute == nil || len(channelExecute.ChannelList) == 0 {
			return
		}
		channelList := strings.Split(channelExecute.ChannelList, commonConsts.Delimiter)
		channelInfos, err2 := channelService.SChannel().GetChannelByChannelCodes(ctx, channelList)
		liberr.ErrIsNil(ctx, err2, "获取渠道信息失败")
		for _, channelInfo := range channelInfos {
			// 1. 调整渠道模板
			if channelExecute.TemplateId != 0 {
				err2 = channelService.SChannel().UpdateProductByTemplateId(ctx, channelInfo.ChannelCode, channelInfo.TemplateId, channelExecute.TemplateId)
				liberr.ErrIsNil(ctx, err2, "调整渠道模板执行失败")
				channelInfo.TemplateId = channelExecute.TemplateId
			}
			// 2. 调整单集价格
			if channelExecute.SinglePrice != 0 {
				channelInfo.BuyPriceGold = channelExecute.SinglePrice
			}
			// 3. 调整付费集数
			if channelExecute.LockNum != 0 {
				channelInfo.LockNum = channelExecute.LockNum
			}
			err2 = channelService.SChannel().UpdateChannel(ctx, &channelModel.SChannelEditReq{
				Id:           gconv.String(channelInfo.Id),
				BuyPriceGold: gconv.String(channelInfo.BuyPriceGold),
				LockNum:      gconv.String(channelInfo.LockNum),
				TemplateId:   channelInfo.TemplateId,
			})
			liberr.ErrIsNil(ctx, err2, "调整渠道信息执行失败")
		}
		var operations = make([]string, 0)
		// 4. 调整回传配置
		if channelExecute.AdSettingId != 0 || channelExecute.RewardId != 0 {
			err3 := channelService.AdSetting().BatchUpdateAdSetting(ctx, channelList, channelExecute.AdSettingId, channelExecute.RewardId)
			liberr.ErrIsNil(ctx, err3, "调整回传配置执行失败")
			operations = append(operations, "修改回传配置")
		}
		if channelExecute.TemplateId != 0 {
			operations = append(operations, "修改充值模板")
		}
		if channelExecute.SinglePrice != 0 {
			operations = append(operations, fmt.Sprintf("单集价格修改为%v金币", channelExecute.SinglePrice))
		}
		if channelExecute.LockNum != 0 {
			operations = append(operations, fmt.Sprintf("付费集数修改为%v集", channelExecute.LockNum))
		}
		operation = strings.Join(operations, ",")
	})
	return
}
