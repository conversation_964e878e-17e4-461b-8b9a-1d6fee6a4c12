// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: internal/app/ad/dao/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adLandingPageTempDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adLandingPageTempDao struct {
	*internal.AdLandingPageTempDao
}

var (
	// AdLandingPageTemp is globally public accessible object for table tools_gen_table operations.
	AdLandingPageTemp = adLandingPageTempDao{
		internal.NewAdLandingPageTempDao(),
	}
)

// Fill with you ideas below.
