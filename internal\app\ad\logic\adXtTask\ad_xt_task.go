// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: internal/app/ad/logic/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
	"time"
)

func init() {
	service.RegisterAdXtTask(New())
}

func New() service.IAdXtTask {
	return &sAdXtTask{}
}

type sAdXtTask struct{}

func (s *sAdXtTask) Pull(ctx context.Context) (count int, err error) {
	innerContext, cancel := context.WithTimeout(context.Background(), 200*time.Hour)
	defer cancel()
	channelRechargeStatKey := model.XTAdCoreDataTask + ":" + libUtils.GetNowDate()
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullDate ")
		return 0, err
	}
	defer mutex.UnlockContext(ctx)
	err = g.Try(innerContext, func(ctx context.Context) {
		// 根据aid 获取 星图的token
		//token, err := service.AdXtAccount().GetAccessTokenByAdvertiserId(ctx, aid)
		accountList, err := service.AdXtAccount().List(innerContext, &model.AdXtAccountSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: 999999,
			},
		})
		liberr.ErrIsNil(ctx, err, "获取列表数据失败")
		// 获取最新的token
		for _, item := range accountList.List {
			item.AccessToken, _ = service.AdXtAccount().GetAccessTokenByAdvertiserId(innerContext, item.AdvertiserId)
			if len(item.AccessToken) > 0 {
				summary, _, _ := GetTask(innerContext, item.AccessToken, item.AdvertiserId, toutiaoModels.Enum_16_StarDemandOmGetDemandListV2UniversalSettlementType, 0, 0)
				count += summary
				summary, _, _ = GetTask(innerContext, item.AccessToken, item.AdvertiserId, toutiaoModels.Enum_19_StarDemandOmGetDemandListV2UniversalSettlementType, 0, 0)
				count += summary
				summary, _, _ = GetTask(innerContext, item.AccessToken, item.AdvertiserId, toutiaoModels.Enum_33_StarDemandOmGetDemandListV2UniversalSettlementType, 0, 0)
				count += summary
			}
			g.Log().Info(ctx, fmt.Sprintf("-------------  执行GetTask结束: AdvertiserId:%v count:%v -------------------", item.AdvertiserId, count))
			service.AdXtTaskSettle().Pull(innerContext, item.AdvertiserId)
		}

		//xtAdService := new(model.XTAdKSService)
		//g.Cfg().MustGet(context.Background(), "advertiser.xt").Scan(xtAdService)
		//appConfig, _ := service.AdAppConfig().GetByAppId(ctx, strconv.FormatInt(xtAdService.AppId, 10))
		//if appConfig == nil {
		//	err = errors.New("应用不存在")
		//	liberr.ErrIsNil(ctx, err)
		//}

	})
	return
}
func GetTask(ctx context.Context, token, aid string, mType toutiaoModels.StarDemandOmGetDemandListV2UniversalSettlementType, createStartTime, createEndTime int64) (count int, taskIdList []string, err error) {
	pageNo, pageSize := 1, 100
	taskIdList = make([]string, 0)
	for {
		taskResponse := new(toutiaoModels.StarDemandOmGetDemandListV2Response)
		if createStartTime > 0 && createEndTime > 0 {
			taskResponse, err = advertiser.GetToutiaoApiClient().StarDemandOmGetDemandListV2ApiService.AccessToken(token).
				SetRequest(toutiaoModel.ApiOpenApi2StarDemandOmGetDemandListGetRequest{
					StarId:                  gconv.Int64(aid),
					PageNo:                  pageNo,
					PageSize:                pageSize,
					UniversalSettlementType: mType,
					CreateEndTime:           createEndTime,
					CreateStartTime:         createStartTime,
				}).Do()
		} else {
			taskResponse, err = advertiser.GetToutiaoApiClient().StarDemandOmGetDemandListV2ApiService.AccessToken(token).
				SetRequest(toutiaoModel.ApiOpenApi2StarDemandOmGetDemandListGetRequest{
					StarId:                  gconv.Int64(aid),
					PageNo:                  pageNo,
					PageSize:                pageSize,
					UniversalSettlementType: mType,
				}).Do()
		}
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("获取StarDemandOmGetDemandListV2ApiService失败: %v", err))
		count += len(taskResponse.Data.Demands)
		if taskResponse.Data == nil || len(taskResponse.Data.Demands) == 0 {
			break
		}
		if len(taskResponse.Data.Demands) < 100 {
			for _, demand := range taskResponse.Data.Demands {
				taskIdList = append(taskIdList, gconv.String(*demand.DemandId))

				taskInfoRes, _ := advertiser.GetToutiaoApiClient().StarDemandOmGetChallengeV2ApiService.AccessToken(token).SetRequest(
					toutiaoModel.ApiOpenApi2StarDemandOmGetChallengeGetRequest{
						StarId:          gconv.Int64(aid),
						ChallengeTaskId: *demand.DemandId,
					}).Do()
				if taskInfoRes.Code == 0 {
					var taskList = make([]*model.AdXtTaskInfoRes, 0)
					// 构建任务数据
					if taskInfoRes.Data.TaskInfo != nil {
						var attachments = ""
						// json 序列化  taskInfoRes.Data.TaskInfo.DemandInfo.Attachments
						attachmentsByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.DemandInfo.Attachments)
						if len(attachmentsByte) > 0 {
							attachments = string(attachmentsByte)
						}
						var sampleVideo = ""
						sampleVideoByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.ChallengeInfo.SampleVideo)
						if len(sampleVideoByte) > 0 {
							sampleVideo = string(sampleVideoByte)
						}
						var omTaskTag = ""
						omTaskTagByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.ChallengeInfo.OmTaskTag)
						if len(omTaskTagByte) > 0 {
							omTaskTag = string(omTaskTagByte)
						}
						var authorList = ""
						authorListByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.ChallengeInfo.AuthorList)
						if len(authorListByte) > 0 {
							authorList = string(authorListByte)
						}
						var md = &model.AdXtTaskInfoRes{
							AdvertiserId:            gconv.String(aid),
							TaskId:                  gconv.String(*demand.DemandId),
							TaskName:                taskInfoRes.Data.TaskInfo.DemandInfo.DemandName,
							UniversalSettlementType: int(mType),
							Attachments:             attachments,
							AuthorTaskName:          taskInfoRes.Data.TaskInfo.ChallengeInfo.AuthorTaskName,
							TaskIcon:                taskInfoRes.Data.TaskInfo.ChallengeInfo.TaskIcon,
							TaskHeadImage:           taskInfoRes.Data.TaskInfo.ChallengeInfo.TaskHeadImage,
							StartTime:               gtime.NewFromTimeStamp(taskInfoRes.Data.TaskInfo.ChallengeInfo.StartTime),
							EndTime:                 gtime.NewFromTimeStamp(taskInfoRes.Data.TaskInfo.ChallengeInfo.EndTime),
							SampleVideo:             sampleVideo,
							MicroAppId:              taskInfoRes.Data.TaskInfo.ChallengeInfo.MicroAppId,
							StartPage:               taskInfoRes.Data.TaskInfo.ChallengeInfo.StartPage,
							AnchorTitle:             taskInfoRes.Data.TaskInfo.ChallengeInfo.AnchorTitle,
							CommissionType:          gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionType),
							AuthorScope:             gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.AuthorScope),
							ProviderScope:           gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.ProviderScope),
							CommissionRate:          gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionRate),
							AdCommissionRate:        float64(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionInfo.AdCommissionRate),
							PayCommissionRate:       float64(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionInfo.PayCommissionRate),
							AccountDivideDay:        gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.AccountDivideDay),
							DemandDesc:              taskInfoRes.Data.TaskInfo.ChallengeInfo.DemandDesc,
							OmTaskStatus:            int(taskInfoRes.Data.TaskInfo.ChallengeInfo.OmTaskStatus),
							OmTaskTag:               omTaskTag,
							AuthorList:              authorList,
							CreatedAt:               gtime.Now(),
							UpdatedAt:               gtime.Now(),
						}
						taskList = append(taskList, md)
					}
					dao.AdXtTask.Ctx(ctx).Save(taskList)
				}
			}
			pageNo = 1
			break
		} else {
			for _, demand := range taskResponse.Data.Demands {
				taskIdList = append(taskIdList, gconv.String(*demand.DemandId))
				taskInfoRes, _ := advertiser.GetToutiaoApiClient().StarDemandOmGetChallengeV2ApiService.AccessToken(token).SetRequest(
					toutiaoModel.ApiOpenApi2StarDemandOmGetChallengeGetRequest{
						StarId:          gconv.Int64(aid),
						ChallengeTaskId: *demand.DemandId,
					}).Do()
				if taskInfoRes.Code == 0 {
					var taskList = make([]*model.AdXtTaskInfoRes, 0)
					// 构建任务数据
					if taskInfoRes.Data.TaskInfo != nil {
						var attachments = ""
						// json 序列化  taskInfoRes.Data.TaskInfo.DemandInfo.Attachments
						attachmentsByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.DemandInfo.Attachments)
						if len(attachmentsByte) > 0 {
							attachments = string(attachmentsByte)
						}
						var sampleVideo = ""
						sampleVideoByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.ChallengeInfo.SampleVideo)
						if len(sampleVideoByte) > 0 {
							sampleVideo = string(sampleVideoByte)
						}
						var omTaskTag = ""
						omTaskTagByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.ChallengeInfo.OmTaskTag)
						if len(omTaskTagByte) > 0 {
							omTaskTag = string(omTaskTagByte)
						}
						var authorList = ""
						authorListByte, _ := json.Marshal(taskInfoRes.Data.TaskInfo.ChallengeInfo.AuthorList)
						if len(authorListByte) > 0 {
							authorList = string(authorListByte)
						}
						var md = &model.AdXtTaskInfoRes{
							AdvertiserId:            gconv.String(aid),
							TaskId:                  gconv.String(*demand.DemandId),
							TaskName:                taskInfoRes.Data.TaskInfo.DemandInfo.DemandName,
							UniversalSettlementType: int(mType),
							Attachments:             attachments,
							AuthorTaskName:          taskInfoRes.Data.TaskInfo.ChallengeInfo.AuthorTaskName,
							TaskIcon:                taskInfoRes.Data.TaskInfo.ChallengeInfo.TaskIcon,
							TaskHeadImage:           taskInfoRes.Data.TaskInfo.ChallengeInfo.TaskHeadImage,
							StartTime:               gtime.NewFromTimeStamp(taskInfoRes.Data.TaskInfo.ChallengeInfo.StartTime),
							EndTime:                 gtime.NewFromTimeStamp(taskInfoRes.Data.TaskInfo.ChallengeInfo.EndTime),
							SampleVideo:             sampleVideo,
							MicroAppId:              taskInfoRes.Data.TaskInfo.ChallengeInfo.MicroAppId,
							StartPage:               taskInfoRes.Data.TaskInfo.ChallengeInfo.StartPage,
							AnchorTitle:             taskInfoRes.Data.TaskInfo.ChallengeInfo.AnchorTitle,
							CommissionType:          gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionType),
							AuthorScope:             gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.AuthorScope),
							ProviderScope:           gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.ProviderScope),
							CommissionRate:          gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionRate),
							AdCommissionRate:        float64(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionInfo.AdCommissionRate),
							PayCommissionRate:       float64(taskInfoRes.Data.TaskInfo.ChallengeInfo.CommissionInfo.PayCommissionRate),
							AccountDivideDay:        gconv.String(taskInfoRes.Data.TaskInfo.ChallengeInfo.AccountDivideDay),
							DemandDesc:              taskInfoRes.Data.TaskInfo.ChallengeInfo.DemandDesc,
							OmTaskStatus:            int(taskInfoRes.Data.TaskInfo.ChallengeInfo.OmTaskStatus),
							OmTaskTag:               omTaskTag,
							AuthorList:              authorList,
							CreatedAt:               gtime.Now(),
							UpdatedAt:               gtime.Now(),
						}
						taskList = append(taskList, md)
					}
					dao.AdXtTask.Ctx(ctx).Save(taskList)
				}
			}
			pageNo++
		}
	}
	return
}

// PullDate 拉一天的数据
func (s *sAdXtTask) PullDate(ctx context.Context, token, aid, statDate string) (count int, err error) {
	channelRechargeStatKey := model.XTAdCoreDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullDate ")
		return 0, err
	}
	defer mutex.UnlockContext(ctx)
	//innerCtx, cancel := context.WithCancel(context.Background())
	//defer cancel()
	err = g.Try(ctx, func(ctx context.Context) {
		startTimestamps, endTimestamps, _ := libUtils.GetDayTimestamps(statDate)
		startTimestamps, endTimestamps = startTimestamps/1000, endTimestamps/1000
		//GetTask(ctx, token, aid, req.UniversalSettlementType, startTimestamps, endTimestamps)
		summary, tId, _ := GetTask(ctx, token, aid, toutiaoModels.Enum_16_StarDemandOmGetDemandListV2UniversalSettlementType, startTimestamps, endTimestamps)
		count += summary
		if len(tId) > 0 {
			// 构建里面的数据
			err = service.AdXtTaskSettle().SaveAdXtTaskSettleByTaskId(ctx, token, aid, tId)
			if err != nil {
				g.Log().Error(ctx, "SaveAdXtTaskSettleByTaskId err", err)
			}
		}
		summary, tId, _ = GetTask(ctx, token, aid, toutiaoModels.Enum_19_StarDemandOmGetDemandListV2UniversalSettlementType, startTimestamps, endTimestamps)
		count += summary
		if len(tId) > 0 {
			// 构建里面的数据
			err = service.AdXtTaskSettle().SaveAdXtTaskSettleByTaskId(ctx, token, aid, tId)
			if err != nil {
				g.Log().Error(ctx, "SaveAdXtTaskSettleByTaskId err", err)
			}
		}
		summary, tId, _ = GetTask(ctx, token, aid, toutiaoModels.Enum_33_StarDemandOmGetDemandListV2UniversalSettlementType, startTimestamps, endTimestamps)
		count += summary
		if len(tId) > 0 {
			// 构建里面的数据
			err = service.AdXtTaskSettle().SaveAdXtTaskSettleByTaskId(ctx, token, aid, tId)
			if err != nil {
				g.Log().Error(ctx, "SaveAdXtTaskSettleByTaskId err", err)
			}
		}
	})
	return

}

// Async
func (s *sAdXtTask) Async(ctx context.Context, startTime string, endTime string) (count int, err error) {
	if startTime > endTime {
		return
	}
	innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()
	// 获取aid
	accountList, err := service.AdXtAccount().List(innerContext, &model.AdXtAccountSearchReq{
		PageReq: comModel.PageReq{
			PageNum:  1,
			PageSize: 999999,
		},
	})
	// 获取最新的token
	for _, item := range accountList.List {
		item.AccessToken, _ = service.AdXtAccount().GetAccessTokenByAdvertiserId(innerContext, item.AdvertiserId)
	}

	for {
		if startTime > endTime {
			break
		}
		for _, item := range accountList.List {
			summaryCount, err := s.PullDate(innerContext, item.AccessToken, item.AdvertiserId, startTime)
			if err != nil {
				if strings.Contains(err.Error(), "Too many requests") {
					time.Sleep(1000 * time.Millisecond)
					summaryCount, err = s.PullDate(innerContext, item.AccessToken, item.AdvertiserId, startTime)
					if err != nil {
						g.Log().Error(ctx, fmt.Sprintf("-------------PullDate Async 星图  err：%v --------------------", err))
					}
				}
			}
			count += summaryCount
		}
		g.Log().Info(ctx, fmt.Sprintf("刷星图数据成功, 统计日期: %s", startTime))
		startTime = libUtils.PlusDays(startTime, 1)
	}
	return
}
func (s *sAdXtTask) List(ctx context.Context, req *model.AdXtTaskSearchReq) (listRes *model.AdXtTaskSearchRes, err error) {
	listRes = new(model.AdXtTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtTask.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtTask.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtTask.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.UniversalSettlementType > 0 {
			m = m.Where(dao.AdXtTask.Columns().UniversalSettlementType+" = ?", req.UniversalSettlementType)
		}
		if req.TaskName != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskName+" like ?", "%"+req.TaskName+"%")
		}
		if req.AuthorTaskName != "" {
			m = m.Where(dao.AdXtTask.Columns().AuthorTaskName+" like ?", "%"+req.AuthorTaskName+"%")
		}
		if req.TaskIcon != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskIcon+" = ?", req.TaskIcon)
		}
		if req.TaskHeadImage != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskHeadImage+" = ?", req.TaskHeadImage)
		}
		if req.StartTime != "" {
			m = m.Where(dao.AdXtTask.Columns().StartTime+" >= ?", gconv.Time(req.StartTime))
		}
		if req.EndTime != "" {
			m = m.Where(dao.AdXtTask.Columns().EndTime+" <= ?", gconv.Time(req.EndTime))
		}
		if req.MicroAppId != "" {
			m = m.Where(dao.AdXtTask.Columns().MicroAppId+" = ?", gconv.Int(req.MicroAppId))
		}
		if req.StartPage != "" {
			m = m.Where(dao.AdXtTask.Columns().StartPage+" = ?", req.StartPage)
		}
		if req.AnchorTitle != "" {
			m = m.Where(dao.AdXtTask.Columns().AnchorTitle+" = ?", req.AnchorTitle)
		}
		if req.CommissionType != "" {
			m = m.Where(dao.AdXtTask.Columns().CommissionType+" = ?", req.CommissionType)
		}
		if req.AuthorScope != "" {
			m = m.Where(dao.AdXtTask.Columns().AuthorScope+" = ?", req.AuthorScope)
		}
		if req.ProviderScope != "" {
			m = m.Where(dao.AdXtTask.Columns().ProviderScope+" = ?", req.ProviderScope)
		}
		if req.CommissionRate != "" {
			m = m.Where(dao.AdXtTask.Columns().CommissionRate+" = ?", req.CommissionRate)
		}
		if req.AccountDivideDay != "" {
			m = m.Where(dao.AdXtTask.Columns().AccountDivideDay+" = ?", req.AccountDivideDay)
		}
		if req.DemandDesc != "" {
			m = m.Where(dao.AdXtTask.Columns().DemandDesc+" = ?", req.DemandDesc)
		}
		if req.OmTaskStatus != "" {
			m = m.Where(dao.AdXtTask.Columns().OmTaskStatus+" = ?", gconv.Int(req.OmTaskStatus))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdXtTask.Columns().CreatedAt+" >=? AND "+dao.AdXtTask.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.AdXtTaskListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdXtTaskListRes, len(res))
		aidList := make([]string, 0)
		mAppid := make([]string, 0)
		for k, v := range res {
			aidList = append(aidList, v.AdvertiserId)
			mAppid = append(mAppid, v.MicroAppId)
			listRes.List[k] = &model.AdXtTaskListRes{
				Id:                      v.Id,
				AdvertiserId:            v.AdvertiserId,
				TaskId:                  v.TaskId,
				TaskName:                v.TaskName,
				UniversalSettlementType: v.UniversalSettlementType,
				Attachments:             v.Attachments,
				AuthorTaskName:          v.AuthorTaskName,
				TaskIcon:                v.TaskIcon,
				TaskHeadImage:           v.TaskHeadImage,
				StartTime:               v.StartTime,
				EndTime:                 v.EndTime,
				SampleVideo:             v.SampleVideo,
				MicroAppId:              v.MicroAppId,
				StartPage:               v.StartPage,
				AnchorTitle:             v.AnchorTitle,
				CommissionType:          v.CommissionType,
				AuthorScope:             v.AuthorScope,
				ProviderScope:           v.ProviderScope,
				CommissionRate:          v.CommissionRate,
				AdCommissionRate:        v.AdCommissionRate,
				PayCommissionRate:       v.PayCommissionRate,
				AccountDivideDay:        v.AccountDivideDay,
				DemandDesc:              v.DemandDesc,
				OmTaskStatus:            v.OmTaskStatus,
				OmTaskTag:               v.OmTaskTag,
				AuthorList:              v.AuthorList,
				CreatedAt:               v.CreatedAt,
			}
		}
		aList, _ := service.AdXtAccount().GetByAIds(ctx, aidList)
		mList, _ := sysService.SPlatRules().GetByAppIds(ctx, mAppid)
		for _, item := range listRes.List {
			for _, adInfo := range aList {
				if item.AdvertiserId == adInfo.AdvertiserId {
					item.AdvertiserName = adInfo.AdvertiserNick
				}
			}
			for _, infoRes := range mList {
				if infoRes.AppId == item.MicroAppId {
					item.AppName = infoRes.AppName
				}
			}
		}
	})
	return
}

func (s *sAdXtTask) GetExportData(ctx context.Context, req *model.AdXtTaskSearchReq) (listRes []*model.AdXtTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtTask.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtTask.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtTask.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.TaskName != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskName+" like ?", "%"+req.TaskName+"%")
		}
		if req.AuthorTaskName != "" {
			m = m.Where(dao.AdXtTask.Columns().AuthorTaskName+" like ?", "%"+req.AuthorTaskName+"%")
		}
		if req.TaskIcon != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskIcon+" = ?", req.TaskIcon)
		}
		if req.TaskHeadImage != "" {
			m = m.Where(dao.AdXtTask.Columns().TaskHeadImage+" = ?", req.TaskHeadImage)
		}
		if req.StartTime != "" {
			m = m.Where(dao.AdXtTask.Columns().StartTime+" = ?", gconv.Time(req.StartTime))
		}
		if req.EndTime != "" {
			m = m.Where(dao.AdXtTask.Columns().EndTime+" = ?", gconv.Time(req.EndTime))
		}
		if req.MicroAppId != "" {
			m = m.Where(dao.AdXtTask.Columns().MicroAppId+" = ?", gconv.Int(req.MicroAppId))
		}
		if req.StartPage != "" {
			m = m.Where(dao.AdXtTask.Columns().StartPage+" = ?", req.StartPage)
		}
		if req.AnchorTitle != "" {
			m = m.Where(dao.AdXtTask.Columns().AnchorTitle+" = ?", req.AnchorTitle)
		}
		if req.CommissionType != "" {
			m = m.Where(dao.AdXtTask.Columns().CommissionType+" = ?", req.CommissionType)
		}
		if req.AuthorScope != "" {
			m = m.Where(dao.AdXtTask.Columns().AuthorScope+" = ?", req.AuthorScope)
		}
		if req.ProviderScope != "" {
			m = m.Where(dao.AdXtTask.Columns().ProviderScope+" = ?", req.ProviderScope)
		}
		if req.CommissionRate != "" {
			m = m.Where(dao.AdXtTask.Columns().CommissionRate+" = ?", req.CommissionRate)
		}
		if req.AccountDivideDay != "" {
			m = m.Where(dao.AdXtTask.Columns().AccountDivideDay+" = ?", req.AccountDivideDay)
		}
		if req.DemandDesc != "" {
			m = m.Where(dao.AdXtTask.Columns().DemandDesc+" = ?", req.DemandDesc)
		}
		if req.OmTaskStatus != "" {
			m = m.Where(dao.AdXtTask.Columns().OmTaskStatus+" = ?", gconv.Int(req.OmTaskStatus))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdXtTask.Columns().CreatedAt+" >=? AND "+dao.AdXtTask.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdXtTask) GetById(ctx context.Context, id int64) (res *model.AdXtTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtTask.Ctx(ctx).WithAll().Where(dao.AdXtTask.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtTask) GetByTaskIds(ctx context.Context, ids []string) (res []*model.AdXtTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtTask.Ctx(ctx).WithAll().WhereIn(dao.AdXtTask.Columns().TaskId, ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtTask) Add(ctx context.Context, req *model.AdXtTaskAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTask.Ctx(ctx).Insert(do.AdXtTask{
			AdvertiserId:      req.AdvertiserId,
			TaskId:            req.TaskId,
			TaskName:          req.TaskName,
			Attachments:       req.Attachments,
			AuthorTaskName:    req.AuthorTaskName,
			TaskIcon:          req.TaskIcon,
			TaskHeadImage:     req.TaskHeadImage,
			StartTime:         req.StartTime,
			EndTime:           req.EndTime,
			SampleVideo:       req.SampleVideo,
			MicroAppId:        req.MicroAppId,
			StartPage:         req.StartPage,
			AnchorTitle:       req.AnchorTitle,
			CommissionType:    req.CommissionType,
			AuthorScope:       req.AuthorScope,
			ProviderScope:     req.ProviderScope,
			CommissionRate:    req.CommissionRate,
			AdCommissionRate:  req.AdCommissionRate,
			PayCommissionRate: req.PayCommissionRate,
			AccountDivideDay:  req.AccountDivideDay,
			DemandDesc:        req.DemandDesc,
			OmTaskStatus:      req.OmTaskStatus,
			OmTaskTag:         req.OmTaskTag,
			AuthorList:        req.AuthorList,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdXtTask) Edit(ctx context.Context, req *model.AdXtTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTask.Ctx(ctx).WherePri(req.Id).Update(do.AdXtTask{
			AdvertiserId:      req.AdvertiserId,
			TaskId:            req.TaskId,
			TaskName:          req.TaskName,
			Attachments:       req.Attachments,
			AuthorTaskName:    req.AuthorTaskName,
			TaskIcon:          req.TaskIcon,
			TaskHeadImage:     req.TaskHeadImage,
			StartTime:         req.StartTime,
			EndTime:           req.EndTime,
			SampleVideo:       req.SampleVideo,
			MicroAppId:        req.MicroAppId,
			StartPage:         req.StartPage,
			AnchorTitle:       req.AnchorTitle,
			CommissionType:    req.CommissionType,
			AuthorScope:       req.AuthorScope,
			ProviderScope:     req.ProviderScope,
			CommissionRate:    req.CommissionRate,
			AdCommissionRate:  req.AdCommissionRate,
			PayCommissionRate: req.PayCommissionRate,
			AccountDivideDay:  req.AccountDivideDay,
			DemandDesc:        req.DemandDesc,
			OmTaskStatus:      req.OmTaskStatus,
			OmTaskTag:         req.OmTaskTag,
			AuthorList:        req.AuthorList,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdXtTask) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTask.Ctx(ctx).Delete(dao.AdXtTask.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
