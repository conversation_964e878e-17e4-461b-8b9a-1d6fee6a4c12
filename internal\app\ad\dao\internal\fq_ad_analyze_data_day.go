// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: internal/app/ad/dao/internal/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdAnalyzeDataDayDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdAnalyzeDataDayDao struct {
	table   string                    // Table is the underlying table name of the DAO.
	group   string                    // Group is the database configuration group name of current DAO.
	columns FqAdAnalyzeDataDayColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdAnalyzeDataDayColumns defines and stores column names for table fq_ad_analyze_data_day.
type FqAdAnalyzeDataDayColumns struct {
	Id             string // id
	DistributorId  string // 渠道ID
	PromotionId    string // 推广链id
	StatDate       string // 统计日期
	AddDesktopNum  string // 新增桌面用户数
	RechargeNum    string // 充值用户数
	UserNum        string // 用户总数
	RoiDetail      string // ROI 明细，JSON 格式存储
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
	RechargeAmount string
}

var fqAdAnalyzeDataDayColumns = FqAdAnalyzeDataDayColumns{
	Id:             "id",
	DistributorId:  "distributor_id",
	PromotionId:    "promotion_id",
	StatDate:       "stat_date",
	AddDesktopNum:  "add_desktop_num",
	RechargeNum:    "recharge_num",
	UserNum:        "user_num",
	RoiDetail:      "roi_detail",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
	RechargeAmount: "recharge_amount",
}

// NewFqAdAnalyzeDataDayDao creates and returns a new DAO object for table data access.
func NewFqAdAnalyzeDataDayDao() *FqAdAnalyzeDataDayDao {
	return &FqAdAnalyzeDataDayDao{
		group:   "default",
		table:   "fq_ad_analyze_data_day",
		columns: fqAdAnalyzeDataDayColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdAnalyzeDataDayDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdAnalyzeDataDayDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdAnalyzeDataDayDao) Columns() FqAdAnalyzeDataDayColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdAnalyzeDataDayDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdAnalyzeDataDayDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdAnalyzeDataDayDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
