// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: internal/app/ad/controller/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type mpAdEventController struct {
	systemController.BaseController
}

var MpAdEvent = new(mpAdEventController)

// List 列表
func (c *mpAdEventController) List(ctx context.Context, req *ad.MpAdEventSearchReq) (res *ad.MpAdEventSearchRes, err error) {
	res = new(ad.MpAdEventSearchRes)
	res.MpAdEventSearchRes, err = service.MpAdEvent().List(ctx, &req.MpAdEventSearchReq)
	return
}

// Get 获取dy小程序广告事件记录
func (c *mpAdEventController) Get(ctx context.Context, req *ad.MpAdEventGetReq) (res *ad.MpAdEventGetRes, err error) {
	res = new(ad.MpAdEventGetRes)
	res.MpAdEventInfoRes, err = service.MpAdEvent().GetById(ctx, req.Id)
	return
}

// MpAdEventPullData
func (c *mpAdEventController) PullData(ctx context.Context, req *ad.MpAdEventPullDataReq) (res *ad.MpAdEventPullDataRes, err error) {
	// Pull Data
	innerContext, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = service.MpAdEvent().PullData(innerContext, req.MpAdEventPullDataReq)
	return
}

// Add 添加dy小程序广告事件记录
func (c *mpAdEventController) Add(ctx context.Context, req *ad.MpAdEventAddReq) (res *ad.MpAdEventAddRes, err error) {
	err = service.MpAdEvent().Add(ctx, req.MpAdEventAddReq)
	return
}

// Edit 修改dy小程序广告事件记录
func (c *mpAdEventController) Edit(ctx context.Context, req *ad.MpAdEventEditReq) (res *ad.MpAdEventEditRes, err error) {
	err = service.MpAdEvent().Edit(ctx, req.MpAdEventEditReq)
	return
}

// Delete 删除dy小程序广告事件记录
func (c *mpAdEventController) Delete(ctx context.Context, req *ad.MpAdEventDeleteReq) (res *ad.MpAdEventDeleteRes, err error) {
	err = service.MpAdEvent().Delete(ctx, req.Ids)
	return
}
