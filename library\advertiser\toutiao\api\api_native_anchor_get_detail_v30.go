/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// NativeAnchorGetDetailV30ApiService NativeAnchorGetDetailV30Api service
type NativeAnchorGetDetailV30ApiService struct {
	ctx          context.Context
	cfg          *conf.Configuration
	token        string
	AnchorIds    *[]string
	AdvertiserId *int64
	AnchorType   *models.NativeAnchorGetDetailV30AnchorType
}

func (r *NativeAnchorGetDetailV30ApiService) SetCfg(cfg *conf.Configuration) *NativeAnchorGetDetailV30ApiService {
	r.cfg = cfg
	return r
}

func (r *NativeAnchorGetDetailV30ApiService) SetReq(anchorIds []string, advertiserId int64, anchorType *models.NativeAnchorGetDetailV30AnchorType) *NativeAnchorGetDetailV30ApiService {
	*r.AnchorIds = anchorIds
	*r.AdvertiserId = advertiserId
	r.AnchorType = anchorType
	return r
}

func (r *NativeAnchorGetDetailV30ApiService) AccessToken(accessToken string) *NativeAnchorGetDetailV30ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *NativeAnchorGetDetailV30ApiService) Do() (data *models.NativeAnchorGetDetailV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/native_anchor/get/detail/"
	localVarQueryParams := map[string]string{}

	if r.AnchorIds == nil {
		return nil, errors.New("anchorIds is required and must be specified")
	}
	if len(*r.AnchorIds) < 1 {
		return nil, errors.New("anchorIds must have at least 1 elements")
	}
	if len(*r.AnchorIds) > 20 {
		return nil, errors.New("anchorIds must have less than 20 elements")
	}
	if r.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.AnchorType == nil {
		return nil, errors.New("anchorType is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "anchor_ids", r.AnchorIds)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "anchor_type", r.AnchorType)

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.NativeAnchorGetDetailV30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.NativeAnchorGetDetailV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/native_anchor/get/detail/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}

//type ApiOpenApiV30NativeAnchorGetDetailGetRequest struct {
//	ctx          context.Context
//	ApiService   *NativeAnchorGetDetailV30ApiService
//	anchorIds    *[]string
//	advertiserId *int64
//	anchorType   *NativeAnchorGetDetailV30AnchorType
//}

// Execute executes the request
//
//	@return NativeAnchorGetDetailV30Response
//func (a *NativeAnchorGetDetailV30ApiService) getExecute(r *ApiOpenApiV30NativeAnchorGetDetailGetRequest) (*NativeAnchorGetDetailV30Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodGet
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *NativeAnchorGetDetailV30Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/v3.0/native_anchor/get/detail/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//	if r.anchorIds == nil {
//		return localVarReturnValue, nil, ReportError("anchorIds is required and must be specified")
//	}
//	if len(*r.anchorIds) < 1 {
//		return localVarReturnValue, nil, ReportError("anchorIds must have at least 1 elements")
//	}
//	if len(*r.anchorIds) > 20 {
//		return localVarReturnValue, nil, ReportError("anchorIds must have less than 20 elements")
//	}
//	if r.advertiserId == nil {
//		return localVarReturnValue, nil, ReportError("advertiserId is required and must be specified")
//	}
//	if r.anchorType == nil {
//		return localVarReturnValue, nil, ReportError("anchorType is required and must be specified")
//	}
//
//	parameterAddToHeaderOrQuery(localVarQueryParams, "anchor_ids", r.anchorIds)
//	parameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
//	parameterAddToHeaderOrQuery(localVarQueryParams, "anchor_type", r.anchorType)
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
