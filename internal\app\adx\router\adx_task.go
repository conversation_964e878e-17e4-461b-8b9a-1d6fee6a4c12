// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: internal/app/adx/router/adx_task.go
// 生成人：cyao
// desc:ADX任务主表
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/adx/controller"
)

func (router *Router) BindAdxTaskController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adxTask", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdxTask,
		)
	})
}
