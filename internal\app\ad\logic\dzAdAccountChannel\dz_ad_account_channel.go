// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: internal/app/ad/logic/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	dzApi "github.com/tiger1103/gfast/v3/library/advertiser/dz/api"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzAdAccountChannel(New())
}

func New() service.IDzAdAccountChannel {
	return &sDzAdAccountChannel{}
}

type sDzAdAccountChannel struct{}

func (s *sDzAdAccountChannel) List(ctx context.Context, req *model.DzAdAccountChannelSearchReq) (listRes *model.DzAdAccountChannelSearchRes, err error) {
	listRes = new(model.DzAdAccountChannelSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.DzAdAccountChannel.Ctx(ctx).WithAll().As("a")
		if !admin {
			m = m.LeftJoin(dao.DzAdAccountDepts.Table(), "d", " a.channel_id = d.channel_id").
				LeftJoin(dao.DzAdAccountUsers.Table(), "u", " a.channel_id = u.channel_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if req.ChannelId != "" {
			m = m.Where("a."+dao.DzAdAccountChannel.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.AccountId != "" {
			m = m.Where("a."+dao.DzAdAccountChannel.Columns().AccountId+" = ?", gconv.Int64(req.AccountId))
		}
		if req.NickName != "" {
			m = m.Where("a."+dao.DzAdAccountChannel.Columns().NickName+" like ?", "%"+req.NickName+"%")
		}
		if req.UserName != "" {
			m = m.Where("a."+dao.DzAdAccountChannel.Columns().UserName+" like ?", "%"+req.UserName+"%")
		}
		if req.JumpType != "" {
			m = m.Where("a."+dao.DzAdAccountChannel.Columns().JumpType+" = ?", gconv.Int(req.JumpType))
		}
		if len(req.DateRange) != 0 {
			m = m.Where("a."+dao.DzAdAccountChannel.Columns().CreatedAt+" >=? AND "+"a."+dao.DzAdAccountChannel.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "channel_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzAdAccountChannelListRes
		err = m.Fields("DISTINCT a.*").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdAccountChannelListRes, len(res))
		cIds := make([]int64, 0)
		appIds := make([]string, 0)
		for k, v := range res {
			cIds = append(cIds, v.ChannelId)
			if len(v.OfAppId) > 0 {
				appIds = append(appIds, v.OfAppId)
			}
			listRes.List[k] = &model.DzAdAccountChannelListRes{
				ChannelId: v.ChannelId,
				AccountId: v.AccountId,
				NickName:  v.NickName,
				UserName:  v.UserName,
				JumpType:  v.JumpType,
				CreatedAt: v.CreatedAt,
				OfAppId:   v.OfAppId,
				AppName:   v.AppName,
			}
		}
		uList, _ := service.DzAdAccountUsers().List(ctx, &model.DzAdAccountUsersSearchReq{
			ChannelIds: cIds,
			PageReq: comModel.PageReq{
				PageNum:  req.PageNum,
				PageSize: consts.MaxPageSize,
			},
		})
		deptList, _ := service.DzAdAccountDepts().List(ctx, &model.DzAdAccountDeptsSearchReq{
			ChannelIds: cIds,
			PageReq: comModel.PageReq{
				PageNum:  req.PageNum,
				PageSize: consts.MaxPageSize,
			},
		})

		appList, _ := service.AdThirdMiniProgramConfig().GetByAppIds(ctx, appIds)

		for _, accountListRes := range listRes.List {
			if uList != nil && len(uList.List) > 0 {
				for _, usersListRes := range uList.List {
					if usersListRes.ChannelId == accountListRes.ChannelId {
						if accountListRes.UserList == nil {
							accountListRes.UserList = make([]int64, 0)
						}
						accountListRes.UserList = append(accountListRes.UserList, gconv.Int64(usersListRes.SpecifyUserId))
					}
				}
			}
			if deptList != nil && len(deptList.List) > 0 {
				for _, usersListRes := range deptList.List {
					if usersListRes.ChannelId == accountListRes.ChannelId {
						if accountListRes.DeptList == nil {
							accountListRes.DeptList = make([]int64, 0)
						}
						accountListRes.DeptList = append(accountListRes.DeptList, gconv.Int64(usersListRes.DetpId))
					}
				}
			}
			if appList != nil && len(appList) > 0 {
				for _, appInfo := range appList {
					if appInfo.AppId == accountListRes.OfAppId {
						accountListRes.OfAppId = appInfo.AppId
						accountListRes.AppName = appInfo.AppName
						break
					}
				}
			}
		}
	})
	return
}

// SyncDzAdAccountChannel 同步点众渠道
func (s *sDzAdAccountChannel) SyncDzAdAccountChannel(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 100
		for {
			accounts, _ := service.DzAdAccount().List(ctx, &model.DzAdAccountSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})
			if len(accounts.List) == 0 {
				break
			}
			for _, adAccount := range accounts.List {
				channelInfoListRes, err1 := dzApi.GetAdDZIClient().ChannelInfoListService.
					SetToken(adAccount.Token).
					SetReq(dzApi.ChannelInfoListRequest{
						ClientId: adAccount.AccountId,
					}).Do()
				liberr.ErrIsNil(ctx, err1)
				if channelInfoListRes.Data != nil && channelInfoListRes.Data.ChannelInfoList != nil && len(channelInfoListRes.Data.ChannelInfoList) > 0 {
					var channelInfoList []*model.DzAdAccountChannelEditReq
					for _, channelInfo := range channelInfoListRes.Data.ChannelInfoList {
						channelInfoList = append(channelInfoList, &model.DzAdAccountChannelEditReq{
							ChannelId: gconv.Int64(channelInfo.ChannelId),
							AccountId: gconv.Int64(adAccount.AccountId),
							NickName:  channelInfo.NickName,
							UserName:  channelInfo.UserName,
							JumpType:  gconv.Int(channelInfo.JumpType),
							OfAppId:   channelInfo.OfAppid,
						})

					}
					err = service.DzAdAccountChannel().BatchSave(ctx, channelInfoList)
				}
			}
			pageNo++
		}

	})
	return
}

// AddDZAuth
func (s *sDzAdAccountChannel) AddDZAuth(ctx context.Context, req *model.DZAdAddAuthReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.ChannelIds) > 1 {
			for _, channelId := range req.ChannelIds {

				//dao.FqAdAccountDepts.Ctx(ctx).Delete(dao.FqAdAccountDepts.Columns().DistributorId, req.DistributorId)
				//dao.FqAdAccountUsers.Ctx(ctx).Delete(dao.FqAdAccountUsers.Columns().DistributorId, req.DistributorId)
				if len(req.DeptIds) > 0 {
					list := make([]do.DzAdAccountDepts, 0)
					for _, detpId := range req.DeptIds {
						list = append(list, do.DzAdAccountDepts{
							ChannelId: channelId,
							DetpId:    detpId,
						})
					}
					dao.DzAdAccountDepts.Ctx(ctx).Save(list)
				}
				if len(req.SpecifyUserIds) > 0 {
					list := make([]do.DzAdAccountUsers, 0)
					for _, id := range req.SpecifyUserIds {
						list = append(list, do.DzAdAccountUsers{
							ChannelId:     channelId,
							SpecifyUserId: id,
						})
					}
					dao.DzAdAccountUsers.Ctx(ctx).Save(list)
				}
			}
		} else {
			if len(req.ChannelIds) == 1 {
				req.ChannelId = req.ChannelIds[0]
			}
			dao.DzAdAccountDepts.Ctx(ctx).Delete(dao.DzAdAccountDepts.Columns().ChannelId, req.ChannelId)
			dao.DzAdAccountUsers.Ctx(ctx).Delete(dao.DzAdAccountUsers.Columns().ChannelId, req.ChannelId)
			if len(req.DeptIds) > 0 {
				list := make([]do.DzAdAccountDepts, 0)
				for _, id := range req.DeptIds {
					list = append(list, do.DzAdAccountDepts{
						ChannelId: req.ChannelId,
						DetpId:    id,
					})
				}
				dao.DzAdAccountDepts.Ctx(ctx).Save(list)
			}
			if len(req.SpecifyUserIds) > 0 {
				list := make([]do.DzAdAccountUsers, 0)
				for _, id := range req.SpecifyUserIds {
					list = append(list, do.DzAdAccountUsers{
						ChannelId:     req.ChannelId,
						SpecifyUserId: id,
					})
				}
				dao.DzAdAccountUsers.Ctx(ctx).Save(list)
			}
		}
	})
	return
}

func (s *sDzAdAccountChannel) GetExportData(ctx context.Context, req *model.DzAdAccountChannelSearchReq) (listRes []*model.DzAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdAccountChannel.Ctx(ctx).WithAll()
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdAccountChannel.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.AccountId != "" {
			m = m.Where(dao.DzAdAccountChannel.Columns().AccountId+" = ?", gconv.Int64(req.AccountId))
		}
		if req.NickName != "" {
			m = m.Where(dao.DzAdAccountChannel.Columns().NickName+" like ?", "%"+req.NickName+"%")
		}
		if req.UserName != "" {
			m = m.Where(dao.DzAdAccountChannel.Columns().UserName+" like ?", "%"+req.UserName+"%")
		}
		if req.JumpType != "" {
			m = m.Where(dao.DzAdAccountChannel.Columns().JumpType+" = ?", gconv.Int(req.JumpType))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.DzAdAccountChannel.Columns().CreatedAt+" >=? AND "+dao.DzAdAccountChannel.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "channel_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sDzAdAccountChannel) GetByChannelId(ctx context.Context, channelId int64) (res *model.DzAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdAccountChannel.Ctx(ctx).WithAll().Where(dao.DzAdAccountChannel.Columns().ChannelId, channelId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdAccountChannel) GetByChannelIds(ctx context.Context, channelIds []int64) (res []*model.DzAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.DzAdAccountChannelInfoRes, 0)
		if len(channelIds) == 0 {
			return
		}
		err = dao.DzAdAccountChannel.Ctx(ctx).WithAll().WhereIn(dao.DzAdAccountChannel.Columns().ChannelId, channelIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetByAccountIds
func (s *sDzAdAccountChannel) GetByAccountIds(ctx context.Context, accountIds []int64) (res []*model.DzAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.DzAdAccountChannelInfoRes, 0)
		if len(accountIds) == 0 {
			return
		}
		err = dao.DzAdAccountChannel.Ctx(ctx).WithAll().WhereIn(dao.DzAdAccountChannel.Columns().AccountId, accountIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdAccountChannel) Add(ctx context.Context, req *model.DzAdAccountChannelAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountChannel.Ctx(ctx).Insert(do.DzAdAccountChannel{
			ChannelId: req.ChannelId,
			AccountId: req.AccountId,
			NickName:  req.NickName,
			UserName:  req.UserName,
			JumpType:  req.JumpType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// 批量给渠道赋值AppId
func (s *sDzAdAccountChannel) SetAppId(ctx context.Context, req *model.DzAdAccountChannelSetAppIdReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, id := range req.ChannelIds {
			_, err = dao.DzAdAccountChannel.Ctx(ctx).Where(dao.DzAdAccountChannel.Columns().ChannelId, id).Update(do.DzAdAccountChannel{
				OfAppId: req.AppId,
			})
			liberr.ErrIsNil(ctx, err, "添加失败")
		}
	})
	return
}

// 批量更新
func (s *sDzAdAccountChannel) BatchSave(ctx context.Context, req []*model.DzAdAccountChannelEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.DzAdAccountChannel, 0)
		for _, v := range req {
			if len(v.OfAppId) > 0 {
				data = append(data, &do.DzAdAccountChannel{
					ChannelId: v.ChannelId,
					AccountId: v.AccountId,
					NickName:  v.NickName,
					UserName:  v.UserName,
					JumpType:  v.JumpType,
					OfAppId:   v.OfAppId,
				})
			} else {
				//先查询
				res, _ := s.GetByChannelId(ctx, v.ChannelId)
				if res != nil && res.ChannelId > 0 {
					_, err = dao.DzAdAccountChannel.Ctx(ctx).WherePri(v.ChannelId).Update(do.DzAdAccountChannel{
						AccountId: v.AccountId,
						NickName:  v.NickName,
						UserName:  v.UserName,
						JumpType:  v.JumpType,
					})
				} else {
					_, err = dao.DzAdAccountChannel.Ctx(ctx).Insert(do.DzAdAccountChannel{
						ChannelId: v.ChannelId,
						AccountId: v.AccountId,
						NickName:  v.NickName,
						UserName:  v.UserName,
						JumpType:  v.JumpType,
					})
				}
			}
		}

		_, err = dao.DzAdAccountChannel.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "批量更新失败")
	})
	return
}

func (s *sDzAdAccountChannel) Edit(ctx context.Context, req *model.DzAdAccountChannelEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountChannel.Ctx(ctx).WherePri(req.ChannelId).Update(do.DzAdAccountChannel{
			AccountId: req.AccountId,
			NickName:  req.NickName,
			UserName:  req.UserName,
			JumpType:  req.JumpType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzAdAccountChannel) Delete(ctx context.Context, channelIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountChannel.Ctx(ctx).Delete(dao.DzAdAccountChannel.Columns().ChannelId+" in (?)", channelIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetChannelList 获取渠道对应的密钥
func (s *sDzAdAccountChannel) GetChannelList(ctx context.Context, req *model.DzAdAccountChannelSearchReq) (listRes []*model.DzAdAccountChannelRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdAccountChannel.Ctx(ctx).WithAll().As("c").
			LeftJoin("dz_ad_account a", "c.account_id = a.account_id").
			Fields("c.channel_id as channelId").
			Fields("c.account_id as accountId").
			Fields("a.token as token")
		if req.AccountId != "" {
			m = m.Where("c.account_id = ?", req.AccountId)
		}
		err = m.Page(req.PageNum, req.PageSize).
			Order("c.channel_id asc").Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}
