// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: api/v1/ad/ad_material_album.go
// 生成人：cyao
// desc:广告素材专辑相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdMaterialAlbumSearchReq 分页请求参数
type AdMaterialAlbumSearchReq struct {
	g.Meta `path:"/list" tags:"广告素材专辑" method:"get" summary:"广告素材专辑列表"`
	commonApi.Author
	model.AdMaterialAlbumSearchReq
}

// AdMaterialAlbumSearchRes 列表返回结果
type AdMaterialAlbumSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialAlbumSearchRes
}

// SpecialProductListReq 文件夹视图列表
type SpecialProductListReq struct {
	g.Meta `path:"/specialProductList" tags:"素材主表" method:"get" summary:"文件夹视图列表"`
	commonApi.Author
	model.SpecialProductListReq
}

type SpecialProductListRes struct {
	g.Meta                       `mime:"application/json"`
	*model.SpecialProductListRes `json:"data"`
}

// AdMaterialAlbumAddReq 添加操作请求参数
type AdMaterialAlbumAddReq struct {
	g.Meta `path:"/add" tags:"广告素材专辑" method:"post" summary:"广告素材专辑添加"`
	commonApi.Author
	*model.AdMaterialAlbumAddReq
}

// AdMaterialAlbumAddRes 添加操作返回结果
type AdMaterialAlbumAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialAlbumEditReq 修改操作请求参数
type AdMaterialAlbumEditReq struct {
	g.Meta `path:"/edit" tags:"广告素材专辑" method:"put" summary:"广告素材专辑修改"`
	commonApi.Author
	*model.AdMaterialAlbumEditReq
}

// AdMaterialAlbumEditRes 修改操作返回结果
type AdMaterialAlbumEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialAlbumGetReq 获取一条数据请求
type AdMaterialAlbumGetReq struct {
	g.Meta `path:"/get" tags:"广告素材专辑" method:"get" summary:"获取广告素材专辑信息"`
	commonApi.Author
	AlbumId int `p:"albumId" v:"required#主键必须"` //通过主键获取
}

// AdMaterialAlbumGetRes 获取一条数据结果
type AdMaterialAlbumGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialAlbumInfoRes
}

// AdMaterialAlbumDeleteReq 删除数据请求
type AdMaterialAlbumDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告素材专辑" method:"delete" summary:"删除广告素材专辑"`
	commonApi.Author
	AlbumIds []int `p:"albumIds" v:"required#主键必须"` //通过主键删除
}

// AdMaterialAlbumDeleteRes 删除数据返回
type AdMaterialAlbumDeleteRes struct {
	commonApi.EmptyRes
}
