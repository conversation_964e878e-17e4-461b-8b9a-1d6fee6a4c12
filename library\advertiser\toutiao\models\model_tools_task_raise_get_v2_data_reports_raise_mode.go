/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsTaskRaiseGetV2DataReportsRaiseMode
type ToolsTaskRaiseGetV2DataReportsRaiseMode string

// List of tools_task_raise_get_v2_data_reports_raise_mode
const (
	CUSTOM_ToolsTaskRaiseGetV2DataReportsRaiseMode ToolsTaskRaiseGetV2DataReportsRaiseMode = "CUSTOM"
	STRONG_ToolsTaskRaiseGetV2DataReportsRaiseMode ToolsTaskRaiseGetV2DataReportsRaiseMode = "STRONG"
)

// Ptr returns reference to tools_task_raise_get_v2_data_reports_raise_mode value
func (v ToolsTaskRaiseGetV2DataReportsRaiseMode) Ptr() *ToolsTaskRaiseGetV2DataReportsRaiseMode {
	return &v
}
