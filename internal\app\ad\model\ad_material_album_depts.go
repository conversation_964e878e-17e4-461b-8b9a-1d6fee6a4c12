// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-13 15:31:08
// 生成路径: internal/app/ad/model/ad_material_album_depts.go
// 生成人：cyao
// desc:广告素材专辑和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdMaterialAlbumDeptsInfoRes is the golang structure for table ad_material_album_depts.
type AdMaterialAlbumDeptsInfoRes struct {
	gmeta.Meta `orm:"table:ad_material_album_depts"`
	AlbumId    int `orm:"album_id,primary" json:"albumId" dc:"专辑ID"` // 专辑ID
	DetpId     int `orm:"detp_id,primary" json:"detpId" dc:"部门ID"`   // 部门ID
}

type AdMaterialAlbumDeptsListRes struct {
	AlbumId int `json:"albumId" dc:"专辑ID"`
	DetpId  int `json:"detpId" dc:"部门ID"`
}

// AdMaterialAlbumDeptsSearchReq 分页请求参数
type AdMaterialAlbumDeptsSearchReq struct {
	comModel.PageReq
	AlbumId string `p:"albumId" dc:"专辑ID"` //专辑ID
	DetpId  string `p:"detpId" dc:"部门ID"`  //部门ID
}

// AdMaterialAlbumDeptsSearchRes 列表返回结果
type AdMaterialAlbumDeptsSearchRes struct {
	comModel.ListRes
	List []*AdMaterialAlbumDeptsListRes `json:"list"`
}

// AdMaterialAlbumDeptsAddReq 添加操作请求参数
type AdMaterialAlbumDeptsAddReq struct {
	AlbumId int `p:"albumId" v:"required#主键ID不能为空" dc:"专辑ID"`
}

// AdMaterialAlbumDeptsEditReq 修改操作请求参数
type AdMaterialAlbumDeptsEditReq struct {
	AlbumId int `p:"albumId" v:"required#主键ID不能为空" dc:"专辑ID"`
}
