// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-18 15:50:58
// 生成路径: api/v1/member/m_member_task_statistics.go
// 生成人：cq
// desc:任务数据统计表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberTaskStatisticsSearchReq 分页请求参数
type MMemberTaskStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"任务数据统计表" method:"post" summary:"任务数据统计表列表"`
	commonApi.Author
	model.MMemberTaskStatisticsSearchReq
}

// MMemberTaskStatisticsSearchRes 列表返回结果
type MMemberTaskStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberTaskStatisticsSearchRes
}

// MMemberTaskStatisticsExportReq 导出请求
type MMemberTaskStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"任务数据统计表" method:"post" summary:"任务数据统计表导出"`
	commonApi.Author
	model.MMemberTaskStatisticsSearchReq
}

// MMemberTaskStatisticsExportRes 导出响应
type MMemberTaskStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberTaskStatisticsTaskReq 分页请求参数
type MMemberTaskStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"任务数据统计表" method:"post" summary:"任务数据统计"`
	commonApi.Author
	model.MMemberTaskStatisticsTaskReq
}

// MMemberTaskStatisticsTaskRes 列表返回结果
type MMemberTaskStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}
