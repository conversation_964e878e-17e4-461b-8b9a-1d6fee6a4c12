// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-07 18:17:18
// 生成路径: api/v1/channel/s_channel_hour_statistics.go
// 生成人：lx
// desc:渠道充值小时统计数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelHourStatisticsSearchReq 分页请求参数
type SChannelHourStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"渠道充值小时统计数据" method:"get" summary:"渠道充值小时统计数据列表"`
	commonApi.Author
	model.SChannelHourStatisticsSearchReq
}

// SChannelHourStatisticsSearchRes 列表返回结果
type SChannelHourStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelHourStatisticsSearchRes
}

// SChannelHourStatisticsAddReq 添加操作请求参数
type SChannelHourStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"渠道充值小时统计数据" method:"post" summary:"渠道充值小时统计数据添加"`
	commonApi.Author
	*model.SChannelHourStatisticsAddReq
}

// SChannelHourStatisticsAddRes 添加操作返回结果
type SChannelHourStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SChannelHourStatisticsEditReq 修改操作请求参数
type SChannelHourStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"渠道充值小时统计数据" method:"put" summary:"渠道充值小时统计数据修改"`
	commonApi.Author
	*model.SChannelHourStatisticsEditReq
}

// SChannelHourStatisticsEditRes 修改操作返回结果
type SChannelHourStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SChannelHourStatisticsGetReq 获取一条数据请求
type SChannelHourStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"渠道充值小时统计数据" method:"get" summary:"获取渠道充值小时统计数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelHourStatisticsGetRes 获取一条数据结果
type SChannelHourStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelHourStatisticsInfoRes
}

// SChannelHourStatisticsDeleteReq 删除数据请求
type SChannelHourStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道充值小时统计数据" method:"delete" summary:"删除渠道充值小时统计数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelHourStatisticsDeleteRes 删除数据返回
type SChannelHourStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
