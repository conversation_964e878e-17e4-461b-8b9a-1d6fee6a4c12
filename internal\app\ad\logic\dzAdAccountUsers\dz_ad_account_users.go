// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:33
// 生成路径: internal/app/ad/logic/dz_ad_account_users.go
// 生成人：cyao
// desc:权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzAdAccountUsers(New())
}

func New() service.IDzAdAccountUsers {
	return &sDzAdAccountUsers{}
}

type sDzAdAccountUsers struct{}

func (s *sDzAdAccountUsers) List(ctx context.Context, req *model.DzAdAccountUsersSearchReq) (listRes *model.DzAdAccountUsersSearchRes, err error) {
	listRes = new(model.DzAdAccountUsersSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdAccountUsers.Ctx(ctx).WithAll()
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdAccountUsers.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if len(req.ChannelIds) > 0 {
			m = m.WhereIn(dao.DzAdAccountUsers.Columns().ChannelId, req.ChannelIds)
		}
		if req.SpecifyUserId != "" {
			m = m.Where(dao.DzAdAccountUsers.Columns().SpecifyUserId+" = ?", req.SpecifyUserId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "channel_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzAdAccountUsersListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdAccountUsersListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzAdAccountUsersListRes{
				ChannelId:     v.ChannelId,
				SpecifyUserId: v.SpecifyUserId,
			}
		}
	})
	return
}

func (s *sDzAdAccountUsers) GetByChannelId(ctx context.Context, channelId int64) (res *model.DzAdAccountUsersInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdAccountUsers.Ctx(ctx).WithAll().Where(dao.DzAdAccountUsers.Columns().ChannelId, channelId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdAccountUsers) Add(ctx context.Context, req *model.DzAdAccountUsersAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountUsers.Ctx(ctx).Insert(do.DzAdAccountUsers{
			ChannelId: req.ChannelId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzAdAccountUsers) Edit(ctx context.Context, req *model.DzAdAccountUsersEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountUsers.Ctx(ctx).WherePri(req.ChannelId).Update(do.DzAdAccountUsers{})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzAdAccountUsers) Delete(ctx context.Context, channelIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdAccountUsers.Ctx(ctx).Delete(dao.DzAdAccountUsers.Columns().SpecifyUserId+" in (?)", channelIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
