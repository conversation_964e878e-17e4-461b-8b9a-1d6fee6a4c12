/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletUpdateV30MembershipType
type ToolsWechatAppletUpdateV30MembershipType string

// List of tools_wechat_applet_update_v3.0_membership_type
const (
	ANNUAL_ToolsWechatAppletUpdateV30MembershipType       ToolsWechatAppletUpdateV30MembershipType = "ANNUAL"
	LIFETIME_ToolsWechatAppletUpdateV30MembershipType     ToolsWechatAppletUpdateV30MembershipType = "LIFETIME"
	MONTHLY_ToolsWechatAppletUpdateV30MembershipType      ToolsWechatAppletUpdateV30MembershipType = "MONTHLY"
	NONE_ToolsWechatAppletUpdateV30MembershipType         ToolsWechatAppletUpdateV30MembershipType = "NONE"
	WEEKLY_DAILY_ToolsWechatAppletUpdateV30MembershipType ToolsWechatAppletUpdateV30MembershipType = "WEEKLY_DAILY"
)

// Ptr returns reference to tools_wechat_applet_update_v3.0_membership_type value
func (v ToolsWechatAppletUpdateV30MembershipType) Ptr() *ToolsWechatAppletUpdateV30MembershipType {
	return &v
}
