// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-24 16:09:27
// 生成路径: api/v1/system/m_member_view.go
// 生成人：cyao
// desc:用户浏览记录表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberViewSearchReq 分页请求参数
type MMemberViewSearchReq struct {
	g.Meta `path:"/list" tags:"用户浏览记录表" method:"get" summary:"用户浏览记录表列表"`
	commonApi.Author
	model.MMemberViewSearchReq
}

// MMemberViewSearchRes 列表返回结果
type MMemberViewSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberViewSearchRes
}

// MMemberViewExportReq 导出请求
type MMemberViewExportReq struct {
	g.Meta `path:"/export" tags:"用户浏览记录表" method:"get" summary:"用户浏览记录表导出"`
	commonApi.Author
	model.MMemberViewSearchReq
}

// MMemberViewExportRes 导出响应
type MMemberViewExportRes struct {
	commonApi.EmptyRes
}
type MMemberViewExcelTemplateReq struct {
	g.Meta `path:"/excelTemplate" tags:"用户浏览记录表" method:"get" summary:"导出模板文件"`
	commonApi.Author
}
type MMemberViewExcelTemplateRes struct {
	commonApi.EmptyRes
}
type MMemberViewImportReq struct {
	g.Meta `path:"/import" tags:"用户浏览记录表" method:"post" summary:"用户浏览记录表导入"`
	commonApi.Author
	File *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}
type MMemberViewImportRes struct {
	commonApi.EmptyRes
}

// MMemberViewAddReq 添加操作请求参数
type MMemberViewAddReq struct {
	g.Meta `path:"/add" tags:"用户浏览记录表" method:"post" summary:"用户浏览记录表添加"`
	commonApi.Author
	*model.MMemberViewAddReq
}

// MMemberViewAddRes 添加操作返回结果
type MMemberViewAddRes struct {
	commonApi.EmptyRes
}

// MMemberViewEditReq 修改操作请求参数
type MMemberViewEditReq struct {
	g.Meta `path:"/edit" tags:"用户浏览记录表" method:"put" summary:"用户浏览记录表修改"`
	commonApi.Author
	*model.MMemberViewEditReq
}

// MMemberViewEditRes 修改操作返回结果
type MMemberViewEditRes struct {
	commonApi.EmptyRes
}

// MMemberViewGetReq 获取一条数据请求
type MMemberViewGetReq struct {
	g.Meta `path:"/get" tags:"用户浏览记录表" method:"get" summary:"获取用户浏览记录表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberViewGetRes 获取一条数据结果
type MMemberViewGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberViewInfoRes
}

// MMemberViewDeleteReq 删除数据请求
type MMemberViewDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户浏览记录表" method:"post" summary:"删除用户浏览记录表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MMemberViewDeleteRes 删除数据返回
type MMemberViewDeleteRes struct {
	commonApi.EmptyRes
}
