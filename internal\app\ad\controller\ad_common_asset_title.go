// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-11 13:50:11
// 生成路径: internal/app/ad/controller/ad_common_asset_title.go
// 生成人：cq
// desc:通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
	"strings"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adCommonAssetTitleController struct {
	systemController.BaseController
}

var AdCommonAssetTitle = new(adCommonAssetTitleController)

// List 列表
func (c *adCommonAssetTitleController) List(ctx context.Context, req *ad.AdCommonAssetTitleSearchReq) (res *ad.AdCommonAssetTitleSearchRes, err error) {
	res = new(ad.AdCommonAssetTitleSearchRes)
	res.AdCommonAssetTitleSearchRes, err = service.AdCommonAssetTitle().List(ctx, &req.AdCommonAssetTitleSearchReq)
	return
}

// AdCommonAssetTitleGetRandomTitles
func (c *adCommonAssetTitleController) GetRandomTitles(ctx context.Context, req *ad.AdCommonAssetTitleGetRandomTitlesReq) (res *ad.AdCommonAssetTitleGetRandomTitlesRes, err error) {
	res = new(ad.AdCommonAssetTitleGetRandomTitlesRes)
	res.AdCommonAssetTitleSearchRes, err = service.AdCommonAssetTitle().GetRandomTitles(ctx, &req.AdCommonAssetTitleGetRandomTitlesReq)
	return
}

// Export 导出excel
func (c *adCommonAssetTitleController) Export(ctx context.Context, req *ad.AdCommonAssetTitleExportReq) (res *ad.AdCommonAssetTitleExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdCommonAssetTitleListRes
		//表头
		tableHead = []interface{}{"标题名称", "标题分类", "创建者", "近3日点击率", "近3日消耗", "历史点击率", "历史消耗", "关联广告数"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.AdCommonAssetTitle().GetExportData(ctx, &req.AdCommonAssetTitleSearchReq)
		if err != nil {
			return
		}
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			var category string
			categories := make([]string, 0)
			if v.CategoryList != nil && len(v.CategoryList) > 0 {
				for _, categoryInfo := range v.CategoryList {
					categories = append(categories, categoryInfo.Category)
				}
				category = strings.Join(categories, ",")
			}
			dt := []interface{}{
				v.Title,
				category,
				v.UserName,
				v.Last3DayClickRate,
				v.Last3DayCost,
				v.HistoryClickRate,
				v.HistoryCost,
				v.AdCount,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("标题库")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取通用资产-标题库
func (c *adCommonAssetTitleController) Get(ctx context.Context, req *ad.AdCommonAssetTitleGetReq) (res *ad.AdCommonAssetTitleGetRes, err error) {
	res = new(ad.AdCommonAssetTitleGetRes)
	res.AdCommonAssetTitleInfoRes, err = service.AdCommonAssetTitle().GetById(ctx, req.Id)
	return
}

// Add 添加通用资产-标题库
func (c *adCommonAssetTitleController) Add(ctx context.Context, req *ad.AdCommonAssetTitleAddReq) (res *ad.AdCommonAssetTitleAddRes, err error) {
	err = service.AdCommonAssetTitle().Add(ctx, req.AdCommonAssetTitleAddReq)
	return
}

// Edit 修改通用资产-标题库
func (c *adCommonAssetTitleController) Edit(ctx context.Context, req *ad.AdCommonAssetTitleEditReq) (res *ad.AdCommonAssetTitleEditRes, err error) {
	err = service.AdCommonAssetTitle().Edit(ctx, req.AdCommonAssetTitleEditReq)
	return
}

// Delete 删除通用资产-标题库
func (c *adCommonAssetTitleController) Delete(ctx context.Context, req *ad.AdCommonAssetTitleDeleteReq) (res *ad.AdCommonAssetTitleDeleteRes, err error) {
	err = service.AdCommonAssetTitle().Delete(ctx, req.Ids)
	return
}
