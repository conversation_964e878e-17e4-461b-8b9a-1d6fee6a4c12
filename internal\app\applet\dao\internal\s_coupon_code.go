// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-07-23 14:36:18
// 生成路径: internal/app/applet/dao/internal/s_coupon_code.go
// 生成人：lx
// desc:兑换码表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SCouponCodeDao is the manager for logic model data accessing and custom defined data operations functions management.
type SCouponCodeDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns SCouponCodeColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SCouponCodeColumns defines and stores column names for table s_coupon_code.
type SCouponCodeColumns struct {
	Id         string //
	Code       string // 兑换码
	ExpireTime string // 过期时间
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
	UseStatus  string // 使用状态，0未使用，1已使用
}

var sCouponCodeColumns = SCouponCodeColumns{
	Id:         "id",
	Code:       "code",
	ExpireTime: "expire_time",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	UseStatus:  "use_status",
}

// NewSCouponCodeDao creates and returns a new DAO object for table data access.
func NewSCouponCodeDao() *SCouponCodeDao {
	return &SCouponCodeDao{
		group:   "default",
		table:   "s_coupon_code",
		columns: sCouponCodeColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SCouponCodeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SCouponCodeDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SCouponCodeDao) Columns() SCouponCodeColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SCouponCodeDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SCouponCodeDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SCouponCodeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
