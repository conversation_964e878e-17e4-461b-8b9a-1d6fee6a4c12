package model

import (
	"fmt"
)

type ReportCustomGetV30DataTopic string

// AdAdvertiserAccountMetricsDataSeed   获取指标数据指标
type AdAdvertiserAccountMetricsDataSeed struct {
	StatCost                   float64 `orm:"stat_cost" json:"statCost" dc:"消耗"`                                                 // 消耗
	ShowCnt                    int64   `orm:"show_cnt" json:"showCnt" dc:"展示数"`                                                  // 展示数
	CpmPlatform                float64 `orm:"cpm_platform" json:"cpmPlatform" dc:"平均千次展现费用(元)"`                                  // 平均千次展现费用(元)
	ClickCnt                   int64   `orm:"click_cnt" json:"clickCnt" dc:"点击数"`                                                // 点击数
	Ctr                        float64 `orm:"ctr" json:"ctr" dc:"点击率"`                                                           // 点击率
	CpcPlatform                float64 `orm:"cpc_platform" json:"cpcPlatform" dc:"平均点击单价(元)"`                                    // 平均点击单价(元)
	ConvertCnt                 int64   `orm:"convert_cnt" json:"convertCnt" dc:"转化数"`                                            // 转化数
	ConversionCost             float64 `orm:"conversion_cost" json:"conversionCost" dc:"平均转化成本"`                                 // 平均转化成本
	ConversionRate             float64 `orm:"conversion_rate" json:"conversionRate" dc:"转化率"`                                    // 转化率
	DeepConvertCnt             int64   `orm:"deep_convert_cnt" json:"deepConvertCnt" dc:"深度转化数"`                                 // 深度转化数
	DeepConvertCost            float64 `orm:"deep_convert_cost" json:"deepConvertCost" dc:"深度转化成本"`                              // 深度转化成本
	DeepConvertRate            float64 `orm:"deep_convert_rate" json:"deepConvertRate" dc:"深度转化率"`                               // 深度转化率
	AttributionConvertCnt      int64   `orm:"attribution_convert_cnt" json:"attributionConvertCnt" dc:"转化数(计费时间)"`               // 转化数(计费时间)
	AttributionConvertCost     float64 `orm:"attribution_convert_cost" json:"attributionConvertCost" dc:"转化成本(计费时间)"`            // 转化成本(计费时间)
	AttributionDeepConvertCnt  int64   `orm:"attribution_deep_convert_cnt" json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`    // 深度转化数(计费时间)
	AttributionDeepConvertCost float64 `orm:"attribution_deep_convert_cost" json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"` // 深度转化成本(计费时间)
	PreConvertCount            int64   `orm:"pre_convert_count" json:"preConvertCount" dc:"预估转化数(计费时间)"`                         // 预估转化数(计费时间)
	PreConvertCost             float64 `orm:"pre_convert_cost" json:"preConvertCost" dc:"预估转化成本(计费时间)"`                          // 预估转化成本(计费时间)
	PreConvertRate             float64 `orm:"pre_convert_rate" json:"preConvertRate" dc:"预估转化率(计费时间)"`                           // 预估转化率(计费时间)
	ClickStartCnt              int64   `orm:"click_start_cnt" json:"clickStartCnt" dc:"安卓下载开始数"`                                 // 安卓下载开始数
	ClickStartCost             float64 `orm:"click_start_cost" json:"clickStartCost" dc:"安卓下载开始成本"`                              // 安卓下载开始成本
	ClickStartRate             float64 `orm:"click_start_rate" json:"clickStartRate" dc:"安卓下载开始率"`                               // 安卓下载开始率
	DownloadFinishCnt          int64   `orm:"download_finish_cnt" json:"downloadFinishCnt" dc:"安卓下载完成数"`                         // 安卓下载完成数
	DownloadFinishCost         float64 `orm:"download_finish_cost" json:"downloadFinishCost" dc:"安卓下载完成成本"`                      // 安卓下载完成成本
	DownloadFinishRate         float64 `orm:"download_finish_rate" json:"downloadFinishRate" dc:"安卓下载完成率"`                       // 安卓下载完成率
	InstallFinishCnt           int64   `orm:"install_finish_cnt" json:"installFinishCnt" dc:"安卓安装完成数"`                           // 安卓安装完成数
	InstallFinishCost          float64 `orm:"install_finish_cost" json:"installFinishCost" dc:"安卓安装完成成本"`                        // 安卓安装完成成本
	InstallFinishRate          float64 `orm:"install_finish_rate" json:"installFinishRate" dc:"安卓安装完成率"`                         // 安卓安装完成率
	Active                     int64   `orm:"active" json:"active" dc:"激活数"`                                                     // 激活数
	ActiveCost                 float64 `orm:"active_cost" json:"activeCost" dc:"激活成本"`                                           // 激活成本
	ActiveRate                 float64 `orm:"active_rate" json:"activeRate" dc:"激活率"`
	ActiveRegister             int64   `orm:"active_register" json:"activeRegister" dc:"注册数"`
	StatPayAmount              float64 `orm:"stat_pay_amount" json:"statPayAmount,omitempty"` // 付费金额（回传时间）
	PayAmountRoi               float64 `orm:"pay_amount_roi" json:"payAmountRoi,omitempty"`   // 付费ROI（回传时间）
	// 激活率
	ActiveRegisterCost                    float64 `orm:"active_register_cost" json:"activeRegisterCost" dc:"注册成本"`                                                 // 注册成本
	ActiveRegisterRate                    float64 `orm:"active_register_rate" json:"activeRegisterRate" dc:"注册率"`                                                  // 注册率
	GameAddiction                         int64   `orm:"game_addiction" json:"gameAddiction" dc:"关键行为数"`                                                           // 关键行为数
	GameAddictionCost                     float64 `orm:"game_addiction_cost" json:"gameAddictionCost" dc:"关键行为成本"`                                                 // 关键行为成本
	GameAddictionRate                     float64 `orm:"game_addiction_rate" json:"gameAddictionRate" dc:"关键行为率"`                                                  // 关键行为率
	AttributionNextDayOpenCnt             int64   `orm:"attribution_next_day_open_cnt" json:"attributionNextDayOpenCnt" dc:"次留数"`                                  // 次留数
	AttributionNextDayOpenCost            float64 `orm:"attribution_next_day_open_cost" json:"attributionNextDayOpenCost" dc:"次留成本"`                               // 次留成本
	AttributionNextDayOpenRate            float64 `orm:"attribution_next_day_open_rate" json:"attributionNextDayOpenRate" dc:"次留率"`                                // 次留率
	NextDayOpen                           int64   `orm:"next_day_open" json:"nextDayOpen" dc:"次留回传数"`                                                              // 次留回传数
	ActivePay                             int64   `orm:"active_pay" json:"activePay" dc:"首次付费数"`                                                                   // 首次付费数
	ActivePayCost                         float64 `orm:"active_pay_cost" json:"activePayCost" dc:"首次付费成本"`                                                         // 首次付费成本
	ActivePayRate                         float64 `orm:"active_pay_rate" json:"activePayRate" dc:"首次付费率"`                                                          // 首次付费率
	GamePayCount                          int64   `orm:"game_pay_count" json:"gamePayCount" dc:"付费次数"`                                                             // 付费次数
	GamePayCost                           float64 `orm:"game_pay_cost" json:"gamePayCost" dc:"付费成本"`                                                               // 付费成本
	AttributionGamePay7DCount             int64   `orm:"attribution_game_pay_7d_count" json:"attributionGamePay7DCount" dc:"7日付费次数(激活时间)"`                         // 7日付费次数(激活时间)
	AttributionGamePay7DCost              float64 `orm:"attribution_game_pay_7d_cost" json:"attributionGamePay7DCost" dc:"7日付费成本(激活时间)"`                           // 7日付费成本(激活时间)
	AttributionActivePay7DPerCount        int64   `orm:"attribution_active_pay_7d_per_count" json:"attributionActivePay7DPerCount" dc:"7日人均付费次数(激活时间)"`            // 7日人均付费次数(激活时间)
	InAppUv                               int64   `orm:"in_app_uv" json:"inAppUv" dc:"APP内访问"`                                                                     // APP内访问
	InAppDetailUv                         int64   `orm:"in_app_detail_uv" json:"inAppDetailUv" dc:"APP内访问详情页"`                                                     // APP内访问详情页
	InAppCart                             int64   `orm:"in_app_cart" json:"inAppCart" dc:"APP内加入购物车"`                                                              // APP内加入购物车
	InAppPay                              int64   `orm:"in_app_pay" json:"inAppPay" dc:"APP内付费"`                                                                   // APP内付费
	InAppOrder                            int64   `orm:"in_app_order" json:"inAppOrder" dc:"APP内下单"`                                                               // APP内下单
	AttributionGameInAppLtv1Day           float64 `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1Day" dc:"当日付费金额"`                          // 当日付费金额
	AttributionGameInAppLtv2Days          float64 `orm:"attribution_game_in_app_ltv_2days" json:"attributionGameInAppLtv2Days" dc:"激活后一日付费金额"`                     // 激活后一日付费金额
	AttributionGameInAppLtv3Days          float64 `orm:"attribution_game_in_app_ltv_3days" json:"attributionGameInAppLtv3Days" dc:"激活后二日付费金额"`                     // 激活后二日付费金额
	AttributionGameInAppLtv4Days          float64 `orm:"attribution_game_in_app_ltv_4days" json:"attributionGameInAppLtv4Days" dc:"激活后三日付费金额"`                     // 激活后三日付费金额
	AttributionGameInAppLtv5Days          float64 `orm:"attribution_game_in_app_ltv_5days" json:"attributionGameInAppLtv5Days" dc:"激活后四日付费金额"`                     // 激活后四日付费金额
	AttributionGameInAppLtv6Days          float64 `orm:"attribution_game_in_app_ltv_6days" json:"attributionGameInAppLtv6Days" dc:"激活后五日付费金额"`                     // 激活后五日付费金额
	AttributionGameInAppLtv7Days          float64 `orm:"attribution_game_in_app_ltv_7days" json:"attributionGameInAppLtv7Days" dc:"激活后六日付费金额"`                     // 激活后六日付费金额
	AttributionGameInAppLtv8Days          float64 `orm:"attribution_game_in_app_ltv_8days" json:"attributionGameInAppLtv8Days" dc:"激活后七日付费金额"`                     // 激活后七日付费金额
	AttributionGameInAppRoi1Day           float64 `orm:"attribution_game_in_app_roi_1day" json:"attributionGameInAppRoi1Day" dc:"当日付费ROI"`                         // 当日付费ROI
	AttributionGameInAppRoi2Days          float64 `orm:"attribution_game_in_app_roi_2days" json:"attributionGameInAppRoi2Days" dc:"激活后一日付费ROI"`                    // 激活后一日付费ROI
	AttributionGameInAppRoi3Days          float64 `orm:"attribution_game_in_app_roi_3days" json:"attributionGameInAppRoi3Days" dc:"激活后二日付费ROI"`                    // 激活后二日付费ROI
	AttributionGameInAppRoi4Days          float64 `orm:"attribution_game_in_app_roi_4days" json:"attributionGameInAppRoi4Days" dc:"激活后三日付费ROI"`                    // 激活后三日付费ROI
	AttributionGameInAppRoi5Days          float64 `orm:"attribution_game_in_app_roi_5days" json:"attributionGameInAppRoi5Days" dc:"激活后四日付费ROI"`                    // 激活后四日付费ROI
	AttributionGameInAppRoi6Days          float64 `orm:"attribution_game_in_app_roi_6days" json:"attributionGameInAppRoi6Days" dc:"激活后五日付费ROI"`                    // 激活后五日付费ROI
	AttributionGameInAppRoi7Days          float64 `orm:"attribution_game_in_app_roi_7days" json:"attributionGameInAppRoi7Days" dc:"激活后六日付费ROI"`                    // 激活后六日付费ROI
	AttributionGameInAppRoi8Days          float64 `orm:"attribution_game_in_app_roi_8days" json:"attributionGameInAppRoi8Days" dc:"激活后七日付费ROI"`                    // 激活后七日付费ROI
	AttributionDayActivePayCount          int64   `orm:"attribution_day_active_pay_count" json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`                   // 计费当日激活且首次付费数
	AttributionActivePayIntraOneDayCount  int64   `orm:"attribution_active_pay_intra_one_day_count" json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`  // 激活后24h首次付费数
	AttributionActivePayIntraOneDayCost   float64 `orm:"attribution_active_pay_intra_one_day_cost" json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`   // 激活后24h首次付费成本
	AttributionActivePayIntraOneDayRate   float64 `orm:"attribution_active_pay_intra_one_day_rate" json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`    // 激活后24h首次付费率
	AttributionActivePayIntraOneDayAmount float64 `orm:"attribution_active_pay_intra_one_day_amount" json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"` // 激活后24h付费金额
	AttributionActivePayIntraOneDayRoi    float64 `orm:"attribution_active_pay_intra_one_day_roi" json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`      // 激活后24h付费ROI
	AttributionRetention2DCnt             int64   `orm:"attribution_retention_2d_cnt" json:"attributionRetention2DCnt" dc:"2日留存数"`                                 // 2日留存数
	AttributionRetention2DCost            float64 `orm:"attribution_retention_2d_cost" json:"attributionRetention2DCost" dc:"2日留存成本"`                              // 2日留存成本
	AttributionRetention2DRate            float64 `orm:"attribution_retention_2d_rate" json:"attributionRetention2DRate" dc:"2日留存率"`                               // 2日留存率
	AttributionRetention3DCnt             int64   `orm:"attribution_retention_3d_cnt" json:"attributionRetention3DCnt" dc:"3日留存数"`                                 // 3日留存数
	AttributionRetention3DCost            float64 `orm:"attribution_retention_3d_cost" json:"attributionRetention3DCost" dc:"3日留存成本"`                              // 3日留存成本
	AttributionRetention3DRate            float64 `orm:"attribution_retention_3d_rate" json:"attributionRetention3DRate" dc:"3日留存率"`                               // 3日留存率
	AttributionRetention4DCnt             int64   `orm:"attribution_retention_4d_cnt" json:"attributionRetention4DCnt" dc:"4日留存数"`                                 // 4日留存数
	AttributionRetention4DCost            float64 `orm:"attribution_retention_4d_cost" json:"attributionRetention4DCost" dc:"4日留存成本"`                              // 4日留存成本
	AttributionRetention4DRate            float64 `orm:"attribution_retention_4d_rate" json:"attributionRetention4DRate" dc:"4日留存率"`                               // 4日留存率
	AttributionRetention5DCnt             int64   `orm:"attribution_retention_5d_cnt" json:"attributionRetention5DCnt" dc:"5日留存数"`                                 // 5日留存数
	AttributionRetention5DCost            float64 `orm:"attribution_retention_5d_cost" json:"attributionRetention5DCost" dc:"5日留存成本"`                              // 5日留存成本
	AttributionRetention5DRate            float64 `orm:"attribution_retention_5d_rate" json:"attributionRetention5DRate" dc:"5日留存率"`                               // 5日留存率
	AttributionRetention6DCnt             int64   `orm:"attribution_retention_6d_cnt" json:"attributionRetention6DCnt" dc:"6日留存数"`                                 // 6日留存数
	AttributionRetention6DCost            float64 `orm:"attribution_retention_6d_cost" json:"attributionRetention6DCost" dc:"6日留存成本"`                              // 6日留存成本
	AttributionRetention6DRate            float64 `orm:"attribution_retention_6d_rate" json:"attributionRetention6DRate" dc:"6日留存率"`                               // 6日留存率
	AttributionRetention7DCnt             int64   `orm:"attribution_retention_7d_cnt" json:"attributionRetention7DCnt" dc:"7日留存数"`                                 // 7日留存数
	AttributionRetention7DCost            float64 `orm:"attribution_retention_7d_cost" json:"attributionRetention7DCost" dc:"7日留存成本"`                              // 7日留存成本
	AttributionRetention7DRate            float64 `orm:"attribution_retention_7d_rate" json:"attributionRetention7DRate" dc:"7日留存率"`                               // 7日留存率
	AttributionRetention7DSumCnt          int64   `orm:"attribution_retention_7d_sum_cnt" json:"attributionRetention7DSumCnt" dc:"7日留存总数"`                         // 7日留存总数
	AttributionRetention7DTotalCost       float64 `orm:"attribution_retention_7d_total_cost" json:"attributionRetention7DTotalCost" dc:"7日留存总成本"`                  // 7日留存总成本
	TotalPlay                             int64   `orm:"total_play" json:"totalPlay" dc:"播放量"`                                                                     // 播放量
	ValidPlay                             int64   `orm:"valid_play" json:"validPlay" dc:"有效播放数"`                                                                   // 有效播放数
	ValidPlayCost                         float64 `orm:"valid_play_cost" json:"validPlayCost" dc:"有效播放成本"`                                                         // 有效播放成本
	ValidPlayRate                         float64 `orm:"valid_play_rate" json:"validPlayRate" dc:"有效播放率"`                                                          // 有效播放率
	Play25FeedBreak                       int64   `orm:"play_25_feed_break" json:"play25FeedBreak" dc:"25%进度播放数"`                                                  // 25%进度播放数
	Play50FeedBreak                       int64   `orm:"play_50_feed_break" json:"play50FeedBreak" dc:"50%进度播放数"`                                                  // 50%进度播放数
	Play75FeedBreak                       int64   `orm:"play_75_feed_break" json:"play75FeedBreak" dc:"75%进度播放数"`                                                  // 75%进度播放数
	Play99FeedBreak                       int64   `orm:"play_99_feed_break" json:"play99FeedBreak" dc:"99%进度播放数"`                                                  // 99%进度播放数
	AveragePlayTimePerPlay                float64 `orm:"average_play_time_per_play" json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`                                   // 平均单次播放时长
	PlayOverRate                          float64 `orm:"play_over_rate" json:"playOverRate" dc:"完播率"`                                                              // 完播率
	WifiPlayRate                          float64 `orm:"wifi_play_rate" json:"wifiPlayRate" dc:"WiFi播放占比"`                                                         // WiFi播放占比
	CardShow                              int64   `orm:"card_show" json:"cardShow" dc:"3秒卡片展现数"`                                                                   // 3秒卡片展现数
	DyLike                                int64   `orm:"dy_like" json:"dyLike" dc:"点赞数"`                                                                           // 点赞数
	DyComment                             int64   `orm:"dy_comment" json:"dyComment" dc:"评论量"`                                                                     // 评论量
	DyShare                               int64   `orm:"dy_share" json:"dyShare" dc:"分享量"`                                                                         // 分享量
	IesChallengeClick                     int64   `orm:"ies_challenge_click" json:"iesChallengeClick" dc:"挑战赛查看数"`                                                 // 挑战赛查看数
	IesMusicClick                         int64   `orm:"ies_music_click" json:"iesMusicClick" dc:"音乐查看数"`                                                          // 音乐查看数
	LocationClick                         int64   `orm:"location_click" json:"locationClick" dc:"POI点击数"`                                                          // POI点击数
	CustomerEffective                     int64   `orm:"customer_effective" json:"customerEffective" dc:"有效获客"`                                                    // 有效获客
	Wechat                                int64   `orm:"wechat" json:"wechat" dc:"微信复制"`                                                                           // 微信复制
	AttributionMicroGame0DLtv             float64 `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0DLtv" dc:"小程序/小游戏当日LTV"`                         // 小程序/小游戏当日LTV
	AttributionMicroGame3DLtv             float64 `orm:"attribution_micro_game_3d_ltv" json:"attributionMicroGame3DLtv" dc:"小程序/小游戏激活后三日LTV"`                      // 小程序/小游戏激活后三日LTV
	AttributionMicroGame7DLtv             float64 `orm:"attribution_micro_game_7d_ltv" json:"attributionMicroGame7DLtv" dc:"小程序/小游戏激活后七日LTV"`                      // 小程序/小游戏激活后七日LTV
	AttributionMicroGame0DRoi             float64 `orm:"attribution_micro_game_0d_roi" json:"attributionMicroGame0DRoi" dc:"小程序/小游戏当日广告变现ROI"`                     // 小程序/小游戏当日广告变现ROI
	AttributionMicroGame3DRoi             float64 `orm:"attribution_micro_game_3d_roi" json:"attributionMicroGame3DRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`                  // 小程序/小游戏激活后三日广告变现ROI
	AttributionMicroGame7DRoi             float64 `orm:"attribution_micro_game_7d_roi" json:"attributionMicroGame7DRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
}

// List of report_custom_get_v3.0_data_topic
const (
	BASIC_DATA_ReportCustomGetV30DataTopic           ReportCustomGetV30DataTopic = "BASIC_DATA"
	BIDWORD_DATA_ReportCustomGetV30DataTopic         ReportCustomGetV30DataTopic = "BIDWORD_DATA"
	DMP_DATA_ReportCustomGetV30DataTopic             ReportCustomGetV30DataTopic = "DMP_DATA"
	MATERIAL_DATA_ReportCustomGetV30DataTopic        ReportCustomGetV30DataTopic = "MATERIAL_DATA"
	ONE_KEY_BOOST_DATA_ReportCustomGetV30DataTopic   ReportCustomGetV30DataTopic = "ONE_KEY_BOOST_DATA"
	PRODUCT_DATA_ReportCustomGetV30DataTopic         ReportCustomGetV30DataTopic = "PRODUCT_DATA"
	QUERY_DATA_ReportCustomGetV30DataTopic           ReportCustomGetV30DataTopic = "QUERY_DATA"
	VIDEO_DUARATION_DATA_ReportCustomGetV30DataTopic ReportCustomGetV30DataTopic = "VIDEO_DUARATION_DATA"
)

// All allowed values of ReportCustomGetV30DataTopic enum
var AllowedReportCustomGetV30DataTopicEnumValues = []ReportCustomGetV30DataTopic{
	"BASIC_DATA",
	"BIDWORD_DATA",
	"DMP_DATA",
	"MATERIAL_DATA",
	"ONE_KEY_BOOST_DATA",
	"PRODUCT_DATA",
	"QUERY_DATA",
	"VIDEO_DUARATION_DATA",
}

// NewReportCustomGetV30DataTopicFromValue returns a pointer to a valid ReportCustomGetV30DataTopic
// for the value passed as argument, or an error if the value passed is not allowed by the enum
func NewReportCustomGetV30DataTopicFromValue(v string) (*ReportCustomGetV30DataTopic, error) {
	ev := ReportCustomGetV30DataTopic(v)
	if ev.IsValid() {
		return &ev, nil
	} else {
		return nil, fmt.Errorf("invalid value '%v' for ReportCustomGetV30DataTopic: valid values are %v", v, AllowedReportCustomGetV30DataTopicEnumValues)
	}
}

// IsValid return true if the value is valid for the enum, false otherwise
func (v ReportCustomGetV30DataTopic) IsValid() bool {
	for _, existing := range AllowedReportCustomGetV30DataTopicEnumValues {
		if existing == v {
			return true
		}
	}
	return false
}

// Ptr returns reference to report_custom_get_v3.0_data_topic value
func (v ReportCustomGetV30DataTopic) Ptr() *ReportCustomGetV30DataTopic {
	return &v
}

type ReportCustomGetV30FiltersInner struct {
	//
	Field string `json:"field"`
	//
	Operator int64 `json:"operator"`
	//
	Type int64 `json:"type"`
	//
	Values []string `json:"values"`
}

type ApiOpenApiV30ReportCustomGetGetRequest struct {
	Dimensions   []string
	AdvertiserId *int64
	Metrics      []string
	Filters      []*ReportCustomGetV30FiltersInner
	StartTime    *string
	EndTime      *string
	OrderBy      []*ReportCustomGetV30OrderByInner
	Page         int32
	PageSize     int32
	DataTopic    *ReportCustomGetV30DataTopic
}

type ReportCustomGetV30OrderByInner struct {
	// 排序字段
	Field string       `json:"field"`
	Type  *OrderByType `json:"type,omitempty"`
}

// ReportCustomGetV30ResponseDataRowsInner struct for ReportCustomGetV30ResponseDataRowsInner
type ReportCustomGetV30ResponseDataRowsInner struct {
	// 维度数据
	Dimensions map[string]string `json:"dimensions"`
	// 指标数据
	Metrics map[string]string `json:"metrics"`
}

// ReportCustomGetV30ResponseDataPageInfo 分页信息
type ReportCustomGetV30ResponseDataPageInfo struct {
	// 页码
	Page int64 `json:"page,omitempty"`
	// 页面大小，即每页展示的数据量
	PageSize int64 `json:"page_size,omitempty"`
	// 总数
	TotalNumber int64 `json:"total_number,omitempty"`
	// 总页数
	TotalPage int64 `json:"total_page,omitempty"`
}

type ReportCustomGetV30ResponseData struct {
	PageInfo *ReportCustomGetV30ResponseDataPageInfo `json:"page_info,omitempty"`
	//
	Rows []*ReportCustomGetV30ResponseDataRowsInner `json:"rows"`
	// 指标汇总数据
	TotalMetrics map[string]string `json:"total_metrics"`
}

type ReportCustomGetV30Response struct {
	//
	Code int64                           `json:"code,omitempty"`
	Data *ReportCustomGetV30ResponseData `json:"data,omitempty"`
	//
	Message string `json:"message,omitempty"`
	//
	RequestId string `json:"request_id,omitempty"`
}
