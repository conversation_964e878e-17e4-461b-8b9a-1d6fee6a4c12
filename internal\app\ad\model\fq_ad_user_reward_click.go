// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: internal/app/ad/model/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdUserRewardClickInfoRes is the golang structure for table fq_ad_user_reward_click.
type FqAdUserRewardClickInfoRes struct {
	gmeta.Meta    `orm:"table:fq_ad_user_reward_click"`
	EcpmNo        string      `orm:"ecpm_no,primary" json:"ecpmNo" dc:"激励收益的唯一键，对齐抖开接口的req_id"`        // 激励收益的唯一键，对齐抖开接口的req_id
	DistributorId string      `orm:"distributor_id" json:"distributorId" dc:"快应用/公众号对应distributor_id"` // 快应用/公众号对应distributor_id
	AppId         string      `orm:"app_id" json:"appId" dc:"公众号/快应用/小程序id（分销平台id）【v1.2】"`             // 公众号/快应用/小程序id（分销平台id）【v1.2】
	AppName       string      `orm:"app_name" json:"appName" dc:"快应用/公众号/小程序名称【v1.2】"`                 // 快应用/公众号/小程序名称【v1.2】
	DeviceId      string      `orm:"device_id" json:"deviceId" dc:"脱敏后的用户设备ID"`                        // 脱敏后的用户设备ID
	PromotionId   string      `orm:"promotion_id" json:"promotionId" dc:"推广链id"`                       // 推广链id
	EcpmCost      int64       `orm:"ecpm_cost" json:"ecpmCost" dc:"激励点击金额，单位十万分之一元"`                   // 激励点击金额，单位十万分之一元
	EventTime     int64       `orm:"event_time" json:"eventTime" dc:"激励点击的时间戳"`                        // 激励点击的时间戳
	RegisterTime  int64       `orm:"register_time" json:"registerTime" dc:"用户染色时间戳"`                   // 用户染色时间戳
	BookId        string      `orm:"book_id" json:"bookId" dc:"染色推广链的短剧ID"`                            // 染色推广链的短剧ID
	BookName      string      `orm:"book_name" json:"bookName" dc:"染色推广链的短剧名称"`                        // 染色推广链的短剧名称
	BookGender    int         `orm:"book_gender" json:"bookGender" dc:"染色推广链短剧性别(0女生、1男生、2无性别)"`       // 染色推广链短剧性别(0女生、1男生、2无性别)
	BookCategory  string      `orm:"book_category" json:"bookCategory" dc:"染色推广链的短剧类型"`                // 染色推广链的短剧类型
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                            // 创建时间
}

type FqAdUserRewardClickListRes struct {
	EventTime              string  `json:"eventTime" dc:"激励点击的时间戳"`
	EventTimeDate          string  `json:"eventTimeDate" dc:"统计时间"`
	DeviceId               string  `json:"deviceId" dc:"脱敏后的用户设备ID"`
	OpenId                 string  `json:"openId" dc:"微信openID"` //微信openID
	RegisterTime           string  `json:"registerTime" dc:"用户染色时间戳"`
	UserId                 uint64  `json:"userId" dc:"投手id"`
	UserName               string  `json:"userName" dc:"投手名"`
	DistributorName        string  `json:"distributorName" dc:"投手上级分销"`
	ChannelCode            string  `orm:"channel_code" json:"channelCode" dc:"渠道号"`
	FqAccount              string  `json:"fqAccount" dc:"番茄账号"`
	FqChannel              string  `json:"fqChannel" dc:"渠道"`
	FqChannelDistributorId string  `json:"fqChannelDistributorId" dc:"番茄渠道ID"`
	EcpmCost               float64 `json:"ecpmCost" dc:"激励点击金额，单位十万分之一元"`
	EcpmCount              float64 `json:"ecpmCount" dc:"激励点击次数"`
	Ip                     string  `json:"ip"  dc:"ip"`
	Clickid                string  `json:"clickid"  dc:"点击ID（仅巨量快应用一跳投放有该数据）"`
	Oaid                   string  `json:"oaid"  dc:"oaid"`
	Caid                   string  `json:"caid"  dc:"caid"`
	Adid                   int64   `json:"adid"  dc:"adid（仅巨量快应用一跳投放有该数据）"`
	OptimizerAccount       string  `json:"optimizerAccount"  dc:"优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount"`
	//EcpmNo           string      `json:"ecpmNo" dc:"激励收益的唯一键，对齐抖开接口的req_id"`
	DistributorId string      `json:"distributorId" dc:"快应用/公众号对应distributor_id"`
	AppId         string      `json:"appId" dc:"公众号/快应用/小程序id（分销平台id）【v1.2】"`
	AppName       string      `json:"appName" dc:"快应用/公众号/小程序名称【v1.2】"`
	PromotionId   string      `json:"promotionId" dc:"推广链id"`
	BookId        string      `json:"bookId" dc:"染色推广链的短剧ID"`
	BookName      string      `json:"bookName" dc:"染色推广链的短剧名称"`
	BookGender    int         `json:"bookGender" dc:"染色推广链短剧性别(0女生、1男生、2无性别)"`
	BookCategory  string      `json:"bookCategory" dc:"染色推广链的短剧类型"`
	ExternalId    string      `json:"externalId"  dc:"企微用户企微id"`
	ProjectId     string      `json:"projectId"  dc:"巨量2.0广告计划组ID"`
	AdIdV2        string      `json:"adIdV2"  dc:"巨量2.0广告计划ID"`
	Mid           string      `json:"mid" v:"required#素材id（分别代表图片、标题、视频、试玩、落地页）不能为空" dc:"素材id（分别代表图片、标题、视频、试玩、落地页）"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
	//7618		/api/v1/ad/fqAdUserRewardClick/list	广告媒体平台	mediaSource	1	1	2025-04-22 10:08:38	0
	MediaSource string `json:"mediaSource" dc:"广告媒体平台"`
	//7616		/api/v1/ad/fqAdUserRewardClick/list	用户手机厂商	deviceBrand	1	1	2025-04-22 10:08:38	0
	DeviceBrand string `json:"deviceBrand" dc:"用户手机厂商"`
}

// FqAdUserRewardClickSearchReq 分页请求参数
type FqAdUserRewardClickSearchReq struct {
	comModel.PageReq
	RegisterStartTime       string   `p:"registerStartTime" dc:"用户染色时间戳"`
	RegisterEndTime         string   `p:"registerEndTime" dc:"用户染色时间戳"`
	StartTime               string   `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime                 string   `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	DeviceId                string   `p:"deviceId" dc:"脱敏后的用户设备ID"`                                                                   //脱敏后的用户设备ID
	BookId                  string   `p:"bookId" dc:"染色推广链的短剧ID"`                                                                     //染色推广链的短剧ID
	BookName                string   `p:"bookName" dc:"染色推广链的短剧名称"`                                                                   //染色推广链的短剧名称
	BookGender              string   `p:"bookGender" v:"bookGender@integer#染色推广链短剧性别(0女生、1男生、2无性别)需为整数" dc:"染色推广链短剧性别(0女生、1男生、2无性别)"` //染色推广链短剧性别(0女生、1男生、2无性别)
	BookCategory            string   `p:"bookCategory" dc:"染色推广链的短剧类型"`                                                               //染色推广链的短剧类型
	DeptIds                 []int    `p:"deptIds"`                                                                                    //部门id
	DistributorId           int      `p:"distributorId" dc:"分销id"`
	ChannelCode             string   `orm:"channel_code" p:"channelCode" dc:"渠道号"`
	ChannelCodes            []string `orm:"channel_codes" p:"channelCodes" dc:"渠道号"`
	PitcherId               int      `p:"pitcherId" dc:"投手id"`
	FqDistributorId         string   `p:"fqDistributorId" dc:"快应用/公众号对应distributor_id"`                           //快应用/公众号对应distributor_id
	FqDistributorIds        []string `p:"fqDistributorIds" dc:"快应用/公众号对应distributor_id"`                          //快应用/公众号对应distributor_id
	FqChannelDistributorId  string   `p:"fqChannelDistributorId"   dc:"番茄渠道ID"`                                   //渠道Id
	FqChannelDistributorIds []string `p:"fqChannelDistributorIds"   dc:"番茄渠道ID"`                                  //渠道Id
	EcpmNo                  string   `p:"ecpmNo" dc:"激励收益的唯一键，对齐抖开接口的req_id"`                                     //激励收益的唯一键，对齐抖开接口的req_id
	AppId                   string   `p:"appId" dc:"公众号/快应用/小程序id（分销平台id）【v1.2】"`                                 //公众号/快应用/小程序id（分销平台id）【v1.2】
	AppName                 string   `p:"appName" dc:"快应用/公众号/小程序名称【v1.2】"`                                       //快应用/公众号/小程序名称【v1.2】
	PromotionId             string   `p:"promotionId" dc:"推广链id"`                                                 //推广链id
	EcpmCost                string   `p:"ecpmCost" v:"ecpmCost@integer#激励点击金额，单位十万分之一元需为整数" dc:"激励点击金额，单位十万分之一元"` //激励点击金额，单位十万分之一元
	EventTime               string   `p:"eventTime" v:"eventTime@integer#激励点击的时间戳需为整数" dc:"激励点击的时间戳"`             //激励点击的时间戳
	RegisterTime            string   `p:"registerTime" v:"registerTime@integer#用户染色时间戳需为整数" dc:"用户染色时间戳"`         //用户染色时间戳
	CreatedAt               string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间

}

// FqAdUserRewardClickSearchRes 列表返回结果
type FqAdUserRewardClickSearchRes struct {
	comModel.ListRes
	List    []*FqAdUserRewardClickListRes `json:"list"`
	Summary *FqAdUserRewardClickSummary   `json:"summary"`
}

type FqAdUserRewardClickSummary struct {
	EcpmCost  float64 `p:"ecpm_cost" json:"ecpmCost" dc:"激励点击金额，单位十万分之一元"` // 激励点击金额，单位十万分之一元
	EcpmCount int64   `p:"ecpm_count" json:"ecpmCount" dc:"广告次数"`          // 激励点击金额，单位十万分之一元
	UserCount int64   `p:"userCount"  dc:"用户总数"`
}

// FqAdUserRewardClickAddReq 添加操作请求参数
type FqAdUserRewardClickAddReq struct {
	EcpmNo        string `p:"ecpmNo" v:"required#主键ID不能为空" dc:"激励收益的唯一键，对齐抖开接口的req_id"`
	DistributorId string `p:"distributorId" v:"required#快应用/公众号对应distributor_id不能为空" dc:"快应用/公众号对应distributor_id"`
	AppId         string `p:"appId"  dc:"公众号/快应用/小程序id（分销平台id）【v1.2】"`
	AppName       string `p:"appName" v:"required#快应用/公众号/小程序名称【v1.2】不能为空" dc:"快应用/公众号/小程序名称【v1.2】"`
	DeviceId      string `p:"deviceId" v:"required#脱敏后的用户设备ID不能为空" dc:"脱敏后的用户设备ID"`
	PromotionId   string `p:"promotionId" v:"required#推广链id不能为空" dc:"推广链id"`
	EcpmCost      int64  `p:"ecpmCost"  dc:"激励点击金额，单位十万分之一元"`
	EventTime     int64  `p:"eventTime"  dc:"激励点击的时间戳"`
	RegisterTime  int64  `p:"registerTime"  dc:"用户染色时间戳"`
	BookId        string `p:"bookId"  dc:"染色推广链的短剧ID"`
	BookName      string `p:"bookName" v:"required#染色推广链的短剧名称不能为空" dc:"染色推广链的短剧名称"`
	BookGender    int    `p:"bookGender"  dc:"染色推广链短剧性别(0女生、1男生、2无性别)"`
	BookCategory  string `p:"bookCategory"  dc:"染色推广链的短剧类型"`
}

type FqAdUserRewardClickInfoSaveRes struct {
	EcpmNo        string      `p:"ecpm_no,primary" json:"ecpmNo" dc:"激励收益的唯一键，对齐抖开接口的req_id"`        // 激励收益的唯一键，对齐抖开接口的req_id
	DistributorId string      `p:"distributor_id" json:"distributorId" dc:"快应用/公众号对应distributor_id"` // 快应用/公众号对应distributor_id
	AppId         string      `p:"app_id" json:"appId" dc:"公众号/快应用/小程序id（分销平台id）【v1.2】"`             // 公众号/快应用/小程序id（分销平台id）【v1.2】
	AppName       string      `p:"app_name" json:"appName" dc:"快应用/公众号/小程序名称【v1.2】"`                 // 快应用/公众号/小程序名称【v1.2】
	DeviceId      string      `p:"device_id" json:"deviceId" dc:"脱敏后的用户设备ID"`                        // 脱敏后的用户设备ID
	PromotionId   string      `p:"promotion_id" json:"promotionId" dc:"推广链id"`                       // 推广链id
	EcpmCost      int64       `p:"ecpm_cost" json:"ecpmCost" dc:"激励点击金额，单位十万分之一元"`                   // 激励点击金额，单位十万分之一元
	EventTime     int64       `p:"event_time" json:"eventTime" dc:"激励点击的时间戳"`                        // 激励点击的时间戳
	RegisterTime  int64       `p:"register_time" json:"registerTime" dc:"用户染色时间戳"`                   // 用户染色时间戳
	BookId        string      `p:"book_id" json:"bookId" dc:"染色推广链的短剧ID"`                            // 染色推广链的短剧ID
	BookName      string      `p:"book_name" json:"bookName" dc:"染色推广链的短剧名称"`                        // 染色推广链的短剧名称
	BookGender    int         `p:"book_gender" json:"bookGender" dc:"染色推广链短剧性别(0女生、1男生、2无性别)"`       // 染色推广链短剧性别(0女生、1男生、2无性别)
	BookCategory  string      `p:"book_category" json:"bookCategory" dc:"染色推广链的短剧类型"`                // 染色推广链的短剧类型
	CreatedAt     *gtime.Time `p:"created_at" json:"createdAt" dc:"创建时间"`                            // 创建时间
}

// FqAdUserRewardClickEditReq 修改操作请求参数
type FqAdUserRewardClickEditReq struct {
	EcpmNo        string `p:"ecpmNo" v:"required#主键ID不能为空" dc:"激励收益的唯一键，对齐抖开接口的req_id"`
	DistributorId string `p:"distributorId" v:"required#快应用/公众号对应distributor_id不能为空" dc:"快应用/公众号对应distributor_id"`
	AppId         string `p:"appId"  dc:"公众号/快应用/小程序id（分销平台id）【v1.2】"`
	AppName       string `p:"appName" v:"required#快应用/公众号/小程序名称【v1.2】不能为空" dc:"快应用/公众号/小程序名称【v1.2】"`
	DeviceId      string `p:"deviceId" v:"required#脱敏后的用户设备ID不能为空" dc:"脱敏后的用户设备ID"`
	PromotionId   string `p:"promotionId" v:"required#推广链id不能为空" dc:"推广链id"`
	EcpmCost      int64  `p:"ecpmCost"  dc:"激励点击金额，单位十万分之一元"`
	EventTime     int64  `p:"eventTime"  dc:"激励点击的时间戳"`
	RegisterTime  int64  `p:"registerTime"  dc:"用户染色时间戳"`
	BookId        string `p:"bookId"  dc:"染色推广链的短剧ID"`
	BookName      string `p:"bookName" v:"required#染色推广链的短剧名称不能为空" dc:"染色推广链的短剧名称"`
	BookGender    int    `p:"bookGender"  dc:"染色推广链短剧性别(0女生、1男生、2无性别)"`
	BookCategory  string `p:"bookCategory"  dc:"染色推广链的短剧类型"`
}
