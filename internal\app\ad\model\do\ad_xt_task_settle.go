// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: internal/app/ad/model/entity/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdXtTaskSettle is the golang structure for table ad_xt_task_settle.
type AdXtTaskSettle struct {
	gmeta.Meta           `orm:"table:ad_xt_task_settle, do:true"`
	Id                   interface{} `orm:"id,primary" json:"id"`                               // ID
	AdvertiserId         interface{} `orm:"advertiser_id" json:"advertiserId"`                  // 账户ID 对应 star_id
	TaskId               interface{} `orm:"task_id" json:"taskId"`                              // 任务ID
	ItemId               interface{} `orm:"item_id" json:"itemId"`                              // 视频唯一ID
	AuthorId             interface{} `orm:"author_id" json:"authorId"`                          // 作者ID
	Uid                  interface{} `orm:"uid" json:"uid"`                                     // 用户平台ID
	ProviderId           interface{} `orm:"provider_id" json:"providerId"`                      // 内容提供方ID
	Title                interface{} `orm:"title" json:"title"`                                 // 视频标题
	Link                 interface{} `orm:"link" json:"link"`                                   // 视频分享链接
	AuthorNickname       interface{} `orm:"author_nickname" json:"authorNickname"`              // 作者昵称
	ReleaseTime          interface{} `orm:"release_time" json:"releaseTime"`                    // 发布时间(unix时间戳)
	AndroidActivateCnt   interface{} `orm:"android_activate_cnt" json:"androidActivateCnt"`     // 安卓端激活数
	IosActivateCnt       interface{} `orm:"ios_activate_cnt" json:"iosActivateCnt"`             // iOS端激活数
	CommentCnt           interface{} `orm:"comment_cnt" json:"commentCnt"`                      // 评论数
	LikeCnt              interface{} `orm:"like_cnt" json:"likeCnt"`                            // 点赞数
	ValidLikeCnt         interface{} `orm:"valid_like_cnt" json:"validLikeCnt"`                 // 有效点赞数
	PlayVv               interface{} `orm:"play_vv" json:"playVv"`                              // 播放次数
	ValidPlayVv          interface{} `orm:"valid_play_vv" json:"validPlayVv"`                   // 有效播放次数
	ShareCnt             interface{} `orm:"share_cnt" json:"shareCnt"`                          // 分享次数
	PromoteCnt           interface{} `orm:"promote_cnt" json:"promoteCnt"`                      // 推广次数
	ComponentClickCnt    interface{} `orm:"component_click_cnt" json:"componentClickCnt"`       // 组件点击次数
	EstAdCost            interface{} `orm:"est_ad_cost" json:"estAdCost"`                       // 预估广告收入(JSON格式)
	EstSales             interface{} `orm:"est_sales" json:"estSales"`                          // 预估销售额(JSON格式)
	Play                 interface{} `orm:"play" json:"play"`                                   // 播放数据(JSON格式)
	SettleAdShare        interface{} `orm:"settle_ad_share" json:"settleAdShare"`               // 广告分成结算(JSON格式)
	SettleCps            interface{} `orm:"settle_cps" json:"settleCps"`                        // CPS结算(JSON格式)
	IaaCostHour          interface{} `orm:"iaa_cost_hour" json:"iaaCostHour"`                   // 小时级IAA消耗
	IapCostHour          interface{} `orm:"iap_cost_hour" json:"iapCostHour"`                   // 小时级IAP消耗
	RewardAmount         interface{} `orm:"reward_amount" json:"rewardAmount"`                  // 打赏金额
	RelevanceAuditResult interface{} `orm:"relevance_audit_result" json:"relevanceAuditResult"` // 相关性审核结果
	RewardLevel          interface{} `orm:"reward_level" json:"rewardLevel"`                    // 打赏等级
	ItemInfoDailyList    interface{} `orm:"item_info_daily_list" json:"itemInfoDailyList"`      // 每日数据明细(JSON数组)
}
