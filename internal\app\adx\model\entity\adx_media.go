// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: internal/app/adx/model/entity/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxMedia is the golang structure for table adx_media.
type AdxMedia struct {
	gmeta.Meta `orm:"table:adx_media"`
	Id         uint   `orm:"id,primary" json:"id"`        // 媒体ID
	LogoUrl    string `orm:"logo_url" json:"logoUrl"`     // 媒体logo
	MediaName  string `orm:"media_name" json:"mediaName"` // 媒体名称
}
