// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/controller/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type dzAdEcpmController struct {
	systemController.BaseController
}

var DzAdEcpm = new(dzAdEcpmController)

// List 列表
func (c *dzAdEcpmController) List(ctx context.Context, req *ad.DzAdEcpmSearchReq) (res *ad.DzAdEcpmSearchRes, err error) {
	res = new(ad.DzAdEcpmSearchRes)
	res.DzAdEcpmSearchRes, err = service.DzAdEcpm().List(ctx, &req.DzAdEcpmSearchReq)
	return
}

// Export 导出excel
func (c *dzAdEcpmController) Export(ctx context.Context, req *ad.DzAdEcpmExportReq) (res *ad.DzAdEcpmExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.DzAdEcpmListRes
		listRes  *model.DzAdEcpmSearchRes
		//表头
		tableHead = []interface{}{"日期", "点众账号", "点众渠道", "分销", "投手", "用户ID", "用户注册时间", "用户染色时间", "广告ID", "广告类型", "收入金额", "渠道ID", "渠道号"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.DzAdEcpm().List(ctx, &req.DzAdEcpmSearchReq)
		if err != nil {
			return
		}
		if listRes == nil || len(listRes.List) == 0 {
			return
		}
		listData = listRes.List
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.CreateDate,
				v.DzAccount,
				v.DzChannel,
				v.DistributorName,
				v.UserName,
				v.UserId,
				v.RegisterTime,
				v.DyeTime,
				v.PromotionId,
				v.AdType,
				v.SumCost,
				v.ChannelId,
				v.ChannelCode,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("广告ECPM信息表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取广告ECPM信息表
func (c *dzAdEcpmController) Get(ctx context.Context, req *ad.DzAdEcpmGetReq) (res *ad.DzAdEcpmGetRes, err error) {
	res = new(ad.DzAdEcpmGetRes)
	res.DzAdEcpmInfoRes, err = service.DzAdEcpm().GetById(ctx, req.Id)
	return
}

// Add 添加广告ECPM信息表
func (c *dzAdEcpmController) Add(ctx context.Context, req *ad.DzAdEcpmAddReq) (res *ad.DzAdEcpmAddRes, err error) {
	err = service.DzAdEcpm().Add(ctx, req.DzAdEcpmAddReq)
	return
}

// Edit 修改广告ECPM信息表
func (c *dzAdEcpmController) Edit(ctx context.Context, req *ad.DzAdEcpmEditReq) (res *ad.DzAdEcpmEditRes, err error) {
	err = service.DzAdEcpm().Edit(ctx, req.DzAdEcpmEditReq)
	return
}

// Delete 删除广告ECPM信息表
func (c *dzAdEcpmController) Delete(ctx context.Context, req *ad.DzAdEcpmDeleteReq) (res *ad.DzAdEcpmDeleteRes, err error) {
	err = service.DzAdEcpm().Delete(ctx, req.Ids)
	return
}
