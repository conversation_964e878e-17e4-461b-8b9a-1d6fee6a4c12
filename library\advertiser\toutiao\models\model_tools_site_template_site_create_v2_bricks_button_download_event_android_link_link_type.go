/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType
type ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType string

// List of tools_site_template_site_create_v2_bricks_button_download_event_android_link_link_type
const (
	QUICK_APP_ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType = "QUICK_APP"
	SCHEME_ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType    ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType = "SCHEME"
	URL_ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType       ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType = "URL"
)

// Ptr returns reference to tools_site_template_site_create_v2_bricks_button_download_event_android_link_link_type value
func (v ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType) Ptr() *ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventAndroidLinkLinkType {
	return &v
}
