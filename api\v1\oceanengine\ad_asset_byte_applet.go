// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-13 16:08:04
// 生成路径: api/v1/oceanengine/ad_asset_byte_applet.go
// 生成人：cq
// desc:资产-字节小程序相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetByteAppletSearchReq 分页请求参数
type AdAssetByteAppletSearchReq struct {
	g.Meta `path:"/list" tags:"资产-字节小程序" method:"post" summary:"资产-字节小程序列表"`
	commonApi.Author
	model.AdAssetByteAppletSearchReq
}

// AdAssetByteAppletSearchRes 列表返回结果
type AdAssetByteAppletSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetByteAppletSearchRes
}

// AdAssetByteAppletAddReq 添加操作请求参数
type AdAssetByteAppletAddReq struct {
	g.Meta `path:"/add" tags:"资产-字节小程序" method:"post" summary:"资产-字节小程序添加"`
	commonApi.Author
	File       *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
	CategoryId string            `p:"categoryId" v:"required#分类id必须"`
}

// AdAssetByteAppletAddRes 添加操作返回结果
type AdAssetByteAppletAddRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletSyncReq 同步字节小程序请求参数
type AdAssetByteAppletSyncReq struct {
	g.Meta `path:"/sync" tags:"资产-字节小程序" method:"post" summary:"资产-同步字节小程序"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds" dc:"广告主ID列表"`
}

// AdAssetByteAppletSyncRes 同步微信小程序返回结果
type AdAssetByteAppletSyncRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletChannelImportReq 渠道导入请求参数
type AdAssetByteAppletChannelImportReq struct {
	g.Meta `path:"/channel/import" tags:"资产-字节小程序" method:"post" summary:"资产-渠道导入"`
	commonApi.Author
	*model.AdAssetChannelImportReq
}

// AdAssetByteAppletChannelImportRes 渠道导入返回结果
type AdAssetByteAppletChannelImportRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletEditReq 修改操作请求参数
type AdAssetByteAppletEditReq struct {
	g.Meta `path:"/edit" tags:"资产-字节小程序" method:"put" summary:"资产-字节小程序修改"`
	commonApi.Author
	*model.AdAssetByteAppletEditReq
}

// AdAssetByteAppletEditRes 修改操作返回结果
type AdAssetByteAppletEditRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletGetReq 获取一条数据请求
type AdAssetByteAppletGetReq struct {
	g.Meta `path:"/get" tags:"资产-字节小程序" method:"get" summary:"获取资产-字节小程序信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetByteAppletGetRes 获取一条数据结果
type AdAssetByteAppletGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetByteAppletInfoRes
}

// AdAssetByteAppletDeleteReq 删除数据请求
type AdAssetByteAppletDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-字节小程序" method:"post" summary:"删除资产-字节小程序"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetByteAppletDeleteRes 删除数据返回
type AdAssetByteAppletDeleteRes struct {
	commonApi.EmptyRes
}
