// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/model/entity/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAnchorPointUpload is the golang structure for table ad_anchor_point_upload.
type AdAnchorPointUpload struct {
	gmeta.Meta    `orm:"table:ad_anchor_point_upload, do:true"`
	Id            interface{} `orm:"id,primary" json:"id"`                 // ID
	AnchorType    interface{} `orm:"anchor_type" json:"anchorType"`        // 可选值:
	AnchorPointId interface{} `orm:"anchor_point_id" json:"anchorPointId"` // 业务的字段锚点id
	AnchorId      interface{} `orm:"anchor_id" json:"anchorId"`            // 原生锚点id
	CreateTime    *gtime.Time `orm:"create_time" json:"createTime"`        // 创建时间
}
