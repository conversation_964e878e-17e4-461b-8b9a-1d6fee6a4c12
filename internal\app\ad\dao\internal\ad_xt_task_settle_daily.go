// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-21 14:30:52
// 生成路径: internal/app/ad/dao/internal/ad_xt_task_settle_daily.go
// 生成人：cyao
// desc:星图结算数据分天
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdXtTaskSettleDailyDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdXtTaskSettleDailyDao struct {
	table   string                     // Table is the underlying table name of the DAO.
	group   string                     // Group is the database configuration group name of current DAO.
	columns AdXtTaskSettleDailyColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdXtTaskSettleDailyColumns defines and stores column names for table ad_xt_task_settle_daily.
type AdXtTaskSettleDailyColumns struct {
	Id            string // ID
	AdvertiserId  string // 账户ID对应star_id
	TaskId        string // 任务ID
	ItemId        string // 视频唯一ID
	AuthorId      string // 作者ID
	Uid           string // 用户平台ID
	ProviderId    string // 内容提供方ID
	PDate         string // 日期
	EstSales      string // 当天产生的预估付费流水金额
	SettleCps     string // 当天发放的达人付费分佣金额
	EstAdCost     string // 当天产生的预估广告消耗金额
	SettleAdShare string // 当天发放的达人广告分成金额
	CreatedAt     string // 创建时间
}

var adXtTaskSettleDailyColumns = AdXtTaskSettleDailyColumns{
	Id:            "id",
	AdvertiserId:  "advertiser_id",
	TaskId:        "task_id",
	ItemId:        "item_id",
	AuthorId:      "author_id",
	Uid:           "uid",
	ProviderId:    "provider_id",
	PDate:         "p_date",
	EstSales:      "est_sales",
	SettleCps:     "settle_cps",
	EstAdCost:     "est_ad_cost",
	SettleAdShare: "settle_ad_share",
	CreatedAt:     "created_at",
}

// NewAdXtTaskSettleDailyDao creates and returns a new DAO object for table data access.
func NewAdXtTaskSettleDailyDao() *AdXtTaskSettleDailyDao {
	return &AdXtTaskSettleDailyDao{
		group:   "default",
		table:   "ad_xt_task_settle_daily",
		columns: adXtTaskSettleDailyColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdXtTaskSettleDailyDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdXtTaskSettleDailyDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdXtTaskSettleDailyDao) Columns() AdXtTaskSettleDailyColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdXtTaskSettleDailyDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdXtTaskSettleDailyDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdXtTaskSettleDailyDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
