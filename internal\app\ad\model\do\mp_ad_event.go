// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: internal/app/ad/model/entity/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// MpAdEvent is the golang structure for table mp_ad_event.
type MpAdEvent struct {
	gmeta.Meta `orm:"table:mp_ad_event, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`          // 记录唯一标识
	MpId       interface{} `orm:"mp_id" json:"mpId"`             // 小程序ID
	Cost       interface{} `orm:"cost" json:"cost"`              // 广告消耗，单位为十万分之一元
	OpenId     interface{} `orm:"open_id" json:"openId"`         // 用户OpenID
	EventTime  interface{} `orm:"event_time" json:"eventTime"`   // 广告计费发生时间戳，单位秒
	AdType     interface{} `orm:"ad_type" json:"adType"`         // 小程序广告类型
	CreateDate interface{} `orm:"create_date" json:"createDate"` // 创建日期，按照event_time来
	CreateTime *gtime.Time `orm:"create_time" json:"createTime"` // 根据event_time来的时间
}
