// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: internal/app/ad/dao/internal/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdAccountChannelDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdAccountChannelDao struct {
	table   string                    // Table is the underlying table name of the DAO.
	group   string                    // Group is the database configuration group name of current DAO.
	columns DzAdAccountChannelColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdAccountChannelColumns defines and stores column names for table dz_ad_account_channel.
type DzAdAccountChannelColumns struct {
	ChannelId string // 渠道ID
	AccountId string // 主账号ID
	NickName  string // 渠道昵称
	UserName  string // 渠道用户名称
	JumpType  string // 1单端，2双端
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 删除时间
}

var dzAdAccountChannelColumns = DzAdAccountChannelColumns{
	ChannelId: "channel_id",
	AccountId: "account_id",
	NickName:  "nick_name",
	UserName:  "user_name",
	JumpType:  "jump_type",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewDzAdAccountChannelDao creates and returns a new DAO object for table data access.
func NewDzAdAccountChannelDao() *DzAdAccountChannelDao {
	return &DzAdAccountChannelDao{
		group:   "default",
		table:   "dz_ad_account_channel",
		columns: dzAdAccountChannelColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdAccountChannelDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdAccountChannelDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdAccountChannelDao) Columns() DzAdAccountChannelColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdAccountChannelDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdAccountChannelDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdAccountChannelDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
