// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: internal/app/ad/logic/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	fqApi "github.com/tiger1103/gfast/v3/library/advertiser/fq/api"
	fqModel "github.com/tiger1103/gfast/v3/library/advertiser/fq/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdAccountChannel(New())
}

func New() service.IFqAdAccountChannel {
	return &sFqAdAccountChannel{}
}

type sFqAdAccountChannel struct{}

func (s *sFqAdAccountChannel) List(ctx context.Context, req *model.FqAdAccountChannelSearchReq) (listRes *model.FqAdAccountChannelSearchRes, err error) {
	listRes = new(model.FqAdAccountChannelSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.FqAdAccountChannel.Ctx(ctx).WithAll().As("a")
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " a.channel_distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " a.channel_distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if req.ChannelDistributorId != "" {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().ChannelDistributorId+" = ?", req.ChannelDistributorId)
		}
		if req.DistributorId != "" {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().DistributorId+" = ?", gconv.Int64(req.DistributorId))
		}
		if req.NickName != "" {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().NickName+" like ?", "%"+req.NickName+"%")
		}
		if req.AppId != "" {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().AppId+" = ?", req.AppId)
		}
		if req.AppName != "" {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().AppName+" like ?", "%"+req.AppName+"%")
		}
		if req.AppType > 0 {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().AppType+" = ?", req.AppType)
		}
		if len(req.DateRange) != 0 {
			m = m.Where("a."+dao.FqAdAccountChannel.Columns().CreatedAt+" >=? AND "+"a."+dao.FqAdAccountChannel.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "channel_distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.FqAdAccountChannelListRes
		err = m.Fields("DISTINCT a.*").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdAccountChannelListRes, len(res))
		disIds := make([]int64, 0)
		for k, v := range res {
			disIds = append(disIds, v.ChannelDistributorId)
			listRes.List[k] = &model.FqAdAccountChannelListRes{
				ChannelDistributorId: v.ChannelDistributorId,
				DistributorId:        v.DistributorId,
				NickName:             v.NickName,
				AppId:                v.AppId,
				AppName:              v.AppName,
				AppType:              v.AppType,
				CreatedAt:            v.CreatedAt,
			}
		}
		uList, _ := service.FqAdAccountUsers().List(ctx, &model.FqAdAccountUsersSearchReq{
			DistributorIds: disIds,
			PageReq: comModel.PageReq{
				PageNum:  req.PageNum,
				PageSize: consts.MaxPageSize,
			},
		})
		if uList != nil && len(uList.List) > 0 {
			for _, usersListRes := range uList.List {
				for _, accountListRes := range listRes.List {
					if usersListRes.DistributorId == accountListRes.ChannelDistributorId {
						if accountListRes.UserList == nil {
							accountListRes.UserList = make([]int64, 0)
						}
						accountListRes.UserList = append(accountListRes.UserList, gconv.Int64(usersListRes.SpecifyUserId))
					}
				}
			}
		}
		deptList, _ := service.FqAdAccountDepts().List(ctx, &model.FqAdAccountDeptsSearchReq{
			DistributorIds: disIds,
			PageReq: comModel.PageReq{
				PageNum:  req.PageNum,
				PageSize: consts.MaxPageSize,
			},
		})
		if uList != nil && len(deptList.List) > 0 {
			for _, usersListRes := range deptList.List {
				for _, accountListRes := range listRes.List {
					if usersListRes.DistributorId == accountListRes.ChannelDistributorId {
						if accountListRes.DeptList == nil {
							accountListRes.DeptList = make([]int64, 0)
						}
						accountListRes.DeptList = append(accountListRes.DeptList, gconv.Int64(usersListRes.DetpId))
					}
				}
			}
		}
	})
	return
}

// FqChannelStatic
func (s *sFqAdAccountChannel) FqChannelStatic(ctx context.Context, req *model.FqChannelStaticReq) (listRes *model.FqChannelStaticRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		listRes = new(model.FqChannelStaticRes)
		list := make([]*model.FqChannelStaticListRes, 0)
		summary := new(model.FqChannelStaticListRes)
		// 获取用户信息
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := channelDao.SChannelRechargeStatisticsAnalytic.Ctx(ctx).As("cs").LeftJoin(channelDao.SChannel.Table(), "c", " cs.account = c.channel_code")
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " c.fq_distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " c.fq_distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}
		if len(req.StartTime) > 0 {
			m = m.Where("cs."+channelDao.SChannelRechargeStatisticsAnalytic.Columns().CreateTime+" >= ? ", req.StartTime)
		}
		if len(req.EndTime) > 0 {
			m = m.Where("cs."+channelDao.SChannelRechargeStatisticsAnalytic.Columns().CreateTime+" <= ? ", req.EndTime)
		}
		if req.FqDistributorId != "" {
			list, _ := service.FqAdAccountChannel().GetByDistributorId(ctx, gconv.Int64(req.FqDistributorId))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("c.fq_distributor_id", ids)
				}
			}
		}
		if len(req.FqDistributorIds) > 0 {
			list, _ := service.FqAdAccountChannel().GetByDistributorIds(ctx, gconv.SliceInt64(req.FqChannelDistributorIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("c.fq_distributor_id", ids)
				}
			}
		}
		if len(req.FqChannelDistributorId) > 0 {
			m = m.Where("c.fq_distributor_id"+" = ?", gconv.Int64(req.FqChannelDistributorId))
		}
		if len(req.FqChannelDistributorIds) > 0 {
			m = m.WhereIn("c.fq_distributor_id", req.FqChannelDistributorIds)
		}
		m = m.Where("fq_distributor_id is not Null")
		listRes.Total, err = m.Count()
		m.FieldSum("cs.new_user_amount", "newUserAmount").
			FieldSum("cs.new_user_recharge_nums ", "newUserRechargeNums").
			FieldSum("cs.account_coin_consume ", "accountCoinConsume").
			FieldSum("cs.total_amount ", "totalAmount").
			FieldSum("cs.total_ad_up ", "totalAdUp").
			FieldSum("cs.new_user_ad_up ", "newUserAdUp").
			FieldSum("cs.wechat_android_recharge_amount ", "wechatAndroidRecharge").
			FieldSum("cs.wechat_ios_recharge_amount ", "wechatIosRecharge").
			FieldSum("cs.dy_android_recharge_amount ", "dyAndroidRecharge").
			FieldSum("cs.dy_ios_recharge_amount ", "dyIosRecharge").
			FieldSum("cs.dy_recharge_amount ", "dyRechargeAmount").
			FieldSum("cs.recharge_nums ", "rechargeNums").
			FieldSum("cs.total_recharge_times ", "totalRechargeTimes").
			FieldSum("cs.customer_price ", "customerPrice").Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 计算summary
		// 计算微信总金额
		summary.WechatRechargeAmount = summary.WechatAndroidRecharge + summary.WechatIosRecharge
		// 计算抖音总金额
		summary.DyRechargeAmount = summary.DyAndroidRecharge + summary.DyIosRecharge
		// 计算ios 总金额
		summary.RechargeIosAmount = summary.WechatIosRecharge + summary.DyIosRecharge
		// 计算安卓 总金额
		summary.RechargeAndroidAmount = summary.WechatAndroidRecharge + summary.DyAndroidRecharge

		summary.DayRoi = libUtils.DivideAndRound(summary.TotalAdUp+summary.TotalAmount, summary.AccountCoinConsume, 2, libUtils.RoundHalfEven)

		summary.CustomerPrice = libUtils.ToRound(libUtils.DivideAndRound(summary.TotalAmount, float64(summary.RechargeNums), 2, libUtils.RoundHalfEven), 2, libUtils.RoundHalfEven)
		summary.AvgRechargeTimes = libUtils.ToRound(libUtils.DivideAndRound(float64(summary.TotalRechargeTimes), float64(summary.RechargeNums), 2, libUtils.RoundHalfEven), 2, libUtils.RoundHalfEven)
		listRes.Summary = summary

		orderBy := "  cs.id desc"
		if req.OrderBy != "" {
			if req.OrderBy == "wechatRechargeAmount" {
				req.OrderBy = " cs.wechat_android_recharge_amount + cs.wechat_ios_recharge_amount"
			}
			orderBy = "cs." + libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
			//orderBy = "cs." + req.OrderBy + " " + req.OrderType
		}
		err = m.Fields("fq_distributor_id as fqDistributorId").Fields("cs.new_user_amount as newUserAmount").
			Fields("cs.new_user_recharge_nums as newUserRechargeNums").
			Fields("cs.account_coin_consume as accountCoinConsume").
			Fields("cs.total_amount as totalAmount").
			Fields("cs.total_ad_up as totalAdUp").
			Fields("cs.new_user_ad_up as newUserAdUp").
			Fields("cs.day_roi as dayRoi").
			Fields("cs.wechat_android_recharge_amount as wechatAndroidRecharge").
			Fields("cs.wechat_ios_recharge_amount as wechatIosRecharge").
			Fields("cs.wechat_android_recharge_amount+cs.wechat_ios_recharge_amount as wechatRechargeAmount").
			Fields("cs.dy_android_recharge_amount as dyAndroidRecharge").
			Fields("cs.dy_ios_recharge_amount as dyIosRecharge").
			Fields("cs.dy_recharge_amount as dyRechargeAmount").
			Fields("cs.recharge_nums as rechargeNums").
			Fields("cs.customer_price as customerPrice").
			Fields("cs.create_time as createTime").
			Fields("cs.avg_recharge_times as avgRechargeTimes").Order(orderBy).Scan(&list)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		disIds := make([]int64, 0)
		for _, item := range list {
			disIds = append(disIds, gconv.Int64(item.FqDistributorId))
		}
		listRes.List = list
		channelList, _ := service.FqAdAccountChannel().GetByChannelDistributorIds(ctx, disIds)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		for _, dataListRes := range listRes.List {
			for _, infoRes := range channelList {
				if gconv.Int64(dataListRes.FqDistributorId) == infoRes.DistributorId {
					dataListRes.NickName = infoRes.NickName
				}
			}
			// 计算微信总金额
			dataListRes.WechatRechargeAmount = dataListRes.WechatAndroidRecharge + dataListRes.WechatIosRecharge
			// 计算抖音总金额
			dataListRes.DyRechargeAmount = dataListRes.DyAndroidRecharge + dataListRes.DyIosRecharge
			// 计算ios 总金额
			dataListRes.RechargeIosAmount = dataListRes.WechatIosRecharge + dataListRes.DyIosRecharge
			// 计算安卓 总金额
			dataListRes.RechargeAndroidAmount = dataListRes.WechatAndroidRecharge + dataListRes.DyAndroidRecharge
			dataListRes.WechatRechargeAmount = libUtils.ToRound(dataListRes.WechatRechargeAmount, 2, libUtils.RoundUp)
			dataListRes.DyRechargeAmount = libUtils.ToRound(dataListRes.DyRechargeAmount, 2, libUtils.RoundUp)

		}

	})
	return
}

// GetChannelList 获取渠道对应的密钥
func (s *sFqAdAccountChannel) GetChannelList(ctx context.Context, req *model.FqAdAccountChannelSearchReq) (listRes []*model.FqAdAccountChannelRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccountChannel.Ctx(ctx).WithAll().As("c").
			LeftJoin("fq_ad_account a", "c.distributor_id = a.distributor_id").
			Fields("c.channel_distributor_id as channelDistributorId").
			Fields("c.distributor_id as distributorId").
			Fields("a.secret_key as secretKey").
			Page(req.PageNum, req.PageSize).
			Order("c.channel_distributor_id asc").Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

// GetSecretByChannelDistributorId 获取渠道对应的密钥
func (s *sFqAdAccountChannel) GetSecretByChannelDistributorId(ctx context.Context, channelDistributorId int64) (res *model.FqAdAccountChannelRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccountChannel.Ctx(ctx).WithAll().As("c").
			Where("c.channel_distributor_id", channelDistributorId).
			LeftJoin("fq_ad_account a", "c.distributor_id = a.distributor_id").
			Fields("c.channel_distributor_id as channelDistributorId").
			Fields("c.distributor_id as distributorId").
			Fields("a.secret_key as secretKey").
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdAccountChannel) GetByChannelDistributorId(ctx context.Context, channelDistributorId int64) (res *model.FqAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccountChannel.Ctx(ctx).WithAll().Where(dao.FqAdAccountChannel.Columns().ChannelDistributorId, channelDistributorId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccountChannel) GetByChannelDistributorIds(ctx context.Context, channelDistributorIds []int64) (res []*model.FqAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.FqAdAccountChannelInfoRes, 0)
		if len(channelDistributorIds) == 0 {
			return
		}
		err = dao.FqAdAccountChannel.Ctx(ctx).WithAll().WhereIn(dao.FqAdAccountChannel.Columns().ChannelDistributorId, channelDistributorIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccountChannel) GetByDistributorId(ctx context.Context, distributorId int64) (res []*model.FqAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.FqAdAccountChannelInfoRes, 0)
		err = dao.FqAdAccountChannel.Ctx(ctx).WithAll().Where(dao.FqAdAccountChannel.Columns().DistributorId, distributorId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccountChannel) GetByDistributorIds(ctx context.Context, distributorIds []int64) (res []*model.FqAdAccountChannelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make([]*model.FqAdAccountChannelInfoRes, 0)
		err = dao.FqAdAccountChannel.Ctx(ctx).WithAll().WhereIn(dao.FqAdAccountChannel.Columns().DistributorId, distributorIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccountChannel) Add(ctx context.Context, req *model.FqAdAccountChannelAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountChannel.Ctx(ctx).Insert(do.FqAdAccountChannel{
			ChannelDistributorId: req.ChannelDistributorId,
			DistributorId:        req.DistributorId,
			NickName:             req.NickName,
			AppType:              req.AppType,
			AppId:                req.AppId,
			AppName:              req.AppName,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdAccountChannel) BatchAdd(ctx context.Context, batchReq []*model.FqAdAccountChannelAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.FqAdAccountChannel, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.FqAdAccountChannel{
				ChannelDistributorId: v.ChannelDistributorId,
				DistributorId:        v.DistributorId,
				NickName:             v.NickName,
				AppId:                v.AppId,
				AppType:              v.AppType,
				AppName:              v.AppName,
			}
		}
		_, err = dao.FqAdAccountChannel.Ctx(ctx).Save(data)
	})
	return
}

func (s *sFqAdAccountChannel) Edit(ctx context.Context, req *model.FqAdAccountChannelEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountChannel.Ctx(ctx).WherePri(req.ChannelDistributorId).Update(do.FqAdAccountChannel{
			DistributorId: req.DistributorId,
			NickName:      req.NickName,
			AppId:         req.AppId,
			AppName:       req.AppName,
			AppType:       req.AppType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdAccountChannel) Delete(ctx context.Context, channelDistributorIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountChannel.Ctx(ctx).Delete(dao.FqAdAccountChannel.Columns().ChannelDistributorId+" in (?)", channelDistributorIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// SyncFqAdAccountChannel 同步番茄渠道
func (s *sFqAdAccountChannel) SyncFqAdAccountChannel(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 100
		for {
			fqAdAccounts, _ := service.FqAdAccount().GetList(ctx, &model.FqAdAccountSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})
			if len(fqAdAccounts) == 0 {
				break
			}
			for _, fqAdAccount := range fqAdAccounts {
				appTypes := []fqModel.AppType{
					fqModel.AppTypeWxPayShortVideo,
					fqModel.AppTypeWxIAAShortVideo,
					fqModel.AppTypeDyPayShortVideo,
					fqModel.AppTypeDyIAAShortVideo,
				}
				for _, appType := range appTypes {
					// 获取小程序列表
					packageList := s.GetAllPackageList(ctx, fqAdAccount.SecretKey, fqAdAccount.DistributorId, appType)
					for _, packageInfo := range packageList {
						// 查询渠道列表
						boundPackageList := s.GetAllBoundPackageList(ctx, fqAdAccount.SecretKey, fqAdAccount.DistributorId, packageInfo.AppId)
						if len(boundPackageList) == 0 {
							continue
						}
						batchAddReq := make([]*model.FqAdAccountChannelAddReq, 0)
						for _, boundPackage := range boundPackageList {
							batchAddReq = append(batchAddReq, &model.FqAdAccountChannelAddReq{
								ChannelDistributorId: boundPackage.DistributorId,
								DistributorId:        fqAdAccount.DistributorId,
								NickName:             boundPackage.NickName,
								AppId:                boundPackage.AppId,
								AppName:              boundPackage.AppName,
								AppType:              gconv.Int(boundPackage.AppType),
							})
						}
						err = s.BatchAdd(ctx, batchAddReq)
						if err != nil {
							g.Log().Error(ctx, err)
						}
					}
				}
			}
			pageNo++
		}

	})
	return
}

func (s *sFqAdAccountChannel) GetAllPackageList(ctx context.Context, secretKey string, distributorId int64, appType fqModel.AppType) (packageInfoOpenList []*fqModel.PackageInfoOpen) {
	var pageIndex = 0
	var pageSize = 50
	for {
		packageListRes, err1 := fqApi.GetAdFQIClient().GetPackageListV2ApiService.
			SetSecert(secretKey).
			Request(fqModel.GetPackageListReq{
				FqCommonReq: &fqModel.FqCommonReq{
					DistributorId: gconv.Int64(distributorId),
					PageIndex:     &pageIndex,
					PageSize:      &pageSize,
				},
				AppType: appType,
			}).Do()
		if err1 != nil {
			g.Log().Error(ctx, err1)
			break
		}
		if len(packageListRes.PackageInfoOpenList) == 0 {
			break
		}
		packageInfoOpenList = append(packageInfoOpenList, packageListRes.PackageInfoOpenList...)
		if packageListRes.Total <= int64((pageIndex+1)*pageSize) {
			break
		}
		pageIndex++
	}
	return
}

func (s *sFqAdAccountChannel) GetAllBoundPackageList(ctx context.Context, secretKey string, distributorId int64, appId int64) (wxPackageDataOpenList []*fqModel.WxPackageDataOpen) {
	var pageIndex = 0
	var pageSize = 50
	for {
		boundPackageListRes, err1 := fqApi.GetAdFQIClient().GetBoundPackageListV1ApiService.
			SetSecert(secretKey).
			Request(fqModel.GetBoundPackageListReq{
				FqCommonReq: &fqModel.FqCommonReq{
					DistributorId: gconv.Int64(distributorId),
					PageIndex:     &pageIndex,
					PageSize:      &pageSize,
				},
				AppId: appId,
			}).Do()
		if err1 != nil {
			g.Log().Error(ctx, err1)
			break
		}
		if len(boundPackageListRes.WxPackageDataOpenList) == 0 {
			break
		}
		wxPackageDataOpenList = append(wxPackageDataOpenList, boundPackageListRes.WxPackageDataOpenList...)
		if boundPackageListRes.Total <= int64((pageIndex+1)*pageSize) {
			break
		}
		pageIndex++
	}
	return
}
