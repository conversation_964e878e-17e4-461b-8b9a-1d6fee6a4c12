/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType
type ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType string

// List of tools_site_template_site_create_v2_bricks_picture_group_content_link_dto_link_type
const (
	QUICK_APP_ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType = "QUICK_APP"
	SCHEME_ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType    ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType = "SCHEME"
	URL_ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType       ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType = "URL"
)

// Ptr returns reference to tools_site_template_site_create_v2_bricks_picture_group_content_link_dto_link_type value
func (v ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType) Ptr() *ToolsSiteTemplateSiteCreateV2BricksPictureGroupContentLinkDtoLinkType {
	return &v
}
