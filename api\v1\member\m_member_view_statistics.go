// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-01 17:33:36
// 生成路径: api/v1/member/m_member_view_statistics.go
// 生成人：cq
// desc:用户看剧次数统计表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberViewStatisticsSearchReq 分页请求参数
type MMemberViewStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"用户看剧次数统计表" method:"get" summary:"用户看剧次数统计表列表"`
	commonApi.Author
	model.MMemberViewStatisticsSearchReq
}

// MMemberViewStatisticsSearchRes 列表返回结果
type MMemberViewStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberViewStatisticsSearchRes
}

// MMemberViewStatisticsAddReq 添加操作请求参数
type MMemberViewStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"用户看剧次数统计表" method:"post" summary:"用户看剧次数统计表添加"`
	commonApi.Author
	*model.MMemberViewStatisticsAddReq
}

// MMemberViewStatisticsAddRes 添加操作返回结果
type MMemberViewStatisticsAddRes struct {
	commonApi.EmptyRes
}

// MMemberViewStatisticsEditReq 修改操作请求参数
type MMemberViewStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"用户看剧次数统计表" method:"put" summary:"用户看剧次数统计表修改"`
	commonApi.Author
	*model.MMemberViewStatisticsEditReq
}

// MMemberViewStatisticsEditRes 修改操作返回结果
type MMemberViewStatisticsEditRes struct {
	commonApi.EmptyRes
}

// MMemberViewStatisticsGetReq 获取一条数据请求
type MMemberViewStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"用户看剧次数统计表" method:"get" summary:"获取用户看剧次数统计表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberViewStatisticsGetRes 获取一条数据结果
type MMemberViewStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberViewStatisticsInfoRes
}

// MMemberViewStatisticsDeleteReq 删除数据请求
type MMemberViewStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户看剧次数统计表" method:"delete" summary:"删除用户看剧次数统计表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MMemberViewStatisticsDeleteRes 删除数据返回
type MMemberViewStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
