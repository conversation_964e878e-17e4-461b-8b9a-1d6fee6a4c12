/*
Oceanengine Open Api
巨量引擎开放平台 Open Api
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service
type AdvertiserFundDailyStatV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2AdvertiserFundDailyStatGetRequest
}

func (r *AdvertiserFundDailyStatV2ApiService) SetCfg(cfg *conf.Configuration) *AdvertiserFundDailyStatV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AdvertiserFundDailyStatV2ApiService) SetRequest(request ApiOpenApi2AdvertiserFundDailyStatGetRequest) *AdvertiserFundDailyStatV2ApiService {
	r.Request = &request
	return r
}

func (r *AdvertiserFundDailyStatV2ApiService) AccessToken(accessToken string) *AdvertiserFundDailyStatV2ApiService {
	r.token = accessToken
	return r
}

type ApiOpenApi2AdvertiserFundDailyStatGetRequest struct {
	AdvertiserId int64
	StartDate    string
	EndDate      string
	Page         int64
	PageSize     int64
	AccountType  *models.AdvertiserFundDailyStatV2AccountType
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AdvertiserFundDailyStatV2ApiService) Do() (data *models.AdvertiserFundDailyStatV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/advertiser/fund/daily_stat/"
	localVarQueryParams := map[string]string{}
	if r.Request == nil {
		return nil, errors.New("请求参数不能为空")
	}
	if r.Request.AdvertiserId > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	}
	if len(r.Request.StartDate) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "start_date", r.Request.StartDate)
	}
	if len(r.Request.EndDate) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "end_date", r.Request.EndDate)
	}
	if r.Request.Page > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AdvertiserFundDailyStatV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AdvertiserFundDailyStatV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/fund/daily_stat/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
