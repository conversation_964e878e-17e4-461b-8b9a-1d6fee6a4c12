/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AssetsCreativeComponentGetV2ApiService AssetsCreativeComponentGetV2Api service
type AssetsCreativeComponentGetV2ApiService struct {
	ctx          context.Context
	cfg          *conf.Configuration
	token        string
	advertiserId *int64
	filtering    *models.AssetsCreativeComponentGetV2Filtering
	page         *int64
	pageSize     *int64
}

func (r *AssetsCreativeComponentGetV2ApiService) SetCfg(cfg *conf.Configuration) *AssetsCreativeComponentGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AssetsCreativeComponentGetV2ApiService) SetFiltering(filtering *models.AssetsCreativeComponentGetV2Filtering) *AssetsCreativeComponentGetV2ApiService {
	r.filtering = filtering
	return r
}

func (r *AssetsCreativeComponentGetV2ApiService) SetAId(aid int64) *AssetsCreativeComponentGetV2ApiService {
	r.advertiserId = &aid
	return r
}

// set  page
func (r *AssetsCreativeComponentGetV2ApiService) SetPageAndSize(page, size int64) *AssetsCreativeComponentGetV2ApiService {
	r.page = &page
	r.pageSize = &size
	return r
}

func (r *AssetsCreativeComponentGetV2ApiService) AccessToken(accessToken string) *AssetsCreativeComponentGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AssetsCreativeComponentGetV2ApiService) Do() (data *models.AssetsCreativeComponentGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/assets/creative_component/get/"
	localVarQueryParams := map[string]string{}
	if r.advertiserId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
	}
	if r.filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.filtering)
	}
	if r.page != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.page)
	}
	if r.pageSize != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.pageSize)
	}
	//libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AssetsCreativeComponentGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AssetsCreativeComponentGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/assets/creative_component/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
