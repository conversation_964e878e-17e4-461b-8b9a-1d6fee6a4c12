// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/dao/internal/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAnchorPointImagesDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdAnchorPointImagesDao struct {
	table   string                     // Table is the underlying table name of the DAO.
	group   string                     // Group is the database configuration group name of current DAO.
	columns AdAnchorPointImagesColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdAnchorPointImagesColumns defines and stores column names for table ad_anchor_point_images.
type AdAnchorPointImagesColumns struct {
	Id             string //
	AnchorPointId  string // 锚点id
	Uri            string // url 地址
	Width          string // 宽
	Height         string // 高
	Loading        string // 0 ， 1 暂时不知道干嘛的
	ClMaterialId   string // 关联的素材id
	ClThumbnailUri string // 关联饿素材url
	ImageType      string // 图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'
}

var adAnchorPointImagesColumns = AdAnchorPointImagesColumns{
	Id:             "id",
	AnchorPointId:  "anchor_point_id",
	Uri:            "uri",
	Width:          "width",
	Height:         "height",
	Loading:        "loading",
	ClMaterialId:   "cl_material_id",
	ClThumbnailUri: "cl_thumbnail_uri",
	ImageType:      "image_type",
}

// NewAdAnchorPointImagesDao creates and returns a new DAO object for table data access.
func NewAdAnchorPointImagesDao() *AdAnchorPointImagesDao {
	return &AdAnchorPointImagesDao{
		group:   "default",
		table:   "ad_anchor_point_images",
		columns: adAnchorPointImagesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdAnchorPointImagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdAnchorPointImagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdAnchorPointImagesDao) Columns() AdAnchorPointImagesColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdAnchorPointImagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdAnchorPointImagesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdAnchorPointImagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
