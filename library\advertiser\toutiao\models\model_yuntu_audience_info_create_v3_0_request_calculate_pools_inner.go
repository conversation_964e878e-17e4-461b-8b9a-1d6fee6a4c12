/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceInfoCreateV30RequestCalculatePoolsInner struct for YuntuAudienceInfoCreateV30RequestCalculatePoolsInner
type YuntuAudienceInfoCreateV30RequestCalculatePoolsInner struct {
	// 池内运算符，枚举值\"|\"或\"&\"，当运算池内只有一个标签/人群包时，该运算符必须为空字符串
	InnerOp string `json:"inner_op"`
	// 池间运算符，枚举值\"|\"或\"&\"或\"~\"，第0个运算池的池间运算符必须为空字符串
	OuterOp string `json:"outer_op"`
	// 运算池中人群包/标签数组
	Tags []*YuntuAudienceInfoCreateV30RequestCalculatePoolsInnerTagsInner `json:"tags"`
}
