// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-11-27 11:18:34
// 生成路径: internal/app/ad/dao/internal/ad_plan_rule.go
// 生成人：cq
// desc:广告计划规则设置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdPlanRuleDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdPlanRuleDao struct {
	table   string            // Table is the underlying table name of the DAO.
	group   string            // Group is the database configuration group name of current DAO.
	columns AdPlanRuleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdPlanRuleColumns defines and stores column names for table ad_plan_rule.
type AdPlanRuleColumns struct {
	Id          string // ID
	PlanId      string // 计划ID
	TimeScope   string // 时间范围，0 表示当天，3 表示过去三天，依次类推
	MetricsName string // 指标名，根据指标筛选数据是否达标
	Operator    string // 运算符，例如 >、<、>= 等
	Unit        string // 单位，例如元、%
	TargetValue string // 运算的最终目标值
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
	DeletedAt   string // 删除时间
}

var adPlanRuleColumns = AdPlanRuleColumns{
	Id:          "id",
	PlanId:      "plan_id",
	TimeScope:   "time_scope",
	MetricsName: "metrics_name",
	Operator:    "operator",
	Unit:        "unit",
	TargetValue: "target_value",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
}

// NewAdPlanRuleDao creates and returns a new DAO object for table data access.
func NewAdPlanRuleDao() *AdPlanRuleDao {
	return &AdPlanRuleDao{
		group:   "default",
		table:   "ad_plan_rule",
		columns: adPlanRuleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdPlanRuleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdPlanRuleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdPlanRuleDao) Columns() AdPlanRuleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdPlanRuleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdPlanRuleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdPlanRuleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
