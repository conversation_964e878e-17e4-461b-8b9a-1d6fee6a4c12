// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/dao/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adAnchorPointImagesDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adAnchorPointImagesDao struct {
	*internal.AdAnchorPointImagesDao
}

var (
	// AdAnchorPointImages is globally public accessible object for table tools_gen_table operations.
	AdAnchorPointImages = adAnchorPointImagesDao{
		internal.NewAdAnchorPointImagesDao(),
	}
)

// Fill with you ideas below.
