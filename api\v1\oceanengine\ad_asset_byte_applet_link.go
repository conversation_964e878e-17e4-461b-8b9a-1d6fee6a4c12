// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-13 16:08:28
// 生成路径: api/v1/oceanengine/ad_asset_byte_applet_link.go
// 生成人：cq
// desc:资产-字节小程序-链接相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetByteAppletLinkSearchReq 分页请求参数
type AdAssetByteAppletLinkSearchReq struct {
	g.Meta `path:"/list" tags:"资产-字节小程序-链接" method:"post" summary:"资产-字节小程序-链接列表"`
	commonApi.Author
	model.AdAssetByteAppletLinkSearchReq
}

// AdAssetByteAppletLinkSearchRes 列表返回结果
type AdAssetByteAppletLinkSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetByteAppletLinkSearchRes
}

// AdAssetByteAppletLinkAddReq 添加操作请求参数
type AdAssetByteAppletLinkAddReq struct {
	g.Meta `path:"/add" tags:"资产-字节小程序-链接" method:"post" summary:"资产-字节小程序-链接添加"`
	commonApi.Author
	*model.AdAssetByteAppletLinkBatchAddReq
}

// AdAssetByteAppletLinkAddRes 添加操作返回结果
type AdAssetByteAppletLinkAddRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletLinkEditReq 修改操作请求参数
type AdAssetByteAppletLinkEditReq struct {
	g.Meta `path:"/edit" tags:"资产-字节小程序-链接" method:"put" summary:"资产-字节小程序-链接修改"`
	commonApi.Author
	*model.AdAssetByteAppletLinkEditReq
}

// AdAssetByteAppletLinkEditRes 修改操作返回结果
type AdAssetByteAppletLinkEditRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletLinkGetReq 获取一条数据请求
type AdAssetByteAppletLinkGetReq struct {
	g.Meta `path:"/get" tags:"资产-字节小程序-链接" method:"get" summary:"获取资产-字节小程序-链接信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetByteAppletLinkGetRes 获取一条数据结果
type AdAssetByteAppletLinkGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetByteAppletLinkInfoRes
}

// AdAssetByteAppletLinkDeleteReq 删除数据请求
type AdAssetByteAppletLinkDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-字节小程序-链接" method:"post" summary:"删除资产-字节小程序-链接"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键删除
}

// AdAssetByteAppletLinkDeleteRes 删除数据返回
type AdAssetByteAppletLinkDeleteRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletLinkBatchImportReq 批量导入字节小程序链接请求参数
type AdAssetByteAppletLinkBatchImportReq struct {
	g.Meta `path:"/batchImport" tags:"资产-字节小程序" method:"post" summary:"资产-批量导入字节小程序链接"`
	commonApi.Author
	File *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}

// AdAssetByteAppletLinkBatchImportRes 添加操作返回结果
type AdAssetByteAppletLinkBatchImportRes struct {
	commonApi.EmptyRes
}

// AdAssetByteAppletLinkBatchImportFromChannelReq 渠道 - 批量导入字节小程序链接请求参数
type AdAssetByteAppletLinkBatchImportFromChannelReq struct {
	g.Meta `path:"/batchImportFromChannel" tags:"资产-字节小程序" method:"post" summary:"资产-渠道-批量导入字节小程序链接"`
	commonApi.Author
	*model.AdAssetByteAppletLinkBatchImportFromChannelReq
}

// AdAssetByteAppletLinkBatchImportFromChannelRes 添加操作返回结果
type AdAssetByteAppletLinkBatchImportFromChannelRes struct {
	commonApi.EmptyRes
}
