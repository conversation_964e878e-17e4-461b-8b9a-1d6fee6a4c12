package model

type Oauth2RefreshTokenRequest struct {
	//
	AppId int64 `json:"app_id,omitempty"`
	//
	RefreshToken string `json:"refresh_token"`
	//
	Secret string `json:"secret"`
}

// Oauth2RefreshTokenResponse struct for Oauth2RefreshTokenResponse
type Oauth2RefreshTokenResponse struct {
	//
	Code int64                           `json:"code,omitempty"`
	Data *Oauth2RefreshTokenResponseData `json:"data,omitempty"`
	//
	Message string `json:"message,omitempty"`
	//
	RequestId string `json:"request_id,omitempty"`
}

type Oauth2RefreshTokenResponseData struct {
	//accessToken
	AccessToken string `json:"access_token,omitempty"`
	//过期时间
	ExpiresIn int64 `json:"expires_in,omitempty"`
	//刷新token
	RefreshToken string `json:"refresh_token,omitempty"`
	//刷新token过期时间
	RefreshTokenExpiresIn int64 `json:"refresh_token_expires_in,omitempty"`
}
