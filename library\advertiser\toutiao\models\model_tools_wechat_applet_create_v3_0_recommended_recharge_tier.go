/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletCreateV30RecommendedRechargeTier
type ToolsWechatAppletCreateV30RecommendedRechargeTier string

// List of tools_wechat_applet_create_v3.0_recommended_recharge_tier
const (
	ABOVE_200_ToolsWechatAppletCreateV30RecommendedRechargeTier             ToolsWechatAppletCreateV30RecommendedRechargeTier = "ABOVE_200"
	FROM_100_TO_200_ToolsWechatAppletCreateV30RecommendedRechargeTier       ToolsWechatAppletCreateV30RecommendedRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletCreateV30RecommendedRechargeTier ToolsWechatAppletCreateV30RecommendedRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletCreateV30RecommendedRechargeTier     ToolsWechatAppletCreateV30RecommendedRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_create_v3.0_recommended_recharge_tier value
func (v ToolsWechatAppletCreateV30RecommendedRechargeTier) Ptr() *ToolsWechatAppletCreateV30RecommendedRechargeTier {
	return &v
}
