// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: api/v1/adx/adx_task.go
// 生成人：cyao
// desc:ADX任务主表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxTaskSearchReq 分页请求参数
type AdxTaskSearchReq struct {
	g.Meta `path:"/list" tags:"ADX任务主表" method:"get" summary:"ADX任务主表列表"`
	commonApi.Author
	model.AdxTaskSearchReq
}

// AdxTaskSearchRes 列表返回结果
type AdxTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxTaskSearchRes
}

// AdxTaskExportReq 导出请求
type AdxTaskExportReq struct {
	g.Meta `path:"/export" tags:"ADX任务主表" method:"get" summary:"ADX任务主表导出"`
	commonApi.Author
	model.AdxTaskSearchReq
}

// AdxTaskExportRes 导出响应
type AdxTaskExportRes struct {
	commonApi.EmptyRes
}

// AdxTaskAddReq 添加操作请求参数
type AdxTaskAddReq struct {
	g.Meta `path:"/add" tags:"ADX任务主表" method:"post" summary:"ADX任务主表添加"`
	commonApi.Author
	*model.AdxTaskAddReq
}

// AdxTaskAddRes 添加操作返回结果
type AdxTaskAddRes struct {
	commonApi.EmptyRes
}

// AdxTaskEditReq 修改操作请求参数
type AdxTaskEditReq struct {
	g.Meta `path:"/edit" tags:"ADX任务主表" method:"put" summary:"ADX任务主表修改"`
	commonApi.Author
	*model.AdxTaskEditReq
}

// AdxTaskEditRes 修改操作返回结果
type AdxTaskEditRes struct {
	commonApi.EmptyRes
}

// AdxTaskGetReq 获取一条数据请求
type AdxTaskGetReq struct {
	g.Meta `path:"/get" tags:"ADX任务主表" method:"get" summary:"获取ADX任务主表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdxTaskGetRes 获取一条数据结果
type AdxTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxTaskInfoRes
}

type CreateByMaterialReq struct {
	g.Meta `path:"/createByMaterial" tags:"ADX任务主表" method:"post" summary:"创建ADX任务主表"`
	commonApi.Author
	*model.CreateByMaterialReq
}

type CreateByMaterialRes struct {
	//commonApi.EmptyRes
	g.Meta `mime:"application/json"`
	*model.CreateByMaterialRes
}

// 更新任务状态
type UpdateStatusReq struct {
	g.Meta `path:"/updateStatus" tags:"ADX任务主表" method:"post" summary:"更新任务状态"`
	commonApi.Author
	*model.UpdateStatusReq
}
type UpdateStatusRes struct {
	commonApi.EmptyRes
}

// AdxTaskDeleteReq 删除数据请求
type AdxTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX任务主表" method:"delete" summary:"删除ADX任务主表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdxTaskDeleteRes 删除数据返回
type AdxTaskDeleteRes struct {
	commonApi.EmptyRes
}
