// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: internal/app/ad/model/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/library/notify"
)

// AdPlanSettingInfoRes is the golang structure for table ad_plan_setting.
type AdPlanSettingInfoRes struct {
	gmeta.Meta         `orm:"table:ad_plan_setting"`
	Id                 int         `orm:"id,primary" json:"id" dc:"ID"`                                                                               // ID
	RuleName           string      `orm:"rule_name" json:"ruleName" dc:"计划规则名称"`                                                                      // 计划规则名称
	UserId             int         `orm:"user_id" json:"userId" dc:"用户id创建计划人id"`                                                                     // 用户id创建计划人id
	MediaType          int         `orm:"media_type" json:"mediaType" dc:"媒体类型 1 巨量 2"`                                                               // 媒体类型 1 巨量 2
	ObjectType         int         `orm:"object_type" json:"objectType" dc:"对象类型 1 账户 2 项目 3 广告"`                                                     // 对象类型 1 账户 2 项目 3 广告
	ScopeType          int         `orm:"scope_type" json:"scopeType" dc:"范围 1.所有 2 指定范围"`                                                            // 范围 1.所有 2 指定范围
	ScopeObjectType    int         `orm:"scope_object_type" json:"scopeObjectType" dc:"指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告"`                // 指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告
	ScopeEntityIds     string      `orm:"scope_entity_ids" json:"scopeEntityIds" dc:"(和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空"` // (和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空
	AdjustChannel      int         `orm:"adjust_channel" json:"adjustChannel" dc:"调整渠道开关，0 关，1 开"`                                                    // 调整渠道开关，0 关，1 开
	OnlyMessage        int         `orm:"only_message" json:"onlyMessage" dc:"1 仅发送通知 2 为需要执行计划"`                                                     // 1 仅发送通知 2 为需要执行计划
	RunFrequency       int         `orm:"run_frequency" json:"runFrequency" dc:"运行频率，单位分钟"`                                                           // 运行频率，单位分钟
	EffectiveTimeType  int         `orm:"effective_time_type" json:"effectiveTimeType" dc:"生效时间类型，1 长期，2 时间范围"`                                       // 生效时间类型，1 长期，2 时间范围
	EffectiveStartTime *gtime.Time `orm:"effective_start_time" json:"effectiveStartTime" dc:"生效开始时间，仅当 effective_time_type 为 2 时有值"`                  // 生效开始时间，仅当 effective_time_type 为 2 时有值
	EffectiveEndTime   *gtime.Time `orm:"effective_end_time" json:"effectiveEndTime" dc:"生效结束时间，仅当 effective_time_type 为 2 时有值"`                      // 生效结束时间，仅当 effective_time_type 为 2 时有值
	ModeOfNotification string      `orm:"mode_of_notification" json:"modeOfNotification" dc:"通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔"`                            // 通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔
	PhoneNo            string      `orm:"phone_no" json:"phoneNo" dc:"手机号，多个以 | 分隔"`                                                                  // 手机号，多个以 | 分隔
	Email              string      `orm:"email" json:"email" dc:"邮箱，以 | 分隔"`                                                                          // 邮箱，以 | 分隔
	Status             int         `orm:"status" json:"status" dc:"1启用 2 未启用"`                                                                        // 1启用 2 未启用
	CreatedAt          *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                      // 创建时间
	UpdatedAt          *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                      // 更新时间
}

type AdPlanSettingListRes struct {
	RuleId             int         `json:"ruleId" dc:"规则ID"`
	RuleName           string      `json:"ruleName" dc:"计划规则名称"`
	UserId             int         `json:"userId" dc:"用户id创建计划人id"`
	UserName           string      `json:"userName" dc:"创建计划人"`
	MediaType          int         `json:"mediaType" dc:"媒体类型 1 巨量 2"`
	ObjectType         int         `json:"objectType" dc:"对象类型 1 账户 2 项目 3 广告"`
	ScopeType          int         `json:"scopeType" dc:"范围 1.所有 2 指定范围"`
	ScopeObjectType    int         `json:"scopeObjectType" dc:"指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告"`
	ScopeEntityIds     string      `json:"scopeEntityIds" dc:"(和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空"`
	ScopeEntityNums    int         `json:"scopeEntityNums" dc:"托管对象个数"`
	AdjustChannel      int         `json:"adjustChannel" dc:"调整渠道开关，0 关，1 开"`
	OnlyMessage        int         `json:"onlyMessage" dc:"1 仅发送通知 2 为需要执行计划"`
	RunFrequency       int         `json:"runFrequency" dc:"运行频率，单位分钟"`
	EffectiveTimeType  int         `json:"effectiveTimeType" dc:"生效时间类型，1 长期，2 时间范围"`
	EffectiveStartTime *gtime.Time `json:"effectiveStartTime" dc:"生效开始时间，仅当 effective_time_type 为 2 时有值"`
	EffectiveEndTime   *gtime.Time `json:"effectiveEndTime" dc:"生效结束时间，仅当 effective_time_type 为 2 时有值"`
	ModeOfNotification string      `json:"modeOfNotification" dc:"通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔"`
	PhoneNo            string      `json:"phoneNo" dc:"手机号，多个以 | 分隔"`
	Email              string      `json:"email" dc:"邮箱，以 | 分隔"`
	Status             int         `json:"status" dc:"1启用 2 未启用"`
	CreatedAt          *gtime.Time `json:"createdAt" dc:"创建时间"`

	AdPlanRuleList []*AdPlanRuleInfoRes `json:"adPlanRuleList" dc:"规则条件列表"`

	ExecuteType    int `json:"executeType" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"`
	AdjustmentType int `json:"adjustmentType" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`
	ValueType      int `json:"valueType" dc:"1 元 2 %"`
	ExecuteValue   int `json:"executeValue"  dc:"调整的目标值，增加或者减少的值"`
	LimitValue     int `json:"limitValue"  dc:"最低/最高调整至"`
	TimeDimension  int `json:"timeDimension" dc:"时间维度，1 天"`
	ExecuteTimes   int `json:"executeTimes"  dc:"执行次数，1、2、3、4 等"`

	ChannelList      string                         `json:"channelList"  dc:"渠道列表，用|分隔"`
	TemplateId       int                            `json:"templateId"  dc:"充值模板ID"`
	SinglePrice      float64                        `json:"singlePrice"  dc:"单集价格"`
	LockNum          int                            `json:"lockNum"  dc:"付费集数"`
	AdSetting        *channelModel.AdSettingListRes `json:"adSetting" dc:"广告回传配置"`
	ScopeEntityInfos []*ScopeEntityInfo             `json:"scopeEntityInfos" dc:"已选择的账户/项目/广告列表"`
	ChannelInfos     []*ScopeEntityInfo             `json:"channelInfos" dc:"已选择的渠道列表"`
}

type ScopeEntityInfo struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// AdPlanSettingSearchReq 分页请求参数
type AdPlanSettingSearchReq struct {
	comModel.PageReq
	RuleId    int    `p:"ruleId" dc:"规则id"`
	RuleName  string `p:"ruleName" dc:"规则名称"`
	Status    string `p:"status" v:"status@integer#1启用 2 未启用需为整数" dc:"1启用 2 未启用"`
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	DeptIds   []int  `p:"deptIds" dc:"部门id"`
}

// AdPlanSettingSearchRes 列表返回结果
type AdPlanSettingSearchRes struct {
	comModel.ListRes
	List []*AdPlanSettingListRes `json:"list"`
}

// AdPlanSettingAddReq 添加操作请求参数
type AdPlanSettingAddReq struct {
	RuleName           string      `p:"ruleName" v:"required#计划规则名称不能为空" dc:"计划规则名称"`
	MediaType          int         `p:"mediaType" v:"required#媒体类型 1 巨量 2不能为空" dc:"媒体类型 1 巨量 2"`
	ObjectType         int         `p:"objectType" v:"required#对象类型 1 账户 2 项目 3 广告不能为空" dc:"对象类型 1 账户 2 项目 3 广告"`
	ScopeType          int         `p:"scopeType" v:"required#范围 1.所有 2 指定范围不能为空" dc:"范围 1.所有 2 指定范围"`
	ScopeObjectType    int         `p:"scopeObjectType"  dc:"指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告"`
	ScopeEntityIds     string      `p:"scopeEntityIds"  dc:"(和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空"`
	AdjustChannel      int         `p:"adjustChannel"  dc:"调整渠道开关，0 关，1 开"`
	OnlyMessage        int         `p:"onlyMessage" v:"required#1 仅发送通知 2 为需要执行计划不能为空" dc:"1 仅发送通知 2 为需要执行计划"`
	RunFrequency       int         `p:"runFrequency"  dc:"运行频率，单位分钟"`
	EffectiveTimeType  int         `p:"effectiveTimeType" v:"required#生效时间类型，1 长期，2 时间范围不能为空" dc:"生效时间类型，1 长期，2 时间范围"`
	EffectiveStartTime *gtime.Time `p:"effectiveStartTime"  dc:"生效开始时间，仅当 effective_time_type 为 2 时有值"`
	EffectiveEndTime   *gtime.Time `p:"effectiveEndTime"  dc:"生效结束时间，仅当 effective_time_type 为 2 时有值"`
	ModeOfNotification string      `p:"modeOfNotification"  dc:"通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔"`
	PhoneNo            string      `p:"phoneNo"  dc:"手机号，多个以 | 分隔"`
	Email              string      `p:"email"  dc:"邮箱，以 | 分隔"`
	Status             int         `p:"status" v:"required#1启用 2 未启用不能为空" dc:"1启用 2 未启用"`

	AdPlanRuleList []*AdPlanRuleAddReq `p:"adPlanRuleList" v:"required#请输入规则条件列表不能为空" dc:"规则条件列表"`

	ExecuteType    int `p:"executeType" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"`
	AdjustmentType int `p:"adjustmentType" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`
	ValueType      int `p:"valueType" dc:"1 元 2 %"`
	ExecuteValue   int `p:"executeValue"  dc:"调整的目标值，增加或者减少的值"`
	LimitValue     int `p:"limitValue"  dc:"最低/最高调整至"`
	TimeDimension  int `p:"timeDimension" dc:"时间维度，1 天"`
	ExecuteTimes   int `p:"executeTimes"  dc:"执行次数，1、2、3、4 等"`

	ChannelList string                         `p:"channelList"  dc:"渠道列表，用|分隔"`
	TemplateId  int                            `p:"templateId"  dc:"充值模板ID"`
	SinglePrice float64                        `p:"singlePrice"  dc:"单集价格"`
	LockNum     int                            `p:"lockNum"  dc:"付费集数"`
	AdSetting   *channelModel.AdSettingEditReq `p:"adSetting" dc:"广告回传配置"`
}

// AdPlanSettingEditReq 修改操作请求参数
type AdPlanSettingEditReq struct {
	RuleId             int         `p:"ruleId" v:"required#主键ID不能为空" dc:"计划ID"`
	RuleName           string      `p:"ruleName" v:"required#计划规则名称不能为空" dc:"计划规则名称"`
	MediaType          int         `p:"mediaType" v:"required#媒体类型 1 巨量 2不能为空" dc:"媒体类型 1 巨量 2"`
	ObjectType         int         `p:"objectType" v:"required#对象类型 1 账户 2 项目 3 广告不能为空" dc:"对象类型 1 账户 2 项目 3 广告"`
	ScopeType          int         `p:"scopeType" v:"required#范围 1.所有 2 指定范围不能为空" dc:"范围 1.所有 2 指定范围"`
	ScopeObjectType    int         `p:"scopeObjectType"  dc:"指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告"`
	ScopeEntityIds     string      `p:"scopeEntityIds"  dc:"(和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空"`
	AdjustChannel      int         `p:"adjustChannel"  dc:"调整渠道开关，0 关，1 开"`
	OnlyMessage        int         `p:"onlyMessage" v:"required#1 仅发送通知 2 为需要执行计划不能为空" dc:"1 仅发送通知 2 为需要执行计划"`
	RunFrequency       int         `p:"runFrequency"  dc:"运行频率，单位分钟"`
	EffectiveTimeType  int         `p:"effectiveTimeType" v:"required#生效时间类型，1 长期，2 时间范围不能为空" dc:"生效时间类型，1 长期，2 时间范围"`
	EffectiveStartTime *gtime.Time `p:"effectiveStartTime"  dc:"生效开始时间，仅当 effective_time_type 为 2 时有值"`
	EffectiveEndTime   *gtime.Time `p:"effectiveEndTime"  dc:"生效结束时间，仅当 effective_time_type 为 2 时有值"`
	ModeOfNotification string      `p:"modeOfNotification"  dc:"通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔"`
	PhoneNo            string      `p:"phoneNo"  dc:"手机号，多个以 | 分隔"`
	Email              string      `p:"email"  dc:"邮箱，以 | 分隔"`
	Status             int         `p:"status" v:"required#1启用 2 未启用不能为空" dc:"1启用 2 未启用"`

	AdPlanRuleList []*AdPlanRuleEditReq `p:"adPlanRuleList" v:"required#请输入规则条件列表不能为空" dc:"规则条件列表"`

	ExecuteType    int `p:"executeType" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"`
	AdjustmentType int `p:"adjustmentType" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`
	ValueType      int `p:"valueType" dc:"1 元 2 %"`
	ExecuteValue   int `p:"executeValue"  dc:"调整的目标值，增加或者减少的值"`
	LimitValue     int `p:"limitValue"  dc:"最低/最高调整至"`
	TimeDimension  int `p:"timeDimension" dc:"时间维度，1 天"`
	ExecuteTimes   int `p:"executeTimes"  dc:"执行次数，1、2、3、4 等"`

	ChannelList string                         `p:"channelList"  dc:"渠道列表，用|分隔"`
	TemplateId  int                            `p:"templateId"  dc:"充值模板ID"`
	SinglePrice float64                        `p:"singlePrice"  dc:"单集价格"`
	LockNum     int                            `p:"lockNum"  dc:"付费集数"`
	AdSetting   *channelModel.AdSettingEditReq `p:"adSetting" dc:"广告回传配置"`
}

// GetManagedObjectListReq 分页请求参数
type GetManagedObjectListReq struct {
	comModel.PageReq
	RuleId int `p:"ruleId" dc:"规则id"`
}

// GetManagedObjectListRes 列表返回结果
type GetManagedObjectListRes struct {
	comModel.ListRes
	List []*GetManagedObjectInfo `json:"list"`
}

type GetManagedObjectInfo struct {
	Name string `json:"name" dc:"名称"`
	Id   string `json:"id" dc:"ID"`
}

type PushNotifyTestReq struct {
	NotifyType notify.NotifyType `p:"notifyType" dc:"通知类型 1 站内信 2 短信 3 邮箱"`
	UserId     int               `p:"userId" dc:"用户ID"`
	PhoneNo    string            `p:"phoneNo" dc:"手机号，多个以 | 分隔"`
	Email      string            `p:"email" dc:"邮箱号，多个以 | 分隔"`
}

type MsgContent struct {
	Title         string            `json:"title" dc:"标题 站内信和邮箱通知必填"`
	Content       string            `json:"content" dc:"内容 站内信和邮箱通知必填"`
	TemplateParam map[string]string `json:"templateParam" dc:"短信模板参数 短信通知必填"`
}

type MsgTemplate struct {
	RuleName   string `json:"RuleName" dc:"规则名称"`
	TargetName string `json:"TargetName" dc:"目标名称"`
	Condition  string `json:"Condition" dc:"条件"`
}
