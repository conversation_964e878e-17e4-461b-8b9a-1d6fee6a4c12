// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:34:02
// 生成路径: internal/app/adx/service/adx_publisher.go
// 生成人：cq
// desc:ADX公司信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxPublisher interface {
	List(ctx context.Context, req *model.AdxPublisherSearchReq) (res *model.AdxPublisherSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxPublisherSearchReq) (listRes []*model.AdxPublisherInfoRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.AdxPublisherInfoRes, err error)
	GetByName(ctx context.Context, name string, likeQuery bool) (res []*model.AdxPublisherInfoRes, err error)
	Add(ctx context.Context, req *model.AdxPublisherAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.AdxPublisherAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxPublisherEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
	RunSyncAdxPublisherTask(ctx context.Context, req *model.AdxPublisherSearchReq) (err error)
	SyncAdxPublisherTask(ctx context.Context)
}

var localAdxPublisher IAdxPublisher

func AdxPublisher() IAdxPublisher {
	if localAdxPublisher == nil {
		panic("implement not found for interface IAdxPublisher, forgot register?")
	}
	return localAdxPublisher
}

func RegisterAdxPublisher(i IAdxPublisher) {
	localAdxPublisher = i
}
