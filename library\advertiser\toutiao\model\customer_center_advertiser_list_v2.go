/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

type CustomerCenterAdvertiserListV2AccountSource string

// List of customer_center_advertiser_list_v2_account_source
const (
	AD_CustomerCenterAdvertiserListV2AccountSource         CustomerCenterAdvertiserListV2AccountSource = "AD"
	QIANCHUAN_CustomerCenterAdvertiserListV2AccountSource  CustomerCenterAdvertiserListV2AccountSource = "QIANCHUAN"
	LOCAL_CustomerCenterAdvertiserListV2AccountSource      CustomerCenterAdvertiserListV2AccountSource = "LOCAL"
	ENTERPRISE_CustomerCenterAdvertiserListV2AccountSource CustomerCenterAdvertiserListV2AccountSource = "ENTERPRISE"
)

type CustomerCenterAdvertiserListV2Request struct {
	AccountSource *CustomerCenterAdvertiserListV2AccountSource
	CcAccountId   *int64
	Filtering     *CustomerCenterAdvertiserListV2Filtering
	Page          *int64
	PageSize      *int64
}

// CustomerCenterAdvertiserListV2Filtering
type CustomerCenterAdvertiserListV2Filtering struct {
	//
	AccountName *string `json:"account_name,omitempty"`
}

// CustomerCenterAdvertiserListV2Response struct for CustomerCenterAdvertiserListV2Response
type CustomerCenterAdvertiserListV2Response struct {
	//
	Code *int64                                      `json:"code,omitempty"`
	Data *CustomerCenterAdvertiserListV2ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// CustomerCenterAdvertiserListV2ResponseData
type CustomerCenterAdvertiserListV2ResponseData struct {
	//
	List     []*CustomerCenterAdvertiserListV2ResponseDataListInner `json:"list,omitempty"`
	PageInfo *CustomerCenterAdvertiserListV2ResponseDataPageInfo    `json:"page_info,omitempty"`
}

// CustomerCenterAdvertiserListV2ResponseDataListInner struct for CustomerCenterAdvertiserListV2ResponseDataListInner
type CustomerCenterAdvertiserListV2ResponseDataListInner struct {
	//
	AccountId *string `json:"account_id,omitempty"`
	//
	AccountName *string `json:"account_name,omitempty"`
	//
	AccountType *string `json:"account_type,omitempty"`
	//
	AdvertiserId *int64 `json:"advertiser_id,omitempty"`
	//
	AdvertiserName *string `json:"advertiser_name,omitempty"`
	//
	AdvertiserType *string `json:"advertiser_type,omitempty"`
	//
	EDouyinId *string `json:"e_douyin_id,omitempty"`
	//
	EDouyinName *string `json:"e_douyin_name,omitempty"`
}

// CustomerCenterAdvertiserListV2ResponseDataPageInfo
type CustomerCenterAdvertiserListV2ResponseDataPageInfo struct {
	//
	Page *int64 `json:"page,omitempty"`
	//
	PageSize *int64 `json:"page_size,omitempty"`
	//
	TotalNumber *int64 `json:"total_number,omitempty"`
	//
	TotalPage *int64 `json:"total_page,omitempty"`
}
