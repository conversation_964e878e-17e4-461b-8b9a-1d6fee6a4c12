// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: api/v1/ad/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdMaterialFileSearchReq 分页请求参数
type AdMaterialFileSearchReq struct {
	g.Meta `path:"/list" tags:"广告素材文件夹" method:"get" summary:"广告素材文件夹列表"`
	commonApi.Author
	model.AdMaterialFileSearchReq
}

// AdMaterialFileSearchRes 列表返回结果
type AdMaterialFileSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialFileSearchRes
}

// AdMaterialFileChildListReq 文件目录
type AdMaterialFileChildListReq struct {
	g.Meta `path:"/file/childList" tags:"广告素材文件夹" method:"get" summary:"文件目录"`
	commonApi.Author
	model.AdMaterialFileChildListReq
}

// AdMaterialFileChildListRes 列表返回结果
type AdMaterialFileChildListRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialFileChildListRes
}

// AdMaterialFileAddReq 添加操作请求参数
type AdMaterialFileAddReq struct {
	g.Meta `path:"/add" tags:"广告素材文件夹" method:"post" summary:"广告素材文件夹添加"`
	commonApi.Author
	*model.AdMaterialFileAddReq
}

// AdMaterialFileAddRes 添加操作返回结果
type AdMaterialFileAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialFileEditReq 修改操作请求参数
type AdMaterialFileEditReq struct {
	g.Meta `path:"/edit" tags:"广告素材文件夹" method:"put" summary:"广告素材文件夹修改"`
	commonApi.Author
	*model.AdMaterialFileEditReq
}

// AdMaterialFileEditRes 修改操作返回结果
type AdMaterialFileEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialFileGetReq 获取一条数据请求
type AdMaterialFileGetReq struct {
	g.Meta `path:"/get" tags:"广告素材文件夹" method:"get" summary:"获取广告素材文件夹信息"`
	commonApi.Author
	FileId int `p:"fileId" v:"required#主键必须"` //通过主键获取
}

// AdMaterialFileGetRes 获取一条数据结果
type AdMaterialFileGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialFileInfoRes
}

// AdMaterialFileDeleteReq 删除数据请求
type AdMaterialFileDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告素材文件夹" method:"delete" summary:"删除广告素材文件夹"`
	commonApi.Author
	FileIds []int `p:"fileIds" v:"required#主键必须"` //通过主键删除
}

// AdMaterialFileDeleteRes 删除数据返回
type AdMaterialFileDeleteRes struct {
	commonApi.EmptyRes
}
