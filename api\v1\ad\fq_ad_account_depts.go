// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-16 11:16:19
// 生成路径: api/v1/ad/fq_ad_account_depts.go
// 生成人：gfast
// desc:番茄账号权限和部门关联相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdAccountDeptsSearchReq 分页请求参数
type FqAdAccountDeptsSearchReq struct {
	g.Meta `path:"/list" tags:"番茄账号权限和部门关联" method:"get" summary:"番茄账号权限和部门关联列表"`
	commonApi.Author
	model.FqAdAccountDeptsSearchReq
}

// FqAdAccountDeptsSearchRes 列表返回结果
type FqAdAccountDeptsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountDeptsSearchRes
}

// FqAdAccountDeptsExportReq 导出请求
type FqAdAccountDeptsExportReq struct {
	g.Meta `path:"/export" tags:"番茄账号权限和部门关联" method:"get" summary:"番茄账号权限和部门关联导出"`
	commonApi.Author
	model.FqAdAccountDeptsSearchReq
}

// FqAdAccountDeptsExportRes 导出响应
type FqAdAccountDeptsExportRes struct {
	commonApi.EmptyRes
}

// FqAdAccountDeptsAddReq 添加操作请求参数
type FqAdAccountDeptsAddReq struct {
	g.Meta `path:"/add" tags:"番茄账号权限和部门关联" method:"post" summary:"番茄账号权限和部门关联添加"`
	commonApi.Author
	*model.FqAdAccountDeptsAddReq
}

// FqAdAccountDeptsAddRes 添加操作返回结果
type FqAdAccountDeptsAddRes struct {
	commonApi.EmptyRes
}

// FqAdAccountDeptsEditReq 修改操作请求参数
type FqAdAccountDeptsEditReq struct {
	g.Meta `path:"/edit" tags:"番茄账号权限和部门关联" method:"put" summary:"番茄账号权限和部门关联修改"`
	commonApi.Author
	*model.FqAdAccountDeptsEditReq
}

// FqAdAccountDeptsEditRes 修改操作返回结果
type FqAdAccountDeptsEditRes struct {
	commonApi.EmptyRes
}

// FqAdAccountDeptsGetReq 获取一条数据请求
type FqAdAccountDeptsGetReq struct {
	g.Meta `path:"/get" tags:"番茄账号权限和部门关联" method:"get" summary:"获取番茄账号权限和部门关联信息"`
	commonApi.Author
	DistributorId int64 `p:"distributorId" v:"required#主键必须"` //通过主键获取
}

// FqAdAccountDeptsGetRes 获取一条数据结果
type FqAdAccountDeptsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountDeptsInfoRes
}

// FqAdAccountDeptsDeleteReq 删除数据请求
type FqAdAccountDeptsDeleteReq struct {
	g.Meta `path:"/delete" tags:"番茄账号权限和部门关联" method:"delete" summary:"删除番茄账号权限和部门关联"`
	commonApi.Author
	DistributorIds []int64 `p:"distributorIds" v:"required#主键必须"` //通过主键删除
}

// FqAdAccountDeptsDeleteRes 删除数据返回
type FqAdAccountDeptsDeleteRes struct {
	commonApi.EmptyRes
}
