/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// EventManagerAvailableEventsGetV2ApiService 获取可创建事件列表  此接口用于查询资产下支持创建的事件列表
type EventManagerAvailableEventsGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2EventManagerAvailableEventsGetGetRequest
}

type ApiOpenApi2EventManagerAvailableEventsGetGetRequest struct {
	AdvertiserId *int64
	AssetId      *int64
}

func (r *EventManagerAvailableEventsGetV2ApiService) SetCfg(cfg *conf.Configuration) *EventManagerAvailableEventsGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *EventManagerAvailableEventsGetV2ApiService) SetReq(req *ApiOpenApi2EventManagerAvailableEventsGetGetRequest) *EventManagerAvailableEventsGetV2ApiService {
	r.Request = req
	return r
}

//// 广告主ID
//func (r *EventManagerAvailableEventsGetV2ApiService) AdvertiserId(advertiserId int64) *EventManagerAvailableEventsGetV2ApiService {
//	r.Request.advertiserId = &advertiserId
//	return r
//}
//
//// 资产ID
//func (r *EventManagerAvailableEventsGetV2ApiService) AssetId(assetId int64) *EventManagerAvailableEventsGetV2ApiService {
//	r.Request.assetId = &assetId
//	return r
//}

func (r *EventManagerAvailableEventsGetV2ApiService) AccessToken(accessToken string) *EventManagerAvailableEventsGetV2ApiService {
	r.token = accessToken
	return r
}

func (r *EventManagerAvailableEventsGetV2ApiService) Do() (data *models.EventManagerAvailableEventsGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/event_manager/available_events/get/"
	localVarQueryParams := map[string]string{}

	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if *r.Request.AdvertiserId < 1 {
		return nil, errors.New("advertiserId must be greater than 1")
	}
	if *r.Request.AdvertiserId > 9223372036854775807 {
		return nil, errors.New("advertiserId must be less than 9223372036854775807")
	}
	if r.Request.AssetId == nil {
		return nil, errors.New("assetId is required and must be specified")
	}
	if *r.Request.AssetId < 1 {
		return nil, errors.New("assetId must be greater than 1")
	}
	if *r.Request.AssetId > 9223372036854775807 {
		return nil, errors.New("assetId must be less than 9223372036854775807")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "asset_id", r.Request.AssetId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.EventManagerAvailableEventsGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.EventManagerAvailableEventsGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/event_manager/available_events/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
