// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-12 14:00:00
// 生成路径: api/v1/channel/m_member_ad_callback.go
// 生成人：cq
// desc:用户广告回传表格相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdCallbackSearchReq 分页请求参数
type MMemberAdCallbackSearchReq struct {
	g.Meta `path:"/list" tags:"用户广告回传表格" method:"post" summary:"用户广告回传表格列表"`
	commonApi.Author
	model.MMemberAdCallbackSearchReq
}

// MMemberAdCallbackSearchRes 列表返回结果
type MMemberAdCallbackSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackSearchRes
}

// MMemberAdCallbackReq 回传请求参数
type MMemberAdCallbackReq struct {
	g.Meta `path:"/callback" tags:"用户广告回传表格" method:"post" summary:"回传"`
	commonApi.Author
	model.MMemberAdCallbackReq
}

// MMemberAdCallbackRes 回传返回结果
type MMemberAdCallbackRes struct {
	g.Meta `mime:"application/json"`
}

// MMemberAdCallbackExportReq 导出请求
type MMemberAdCallbackExportReq struct {
	g.Meta `path:"/export" tags:"用户广告回传表格" method:"post" summary:"用户广告回传表格导出"`
	commonApi.Author
	model.MMemberAdCallbackSearchReq
}

// MMemberAdCallbackExportRes 导出响应
type MMemberAdCallbackExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackCostStatisticsReq 激励广告消耗统计请求参数
type MMemberAdCallbackCostStatisticsReq struct {
	g.Meta `path:"/task" tags:"用户广告回传表格" method:"post" summary:"激励广告消耗统统计"`
	commonApi.Author
	model.MMemberAdCallbackCostStatisticsReq
}

// MMemberAdCallbackCostStatisticsRes 列表返回结果
type MMemberAdCallbackCostStatisticsRes struct {
	g.Meta `mime:"application/json"`
}

// MMemberAdCallbackRefreshDefaultChannelReq 刷新默认渠道广告数据请求参数
type MMemberAdCallbackRefreshDefaultChannelReq struct {
	g.Meta `path:"/refreshDefaultChannelData" tags:"用户广告回传表格" method:"post" summary:"刷新默认渠道广告数据"`
	commonApi.Author
	model.MMemberAdCallbackRefreshDefaultChannelReq
}

// MMemberAdCallbackRefreshDefaultChannelRes 刷新默认渠道广告数据返回结果
type MMemberAdCallbackRefreshDefaultChannelRes struct {
	g.Meta `mime:"application/json"`
}

// UpdateMemberAdCallbackReq 刷新默认渠道广告数据请求参数
type UpdateMemberAdCallbackReq struct {
	g.Meta `path:"/updateMemberAdCallback" tags:"用户广告回传表格" method:"post" summary:"更新用户归属和广告回传计划归属"`
	commonApi.Author
	PitcherId int `p:"pitcherId" dc:"投手ID"`
}

// UpdateMemberAdCallbackRes 刷新默认渠道广告数据返回结果
type UpdateMemberAdCallbackRes struct {
	g.Meta `mime:"application/json"`
}
