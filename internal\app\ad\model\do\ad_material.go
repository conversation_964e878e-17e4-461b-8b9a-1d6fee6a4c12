// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-12 16:58:16
// 生成路径: internal/app/ad/model/entity/ad_material.go
// 生成人：cyao
// desc:素材主表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterial is the golang structure for table ad_material.
type AdMaterial struct {
	gmeta.Meta    `orm:"table:ad_material, do:true"`
	MaterialId    interface{} `orm:"material_id,primary" json:"materialId"`       // 素材id
	AlbumId       interface{} `orm:"album_id" json:"albumId"`                     // 专辑id
	FileId        interface{} `orm:"file_id" json:"fileId"`                       // 文件夹id
	MaterialName  interface{} `orm:"material_name" json:"materialName"`           // 素材名
	MaterialType  interface{} `orm:"material_type" json:"materialType"`           // material_type 素材类型 video image
	UserId        interface{} `orm:"user_id" json:"userId"`                       // 创建人
	DesignUserId  interface{} `orm:"design_user_id" json:"designUserId" dc:"设计师"` // 设计师
	FileUri       interface{} `orm:"file_uri" json:"fileUri"`                     // 文件url地址
	ThumbnailUri  interface{} `orm:"thumbnail_uri" json:"thumbnailUri"`           // 缩略图url / 封面图
	Labels        interface{} `orm:"labels" json:"labels"`                        // 字符串数组以| 分隔
	FileFormat    interface{} `orm:"file_format" json:"fileFormat"`               // 文件格式 MP4 JPG 等
	FileSize      interface{} `orm:"file_size" json:"fileSize"`                   // 文件大小 eg: 413.87M
	Width         interface{} `orm:"width" json:"width"`                          // 尺寸宽
	Height        interface{} `orm:"height" json:"height"`                        // 尺寸高
	Remark        interface{} `orm:"remark" json:"remark"`                        // 备注
	VideoDuration interface{} `orm:"video_duration" json:"videoDuration"`         // 视频时长
	ManageStatus  interface{} `orm:"manage_status" json:"manageStatus"`           // 素材管理状态
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`                 // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt"`                 // 更新时间
	DeletedAt     *gtime.Time `orm:"deleted_at" json:"deletedAt"`                 // 删除时间
}
