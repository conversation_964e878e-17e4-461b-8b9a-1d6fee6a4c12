// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/dao/internal/ad_anchor_point.go
// 生成人：cyao
// desc:锚点表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAnchorPointDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdAnchorPointDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns AdAnchorPointColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdAnchorPointColumns defines and stores column names for table ad_anchor_point.
type AdAnchorPointColumns struct {
	Id                 string // ID
	AnchorPointName    string // Anchor Point 名称
	ToolTitle          string // 工具标题
	AnchorType         string // Anchor 类型
	AndroidAnchorTitle string // Android Anchor 标题
	IosAnchorTitle     string // iOS Anchor 标题
	AppTags            string // APP标签
	GuideText          string // 引导文案
	AnchorImageMode    string // Anchor 图像模式  1 横图 2竖图
	GameDescription    string // 游戏描述
	AppDescription     string // 应用描述
	GameCharatoristic  string // 游戏特点
	OtherDescription   string // 其他描述
	MainUserId         string // 主用户ID
	CreateTime         string // 创建时间
}

var adAnchorPointColumns = AdAnchorPointColumns{
	Id:                 "id",
	AnchorPointName:    "anchor_point_name",
	ToolTitle:          "tool_title",
	AnchorType:         "anchor_type",
	AndroidAnchorTitle: "android_anchor_title",
	IosAnchorTitle:     "ios_anchor_title",
	AppTags:            "app_tags",
	GuideText:          "guide_text",
	AnchorImageMode:    "anchor_image_mode",
	GameDescription:    "game_description",
	AppDescription:     "app_description",
	GameCharatoristic:  "game_charatoristic",
	OtherDescription:   "other_description",
	MainUserId:         "main_user_id",
	CreateTime:         "create_time",
}

// NewAdAnchorPointDao creates and returns a new DAO object for table data access.
func NewAdAnchorPointDao() *AdAnchorPointDao {
	return &AdAnchorPointDao{
		group:   "default",
		table:   "ad_anchor_point",
		columns: adAnchorPointColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdAnchorPointDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdAnchorPointDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdAnchorPointDao) Columns() AdAnchorPointColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdAnchorPointDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdAnchorPointDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdAnchorPointDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
