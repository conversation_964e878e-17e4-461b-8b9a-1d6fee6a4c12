package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DpaAlbumStatusGetApiService 查询短剧可投状态 DpaAlbumStatusGetApiService service
type DpaAlbumStatusGetApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DpaAlbumStatusGetV2Request
}

type DpaAlbumStatusGetV2Request struct {
	AppId   *string `json:"app_id"` //app_id
	AlbumId *string `json:"album_id,omitempty"`
}

type DpaAlbumStatusGetV2Response struct {
	//
	Code *int64 `json:"code,omitempty"`
	//
	Data *DpaAlbumStatusGetResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

type DpaAlbumStatusGetResponseData struct {
	//can_promotion bool
	CanPromotion *bool `json:"can_promotion,omitempty"`
	// can_not_promotion_reason string
	CanNotPromotionReason *string `json:"can_not_promotion_reason,omitempty"`
}

func (r *DpaAlbumStatusGetApiService) SetCfg(cfg *conf.Configuration) *DpaAlbumStatusGetApiService {
	r.cfg = cfg
	return r
}

func (r *DpaAlbumStatusGetApiService) DpaAlbumStatusGetV2Request(DpaAlbumStatusGetV2Request DpaAlbumStatusGetV2Request) *DpaAlbumStatusGetApiService {
	r.Request = &DpaAlbumStatusGetV2Request
	return r
}

func (r *DpaAlbumStatusGetApiService) AccessToken(accessToken string) *DpaAlbumStatusGetApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DpaAlbumStatusGetApiService) Do() (data *DpaAlbumStatusGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/dpa/album_status/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AlbumId == nil {
		return nil, errors.New("AlbumId is required and must be specified")
	}
	if r.Request.AppId == nil {
		return nil, errors.New("AppId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "album_id", r.Request.AlbumId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "app_id", r.Request.AppId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("App-Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&DpaAlbumStatusGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(DpaAlbumStatusGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/dpa/album_status/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
