/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletCreateV30MaxRechargeTier
type ToolsWechatAppletCreateV30MaxRechargeTier string

// List of tools_wechat_applet_create_v3.0_max_recharge_tier
const (
	ABOVE_200_ToolsWechatAppletCreateV30MaxRechargeTier             ToolsWechatAppletCreateV30MaxRechargeTier = "ABOVE_200"
	FROM_100_TO_200_ToolsWechatAppletCreateV30MaxRechargeTier       ToolsWechatAppletCreateV30MaxRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletCreateV30MaxRechargeTier ToolsWechatAppletCreateV30MaxRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletCreateV30MaxRechargeTier     ToolsWechatAppletCreateV30MaxRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_create_v3.0_max_recharge_tier value
func (v ToolsWechatAppletCreateV30MaxRechargeTier) Ptr() *ToolsWechatAppletCreateV30MaxRechargeTier {
	return &v
}
