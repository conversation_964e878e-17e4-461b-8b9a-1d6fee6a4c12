/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AdvertiserPublicInfoV2ApiService AdvertiserPublicInfoV2Api service
type AdvertiserPublicInfoV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *model.AdvertiserInfoV2Request
}

func (r *AdvertiserPublicInfoV2ApiService) SetCfg(cfg *conf.Configuration) *AdvertiserPublicInfoV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AdvertiserPublicInfoV2ApiService) AdvertiserPublicInfoV2Request(advertiserInfoV2Request model.AdvertiserInfoV2Request) *AdvertiserPublicInfoV2ApiService {
	r.Request = &advertiserInfoV2Request
	return r
}

func (r *AdvertiserPublicInfoV2ApiService) AccessToken(accessToken string) *AdvertiserPublicInfoV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AdvertiserPublicInfoV2ApiService) Do() (data *models.AdvertiserPublicInfoV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/advertiser/public_info/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_ids", r.Request.AdvertiserIds)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AdvertiserPublicInfoV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AdvertiserPublicInfoV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/public_info/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
