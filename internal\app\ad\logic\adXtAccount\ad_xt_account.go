// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: internal/app/ad/logic/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strconv"
	"time"
)

func init() {
	service.RegisterAdXtAccount(New())
}

func New() service.IAdXtAccount {
	return &sAdXtAccount{}
}

type sAdXtAccount struct{}

func (s *sAdXtAccount) List(ctx context.Context, req *model.AdXtAccountSearchReq) (listRes *model.AdXtAccountSearchRes, err error) {
	listRes = new(model.AdXtAccountSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtAccount.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtAccount.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtAccount.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdvertiserNick != "" {
			m = m.Where(dao.AdXtAccount.Columns().AdvertiserNick+" = ?", req.AdvertiserNick)
		}
		if req.RoleName != "" {
			m = m.Where(dao.AdXtAccount.Columns().RoleName+" like ?", "%"+req.RoleName+"%")
		}
		if req.AuthTime != "" {
			m = m.Where(dao.AdXtAccount.Columns().AuthTime+" = ?", gconv.Time(req.AuthTime))
		}
		if req.AppId != "" {
			m = m.Where(dao.AdXtAccount.Columns().AppId+" = ?", gconv.Int(req.AppId))
		}
		if req.AccessToken != "" {
			m = m.Where(dao.AdXtAccount.Columns().AccessToken+" = ?", req.AccessToken)
		}
		if req.RefreshToken != "" {
			m = m.Where(dao.AdXtAccount.Columns().RefreshToken+" = ?", req.RefreshToken)
		}
		if req.Status != "" {
			m = m.Where(dao.AdXtAccount.Columns().Status+" = ?", req.Status)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdXtAccount.Columns().CreatedAt+" >=? AND "+dao.AdXtAccount.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdXtAccountListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdXtAccountListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdXtAccountListRes{
				Id:             v.Id,
				AdvertiserId:   v.AdvertiserId,
				AdvertiserNick: v.AdvertiserNick,
				RoleName:       v.RoleName,
				AuthTime:       v.AuthTime,
				AppId:          v.AppId,
				AccessToken:    v.AccessToken,
				RefreshToken:   v.RefreshToken,
				Status:         v.Status,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdXtAccount) GetExportData(ctx context.Context, req *model.AdXtAccountSearchReq) (listRes []*model.AdXtAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtAccount.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtAccount.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtAccount.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdvertiserNick != "" {
			m = m.Where(dao.AdXtAccount.Columns().AdvertiserNick+" = ?", req.AdvertiserNick)
		}
		if req.RoleName != "" {
			m = m.Where(dao.AdXtAccount.Columns().RoleName+" like ?", "%"+req.RoleName+"%")
		}
		if req.AuthTime != "" {
			m = m.Where(dao.AdXtAccount.Columns().AuthTime+" = ?", gconv.Time(req.AuthTime))
		}
		if req.AppId != "" {
			m = m.Where(dao.AdXtAccount.Columns().AppId+" = ?", gconv.Int(req.AppId))
		}
		if req.AccessToken != "" {
			m = m.Where(dao.AdXtAccount.Columns().AccessToken+" = ?", req.AccessToken)
		}
		if req.RefreshToken != "" {
			m = m.Where(dao.AdXtAccount.Columns().RefreshToken+" = ?", req.RefreshToken)
		}
		if req.Status != "" {
			m = m.Where(dao.AdXtAccount.Columns().Status+" = ?", req.Status)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdXtAccount.Columns().CreatedAt+" >=? AND "+dao.AdXtAccount.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdXtAccount) GetById(ctx context.Context, id int64) (res *model.AdXtAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtAccount.Ctx(ctx).WithAll().Where(dao.AdXtAccount.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtAccount) GetByAId(ctx context.Context, aid string) (res *model.AdXtAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtAccount.Ctx(ctx).WithAll().Where(dao.AdXtAccount.Columns().AdvertiserId, aid).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtAccount) GetByAIds(ctx context.Context, aids []string) (res []*model.AdXtAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtAccount.Ctx(ctx).WithAll().WhereIn(dao.AdXtAccount.Columns().AdvertiserId, aids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtAccount) GetTokenByAId(ctx context.Context, aid int64) (res *model.AdXtAccountInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtAccount.Ctx(ctx).WithAll().Where(dao.AdXtAccount.Columns().AdvertiserId, aid).Scan(&res)
		//

		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtAccount) Add(ctx context.Context, req *model.AdXtAccountAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtAccount.Ctx(ctx).Insert(do.AdXtAccount{
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			RoleName:       req.RoleName,
			AuthTime:       req.AuthTime,
			AppId:          req.AppId,
			AccessToken:    req.AccessToken,
			RefreshToken:   req.RefreshToken,
			Status:         req.Status,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// AddBatch 批量添加
func (s *sAdXtAccount) AddBatch(ctx context.Context, req []*model.AdXtAccountAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtAccount.Ctx(ctx).Save(req)
		liberr.ErrIsNil(ctx, err, "批量添加失败")
	})
	return
}

func (s *sAdXtAccount) Edit(ctx context.Context, req *model.AdXtAccountEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtAccount.Ctx(ctx).WherePri(req.Id).Update(do.AdXtAccount{
			AdvertiserId:   req.AdvertiserId,
			AdvertiserNick: req.AdvertiserNick,
			RoleName:       req.RoleName,
			AuthTime:       req.AuthTime,
			AppId:          req.AppId,
			Status:         req.Status,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdXtAccount) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtAccount.Ctx(ctx).Delete(dao.AdXtAccount.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetAccessTokenByAdvertiserId 星图的根据aid获取token
func (s *sAdXtAccount) GetAccessTokenByAdvertiserId(ctx context.Context, advertiserId string) (AccessToken string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var res model.AdXtAccountInfoRes
		err = dao.AdXtAccount.Ctx(ctx).Where(dao.AdXtAccount.Columns().AdvertiserId, advertiserId).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取access_token失败")
		if res.Id > 0 {
			// 判断token 是否过期
			if res.AccessToken != "" && res.RefreshToken != "" {
				// 当前时间是否大于过期时间
				if res.ExpiresIn.Before(gtime.Now()) {
					// 刷新token
					appConfig, _ := service.AdAppConfig().GetByAppId(ctx, strconv.FormatInt(int64(res.AppId), 10))
					if appConfig == nil {
						err = errors.New("应用不存在")
						liberr.ErrIsNil(ctx, err)
					}
					oauth2Response, err1 := advertiser.GetToutiaoApiClient().Oauth2RefreshTokenApi.
						SetRequest(toutiaoModel.Oauth2RefreshTokenRequest{
							AppId:        int64(res.AppId),
							RefreshToken: res.RefreshToken,
							Secret:       appConfig.Secret,
						}).Do()
					liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取access_token失败: %v", err1))
					// 更新数据库数据
					updateList := make([]*do.AdXtAccount, 0)
					err = dao.AdXtAccount.Ctx(ctx).Where(dao.AdXtAccount.Columns().AccessToken, res.AccessToken).Scan(&updateList)
					for _, item := range updateList {
						item.AccessToken = oauth2Response.AccessToken
						item.ExpiresIn = gtime.Now().Add(time.Duration(oauth2Response.ExpiresIn) * time.Second)
						item.RefreshTokenExpiresIn = gtime.Now().Add(time.Duration(oauth2Response.RefreshTokenExpiresIn) * time.Second)
						item.RefreshToken = oauth2Response.RefreshToken
					}
					_, err = dao.AdXtAccount.Ctx(ctx).Save(updateList)
					AccessToken = oauth2Response.AccessToken
				} else {
					AccessToken = res.AccessToken
					return
				}
			}
		}
	})
	return
}
