// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-23 15:45:10
// 生成路径: api/v1/system/s_channel.go
// 生成人：cyao
// desc:渠道表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// SChannelSearchReq 分页请求参数
type SChannelSearchReq struct {
	g.Meta `path:"/list" tags:"渠道表" method:"get" summary:"渠道表列表"`
	commonApi.Author
	model.SChannelSearchReq
}

// GetChannelListReq 分页获取渠道列表
type GetChannelListReq struct {
	g.Meta `path:"/getChannelList" tags:"渠道表" method:"GET" summary:"获取渠道列表"`
	commonApi.Author
	comModel.PageReq
	KeyWard         string //关键字搜索
	Remark          string
	Ids             []string //渠道列表,前端要求用ids
	ChannelPlatform int      // 平台
}

// GetChannelListRemarkReq 去重获取渠道列表的remark
type GetChannelListRemarkReq struct {
	g.Meta `path:"/getChannelList/remark" tags:"渠道表" method:"GET" summary:"获取渠道列表的remark去重"`
	commonApi.Author
	comModel.PageReq
	Remark string
}

// GetChannelListRemarkRes 去重获取渠道列表的remark
type GetChannelListRemarkRes struct {
	g.Meta `mime:"application/json"`
	*model.GetChannelListRemarkRes
}

type GetChannelListRes struct {
	g.Meta `mime:"application/json"`
	*model.GetChannelListRes
}

// SChannelSearchRes 列表返回结果
type SChannelSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelSearchRes
}

// SChannelExportReq 导出请求
type SChannelExportReq struct {
	g.Meta `path:"/export" tags:"渠道表" method:"get" summary:"渠道表导出"`
	commonApi.Author
	model.SChannelSearchReq
}

// SChannelExportRes 导出响应
type SChannelExportRes struct {
	commonApi.EmptyRes
}
type SChannelExcelTemplateReq struct {
	g.Meta `path:"/excelTemplate" tags:"渠道表" method:"get" summary:"导出模板文件"`
	commonApi.Author
}
type SChannelExcelTemplateRes struct {
	commonApi.EmptyRes
}
type SChannelImportReq struct {
	g.Meta `path:"/import" tags:"渠道表" method:"post" summary:"渠道表导入"`
	commonApi.Author
	File *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}
type SChannelImportRes struct {
	commonApi.EmptyRes
}

// SChannelAddReq 添加操作请求参数
type SChannelAddReq struct {
	g.Meta `path:"/add" tags:"渠道表" method:"post" summary:"渠道表添加"`
	commonApi.Author
	*model.SChannelAddReq
}

// SChannelAddRes 添加操作返回结果
type SChannelAddRes struct {
	commonApi.EmptyRes
}

// SChannelEditReq 修改操作请求参数
type SChannelEditReq struct {
	g.Meta `path:"/edit" tags:"小程序设置" method:"post" summary:"渠道表修改"`
	commonApi.Author
	*model.SChannelEditReq
}

// SChannelEditRes 修改操作返回结果
type SChannelEditRes struct {
	commonApi.EmptyRes
}

// SChannelGetReq 获取一条数据请求
type SChannelGetReq struct {
	g.Meta `path:"/get" tags:"渠道表" method:"get" summary:"获取渠道表信息"`
	commonApi.Author
	Id string `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelGetRes 获取一条数据结果
type SChannelGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelInfoRes
}

// SChannelDeleteReq 删除数据请求
type SChannelDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道表" method:"post" summary:"删除渠道表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelDeleteRes 删除数据返回
type SChannelDeleteRes struct {
	commonApi.EmptyRes
}

// SChannelAdSettingSearchReq 渠道广告设置分页请求参数
type SChannelAdSettingSearchReq struct {
	g.Meta `path:"/getChannelAdList" tags:"小程序设置" method:"post" summary:"小程序渠道号管理"`
	commonApi.Author
	model.SChannelAdSettingSearchReq
}

// SChannelAdSettingSearchRes 列表返回结果
type SChannelAdSettingSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelAdSettingSearchRes
}

// SChannelBatchAddReq 渠道广告设置分页请求参数
type SChannelBatchAddReq struct {
	g.Meta `path:"/batchCopyAdd" tags:"小程序设置" method:"post" summary:"批量复制添加"`
	commonApi.Author
	model.SChannelBatchAddReq
}

// SChannelBatchAddFqReq 批量复制添加番茄
type SChannelBatchAddFqReq struct {
	g.Meta `path:"/batchCopyAddFq" tags:"小程序设置" method:"post" summary:"批量复制添加番茄"`
	commonApi.Author
	model.SChannelBatchAddFqReq
}

// SChannelBatchAddAndPushReq 批量新增并且推送
type SChannelBatchAddAndPushReq struct {
	g.Meta `path:"/batchCopyAddAndPush" tags:"小程序设置" method:"post" summary:"批量新增并且推送"`
	commonApi.Author
	model.SChannelBatchAddAndPushReq
}

// SChannelBatchAddFqAndPushReq 批量新增番茄并且推送
type SChannelBatchAddFqAndPushReq struct {
	g.Meta `path:"/batchCopyAddFqAndPush" tags:"小程序设置" method:"post" summary:"批量新增番茄并且推送"`
	commonApi.Author
	model.SChannelBatchAddFqAndPushReq
}

// DistributionFqChannelReq 批量复制添加番茄
type DistributionFqChannelReq struct {
	g.Meta `path:"/distributionFqChannel" tags:"小程序设置" method:"post" summary:"分配番茄/点众渠道"`
	commonApi.Author
	model.DistributionFqChannelReq
}

// SChannelBatchAddDzReq 批量复制添加点众
type SChannelBatchAddDzReq struct {
	g.Meta `path:"/batchCopyAddDz" tags:"小程序设置" method:"post" summary:"批量复制添加点众"`
	commonApi.Author
	model.SChannelBatchAddDzReq
}

// SChannelBatchAddDzAndPushReq 批量新增点众并且推送
type SChannelBatchAddDzAndPushReq struct {
	g.Meta `path:"/batchCopyAddDzAndPush" tags:"小程序设置" method:"post" summary:"批量新增点众并且推送"`
	commonApi.Author
	model.SChannelBatchAddDzAndPushReq
}

// GetFqAppListReq 获取番茄小程序列表请求参数
type GetFqAppListReq struct {
	g.Meta `path:"/getFqAppList" tags:"小程序设置" method:"post" summary:"获取番茄小程序列表"`
	commonApi.Author
	model.GetFqAppListReq
}

// GetFqAppListRes 获取番茄小程序列表返回结果
type GetFqAppListRes struct {
	commonApi.EmptyRes
	*model.GetFqAppListRes
}

// GetFqRechargeTemplateListReq 获取番充值模板列表请求参数
type GetFqRechargeTemplateListReq struct {
	g.Meta `path:"/getFqRechargeTemplateList" tags:"小程序设置" method:"post" summary:"获取番充值模板列表"`
	commonApi.Author
	model.GetFqRechargeTemplateListReq
}

// GetFqRechargeTemplateListRes 获取番充值模板列表返回结果
type GetFqRechargeTemplateListRes struct {
	commonApi.EmptyRes
	*model.GetFqRechargeTemplateListRes
}

// GetFqCallbackConfigListReq 获取番广告回传规则列表请求参数
type GetFqCallbackConfigListReq struct {
	g.Meta `path:"/getFqCallbackConfigList" tags:"小程序设置" method:"post" summary:"获取番广告回传规则列表"`
	commonApi.Author
	model.GetFqCallbackConfigListReq
}

// GetFqCallbackConfigListRes 获取番广告回传规则列表返回结果
type GetFqCallbackConfigListRes struct {
	commonApi.EmptyRes
	*model.GetFqCallbackConfigListRes
}

// GetFqBookInfoReq 获取番广告回传规则列表请求参数
type GetFqBookInfoReq struct {
	g.Meta `path:"/getFqBookInfo" tags:"小程序设置" method:"post" summary:"获取番茄短剧信息"`
	commonApi.Author
	model.GetFqBookInfoReq
}

// GetFqBookInfoRes 获取番广告回传规则列表返回结果
type GetFqBookInfoRes struct {
	commonApi.EmptyRes
	*model.GetFqBookInfoRes
}

// GetDzChannelListReq 获取点众渠道列表请求参数
type GetDzChannelListReq struct {
	g.Meta `path:"/getDzChannelList" tags:"小程序设置" method:"post" summary:"获取点众渠道列表"`
	commonApi.Author
	model.GetDzChannelListReq
}

// GetDzChannelListRes 获取点众渠道列表返回结果
type GetDzChannelListRes struct {
	commonApi.EmptyRes
	*model.GetDzChannelListRes
}

// GetDzRechargeTemplateListReq 获取点众充值模板列表请求参数
type GetDzRechargeTemplateListReq struct {
	g.Meta `path:"/getDzRechargeTemplateList" tags:"小程序设置" method:"post" summary:"获取点众充值模板列表"`
	commonApi.Author
	model.GetDzRechargeTemplateListReq
}

// GetDzRechargeTemplateListRes 获取点众充值模板列表返回结果
type GetDzRechargeTemplateListRes struct {
	commonApi.EmptyRes
	*model.GetDzRechargeTemplateListRes
}

// GetDzCallbackConfigListReq 获取点众回传规则列表请求参数
type GetDzCallbackConfigListReq struct {
	g.Meta `path:"/getDzCallbackConfigList" tags:"小程序设置" method:"post" summary:"获取点众回传规则列表"`
	commonApi.Author
	model.GetDzCallbackConfigListReq
}

// GetDzCallbackConfigListRes 获取点众回传规则列表返回结果
type GetDzCallbackConfigListRes struct {
	commonApi.EmptyRes
	*model.GetDzCallbackConfigListRes
}

// GetDzBookInfoReq 获取点众短剧信息请求参数
type GetDzBookInfoReq struct {
	g.Meta `path:"/getDzBookInfo" tags:"小程序设置" method:"post" summary:"获取点众短剧信息"`
	commonApi.Author
	model.GetDzBookInfoReq
}

// GetDzBookInfoRes 获取点众短剧信息返回结果
type GetDzBookInfoRes struct {
	commonApi.EmptyRes
	*model.GetDzBookInfoRes
}

// SyncFqPromotionUrlTaskReq 同步番茄推广链接请求参数
type SyncFqPromotionUrlTaskReq struct {
	g.Meta `path:"/runSyncFqPromotionUrl" tags:"小程序设置" method:"post" summary:"同步番茄推广链接"`
	commonApi.Author
	model.SyncFqPromotionUrlReq
}

// SyncFqPromotionUrlTaskRes 同步番茄推广链接返回结果
type SyncFqPromotionUrlTaskRes struct {
	g.Meta `mime:"application/json"`
}

// SyncDzPromotionUrlTaskReq 同步点众推广链接请求参数
type SyncDzPromotionUrlTaskReq struct {
	g.Meta `path:"/runSyncDzPromotionUrl" tags:"小程序设置" method:"post" summary:"同步点众推广链接"`
	commonApi.Author
	model.SyncFqPromotionUrlReq
}

// SChannelVerifyAdvertiserIdReq 校验广告主ID是否绑定请求参数
type SChannelVerifyAdvertiserIdReq struct {
	g.Meta `path:"/verifyAdvertiserId" tags:"小程序设置" method:"post" summary:"校验广告主ID是否绑定"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds" v:"required#广告主ID列表必须"`
}

type SChannelCodesReq struct {
	g.Meta `path:"/getByChannelCodes" tags:"小程序设置" method:"post" summary:"通过codes查询"`
	commonApi.Author
	ChannelCodes []string `p:"channelCodes" v:"required#主键必须"` //通过主键删除
}

// SChannelBatchEditReq 渠道广告批量操作数
type SChannelBatchEditReq struct {
	g.Meta `path:"/batchEdit" tags:"小程序设置" method:"post" summary:"批量"`
	commonApi.Author
	*model.SChannelBatchEditReq
}

// SChannelBatchEditAdvertiserIdsReq 批量修改广告主ID
type SChannelBatchEditAdvertiserIdsReq struct {
	g.Meta `path:"/batchEditAdvertiserIds" tags:"小程序设置" method:"post" summary:"批量修改广告主ID"`
	commonApi.Author
	*model.SChannelBatchEditAdvertiserIdsReq
}

// SChannelEditDzReq 修改点众渠道
type SChannelEditDzReq struct {
	g.Meta `path:"/editDzChannel" tags:"小程序设置" method:"post" summary:"修改点众渠道"`
	commonApi.Author
	*model.SChannelEditDzReq
}

// AdAssetWechatAppletChannelReq 获取渠道导流链接请求参数
type AdAssetWechatAppletChannelReq struct {
	g.Meta `path:"/ad/channel/list" tags:"资产-微信小程序" method:"post" summary:"资产-获取渠道列表"`
	commonApi.Author
	model.AdAssetWechatAppletChannelReq
}

// AdAssetWechatAppletChannelRes 获取渠道导流链接返回结果
type AdAssetWechatAppletChannelRes struct {
	commonApi.EmptyRes
	*model.AdAssetWechatAppletChannelRes
}

// SChannelCodeGenerateReq 生成渠道号列表请求参数
type SChannelCodeGenerateReq struct {
	g.Meta `path:"/generateChannelCodeList" tags:"小程序设置" method:"post" summary:"生成渠道号列表"`
	commonApi.Author
	ChannelCode string `p:"channelCode" v:"required#渠道号必须"`
	Num         int    `p:"num" v:"required#数量必须"`
}

// SChannelCodeGenerateRes 生成渠道号列表返回结果
type SChannelCodeGenerateRes struct {
	g.Meta `mime:"application/json"`
	*model.GenerateChannelCodeListRes
}
