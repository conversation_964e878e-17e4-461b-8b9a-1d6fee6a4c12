// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-12 16:58:17
// 生成路径: internal/app/ad/logic/ad_material.go
// 生成人：cyao
// desc:素材主表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdMaterial(New())
}

func New() service.IAdMaterial {
	return &sAdMaterial{}
}

type sAdMaterial struct{}

func (s *sAdMaterial) List(ctx context.Context, req *model.AdMaterialSearchReq) (listRes *model.AdMaterialSearchRes, err error) {
	listRes = new(model.AdMaterialSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := systemService.Context().GetLoginUser(ctx)
		userIds, admin, _ := systemService.SysUser().GetMaterialContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})

		//userIds2, admin2, _ := systemService.SysUser().GetMaterialContainUser(ctx, &systemModel.ContextUser{
		//	LoginUserRes: &systemModel.LoginUserRes{
		//		Id:     userInfo.Id,
		//		DeptId: userInfo.DeptId,
		//	},
		//})

		// 取交集
		//if admin && admin2 {
		//	admin = true
		//} else if admin == true && admin2 == false {
		//	userIds = userIds2
		//} else if admin == false && admin2 == true {
		//	//userIds = userIds
		//} else {
		//	// 取交集
		//	var newUserIds = make([]int, 0)
		//	for _, v := range userIds {
		//		for _, v1 := range userIds2 {
		//			if v == v1 {
		//				newUserIds = append(newUserIds, v)
		//			}
		//		}
		//	}
		//	userIds = newUserIds
		//}

		m := dao.AdMaterial.Ctx(ctx).WithAll()
		if req.MaterialId != "" {
			m = m.Where(dao.AdMaterial.Columns().MaterialId+" = ?", req.MaterialId)
		}
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdMaterial.Columns().UserId, userIds)
		}
		if req.AlbumId > 0 {
			m = m.Where(dao.AdMaterial.Columns().AlbumId+" = ?", req.AlbumId)
		}
		if req.FileId > 0 {
			m = m.Where(dao.AdMaterial.Columns().FileId+" = ?", req.FileId)
		}
		if req.MaterialName != "" {
			m = m.Where(dao.AdMaterial.Columns().MaterialName+" like ?", "%"+req.MaterialName+"%")
		}
		if req.MaterialType != "" {
			m = m.Where(dao.AdMaterial.Columns().MaterialType+" = ?", req.MaterialType)
		}
		if req.UserId != "" {
			m = m.Where(dao.AdMaterial.Columns().UserId+" = ?", gconv.Int(req.UserId))
		}
		if req.FileUri != "" {
			m = m.Where(dao.AdMaterial.Columns().FileUri+" = ?", req.FileUri)
		}
		if req.ThumbnailUri != "" {
			m = m.Where(dao.AdMaterial.Columns().ThumbnailUri+" = ?", req.ThumbnailUri)
		}
		if req.Labels != "" {
			m = m.Where(dao.AdMaterial.Columns().Labels+" = ?", req.Labels)
		}
		if req.FileFormat != "" {
			m = m.Where(dao.AdMaterial.Columns().FileFormat+" = ?", req.FileFormat)
		}
		if req.FileSize != "" {
			m = m.Where(dao.AdMaterial.Columns().FileSize+" = ?", req.FileSize)
		}
		if req.Width != "" {
			m = m.Where(dao.AdMaterial.Columns().Width+" = ?", gconv.Int(req.Width))
		}
		if req.Height != "" {
			m = m.Where(dao.AdMaterial.Columns().Height+" = ?", gconv.Int(req.Height))
		}
		if req.VideoDuration != "" {
			m = m.Where(dao.AdMaterial.Columns().VideoDuration+" = ?", gconv.Int(req.VideoDuration))
		}
		if req.StartTime != "" {
			m = m.Where(dao.AdMaterial.Columns().CreatedAt+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where(dao.AdMaterial.Columns().CreatedAt+" <= ?", req.EndTime)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdMaterial.Columns().CreatedAt+" >=? AND "+dao.AdMaterial.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "material_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdMaterialListRes
		// 手动改造分页查询语句
		if req.SkipNum > 0 {
			err = m.Limit(req.SkipNum, req.PageSize).Order(order).Scan(&res)
		} else {
			err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		}
		//err = m.Limit((req.PageNum-1)*req.PageSize+req.SkipNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdMaterialListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdMaterialListRes{
				MaterialId:    v.MaterialId,
				AlbumId:       v.AlbumId,
				FileId:        v.FileId,
				MaterialName:  v.MaterialName,
				MaterialType:  v.MaterialType,
				UserId:        v.UserId,
				FileUri:       v.FileUri,
				ThumbnailUri:  v.ThumbnailUri,
				Labels:        v.Labels,
				FileFormat:    v.FileFormat,
				FileSize:      v.FileSize,
				Width:         v.Width,
				Height:        v.Height,
				Remark:        v.Remark,
				VideoDuration: v.VideoDuration,
				CreatedAt:     v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdMaterial) GetByMaterialId(ctx context.Context, materialId int) (res *model.AdMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterial.Ctx(ctx).WithAll().Where(dao.AdMaterial.Columns().MaterialId, materialId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		// 获取文件信息
		allPath, _ := service.AdMaterialFile().GetAllPath(ctx, res.FileId)
		res.FilePath = allPath.AllPath
		res.AlbumId = allPath.AlbumId
		if res != nil && res.AlbumId > 0 {
			album, innerErr := service.AdMaterialAlbum().GetByAlbumId(ctx, res.AlbumId)
			if innerErr != nil || album == nil {
				err = innerErr
				return
			}
			res.AlbumName = album.AlbumName
		}
		// 获取设计师名称
		if res.DesignUserId > 0 {
			user, _ := systemService.SysUser().GetUserById(ctx, uint64(res.DesignUserId))
			if user != nil && user.Id > 0 {
				res.DesignUser = user.UserName
			}
		}

	})
	return
}

func (s *sAdMaterial) BatchAdd(ctx context.Context, fileId int, batchAddReq []*model.AdMaterialAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询出当前的文件夹
		fileModel, innerError := service.AdMaterialFile().GetByFileId(ctx, fileId)
		liberr.ErrIsNil(ctx, innerError)
		if fileModel.FileId == 0 {
			err = errors.New("文件夹不存在！")
			return
		}
		//urlArray := gstr.Split(fileModel.Preview, "|") todo 这里的缩略图待定
		//if len(urlArray) == 4 {
		//	urlArray[0] = req.ThumbnailUri
		//} else {
		//	urlArray = append(urlArray, req.ThumbnailUri)
		//}
		//fileModel.Preview = gstr.Join(urlArray, "|")
		fileModel.MaterialNum += len(batchAddReq)
		innerError = service.AdMaterialFile().Edit(ctx, &model.AdMaterialFileEditReq{
			FileId:      fileModel.FileId,
			FileName:    fileModel.FileName,
			UserId:      fileModel.UserId,
			AlbumId:     fileModel.UserId,
			ParentId:    fileModel.ParentId,
			Remark:      fileModel.Remark,
			AllPath:     fileModel.AllPath,
			Preview:     fileModel.Preview,
			MaterialNum: fileModel.MaterialNum,
		})
		_, innerError = dao.AdMaterial.Ctx(ctx).Save(batchAddReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func AddThumbnailUri(ctx context.Context, fileId int, thumbnailUri string) int {
	fileModel, innerError := service.AdMaterialFile().GetByFileId(ctx, fileId)
	liberr.ErrIsNil(ctx, innerError)
	if fileModel.FileId == 0 {
		return -1
	}
	urlArray := make([]string, 0)
	if len(fileModel.Preview) > 0 {
		urlArray = gstr.Split(fileModel.Preview, "|")
	}
	if len(urlArray) == 4 {
		urlArray[0] = thumbnailUri
	} else {
		urlArray = append(urlArray, thumbnailUri)
	}
	fileModel.Preview = gstr.Join(urlArray, "|")
	fileModel.MaterialNum += 1
	innerError = service.AdMaterialFile().Edit(ctx, &model.AdMaterialFileEditReq{
		FileId:      fileModel.FileId,
		FileName:    fileModel.FileName,
		UserId:      fileModel.UserId,
		AlbumId:     fileModel.AlbumId,
		ParentId:    fileModel.ParentId,
		Remark:      fileModel.Remark,
		AllPath:     fileModel.AllPath,
		Preview:     fileModel.Preview,
		MaterialNum: fileModel.MaterialNum,
	})
	return fileModel.ParentId
}

func DelThumbnailUri(ctx context.Context, fileId int, thumbnailUri string) int {
	fileModel, innerError := service.AdMaterialFile().GetByFileId(ctx, fileId)
	liberr.ErrIsNil(ctx, innerError)
	if fileModel.FileId == 0 {
		return -1
	}
	urlArray := make([]string, 0)
	if len(fileModel.Preview) > 0 {
		urlArray = gstr.Split(fileModel.Preview, "|")
	}
	if len(urlArray) > 0 {
		for i, url := range urlArray {
			if url == thumbnailUri {
				urlArray = append(urlArray[:i], urlArray[i+1:]...)
				fileModel.Preview = gstr.Join(urlArray, "|")
				break
			}
		}
	}
	fileModel.MaterialNum -= 1
	innerError = service.AdMaterialFile().Edit(ctx, &model.AdMaterialFileEditReq{
		FileId:      fileModel.FileId,
		FileName:    fileModel.FileName,
		UserId:      fileModel.UserId,
		AlbumId:     fileModel.AlbumId,
		ParentId:    fileModel.ParentId,
		Remark:      fileModel.Remark,
		AllPath:     fileModel.AllPath,
		Preview:     fileModel.Preview,
		MaterialNum: fileModel.MaterialNum,
	})
	return fileModel.ParentId
}

func (s *sAdMaterial) Add(ctx context.Context, req *model.AdMaterialAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询出当前的文件夹
		pid := req.FileId
		for {
			if pid <= 0 {
				break
			}
			pid = AddThumbnailUri(ctx, pid, req.ThumbnailUri)
		}
		//fileModel, innerError := service.AdMaterialFile().GetByFileId(ctx, req.FileId)
		//liberr.ErrIsNil(ctx, innerError)
		//if fileModel.FileId == 0 {
		//	err = errors.New("文件夹不存在！")
		//	return
		//}
		//urlArray := make([]string, 0)
		//if len(fileModel.Preview) > 0 {
		//	urlArray = gstr.Split(fileModel.Preview, "|")
		//}
		//if len(urlArray) == 4 {
		//	urlArray[0] = req.ThumbnailUri
		//} else {
		//	urlArray = append(urlArray, req.ThumbnailUri)
		//}
		//fileModel.Preview = gstr.Join(urlArray, "|")
		//fileModel.MaterialNum += 1
		//innerError = service.AdMaterialFile().Edit(ctx, &model.AdMaterialFileEditReq{
		//	FileId:      fileModel.FileId,
		//	FileName:    fileModel.FileName,
		//	UserId:      fileModel.UserId,
		//	AlbumId:     fileModel.UserId,
		//	ParentId:    fileModel.ParentId,
		//	Remark:      fileModel.Remark,
		//	AllPath:     fileModel.AllPath,
		//	Preview:     fileModel.Preview,
		//	MaterialNum: fileModel.MaterialNum,
		//})
		_, err = dao.AdMaterial.Ctx(ctx).Insert(do.AdMaterial{
			//MaterialId:    req.MaterialId,
			AlbumId:       req.AlbumId,
			FileId:        req.FileId,
			MaterialName:  req.MaterialName,
			MaterialType:  req.MaterialType,
			DesignUserId:  req.DesignUserId,
			UserId:        req.UserId,
			FileUri:       req.FileUri,
			ThumbnailUri:  req.ThumbnailUri,
			Labels:        req.Labels,
			FileFormat:    req.FileFormat,
			FileSize:      req.FileSize,
			Width:         req.Width,
			Height:        req.Height,
			Remark:        req.Remark,
			VideoDuration: req.VideoDuration,
			ManageStatus:  req.ManageStatus,
			CreatedAt:     gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdMaterial) Edit(ctx context.Context, req *model.AdMaterialEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterial.Ctx(ctx).WherePri(req.MaterialId).Update(do.AdMaterial{
			MaterialId:    req.MaterialId,
			AlbumId:       req.AlbumId,
			FileId:        req.FileId,
			MaterialName:  req.MaterialName,
			MaterialType:  req.MaterialType,
			UserId:        req.UserId,
			DesignUserId:  req.DesignUserId,
			FileUri:       req.FileUri,
			ThumbnailUri:  req.ThumbnailUri,
			Labels:        req.Labels,
			FileFormat:    req.FileFormat,
			FileSize:      req.FileSize,
			Width:         req.Width,
			Height:        req.Height,
			Remark:        req.Remark,
			VideoDuration: req.VideoDuration,
			ManageStatus:  req.ManageStatus,
			UpdatedAt:     gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdMaterial) Delete(ctx context.Context, materialIds []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		delModel := make([]model.AdMaterialInfoRes, 0)
		err = dao.AdMaterial.Ctx(ctx).WhereIn(dao.AdMaterial.Columns().MaterialId, materialIds).Scan(&delModel)
		if err == nil {
			for _, item := range delModel {
				pid := item.FileId
				for {
					if pid <= 0 {
						break
					}
					pid = DelThumbnailUri(ctx, pid, item.ThumbnailUri)
				}
			}
			_, err = dao.AdMaterial.Ctx(ctx).Delete(dao.AdMaterial.Columns().MaterialId+" in (?)", materialIds)
			liberr.ErrIsNil(ctx, err, "删除失败")
		}
	})
	return
}
