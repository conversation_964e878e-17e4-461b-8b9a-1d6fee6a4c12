package model

import (
	"github.com/smartwalle/alipay/v3"
	"io"
)

// ali的生活号接口相关的model

type AliUploadReq struct {
	//Name     string    `json:"name"`     // 默认 ： file_content
	FileName string    `json:"filename"` //eg a.jpg
	Reader   io.Reader `json:"reader"`
}
type AliUploadRes struct {
	alipay.Error
	ExternId string `json:"extern_id"`
	FileId   string `json:"file_id"`
}

//type AutoGenerated struct {
//	PermissionStatus  string `json:"permission_status"`
//	SourcePublishDate string `json:"source_publish_date"`
//	SourceContent     string `json:"source_content"`
//
//	SourceAuthor string `json:"source_author"`
//	SourceType   string `json:"source_type"`
//	SourceOffers []struct {
//		OfferID   string `json:"offer_id"`
//		OfferType string `json:"offer_type"`
//	} `json:"source_offers"`
//	SourceLink  string `json:"source_link"`
//	SourceTitle string `json:"source_title"`
//	PublicID    string `json:"public_id"`
//}

type AliContentPublishReq struct {
	SourceTitle       string            `json:"source_title"`
	SourceType        string            `json:"source_type"`    //内容类型（1、短图文；2、视频）
	SourceContent     string            `json:"source_content"` // 理解为短图文/视频的文本部分，仅支持纯文本
	SourceSummary     string            `json:"source_summary"` // 文章摘要；预留字段，当前不对用户透出
	SourceAuthor      string            `json:"source_author"  dc:"作者；预留字段，当前不对用户透出"`
	SourcePublishDate string            `json:"source_publish_date"  dc:"文章发布时间 仅支持 yyyy-MM-dd HH:mm:ss 格式"`
	SourceMediaInfos  []SourceMediaInfo `json:"source_media_infos"`
}

type SourceMediaInfo struct {
	MediaType string `json:"media_type"` //素材ID，对应“支付宝文件上传接口”获取的file_id
	MediaID   string `json:"media_id"`   //素材类型。 image：图片 video：视频 cover_static：静态封面
}

type AliContentPublishRes struct {
	alipay.Error
	ContentId string `json:"content_id"`
}

type AliContentStatusQueryReq struct {
	ContentId string `json:"content_id"`
	PublicID  string `json:"public_id"`
}

type AliContentDeleteReq struct {
	ContentId string `json:"content_id"`
	PublicID  string `json:"public_id"`
}

type AliContentStatusQueryRes struct {
	Code              string   `json:"code"`
	Msg               string   `json:"msg"`
	ContentID         string   `json:"content_id"`
	SourceTitle       string   `json:"source_title"`
	SourceAuthor      string   `json:"source_author"`
	SourceSummary     string   `json:"source_summary"`
	SourceType        string   `json:"source_type"`
	SourceContent     string   `json:"source_content"`
	SourcePublishDate string   `json:"source_publish_date"`
	SourceLink        string   `json:"source_link"`
	Link              string   `json:"link"`
	SpecialTags       []string `json:"special_tags"`
	SourceMediaInfos  []struct {
		MediaID   string `json:"media_id"`
		MediaType string `json:"media_type"`
	} `json:"source_media_infos"`
	SourceOffers []struct {
		OfferID   string `json:"offer_id"`
		OfferType string `json:"offer_type"`
	} `json:"source_offers"`
	SourceStatus     string `json:"source_status"`
	PermissionStatus string `json:"permission_status"`
}

type AliContentDeleteRes struct {
	alipay.Error
}

type AliContentBatchQueryReq struct {
	PublicID   string `json:"public_id"`
	NeedDetail bool   `json:"need_detail"`
	Status     string `json:"status"`
	PageNum    int    `json:"page_num"`
	PageSize   int    `json:"page_size"`
}

type AliContentBatchQueryRes struct {
	Code           string   `json:"code"`
	Msg            string   `json:"msg"`
	ContentIds     []string `json:"content_ids"`
	ContentDetails []struct {
		ContentID         string `json:"content_id"`
		SourceTitle       string `json:"source_title"`
		SourceAuthor      string `json:"source_author"`
		SourceSummary     string `json:"source_summary"`
		SourceType        string `json:"source_type"`
		SourcePublishDate string `json:"source_publish_date"`
		SourceLink        string `json:"source_link"`
		Link              string `json:"link"`
		SourceMediaInfos  []struct {
			MediaID   string `json:"media_id"`
			MediaType string `json:"media_type"`
		} `json:"source_media_infos"`
		SourceOffers []struct {
			OfferID   string `json:"offer_id"`
			OfferType string `json:"offer_type"`
		} `json:"source_offers"`
		SpecialTags      []string `json:"special_tags"`
		SourceStatus     string   `json:"source_status"`
		PermissionStatus string   `json:"permission_status"`
	} `json:"content_details"`
	PageNum  int `json:"page_num"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}
