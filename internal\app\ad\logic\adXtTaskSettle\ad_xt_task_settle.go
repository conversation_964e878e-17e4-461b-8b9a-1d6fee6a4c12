// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: internal/app/ad/logic/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdXtTaskSettle(New())
}

func New() service.IAdXtTaskSettle {
	return &sAdXtTaskSettle{}
}

type sAdXtTaskSettle struct{}

func (s *sAdXtTaskSettle) List(ctx context.Context, req *model.AdXtTaskSettleSearchReq) (listRes *model.AdXtTaskSettleSearchRes, err error) {
	listRes = new(model.AdXtTaskSettleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtTaskSettle.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().TaskId+" = ?", req.TaskId)
		}
		if len(req.TaskIds) > 0 {
			m = m.WhereIn(dao.AdXtTaskSettle.Columns().TaskId, req.TaskIds)
		}
		if req.ItemId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ItemId+" = ?", gconv.Int64(req.ItemId))
		}
		if req.AuthorId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AuthorId+" = ?", gconv.Int64(req.AuthorId))
		}
		if req.Uid != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Uid+" = ?", gconv.Int64(req.Uid))
		}
		if req.ProviderId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ProviderId+" = ?", gconv.Int64(req.ProviderId))
		}
		if req.Title != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Title+" = ?", req.Title)
		}
		if req.Link != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Link+" = ?", req.Link)
		}
		if req.AuthorNickname != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AuthorNickname+" like ?", "%"+req.AuthorNickname+"%")
		}
		if req.ReleaseTime != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ReleaseTime+" = ?", gconv.Int(req.ReleaseTime))
		}
		if req.AndroidActivateCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AndroidActivateCnt+" = ?", gconv.Int(req.AndroidActivateCnt))
		}
		if req.IosActivateCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().IosActivateCnt+" = ?", gconv.Int(req.IosActivateCnt))
		}
		if req.CommentCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().CommentCnt+" = ?", gconv.Int(req.CommentCnt))
		}
		if req.LikeCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().LikeCnt+" = ?", gconv.Int(req.LikeCnt))
		}
		if req.ValidLikeCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ValidLikeCnt+" = ?", gconv.Int(req.ValidLikeCnt))
		}
		if req.PlayVv != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().PlayVv+" = ?", gconv.Int(req.PlayVv))
		}
		if req.ValidPlayVv != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ValidPlayVv+" = ?", gconv.Int(req.ValidPlayVv))
		}
		if req.ShareCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ShareCnt+" = ?", gconv.Int(req.ShareCnt))
		}
		if req.PromoteCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().PromoteCnt+" = ?", gconv.Int(req.PromoteCnt))
		}
		if req.ComponentClickCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ComponentClickCnt+" = ?", gconv.Int(req.ComponentClickCnt))
		}
		if req.RelevanceAuditResult != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().RelevanceAuditResult+" = ?", gconv.Int(req.RelevanceAuditResult))
		}
		if req.RewardLevel != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().RewardLevel+" = ?", gconv.Int(req.RewardLevel))
		}
		var summary = new(model.AdXtTaskSettleSummary)
		m.Fields(" SUM(CAST(est_sales->>'$.value' AS UNSIGNED)) as estSales  ,SUM(CAST(settle_ad_share->>'$.value' AS UNSIGNED)) as settleAdShare ,SUM(CAST(est_ad_cost->>'$.value' AS UNSIGNED)) as estAdCost , SUM(CAST(settle_cps->>'$.value' AS UNSIGNED)) as settleCps").
			FieldSum(dao.AdXtTaskSettle.Columns().IapCostHour, "iaaCostHour").
			FieldSum(dao.AdXtTaskSettle.Columns().IapCostHour, "iapCostHour").
			Scan(&summary)
		listRes.Summary = summary

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.AdXtTaskSettleListRes
		err = m.Fields("id",
			"advertiser_id",
			"task_id",
			"item_id",
			"author_id",
			"uid",
			"provider_id",
			"title",
			"link",
			"author_nickname",
			"release_time",
			"android_activate_cnt",
			"ios_activate_cnt",
			"comment_cnt",
			"like_cnt",
			"valid_like_cnt",
			"play_vv",
			"valid_play_vv",
			"share_cnt",
			"promote_cnt",
			"component_click_cnt",
			"iaa_cost_hour",
			"iap_cost_hour",
			"reward_amount",
			"relevance_audit_result",
			"reward_level",
			"item_info_daily_list").Fields("  CAST(est_sales->>'$.value' AS UNSIGNED) as estSales  ,CAST(settle_ad_share->>'$.value' AS UNSIGNED) as settleAdShare ,CAST(est_ad_cost->>'$.value' AS UNSIGNED) as estAdCost , CAST(settle_cps->>'$.value' AS UNSIGNED) as settleCps, CAST(play->>'$.value' AS UNSIGNED) as play").Page(req.PageNum, req.PageSize).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdXtTaskSettleListRes, len(res))
		tids := make([]string, 0)
		aidList := make([]string, 0)
		for k, v := range res {
			tids = append(tids, v.TaskId)
			aidList = append(aidList, v.AdvertiserId)
			listRes.List[k] = &model.AdXtTaskSettleListRes{
				Id:                   v.Id,
				AdvertiserId:         v.AdvertiserId,
				TaskId:               v.TaskId,
				ItemId:               v.ItemId,
				AuthorId:             v.AuthorId,
				Uid:                  v.Uid,
				ProviderId:           v.ProviderId,
				Title:                v.Title,
				Link:                 v.Link,
				AuthorNickname:       v.AuthorNickname,
				ReleaseTime:          v.ReleaseTime,
				AndroidActivateCnt:   v.AndroidActivateCnt,
				IosActivateCnt:       v.IosActivateCnt,
				CommentCnt:           v.CommentCnt,
				LikeCnt:              v.LikeCnt,
				ValidLikeCnt:         v.ValidLikeCnt,
				PlayVv:               v.PlayVv,
				ValidPlayVv:          v.ValidPlayVv,
				ShareCnt:             v.ShareCnt,
				PromoteCnt:           v.PromoteCnt,
				ComponentClickCnt:    v.ComponentClickCnt,
				EstAdCost:            v.EstAdCost,
				EstSales:             v.EstSales,
				Play:                 v.Play,
				SettleAdShare:        v.SettleAdShare,
				SettleCps:            v.SettleCps,
				IaaCostHour:          v.IaaCostHour,
				IapCostHour:          v.IapCostHour,
				RewardAmount:         v.RewardAmount,
				RelevanceAuditResult: v.RelevanceAuditResult,
				RewardLevel:          v.RewardLevel,
				ItemInfoDailyList:    v.ItemInfoDailyList,
			}
		}
		aList, _ := service.AdXtAccount().GetByAIds(ctx, aidList)
		tList, _ := service.AdXtTask().GetByTaskIds(ctx, tids)
		for _, item := range listRes.List {
			for _, adInfo := range tList {
				if item.TaskId == adInfo.TaskId {
					item.TaskName = adInfo.TaskName
				}
			}
			for _, adInfo := range aList {
				if item.AdvertiserId == adInfo.AdvertiserId {
					item.AdvertiserNick = adInfo.AdvertiserNick
				}
			}
		}
	})
	return
}

func (s *sAdXtTaskSettle) GetExportData(ctx context.Context, req *model.AdXtTaskSettleSearchReq) (listRes []*model.AdXtTaskSettleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtTaskSettle.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.ItemId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ItemId+" = ?", gconv.Int64(req.ItemId))
		}
		if req.AuthorId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AuthorId+" = ?", gconv.Int64(req.AuthorId))
		}
		if req.Uid != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Uid+" = ?", gconv.Int64(req.Uid))
		}
		if req.ProviderId != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ProviderId+" = ?", gconv.Int64(req.ProviderId))
		}
		if req.Title != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Title+" = ?", req.Title)
		}
		if req.Link != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().Link+" = ?", req.Link)
		}
		if req.AuthorNickname != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AuthorNickname+" like ?", "%"+req.AuthorNickname+"%")
		}
		if req.ReleaseTime != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ReleaseTime+" = ?", gconv.Int(req.ReleaseTime))
		}
		if req.AndroidActivateCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().AndroidActivateCnt+" = ?", gconv.Int(req.AndroidActivateCnt))
		}
		if req.IosActivateCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().IosActivateCnt+" = ?", gconv.Int(req.IosActivateCnt))
		}
		if req.CommentCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().CommentCnt+" = ?", gconv.Int(req.CommentCnt))
		}
		if req.LikeCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().LikeCnt+" = ?", gconv.Int(req.LikeCnt))
		}
		if req.ValidLikeCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ValidLikeCnt+" = ?", gconv.Int(req.ValidLikeCnt))
		}
		if req.PlayVv != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().PlayVv+" = ?", gconv.Int(req.PlayVv))
		}
		if req.ValidPlayVv != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ValidPlayVv+" = ?", gconv.Int(req.ValidPlayVv))
		}
		if req.ShareCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ShareCnt+" = ?", gconv.Int(req.ShareCnt))
		}
		if req.PromoteCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().PromoteCnt+" = ?", gconv.Int(req.PromoteCnt))
		}
		if req.ComponentClickCnt != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().ComponentClickCnt+" = ?", gconv.Int(req.ComponentClickCnt))
		}
		if req.RelevanceAuditResult != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().RelevanceAuditResult+" = ?", gconv.Int(req.RelevanceAuditResult))
		}
		if req.RewardLevel != "" {
			m = m.Where(dao.AdXtTaskSettle.Columns().RewardLevel+" = ?", gconv.Int(req.RewardLevel))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		err = m.Fields("id",
			"advertiser_id",
			"task_id",
			"item_id",
			"author_id",
			"uid",
			"provider_id",
			"title",
			"link",
			"author_nickname",
			"release_time",
			"android_activate_cnt",
			"ios_activate_cnt",
			"comment_cnt",
			"like_cnt",
			"valid_like_cnt",
			"play_vv",
			"valid_play_vv",
			"share_cnt",
			"promote_cnt",
			"component_click_cnt",
			"iaa_cost_hour",
			"iap_cost_hour",
			"reward_amount",
			"relevance_audit_result",
			"reward_level",
			"item_info_daily_list").Fields("  CAST(est_sales->>'$.value' AS UNSIGNED) as estSales  ,CAST(settle_ad_share->>'$.value' AS UNSIGNED) as settleAdShare ,CAST(est_ad_cost->>'$.value' AS UNSIGNED) as estAdCost , CAST(settle_cps->>'$.value' AS UNSIGNED) as settleCps, CAST(play->>'$.value' AS UNSIGNED) as play").Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdXtTaskSettle) GetById(ctx context.Context, id int64) (res *model.AdXtTaskSettleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtTaskSettle.Ctx(ctx).WithAll().Where(dao.AdXtTaskSettle.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetByItemIds
func (s *sAdXtTaskSettle) GetByItemIds(ctx context.Context, ids []int64) (res []*model.AdXtTaskSettleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtTaskSettle.Ctx(ctx).WithAll().Where(dao.AdXtTaskSettle.Columns().ItemId+" in(?)", ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtTaskSettle) Add(ctx context.Context, req *model.AdXtTaskSettleAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTaskSettle.Ctx(ctx).Insert(do.AdXtTaskSettle{
			AdvertiserId:         req.AdvertiserId,
			TaskId:               req.TaskId,
			ItemId:               req.ItemId,
			AuthorId:             req.AuthorId,
			Uid:                  req.Uid,
			ProviderId:           req.ProviderId,
			Title:                req.Title,
			Link:                 req.Link,
			AuthorNickname:       req.AuthorNickname,
			ReleaseTime:          req.ReleaseTime,
			AndroidActivateCnt:   req.AndroidActivateCnt,
			IosActivateCnt:       req.IosActivateCnt,
			CommentCnt:           req.CommentCnt,
			LikeCnt:              req.LikeCnt,
			ValidLikeCnt:         req.ValidLikeCnt,
			PlayVv:               req.PlayVv,
			ValidPlayVv:          req.ValidPlayVv,
			ShareCnt:             req.ShareCnt,
			PromoteCnt:           req.PromoteCnt,
			ComponentClickCnt:    req.ComponentClickCnt,
			EstAdCost:            req.EstAdCost,
			EstSales:             req.EstSales,
			Play:                 req.Play,
			SettleAdShare:        req.SettleAdShare,
			SettleCps:            req.SettleCps,
			IaaCostHour:          req.IaaCostHour,
			IapCostHour:          req.IapCostHour,
			RewardAmount:         req.RewardAmount,
			RelevanceAuditResult: req.RelevanceAuditResult,
			RewardLevel:          req.RewardLevel,
			ItemInfoDailyList:    req.ItemInfoDailyList,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdXtTaskSettle) Edit(ctx context.Context, req *model.AdXtTaskSettleEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTaskSettle.Ctx(ctx).WherePri(req.Id).Update(do.AdXtTaskSettle{
			AdvertiserId:         req.AdvertiserId,
			TaskId:               req.TaskId,
			ItemId:               req.ItemId,
			AuthorId:             req.AuthorId,
			Uid:                  req.Uid,
			ProviderId:           req.ProviderId,
			Title:                req.Title,
			Link:                 req.Link,
			AuthorNickname:       req.AuthorNickname,
			ReleaseTime:          req.ReleaseTime,
			AndroidActivateCnt:   req.AndroidActivateCnt,
			IosActivateCnt:       req.IosActivateCnt,
			CommentCnt:           req.CommentCnt,
			LikeCnt:              req.LikeCnt,
			ValidLikeCnt:         req.ValidLikeCnt,
			PlayVv:               req.PlayVv,
			ValidPlayVv:          req.ValidPlayVv,
			ShareCnt:             req.ShareCnt,
			PromoteCnt:           req.PromoteCnt,
			ComponentClickCnt:    req.ComponentClickCnt,
			EstAdCost:            req.EstAdCost,
			EstSales:             req.EstSales,
			Play:                 req.Play,
			SettleAdShare:        req.SettleAdShare,
			SettleCps:            req.SettleCps,
			IaaCostHour:          req.IaaCostHour,
			IapCostHour:          req.IapCostHour,
			RewardAmount:         req.RewardAmount,
			RelevanceAuditResult: req.RelevanceAuditResult,
			RewardLevel:          req.RewardLevel,
			ItemInfoDailyList:    req.ItemInfoDailyList,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdXtTaskSettle) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTaskSettle.Ctx(ctx).Delete(dao.AdXtTaskSettle.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdXtTaskSettle) Pull(ctx context.Context, aid string) (count int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 根据id 获取task 任务的列表
		idList := make([]model.AdAndTaskId, 0)
		err = dao.AdXtTask.Ctx(ctx).Where(dao.AdXtTask.Columns().AdvertiserId, aid).Fields(dao.AdXtTask.Columns().AdvertiserId, dao.AdXtTask.Columns().TaskId).Fields(dao.AdXtTask.Columns().AdvertiserId).Scan(&idList)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		token, err := service.AdXtAccount().GetAccessTokenByAdvertiserId(ctx, aid)
		liberr.ErrIsNil(ctx, err, "获取token失败")
		xtAdService := new(model.XTAdKSService)
		g.Cfg().MustGet(context.Background(), "advertiser.xt").Scan(xtAdService)
		appConfig, _ := service.AdAppConfig().GetByAppId(ctx, strconv.FormatInt(xtAdService.AppId, 10))
		if appConfig == nil {
			err = errors.New("应用不存在")
			liberr.ErrIsNil(ctx, err)
		}
		for _, adAndTaskId := range idList {
			g.Log().Info(ctx, fmt.Sprintf("-------------  执行Pull开始: taskId:%v aid:%v -------------------", adAndTaskId, aid))
			pageNo := int32(1)
			for {
				taskResponse, err1 := advertiser.GetToutiaoApiClient().StarDemandOmGetChallengeItemsDataV2ApiService.AccessToken(token).
					SetRequest(toutiaoModel.ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest{
						StarId:          gconv.Int64(aid),
						ChallengeTaskId: gconv.Int64(adAndTaskId.TaskId),
						Page:            pageNo,
						Limit:           30,
					}).Do()
				if err1 != nil {
					if strings.Contains(err1.Error(), "Too many requests") {
						time.Sleep(time.Second * 1)
						taskResponse, err1 = advertiser.GetToutiaoApiClient().StarDemandOmGetChallengeItemsDataV2ApiService.AccessToken(token).
							SetRequest(toutiaoModel.ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest{
								StarId:          gconv.Int64(aid),
								ChallengeTaskId: gconv.Int64(adAndTaskId.TaskId),
								Page:            pageNo,
								Limit:           30,
							}).Do()
						if err1 != nil {
							liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取StarDemandOmGetChallengeItemsDataV2ApiService失败: %v", err1))
						}
					}
				}
				if taskResponse == nil || taskResponse.Data == nil {
					g.Log().Info(ctx, fmt.Sprintf("获取数据为空aid:%v,pageNo:%v", aid, pageNo))
					break
				}
				if len(taskResponse.Data.DataList) > 0 {
					taskSettleList := make([]*model.AdXtTaskSettleAddReq, 0)
					taskSettleDailyList := make([]*model.AdXtTaskSettleDailyAddReq, 0)
					for _, item := range taskResponse.Data.DataList {
						count++
						tModel := &model.AdXtTaskSettleAddReq{
							AdvertiserId:       adAndTaskId.AdvertiserId,
							TaskId:             adAndTaskId.TaskId,
							ItemId:             item.ItemId,
							AuthorId:           item.AuthorId,
							Uid:                item.Uid,
							ProviderId:         item.ProviderId,
							Title:              item.Title,
							Link:               item.Link,
							AuthorNickname:     item.AuthorNickname,
							ReleaseTime:        item.ReleaseTime,
							AndroidActivateCnt: item.AndroidActivateCnt,
							IosActivateCnt:     item.IosActivateCnt,
							CommentCnt:         item.CommentCnt,
							LikeCnt:            item.LikeCnt,
							ValidLikeCnt:       item.ValidLikeCnt,
							PlayVv:             item.PlayVv,
							ValidPlayVv:        item.ValidPlayVv,
							ShareCnt:           item.ShareCnt,
							PromoteCnt:         item.PromoteCnt,
							ComponentClickCnt:  item.ComponentClickCnt,
							//Play:                 item.Play,
							//EstAdCost:            item.EstAdCost,
							//EstSales:             item.EstSales,
							//SettleAdShare:        item.SettleAdShare,
							//SettleCps:            item.SettleCps,
							IaaCostHour:          float64(item.IaaCostHour),
							IapCostHour:          float64(item.IapCostHour),
							RewardAmount:         float64(item.RewardAmount),
							RelevanceAuditResult: item.RelevanceAuditResult,
							RewardLevel:          item.RewardLevel,
						}
						if len(item.Play) > 0 {
							tModel.Play = &item.Play
						}
						if len(item.EstAdCost) > 0 {
							tModel.EstAdCost = &item.EstAdCost
						}
						if len(item.EstSales) > 0 {
							tModel.EstSales = &item.EstSales
						}
						if len(item.SettleAdShare) > 0 {
							tModel.SettleAdShare = &item.SettleAdShare
						}
						if len(item.SettleCps) > 0 {
							tModel.SettleCps = &item.SettleCps
						}
						taskSettleList = append(taskSettleList, tModel)
						if item.ItemInfoList != nil && len(item.ItemInfoList) > 0 {
							for _, itemInfo := range item.ItemInfoList {
								taskSettleDailyList = append(taskSettleDailyList, &model.AdXtTaskSettleDailyAddReq{
									AdvertiserId:  adAndTaskId.AdvertiserId,
									TaskId:        adAndTaskId.TaskId,
									ItemId:        item.ItemId,
									AuthorId:      item.AuthorId,
									Uid:           item.Uid,
									ProviderId:    item.ProviderId,
									PDate:         gtime.NewFromStrFormat(itemInfo.PDate, "20060102"),
									EstSales:      itemInfo.EstSales,
									SettleCps:     itemInfo.SettleCps,
									EstAdCost:     itemInfo.EstAdCost,
									SettleAdShare: itemInfo.SettleAdShare,
								})
							}
						}
					}
					// 批量插入数据
					if len(taskSettleList) > 0 {
						_, err = dao.AdXtTaskSettle.Ctx(ctx).Save(taskSettleList)
						if err != nil {
							g.Log().Error(ctx, "AdXtTaskSettle Save(taskSettleList) 添加失败")
						}
					}
					if len(taskSettleDailyList) > 0 {
						_, err = dao.AdXtTaskSettleDaily.Ctx(ctx).Save(taskSettleDailyList)
						if err != nil {
							g.Log().Error(ctx, "AdXtTaskSettleDaily Save(taskSettleDailyList) 添加失败")
						}
					}
				} else {
					break
				}
				if !*taskResponse.Data.PageInfo.HasMore {
					break
				}
				pageNo++
			}
			g.Log().Info(ctx, fmt.Sprintf("-------------  执行PullEnd: taskId:%v aid:%v -------------------", adAndTaskId, aid))
		}

	})
	return
}

func (s *sAdXtTaskSettle) PullDetail(ctx context.Context, aid string) (err error) {
	channelRechargeStatKey := model.XTAdCoreDetailDataTask + ":" + aid
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullDate ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = g.Try(innerCtx, func(innerCtx context.Context) {
		accountList, _ := service.AdXtAccount().List(innerCtx, &model.AdXtAccountSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: 999999,
			},
			AdvertiserId: aid,
		})
		// 获取最新的token
		for _, item := range accountList.List {
			item.AccessToken, _ = service.AdXtAccount().GetAccessTokenByAdvertiserId(innerCtx, item.AdvertiserId)
			// 获取taskList
			taskList, err := service.AdXtTaskSettle().List(innerCtx, &model.AdXtTaskSettleSearchReq{
				PageReq: comModel.PageReq{
					PageNum:   1,
					PageSize:  100,
					OrderType: "desc",
					OrderBy:   "estAdCost",
				},
				AdvertiserId: item.AdvertiserId,
			})
			idList := make(map[string][]string, 0)
			for _, taskItem := range taskList.List {
				// 判断taskItem.aid 是否在key中
				if _, ok := idList[taskItem.AdvertiserId]; ok {
					idList[taskItem.AdvertiserId] = append(idList[taskItem.AdvertiserId], taskItem.TaskId)
				} else {
					idList[taskItem.AdvertiserId] = []string{taskItem.TaskId}
				}

			}
			for advId, iList := range idList {
				err = s.SaveAdXtTaskSettleByTaskId(innerCtx, item.AccessToken, advId, iList)
			}
			if err != nil {
				g.Log().Error(ctx, err, "保存AdXtTaskSettleByTaskId失败")
			}
		}
	})
	return
}

func (s *sAdXtTaskSettle) SaveAdXtTaskSettleByTaskId(ctx context.Context, token, aid string, idList []string) (err error) {
	for _, taskId := range idList {
		//判断redis 是否今天已经刷新过数据
		key := model.XTAdCoreDetailDataTask + ":" + aid + ":" + taskId
		ready := commonService.GetGoRedis().Get(ctx, key).Val()
		if len(ready) > 0 {
			continue
		}
		pageNo := int32(1)
		for {
			taskResponse, err1 := advertiser.GetToutiaoApiClient().StarDemandOmGetChallengeItemsDataV2ApiService.AccessToken(token).
				SetRequest(toutiaoModel.ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest{
					StarId:          gconv.Int64(aid),
					ChallengeTaskId: gconv.Int64(taskId),
					Page:            pageNo,
					Limit:           30,
				}).Do()

			if err1 != nil && (strings.Contains(err1.Error(), "Too many requests") || strings.Contains(err1.Error(), "Internal service timed out")) {
				time.Sleep(time.Second * 1)
				taskResponse, err1 = advertiser.GetToutiaoApiClient().StarDemandOmGetChallengeItemsDataV2ApiService.AccessToken(token).
					SetRequest(toutiaoModel.ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest{
						StarId:          gconv.Int64(aid),
						ChallengeTaskId: gconv.Int64(taskId),
						Page:            pageNo,
						Limit:           30,
					}).Do()
				if err1 != nil {
					liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取StarDemandOmGetChallengeItemsDataV2ApiService失败: %v", err1))
				}
			}
			if err1 != nil {
				liberr.ErrIsNil(ctx, err1, fmt.Sprintf("获取StarDemandOmGetChallengeItemsDataV2ApiService失败: %v", err1))
			}

			if taskResponse != nil && taskResponse.Data != nil && len(taskResponse.Data.DataList) > 0 {
				taskSettleList := make([]*model.AdXtTaskSettleAddReq, 0)
				taskSettleDailyList := make([]*model.AdXtTaskSettleDailyAddReq, 0)
				for _, item := range taskResponse.Data.DataList {

					tModel := &model.AdXtTaskSettleAddReq{
						AdvertiserId:       aid,
						TaskId:             taskId,
						ItemId:             item.ItemId,
						AuthorId:           item.AuthorId,
						Uid:                item.Uid,
						ProviderId:         item.ProviderId,
						Title:              item.Title,
						Link:               item.Link,
						AuthorNickname:     item.AuthorNickname,
						ReleaseTime:        item.ReleaseTime,
						AndroidActivateCnt: item.AndroidActivateCnt,
						IosActivateCnt:     item.IosActivateCnt,
						CommentCnt:         item.CommentCnt,
						LikeCnt:            item.LikeCnt,
						ValidLikeCnt:       item.ValidLikeCnt,
						PlayVv:             item.PlayVv,
						ValidPlayVv:        item.ValidPlayVv,
						ShareCnt:           item.ShareCnt,
						PromoteCnt:         item.PromoteCnt,
						ComponentClickCnt:  item.ComponentClickCnt,
						//Play:                 item.Play,
						//EstAdCost:            item.EstAdCost,
						//EstSales:             item.EstSales,
						//SettleAdShare:        item.SettleAdShare,
						//SettleCps:            item.SettleCps,
						IaaCostHour:          float64(item.IaaCostHour),
						IapCostHour:          float64(item.IapCostHour),
						RewardAmount:         float64(item.RewardAmount),
						RelevanceAuditResult: item.RelevanceAuditResult,
						RewardLevel:          item.RewardLevel,
					}
					if len(item.Play) > 0 {
						tModel.Play = &item.Play
					}
					if len(item.EstAdCost) > 0 {
						tModel.EstAdCost = &item.EstAdCost
					}
					if len(item.EstSales) > 0 {
						tModel.EstSales = &item.EstSales
					}
					if len(item.SettleAdShare) > 0 {
						tModel.SettleAdShare = &item.SettleAdShare
					}
					if len(item.SettleCps) > 0 {
						tModel.SettleCps = &item.SettleCps
					}
					taskSettleList = append(taskSettleList, tModel)
					if item.ItemInfoList != nil && len(item.ItemInfoList) > 0 {
						for _, itemInfo := range item.ItemInfoList {
							taskSettleDailyList = append(taskSettleDailyList, &model.AdXtTaskSettleDailyAddReq{
								AdvertiserId:  aid,
								TaskId:        taskId,
								ItemId:        item.ItemId,
								AuthorId:      item.AuthorId,
								Uid:           item.Uid,
								ProviderId:    item.ProviderId,
								PDate:         gtime.NewFromStrFormat(itemInfo.PDate, "20060102"),
								EstSales:      itemInfo.EstSales,
								SettleCps:     itemInfo.SettleCps,
								EstAdCost:     itemInfo.EstAdCost,
								SettleAdShare: itemInfo.SettleAdShare,
							})
						}
					}
				}
				// 批量插入数据
				if len(taskSettleList) > 0 {
					_, err = dao.AdXtTaskSettle.Ctx(ctx).Save(taskSettleList)
					if err != nil {
						g.Log().Error(ctx, "AdXtTaskSettle Save(taskSettleList) 添加失败")
					}
				}
				if len(taskSettleDailyList) > 0 {
					_, err = dao.AdXtTaskSettleDaily.Ctx(ctx).Save(taskSettleDailyList)
					if err != nil {
						g.Log().Error(ctx, "AdXtTaskSettleDaily Save(taskSettleDailyList) 添加失败")
					}
				}
			} else {
				break
			}
			if !*taskResponse.Data.PageInfo.HasMore {
				break
			}
			pageNo++
		}
		commonService.GetGoRedis().Set(ctx, key, "1", time.Hour*24)
	}
	return
}
