// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-31 15:46:33
// 生成路径: api/v1/oceanengine/ad_project_subscribe_record.go
// 生成人：cq
// desc:巨量项目报表订阅记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdProjectSubscribeRecordSearchReq 分页请求参数
type AdProjectSubscribeRecordSearchReq struct {
	g.Meta `path:"/list" tags:"巨量项目报表订阅记录" method:"get" summary:"巨量项目报表订阅记录列表"`
	commonApi.Author
	model.AdProjectSubscribeRecordSearchReq
}

// AdProjectSubscribeRecordSearchRes 列表返回结果
type AdProjectSubscribeRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectSubscribeRecordSearchRes
}

// AdProjectSubscribeRecordAddReq 添加操作请求参数
type AdProjectSubscribeRecordAddReq struct {
	g.Meta `path:"/add" tags:"巨量项目报表订阅记录" method:"post" summary:"巨量项目报表订阅记录添加"`
	commonApi.Author
	*model.AdProjectSubscribeRecordAddReq
}

// AdProjectSubscribeRecordAddRes 添加操作返回结果
type AdProjectSubscribeRecordAddRes struct {
	commonApi.EmptyRes
}

// AdProjectSubscribeRecordEditReq 修改操作请求参数
type AdProjectSubscribeRecordEditReq struct {
	g.Meta `path:"/edit" tags:"巨量项目报表订阅记录" method:"put" summary:"巨量项目报表订阅记录修改"`
	commonApi.Author
	*model.AdProjectSubscribeRecordEditReq
}

// AdProjectSubscribeRecordEditRes 修改操作返回结果
type AdProjectSubscribeRecordEditRes struct {
	commonApi.EmptyRes
}

// AdProjectSubscribeRecordGetReq 获取一条数据请求
type AdProjectSubscribeRecordGetReq struct {
	g.Meta `path:"/get" tags:"巨量项目报表订阅记录" method:"get" summary:"获取巨量项目报表订阅记录信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdProjectSubscribeRecordGetRes 获取一条数据结果
type AdProjectSubscribeRecordGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectSubscribeRecordInfoRes
}

// AdProjectSubscribeRecordDeleteReq 删除数据请求
type AdProjectSubscribeRecordDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量项目报表订阅记录" method:"delete" summary:"删除巨量项目报表订阅记录"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdProjectSubscribeRecordDeleteRes 删除数据返回
type AdProjectSubscribeRecordDeleteRes struct {
	commonApi.EmptyRes
}
