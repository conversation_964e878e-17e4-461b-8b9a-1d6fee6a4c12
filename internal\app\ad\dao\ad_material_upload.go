// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-23 15:51:08
// 生成路径: internal/app/ad/dao/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adMaterialUploadDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adMaterialUploadDao struct {
	*internal.AdMaterialUploadDao
}

var (
	// AdMaterialUpload is globally public accessible object for table tools_gen_table operations.
	AdMaterialUpload = adMaterialUploadDao{
		internal.NewAdMaterialUploadDao(),
	}
)

// Fill with you ideas below.
