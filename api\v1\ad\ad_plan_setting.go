// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: api/v1/ad/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdPlanSettingSearchReq 分页请求参数
type AdPlanSettingSearchReq struct {
	g.Meta `path:"/list" tags:"广告计划设置表" method:"post" summary:"广告计划设置表列表"`
	commonApi.Author
	model.AdPlanSettingSearchReq
}

// AdPlanSettingSearchRes 列表返回结果
type AdPlanSettingSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanSettingSearchRes
}

// AdPlanSettingAddReq 添加操作请求参数
type AdPlanSettingAddReq struct {
	g.Meta `path:"/add" tags:"广告计划设置表" method:"post" summary:"广告计划设置表添加"`
	commonApi.Author
	*model.AdPlanSettingAddReq
}

// AdPlanSettingAddRes 添加操作返回结果
type AdPlanSettingAddRes struct {
	commonApi.EmptyRes
}

// AdPlanSettingEditReq 修改操作请求参数
type AdPlanSettingEditReq struct {
	g.Meta `path:"/edit" tags:"广告计划设置表" method:"put" summary:"广告计划设置表修改"`
	commonApi.Author
	*model.AdPlanSettingEditReq
}

// AdPlanSettingEditRes 修改操作返回结果
type AdPlanSettingEditRes struct {
	commonApi.EmptyRes
}

// AdPlanSettingGetReq 获取一条数据请求
type AdPlanSettingGetReq struct {
	g.Meta `path:"/get" tags:"广告计划设置表" method:"get" summary:"获取广告计划设置表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdExecutePlanReq
type AdExecutePlanReq struct {
	g.Meta `path:"/execute/plan" tags:"广告计划设置表" method:"get" summary:"执行计划"`
	commonApi.Author
	Id string `p:"id" v:"required#id必须"`
}

// AdExecutePlanRes
type AdExecutePlanRes struct {
	commonApi.EmptyRes
}

// AdPlanSettingGetRes 获取一条数据结果
type AdPlanSettingGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanSettingListRes
}

// AdPlanSettingDeleteReq 删除数据请求
type AdPlanSettingDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告计划设置表" method:"post" summary:"删除广告计划设置表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPlanSettingDeleteRes 删除数据返回
type AdPlanSettingDeleteRes struct {
	commonApi.EmptyRes
}

// AdPlanSettingStatusReq 修改状态请求
type AdPlanSettingStatusReq struct {
	g.Meta `path:"/update/status" tags:"广告计划设置表" method:"post" summary:"修改状态"`
	commonApi.Author
	Ids    []int `p:"ids" v:"required#主键必须"` //通过主键删除
	Status int   `p:"status" v:"required#状态必须"`
}

// AdPlanSettingStatusRes 修改状态返回
type AdPlanSettingStatusRes struct {
	commonApi.EmptyRes
}

// GetManagedObjectListReq 获取托管对象请求
type GetManagedObjectListReq struct {
	g.Meta `path:"/getManagedObjectList" tags:"广告计划设置表" method:"post" summary:"获取托管对象列表"`
	commonApi.Author
	model.GetManagedObjectListReq
}

// GetManagedObjectListRes 获取托管对象返回
type GetManagedObjectListRes struct {
	commonApi.EmptyRes
	*model.GetManagedObjectListRes
}

// PushNotifyTestReq 推送测试通知请求
type PushNotifyTestReq struct {
	g.Meta `path:"/pushNotifyTest" tags:"广告计划设置表" method:"post" summary:"推送测试通知"`
	commonApi.Author
	model.PushNotifyTestReq
}

// PushNotifyTestRes 推送测试通知返回
type PushNotifyTestRes struct {
	commonApi.EmptyRes
}

// SendNotifyReq 发送通知请求
type SendNotifyReq struct {
	g.Meta `path:"/sendNotify" tags:"广告计划设置表" method:"post" summary:"发送通知"`
	commonApi.Author
	RuleId int `p:"ruleId" dc:"规则id"`
}

// SendNotifyRes 发送通知返回
type SendNotifyRes struct {
	commonApi.EmptyRes
}
