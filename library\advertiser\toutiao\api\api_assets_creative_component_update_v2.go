/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AssetsCreativeComponentUpdateV2ApiService 创意组件更新 service
type AssetsCreativeComponentUpdateV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.AssetsCreativeComponentUpdateV2Request
}

func (r *AssetsCreativeComponentUpdateV2ApiService) SetCfg(cfg *conf.Configuration) *AssetsCreativeComponentUpdateV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AssetsCreativeComponentUpdateV2ApiService) SetReq(req models.AssetsCreativeComponentUpdateV2Request) *AssetsCreativeComponentUpdateV2ApiService {
	r.Request = &req
	return r
}

func (r *AssetsCreativeComponentUpdateV2ApiService) AccessToken(accessToken string) *AssetsCreativeComponentUpdateV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AssetsCreativeComponentUpdateV2ApiService) Do() (data *models.AssetsCreativeComponentUpdateV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/assets/creative_component/update/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.AssetsCreativeComponentUpdateV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AssetsCreativeComponentUpdateV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/assets/creative_component/update/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
