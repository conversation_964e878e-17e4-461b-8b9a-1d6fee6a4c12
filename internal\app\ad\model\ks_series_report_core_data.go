// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-10 14:40:38
// 生成路径: internal/app/ad/model/ks_series_report_core_data.go
// 生成人：cyao
// desc:短剧核心总览数据报表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsSeriesReportCoreDataInfoRes is the golang structure for table ks_series_report_core_data.
type KsSeriesReportCoreDataInfoRes struct {
	gmeta.Meta                       `orm:"table:ks_series_report_core_data"`
	Id                               uint64      `orm:"id,primary" json:"id" dc:"id自增"`                                                                        // id自增
	AdvertiserId                     int64       `orm:"advertiser_id" json:"advertiserId" dc:"账户ID"`                                                           // 账户ID
	SeriesId                         int64       `orm:"series_id" json:"seriesId" dc:"短剧id"`                                                                   // 短剧id
	TotalCharge                      float64     `orm:"total_charge" json:"totalCharge" dc:"消耗"`                                                               // 消耗
	AccuFansUserNum                  int         `orm:"accu_fans_user_num" json:"accuFansUserNum" dc:"粉丝峰值量"`                                                  // 粉丝峰值量
	FansUserNum                      int         `orm:"fans_user_num" json:"fansUserNum" dc:"粉丝净增量"`                                                           // 粉丝净增量
	EventPayRoi                      float64     `orm:"event_pay_roi" json:"eventPayRoi" dc:"商业化ROI"`                                                          // 商业化ROI
	EventPayRoiAll                   float64     `orm:"event_pay_roi_all" json:"eventPayRoiAll" dc:"全域ROI"`                                                    // 全域ROI
	PayUserCount                     int         `orm:"pay_user_count" json:"payUserCount" dc:"付费人数"`                                                          // 付费人数
	PayCount                         string      `orm:"pay_count" json:"payCount" dc:"付费订单数"`                                                                  // 付费订单数
	PayAmt                           float64     `orm:"pay_amt" json:"payAmt" dc:"付费金额"`                                                                       // 付费金额
	IsFansUser                       int         `orm:"is_fans_user" json:"isFansUser" dc:"是否粉丝"`                                                              // 是否粉丝
	DisplayPlayCnt                   int         `orm:"display_play_cnt" json:"displayPlayCnt" dc:"播放数"`                                                       // 播放数
	DisplayLikeCnt                   int         `orm:"display_like_cnt" json:"displayLikeCnt" dc:"点赞数"`                                                       // 点赞数
	DisplayCommentCnt                int         `orm:"display_comment_cnt" json:"displayCommentCnt" dc:"评论数"`                                                 // 评论数
	DisplayCollectCnt                int         `orm:"display_collect_cnt" json:"displayCollectCnt" dc:"收藏数"`                                                 // 收藏数
	MiniGameIaaRoi                   float64     `orm:"mini_game_iaa_roi" json:"miniGameIaaRoi" dc:"IAA广告变现ROI（含返货）"`                                          // IAA广告变现ROI（含返货）
	MiniGameIaaPurchaseAmount        float64     `orm:"mini_game_iaa_purchase_amount" json:"miniGameIaaPurchaseAmount" dc:"IAA广告变现LTV（含返货，元）"`                 // IAA广告变现LTV（含返货，元）
	MiniGameIaaPurchaseAmountDivided float64     `orm:"mini_game_iaa_purchase_amount_divided" json:"miniGameIaaPurchaseAmountDivided" dc:"IAA广告变现LTV（不含返货，元）"` // IAA广告变现LTV（不含返货，元）
	MiniGameIaaDividedRoi            float64     `orm:"mini_game_iaa_divided_roi" json:"miniGameIaaDividedRoi" dc:"IAA广告变现ROI（不含返货）"`                          // IAA广告变现ROI（不含返货）
	Date                             *gtime.Time `orm:"date" json:"date" dc:"日期（yyyy-MM-dd hh:mm:ss）"`                                                         // 日期（yyyy-MM-dd hh:mm:ss）
}

type KsSeriesReportCoreDataListRes struct {
	Id                               uint64      `json:"id" dc:"id自增"`
	AdvertiserId                     int64       `json:"advertiserId" dc:"账户ID"`
	AdAccountName                    string      `json:"adAccountName"  dc:"快手经营者账号名称"`
	AccountMainName                  string      `json:"accountMainName"   dc:"账号主体名称"`
	SeriesId                         int64       `json:"seriesId" dc:"短剧id"`
	TotalCharge                      float64     `json:"totalCharge" dc:"消耗"`
	AccuFansUserNum                  int         `json:"accuFansUserNum" dc:"粉丝峰值量"`
	FansUserNum                      int         `json:"fansUserNum" dc:"粉丝净增量"`
	EventPayRoi                      float64     `json:"eventPayRoi" dc:"商业化ROI"`
	EventPayRoiAll                   float64     `json:"eventPayRoiAll" dc:"全域ROI"`
	PayUserCount                     int         `json:"payUserCount" dc:"付费人数"`
	PayCount                         string      `json:"payCount" dc:"付费订单数"`
	PayAmt                           float64     `json:"payAmt" dc:"付费金额"`
	IsFansUser                       int         `json:"isFansUser" dc:"是否粉丝"`
	DisplayPlayCnt                   int         `json:"displayPlayCnt" dc:"播放数"`
	DisplayLikeCnt                   int         `json:"displayLikeCnt" dc:"点赞数"`
	DisplayCommentCnt                int         `json:"displayCommentCnt" dc:"评论数"`
	DisplayCollectCnt                int         `json:"displayCollectCnt" dc:"收藏数"`
	MiniGameIaaRoi                   float64     `json:"miniGameIaaRoi" dc:"IAA广告变现ROI（含返货）"`
	MiniGameIaaPurchaseAmount        float64     `json:"miniGameIaaPurchaseAmount" dc:"IAA广告变现LTV（含返货，元）"`
	MiniGameIaaPurchaseAmountDivided float64     `json:"miniGameIaaPurchaseAmountDivided" dc:"IAA广告变现LTV（不含返货，元）"`
	MiniGameIaaDividedRoi            float64     `json:"miniGameIaaDividedRoi" dc:"IAA广告变现ROI（不含返货）"`
	Date                             *gtime.Time `json:"date" dc:"日期（yyyy-MM-dd hh:mm:ss）"`
}

//type KsCoreDataApiReq struct {
//	AdvertiserID  int64    `json:"advertiser_id"`  // 用户快手号id（必填）
//	PageNo        int      `json:"page_no"`        // 页码（必填）
//	PageSize      int      `json:"page_size"`      // 每页条数（必填）
//	StartTime     int64    `json:"start_time"`     // 开始时间（13位时间戳，必填）
//	EndTime       int64    `json:"end_time"`       // 结束时间（13位时间戳，必填）
//	SeriesIDs     []int64  `json:"series_ids"`     // 短剧id列表
//	ResourceType  int      `json:"resource_type"`  // 流量来源，1-自然流量，2-商业流量
//	Source        int      `json:"source"`         // 数据来源，0-实时数据，1-离线数据（必填）
//	SelectColumns []string `json:"select_columns"` // 要查询的指标列表（驼峰命名），不传默认查询所有
//}

// KsSeriesReportCoreDataSearchReq 分页请求参数
type KsSeriesReportCoreDataSearchReq struct {
	comModel.PageReq
	Id                               string  `p:"id" dc:"id自增"`                                                                                                                //id自增
	AdvertiserId                     string  `p:"advertiserId" v:"advertiserId@integer#账户ID需为整数" dc:"账户ID"`                                                                    //账户ID
	SeriesId                         string  `p:"seriesId" v:"seriesId@integer#短剧id需为整数" dc:"短剧id"`                                                                            //短剧id
	SeriesIds                        []int64 `p:"seriesIds"  dc:"短剧IDs"`                                                                                                       //短剧IDs
	TotalCharge                      string  `p:"totalCharge" v:"totalCharge@float#消耗需为浮点数" dc:"消耗"`                                                                           //消耗
	AccuFansUserNum                  string  `p:"accuFansUserNum" v:"accuFansUserNum@integer#粉丝峰值量需为整数" dc:"粉丝峰值量"`                                                            //粉丝峰值量
	FansUserNum                      string  `p:"fansUserNum" v:"fansUserNum@integer#粉丝净增量需为整数" dc:"粉丝净增量"`                                                                    //粉丝净增量
	EventPayRoi                      string  `p:"eventPayRoi" v:"eventPayRoi@float#商业化ROI需为浮点数" dc:"商业化ROI"`                                                                   //商业化ROI
	EventPayRoiAll                   string  `p:"eventPayRoiAll" v:"eventPayRoiAll@float#全域ROI需为浮点数" dc:"全域ROI"`                                                               //全域ROI
	PayUserCount                     string  `p:"payUserCount" v:"payUserCount@integer#付费人数需为整数" dc:"付费人数"`                                                                    //付费人数
	PayCount                         string  `p:"payCount" dc:"付费订单数"`                                                                                                         //付费订单数
	PayAmt                           string  `p:"payAmt" v:"payAmt@float#付费金额需为浮点数" dc:"付费金额"`                                                                                 //付费金额
	IsFansUser                       string  `p:"isFansUser" v:"isFansUser@integer#是否粉丝需为整数" dc:"是否粉丝"`                                                                        //是否粉丝
	DisplayPlayCnt                   string  `p:"displayPlayCnt" v:"displayPlayCnt@integer#播放数需为整数" dc:"播放数"`                                                                  //播放数
	DisplayLikeCnt                   string  `p:"displayLikeCnt" v:"displayLikeCnt@integer#点赞数需为整数" dc:"点赞数"`                                                                  //点赞数
	DisplayCommentCnt                string  `p:"displayCommentCnt" v:"displayCommentCnt@integer#评论数需为整数" dc:"评论数"`                                                            //评论数
	DisplayCollectCnt                string  `p:"displayCollectCnt" v:"displayCollectCnt@integer#收藏数需为整数" dc:"收藏数"`                                                            //收藏数
	MiniGameIaaRoi                   string  `p:"miniGameIaaRoi" v:"miniGameIaaRoi@float#IAA广告变现ROI（含返货）需为浮点数" dc:"IAA广告变现ROI（含返货）"`                                           //IAA广告变现ROI（含返货）
	MiniGameIaaPurchaseAmount        string  `p:"miniGameIaaPurchaseAmount" v:"miniGameIaaPurchaseAmount@float#IAA广告变现LTV（含返货，元）需为浮点数" dc:"IAA广告变现LTV（含返货，元）"`                 //IAA广告变现LTV（含返货，元）
	MiniGameIaaPurchaseAmountDivided string  `p:"miniGameIaaPurchaseAmountDivided" v:"miniGameIaaPurchaseAmountDivided@float#IAA广告变现LTV（不含返货，元）需为浮点数" dc:"IAA广告变现LTV（不含返货，元）"` //IAA广告变现LTV（不含返货，元）
	MiniGameIaaDividedRoi            string  `p:"miniGameIaaDividedRoi" v:"miniGameIaaDividedRoi@float#IAA广告变现ROI（不含返货）需为浮点数" dc:"IAA广告变现ROI（不含返货）"`                           //IAA广告变现ROI（不含返货）
	Date                             string  `p:"date" v:"date@datetime#日期（yyyy-MM-dd hh:mm:ss）需为YYYY-MM-DD hh:mm:ss格式" dc:"日期（yyyy-MM-dd hh:mm:ss）"`                          //日期（yyyy-MM-dd hh:mm:ss）
	StartDate                        string  `p:"startDate" dc:"开始时间"`
	EndDate                          string  `p:"endDate" dc:"结束时间"`
}

// KsSeriesReportCoreDataSearchRes 列表返回结果
type KsSeriesReportCoreDataSearchRes struct {
	comModel.ListRes
	List        []*KsSeriesReportCoreDataListRes `json:"list"`
	SummaryData *StatSummaryDataRes              `json:"summaryData"` //汇总数据
}

type StatSummaryDataRes struct {
	TotalCharge               float64 `json:"totalCharge" dc:"消耗"`
	AccuFansUserNum           int     `json:"accuFansUserNum"  dc:"粉丝峰值量"`
	FansUserNum               int     `json:"fansUserNum"  dc:"粉丝净增量"`
	PayUserCount              int     `json:"payUserCount"  dc:"付费人数"`
	PayAmt                    float64 `json:"payAmt"  dc:"付费金额"`
	DisplayPlayCnt            int     `json:"displayPlayCnt"  dc:"播放数"`
	DisplayLikeCnt            int     `json:"displayLikeCnt"  dc:"点赞数"`
	DisplayCommentCnt         int     `json:"displayCommentCnt"  dc:"评论数"`
	DisplayCollectCnt         int     `json:"displayCollectCnt"  dc:"收藏数"`
	MiniGameIaaPurchaseAmount float64 `json:"miniGameIaaPurchaseAmount"  dc:"IAA广告变现LTV（含返货，元）"`
}

// KsSeriesReportCoreDataAddReq 添加操作请求参数
type KsSeriesReportCoreDataAddReq struct {
	AdvertiserId                     int64       `p:"advertiserId" v:"required#账户ID不能为空" dc:"账户ID"`
	SeriesId                         int64       `p:"seriesId" v:"required#短剧id不能为空" dc:"短剧id"`
	TotalCharge                      float64     `p:"totalCharge"  dc:"消耗"`
	AccuFansUserNum                  int         `p:"accuFansUserNum"  dc:"粉丝峰值量"`
	FansUserNum                      int         `p:"fansUserNum"  dc:"粉丝净增量"`
	EventPayRoi                      float64     `p:"eventPayRoi"  dc:"商业化ROI"`
	EventPayRoiAll                   float64     `p:"eventPayRoiAll"  dc:"全域ROI"`
	PayUserCount                     int         `p:"payUserCount"  dc:"付费人数"`
	PayCount                         string      `p:"payCount"  dc:"付费订单数"`
	PayAmt                           float64     `p:"payAmt"  dc:"付费金额"`
	IsFansUser                       int         `p:"isFansUser"  dc:"是否粉丝"`
	DisplayPlayCnt                   int         `p:"displayPlayCnt"  dc:"播放数"`
	DisplayLikeCnt                   int         `p:"displayLikeCnt"  dc:"点赞数"`
	DisplayCommentCnt                int         `p:"displayCommentCnt"  dc:"评论数"`
	DisplayCollectCnt                int         `p:"displayCollectCnt"  dc:"收藏数"`
	MiniGameIaaRoi                   float64     `p:"miniGameIaaRoi"  dc:"IAA广告变现ROI（含返货）"`
	MiniGameIaaPurchaseAmount        float64     `p:"miniGameIaaPurchaseAmount"  dc:"IAA广告变现LTV（含返货，元）"`
	MiniGameIaaPurchaseAmountDivided float64     `p:"miniGameIaaPurchaseAmountDivided"  dc:"IAA广告变现LTV（不含返货，元）"`
	MiniGameIaaDividedRoi            float64     `p:"miniGameIaaDividedRoi"  dc:"IAA广告变现ROI（不含返货）"`
	Date                             *gtime.Time `p:"date"  dc:"日期（yyyy-MM-dd hh:mm:ss）"`
}

// KsSeriesReportCoreDataEditReq 修改操作请求参数
type KsSeriesReportCoreDataEditReq struct {
	Id                               uint64      `p:"id" v:"required#主键ID不能为空" dc:"id自增"`
	AdvertiserId                     int64       `p:"advertiserId" v:"required#账户ID不能为空" dc:"账户ID"`
	SeriesId                         int64       `p:"seriesId" v:"required#短剧id不能为空" dc:"短剧id"`
	TotalCharge                      float64     `p:"totalCharge"  dc:"消耗"`
	AccuFansUserNum                  int         `p:"accuFansUserNum"  dc:"粉丝峰值量"`
	FansUserNum                      int         `p:"fansUserNum"  dc:"粉丝净增量"`
	EventPayRoi                      float64     `p:"eventPayRoi"  dc:"商业化ROI"`
	EventPayRoiAll                   float64     `p:"eventPayRoiAll"  dc:"全域ROI"`
	PayUserCount                     int         `p:"payUserCount"  dc:"付费人数"`
	PayCount                         string      `p:"payCount"  dc:"付费订单数"`
	PayAmt                           float64     `p:"payAmt"  dc:"付费金额"`
	IsFansUser                       int         `p:"isFansUser"  dc:"是否粉丝"`
	DisplayPlayCnt                   int         `p:"displayPlayCnt"  dc:"播放数"`
	DisplayLikeCnt                   int         `p:"displayLikeCnt"  dc:"点赞数"`
	DisplayCommentCnt                int         `p:"displayCommentCnt"  dc:"评论数"`
	DisplayCollectCnt                int         `p:"displayCollectCnt"  dc:"收藏数"`
	MiniGameIaaRoi                   float64     `p:"miniGameIaaRoi"  dc:"IAA广告变现ROI（含返货）"`
	MiniGameIaaPurchaseAmount        float64     `p:"miniGameIaaPurchaseAmount"  dc:"IAA广告变现LTV（含返货，元）"`
	MiniGameIaaPurchaseAmountDivided float64     `p:"miniGameIaaPurchaseAmountDivided"  dc:"IAA广告变现LTV（不含返货，元）"`
	MiniGameIaaDividedRoi            float64     `p:"miniGameIaaDividedRoi"  dc:"IAA广告变现ROI（不含返货）"`
	Date                             *gtime.Time `p:"date"  dc:"日期（yyyy-MM-dd hh:mm:ss）"`
}
