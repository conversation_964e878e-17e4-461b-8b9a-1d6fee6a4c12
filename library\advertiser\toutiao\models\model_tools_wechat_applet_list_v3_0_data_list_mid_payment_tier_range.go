/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListMidPaymentTierRange
type ToolsWechatAppletListV30DataListMidPaymentTierRange string

// List of tools_wechat_applet_list_v3.0_data_list_mid_payment_tier_range
const (
	ABOVE_500_ToolsWechatAppletListV30DataListMidPaymentTierRange       ToolsWechatAppletListV30DataListMidPaymentTierRange = "ABOVE_500"
	BELOW_100_ToolsWechatAppletListV30DataListMidPaymentTierRange       ToolsWechatAppletListV30DataListMidPaymentTierRange = "BELOW_100"
	FROM_100_TO_500_ToolsWechatAppletListV30DataListMidPaymentTierRange ToolsWechatAppletListV30DataListMidPaymentTierRange = "FROM_100_TO_500"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_mid_payment_tier_range value
func (v ToolsWechatAppletListV30DataListMidPaymentTierRange) Ptr() *ToolsWechatAppletListV30DataListMidPaymentTierRange {
	return &v
}
