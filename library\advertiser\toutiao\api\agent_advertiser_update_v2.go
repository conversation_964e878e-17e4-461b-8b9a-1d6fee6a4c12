/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AgentAdvertiserUpdateV2ApiService AgentAdvertiserUpdateV2Api service
type AgentAdvertiserUpdateV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.AgentAdvertiserUpdateV2Request
}

func (r *AgentAdvertiserUpdateV2ApiService) SetCfg(cfg *conf.Configuration) *AgentAdvertiserUpdateV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AgentAdvertiserUpdateV2ApiService) AgentAdvertiserUpdateV2Request(agentAdvertiserUpdateV2Request models.AgentAdvertiserUpdateV2Request) *AgentAdvertiserUpdateV2ApiService {
	r.Request = &agentAdvertiserUpdateV2Request
	return r
}

func (r *AgentAdvertiserUpdateV2ApiService) AccessToken(accessToken string) *AgentAdvertiserUpdateV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AgentAdvertiserUpdateV2ApiService) Do() (data *models.AgentAdvertiserUpdateV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/agent/advertiser/update/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.AgentAdvertiserUpdateV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AgentAdvertiserUpdateV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/agent/advertiser/update/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
