/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameListV30DataListScheduleCards
type ToolsWechatGameListV30DataListScheduleCards string

// List of tools_wechat_game_list_v3.0_data_list_schedule_cards
const (
	ANNUAL_ToolsWechatGameListV30DataListScheduleCards    ToolsWechatGameListV30DataListScheduleCards = "Annual"
	LIFETIME_ToolsWechatGameListV30DataListScheduleCards  ToolsWechatGameListV30DataListScheduleCards = "Lifetime"
	MONTHLY_ToolsWechatGameListV30DataListScheduleCards   ToolsWechatGameListV30DataListScheduleCards = "Monthly"
	NONE_ToolsWechatGameListV30DataListScheduleCards      ToolsWechatGameListV30DataListScheduleCards = "None"
	QUARTERLY_ToolsWechatGameListV30DataListScheduleCards ToolsWechatGameListV30DataListScheduleCards = "Quarterly"
	WEEKLY_ToolsWechatGameListV30DataListScheduleCards    ToolsWechatGameListV30DataListScheduleCards = "Weekly"
)

// Ptr returns reference to tools_wechat_game_list_v3.0_data_list_schedule_cards value
func (v ToolsWechatGameListV30DataListScheduleCards) Ptr() *ToolsWechatGameListV30DataListScheduleCards {
	return &v
}
