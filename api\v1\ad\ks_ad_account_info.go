// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-07 10:33:27
// 生成路径: api/v1/ad/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdAccountInfoSearchReq 分页请求参数
type KsAdAccountInfoSearchReq struct {
	g.Meta `path:"/list" tags:"广告主资质信息余额信息" method:"get" summary:"广告主资质信息余额信息列表"`
	commonApi.Author
	model.KsAdAccountInfoSearchReq
}

// KsAdAccountInfoSearchRes 列表返回结果
type KsAdAccountInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdAccountInfoSearchRes
}

// KsAdAccountInfoAddReq 添加操作请求参数
type KsAdAccountInfoAddReq struct {
	g.Meta `path:"/add" tags:"广告主资质信息余额信息" method:"post" summary:"广告主资质信息余额信息添加"`
	commonApi.Author
	*model.KsAdAccountInfoAddReq
}

// KsAdAccountInfoAddRes 添加操作返回结果
type KsAdAccountInfoAddRes struct {
	commonApi.EmptyRes
}

// KsAdAccountInfoEditReq 修改操作请求参数
type KsAdAccountInfoEditReq struct {
	g.Meta `path:"/edit" tags:"广告主资质信息余额信息" method:"put" summary:"广告主资质信息余额信息修改"`
	commonApi.Author
	*model.KsAdAccountInfoEditReq
}

// KsAdAccountInfoEditRes 修改操作返回结果
type KsAdAccountInfoEditRes struct {
	commonApi.EmptyRes
}

// KsAdAccountInfoGetReq 获取一条数据请求
type KsAdAccountInfoGetReq struct {
	g.Meta `path:"/get" tags:"广告主资质信息余额信息" method:"get" summary:"获取广告主资质信息余额信息信息"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiserId" v:"required#主键必须"` //通过主键获取
}

// KsAdAccountInfoGetRes 获取一条数据结果
type KsAdAccountInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdAccountInfoInfoRes
}

// KsAdAccountInfoDeleteReq 删除数据请求
type KsAdAccountInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告主资质信息余额信息" method:"delete" summary:"删除广告主资质信息余额信息"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#主键必须"` //通过主键删除
}

// KsAdAccountInfoDeleteRes 删除数据返回
type KsAdAccountInfoDeleteRes struct {
	commonApi.EmptyRes
}

type GetKsAdAccountListReq struct {
	g.Meta `path:"/getKsAdAccountList" tags:"广告主资质信息余额信息" method:"get" summary:"获取快手账户列表"`
	commonApi.Author
	*model.GetKsAdAccountListReq
}

type GetKsAdAccountListRes struct {
	g.Meta `mime:"application/json"`
	*model.GetKsAdAccountListRes
}
