/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteUpdateV2RequestBricksInner struct for ToolsSiteUpdateV2RequestBricksInner
type ToolsSiteUpdateV2RequestBricksInner struct {
	//
	AutoPlay   *int64                                         `json:"auto_play,omitempty"`
	Background *ToolsSiteUpdateV2RequestBricksInnerBackground `json:"background,omitempty"`
	//
	BgColor *string `json:"bg_color,omitempty"`
	//
	BgImage *string `json:"bg_image,omitempty"`
	//
	BgType *string `json:"bg_type,omitempty"`
	//
	BorderColor *string `json:"border_color,omitempty"`
	//
	BorderRadius *int64 `json:"border_radius,omitempty"`
	//
	BorderWidth *int64 `json:"border_width,omitempty"`
	//
	BulbEffect *bool `json:"bulb_effect,omitempty"`
	//
	Color *string `json:"color,omitempty"`
	//
	Comments []*ToolsSiteUpdateV2RequestBricksInnerCommentsInner `json:"comments,omitempty"`
	//
	Content *string `json:"content,omitempty"`
	//
	Elements []*ToolsSiteUpdateV2RequestBricksInnerElementsInner `json:"elements,omitempty"`
	//
	Events      []*ToolsSiteUpdateV2RequestBricksInnerEventsInner `json:"events,omitempty"`
	FailureLink *ToolsSiteUpdateV2RequestBricksInnerFailureLink   `json:"failure_link,omitempty"`
	//
	Float *string `json:"float,omitempty"`
	//
	FontFamily *string `json:"font_family,omitempty"`
	//
	FontSize *int64 `json:"font_size,omitempty"`
	//
	FontStyle []string                                     `json:"font_style,omitempty"`
	FormData  *ToolsSiteUpdateV2RequestBricksInnerFormData `json:"form_data,omitempty"`
	//
	GamePath *string `json:"game_path,omitempty"`
	//
	GroupContent []*ToolsSiteUpdateV2RequestBricksInnerGroupContentInner `json:"group_content,omitempty"`
	//
	GroupType *string `json:"group_type,omitempty"`
	//
	Height *int64 `json:"height,omitempty"`
	//
	IcId *string `json:"ic_id,omitempty"`
	//
	Icon *string `json:"icon,omitempty"`
	//
	ImageUrl *string `json:"image_url,omitempty"`
	//
	InstanceId *int64 `json:"instance_id,omitempty"`
	//
	IsCover *int64 `json:"is_cover,omitempty"`
	//
	LetterSpacing *int64 `json:"letter_spacing,omitempty"`
	//
	LineHeight *int64                                   `json:"line_height,omitempty"`
	Link       *ToolsSiteUpdateV2RequestBricksInnerLink `json:"link,omitempty"`
	//
	Linkable    *int64                                          `json:"linkable,omitempty"`
	LocalSource *ToolsSiteUpdateV2RequestBricksInnerLocalSource `json:"local_source,omitempty"`
	Marquee     *ToolsSiteUpdateV2RequestBricksInnerMarquee     `json:"marquee,omitempty"`
	//
	Name string `json:"name"`
	//
	OffsetX *int64 `json:"offset_x,omitempty"`
	//
	OffsetY      *int64                                           `json:"offset_y,omitempty"`
	OnlineSource *ToolsSiteUpdateV2RequestBricksInnerOnlineSource `json:"online_source,omitempty"`
	PackageInfo  *ToolsSiteUpdateV2RequestBricksInnerPackageInfo  `json:"package_info,omitempty"`
	//
	Rewards []*ToolsSiteUpdateV2RequestBricksInnerRewardsInner `json:"rewards,omitempty"`
	//
	RewardsButtonText *string `json:"rewards_button_text,omitempty"`
	//
	RewardsButtonTextColor *string `json:"rewards_button_text_color,omitempty"`
	//
	RewardsButtonTextFontSize *int64                                          `json:"rewards_button_text_font_size,omitempty"`
	RuleText                  *ToolsSiteUpdateV2RequestBricksInnerRuleText    `json:"rule_text,omitempty"`
	Setting                   *ToolsSiteUpdateV2RequestBricksInnerSetting     `json:"setting,omitempty"`
	SuccessLink               *ToolsSiteUpdateV2RequestBricksInnerSuccessLink `json:"success_link,omitempty"`
	//
	SuccessTip *string `json:"success_tip,omitempty"`
	//
	Text *string `json:"text,omitempty"`
	//
	TextAlign *string `json:"text_align,omitempty"`
	//
	Width *int64 `json:"width,omitempty"`
	//
	X *int64 `json:"x,omitempty"`
	//
	Y *int64 `json:"y,omitempty"`
}
