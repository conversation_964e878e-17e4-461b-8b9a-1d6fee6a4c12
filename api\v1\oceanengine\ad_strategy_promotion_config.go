// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:19
// 生成路径: api/v1/oceanengine/ad_strategy_promotion_config.go
// 生成人：gfast
// desc:巨量广告搭建-广告信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyPromotionConfigSearchReq 分页请求参数
type AdStrategyPromotionConfigSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-广告信息" method:"get" summary:"巨量广告搭建-广告信息列表"`
	commonApi.Author
	model.AdStrategyPromotionConfigSearchReq
}

// AdStrategyPromotionConfigSearchRes 列表返回结果
type AdStrategyPromotionConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyPromotionConfigSearchRes
}

// AdStrategyPromotionConfigAddReq 添加操作请求参数
type AdStrategyPromotionConfigAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-广告信息" method:"post" summary:"巨量广告搭建-广告信息添加"`
	commonApi.Author
	*model.AdStrategyPromotionConfigAddReq
}

// AdStrategyPromotionConfigAddRes 添加操作返回结果
type AdStrategyPromotionConfigAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyPromotionConfigEditReq 修改操作请求参数
type AdStrategyPromotionConfigEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-广告信息" method:"put" summary:"巨量广告搭建-广告信息修改"`
	commonApi.Author
	*model.AdStrategyPromotionConfigEditReq
}

// AdStrategyPromotionConfigEditRes 修改操作返回结果
type AdStrategyPromotionConfigEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyPromotionConfigGetReq 获取一条数据请求
type AdStrategyPromotionConfigGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-广告信息" method:"get" summary:"获取巨量广告搭建-广告信息信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyPromotionConfigGetRes 获取一条数据结果
type AdStrategyPromotionConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyPromotionConfigInfoRes
}

// AdStrategyPromotionConfigDeleteReq 删除数据请求
type AdStrategyPromotionConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-广告信息" method:"delete" summary:"删除巨量广告搭建-广告信息"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyPromotionConfigDeleteRes 删除数据返回
type AdStrategyPromotionConfigDeleteRes struct {
	commonApi.EmptyRes
}
