// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-03 17:33:01
// 生成路径: api/v1/system/sys_mq_task.go
// 生成人：cyao
// desc:mq 执行任务表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// SysMqTaskSearchReq 分页请求参数
type SysMqTaskSearchReq struct {
	g.Meta `path:"/list" tags:"mq 执行任务表" method:"get" summary:"mq 执行任务表列表"`
	commonApi.Author
	model.SysMqTaskSearchReq
}

// SysMqTaskSearchRes 列表返回结果
type SysMqTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SysMqTaskSearchRes
}

// SysMqTaskAddReq 添加操作请求参数
type SysMqTaskAddReq struct {
	g.Meta `path:"/add" tags:"mq 执行任务表" method:"post" summary:"mq 执行任务表添加"`
	commonApi.Author
	*model.SysMqTaskAddReq
}

// SysMqTaskAddRes 添加操作返回结果
type SysMqTaskAddRes struct {
	commonApi.EmptyRes
}

// SysMqTaskEditReq 修改操作请求参数
type SysMqTaskEditReq struct {
	g.Meta `path:"/edit" tags:"mq 执行任务表" method:"put" summary:"mq 执行任务表修改"`
	commonApi.Author
	*model.SysMqTaskEditReq
}

// SysMqTaskEditRes 修改操作返回结果
type SysMqTaskEditRes struct {
	commonApi.EmptyRes
}

// SysMqTaskGetReq 获取一条数据请求
type SysMqTaskGetReq struct {
	g.Meta `path:"/get" tags:"mq 执行任务表" method:"get" summary:"获取mq 执行任务表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SysMqTaskGetRes 获取一条数据结果
type SysMqTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SysMqTaskInfoRes
}

// SysMqTaskDeleteReq 删除数据请求
type SysMqTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"mq 执行任务表" method:"delete" summary:"删除mq 执行任务表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SysMqTaskDeleteRes 删除数据返回
type SysMqTaskDeleteRes struct {
	commonApi.EmptyRes
}
