// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-07 11:44:22
// 生成路径: internal/app/ad/dao/internal/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdAccountInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdAccountInfoDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns KsAdAccountInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdAccountInfoColumns defines and stores column names for table ks_ad_account_info.
type KsAdAccountInfoColumns struct {
	AdvertiserId        string // 快手账户ID
	Id                  string // id
	PrimaryIndustryName string // 一级行业名称
	AppId               string // 授权的app_id
	IndustryId          string // 二级行业ID
	AccountId           string // 账户id
	IndustryName        string // 二级行业名称
	AccountName         string // 快手账户名称
	DeliveryType        string // 投放方式: 0:默认；1:优先效果
	PrimaryIndustryId   string // 一级行业ID
	EffectFirst         string // 优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定
	CorporationName     string // 公司名称
	ProductName         string // 账户产品名称
	DirectRebate        string // 激励余额，单位：元
	ContractRebate      string // 框返余额，单位：元
	RechargeBalance     string // 充值余额，单位：元
	Balance             string // 账户总余额，单位：元
	CreatedAt           string // 创建时间
	DeletedAt           string // 删除时间
}

var ksAdAccountInfoColumns = KsAdAccountInfoColumns{
	AdvertiserId:        "advertiser_id",
	Id:                  "id",
	PrimaryIndustryName: "primary_industry_name",
	AppId:               "app_id",
	IndustryId:          "industry_id",
	AccountId:           "account_id",
	IndustryName:        "industry_name",
	AccountName:         "account_name",
	DeliveryType:        "delivery_type",
	PrimaryIndustryId:   "primary_industry_id",
	EffectFirst:         "effect_first",
	CorporationName:     "corporation_name",
	ProductName:         "product_name",
	DirectRebate:        "direct_rebate",
	ContractRebate:      "contract_rebate",
	RechargeBalance:     "recharge_balance",
	Balance:             "balance",
	CreatedAt:           "created_at",
	DeletedAt:           "deleted_at",
}

// NewKsAdAccountInfoDao creates and returns a new DAO object for table data access.
func NewKsAdAccountInfoDao() *KsAdAccountInfoDao {
	return &KsAdAccountInfoDao{
		group:   "default",
		table:   "ks_ad_account_info",
		columns: ksAdAccountInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdAccountInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdAccountInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdAccountInfoDao) Columns() KsAdAccountInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdAccountInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdAccountInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdAccountInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
