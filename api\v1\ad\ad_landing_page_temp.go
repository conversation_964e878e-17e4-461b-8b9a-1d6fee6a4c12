// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: api/v1/ad/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdLandingPageTempSearchReq 分页请求参数
type AdLandingPageTempSearchReq struct {
	g.Meta `path:"/list" tags:"落地页模板" method:"post" summary:"落地页模板列表"`
	commonApi.Author
	model.AdLandingPageTempSearchReq
}

// AdLandingPageTempSearchRes 列表返回结果
type AdLandingPageTempSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdLandingPageTempSearchRes
}

// AdLandingPageTempExportReq 导出请求
type AdLandingPageTempExportReq struct {
	g.Meta `path:"/export" tags:"落地页模板" method:"get" summary:"落地页模板导出"`
	commonApi.Author
	model.AdLandingPageTempSearchReq
}

// AdLandingPageTempExportRes 导出响应
type AdLandingPageTempExportRes struct {
	commonApi.EmptyRes
}

// AdLandingPageTempAddReq 添加操作请求参数
type AdLandingPageTempAddReq struct {
	g.Meta `path:"/add" tags:"落地页模板" method:"post" summary:"落地页模板添加"`
	commonApi.Author
	*model.AdLandingPageTempAddReq
}

// AdLandingPageTempAddRes 添加操作返回结果
type AdLandingPageTempAddRes struct {
	commonApi.EmptyRes
}

// AdLandingPageTempEditReq 修改操作请求参数
type AdLandingPageTempEditReq struct {
	g.Meta `path:"/edit" tags:"落地页模板" method:"put" summary:"落地页模板修改"`
	commonApi.Author
	*model.AdLandingPageTempEditReq
}

// AdLandingPageTempEditRes 修改操作返回结果
type AdLandingPageTempEditRes struct {
	commonApi.EmptyRes
}

// AdLandingPageTempGetReq 获取一条数据请求
type AdLandingPageTempGetReq struct {
	g.Meta `path:"/get" tags:"落地页模板" method:"get" summary:"获取落地页模板信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdLandingPageTempGetRes 获取一条数据结果
type AdLandingPageTempGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdLandingPageTempInfoRes
}

// AdLandingPageTempDeleteReq 删除数据请求
type AdLandingPageTempDeleteReq struct {
	g.Meta `path:"/delete" tags:"落地页模板" method:"delete" summary:"删除落地页模板"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdLandingPageTempDeleteRes 删除数据返回
type AdLandingPageTempDeleteRes struct {
	commonApi.EmptyRes
}
