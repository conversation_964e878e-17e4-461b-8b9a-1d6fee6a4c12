/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// FileImageAdV2ApiService FileImageAdV2Api service

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service  上传广告图片
type FileImageAdV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2FileImageAdPostRequest
}

type ApiOpenApi2FileImageAdPostRequest struct {
	AdvertiserId   int64                          `json:"advertiser_id"`
	Filename       string                         `json:"filename"`
	ImageFile      *models.FormFileInfo           `json:"image_file"`
	ImageSignature string                         `json:"image_signature"` // 用于服务端校验
	ImageUrl       string                         `json:"image_url"`
	IsAigc         bool                           `json:"is_aigc"`
	UploadType     models.FileImageAdV2UploadType `json:"upload_type"`
}

func (r *FileImageAdV2ApiService) SetCfg(cfg *conf.Configuration) *FileImageAdV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileImageAdV2ApiService) SetRequest(req ApiOpenApi2FileImageAdPostRequest) *FileImageAdV2ApiService {
	r.Request = &req
	return r
}

func (r *FileImageAdV2ApiService) AccessToken(accessToken string) *FileImageAdV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
//func (r *FileImageAdV2ApiService) Do() (data *models.FileImageAdV2Response, err error) {
//	localBasePath := r.cfg.GetBasePath()
//	localVarPath := localBasePath + "/open_api/2/file/image/ad/"
//	localVarQueryParams := map[string]string{}
//	if r.Request == nil {
//		return nil, errors.New("请求参数不能为空")
//	}
//	if r.Request.AdvertiserId == 0 {
//		return nil, errors.New("请求参数AdvertiserId不能为空")
//	}
//	if r.Request.UploadType == "" {
//		// 默认 file 但是我们默认走url
//		r.Request.UploadType = models.UPLOAD_BY_URL_FileImageAdV2UploadType
//	}
//	if r.Request.UploadType == models.UPLOAD_BY_FILE_FileImageAdV2UploadType && r.Request.ImageFile == nil {
//		return nil, errors.New("upload_type为UPLOAD_BY_FILE,ImageFile必填")
//	}
//	if r.Request.UploadType == models.UPLOAD_BY_URL_FileImageAdV2UploadType && r.Request.ImageUrl == "" {
//		return nil, errors.New("upload_type为UPLOAD_BY_URL,ImageUrl 必须填写")
//	}
//	if r.Request.AdvertiserId > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
//	}
//	if len(r.Request.UploadType) > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "upload_type", r.Request.UploadType)
//	}
//
//	if len(r.Request.ImageSignature) > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_signature", r.Request.ImageSignature)
//	}
//	if len(r.Request.ImageUrl) > 0 {
//		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_url", r.Request.ImageUrl)
//	}
//
//	request := r.cfg.HTTPClient.R().
//		SetHeader("Content-Type", "multipart/form-data").
//		SetHeader("Access-Token", r.token).
//		SetBody(r.Request).
//		SetResult(&models.FileImageAdV2Response{})
//	if r.Request.ImageFile != nil {
//		var formFiles = make(map[string]*models.FormFileInfo)
//		formFiles["image_file"] = r.Request.ImageFile
//		request.SetFileReader("image_file", r.Request.ImageFile.FileName, bytes.NewReader(r.Request.ImageFile.FileBytes))
//	}
//	response, err := request.Post(localVarPath)
//	if err != nil {
//		return nil, err
//	}
//	resp := new(models.FileImageAdV2Response)
//	// 将 JSON 响应解码到结构体中
//	err = json.Unmarshal(response.Body(), &resp)
//	if err != nil {
//		return nil, errors.New(fmt.Sprintf("/open_api/2/file/image/ad/解析响应出错: %v\n", err))
//	}
//	if *resp.Code == 0 && resp.Data != nil {
//		return resp, nil
//	} else {
//		return resp, errors.New(*resp.Message)
//	}
//}

func (r *FileImageAdV2ApiService) Do() (data *models.FileImageAdV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/image/ad/"
	localVarQueryParams := map[string]string{}
	//localVarQueryParams := url.Values{}
	if r.Request == nil {
		return nil, errors.New("请求参数不能为空")
	}
	if r.Request.AdvertiserId == 0 {
		return nil, errors.New("请求参数AdvertiserId不能为空")
	}
	if r.Request.UploadType == "" {
		// 默认 file 但是我们默认走url
		r.Request.UploadType = models.UPLOAD_BY_URL_FileImageAdV2UploadType
	}
	if r.Request.UploadType == models.UPLOAD_BY_FILE_FileImageAdV2UploadType && r.Request.ImageFile == nil {
		return nil, errors.New("upload_type为UPLOAD_BY_FILE,ImageFile必填")
	}
	if r.Request.UploadType == models.UPLOAD_BY_URL_FileImageAdV2UploadType && r.Request.ImageUrl == "" {
		return nil, errors.New("upload_type为UPLOAD_BY_URL,ImageUrl 必须填写")
	}
	if r.Request.AdvertiserId > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	}
	if len(r.Request.UploadType) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "upload_type", r.Request.UploadType)
	}

	if len(r.Request.ImageSignature) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_signature", r.Request.ImageSignature)
	}
	if len(r.Request.ImageUrl) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_url", r.Request.ImageUrl)
	}

	request := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "multipart/form-data").
		SetHeader("Access-Token", r.token).
		SetMultipartFormData(localVarQueryParams).
		SetResult(&models.FileImageAdV2Response{})
	request.URL = localVarPath
	request.Method = "POST"
	if r.Request.ImageFile != nil {
		var formFiles = make(map[string]*models.FormFileInfo)
		formFiles["image_file"] = r.Request.ImageFile
		request.SetFileReader("image_file", r.Request.ImageFile.FileName, bytes.NewReader(r.Request.ImageFile.FileBytes))
	}
	response, err := request.Send()
	if err != nil {
		return nil, err
	}
	resp := new(models.FileImageAdV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/image/ad/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
