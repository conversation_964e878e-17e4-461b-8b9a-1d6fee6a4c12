// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: internal/app/ad/model/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdLandingPageTempInfoRes is the golang structure for table ad_landing_page_temp.
type AdLandingPageTempInfoRes struct {
	gmeta.Meta `orm:"table:ad_landing_page_temp"`
	Id         int64       `orm:"id,primary" json:"id" dc:"ID"`                                    // ID
	MainUserId int         `orm:"main_user_id" json:"mainUserId" dc:"创建用户id"`                      // 创建用户id
	TempleName string      `orm:"temple_name" json:"templeName" dc:"模板名称"`                         // 模板名称
	TempleType int         `orm:"temple_type" json:"templeType" dc:"模板类型 0 应用下载  1 微信小程序  2微信小游戏"` // 模板类型 0 应用下载  1 微信小程序  2微信小游戏
	Bricks     string      `orm:"bricks" json:"bricks" dc:"json的结构体详情结构见文档"`                       // json的结构体详情结构见文档
	AdNum      int         `orm:"ad_num" json:"adNum" dc:"广告数量"`                                   // 广告数量
	CreateTime *gtime.Time `orm:"create_time" json:"createTime" dc:"创建时间"`                         // 创建时间
	UpdateTime *gtime.Time `orm:"update_time" json:"updateTime" dc:"更新时间"`                         // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                           // 删除时间
}

type AdLandingPageTempListRes struct {
	Id         int64       `json:"id" dc:"ID"`
	MainUserId uint64      `json:"mainUserId" dc:"创建用户id"`
	UserName   string      `json:"userName" dc:"用户名"`
	TempleName string      `json:"templeName" dc:"模板名称"`
	TempleType int         `json:"templeType" dc:"模板类型 0 应用下载  1 微信小程序  2微信小游戏"`
	Bricks     string      `json:"bricks" dc:"json的结构体详情结构见文档"`
	AdNum      int         `json:"adNum" dc:"广告数量"`
	CreateTime *gtime.Time `json:"createTime" dc:"创建时间"`
	UpdateTime *gtime.Time `json:"updateTime" dc:"更新时间"`
}

// AdLandingPageTempSearchReq 分页请求参数
type AdLandingPageTempSearchReq struct {
	comModel.PageReq
	Id          string   `p:"id" dc:"ID"`                                               //ID
	Ids         []int64  `p:"ids" dc:"ID"`                                              //ID
	MainUserId  string   `p:"mainUserId" v:"mainUserId@integer#创建用户id需为整数" dc:"创建用户id"` //创建用户id
	UserIds     []int64  `p:"userIds" dc:"userIds"`
	TempleName  string   `p:"templeName" dc:"模板名称"`                                                     //模板名称
	TempleNames []string `p:"templeNames" dc:"模板名称"`                                                    //模板名称
	TempleType  *int     `p:"templeType" dc:"模板类型 0 应用下载  1 微信小程序  2微信小游戏"`                             //模板类型 0 应用下载  1 微信小程序  2微信小游戏
	Bricks      string   `p:"bricks" dc:"json的结构体详情结构见文档"`                                              //json的结构体详情结构见文档
	AdNum       string   `p:"adNum" v:"adNum@integer#广告数量需为整数" dc:"广告数量"`                               //广告数量
	CreateTime  string   `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	UpdateTime  string   `p:"updateTime" v:"updateTime@datetime#更新时间需为YYYY-MM-DD hh:mm:ss格式" dc:"更新时间"` //更新时间
}

// AdLandingPageTempSearchRes 列表返回结果
type AdLandingPageTempSearchRes struct {
	comModel.ListRes
	List []*AdLandingPageTempListRes `json:"list"`
}

// AdLandingPageTempAddReq 添加操作请求参数
type AdLandingPageTempAddReq struct {
	TempleName string `p:"templeName" v:"required#模板名称不能为空" dc:"模板名称"`
	TempleType int    `p:"templeType" v:"required#模板类型 0 应用下载  1 微信小程序  2微信小游戏不能为空" dc:"模板类型 0 应用下载  1 微信小程序  2微信小游戏"`
	Bricks     string `p:"bricks"  dc:"json的结构体详情结构见文档"`
	AdNum      int    `p:"adNum"  dc:"广告数量"`
}

// AdLandingPageTempEditReq 修改操作请求参数
type AdLandingPageTempEditReq struct {
	Id         int64  `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	TempleName string `p:"templeName" v:"required#模板名称不能为空" dc:"模板名称"`
	TempleType int    `p:"templeType" v:"required#模板类型 0 应用下载  1 微信小程序  2微信小游戏不能为空" dc:"模板类型 0 应用下载  1 微信小程序  2微信小游戏"`
	Bricks     string `p:"bricks"  dc:"json的结构体详情结构见文档"`
	AdNum      int    `p:"adNum"  dc:"广告数量"`
}
