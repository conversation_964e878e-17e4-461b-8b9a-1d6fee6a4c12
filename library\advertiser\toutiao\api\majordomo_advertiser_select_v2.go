package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
)

type MajordomoAdvertiserSelectV2Service struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *model.MajordomoAdvertiserSelectV2Request
}

func (r *MajordomoAdvertiserSelectV2Service) SetCfg(cfg *Configuration) *MajordomoAdvertiserSelectV2Service {
	r.cfg = cfg
	return r
}

func (r *MajordomoAdvertiserSelectV2Service) SetToken(token string) *MajordomoAdvertiserSelectV2Service {
	r.token = token
	return r
}

func (r *MajordomoAdvertiserSelectV2Service) SetRequest(oauth2RefreshTokenRequest *model.MajordomoAdvertiserSelectV2Request) *MajordomoAdvertiserSelectV2Service {
	r.Request = oauth2RefreshTokenRequest
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *MajordomoAdvertiserSelectV2Service) Do() (data *model.MajordomoAdvertiserSelectV2ResponseData, err error) {
	localBasePath := r.cfg.GetBasePath()
	if r.Request.AdvertiserId == 0 {
		return nil, errors.New("advertiserId 不能为空")
	}
	localVarPath := localBasePath + "/open_api/2/majordomo/advertiser/select/"
	var header = map[string]string{}
	header["Access-Token"] = r.token
	response, err := r.cfg.HTTPClient.R().SetHeader("Content-Type", "application/json").
		SetResult(&model.MajordomoAdvertiserSelectV2Response{}).
		SetQueryString(fmt.Sprintf("advertiser_id=%d", r.Request.AdvertiserId)).
		SetHeaders(header).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(model.MajordomoAdvertiserSelectV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp.Data, nil
	} else {
		return resp.Data, errors.New(resp.Message)
	}
}
