// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/controller/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAnchorPointUploadController struct {
	systemController.BaseController
}

var AdAnchorPointUpload = new(adAnchorPointUploadController)

// List 列表
func (c *adAnchorPointUploadController) List(ctx context.Context, req *ad.AdAnchorPointUploadSearchReq) (res *ad.AdAnchorPointUploadSearchRes, err error) {
	res = new(ad.AdAnchorPointUploadSearchRes)
	res.AdAnchorPointUploadSearchRes, err = service.AdAnchorPointUpload().List(ctx, &req.AdAnchorPointUploadSearchReq)
	return
}

// Get 获取推送到巨量的原生锚点
func (c *adAnchorPointUploadController) Get(ctx context.Context, req *ad.AdAnchorPointUploadGetReq) (res *ad.AdAnchorPointUploadGetRes, err error) {
	res = new(ad.AdAnchorPointUploadGetRes)
	res.AdAnchorPointUploadInfoRes, err = service.AdAnchorPointUpload().GetById(ctx, req.Id)
	return
}

// Add 添加推送到巨量的原生锚点
func (c *adAnchorPointUploadController) Add(ctx context.Context, req *ad.AdAnchorPointUploadAddReq) (res *ad.AdAnchorPointUploadAddRes, err error) {
	err = service.AdAnchorPointUpload().Add(ctx, req.AdAnchorPointUploadAddReq)
	return
}

// Edit 修改推送到巨量的原生锚点
func (c *adAnchorPointUploadController) Edit(ctx context.Context, req *ad.AdAnchorPointUploadEditReq) (res *ad.AdAnchorPointUploadEditRes, err error) {
	err = service.AdAnchorPointUpload().Edit(ctx, req.AdAnchorPointUploadEditReq)
	return
}

// Delete 删除推送到巨量的原生锚点
func (c *adAnchorPointUploadController) Delete(ctx context.Context, req *ad.AdAnchorPointUploadDeleteReq) (res *ad.AdAnchorPointUploadDeleteRes, err error) {
	err = service.AdAnchorPointUpload().Delete(ctx, req.Ids)
	return
}
