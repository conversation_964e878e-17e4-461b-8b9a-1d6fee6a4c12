/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsUnionFlowPackagePromotionReportV30FilterLandingType
type ToolsUnionFlowPackagePromotionReportV30FilterLandingType string

// List of tools_union_flow_package_promotion_report_v3.0_filter_landing_type
const (
	APP_ToolsUnionFlowPackagePromotionReportV30FilterLandingType        ToolsUnionFlowPackagePromotionReportV30FilterLandingType = "APP"
	DPA_ToolsUnionFlowPackagePromotionReportV30FilterLandingType        ToolsUnionFlowPackagePromotionReportV30FilterLandingType = "DPA"
	LINK_ToolsUnionFlowPackagePromotionReportV30FilterLandingType       ToolsUnionFlowPackagePromotionReportV30FilterLandingType = "LINK"
	MICRO_GAME_ToolsUnionFlowPackagePromotionReportV30FilterLandingType ToolsUnionFlowPackagePromotionReportV30FilterLandingType = "MICRO_GAME"
	QUICK_APP_ToolsUnionFlowPackagePromotionReportV30FilterLandingType  ToolsUnionFlowPackagePromotionReportV30FilterLandingType = "QUICK_APP"
	SHOP_ToolsUnionFlowPackagePromotionReportV30FilterLandingType       ToolsUnionFlowPackagePromotionReportV30FilterLandingType = "SHOP"
)

// Ptr returns reference to tools_union_flow_package_promotion_report_v3.0_filter_landing_type value
func (v ToolsUnionFlowPackagePromotionReportV30FilterLandingType) Ptr() *ToolsUnionFlowPackagePromotionReportV30FilterLandingType {
	return &v
}
