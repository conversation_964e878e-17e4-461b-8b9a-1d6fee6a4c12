// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: api/v1/ad/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type ToolsSiteHandselReq struct {
	g.Meta `path:"/handsel" tags:"广告落地页（工具站点）表" method:"post" summary:"转赠"`
	commonApi.Author
	model.ToolsSiteHandselReq
}

type ToolsSiteHandselRes struct {
	g.Meta `mime:"application/json"`
	model.ToolsSiteHandselRes
}

// AdToolsSiteSearchReq 分页请求参数
type AdToolsSiteSearchReq struct {
	g.Meta `path:"/list" tags:"广告落地页（工具站点）表" method:"get" summary:"广告落地页（工具站点）表列表"`
	commonApi.Author
	model.AdToolsSiteSearchReq
}

// AdToolsSiteSearchRes 列表返回结果
type AdToolsSiteSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdToolsSiteSearchRes
}

// 获取预览的url 预览地址有效期：20分钟
type GetPreviewUrlReq struct {
	g.Meta `path:"/getPreviewUrl" tags:"广告落地页（工具站点）表" method:"get" summary:"获取预览地址"`
	commonApi.Author
	*model.GetPreviewUrlReq
}

type GetPreviewUrlRes struct {
	Url string
}

// update_status
type UpdateStatusReq struct {
	g.Meta `path:"/updateStatus" tags:"广告落地页（工具站点）表" method:"post" summary:"广告落地页（工具站点）表列表"`
	commonApi.Author
	model.UpdateStatusReq
}

type UpdateStatusRes struct {
	g.Meta `mime:"application/json"`
	model.UpdateStatusRes
}

// SynchronizeSiteReq 同步站点请求参数
type SynchronizeSiteReq struct {
	g.Meta `path:"/synchronizeSite" tags:"广告落地页（工具站点）表" method:"post" summary:"广告落地页（工具站点）表列表"`
	commonApi.Author
	model.SynchronizeSiteReq
}

type SynchronizeSiteRes struct {
	commonApi.EmptyRes
}

// AdToolsSiteAddReq 添加操作请求参数
type AdToolsSiteAddReq struct {
	g.Meta `path:"/add" tags:"广告落地页（工具站点）表" method:"post" summary:"广告落地页（工具站点）表添加"`
	commonApi.Author
	*model.AdToolsSiteAddReq
}

// AdToolsSiteAddRes 添加操作返回结果
type AdToolsSiteAddRes struct {
	commonApi.EmptyRes
}

// AdToolsSiteEditReq 修改操作请求参数
type AdToolsSiteEditReq struct {
	g.Meta `path:"/edit" tags:"广告落地页（工具站点）表" method:"put" summary:"广告落地页（工具站点）表修改"`
	commonApi.Author
	*model.AdToolsSiteEditReq
}

// AdToolsSiteEditRes 修改操作返回结果
type AdToolsSiteEditRes struct {
	commonApi.EmptyRes
}

// AdToolsSiteGetReq 获取一条数据请求
type AdToolsSiteGetReq struct {
	g.Meta `path:"/get" tags:"广告落地页（工具站点）表" method:"get" summary:"获取广告落地页（工具站点）表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdToolsSiteGetRes 获取一条数据结果
type AdToolsSiteGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdToolsSiteInfoRes
}

// AdToolsSiteDeleteReq 删除数据请求
type AdToolsSiteDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告落地页（工具站点）表" method:"delete" summary:"删除广告落地页（工具站点）表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdToolsSiteDeleteRes 删除数据返回
type AdToolsSiteDeleteRes struct {
	commonApi.EmptyRes
}
