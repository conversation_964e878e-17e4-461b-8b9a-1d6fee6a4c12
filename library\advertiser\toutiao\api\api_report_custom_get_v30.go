package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"
	"time"
)

type ReportCustomGetV30ApiService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *model.ApiOpenApiV30ReportCustomGetGetRequest
}

func (r *ReportCustomGetV30ApiService) SetCfg(cfg *Configuration) *ReportCustomGetV30ApiService {
	r.cfg = cfg
	return r
}

func (r *ReportCustomGetV30ApiService) SetRequest(request model.ApiOpenApiV30ReportCustomGetGetRequest) *ReportCustomGetV30ApiService {
	r.Request = &request
	return r
}

func (r *ReportCustomGetV30ApiService) SetToken(token string) *ReportCustomGetV30ApiService {
	r.token = token
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ReportCustomGetV30ApiService) Do() (data *model.ReportCustomGetV30ResponseData, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/report/custom/get/"

	if r.Request.Dimensions == nil {
		return nil, errors.New("dimensions is required and must be specified")
	}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.Metrics == nil {
		return nil, errors.New("metrics is required and must be specified")
	}
	if r.Request.Filters == nil {
		return nil, errors.New("filters is required and must be specified")
	}
	if r.Request.StartTime == nil {
		return nil, errors.New("startTime is required and must be specified")
	}
	if r.Request.EndTime == nil {
		return nil, errors.New("endTime is required and must be specified")
	}
	if r.Request.OrderBy == nil {
		return nil, errors.New("orderBy is required and must be specified")
	}
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "dimensions", r.Request.Dimensions)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "metrics", r.Request.Metrics)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filters", r.Request.Filters)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "start_time", r.Request.StartTime)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "end_time", r.Request.EndTime)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "order_by", r.Request.OrderBy)
	if r.Request.Page > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}
	if r.Request.DataTopic != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "data_topic", r.Request.DataTopic)
	}

	var header = map[string]string{}
	//g.Log().Info(context.Background(), fmt.Sprintf("------------- 执行HttpClient请求 accessToken: (%v)   --------------------", r.token))
	header["Access-Token"] = r.token
	response, err := r.cfg.HTTPClient.R().SetHeader("Content-Type", "application/json").
		SetResult(&model.ReportCustomGetV30Response{}).
		SetQueryParams(localVarQueryParams).
		SetHeaders(header).
		SetAuthToken(r.token).
		//SetQueryString(fmt.Sprintf("advertiser_id=%d", r.Request.advertiserId)).
		//SetAuthToken(r.token).
		Get(localVarPath)

	if err != nil && strings.Contains(err.Error(), "Too many requests") {
		time.Sleep(500 * time.Millisecond)
		// 重试一次
		response, err = r.cfg.HTTPClient.R().SetHeader("Content-Type", "application/json").
			SetResult(&model.ReportCustomGetV30Response{}).
			SetQueryParams(localVarQueryParams).
			SetHeaders(header).
			SetAuthToken(r.token).
			Get(localVarPath)

	}

	if err != nil {
		return nil, err
	}
	resp := new(model.ReportCustomGetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp.Data, nil
	} else {
		return resp.Data, errors.New(resp.Message)
	}
}
