// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-13 15:31:08
// 生成路径: api/v1/ad/ad_material_album_depts.go
// 生成人：cyao
// desc:广告素材专辑和部门关联相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdMaterialAlbumDeptsSearchReq 分页请求参数
type AdMaterialAlbumDeptsSearchReq struct {
	g.Meta `path:"/list" tags:"广告素材专辑和部门关联" method:"get" summary:"广告素材专辑和部门关联列表"`
	commonApi.Author
	model.AdMaterialAlbumDeptsSearchReq
}

// AdMaterialAlbumDeptsSearchRes 列表返回结果
type AdMaterialAlbumDeptsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialAlbumDeptsSearchRes
}

// AdMaterialAlbumDeptsAddReq 添加操作请求参数
type AdMaterialAlbumDeptsAddReq struct {
	g.Meta `path:"/add" tags:"广告素材专辑和部门关联" method:"post" summary:"广告素材专辑和部门关联添加"`
	commonApi.Author
	*model.AdMaterialAlbumDeptsAddReq
}

// AdMaterialAlbumDeptsAddRes 添加操作返回结果
type AdMaterialAlbumDeptsAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialAlbumDeptsEditReq 修改操作请求参数
type AdMaterialAlbumDeptsEditReq struct {
	g.Meta `path:"/edit" tags:"广告素材专辑和部门关联" method:"put" summary:"广告素材专辑和部门关联修改"`
	commonApi.Author
	*model.AdMaterialAlbumDeptsEditReq
}

// AdMaterialAlbumDeptsEditRes 修改操作返回结果
type AdMaterialAlbumDeptsEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialAlbumDeptsGetReq 获取一条数据请求
type AdMaterialAlbumDeptsGetReq struct {
	g.Meta `path:"/get" tags:"广告素材专辑和部门关联" method:"get" summary:"获取广告素材专辑和部门关联信息"`
	commonApi.Author
	AlbumId int `p:"albumId" v:"required#主键必须"` //通过主键获取
}

// AdMaterialAlbumDeptsGetRes 获取一条数据结果
type AdMaterialAlbumDeptsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialAlbumDeptsInfoRes
}

// AdMaterialAlbumDeptsDeleteReq 删除数据请求
type AdMaterialAlbumDeptsDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告素材专辑和部门关联" method:"delete" summary:"删除广告素材专辑和部门关联"`
	commonApi.Author
	AlbumIds []int `p:"albumIds" v:"required#主键必须"` //通过主键删除
}

// AdMaterialAlbumDeptsDeleteRes 删除数据返回
type AdMaterialAlbumDeptsDeleteRes struct {
	commonApi.EmptyRes
}
