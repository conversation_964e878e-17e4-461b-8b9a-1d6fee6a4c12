// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-10 11:30:53
// 生成路径: api/v1/theater/theater_category.go
// 生成人：cq
// desc:短剧分类相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterCategorySearchReq 分页请求参数
type TheaterCategorySearchReq struct {
	g.Meta `path:"/list" tags:"短剧分类" method:"post" summary:"短剧分类列表"`
	commonApi.Author
	model.TheaterCategorySearchReq
}

// TheaterCategorySearchRes 列表返回结果
type TheaterCategorySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterCategorySearchRes
}

// TheaterCategoryAddReq 添加操作请求参数
type TheaterCategoryAddReq struct {
	g.Meta `path:"/add" tags:"短剧分类" method:"post" summary:"短剧分类添加"`
	commonApi.Author
	*model.TheaterCategoryAddReq
}

// TheaterCategoryAddRes 添加操作返回结果
type TheaterCategoryAddRes struct {
	commonApi.EmptyRes
}

// TheaterCategoryEditReq 修改操作请求参数
type TheaterCategoryEditReq struct {
	g.Meta `path:"/edit" tags:"短剧分类" method:"put" summary:"短剧分类修改"`
	commonApi.Author
	*model.TheaterCategoryEditReq
}

// TheaterCategoryEditRes 修改操作返回结果
type TheaterCategoryEditRes struct {
	commonApi.EmptyRes
}

// TheaterCategoryGetReq 获取一条数据请求
type TheaterCategoryGetReq struct {
	g.Meta `path:"/get" tags:"短剧分类" method:"get" summary:"获取短剧分类信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// TheaterCategoryGetRes 获取一条数据结果
type TheaterCategoryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterCategoryInfoRes
}

// TheaterCategoryDeleteReq 删除数据请求
type TheaterCategoryDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧分类" method:"post" summary:"删除短剧分类"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// TheaterCategoryDeleteRes 删除数据返回
type TheaterCategoryDeleteRes struct {
	commonApi.EmptyRes
}
