// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-10 13:52:08
// 生成路径: api/v1/member/m_member_info.go
// 生成人：gfast
// desc:用户基础信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberInfoSearchReq 分页请求参数
type MMemberInfoSearchReq struct {
	g.Meta `path:"/list" tags:"用户基础信息表" method:"get" summary:"用户基础信息表列表"`
	commonApi.Author
	model.MMemberInfoSearchReq
}

// MMemberInfoSearchRes 列表返回结果
type MMemberInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberInfoSearchRes
}

// MMemberInfoAddReq 添加操作请求参数
type MMemberInfoAddReq struct {
	g.Meta `path:"/add" tags:"用户基础信息表" method:"post" summary:"用户基础信息表添加"`
	commonApi.Author
	*model.MMemberInfoAddReq
}

// MMemberInfoAddRes 添加操作返回结果
type MMemberInfoAddRes struct {
	commonApi.EmptyRes
}

// MMemberInfoEditReq 修改操作请求参数
type MMemberInfoEditReq struct {
	g.Meta `path:"/edit" tags:"用户基础信息表" method:"put" summary:"用户基础信息表修改"`
	commonApi.Author
	*model.MMemberInfoEditReq
}

// MMemberInfoEditRes 修改操作返回结果
type MMemberInfoEditRes struct {
	commonApi.EmptyRes
}

// MMemberInfoGetReq 获取一条数据请求
type MMemberInfoGetReq struct {
	g.Meta `path:"/get" tags:"用户基础信息表" method:"get" summary:"获取用户基础信息表信息"`
	commonApi.Author
	SaasId string `p:"saasId" v:"required#主键必须"` //通过主键获取
}

// MMemberInfoGetRes 获取一条数据结果
type MMemberInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberInfoInfoRes
}

// MMemberInfoDeleteReq 删除数据请求
type MMemberInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户基础信息表" method:"delete" summary:"删除用户基础信息表"`
	commonApi.Author
	SaasIds []string `p:"saasIds" v:"required#主键必须"` //通过主键删除
}

// MMemberInfoDeleteRes 删除数据返回
type MMemberInfoDeleteRes struct {
	commonApi.EmptyRes
}
