// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: internal/app/ad/logic/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/fq/api"
	fqModel "github.com/tiger1103/gfast/v3/library/advertiser/fq/model"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdUserInfo(New())
}

func New() service.IFqAdUserInfo {
	return &sFqAdUserInfo{}
}

type sFqAdUserInfo struct{}

func (s *sFqAdUserInfo) List(ctx context.Context, req *model.FqAdUserInfoSearchReq) (listRes *model.FqAdUserInfoSearchRes, err error) {
	listRes = new(model.FqAdUserInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdUserInfoAnalytic.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.EncryptedDeviceId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().EncryptedDeviceId+" = ?", req.EncryptedDeviceId)
		}
		if req.OpenId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().OpenId+" = ?", req.OpenId)
		}
		//if req.RegisterTime != "" {
		//	m = m.Where(dao.FqAdUserInfo.Columns().RegisterTime+" = ?", gconv.Int64(req.RegisterTime))
		//}
		if req.PromotionId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.PromotionName != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().PromotionName+" like ?", "%"+req.PromotionName+"%")
		}
		if req.DeviceBrand != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().DeviceBrand+" = ?", req.DeviceBrand)
		}
		if req.MediaSource != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().MediaSource+" = ?", req.MediaSource)
		}
		if req.BookName != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.BookSource != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().BookSource+" = ?", req.BookSource)
		}
		if req.Clickid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Clickid+" = ?", req.Clickid)
		}
		if req.Oaid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Oaid+" = ?", req.Oaid)
		}
		if req.Caid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Caid+" = ?", req.Caid)
		}
		if req.Adid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Adid+" = ?", gconv.Int64(req.Adid))
		}
		if req.Creativeid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Creativeid+" = ?", gconv.Int64(req.Creativeid))
		}
		if req.Creativetype != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Creativetype+" = ?", gconv.Int64(req.Creativetype))
		}
		if req.Ip != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Ip+" = ?", req.Ip)
		}
		if req.UserAgent != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().UserAgent+" = ?", req.UserAgent)
		}
		if req.Timestamp != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Timestamp+" = ?", gconv.Int64(req.Timestamp))
		}
		if req.OptimizerAccount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().OptimizerAccount+" = ?", req.OptimizerAccount)
		}
		if req.EcpmAmount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().EcpmAmount+" = ?", gconv.Int64(req.EcpmAmount))
		}
		if req.EcpmCnt != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().EcpmCnt+" = ?", gconv.Int64(req.EcpmCnt))
		}
		if req.ExternalId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().ExternalId+" = ?", req.ExternalId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.AdIdV2 != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().AdIdV2+" = ?", req.AdIdV2)
		}
		if req.Mid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Mid+" = ?", req.Mid)
		}
		if req.BalanceAmount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().BalanceAmount+" = ?", gconv.Int64(req.BalanceAmount))
		}
		if req.RechargeAmount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().RechargeAmount+" = ?", gconv.Int64(req.RechargeAmount))
		}
		if req.RechargeTimes != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().RechargeTimes+" = ?", gconv.Int64(req.RechargeTimes))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.FqAdUserInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdUserInfoListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.FqAdUserInfoListRes{
				DistributorId:     v.DistributorId,
				EncryptedDeviceId: v.EncryptedDeviceId,
				OpenId:            v.OpenId,
				RegisterTime:      v.RegisterTime,
				PromotionId:       v.PromotionId,
				PromotionName:     v.PromotionName,
				DeviceBrand:       v.DeviceBrand,
				MediaSource:       v.MediaSource,
				BookName:          v.BookName,
				BookSource:        v.BookSource,
				Clickid:           v.Clickid,
				Oaid:              v.Oaid,
				Caid:              v.Caid,
				Adid:              v.Adid,
				Creativeid:        v.Creativeid,
				Creativetype:      v.Creativetype,
				Ip:                v.Ip,
				UserAgent:         v.UserAgent,
				Timestamp:         v.Timestamp,
				OptimizerAccount:  v.OptimizerAccount,
				EcpmAmount:        v.EcpmAmount,
				EcpmCnt:           v.EcpmCnt,
				ExternalId:        v.ExternalId,
				ProjectId:         v.ProjectId,
				AdIdV2:            v.AdIdV2,
				Mid:               v.Mid,
				BalanceAmount:     v.BalanceAmount,
				RechargeAmount:    v.RechargeAmount,
				RechargeTimes:     v.RechargeTimes,
			}
		}
	})
	return
}

// Pull
func (s *sFqAdUserInfo) Pull(ctx context.Context) (count int, err error) {
	count = 0
	err = g.Try(ctx, func(ctx context.Context) {
		//var fqAccount = make([]model.FqAdAccountInfoRes, 0)
		//err = dao.FqAdAccount.Ctx(ctx).WithAll().Scan(&fqAccount)
		var pageNo = 1
		var pageSize = 100
		for {
			fqAdAccountChannels, _ := service.FqAdAccountChannel().GetChannelList(ctx, &model.FqAdAccountChannelSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})

			if len(fqAdAccountChannels) == 0 {
				break
			}
			for _, accountInfoRes := range fqAdAccountChannels {
				nextPage := int64(0)
				for {
					var fqAdUserInfoAddReqs = make([]*model.FqAdUserInfoAddReq, 0)
					rechargeRes, err := api.GetAdFQIClient().GetUserListService.SetReq(fqModel.GetUserListReq{
						PageSize:  500,
						PageIndex: nextPage,
					}).SetBase(api.FqBaseReq{
						DistributorID: accountInfoRes.ChannelDistributorId,
						Secret:        accountInfoRes.SecretKey,
					}).Do()
					if err != nil {
						g.Log().Error(ctx, fmt.Sprintf("------------- GetAdFQIClient GetUserListService err：%v --------------------", err))
					}
					// 数据存储
					if len(rechargeRes.Data) > 0 {
						nextPage++
						for _, result := range rechargeRes.Data {
							count++
							midStr := ""
							if len(result.Mid) > 0 {
								midStr = strings.Join(result.Mid, ",")
							}
							fqAdUserInfoAddReqs = append(fqAdUserInfoAddReqs, &model.FqAdUserInfoAddReq{
								EncryptedDeviceId: result.EncryptedDeviceId,
								OpenId:            result.OpenId,
								DistributorId:     accountInfoRes.ChannelDistributorId,
								RegisterTime:      result.RegisterTime,
								PromotionId:       gconv.String(result.PromotionId),
								PromotionName:     result.PromotionName,
								DeviceBrand:       result.DeviceBrand,
								MediaSource:       result.MediaSource,
								BookName:          result.BookName,
								BookSource:        result.BookSource,
								Clickid:           result.Clickid,
								Oaid:              result.Oaid,
								Caid:              result.Caid,
								Adid:              result.Adid,
								Creativeid:        result.Creativeid,
								Creativetype:      result.Creativetype,
								Ip:                result.Ip,
								UserAgent:         result.UserAgent,
								Timestamp:         result.Timestamp,
								OptimizerAccount:  result.OptimizerAccount,
								EcpmAmount:        result.EcpmAmount,
								EcpmCnt:           result.EcpmCnt,
								ExternalId:        result.ExternalId,
								ProjectId:         result.ProjectId,
								AdIdV2:            result.AdIdV2,
								Mid:               midStr,
								BalanceAmount:     result.BalanceAmount,
								RechargeAmount:    result.RechargeAmount,
								RechargeTimes:     result.RechargeTimes,
							})
						}
						err = s.BatchAdd(ctx, fqAdUserInfoAddReqs)
						liberr.ErrIsNil(ctx, err, "批量添加失败")
					} else {
						break
					}
				}
			}
			pageNo++
		}

	})
	return
}

// TimedPull 定时任务拉取
func (s *sFqAdUserInfo) TimedPull(ctx context.Context, statDate string) (count int, err error) {
	channelRechargeStatKey := model.FqAdUserDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullDate ")
		return 0, err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	count, err = s.Pull(innerCtx)
	if err != nil {
		g.Log().Error(ctx, "Pull err：", err)
	}
	return

}

func (s *sFqAdUserInfo) GetExportData(ctx context.Context, req *model.FqAdUserInfoSearchReq) (listRes []*model.FqAdUserInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdUserInfoAnalytic.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.EncryptedDeviceId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().EncryptedDeviceId+" = ?", req.EncryptedDeviceId)
		}
		if req.OpenId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().OpenId+" = ?", req.OpenId)
		}
		//if req.RegisterTime != "" {
		//	m = m.Where(dao.FqAdUserInfo.Columns().RegisterTime+" = ?", gconv.Int64(req.RegisterTime))
		//}
		if req.PromotionId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.PromotionName != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().PromotionName+" like ?", "%"+req.PromotionName+"%")
		}
		if req.DeviceBrand != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().DeviceBrand+" = ?", req.DeviceBrand)
		}
		if req.MediaSource != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().MediaSource+" = ?", req.MediaSource)
		}
		if req.BookName != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.BookSource != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().BookSource+" = ?", req.BookSource)
		}
		if req.Clickid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Clickid+" = ?", req.Clickid)
		}
		if req.Oaid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Oaid+" = ?", req.Oaid)
		}
		if req.Caid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Caid+" = ?", req.Caid)
		}
		if req.Adid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Adid+" = ?", gconv.Int64(req.Adid))
		}
		if req.Creativeid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Creativeid+" = ?", gconv.Int64(req.Creativeid))
		}
		if req.Creativetype != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Creativetype+" = ?", gconv.Int64(req.Creativetype))
		}
		if req.Ip != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Ip+" = ?", req.Ip)
		}
		if req.UserAgent != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().UserAgent+" = ?", req.UserAgent)
		}
		if req.Timestamp != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Timestamp+" = ?", gconv.Int64(req.Timestamp))
		}
		if req.OptimizerAccount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().OptimizerAccount+" = ?", req.OptimizerAccount)
		}
		if req.EcpmAmount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().EcpmAmount+" = ?", gconv.Int64(req.EcpmAmount))
		}
		if req.EcpmCnt != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().EcpmCnt+" = ?", gconv.Int64(req.EcpmCnt))
		}
		if req.ExternalId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().ExternalId+" = ?", req.ExternalId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.AdIdV2 != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().AdIdV2+" = ?", req.AdIdV2)
		}
		if req.Mid != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().Mid+" = ?", req.Mid)
		}
		if req.BalanceAmount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().BalanceAmount+" = ?", gconv.Int64(req.BalanceAmount))
		}
		if req.RechargeAmount != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().RechargeAmount+" = ?", gconv.Int64(req.RechargeAmount))
		}
		if req.RechargeTimes != "" {
			m = m.Where(dao.FqAdUserInfo.Columns().RechargeTimes+" = ?", gconv.Int64(req.RechargeTimes))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdUserInfo) GetByDistributorId(ctx context.Context, distributorId int64) (res *model.FqAdUserInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdUserInfoAnalytic.Ctx(ctx).WithAll().Where(dao.FqAdUserInfo.Columns().DistributorId, distributorId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdUserInfo) Add(ctx context.Context, req *model.FqAdUserInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserInfo.Ctx(ctx).Insert(do.FqAdUserInfo{
			DistributorId:     req.DistributorId,
			EncryptedDeviceId: req.EncryptedDeviceId,
			OpenId:            req.OpenId,
			RegisterTime:      req.RegisterTime,
			PromotionId:       req.PromotionId,
			PromotionName:     req.PromotionName,
			DeviceBrand:       req.DeviceBrand,
			MediaSource:       req.MediaSource,
			BookName:          req.BookName,
			BookSource:        req.BookSource,
			Clickid:           req.Clickid,
			Oaid:              req.Oaid,
			Caid:              req.Caid,
			Adid:              req.Adid,
			Creativeid:        req.Creativeid,
			Creativetype:      req.Creativetype,
			Ip:                req.Ip,
			UserAgent:         req.UserAgent,
			Timestamp:         req.Timestamp,
			OptimizerAccount:  req.OptimizerAccount,
			EcpmAmount:        req.EcpmAmount,
			EcpmCnt:           req.EcpmCnt,
			ExternalId:        req.ExternalId,
			ProjectId:         req.ProjectId,
			AdIdV2:            req.AdIdV2,
			Mid:               req.Mid,
			BalanceAmount:     req.BalanceAmount,
			RechargeAmount:    req.RechargeAmount,
			RechargeTimes:     req.RechargeTimes,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdUserInfo) BatchAdd(ctx context.Context, reqs []*model.FqAdUserInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []do.FqAdUserInfo
		for _, req := range reqs {
			list = append(list, do.FqAdUserInfo{
				DistributorId:     req.DistributorId,
				EncryptedDeviceId: req.EncryptedDeviceId,
				OpenId:            req.OpenId,
				RegisterTime:      req.RegisterTime,
				PromotionId:       req.PromotionId,
				PromotionName:     req.PromotionName,
				DeviceBrand:       req.DeviceBrand,
				MediaSource:       req.MediaSource,
				BookName:          req.BookName,
				BookSource:        req.BookSource,
				Clickid:           req.Clickid,
				Oaid:              req.Oaid,
				Caid:              req.Caid,
				Adid:              req.Adid,
				Creativeid:        req.Creativeid,
				Creativetype:      req.Creativetype,
				Ip:                req.Ip,
				UserAgent:         req.UserAgent,
				Timestamp:         req.Timestamp,
				OptimizerAccount:  req.OptimizerAccount,
				EcpmAmount:        req.EcpmAmount,
				EcpmCnt:           req.EcpmCnt,
				ExternalId:        req.ExternalId,
				ProjectId:         req.ProjectId,
				AdIdV2:            req.AdIdV2,
				Mid:               req.Mid,
				BalanceAmount:     req.BalanceAmount,
				RechargeAmount:    req.RechargeAmount,
				RechargeTimes:     req.RechargeTimes,
			})
		}
		_, err = dao.FqAdUserInfo.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdUserInfo) Edit(ctx context.Context, req *model.FqAdUserInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserInfo.Ctx(ctx).WherePri(req.DistributorId).Update(do.FqAdUserInfo{
			DistributorId:     req.DistributorId,
			EncryptedDeviceId: req.EncryptedDeviceId,
			OpenId:            req.OpenId,
			RegisterTime:      req.RegisterTime,
			PromotionId:       req.PromotionId,
			PromotionName:     req.PromotionName,
			DeviceBrand:       req.DeviceBrand,
			MediaSource:       req.MediaSource,
			BookName:          req.BookName,
			BookSource:        req.BookSource,
			Clickid:           req.Clickid,
			Oaid:              req.Oaid,
			Caid:              req.Caid,
			Adid:              req.Adid,
			Creativeid:        req.Creativeid,
			Creativetype:      req.Creativetype,
			Ip:                req.Ip,
			UserAgent:         req.UserAgent,
			Timestamp:         req.Timestamp,
			OptimizerAccount:  req.OptimizerAccount,
			EcpmAmount:        req.EcpmAmount,
			EcpmCnt:           req.EcpmCnt,
			ExternalId:        req.ExternalId,
			ProjectId:         req.ProjectId,
			AdIdV2:            req.AdIdV2,
			Mid:               req.Mid,
			BalanceAmount:     req.BalanceAmount,
			RechargeAmount:    req.RechargeAmount,
			RechargeTimes:     req.RechargeTimes,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdUserInfo) Delete(ctx context.Context, distributorIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserInfo.Ctx(ctx).Delete(dao.FqAdUserInfo.Columns().OpenId+" in (?)", distributorIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
