// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: internal/app/ad/model/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdUserInfoInfoRes is the golang structure for table fq_ad_user_info.
type FqAdUserInfoInfoRes struct {
	gmeta.Meta        `orm:"table:fq_ad_user_info"`
	DistributorId     int64       `orm:"distributor_id,primary" json:"distributorId" dc:"渠道ID"`                                   // 渠道ID
	EncryptedDeviceId string      `orm:"encrypted_device_id,primary" json:"encryptedDeviceId" dc:"用户设备id"`                        // 用户设备id
	OpenId            string      `orm:"open_id,primary" json:"openId" dc:"微信openID"`                                             // 微信openID
	RegisterTime      *gtime.Time `orm:"register_time" json:"registerTime" dc:"注册时间（染色时间，注册时染色）"`                                 // 注册时间（染色时间，注册时染色）
	PromotionId       string      `orm:"promotion_id" json:"promotionId" dc:"推广链id"`                                              // 推广链id
	PromotionName     string      `orm:"promotion_name" json:"promotionName" dc:"推广链名称"`                                          // 推广链名称
	DeviceBrand       string      `orm:"device_brand" json:"deviceBrand" dc:"用户手机厂商"`                                             // 用户手机厂商
	MediaSource       string      `orm:"media_source" json:"mediaSource" dc:"投放来源。1：字节"`                                          // 投放来源。1：字节
	BookName          string      `orm:"book_name" json:"bookName" dc:"短剧名称"`                                                     // 短剧名称
	BookSource        string      `orm:"book_source" json:"bookSource" dc:"书本来源（短剧id）"`                                           // 书本来源（短剧id）
	Clickid           string      `orm:"clickid" json:"clickid" dc:"点击ID（仅巨量快应用一跳投放有该数据）"`                                        // 点击ID（仅巨量快应用一跳投放有该数据）
	Oaid              string      `orm:"oaid" json:"oaid" dc:"oaid"`                                                              // oaid
	Caid              string      `orm:"caid" json:"caid" dc:"caid"`                                                              // caid
	Adid              int64       `orm:"adid" json:"adid" dc:"adid（仅巨量快应用一跳投放有该数据）"`                                              // adid（仅巨量快应用一跳投放有该数据）
	Creativeid        int64       `orm:"creativeid" json:"creativeid" dc:"creativeid（仅巨量快应用一跳投放有该数据）"`                            // creativeid（仅巨量快应用一跳投放有该数据）
	Creativetype      int64       `orm:"creativetype" json:"creativetype" dc:"creativetype（仅巨量快应用一跳投放有该数据）"`                      // creativetype（仅巨量快应用一跳投放有该数据）
	Ip                string      `orm:"ip" json:"ip" dc:"ip"`                                                                    // ip
	UserAgent         string      `orm:"user_agent" json:"userAgent" dc:"user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua"`         // user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua
	Timestamp         int64       `orm:"timestamp" json:"timestamp" dc:"最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号"`                         // 最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号
	OptimizerAccount  string      `orm:"optimizer_account" json:"optimizerAccount" dc:"优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount"` // 优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount
	EcpmAmount        int64       `orm:"ecpm_amount" json:"ecpmAmount" dc:"广告激励总收入,单位分"`                                          // 广告激励总收入,单位分
	EcpmCnt           int64       `orm:"ecpm_cnt" json:"ecpmCnt" dc:"广告点击次数"`                                                     // 广告点击次数
	ExternalId        string      `orm:"external_id" json:"externalId" dc:"企微用户企微id"`                                             // 企微用户企微id
	ProjectId         string      `orm:"project_id" json:"projectId" dc:"巨量2.0广告计划组ID"`                                           // 巨量2.0广告计划组ID
	AdIdV2            string      `orm:"ad_id_v2" json:"adIdV2" dc:"巨量2.0广告计划ID"`                                                 // 巨量2.0广告计划ID
	Mid               string      `orm:"mid" json:"mid" dc:"素材id（分别代表图片、标题、视频、试玩、落地页）"`                                           // 素材id（分别代表图片、标题、视频、试玩、落地页）
	BalanceAmount     int64       `orm:"balance_amount" json:"balanceAmount" dc:"余额，iaa不需要关注"`                                    // 余额，iaa不需要关注
	RechargeAmount    int64       `orm:"recharge_amount" json:"rechargeAmount" dc:"充值金额，iaa不需要关注"`                                // 充值金额，iaa不需要关注
	RechargeTimes     int64       `orm:"recharge_times" json:"rechargeTimes" dc:"充值次数，iaa不需要关注"`                                  // 充值次数，iaa不需要关注
}

type FqAdUserInfoListRes struct {
	DistributorId     int64       `json:"distributorId" dc:"渠道ID"`
	EncryptedDeviceId string      `json:"encryptedDeviceId" dc:"用户设备id"`
	OpenId            string      `json:"openId" dc:"微信openID"`
	RegisterTime      *gtime.Time `json:"registerTime" dc:"注册时间（染色时间，注册时染色）"`
	PromotionId       string      `json:"promotionId" dc:"推广链id"`
	PromotionName     string      `json:"promotionName" dc:"推广链名称"`
	DeviceBrand       string      `json:"deviceBrand" dc:"用户手机厂商"`
	MediaSource       string      `json:"mediaSource" dc:"投放来源。1：字节"`
	BookName          string      `json:"bookName" dc:"短剧名称"`
	BookSource        string      `json:"bookSource" dc:"书本来源（短剧id）"`
	Clickid           string      `json:"clickid" dc:"点击ID（仅巨量快应用一跳投放有该数据）"`
	Oaid              string      `json:"oaid" dc:"oaid"`
	Caid              string      `json:"caid" dc:"caid"`
	Adid              int64       `json:"adid" dc:"adid（仅巨量快应用一跳投放有该数据）"`
	Creativeid        int64       `json:"creativeid" dc:"creativeid（仅巨量快应用一跳投放有该数据）"`
	Creativetype      int64       `json:"creativetype" dc:"creativetype（仅巨量快应用一跳投放有该数据）"`
	Ip                string      `json:"ip" dc:"ip"`
	UserAgent         string      `json:"userAgent" dc:"user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua"`
	Timestamp         int64       `json:"timestamp" dc:"最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号"`
	OptimizerAccount  string      `json:"optimizerAccount" dc:"优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount"`
	EcpmAmount        int64       `json:"ecpmAmount" dc:"广告激励总收入,单位分"`
	EcpmCnt           int64       `json:"ecpmCnt" dc:"广告点击次数"`
	ExternalId        string      `json:"externalId" dc:"企微用户企微id"`
	ProjectId         string      `json:"projectId" dc:"巨量2.0广告计划组ID"`
	AdIdV2            string      `json:"adIdV2" dc:"巨量2.0广告计划ID"`
	Mid               string      `json:"mid" dc:"素材id（分别代表图片、标题、视频、试玩、落地页）"`
	BalanceAmount     int64       `json:"balanceAmount" dc:"余额，iaa不需要关注"`
	RechargeAmount    int64       `json:"rechargeAmount" dc:"充值金额，iaa不需要关注"`
	RechargeTimes     int64       `json:"rechargeTimes" dc:"充值次数，iaa不需要关注"`
}

// FqAdUserInfoSearchReq 分页请求参数
type FqAdUserInfoSearchReq struct {
	comModel.PageReq
	DistributorId     string `p:"distributorId" dc:"渠道ID"`                                                                                  //渠道ID
	EncryptedDeviceId string `p:"encryptedDeviceId" dc:"用户设备id"`                                                                            //用户设备id
	OpenId            string `p:"openId" dc:"微信openID"`                                                                                     //微信openID
	RegisterTime      string `p:"registerTime" v:"registerTime@integer#注册时间（染色时间，注册时染色）需为整数" dc:"注册时间（染色时间，注册时染色）"`                         //注册时间（染色时间，注册时染色）
	UserAgent         string `p:"userAgent" dc:"user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua"`                                            //user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua
	PromotionId       string `p:"promotionId" dc:"推广链id"`                                                                                   //推广链id
	PromotionName     string `p:"promotionName" dc:"推广链名称"`                                                                                 //推广链名称
	DeviceBrand       string `p:"deviceBrand" dc:"用户手机厂商"`                                                                                  //用户手机厂商
	MediaSource       string `p:"mediaSource" dc:"投放来源。1：字节"`                                                                               //投放来源。1：字节
	BookName          string `p:"bookName" dc:"短剧名称"`                                                                                       //短剧名称
	BookSource        string `p:"bookSource" dc:"书本来源（短剧id）"`                                                                               //书本来源（短剧id）
	Clickid           string `p:"clickid" dc:"点击ID（仅巨量快应用一跳投放有该数据）"`                                                                        //点击ID（仅巨量快应用一跳投放有该数据）
	Oaid              string `p:"oaid" dc:"oaid"`                                                                                           //oaid
	Caid              string `p:"caid" dc:"caid"`                                                                                           //caid
	Adid              string `p:"adid" v:"adid@integer#adid（仅巨量快应用一跳投放有该数据）需为整数" dc:"adid（仅巨量快应用一跳投放有该数据）"`                                 //adid（仅巨量快应用一跳投放有该数据）
	Creativeid        string `p:"creativeid" v:"creativeid@integer#creativeid（仅巨量快应用一跳投放有该数据）需为整数" dc:"creativeid（仅巨量快应用一跳投放有该数据）"`         //creativeid（仅巨量快应用一跳投放有该数据）
	Creativetype      string `p:"creativetype" v:"creativetype@integer#creativetype（仅巨量快应用一跳投放有该数据）需为整数" dc:"creativetype（仅巨量快应用一跳投放有该数据）"` //creativetype（仅巨量快应用一跳投放有该数据）
	Ip                string `p:"ip" dc:"ip"`                                                                                               //ip

	Timestamp        string `p:"timestamp" v:"timestamp@integer#最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号需为整数" dc:"最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号"` //最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号
	OptimizerAccount string `p:"optimizerAccount" dc:"优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount"`                                           //优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount
	EcpmAmount       string `p:"ecpmAmount" v:"ecpmAmount@integer#广告激励总收入,单位分需为整数" dc:"广告激励总收入,单位分"`                                       //广告激励总收入,单位分
	EcpmCnt          string `p:"ecpmCnt" v:"ecpmCnt@integer#广告点击次数需为整数" dc:"广告点击次数"`                                                       //广告点击次数
	ExternalId       string `p:"externalId" dc:"企微用户企微id"`                                                                                 //企微用户企微id
	ProjectId        string `p:"projectId" dc:"巨量2.0广告计划组ID"`                                                                              //巨量2.0广告计划组ID
	AdIdV2           string `p:"adIdV2" dc:"巨量2.0广告计划ID"`                                                                                  //巨量2.0广告计划ID
	Mid              string `p:"mid" dc:"素材id（分别代表图片、标题、视频、试玩、落地页）"`                                                                       //素材id（分别代表图片、标题、视频、试玩、落地页）
	BalanceAmount    string `p:"balanceAmount" v:"balanceAmount@integer#余额，iaa不需要关注需为整数" dc:"余额，iaa不需要关注"`                                 //余额，iaa不需要关注
	RechargeAmount   string `p:"rechargeAmount" v:"rechargeAmount@integer#充值金额，iaa不需要关注需为整数" dc:"充值金额，iaa不需要关注"`                           //充值金额，iaa不需要关注
	RechargeTimes    string `p:"rechargeTimes" v:"rechargeTimes@integer#充值次数，iaa不需要关注需为整数" dc:"充值次数，iaa不需要关注"`                             //充值次数，iaa不需要关注
}

// FqAdUserInfoSearchRes 列表返回结果
type FqAdUserInfoSearchRes struct {
	comModel.ListRes
	List []*FqAdUserInfoListRes `json:"list"`
}

// FqAdUserInfoAddReq 添加操作请求参数
type FqAdUserInfoAddReq struct {
	EncryptedDeviceId string      `p:"encryptedDeviceId" dc:"用户设备id"` // 用户设备id
	OpenId            string      `p:"openId" dc:"微信openID"`          // 微信openID
	DistributorId     int64       `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
	RegisterTime      *gtime.Time `p:"registerTime" v:"required#注册时间（染色时间，注册时染色）不能为空" dc:"注册时间（染色时间，注册时染色）"`
	PromotionId       string      `p:"promotionId"  dc:"推广链id"`
	PromotionName     string      `p:"promotionName" v:"required#推广链名称不能为空" dc:"推广链名称"`
	DeviceBrand       string      `p:"deviceBrand"  dc:"用户手机厂商"`
	MediaSource       string      `p:"mediaSource"  dc:"投放来源。1：字节"`
	BookName          string      `p:"bookName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	BookSource        string      `p:"bookSource"  dc:"书本来源（短剧id）"`
	Clickid           string      `p:"clickid"  dc:"点击ID（仅巨量快应用一跳投放有该数据）"`
	Oaid              string      `p:"oaid"  dc:"oaid"`
	Caid              string      `p:"caid"  dc:"caid"`
	Adid              int64       `p:"adid"  dc:"adid（仅巨量快应用一跳投放有该数据）"`
	Creativeid        int         `p:"creativeid"  dc:"creativeid（仅巨量快应用一跳投放有该数据）"`
	Creativetype      int         `p:"creativetype"  dc:"creativetype（仅巨量快应用一跳投放有该数据）"`
	Ip                string      `p:"ip"  dc:"ip"`
	UserAgent         string      `p:"userAgent" v:"required#user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua不能为空" dc:"user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua"`
	Timestamp         int64       `p:"timestamp"  dc:"最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号"`
	OptimizerAccount  string      `p:"optimizerAccount"  dc:"优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount"`
	EcpmAmount        int         `p:"ecpmAmount"  dc:"广告激励总收入,单位分"`
	EcpmCnt           int         `p:"ecpmCnt"  dc:"广告点击次数"`
	ExternalId        string      `p:"externalId"  dc:"企微用户企微id"`
	ProjectId         string      `p:"projectId"  dc:"巨量2.0广告计划组ID"`
	AdIdV2            string      `p:"adIdV2"  dc:"巨量2.0广告计划ID"`
	Mid               string      `p:"mid" v:"required#素材id（分别代表图片、标题、视频、试玩、落地页）不能为空" dc:"素材id（分别代表图片、标题、视频、试玩、落地页）"`
	BalanceAmount     int         `p:"balanceAmount"  dc:"余额，iaa不需要关注"`
	RechargeAmount    int         `p:"rechargeAmount"  dc:"充值金额，iaa不需要关注"`
	RechargeTimes     int         `p:"rechargeTimes"  dc:"充值次数，iaa不需要关注"`
}

// FqAdUserInfoEditReq 修改操作请求参数
type FqAdUserInfoEditReq struct {
	EncryptedDeviceId string      `p:"encryptedDeviceId" dc:"用户设备id"` // 用户设备id
	OpenId            string      `p:"openId" dc:"微信openID"`          // 微信openID
	DistributorId     int64       `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
	RegisterTime      *gtime.Time `p:"registerTime" v:"required#注册时间（染色时间，注册时染色）不能为空" dc:"注册时间（染色时间，注册时染色）"`
	PromotionId       string      `p:"promotionId"  dc:"推广链id"`
	PromotionName     string      `p:"promotionName" v:"required#推广链名称不能为空" dc:"推广链名称"`
	DeviceBrand       string      `p:"deviceBrand"  dc:"用户手机厂商"`
	MediaSource       string      `p:"mediaSource"  dc:"投放来源。1：字节"`
	BookName          string      `p:"bookName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	BookSource        string      `p:"bookSource"  dc:"书本来源（短剧id）"`
	Clickid           string      `p:"clickid"  dc:"点击ID（仅巨量快应用一跳投放有该数据）"`
	Oaid              string      `p:"oaid"  dc:"oaid"`
	Caid              string      `p:"caid"  dc:"caid"`
	Adid              int64       `p:"adid"  dc:"adid（仅巨量快应用一跳投放有该数据）"`
	Creativeid        int64       `p:"creativeid"  dc:"creativeid（仅巨量快应用一跳投放有该数据）"`
	Creativetype      int64       `p:"creativetype"  dc:"creativetype（仅巨量快应用一跳投放有该数据）"`
	Ip                string      `p:"ip"  dc:"ip"`
	UserAgent         string      `p:"userAgent" v:"required#user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua不能为空" dc:"user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua"`
	Timestamp         int64       `p:"timestamp"  dc:"最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号"`
	OptimizerAccount  string      `p:"optimizerAccount"  dc:"优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount"`
	EcpmAmount        int64       `p:"ecpmAmount"  dc:"广告激励总收入,单位分"`
	EcpmCnt           int64       `p:"ecpmCnt"  dc:"广告点击次数"`
	ExternalId        string      `p:"externalId"  dc:"企微用户企微id"`
	ProjectId         string      `p:"projectId"  dc:"巨量2.0广告计划组ID"`
	AdIdV2            string      `p:"adIdV2"  dc:"巨量2.0广告计划ID"`
	Mid               string      `p:"mid" v:"required#素材id（分别代表图片、标题、视频、试玩、落地页）不能为空" dc:"素材id（分别代表图片、标题、视频、试玩、落地页）"`
	BalanceAmount     int64       `p:"balanceAmount"  dc:"余额，iaa不需要关注"`
	RechargeAmount    int64       `p:"rechargeAmount"  dc:"充值金额，iaa不需要关注"`
	RechargeTimes     int64       `p:"rechargeTimes"  dc:"充值次数，iaa不需要关注"`
}
