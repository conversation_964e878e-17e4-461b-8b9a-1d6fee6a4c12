// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/service/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IFqAdAccount interface {
	List(ctx context.Context, req *model.FqAdAccountSearchReq) (res *model.FqAdAccountSearchRes, err error)
	List2(ctx context.Context) (listRes *model.FqAdAccountSearchRes, err error)
	GetList(ctx context.Context, req *model.FqAdAccountSearchReq) (listRes []*model.FqAdAccountInfoRes, err error)
	AddAuth(ctx context.Context, req *model.FqAdAddAuthReq) (err error)
	GetExportData(ctx context.Context, req *model.FqAdAccountSearchReq) (listRes []*model.FqAdAccountInfoRes, err error)
	GetByDistributorId(ctx context.Context, DistributorId int64) (res *model.FqAdAccountInfoRes, err error)
	GetByDistributorIds(ctx context.Context, distributorIds []string) (res []*model.FqAdAccountInfoRes, err error)
	Add(ctx context.Context, req *model.FqAdAccountAddReq) (err error)
	Edit(ctx context.Context, req *model.FqAdAccountEditReq) (err error)
	Delete(ctx context.Context, DistributorId []int64) (err error)
}

var localFqAdAccount IFqAdAccount

func FqAdAccount() IFqAdAccount {
	if localFqAdAccount == nil {
		panic("implement not found for interface IFqAdAccount, forgot register?")
	}
	return localFqAdAccount
}

func RegisterFqAdAccount(i IFqAdAccount) {
	localFqAdAccount = i
}
