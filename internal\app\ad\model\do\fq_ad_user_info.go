// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: internal/app/ad/model/entity/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdUserInfo is the golang structure for table fq_ad_user_info.
type FqAdUserInfo struct {
	gmeta.Meta        `orm:"table:fq_ad_user_info, do:true"`
	DistributorId     interface{} `orm:"distributor_id" json:"distributorId"`          // 渠道ID
	EncryptedDeviceId interface{} `orm:"encrypted_device_id" json:"encryptedDeviceId"` // 用户设备id
	OpenId            interface{} `orm:"open_id" json:"openId"`                        // 微信openID
	RegisterTime      interface{} `orm:"register_time" json:"registerTime"`            // 注册时间（染色时间，注册时染色）
	PromotionId       interface{} `orm:"promotion_id" json:"promotionId"`              // 推广链id
	PromotionName     interface{} `orm:"promotion_name" json:"promotionName"`          // 推广链名称
	DeviceBrand       interface{} `orm:"device_brand" json:"deviceBrand"`              // 用户手机厂商
	MediaSource       interface{} `orm:"media_source" json:"mediaSource"`              // 投放来源。1：字节
	BookName          interface{} `orm:"book_name" json:"bookName"`                    // 短剧名称
	BookSource        interface{} `orm:"book_source" json:"bookSource"`                // 书本来源（短剧id）
	Clickid           interface{} `orm:"clickid" json:"clickid"`                       // 点击ID（仅巨量快应用一跳投放有该数据）
	Oaid              interface{} `orm:"oaid" json:"oaid"`                             // oaid
	Caid              interface{} `orm:"caid" json:"caid"`                             // caid
	Adid              interface{} `orm:"adid" json:"adid"`                             // adid（仅巨量快应用一跳投放有该数据）
	Creativeid        interface{} `orm:"creativeid" json:"creativeid"`                 // creativeid（仅巨量快应用一跳投放有该数据）
	Creativetype      interface{} `orm:"creativetype" json:"creativetype"`             // creativetype（仅巨量快应用一跳投放有该数据）
	Ip                interface{} `orm:"ip" json:"ip"`                                 // ip
	UserAgent         interface{} `orm:"user_agent" json:"userAgent"`                  // user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua
	Timestamp         interface{} `orm:"timestamp" json:"timestamp"`                   // 最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号
	OptimizerAccount  interface{} `orm:"optimizer_account" json:"optimizerAccount"`    // 优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount
	EcpmAmount        interface{} `orm:"ecpm_amount" json:"ecpmAmount"`                // 广告激励总收入,单位分
	EcpmCnt           interface{} `orm:"ecpm_cnt" json:"ecpmCnt"`                      // 广告点击次数
	ExternalId        interface{} `orm:"external_id" json:"externalId"`                // 企微用户企微id
	ProjectId         interface{} `orm:"project_id" json:"projectId"`                  // 巨量2.0广告计划组ID
	AdIdV2            interface{} `orm:"ad_id_v2" json:"adIdV2"`                       // 巨量2.0广告计划ID
	Mid               interface{} `orm:"mid" json:"mid"`                               // 素材id（分别代表图片、标题、视频、试玩、落地页）
	BalanceAmount     interface{} `orm:"balance_amount" json:"balanceAmount"`          // 余额，iaa不需要关注
	RechargeAmount    interface{} `orm:"recharge_amount" json:"rechargeAmount"`        // 充值金额，iaa不需要关注
	RechargeTimes     interface{} `orm:"recharge_times" json:"rechargeTimes"`          // 充值次数，iaa不需要关注
}
