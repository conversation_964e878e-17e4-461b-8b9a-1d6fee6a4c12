// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: internal/app/ad/model/entity/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzTask is the golang structure for table dz_task.
type DzTask struct {
	gmeta.Meta `orm:"table:dz_task, do:true"`
	TaskId     interface{} `orm:"task_id,primary" json:"taskId"` // 任务id
	QueryUrl   interface{} `orm:"query_url" json:"queryUrl"`     // 查询url
	QueryJson  interface{} `orm:"query_json" json:"queryJson"`   // 查询参数
	Status     interface{} `orm:"status" json:"status"`
	Xurl       interface{} `orm:"xurl" json:"xurl"`
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
}
