// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-07-16 17:18:05
// 生成路径: internal/app/applet/dao/internal/s_exchange_config.go
// 生成人：cq
// desc:兑换配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SExchangeConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type SExchangeConfigDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns SExchangeConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SExchangeConfigColumns defines and stores column names for table s_exchange_config.
type SExchangeConfigColumns struct {
	Id         string //
	AppId      string // 小程序ID
	ImgUrl     string // 封面图
	Title      string // 兑换标题
	Type       string // 兑换类型 1：乘车券 2：金币 3：VIP 4：观影券
	VipType    string // vip类型 1：月卡 2：季卡 3：年卡 4：半年卡 5：3天卡 6：1天卡 7：剧vip 8：周卡
	Num        string // 兑换数量
	Consume    string // 消耗积分
	Sort       string // 排序
	Remark     string // 备注
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

var sExchangeConfigColumns = SExchangeConfigColumns{
	Id:         "id",
	AppId:      "app_id",
	ImgUrl:     "img_url",
	Title:      "title",
	Type:       "type",
	VipType:    "vip_type",
	Num:        "num",
	Consume:    "consume",
	Sort:       "sort",
	Remark:     "remark",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewSExchangeConfigDao creates and returns a new DAO object for table data access.
func NewSExchangeConfigDao() *SExchangeConfigDao {
	return &SExchangeConfigDao{
		group:   "default",
		table:   "s_exchange_config",
		columns: sExchangeConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SExchangeConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SExchangeConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SExchangeConfigDao) Columns() SExchangeConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SExchangeConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SExchangeConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SExchangeConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
