// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: internal/app/ad/model/entity/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdUserPaymentRecord is the golang structure for table fq_ad_user_payment_record.
type FqAdUserPaymentRecord struct {
	gmeta.Meta       `orm:"table:fq_ad_user_payment_record"`
	TradeNo          string `orm:"trade_no,primary" json:"tradeNo"`             // 常读订单id
	DistributorId    string `orm:"distributor_id" json:"distributorId"`         // 快应用/公众号distributor_id
	AppId            string `orm:"app_id" json:"appId"`                         // 公众号/快应用id（分销平台id）
	AppName          string `orm:"app_name" json:"appName"`                     // 快应用/公众号名称
	PromotionId      string `orm:"promotion_id" json:"promotionId"`             // 付费推广链id（用户染色归属推广链）
	OutTradeNo       string `orm:"out_trade_no" json:"outTradeNo"`              // 第三方订单ID
	DeviceId         string `orm:"device_id" json:"deviceId"`                   // 设备ID
	OpenId           string `orm:"open_id" json:"openId"`                       // 用户openid（H5书城、微信小程序、抖音小程序）
	WxOaOpenId       string `orm:"wx_oa_open_id" json:"wxOaOpenId"`             // 微信公众号 open_id（仅适用网文微小复访的公众号用户）
	WxOaName         string `orm:"wx_oa_name" json:"wxOaName"`                  // 公众号名称（微小的小程序维度返回）
	PayAmount        int64  `orm:"pay_amount" json:"payAmount"`                 // 付费金额，单位分
	Ip               string `orm:"ip" json:"ip"`                                // 用户最近一次点击推广链时的IP
	UserAgent        string `orm:"user_agent" json:"userAgent"`                 // 用户最近一次点击推广链时的UA
	Oaid             string `orm:"oaid" json:"oaid"`                            // 付费时用户OAID（仅支持快应用）
	AndroidId        string `orm:"android_id" json:"androidId"`                 // 付费时用户android_id（仅支持快应用）
	RegisterTime     string `orm:"register_time" json:"registerTime"`           // 用户染色时间戳
	WxPlatformAppKey string `orm:"wx_platform_app_key" json:"wxPlatformAppKey"` // 微信开发者id(仅支持微信H5)【v1.2】
	BookId           string `orm:"book_id" json:"bookId"`                       // 染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）
	BookName         string `orm:"book_name" json:"bookName"`                   // 染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称
	BookGender       string `orm:"book_gender" json:"bookGender"`               // 染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别
	BookCategory     string `orm:"book_category" json:"bookCategory"`           // 染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型
	Activity         string `orm:"activity" json:"activity"`                    // 是否是充值活动(仅支持微信H5)【v1.2】
	RecentReadBookId string `orm:"recent_read_book_id" json:"recentReadBookId"` // H5书城用户订单最近阅读书籍（小程序不返回）
	ExternalId       string `orm:"external_id" json:"externalId"`               // 企微用户企微id（公众号返回）
	OrderType        int64  `orm:"order_type" json:"orderType"`                 // 订单类型：1 拟支付，2 非虚拟支付
	AdvertiserId     string `orm:"advertiser_id" json:"advertiserId"`           // 腾讯广告主id
	AdgroupId        string `orm:"adgroup_id" json:"adgroupId"`                 // 腾讯广告id
	AdId             string `orm:"ad_id" json:"adId"`                           // 腾讯广告创意id
	UnionId          string `orm:"union_id" json:"unionId"`                     // 用户在微信/抖音开放平台下的唯一id
	WxVideoId        string `orm:"wx_video_id" json:"wxVideoId"`                // 视频ID（仅视频号场景）
	WxVcSourceType   int    `orm:"wx_vc_source_type" json:"wxVcSourceType"`     // 视频号订单类型（仅视频号场景）1.自然流量 2.加热流量
	WxPromotionId    string `orm:"wx_promotion_id" json:"wxPromotionId"`        // 视频号加热订单ID（仅视频号场景）
	WxSourceType     string `orm:"wx_source_type" json:"wxSourceType"`          // 场景参数，用于区分分销自挂载和CPS达人模式
	WxVideoChannelId string `orm:"wx_video_channel_id" json:"wxVideoChannelId"` // 视频号ID（仅视频号场景）
	Status           int    `orm:"status" json:"status"`                        // 0-已支付-1-未支付
	PayWay           int    `orm:"pay_way" json:"payWay"`                       // 1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成
	PayTimestamp     string `orm:"pay_timestamp" json:"payTimestamp"`           // 付费时间戳
	CreateTime       string `orm:"create_time" json:"createTime"`               // 订单创建时间
}
