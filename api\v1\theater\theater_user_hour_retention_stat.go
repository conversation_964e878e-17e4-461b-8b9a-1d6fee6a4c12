// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-06 17:10:48
// 生成路径: api/v1/theater/theater_user_hour_retention_stat.go
// 生成人：cyao
// desc:按小时统计剧集相关的数据 相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterUserHourRetentionStatSearchReq 分页请求参数
type TheaterUserHourRetentionStatSearchReq struct {
	g.Meta `path:"/list" tags:"按小时统计剧集相关的数据 " method:"get" summary:"按小时统计剧集相关的数据 列表"`
	commonApi.Author
	model.TheaterUserHourRetentionStatSearchReq
}

// TheaterUserHourRetentionStatSearchRes 列表返回结果
type TheaterUserHourRetentionStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterUserHourRetentionStatSearchRes
}

// TheaterUserHourRetentionStatAddReq 添加操作请求参数
type TheaterUserHourRetentionStatAddReq struct {
	g.Meta `path:"/add" tags:"按小时统计剧集相关的数据 " method:"post" summary:"按小时统计剧集相关的数据 添加"`
	commonApi.Author
	*model.TheaterUserHourRetentionStatAddReq
}

// TheaterUserHourRetentionStatAddRes 添加操作返回结果
type TheaterUserHourRetentionStatAddRes struct {
	commonApi.EmptyRes
}

// TheaterUserHourRetentionStatEditReq 修改操作请求参数
type TheaterUserHourRetentionStatEditReq struct {
	g.Meta `path:"/edit" tags:"按小时统计剧集相关的数据 " method:"put" summary:"按小时统计剧集相关的数据 修改"`
	commonApi.Author
	*model.TheaterUserHourRetentionStatEditReq
}

// TheaterUserHourRetentionStatEditRes 修改操作返回结果
type TheaterUserHourRetentionStatEditRes struct {
	commonApi.EmptyRes
}

// TheaterUserHourRetentionStatGetReq 获取一条数据请求
type TheaterUserHourRetentionStatGetReq struct {
	g.Meta `path:"/get" tags:"按小时统计剧集相关的数据 " method:"get" summary:"获取按小时统计剧集相关的数据 信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// TheaterUserHourRetentionStatGetRes 获取一条数据结果
type TheaterUserHourRetentionStatGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterUserHourRetentionStatInfoRes
}

// TheaterUserHourRetentionStatDeleteReq 删除数据请求
type TheaterUserHourRetentionStatDeleteReq struct {
	g.Meta `path:"/delete" tags:"按小时统计剧集相关的数据 " method:"delete" summary:"删除按小时统计剧集相关的数据 "`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// TheaterUserHourRetentionStatDeleteRes 删除数据返回
type TheaterUserHourRetentionStatDeleteRes struct {
	commonApi.EmptyRes
}
