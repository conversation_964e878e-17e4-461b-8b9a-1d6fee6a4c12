/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// BusinessPlatformCompanyInfoGetV3ApiService BusinessPlatformCompanyInfoGetV3Api service
type BusinessPlatformCompanyInfoGetV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *model.BusinessPlatformCompanyInfoGetV3Request
}

func (r *BusinessPlatformCompanyInfoGetV3ApiService) SetCfg(cfg *conf.Configuration) *BusinessPlatformCompanyInfoGetV3ApiService {
	r.cfg = cfg
	return r
}

func (r *BusinessPlatformCompanyInfoGetV3ApiService) BusinessPlatformCompanyInfoGetV3Request(businessPlatformCompanyInfoGetV3Request model.BusinessPlatformCompanyInfoGetV3Request) *BusinessPlatformCompanyInfoGetV3ApiService {
	r.Request = &businessPlatformCompanyInfoGetV3Request
	return r
}

func (r *BusinessPlatformCompanyInfoGetV3ApiService) AccessToken(accessToken string) *BusinessPlatformCompanyInfoGetV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *BusinessPlatformCompanyInfoGetV3ApiService) Do() (data *model.BusinessPlatformCompanyInfoGetV3Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/business_platform/company_info/get/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "organization_id", r.Request.OrganizationId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&model.BusinessPlatformCompanyInfoGetV3Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(model.BusinessPlatformCompanyInfoGetV3Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/business_platform/company_info/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
