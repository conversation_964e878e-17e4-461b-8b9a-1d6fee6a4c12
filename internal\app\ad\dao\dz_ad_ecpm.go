// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/dao/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// dzAdEcpmDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzAdEcpmDao struct {
	*internal.DzAdEcpmDao
}

var (
	// DzAdEcpm is globally public accessible object for table tools_gen_table operations.
	DzAdEcpm = dzAdEcpmDao{
		internal.NewDzAdEcpmDao(),
	}
	DzAdEcpmAnalytic = dzAdEcpmDao{
		internal.NewDzAdEcpmAnalyticDao(),
	}
)

// Fill with you ideas below.
