// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:30
// 生成路径: api/v1/oceanengine/ad_strategy_config.go
// 生成人：gfast
// desc:巨量广告搭建-策略配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyGenerateReq 生成广告预览请求参数
type AdStrategyGenerateReq struct {
	g.Meta `path:"/generateAdPreview" tags:"巨量广告搭建-生成广告" method:"post" summary:"巨量广告搭建-生成广告预览"`
	commonApi.Author
	*model.AdStrategyGenerateReq
}

// AdStrategyGenerateRes 生成广告返回结果
type AdStrategyGenerateRes struct {
	commonApi.EmptyRes
	*model.AdStrategyGenerateRes
}

// AdStrategyGetQuotaReq 查询在投计划配额请求参数
type AdStrategyGetQuotaReq struct {
	g.Meta `path:"/getQuota" tags:"巨量广告搭建-生成广告" method:"post" summary:"巨量广告搭建-查询在投计划配额"`
	commonApi.Author
	*model.AdStrategyGetQuotaReq
}

// AdStrategyGetQuotaRes 查询在投计划配额返回结果
type AdStrategyGetQuotaRes struct {
	commonApi.EmptyRes
	*model.AdStrategyGetQuotaRes
}

type AdExecuteTaskReq struct {
	g.Meta `path:"/executeTask" tags:"巨量广告搭建-生成广告" method:"post" summary:"巨量广告搭建-执行任务"`
	commonApi.Author
	*model.AdExecuteTaskRes
}

type AdExecuteTaskRes struct {
	commonApi.EmptyRes
}
