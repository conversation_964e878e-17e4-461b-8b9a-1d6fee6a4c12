// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-10 16:07:13
// 生成路径: api/v1/channel/ad_reward_setting.go
// 生成人：cq
// desc:激励广告配置表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// AdRewardSettingSearchReq 分页请求参数
type AdRewardSettingSearchReq struct {
	g.Meta `path:"/list" tags:"激励广告配置表信息" method:"get" summary:"激励广告配置表信息列表"`
	commonApi.Author
	model.AdRewardSettingSearchReq
}

// AdRewardSettingSearchRes 列表返回结果
type AdRewardSettingSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdRewardSettingSearchRes
}

// AdRewardSettingAddReq 添加操作请求参数
type AdRewardSettingAddReq struct {
	g.Meta `path:"/add" tags:"激励广告配置表信息" method:"post" summary:"激励广告配置表信息添加"`
	commonApi.Author
	*model.AdRewardSettingAddReq
}

// AdRewardSettingAddRes 添加操作返回结果
type AdRewardSettingAddRes struct {
	commonApi.EmptyRes
}

// AdRewardSettingEditReq 修改操作请求参数
type AdRewardSettingEditReq struct {
	g.Meta `path:"/edit" tags:"激励广告配置表信息" method:"put" summary:"激励广告配置表信息修改"`
	commonApi.Author
	*model.AdRewardSettingEditReq
}

// AdRewardSettingEditRes 修改操作返回结果
type AdRewardSettingEditRes struct {
	commonApi.EmptyRes
}

// AdRewardSettingGetReq 获取一条数据请求
type AdRewardSettingGetReq struct {
	g.Meta `path:"/get" tags:"激励广告配置表信息" method:"get" summary:"获取激励广告配置表信息信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdRewardSettingGetRes 获取一条数据结果
type AdRewardSettingGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdRewardSettingInfoRes
}

// AdRewardSettingDeleteReq 删除数据请求
type AdRewardSettingDeleteReq struct {
	g.Meta `path:"/delete" tags:"激励广告配置表信息" method:"delete" summary:"删除激励广告配置表信息"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdRewardSettingDeleteRes 删除数据返回
type AdRewardSettingDeleteRes struct {
	commonApi.EmptyRes
}
