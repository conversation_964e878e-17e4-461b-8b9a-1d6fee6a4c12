// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/dao/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdAccountDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdAccountDao struct {
	*internal.FqAdAccountDao
}

var (
	// FqAdAccount is globally public accessible object for table tools_gen_table operations.
	FqAdAccount = fqAdAccountDao{
		internal.NewFqAdAccountDao(),
	}
)

// Fill with you ideas below.
