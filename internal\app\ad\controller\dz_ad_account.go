// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:26
// 生成路径: internal/app/ad/controller/dz_ad_account.go
// 生成人：cyao
// desc:点众账号管理表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type dzAdAccountController struct {
	systemController.BaseController
}

var DzAdAccount = new(dzAdAccountController)

// List 列表
func (c *dzAdAccountController) List(ctx context.Context, req *ad.DzAdAccountSearchReq) (res *ad.DzAdAccountSearchRes, err error) {
	res = new(ad.DzAdAccountSearchRes)
	res.DzAdAccountSearchRes, err = service.DzAdAccount().List(ctx, &req.DzAdAccountSearchReq)
	return
}
func (c *dzAdAccountController) ExcelTemplate(ctx context.Context, req *ad.DzAdAccountExcelTemplateReq) (res *ad.DzAdAccountExcelTemplateRes, err error) {
	var (
		r = ghttp.RequestFromCtx(ctx)
		//表头
		tableHead = []interface{}{"番茄账号名称", "账号ID", "接口token", "备注", "创建时间", "更新时间"}
		excelData = [][]interface{}{tableHead}
	)
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("点众账号管理表模板")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}
func (c *dzAdAccountController) Import(ctx context.Context, req *ad.DzAdAccountImportReq) (res *ad.DzAdAccountImportRes, err error) {
	err = service.DzAdAccount().Import(ctx, req.File)
	return
}

// Get 获取点众账号管理表
func (c *dzAdAccountController) Get(ctx context.Context, req *ad.DzAdAccountGetReq) (res *ad.DzAdAccountGetRes, err error) {
	res = new(ad.DzAdAccountGetRes)
	res.DzAdAccountInfoRes, err = service.DzAdAccount().GetById(ctx, req.Id)
	return
}

// Add 添加点众账号管理表
func (c *dzAdAccountController) Add(ctx context.Context, req *ad.DzAdAccountAddReq) (res *ad.DzAdAccountAddRes, err error) {
	err = service.DzAdAccount().Add(ctx, req.DzAdAccountAddReq)
	return
}

// Edit 修改点众账号管理表
func (c *dzAdAccountController) Edit(ctx context.Context, req *ad.DzAdAccountEditReq) (res *ad.DzAdAccountEditRes, err error) {
	err = service.DzAdAccount().Edit(ctx, req.DzAdAccountEditReq)
	return
}

// Delete 删除点众账号管理表
func (c *dzAdAccountController) Delete(ctx context.Context, req *ad.DzAdAccountDeleteReq) (res *ad.DzAdAccountDeleteRes, err error) {
	err = service.DzAdAccount().Delete(ctx, req.Ids)
	return
}
