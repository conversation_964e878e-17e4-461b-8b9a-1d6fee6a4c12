// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-31 15:46:43
// 生成路径: api/v1/oceanengine/ad_promotion_subscribe_record.go
// 生成人：cq
// desc:巨量广告报表订阅记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdPromotionSubscribeRecordSearchReq 分页请求参数
type AdPromotionSubscribeRecordSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告报表订阅记录" method:"get" summary:"巨量广告报表订阅记录列表"`
	commonApi.Author
	model.AdPromotionSubscribeRecordSearchReq
}

// AdPromotionSubscribeRecordSearchRes 列表返回结果
type AdPromotionSubscribeRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionSubscribeRecordSearchRes
}

// AdPromotionSubscribeRecordAddReq 添加操作请求参数
type AdPromotionSubscribeRecordAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告报表订阅记录" method:"post" summary:"巨量广告报表订阅记录添加"`
	commonApi.Author
	*model.AdPromotionSubscribeRecordAddReq
}

// AdPromotionSubscribeRecordAddRes 添加操作返回结果
type AdPromotionSubscribeRecordAddRes struct {
	commonApi.EmptyRes
}

// AdPromotionSubscribeRecordEditReq 修改操作请求参数
type AdPromotionSubscribeRecordEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告报表订阅记录" method:"put" summary:"巨量广告报表订阅记录修改"`
	commonApi.Author
	*model.AdPromotionSubscribeRecordEditReq
}

// AdPromotionSubscribeRecordEditRes 修改操作返回结果
type AdPromotionSubscribeRecordEditRes struct {
	commonApi.EmptyRes
}

// AdPromotionSubscribeRecordGetReq 获取一条数据请求
type AdPromotionSubscribeRecordGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告报表订阅记录" method:"get" summary:"获取巨量广告报表订阅记录信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPromotionSubscribeRecordGetRes 获取一条数据结果
type AdPromotionSubscribeRecordGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionSubscribeRecordInfoRes
}

// AdPromotionSubscribeRecordDeleteReq 删除数据请求
type AdPromotionSubscribeRecordDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告报表订阅记录" method:"delete" summary:"删除巨量广告报表订阅记录"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPromotionSubscribeRecordDeleteRes 删除数据返回
type AdPromotionSubscribeRecordDeleteRes struct {
	commonApi.EmptyRes
}
