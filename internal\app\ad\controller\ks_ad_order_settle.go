// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: internal/app/ad/controller/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type ksAdOrderSettleController struct {
	systemController.BaseController
}

var KsAdOrderSettle = new(ksAdOrderSettleController)

// List 列表
func (c *ksAdOrderSettleController) List(ctx context.Context, req *ad.KsAdOrderSettleSearchReq) (res *ad.KsAdOrderSettleSearchRes, err error) {
	res = new(ad.KsAdOrderSettleSearchRes)
	res.KsAdOrderSettleSearchRes, err = service.KsAdOrderSettle().List(ctx, &req.KsAdOrderSettleSearchReq)
	return
}

// Export 导出excel
func (c *ksAdOrderSettleController) Export(ctx context.Context, req *ad.KsAdOrderSettleExportReq) (res *ad.KsAdOrderSettleExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.KsAdOrderSettleListRes
		//表头
		tableHead = []interface{}{"支付时间", "快手账号", "快手账号id", "支付渠道", "总结算收入(元)", "本账号结算(元)", "分销分成前结算(元)（版权视角）",
			"分销分成前结算(元)（分销视角）", "总支出", "退款金额(元)", "佣金(元)", "可提现金额(元)"}
		excelData [][]interface{}
		//字典选项处理
	)
	if req.OrderType == commonConsts.DistributionSale {
		tableHead = []interface{}{"支付时间", "快手账号", "快手账号id", "支付渠道", "版权商ID", "版权商", "总结算收入(元)", "本账号结算(元)", "分销分成前结算(元)（版权视角）",
			"分销分成前结算(元)（分销视角）", "总支出", "退款金额(元)", "佣金(元)", "可提现金额(元)"}
	}
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.KsAdOrderSettle().GetExportData(ctx, &req.KsAdOrderSettleSearchReq)
		if err != nil {
			return
		}
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			var dt []interface{}
			if req.OrderType == commonConsts.DistributionSale {
				dt = []interface{}{
					v.PayDate,
					v.AdvertiserName,
					v.AdvertiserId,
					v.PayProvider,
					v.CopyrightUid,
					v.CopyrightName,
					v.PayAmt,
					v.CurAccountOrderPayAmt,
					v.CopyrightDistributionOrderPayAmt,
					v.SalerDistributionOrderPayAmt,
					v.Expenditure,
					v.RedundPrice,
					v.CommissionPrice,
					v.SettlePrice,
				}
			} else {
				dt = []interface{}{
					v.PayDate,
					v.AdvertiserName,
					v.AdvertiserId,
					v.PayProvider,
					v.PayAmt,
					v.CurAccountOrderPayAmt,
					v.CopyrightDistributionOrderPayAmt,
					v.SalerDistributionOrderPayAmt,
					v.Expenditure,
					v.RedundPrice,
					v.CommissionPrice,
					v.SettlePrice,
				}
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("快手订单日结算汇总")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取快手订单日结算汇总
func (c *ksAdOrderSettleController) Get(ctx context.Context, req *ad.KsAdOrderSettleGetReq) (res *ad.KsAdOrderSettleGetRes, err error) {
	res = new(ad.KsAdOrderSettleGetRes)
	res.KsAdOrderSettleInfoRes, err = service.KsAdOrderSettle().GetById(ctx, req.Id)
	return
}

// Add 添加快手订单日结算汇总
func (c *ksAdOrderSettleController) Add(ctx context.Context, req *ad.KsAdOrderSettleAddReq) (res *ad.KsAdOrderSettleAddRes, err error) {
	err = service.KsAdOrderSettle().Add(ctx, req.KsAdOrderSettleAddReq)
	return
}

// Edit 修改快手订单日结算汇总
func (c *ksAdOrderSettleController) Edit(ctx context.Context, req *ad.KsAdOrderSettleEditReq) (res *ad.KsAdOrderSettleEditRes, err error) {
	err = service.KsAdOrderSettle().Edit(ctx, req.KsAdOrderSettleEditReq)
	return
}

// Delete 删除快手订单日结算汇总
func (c *ksAdOrderSettleController) Delete(ctx context.Context, req *ad.KsAdOrderSettleDeleteReq) (res *ad.KsAdOrderSettleDeleteRes, err error) {
	err = service.KsAdOrderSettle().Delete(ctx, req.Ids)
	return
}

// RunCalcAdOrderSettleStat 快手订单日结算汇总任务
func (c *ksAdOrderSettleController) RunCalcAdOrderSettleStat(ctx context.Context, req *ad.KsAdOrderSettleTaskReq) (res *ad.KsAdOrderSettleTaskRes, err error) {
	err = service.KsAdOrderSettle().RunCalcAdOrderSettleStat(ctx, &req.KsAdOrderSettleSearchReq)
	return
}
