// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: api/v1/ad/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdUserRewardClickSearchReq 分页请求参数
type FqAdUserRewardClickSearchReq struct {
	g.Meta `path:"/list" tags:"番茄用户激励点击记录表" method:"post" summary:"番茄用户激励点击记录表列表"`
	commonApi.Author
	model.FqAdUserRewardClickSearchReq
}

// FqAdUserRewardClickSearchRes 列表返回结果
type FqAdUserRewardClickSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdUserRewardClickSearchRes
}

// FqAdUserRewardClickExportReq 导出请求
type FqAdUserRewardClickExportReq struct {
	g.Meta `path:"/export" tags:"番茄用户激励点击记录表" method:"get" summary:"番茄用户激励点击记录表导出"`
	commonApi.Author
	model.FqAdUserRewardClickSearchReq
}

// FqAdUserRewardClickExportRes 导出响应
type FqAdUserRewardClickExportRes struct {
	commonApi.EmptyRes
}

// FqAdUserRewardClickAddReq 添加操作请求参数
type FqAdUserRewardClickAddReq struct {
	g.Meta `path:"/add" tags:"番茄用户激励点击记录表" method:"post" summary:"番茄用户激励点击记录表添加"`
	commonApi.Author
	*model.FqAdUserRewardClickInfoSaveRes
}

// FqAdUserRewardClickAddRes 添加操作返回结果
type FqAdUserRewardClickAddRes struct {
	commonApi.EmptyRes
}

// FqAdUserRewardClickEditReq 修改操作请求参数
type FqAdUserRewardClickEditReq struct {
	g.Meta `path:"/edit" tags:"番茄用户激励点击记录表" method:"put" summary:"番茄用户激励点击记录表修改"`
	commonApi.Author
	*model.FqAdUserRewardClickEditReq
}

// FqAdUserRewardClickEditRes 修改操作返回结果
type FqAdUserRewardClickEditRes struct {
	commonApi.EmptyRes
}

// FqAdUserRewardClickGetReq 获取一条数据请求
type FqAdUserRewardClickGetReq struct {
	g.Meta `path:"/get" tags:"番茄用户激励点击记录表" method:"get" summary:"获取番茄用户激励点击记录表信息"`
	commonApi.Author
	EcpmNo string `p:"ecpmNo" v:"required#主键必须"` //通过主键获取
}

// FqAdUserRewardClickGetRes 获取一条数据结果
type FqAdUserRewardClickGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdUserRewardClickInfoRes
}

// FqAdUserRewardClickDeleteReq 删除数据请求
type FqAdUserRewardClickDeleteReq struct {
	g.Meta `path:"/delete" tags:"番茄用户激励点击记录表" method:"delete" summary:"删除番茄用户激励点击记录表"`
	commonApi.Author
	EcpmNos []string `p:"ecpmNos" v:"required#主键必须"` //通过主键删除
}

// FqAdUserRewardClickDeleteRes 删除数据返回
type FqAdUserRewardClickDeleteRes struct {
	commonApi.EmptyRes
}
