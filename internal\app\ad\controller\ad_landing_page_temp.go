// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: internal/app/ad/controller/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type adLandingPageTempController struct {
	systemController.BaseController
}

var AdLandingPageTemp = new(adLandingPageTempController)

// List 列表
func (c *adLandingPageTempController) List(ctx context.Context, req *ad.AdLandingPageTempSearchReq) (res *ad.AdLandingPageTempSearchRes, err error) {
	res = new(ad.AdLandingPageTempSearchRes)
	res.AdLandingPageTempSearchRes, err = service.AdLandingPageTemp().List(ctx, &req.AdLandingPageTempSearchReq)
	return
}

// Export 导出excel
func (c *adLandingPageTempController) Export(ctx context.Context, req *ad.AdLandingPageTempExportReq) (res *ad.AdLandingPageTempExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdLandingPageTempInfoRes
		//表头
		tableHead = []interface{}{"ID", "创建用户id", "模板名称", "模板类型 0 应用下载  1 微信小程序  2微信小游戏", "json的结构体详情结构见文档", "广告数量", "创建时间", "更新时间", "删除时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.AdLandingPageTemp().GetExportData(ctx, &req.AdLandingPageTempSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.Id,
				v.MainUserId,
				v.TempleName,
				v.TempleType,
				v.Bricks,
				v.AdNum,
				v.CreateTime.Format("Y-m-d H:i:s"),
				v.UpdateTime.Format("Y-m-d H:i:s"),
				v.DeletedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("落地页模板")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取落地页模板
func (c *adLandingPageTempController) Get(ctx context.Context, req *ad.AdLandingPageTempGetReq) (res *ad.AdLandingPageTempGetRes, err error) {
	res = new(ad.AdLandingPageTempGetRes)
	res.AdLandingPageTempInfoRes, err = service.AdLandingPageTemp().GetById(ctx, req.Id)
	return
}

// Add 添加落地页模板
func (c *adLandingPageTempController) Add(ctx context.Context, req *ad.AdLandingPageTempAddReq) (res *ad.AdLandingPageTempAddRes, err error) {
	err = service.AdLandingPageTemp().Add(ctx, req.AdLandingPageTempAddReq)
	return
}

// Edit 修改落地页模板
func (c *adLandingPageTempController) Edit(ctx context.Context, req *ad.AdLandingPageTempEditReq) (res *ad.AdLandingPageTempEditRes, err error) {
	err = service.AdLandingPageTemp().Edit(ctx, req.AdLandingPageTempEditReq)
	return
}

// Delete 删除落地页模板
func (c *adLandingPageTempController) Delete(ctx context.Context, req *ad.AdLandingPageTempDeleteReq) (res *ad.AdLandingPageTempDeleteRes, err error) {
	err = service.AdLandingPageTemp().Delete(ctx, req.Ids)
	return
}
