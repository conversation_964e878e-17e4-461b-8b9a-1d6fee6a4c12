// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: api/v1/ad/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// MpAdEventSearchReq 分页请求参数
type MpAdEventSearchReq struct {
	g.Meta `path:"/list" tags:"dy小程序广告事件记录" method:"get" summary:"dy小程序广告事件记录列表"`
	commonApi.Author
	model.MpAdEventSearchReq
}

// MpAdEventSearchRes 列表返回结果
type MpAdEventSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MpAdEventSearchRes
}

type MpAdEventPullDataReq struct {
	g.Meta `path:"/pullData" tags:"dy小程序广告事件记录" method:"get" summary:"pullData"`
	commonApi.Author
	*model.MpAdEventPullDataReq
}
type MpAdEventPullDataRes struct {
	commonApi.EmptyRes
}

// MpAdEventAddReq 添加操作请求参数
type MpAdEventAddReq struct {
	g.Meta `path:"/add" tags:"dy小程序广告事件记录" method:"post" summary:"dy小程序广告事件记录添加"`
	commonApi.Author
	*model.MpAdEventAddReq
}

// MpAdEventAddRes 添加操作返回结果
type MpAdEventAddRes struct {
	commonApi.EmptyRes
}

// MpAdEventEditReq 修改操作请求参数
type MpAdEventEditReq struct {
	g.Meta `path:"/edit" tags:"dy小程序广告事件记录" method:"put" summary:"dy小程序广告事件记录修改"`
	commonApi.Author
	*model.MpAdEventEditReq
}

// MpAdEventEditRes 修改操作返回结果
type MpAdEventEditRes struct {
	commonApi.EmptyRes
}

// MpAdEventGetReq 获取一条数据请求
type MpAdEventGetReq struct {
	g.Meta `path:"/get" tags:"dy小程序广告事件记录" method:"get" summary:"获取dy小程序广告事件记录信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// MpAdEventGetRes 获取一条数据结果
type MpAdEventGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MpAdEventInfoRes
}

// MpAdEventDeleteReq 删除数据请求
type MpAdEventDeleteReq struct {
	g.Meta `path:"/delete" tags:"dy小程序广告事件记录" method:"delete" summary:"删除dy小程序广告事件记录"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MpAdEventDeleteRes 删除数据返回
type MpAdEventDeleteRes struct {
	commonApi.EmptyRes
}
