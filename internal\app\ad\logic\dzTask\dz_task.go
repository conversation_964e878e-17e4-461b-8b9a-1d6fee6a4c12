// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-08 14:56:12
// 生成路径: internal/app/ad/logic/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzTask(New())
}

func New() service.IDzTask {
	return &sDzTask{}
}

type sDzTask struct{}

func (s *sDzTask) List(ctx context.Context, req *model.DzTaskSearchReq) (listRes *model.DzTaskSearchRes, err error) {
	listRes = new(model.DzTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzTask.Ctx(ctx).WithAll()
		if req.TaskId != "" {
			m = m.Where(dao.DzTask.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.QueryUrl != "" {
			m = m.Where(dao.DzTask.Columns().QueryUrl+" = ?", req.QueryUrl)
		}
		if req.QueryJson != "" {
			m = m.Where(dao.DzTask.Columns().QueryJson+" = ?", req.QueryJson)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.DzTask.Columns().CreatedAt+" >=? AND "+dao.DzTask.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "task_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzTaskListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzTaskListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzTaskListRes{
				TaskId:    v.TaskId,
				QueryUrl:  v.QueryUrl,
				QueryJson: v.QueryJson,
				Xurl:      v.Xurl,
				Status:    v.Status,
				CreatedAt: v.CreatedAt,
			}
		}
	})
	return
}

func (s *sDzTask) GetByTaskId(ctx context.Context, taskId string) (res *model.DzTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzTask.Ctx(ctx).WithAll().Where(dao.DzTask.Columns().TaskId, taskId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzTask) Add(ctx context.Context, req *model.DzTaskAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzTask.Ctx(ctx).Insert(do.DzTask{
			TaskId:    req.TaskId,
			QueryUrl:  req.QueryUrl,
			QueryJson: req.QueryJson,
			Xurl:      req.Xurl,
			Status:    req.Status,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzTask) Edit(ctx context.Context, req *model.DzTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzTask.Ctx(ctx).WherePri(req.TaskId).Update(do.DzTask{
			QueryUrl:  req.QueryUrl,
			QueryJson: req.QueryJson,
			Xurl:      req.Xurl,
			Status:    req.Status,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzTask) Delete(ctx context.Context, taskIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzTask.Ctx(ctx).Delete(dao.DzTask.Columns().TaskId+" in (?)", taskIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
