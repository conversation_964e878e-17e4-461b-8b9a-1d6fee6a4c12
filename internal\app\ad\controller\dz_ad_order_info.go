// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:38
// 生成路径: internal/app/ad/controller/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"time"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type dzAdOrderInfoController struct {
	systemController.BaseController
}

var DzAdOrderInfo = new(dzAdOrderInfoController)

// List 列表
func (c *dzAdOrderInfoController) List(ctx context.Context, req *ad.DzAdOrderInfoSearchReq) (res *ad.DzAdOrderInfoSearchRes, err error) {
	res = new(ad.DzAdOrderInfoSearchRes)
	res.DzAdOrderInfoSearchRes, err = service.DzAdOrderInfo().List(ctx, &req.DzAdOrderInfoSearchReq)
	return
}

// Callback 回调订单
func (c *dzAdOrderInfoController) Callback(ctx context.Context, req *ad.DzAdOrderInfoCallbackReq) (res *ad.DzAdOrderInfoCallbackRes, err error) {
	res = new(ad.DzAdOrderInfoCallbackRes)
	innerContext, cancel := context.WithTimeout(context.Background(), 60*time.Minute)
	defer cancel()
	err = service.DzAdOrderInfo().Callback(innerContext, req.DzAdOrderInfoCallbackReq)
	return

}

// Export 导出excel
func (c *dzAdOrderInfoController) Export(ctx context.Context, req *ad.DzAdOrderInfoExportReq) (res *ad.DzAdOrderInfoExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.DzAdOrderInfoListRes
		response *model.DzAdOrderInfoSearchRes
		//表头
		tableHead = []interface{}{"下单时间", "支付时间", "点众账号", "点众渠道", "分销", "投手", "交易单号", "订单类型", "订单状态", "订单金额（单位元）", "渠道分成金额", "广告ID", "用户ID", "注册时间", "染色时间", "投放媒体", "来源短剧", "机型", "达人ID"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		response, err = service.DzAdOrderInfo().List(ctx, &req.DzAdOrderInfoSearchReq)
		if err != nil {
			return
		}
		if response == nil || len(response.List) == 0 {
			return
		}
		listData = response.List
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.CreateTime,
				v.PayTime,
				v.DzAccount,
				v.DzChannel,
				v.DistributorName,
				v.UserName,
				v.OutTradeNo,
				v.Type,
				v.StatusNotify,
				v.Discount,
				v.MoneyBenefit,
				v.ReferralId,
				v.UserId,
				//v.AppType,
				v.RegisterDate,
				v.DyeTime,
				v.From,
				v.SourceDesc,
				v.Os,
				v.FromDrId,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("广告订单信息表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取广告订单信息表
func (c *dzAdOrderInfoController) Get(ctx context.Context, req *ad.DzAdOrderInfoGetReq) (res *ad.DzAdOrderInfoGetRes, err error) {
	res = new(ad.DzAdOrderInfoGetRes)
	res.DzAdOrderInfoInfoRes, err = service.DzAdOrderInfo().GetById(ctx, req.Id)
	return
}

// Add 添加广告订单信息表
func (c *dzAdOrderInfoController) Add(ctx context.Context, req *ad.DzAdOrderInfoAddReq) (res *ad.DzAdOrderInfoAddRes, err error) {
	err = service.DzAdOrderInfo().Add(ctx, req.DzAdOrderInfoAddReq)
	return
}

// Edit 修改广告订单信息表
func (c *dzAdOrderInfoController) Edit(ctx context.Context, req *ad.DzAdOrderInfoEditReq) (res *ad.DzAdOrderInfoEditRes, err error) {
	err = service.DzAdOrderInfo().Edit(ctx, req.DzAdOrderInfoEditReq)
	return
}

// Delete 删除广告订单信息表
func (c *dzAdOrderInfoController) Delete(ctx context.Context, req *ad.DzAdOrderInfoDeleteReq) (res *ad.DzAdOrderInfoDeleteRes, err error) {
	err = service.DzAdOrderInfo().Delete(ctx, req.Ids)
	return
}

// PullOrderData
func (c *dzAdOrderInfoController) PullOrderData(ctx context.Context, req *ad.DzAdOrderInfoPullOrderDataReq) (res *ad.DzAdOrderInfoPullOrderDataRes, err error) {
	// 修改ctx 不超时
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	for {
		if req.StartTime > req.EndTime {
			break
		}
		err = service.DzAdOrderInfo().PullOrderData(innerCtx, req.StartTime)
		if err != nil {
			g.Log().Error(ctx, err)
		}

		req.StartTime = libUtils.PlusDays(req.StartTime, 1)
	}
	return
}
