// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-07 15:25:37
// 生成路径: internal/app/ad/dao/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// dzAdOrderInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzAdOrderInfoDao struct {
	*internal.DzAdOrderInfoDao
}

var (
	// DzAdOrderInfo is globally public accessible object for table tools_gen_table operations.
	DzAdOrderInfo = dzAdOrderInfoDao{
		internal.NewDzAdOrderInfoDao(),
	}
	DzAdOrderInfoAnalytic = dzAdOrderInfoDao{
		internal.NewDzAdOrderInfoAnalyticDao(),
	}
)

// Fill with you ideas below.
