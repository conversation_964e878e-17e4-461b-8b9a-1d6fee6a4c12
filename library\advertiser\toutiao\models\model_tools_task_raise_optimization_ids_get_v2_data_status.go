/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsTaskRaiseOptimizationIdsGetV2DataStatus
type ToolsTaskRaiseOptimizationIdsGetV2DataStatus string

// List of tools_task_raise_optimization_ids_get_v2_data_status
const (
	DISABLERAISE_ToolsTaskRaiseOptimizationIdsGetV2DataStatus ToolsTaskRaiseOptimizationIdsGetV2DataStatus = "DISABLERAISE"
	ENABLERAISE_ToolsTaskRaiseOptimizationIdsGetV2DataStatus  ToolsTaskRaiseOptimizationIdsGetV2DataStatus = "ENABLERAISE"
	RAISING_ToolsTaskRaiseOptimizationIdsGetV2DataStatus      ToolsTaskRaiseOptimizationIdsGetV2DataStatus = "RAISING"
)

// Ptr returns reference to tools_task_raise_optimization_ids_get_v2_data_status value
func (v ToolsTaskRaiseOptimizationIdsGetV2DataStatus) Ptr() *ToolsTaskRaiseOptimizationIdsGetV2DataStatus {
	return &v
}
