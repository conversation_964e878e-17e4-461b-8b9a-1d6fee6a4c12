// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/model/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdEcpmInfoRes is the golang structure for table dz_ad_ecpm.
type DzAdEcpmInfoRes struct {
	gmeta.Meta  `orm:"table:dz_ad_ecpm"`
	Id          uint64      `orm:"id,primary" json:"id" dc:"主键ID"`                  // 主键ID
	Time        int64       `orm:"time" json:"time" dc:"请求时间戳，单位毫秒"`                // 请求时间戳，单位毫秒
	UserId      string      `orm:"user_id" json:"userId" dc:"用户ID"`                 // 用户ID
	ChannelId   string      `orm:"channel_id" json:"channelId" dc:"渠道ID"`           // 渠道ID
	AppId       string      `orm:"app_id" json:"appId" dc:"小程序ID"`                  // 小程序ID
	PromotionId string      `orm:"promotion_id" json:"promotionId" dc:"推广链路ID"`     // 推广链路ID
	OpenId      string      `orm:"open_id" json:"openId" dc:"openID"`               // openID
	EcpmId      string      `orm:"ecpm_id" json:"ecpmId" dc:"ecpm接口ID"`             // ecpm接口ID
	EcpmCost    string      `orm:"ecpm_cost" json:"ecpmCost" dc:"ecpm接口成本，单位十万分之元"` // ecpm接口成本，单位十万分之元
	AdType      string      `orm:"ad_type" json:"adType" dc:"广告类型，如激励视频广告/插屏广告等"`   // 广告类型，如激励视频广告/插屏广告等
	EventTime   int64       `orm:"event_time" json:"eventTime" dc:"ecpm接口事件时间，单位秒"` // ecpm接口事件时间，单位秒
	DyeTime     int64       `orm:"dye_time" json:"dyeTime" dc:"用户染色时间戳，单位秒"`        // 用户染色时间戳，单位秒
	CreateDate  string      `orm:"create_date" json:"createDate" dc:"创建时间"`
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
}

type DzAdEcpmListRes struct {

	//create_date
	CreateDate      string  `json:"createDate" dc:"日期"`
	DzAccount       string  `json:"dzAccount" dc:"点众账号"`
	DzChannel       string  `json:"dzChannel" dc:"点众渠道"`
	DistributorName string  `json:"distributorName" dc:"分销"`
	UserName        string  `json:"userName" dc:"投手"`
	UserId          string  `json:"userId" dc:"用户ID"`
	RegisterTime    string  `json:"registerTime" dc:"用户注册时间"`
	DyeTime         string  `json:"dyeTime" dc:"用户染色时间"`
	PromotionId     string  `json:"promotionId" dc:"广告ID"`
	AdType          string  `json:"adType" dc:"广告类型，如激励视频广告/插屏广告等"`
	SumCost         float64 `json:"sumCost" dc:"收入金额"`
	ChannelId       string  `json:"channelId" dc:"渠道ID"`
	ChannelCode     string  `orm:"channel_code" json:"channelCode" dc:"渠道号"`

	//Time      string      `json:"Time" dc:"请求时间戳，单位毫秒"`
	//AppId     string      `json:"appId" dc:"小程序ID"`
	//OpenId    string      `json:"openId" dc:"openID"`
	//EcpmId    string      `json:"ecpmId" dc:"ecpm接口ID"`
	//EcpmCost  string      `json:"ecpmCost" dc:"ecpm接口成本，单位十万分之元"`
	//EventTime int64       `json:"eventTime" dc:"ecpm接口事件时间，单位秒"`
	//Id        uint64      `json:"id" dc:"主键ID"`
	//CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// DzAdEcpmSearchReq 分页请求参数
type DzAdEcpmSearchReq struct {
	comModel.PageReq

	AccountIds    []int64 `p:"accountIds" dc:"账户IDs"`
	ChannelIds    []int64 `p:"channelIds" dc:"渠道IDs"`
	DistributorId int     `p:"distributorId" dc:"分销id"`
	//部门id
	DeptIds           []int  `p:"deptIds"`
	PitcherId         int    `p:"pitcherId" dc:"投手id"`
	StartTime         string `p:"startTime" dc:"下单时间"`
	EndTime           string `p:"endTime" dc:"结束下单时间 YYYY-MM-DD格式"`
	RegisterStartTime string `p:"registerStartTime" dc:"付费开始时间"`
	RegisterEndTime   string `p:"registerEndTime" dc:"付费结束时间"`
	DyeStartTime      string `p:"dyeStartTime" dc:"用户染色时间戳"`
	DyeEndTime        string `p:"dyeEndTime" dc:"用户染色时间戳"`
	PromotionId       string `p:"promotionId" dc:"广告id"`          //推广链路ID
	AdType            string `p:"adType" dc:"广告类型，如激励视频广告/插屏广告等"` //广告类型，如激励视频广告/插屏广告等

	Id        string `p:"id" dc:"主键ID"`                                         //主键ID
	Time      string `p:"time" v:"time@integer#请求时间戳，单位毫秒需为整数" dc:"请求时间戳，单位毫秒"` //请求时间戳，单位毫秒
	UserId    string `p:"userId" dc:"用户ID"`                                     //用户ID
	ChannelId string `p:"channelId" dc:"渠道ID"`                                  //渠道ID
	AppId     string `p:"appId" dc:"小程序ID"`                                     //小程序ID
	OpenId    string `p:"openId" dc:"openID"`                                   //openID
	EcpmId    string `p:"ecpmId" dc:"ecpm接口ID"`                                 //ecpm接口ID
	EcpmCost  string `p:"ecpmCost" dc:"ecpm接口成本，单位十万分之元"`                       //ecpm接口成本，单位十万分之元

	EventTime string `p:"eventTime" v:"eventTime@integer#ecpm接口事件时间，单位秒需为整数" dc:"ecpm接口事件时间，单位秒"` //ecpm接口事件时间，单位秒
	DyeTime   string `p:"dyeTime" v:"dyeTime@integer#用户染色时间戳，单位秒需为整数" dc:"用户染色时间戳，单位秒"`           //用户染色时间戳，单位秒
	CreatedAt string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// DzAdEcpmSearchRes 列表返回结果
type DzAdEcpmSearchRes struct {
	comModel.ListRes
	List    []*DzAdEcpmListRes `json:"list"`
	Summary *DzAdEcpmSummary   `json:"summary"`
}
type DzAdEcpmSummary struct {
	SumCost float64 `json:"sumCost" dc:"收入金额"`
}

// DzAdEcpmAddReq 添加操作请求参数
type DzAdEcpmAddReq struct {
	Time        int64  `p:"time" v:"required#请求时间戳，单位毫秒不能为空" dc:"请求时间戳，单位毫秒"`
	UserId      string `p:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	ChannelId   string `p:"channelId"  dc:"渠道ID"`
	AppId       string `p:"appId"  dc:"小程序ID"`
	PromotionId string `p:"promotionId"  dc:"推广链路ID"`
	OpenId      string `p:"openId"  dc:"openID"`
	EcpmId      string `p:"ecpmId"  dc:"ecpm接口ID"`
	EcpmCost    string `p:"ecpmCost"  dc:"ecpm接口成本，单位十万分之元"`
	AdType      string `p:"adType"  dc:"广告类型，如激励视频广告/插屏广告等"`
	EventTime   int64  `p:"eventTime"  dc:"ecpm接口事件时间，单位秒"`
	DyeTime     int64  `p:"dyeTime"  dc:"用户染色时间戳，单位秒"`
	CreateDate  string `p:"createDate" dc:"创建时间"`
}

// DzAdEcpmEditReq 修改操作请求参数
type DzAdEcpmEditReq struct {
	Id          uint64 `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	Time        int64  `p:"time" v:"required#请求时间戳，单位毫秒不能为空" dc:"请求时间戳，单位毫秒"`
	UserId      string `p:"userId" v:"required#用户ID不能为空" dc:"用户ID"`
	ChannelId   string `p:"channelId"  dc:"渠道ID"`
	AppId       string `p:"appId"  dc:"小程序ID"`
	PromotionId string `p:"promotionId"  dc:"推广链路ID"`
	OpenId      string `p:"openId"  dc:"openID"`
	EcpmId      string `p:"ecpmId"  dc:"ecpm接口ID"`
	EcpmCost    string `p:"ecpmCost"  dc:"ecpm接口成本，单位十万分之元"`
	AdType      string `p:"adType"  dc:"广告类型，如激励视频广告/插屏广告等"`
	EventTime   int64  `p:"eventTime"  dc:"ecpm接口事件时间，单位秒"`
	DyeTime     int64  `p:"dyeTime"  dc:"用户染色时间戳，单位秒"`
}
