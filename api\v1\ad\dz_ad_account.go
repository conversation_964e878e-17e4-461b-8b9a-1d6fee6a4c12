// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:25
// 生成路径: api/v1/ad/dz_ad_account.go
// 生成人：cyao
// desc:点众账号管理表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdAccountSearchReq 分页请求参数
type DzAdAccountSearchReq struct {
	g.Meta `path:"/list" tags:"点众账号管理表" method:"post" summary:"点众账号管理表列表"`
	commonApi.Author
	model.DzAdAccountSearchReq
}

// DzAdAccountSearchRes 列表返回结果
type DzAdAccountSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountSearchRes
}
type DzAdAccountExcelTemplateReq struct {
	g.Meta `path:"/excelTemplate" tags:"点众账号管理表" method:"get" summary:"导出模板文件"`
	commonApi.Author
}
type DzAdAccountExcelTemplateRes struct {
	commonApi.EmptyRes
}
type DzAdAccountImportReq struct {
	g.Meta `path:"/import" tags:"点众账号管理表" method:"post" summary:"点众账号管理表导入"`
	commonApi.Author
	File *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}
type DzAdAccountImportRes struct {
	commonApi.EmptyRes
}

// DzAdAccountAddReq 添加操作请求参数
type DzAdAccountAddReq struct {
	g.Meta `path:"/add" tags:"点众账号管理表" method:"post" summary:"点众账号管理表添加"`
	commonApi.Author
	*model.DzAdAccountAddReq
}

// DzAdAccountAddRes 添加操作返回结果
type DzAdAccountAddRes struct {
	commonApi.EmptyRes
}

// DzAdAccountEditReq 修改操作请求参数
type DzAdAccountEditReq struct {
	g.Meta `path:"/edit" tags:"点众账号管理表" method:"put" summary:"点众账号管理表修改"`
	commonApi.Author
	*model.DzAdAccountEditReq
}

// DzAdAccountEditRes 修改操作返回结果
type DzAdAccountEditRes struct {
	commonApi.EmptyRes
}

// DzAdAccountGetReq 获取一条数据请求
type DzAdAccountGetReq struct {
	g.Meta `path:"/get" tags:"点众账号管理表" method:"get" summary:"获取点众账号管理表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// DzAdAccountGetRes 获取一条数据结果
type DzAdAccountGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountInfoRes
}

// DzAdAccountDeleteReq 删除数据请求
type DzAdAccountDeleteReq struct {
	g.Meta `path:"/delete" tags:"点众账号管理表" method:"delete" summary:"删除点众账号管理表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// DzAdAccountDeleteRes 删除数据返回
type DzAdAccountDeleteRes struct {
	commonApi.EmptyRes
}
