// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:25
// 生成路径: internal/app/ad/model/entity/dz_ad_account.go
// 生成人：cyao
// desc:点众账号管理表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdAccount is the golang structure for table dz_ad_account.
type DzAdAccount struct {
	gmeta.Meta  `orm:"table:dz_ad_account"`
	Id          uint64      `orm:"id,primary" json:"id"`            // 主键ID
	AccountName string      `orm:"account_name" json:"accountName"` // 番茄账号名称
	AccountId   string      `orm:"account_id" json:"accountId"`     // 账号ID
	Token       string      `orm:"token" json:"token"`              // 接口token
	Remark      string      `orm:"remark" json:"remark"`            // 备注
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt"`     // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt"`     // 更新时间
}
