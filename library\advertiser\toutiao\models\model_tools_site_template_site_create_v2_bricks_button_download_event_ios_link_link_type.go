/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType
type ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType string

// List of tools_site_template_site_create_v2_bricks_button_download_event_ios_link_link_type
const (
	QUICK_APP_ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType = "QUICK_APP"
	SCHEME_ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType    ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType = "SCHEME"
	URL_ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType       ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType = "URL"
)

// Ptr returns reference to tools_site_template_site_create_v2_bricks_button_download_event_ios_link_link_type value
func (v ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType) Ptr() *ToolsSiteTemplateSiteCreateV2BricksButtonDownloadEventIosLinkLinkType {
	return &v
}
