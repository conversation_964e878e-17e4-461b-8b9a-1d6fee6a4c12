// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: internal/app/ad/controller/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdUserInfoController struct {
	systemController.BaseController
}

var FqAdUserInfo = new(fqAdUserInfoController)

// List 列表
func (c *fqAdUserInfoController) List(ctx context.Context, req *ad.FqAdUserInfoSearchReq) (res *ad.FqAdUserInfoSearchRes, err error) {
	res = new(ad.FqAdUserInfoSearchRes)
	res.FqAdUserInfoSearchRes, err = service.FqAdUserInfo().List(ctx, &req.FqAdUserInfoSearchReq)
	return
}

func (c *fqAdUserInfoController) Pull(ctx context.Context, req *ad.FqAdUserInfoPullReq) (res *ad.FqAdUserInfoPullRes, err error) {
	res = new(ad.FqAdUserInfoPullRes)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	res.Count, err = service.FqAdUserInfo().Pull(innerCtx)
	return
}

// Export 导出excel
func (c *fqAdUserInfoController) Export(ctx context.Context, req *ad.FqAdUserInfoExportReq) (res *ad.FqAdUserInfoExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdUserInfoInfoRes
		//表头
		tableHead = []interface{}{"渠道ID", "用户设备id", "微信openID", "注册时间（染色时间，注册时染色）", "推广链id", "推广链名称", "用户手机厂商", "投放来源。1：字节", "短剧名称", "书本来源（短剧id）", "点击ID（仅巨量快应用一跳投放有该数据）", "oaid", "caid", "adid（仅巨量快应用一跳投放有该数据）", "creativeid（仅巨量快应用一跳投放有该数据）", "creativetype（仅巨量快应用一跳投放有该数据）", "ip", "user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua", "最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号", "优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount", "广告激励总收入,单位分", "广告点击次数", "企微用户企微id", "巨量2.0广告计划组ID", "巨量2.0广告计划ID", "素材id（分别代表图片、标题、视频、试玩、落地页）", "余额，iaa不需要关注", "充值金额，iaa不需要关注", "充值次数，iaa不需要关注"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.FqAdUserInfo().GetExportData(ctx, &req.FqAdUserInfoSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.DistributorId,
				v.EncryptedDeviceId,
				v.OpenId,
				v.RegisterTime,
				v.PromotionId,
				v.PromotionName,
				v.DeviceBrand,
				v.MediaSource,
				v.BookName,
				v.BookSource,
				v.Clickid,
				v.Oaid,
				v.Caid,
				v.Adid,
				v.Creativeid,
				v.Creativetype,
				v.Ip,
				v.UserAgent,
				v.Timestamp,
				v.OptimizerAccount,
				v.EcpmAmount,
				v.EcpmCnt,
				v.ExternalId,
				v.ProjectId,
				v.AdIdV2,
				v.Mid,
				v.BalanceAmount,
				v.RechargeAmount,
				v.RechargeTimes,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("用户注册信息表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取用户注册信息表
func (c *fqAdUserInfoController) Get(ctx context.Context, req *ad.FqAdUserInfoGetReq) (res *ad.FqAdUserInfoGetRes, err error) {
	res = new(ad.FqAdUserInfoGetRes)
	res.FqAdUserInfoInfoRes, err = service.FqAdUserInfo().GetByDistributorId(ctx, req.DistributorId)
	return
}

// Add 添加用户注册信息表
func (c *fqAdUserInfoController) Add(ctx context.Context, req *ad.FqAdUserInfoAddReq) (res *ad.FqAdUserInfoAddRes, err error) {
	err = service.FqAdUserInfo().Add(ctx, req.FqAdUserInfoAddReq)
	return
}

// Edit 修改用户注册信息表
func (c *fqAdUserInfoController) Edit(ctx context.Context, req *ad.FqAdUserInfoEditReq) (res *ad.FqAdUserInfoEditRes, err error) {
	err = service.FqAdUserInfo().Edit(ctx, req.FqAdUserInfoEditReq)
	return
}

// Delete 删除用户注册信息表
func (c *fqAdUserInfoController) Delete(ctx context.Context, req *ad.FqAdUserInfoDeleteReq) (res *ad.FqAdUserInfoDeleteRes, err error) {
	err = service.FqAdUserInfo().Delete(ctx, req.DistributorIds)
	return
}
