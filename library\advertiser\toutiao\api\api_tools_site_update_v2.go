/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// ToolsSiteUpdateV2ApiService 修改橙子建站站点
type ToolsSiteUpdateV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.ToolsSiteUpdateV2Request
}

func (r *ToolsSiteUpdateV2ApiService) SetCfg(cfg *conf.Configuration) *ToolsSiteUpdateV2ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsSiteUpdateV2ApiService) SetRequest(req models.ToolsSiteUpdateV2Request) *ToolsSiteUpdateV2ApiService {
	r.Request = &req
	return r
}

func (r *ToolsSiteUpdateV2ApiService) AccessToken(accessToken string) *ToolsSiteUpdateV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsSiteUpdateV2ApiService) Do() (data *models.ToolsSiteUpdateV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/tools/site/update/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.ToolsSiteUpdateV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsSiteUpdateV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/tools/site/update/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}

// Execute executes the request
//
//	@return ToolsSiteUpdateV2Response
//func (a *ToolsSiteUpdateV2ApiService) postExecute(r *ApiOpenApi2ToolsSiteUpdatePostRequest) (*ToolsSiteUpdateV2Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodPost
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *ToolsSiteUpdateV2Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/2/tools/site/update/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{"application/json"}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	if r.xOrangeCaller != nil {
//		parameterAddToHeaderOrQuery(localVarHeaderParams, "x-orange-caller", r.xOrangeCaller)
//	}
//	// body params
//	localVarPostBody = r.toolsSiteUpdateV2Request
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
