/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AdvertiserPublicInfoV2ApiService 获取同主体下广告主图片素材 AdvertiserPublicInfoV2Api service
type FileImageAdGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2FileImageAdGetGetRequest
}

type ApiOpenApi2FileImageAdGetGetRequest struct {
	AdvertiserId int64
	ImageIds     []string
}

func (r *FileImageAdGetV2ApiService) SetCfg(cfg *conf.Configuration) *FileImageAdGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileImageAdGetV2ApiService) SetRequest(advertiserInfoV2Request ApiOpenApi2FileImageAdGetGetRequest) *FileImageAdGetV2ApiService {
	r.Request = &advertiserInfoV2Request
	return r
}

func (r *FileImageAdGetV2ApiService) AccessToken(accessToken string) *FileImageAdGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *FileImageAdGetV2ApiService) Do() (data *models.FileImageAdGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/image/ad/get/"
	localVarQueryParams := map[string]string{}

	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	if len(r.Request.ImageIds) > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "image_ids", r.Request.ImageIds)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.FileImageAdGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.FileImageAdGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/image/ad/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
