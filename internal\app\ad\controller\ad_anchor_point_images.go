// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-16 15:34:40
// 生成路径: internal/app/ad/controller/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAnchorPointImagesController struct {
	systemController.BaseController
}

var AdAnchorPointImages = new(adAnchorPointImagesController)

// List 列表
func (c *adAnchorPointImagesController) List(ctx context.Context, req *ad.AdAnchorPointImagesSearchReq) (res *ad.AdAnchorPointImagesSearchRes, err error) {
	res = new(ad.AdAnchorPointImagesSearchRes)
	res.AdAnchorPointImagesSearchRes, err = service.AdAnchorPointImages().List(ctx, &req.AdAnchorPointImagesSearchReq)
	return
}

// Get 获取锚点图片表
func (c *adAnchorPointImagesController) Get(ctx context.Context, req *ad.AdAnchorPointImagesGetReq) (res *ad.AdAnchorPointImagesGetRes, err error) {
	res = new(ad.AdAnchorPointImagesGetRes)
	res.AdAnchorPointImagesInfoRes, err = service.AdAnchorPointImages().GetById(ctx, req.Id)
	return
}

// Add 添加锚点图片表
func (c *adAnchorPointImagesController) Add(ctx context.Context, req *ad.AdAnchorPointImagesAddReq) (res *ad.AdAnchorPointImagesAddRes, err error) {
	err = service.AdAnchorPointImages().Add(ctx, req.AdAnchorPointImagesAddReq)
	return
}

// Edit 修改锚点图片表
func (c *adAnchorPointImagesController) Edit(ctx context.Context, req *ad.AdAnchorPointImagesEditReq) (res *ad.AdAnchorPointImagesEditRes, err error) {
	err = service.AdAnchorPointImages().Edit(ctx, req.AdAnchorPointImagesEditReq)
	return
}

// Delete 删除锚点图片表
func (c *adAnchorPointImagesController) Delete(ctx context.Context, req *ad.AdAnchorPointImagesDeleteReq) (res *ad.AdAnchorPointImagesDeleteRes, err error) {
	err = service.AdAnchorPointImages().Delete(ctx, req.Ids)
	return
}
