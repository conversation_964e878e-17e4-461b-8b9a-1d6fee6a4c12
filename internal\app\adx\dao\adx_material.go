// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/dao/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao/internal"
)

// adxMaterialDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adxMaterialDao struct {
	*internal.AdxMaterialDao
}

var (
	// AdxMaterial is globally public accessible object for table tools_gen_table operations.
	AdxMaterial = adxMaterialDao{
		internal.NewAdxMaterialDao(),
	}
	AdxMaterialAnalytic = adxMaterialDao{
		internal.NewAdxMaterialAnalyticDao(),
	}
)

// Fill with you ideas below.
