// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: internal/app/ad/logic/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdMaterialFile(New())
}

func New() service.IAdMaterialFile {
	return &sAdMaterialFile{}
}

type sAdMaterialFile struct{}

func (s *sAdMaterialFile) List(ctx context.Context, req *model.AdMaterialFileSearchReq) (listRes *model.AdMaterialFileSearchRes, err error) {
	listRes = new(model.AdMaterialFileSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetMaterialContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdMaterialFile.Ctx(ctx).WithAll()
		if req.FileId > 0 {
			m = m.Where(dao.AdMaterialFile.Columns().FileId+" = ?", req.FileId)
		}
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdMaterialFile.Columns().UserId, userIds)
		}
		if req.FileName != "" {
			m = m.Where(dao.AdMaterialFile.Columns().FileName+" like ?", "%"+req.FileName+"%")
		}
		if req.UserId != "" {
			m = m.Where(dao.AdMaterialFile.Columns().UserId+" = ?", gconv.Int(req.UserId))
		}
		if req.AlbumId > 0 {
			m = m.Where(dao.AdMaterialFile.Columns().AlbumId+" = ?", req.AlbumId)
		}
		if req.ParentId > 0 {
			m = m.Where(dao.AdMaterialFile.Columns().ParentId+" = ?", gconv.Int(req.ParentId))
		}
		if req.AllPath != "" {
			m = m.Where(dao.AdMaterialFile.Columns().AllPath+" = ?", req.AllPath)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdMaterialFile.Columns().CreatedAt+" >=? AND "+dao.AdMaterialFile.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "created_at desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.AdMaterialFileListRes

		if req.SkipNum > 0 {
			err = m.Limit(req.SkipNum, req.PageSize).Order(order).Scan(&res)
		} else {
			err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		}
		//err = m.Limit((req.PageNum-1)*req.PageSize+req.SkipNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdMaterialFileListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdMaterialFileListRes{
				FileId:      v.FileId,
				FileName:    v.FileName,
				UserId:      v.UserId,
				AlbumId:     v.AlbumId,
				ParentId:    v.ParentId,
				Preview:     v.Preview,
				MaterialNum: v.MaterialNum,
				Remark:      v.Remark,
				AllPath:     v.AllPath,
				CreatedAt:   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdMaterialFile) GetAllPath(ctx context.Context, filedId int) (res *model.GetAllPath, err error) {
	pid := filedId
	res = new(model.GetAllPath)
	for {
		file, _ := s.GetByFileId(ctx, pid)
		if file != nil && file.FileId > 0 {
			res.AllPath = "/" + file.FileName + res.AllPath
			pid = file.ParentId
		} else {
			break
		}
		if file.ParentId <= 0 {
			album, innerErr := service.AdMaterialAlbum().GetByAlbumId(ctx, file.AlbumId)
			if innerErr != nil || album == nil {
				err = innerErr
				return
			}
			res.AlbumId = album.AlbumId
			res.AllPath = res.AllPath + "/" + album.AlbumName
			break
		}
	}
	return
}

func (s *sAdMaterialFile) ChildList(ctx context.Context, req *model.AdMaterialFileChildListReq) (listRes *model.AdMaterialFileChildListRes, err error) {
	listRes = new(model.AdMaterialFileChildListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		var childList = make([]*model.AdMaterialFileChildList, 0)
		var ids = make([]int64, 0)
		m := dao.AdMaterialFile.Ctx(ctx).WithAll()
		if req.FileId > 0 {
			m = m.Where(dao.AdMaterialFile.Columns().ParentId+" = ?", req.FileId).Fields("2", "thisType")
			err = m.Fields(dao.AdMaterialFile.Columns().FileId).
				Fields(dao.AdMaterialFile.Columns().FileName + " as titleName").Scan(&listRes.List)

			for _, item := range listRes.List {
				ids = append(ids, item.FileId)
			}
			_ = dao.AdMaterialFile.Ctx(ctx).As("f1").
				Fields(dao.AdMaterialFile.Columns().FileId).
				Fields(dao.AdMaterialFile.Columns().ParentId).
				Fields(dao.AdMaterialFile.Columns().FileName+" as  titleName").
				Fields(" IF(EXISTS (SELECT 1 FROM ad_material_file f2 WHERE f2.parent_id = f1.file_id), 1, 0) AS haveChild").
				WhereIn(dao.AdMaterialFile.Columns().ParentId, ids).Scan(&childList)
			for i, item := range listRes.List {
				//item.ThisType = 1
				for _, file := range childList {
					//file.ThisType = 2
					if file.ParentId == item.FileId {
						listRes.List[i].Children = append(listRes.List[i].Children, file)
					}
				}
			}
		}
		if req.AlbumId > 0 {
			// 查询这一层的文件夹
			err = m.As("f1").Where(dao.AdMaterialAlbum.Columns().AlbumId+" = ?", req.AlbumId).
				Fields("2", "thisType").
				Fields(dao.AdMaterialFile.Columns().FileId).
				Fields(" IF(EXISTS (SELECT 1 FROM ad_material_file f2 WHERE f2.parent_id = f1.file_id), 1, 0) AS haveChild").
				Fields(dao.AdMaterialFile.Columns().FileName + " as titleName").Scan(&listRes.List)
			for _, item := range listRes.List {
				ids = append(ids, item.FileId)
			}
			_ = dao.AdMaterialFile.Ctx(ctx).As("f1").
				Fields(dao.AdMaterialFile.Columns().FileId).
				Fields(dao.AdMaterialFile.Columns().ParentId).
				Fields(dao.AdMaterialFile.Columns().FileName+" as  titleName").
				Fields(" IF(EXISTS (SELECT 1 FROM ad_material_file f2 WHERE f2.parent_id = f1.file_id), 1, 0) AS haveChild").
				WhereIn(dao.AdMaterialFile.Columns().ParentId, ids).Scan(&childList)
			for i, item := range listRes.List {
				item.ThisType = 1
				for _, file := range childList {
					file.ThisType = 2
					if file.ParentId == item.FileId {
						listRes.List[i].Children = append(listRes.List[i].Children, file)
					}
				}
			}
			return
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdMaterialFile) GetByFileId(ctx context.Context, fileId int) (res *model.AdMaterialFileInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialFile.Ctx(ctx).WithAll().Where(dao.AdMaterialFile.Columns().FileId, fileId).Scan(&res)
		// 根据专辑id 获取专辑名称
		if res != nil && res.AlbumId > 0 {
			album, innerErr := service.AdMaterialAlbum().GetByAlbumId(ctx, res.AlbumId)
			if innerErr != nil || album == nil {
				err = innerErr
				return
			}
			res.AlbumName = album.AlbumName
		}
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialFile) GetByFileIds(ctx context.Context, fileIds []int) (res []*model.AdMaterialFileInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialFile.Ctx(ctx).WithAll().WhereIn(dao.AdMaterialFile.Columns().FileId, fileIds).Scan(&res)
		// 根据专辑id 获取专辑名称
		aids := make([]int, 0)
		for _, item := range res {
			if item.AlbumId > 0 {
				aids = append(aids, item.AlbumId)
			}
		}
		if len(aids) > 0 {
			albums, innerErr := service.AdMaterialAlbum().GetByAlbumIds(ctx, aids)
			if innerErr != nil {
				err = innerErr
				return
			}
			for _, item := range res {
				for _, album := range albums {
					if item.AlbumId == album.AlbumId {
						item.AlbumName = album.AlbumName
						break
					}
				}
			}
		}
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetMaterialNumByAId 获取素材数量根据id
func (s *sAdMaterialFile) GetMaterialNumByAId(ctx context.Context, albumIds []int) (res []*model.MaterialNumByAlbumId, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialFile.Ctx(ctx).WithAll().Fields(dao.AdMaterialFile.Columns().AlbumId, "album_id").FieldSum(dao.AdMaterialFile.Columns().MaterialNum, "material_num").WhereIn(dao.AdMaterialFile.Columns().AlbumId, albumIds).Group(dao.AdMaterialFile.Columns().AlbumId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialFile) GetPreviewByAId(ctx context.Context, albumIds []int) (res []*model.MaterialNumByAlbumId, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(albumIds) > 0 {
			err = dao.AdMaterialFile.Ctx(ctx).WithAll().Raw(`SELECT
		    album_id,
		    preview  
		FROM
		    ad_material_file as a
		WHERE
		    album_id in (?)
		    AND deleted_at IS NULL
		    AND created_at = (
		        SELECT MAX(created_at)
		        FROM ad_material_file
		        WHERE album_id = a.album_id
		        AND deleted_at IS NULL
		    )`, albumIds).Scan(&res)
		}
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})

	return
}

func (s *sAdMaterialFile) Add(ctx context.Context, req *model.AdMaterialFileAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialFile.Ctx(ctx).Insert(do.AdMaterialFile{
			FileName:    req.FileName,
			UserId:      req.UserId,
			AlbumId:     req.AlbumId,
			ParentId:    req.ParentId,
			Remark:      req.Remark,
			AllPath:     req.AllPath,
			Preview:     req.Preview,
			MaterialNum: req.MaterialNum,
			CreatedAt:   gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdMaterialFile) Edit(ctx context.Context, req *model.AdMaterialFileEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialFile.Ctx(ctx).WherePri(req.FileId).Update(do.AdMaterialFile{
			FileId:      req.FileId,
			FileName:    req.FileName,
			UserId:      req.UserId,
			AlbumId:     req.AlbumId,
			ParentId:    req.ParentId,
			Remark:      req.Remark,
			AllPath:     req.AllPath,
			Preview:     req.Preview,
			MaterialNum: req.MaterialNum,
			UpdatedAt:   gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdMaterialFile) Delete(ctx context.Context, fileIds []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialFile.Ctx(ctx).Delete(dao.AdMaterialFile.Columns().FileId+" in (?)", fileIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
