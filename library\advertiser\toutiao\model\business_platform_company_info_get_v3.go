/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

type BusinessPlatformCompanyInfoGetV3Request struct {
	OrganizationId *int64
	Page           *int32
	PageSize       *int32
}

// BusinessPlatformCompanyInfoGetV3Response struct for BusinessPlatformCompanyInfoGetV3Response
type BusinessPlatformCompanyInfoGetV3Response struct {
	//
	Code *int64                                        `json:"code,omitempty"`
	Data *BusinessPlatformCompanyInfoGetV3ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// BusinessPlatformCompanyInfoGetV30ResponseData
type BusinessPlatformCompanyInfoGetV3ResponseData struct {
	//
	CompanyInfo []*BusinessPlatformCompanyInfoGetV3ResponseDataCompanyInfoInner `json:"company_info,omitempty"`
	PageInfo    *BusinessPlatformCompanyInfoGetV3ResponseDataPageInfo           `json:"page_info,omitempty"`
}

// BusinessPlatformCompanyInfoGetV3ResponseDataCompanyInfoInner struct for BusinessPlatformCompanyInfoGetV3ResponseDataCompanyInfoInner
type BusinessPlatformCompanyInfoGetV3ResponseDataCompanyInfoInner struct {
	//
	CompanyId *int64 `json:"company_id,omitempty"`
	//
	CompanyName *string                                                `json:"company_name,omitempty"`
	Status      *BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus `json:"status,omitempty"`
	Type        *BusinessPlatformCompanyInfoGetV3DataCompanyInfoType   `json:"type,omitempty"`
}

// BusinessPlatformCompanyInfoGetV0ResponseDataPageInfo
type BusinessPlatformCompanyInfoGetV3ResponseDataPageInfo struct {
	//
	Page *int32 `json:"page,omitempty"`
	//
	PageSize *int32 `json:"page_size,omitempty"`
	//
	TotalNumber *int32 `json:"total_number,omitempty"`
	//
	TotalPage *int32 `json:"total_page,omitempty"`
}

// BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus
type BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus string

// List of business_platform_company_info_get_v3.0_data_company_info_status
const (
	EXPIRED_BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus     BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus = "EXPIRED"
	FAILED_BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus      BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus = "FAILED"
	NOT_STARTED_BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus = "NOT_STARTED"
	PROCESSING_BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus  BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus = "PROCESSING"
	SUCCESS_BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus     BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus = "SUCCESS"
	WAITING_BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus     BusinessPlatformCompanyInfoGetV3DataCompanyInfoStatus = "WAITING"
)

// BusinessPlatformCompanyInfoGetV3DataCompanyInfoType
type BusinessPlatformCompanyInfoGetV3DataCompanyInfoType string

// List of business_platform_company_info_get_v3.0_data_company_info_type
const (
	BP_OTHER_BusinessPlatformCompanyInfoGetV3DataCompanyInfoType BusinessPlatformCompanyInfoGetV3DataCompanyInfoType = "BP_OTHER"
	BP_OWN_BusinessPlatformCompanyInfoGetV3DataCompanyInfoType   BusinessPlatformCompanyInfoGetV3DataCompanyInfoType = "BP_OWN"
)
