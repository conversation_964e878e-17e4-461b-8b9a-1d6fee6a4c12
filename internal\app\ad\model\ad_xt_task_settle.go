// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: internal/app/ad/model/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdXtTaskSettleInfoRes is the golang structure for table ad_xt_task_settle.
type AdXtTaskSettleInfoRes struct {
	gmeta.Meta           `orm:"table:ad_xt_task_settle"`
	Id                   int64   `orm:"id,primary" json:"id" dc:"ID"`                                      // ID
	AdvertiserId         string  `orm:"advertiser_id" json:"advertiserId" dc:"账户ID 对应 star_id "`           // 账户ID 对应 star_id
	TaskId               string  `orm:"task_id" json:"taskId" dc:"任务ID"`                                   // 任务ID
	ItemId               int64   `orm:"item_id" json:"itemId" dc:"视频唯一ID"`                                 // 视频唯一ID
	AuthorId             int64   `orm:"author_id" json:"authorId" dc:"作者ID"`                               // 作者ID
	Uid                  int64   `orm:"uid" json:"uid" dc:"用户平台ID"`                                        // 用户平台ID
	ProviderId           int64   `orm:"provider_id" json:"providerId" dc:"内容提供方ID"`                        // 内容提供方ID
	Title                string  `orm:"title" json:"title" dc:"视频标题"`                                      // 视频标题
	Link                 string  `orm:"link" json:"link" dc:"视频分享链接"`                                      // 视频分享链接
	AuthorNickname       string  `orm:"author_nickname" json:"authorNickname" dc:"作者昵称"`                   // 达人名称
	ReleaseTime          int     `orm:"release_time" json:"releaseTime" dc:"发布时间(unix时间戳)"`                // 发布时间(unix时间戳)
	AndroidActivateCnt   int     `orm:"android_activate_cnt" json:"androidActivateCnt" dc:"安卓端激活数"`        // 安卓端激活数
	IosActivateCnt       int     `orm:"ios_activate_cnt" json:"iosActivateCnt" dc:"iOS端激活数"`               // iOS端激活数
	CommentCnt           int     `orm:"comment_cnt" json:"commentCnt" dc:"评论数"`                            // 评论数
	LikeCnt              int     `orm:"like_cnt" json:"likeCnt" dc:"点赞数"`                                  // 点赞数
	ValidLikeCnt         int     `orm:"valid_like_cnt" json:"validLikeCnt" dc:"有效点赞数"`                     // 有效点赞数
	PlayVv               int     `orm:"play_vv" json:"playVv" dc:"播放次数"`                                   // 播放次数
	ValidPlayVv          int     `orm:"valid_play_vv" json:"validPlayVv" dc:"有效播放次数"`                      // 有效播放次数
	ShareCnt             int     `orm:"share_cnt" json:"shareCnt" dc:"分享次数"`                               // 分享次数
	PromoteCnt           int     `orm:"promote_cnt" json:"promoteCnt" dc:"推广次数"`                           // 推广次数
	ComponentClickCnt    int     `orm:"component_click_cnt" json:"componentClickCnt" dc:"组件点击次数"`          // 组件点击次数
	EstAdCost            string  `orm:"est_ad_cost" json:"estAdCost" dc:"预估广告收入(JSON格式)"`                  // 预估广告收入(JSON格式)
	EstSales             string  `orm:"est_sales" json:"estSales" dc:"预估销售额(JSON格式)"`                      // 预估销售额(JSON格式)
	Play                 string  `orm:"play" json:"play" dc:"播放数据(JSON格式)"`                                // 播放数据(JSON格式)
	SettleAdShare        string  `orm:"settle_ad_share" json:"settleAdShare" dc:"广告分成结算(JSON格式)"`          // 广告分成结算(JSON格式)
	SettleCps            string  `orm:"settle_cps" json:"settleCps" dc:"CPS结算(JSON格式)"`                    // CPS结算(JSON格式)
	IaaCostHour          float64 `orm:"iaa_cost_hour" json:"iaaCostHour" dc:"小时级IAA消耗"`                    // 小时级IAA消耗
	IapCostHour          float64 `orm:"iap_cost_hour" json:"iapCostHour" dc:"小时级IAP消耗"`                    // 小时级IAP消耗
	RewardAmount         float64 `orm:"reward_amount" json:"rewardAmount" dc:"打赏金额"`                       // 打赏金额
	RelevanceAuditResult int     `orm:"relevance_audit_result" json:"relevanceAuditResult" dc:"相关性审核结果"`   // 相关性审核结果
	RewardLevel          int     `orm:"reward_level" json:"rewardLevel" dc:"打赏等级"`                         // 打赏等级
	ItemInfoDailyList    string  `orm:"item_info_daily_list" json:"itemInfoDailyList" dc:"每日数据明细(JSON数组)"` // 每日数据明细(JSON数组)
}

type AdXtTaskSettleListRes struct {
	Id                   int64   `json:"id" dc:"ID"`
	AdvertiserId         string  `json:"advertiserId" dc:"账户ID 对应 star_id "`
	AdvertiserNick       string  `json:"advertiserNick" dc:"账户名称"` // 账户名称
	TaskId               string  `json:"taskId" dc:"任务ID"`
	TaskName             string  `json:"taskName" dc:"任务名称"`
	ItemId               string  `json:"itemId" dc:"视频唯一ID"`
	AuthorId             string  `json:"authorId" dc:"作者ID"`
	Uid                  int64   `json:"uid" dc:"用户平台ID"`
	ProviderId           int64   `json:"providerId" dc:"内容提供方ID"`
	Title                string  `json:"title" dc:"视频标题"`
	Link                 string  `json:"link" dc:"视频分享链接"`
	AuthorNickname       string  `json:"authorNickname" dc:"作者昵称"`
	ReleaseTime          int     `json:"releaseTime" dc:"发布时间(unix时间戳)"`
	AndroidActivateCnt   int     `json:"androidActivateCnt" dc:"安卓端激活数"`
	IosActivateCnt       int     `json:"iosActivateCnt" dc:"iOS端激活数"`
	CommentCnt           int     `json:"commentCnt" dc:"评论数"`
	LikeCnt              int     `json:"likeCnt" dc:"点赞数"`
	ValidLikeCnt         int     `json:"validLikeCnt" dc:"有效点赞数"`
	PlayVv               int     `json:"playVv" dc:"播放次数"`
	ValidPlayVv          int     `json:"validPlayVv" dc:"有效播放次数"`
	ShareCnt             int     `json:"shareCnt" dc:"分享次数"`
	PromoteCnt           int     `json:"promoteCnt" dc:"推广次数"`
	ComponentClickCnt    int     `json:"componentClickCnt" dc:"组件点击次数"`
	EstAdCost            string  `json:"estAdCost" dc:"预估广告收入(JSON格式)"`
	EstSales             string  `json:"estSales" dc:"预估销售额(JSON格式)"`
	Play                 string  `json:"play" dc:"播放数据(JSON格式)"`
	SettleAdShare        string  `json:"settleAdShare" dc:"广告分成结算(JSON格式)"`
	SettleCps            string  `json:"settleCps" dc:"CPS结算(JSON格式)"`
	IaaCostHour          float64 `json:"iaaCostHour" dc:"小时级IAA消耗"`
	IapCostHour          float64 `json:"iapCostHour" dc:"小时级IAP消耗"`
	RewardAmount         float64 `json:"rewardAmount" dc:"打赏金额"`
	RelevanceAuditResult int     `json:"relevanceAuditResult" dc:"相关性审核结果"`
	RewardLevel          int     `json:"rewardLevel" dc:"打赏等级"`
	ItemInfoDailyList    string  `json:"itemInfoDailyList" dc:"每日数据明细(JSON数组)"`
}

// AdXtTaskSettleSearchReq 分页请求参数
type AdXtTaskSettleSearchReq struct {
	comModel.PageReq
	Id                   string   `p:"id" dc:"ID"`                                                                     //ID
	AdvertiserId         string   `p:"advertiserId" dc:"账户ID 对应 star_id "`                                             //账户ID 对应 star_id
	TaskId               string   `p:"taskId" dc:"任务ID"`                                                               //任务ID
	TaskIds              []string `p:"taskIds" dc:"任务ID"`                                                              //任务IDs
	ItemId               string   `p:"itemId" v:"itemId@integer#视频唯一ID需为整数" dc:"视频唯一ID"`                               //视频唯一ID
	AuthorId             string   `p:"authorId" v:"authorId@integer#作者ID需为整数" dc:"作者ID"`                               //作者ID
	Uid                  string   `p:"uid" v:"uid@integer#用户平台ID需为整数" dc:"用户平台ID"`                                     //用户平台ID
	ProviderId           string   `p:"providerId" v:"providerId@integer#内容提供方ID需为整数" dc:"内容提供方ID"`                     //内容提供方ID
	Title                string   `p:"title" dc:"视频标题"`                                                                //视频标题
	Link                 string   `p:"link" dc:"视频分享链接"`                                                               //视频分享链接
	AuthorNickname       string   `p:"authorNickname" dc:"作者昵称"`                                                       //作者昵称
	ReleaseTime          string   `p:"releaseTime" v:"releaseTime@integer#发布时间(unix时间戳)需为整数" dc:"发布时间(unix时间戳)"`       //发布时间(unix时间戳)
	AndroidActivateCnt   string   `p:"androidActivateCnt" v:"androidActivateCnt@integer#安卓端激活数需为整数" dc:"安卓端激活数"`       //安卓端激活数
	IosActivateCnt       string   `p:"iosActivateCnt" v:"iosActivateCnt@integer#iOS端激活数需为整数" dc:"iOS端激活数"`             //iOS端激活数
	CommentCnt           string   `p:"commentCnt" v:"commentCnt@integer#评论数需为整数" dc:"评论数"`                             //评论数
	LikeCnt              string   `p:"likeCnt" v:"likeCnt@integer#点赞数需为整数" dc:"点赞数"`                                   //点赞数
	ValidLikeCnt         string   `p:"validLikeCnt" v:"validLikeCnt@integer#有效点赞数需为整数" dc:"有效点赞数"`                     //有效点赞数
	PlayVv               string   `p:"playVv" v:"playVv@integer#播放次数需为整数" dc:"播放次数"`                                   //播放次数
	ValidPlayVv          string   `p:"validPlayVv" v:"validPlayVv@integer#有效播放次数需为整数" dc:"有效播放次数"`                     //有效播放次数
	ShareCnt             string   `p:"shareCnt" v:"shareCnt@integer#分享次数需为整数" dc:"分享次数"`                               //分享次数
	PromoteCnt           string   `p:"promoteCnt" v:"promoteCnt@integer#推广次数需为整数" dc:"推广次数"`                           //推广次数
	ComponentClickCnt    string   `p:"componentClickCnt" v:"componentClickCnt@integer#组件点击次数需为整数" dc:"组件点击次数"`         //组件点击次数
	EstAdCost            string   `p:"estAdCost" dc:"预估广告收入(JSON格式)"`                                                  //预估广告收入(JSON格式)
	EstSales             string   `p:"estSales" dc:"预估销售额(JSON格式)"`                                                    //预估销售额(JSON格式)
	Play                 string   `p:"play" dc:"播放数据(JSON格式)"`                                                         //播放数据(JSON格式)
	SettleAdShare        string   `p:"settleAdShare" dc:"广告分成结算(JSON格式)"`                                              //广告分成结算(JSON格式)
	SettleCps            string   `p:"settleCps" dc:"CPS结算(JSON格式)"`                                                   //CPS结算(JSON格式)
	IaaCostHour          string   `p:"iaaCostHour" v:"iaaCostHour@float#小时级IAA消耗需为浮点数" dc:"小时级IAA消耗"`                  //小时级IAA消耗
	IapCostHour          string   `p:"iapCostHour" v:"iapCostHour@float#小时级IAP消耗需为浮点数" dc:"小时级IAP消耗"`                  //小时级IAP消耗
	RewardAmount         string   `p:"rewardAmount" v:"rewardAmount@float#打赏金额需为浮点数" dc:"打赏金额"`                        //打赏金额
	RelevanceAuditResult string   `p:"relevanceAuditResult" v:"relevanceAuditResult@integer#相关性审核结果需为整数" dc:"相关性审核结果"` //相关性审核结果
	RewardLevel          string   `p:"rewardLevel" v:"rewardLevel@integer#打赏等级需为整数" dc:"打赏等级"`                         //打赏等级
	ItemInfoDailyList    string   `p:"itemInfoDailyList" dc:"每日数据明细(JSON数组)"`                                          //每日数据明细(JSON数组)
}

// AdXtTaskSettleSearchRes 列表返回结果
type AdXtTaskSettleSearchRes struct {
	comModel.ListRes
	List    []*AdXtTaskSettleListRes `json:"list"`
	Summary *AdXtTaskSettleSummary   `json:"summary"`
}

type AdXtTaskSettleSummary struct {
	EstAdCost     *string `json:"estAdCost"  dc:"预估广告消耗金额"`
	SettleAdShare *string `json:"settleAdShare"  dc:"广告分成结算"`
	EstSales      *string `json:"estSales"  dc:"付费流水"`
	SettleCps     *string `json:"settleCps"  dc:"达人付费分佣"`
	IaaCostHour   float64 `json:"iaaCostHour"  dc:"累计广告收益"`
	IapCostHour   float64 `json:"iapCostHour"  dc:"累计订单金额"`
}

// AdXtTaskSettleAddReq 添加操作请求参数
type AdXtTaskSettleAddReq struct {
	AdvertiserId         string  `p:"advertiserId" v:"required#账户ID 对应 star_id 不能为空" dc:"账户ID 对应 star_id "`
	TaskId               string  `p:"taskId" v:"required#任务ID不能为空" dc:"任务ID"`
	ItemId               int64   `p:"itemId"  dc:"视频唯一ID"`
	AuthorId             int64   `p:"authorId"  dc:"作者ID"`
	Uid                  int64   `p:"uid"  dc:"用户平台ID"`
	ProviderId           int64   `p:"providerId"  dc:"内容提供方ID"`
	Title                string  `p:"title"  dc:"视频标题"`
	Link                 string  `p:"link"  dc:"视频分享链接"`
	AuthorNickname       string  `p:"authorNickname" v:"required#作者昵称不能为空" dc:"作者昵称"`
	ReleaseTime          int64   `p:"releaseTime"  dc:"发布时间(unix时间戳)"`
	AndroidActivateCnt   int64   `p:"androidActivateCnt"  dc:"安卓端激活数"`
	IosActivateCnt       int64   `p:"iosActivateCnt"  dc:"iOS端激活数"`
	CommentCnt           int64   `p:"commentCnt"  dc:"评论数"`
	LikeCnt              int64   `p:"likeCnt"  dc:"点赞数"`
	ValidLikeCnt         int64   `p:"validLikeCnt"  dc:"有效点赞数"`
	PlayVv               int64   `p:"playVv"  dc:"播放次数"`
	ValidPlayVv          int64   `p:"validPlayVv"  dc:"有效播放次数"`
	ShareCnt             int64   `p:"shareCnt"  dc:"分享次数"`
	PromoteCnt           int64   `p:"promoteCnt"  dc:"推广次数"`
	ComponentClickCnt    int64   `p:"componentClickCnt"  dc:"组件点击次数"`
	EstAdCost            *string `p:"estAdCost"  dc:"预估广告收入(JSON格式)"`
	EstSales             *string `p:"estSales"  dc:"预估销售额(JSON格式)"`
	Play                 *string `p:"play"  dc:"播放数据(JSON格式)"`
	SettleAdShare        *string `p:"settleAdShare"  dc:"广告分成结算(JSON格式)"`
	SettleCps            *string `p:"settleCps"  dc:"CPS结算(JSON格式)"`
	IaaCostHour          float64 `p:"iaaCostHour"  dc:"小时级IAA消耗"`
	IapCostHour          float64 `p:"iapCostHour"  dc:"小时级IAP消耗"`
	RewardAmount         float64 `p:"rewardAmount"  dc:"打赏金额"`
	RelevanceAuditResult int64   `p:"relevanceAuditResult"  dc:"相关性审核结果"`
	RewardLevel          int64   `p:"rewardLevel"  dc:"打赏等级"`
	ItemInfoDailyList    *string `p:"itemInfoDailyList"  dc:"每日数据明细(JSON数组)"`
}

// AdXtTaskSettleEditReq 修改操作请求参数
type AdXtTaskSettleEditReq struct {
	Id                   int64   `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AdvertiserId         string  `p:"advertiserId" v:"required#账户ID 对应 star_id 不能为空" dc:"账户ID 对应 star_id "`
	TaskId               string  `p:"taskId" v:"required#任务ID不能为空" dc:"任务ID"`
	ItemId               int64   `p:"itemId"  dc:"视频唯一ID"`
	AuthorId             int64   `p:"authorId"  dc:"作者ID"`
	Uid                  int64   `p:"uid"  dc:"用户平台ID"`
	ProviderId           int64   `p:"providerId"  dc:"内容提供方ID"`
	Title                string  `p:"title"  dc:"视频标题"`
	Link                 string  `p:"link"  dc:"视频分享链接"`
	AuthorNickname       string  `p:"authorNickname" v:"required#作者昵称不能为空" dc:"作者昵称"`
	ReleaseTime          int     `p:"releaseTime"  dc:"发布时间(unix时间戳)"`
	AndroidActivateCnt   int     `p:"androidActivateCnt"  dc:"安卓端激活数"`
	IosActivateCnt       int     `p:"iosActivateCnt"  dc:"iOS端激活数"`
	CommentCnt           int     `p:"commentCnt"  dc:"评论数"`
	LikeCnt              int     `p:"likeCnt"  dc:"点赞数"`
	ValidLikeCnt         int     `p:"validLikeCnt"  dc:"有效点赞数"`
	PlayVv               int     `p:"playVv"  dc:"播放次数"`
	ValidPlayVv          int     `p:"validPlayVv"  dc:"有效播放次数"`
	ShareCnt             int     `p:"shareCnt"  dc:"分享次数"`
	PromoteCnt           int     `p:"promoteCnt"  dc:"推广次数"`
	ComponentClickCnt    int     `p:"componentClickCnt"  dc:"组件点击次数"`
	EstAdCost            string  `p:"estAdCost"  dc:"预估广告收入(JSON格式)"`
	EstSales             string  `p:"estSales"  dc:"预估销售额(JSON格式)"`
	Play                 string  `p:"play"  dc:"播放数据(JSON格式)"`
	SettleAdShare        string  `p:"settleAdShare"  dc:"广告分成结算(JSON格式)"`
	SettleCps            string  `p:"settleCps"  dc:"CPS结算(JSON格式)"`
	IaaCostHour          float64 `p:"iaaCostHour"  dc:"小时级IAA消耗"`
	IapCostHour          float64 `p:"iapCostHour"  dc:"小时级IAP消耗"`
	RewardAmount         float64 `p:"rewardAmount"  dc:"打赏金额"`
	RelevanceAuditResult int     `p:"relevanceAuditResult"  dc:"相关性审核结果"`
	RewardLevel          int     `p:"rewardLevel"  dc:"打赏等级"`
	ItemInfoDailyList    string  `p:"itemInfoDailyList"  dc:"每日数据明细(JSON数组)"`
}
