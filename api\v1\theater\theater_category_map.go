// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-10 13:50:02
// 生成路径: api/v1/theater/theater_category_map.go
// 生成人：cq
// desc:短剧分类绑定表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterCategoryMapSearchReq 分页请求参数
type TheaterCategoryMapSearchReq struct {
	g.Meta `path:"/list" tags:"短剧分类绑定表" method:"get" summary:"短剧分类绑定表列表"`
	commonApi.Author
	model.TheaterCategoryMapSearchReq
}

// TheaterCategoryMapSearchRes 列表返回结果
type TheaterCategoryMapSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterCategoryMapSearchRes
}

// TheaterCategoryMapAddReq 添加操作请求参数
type TheaterCategoryMapAddReq struct {
	g.Meta `path:"/add" tags:"短剧分类绑定表" method:"post" summary:"短剧分类绑定表添加"`
	commonApi.Author
	*model.TheaterCategoryMapAddReq
}

// TheaterCategoryMapAddRes 添加操作返回结果
type TheaterCategoryMapAddRes struct {
	commonApi.EmptyRes
}

// TheaterCategoryMapEditReq 修改操作请求参数
type TheaterCategoryMapEditReq struct {
	g.Meta `path:"/edit" tags:"短剧分类绑定表" method:"put" summary:"短剧分类绑定表修改"`
	commonApi.Author
	*model.TheaterCategoryMapEditReq
}

// TheaterCategoryMapEditRes 修改操作返回结果
type TheaterCategoryMapEditRes struct {
	commonApi.EmptyRes
}

// TheaterCategoryMapGetReq 获取一条数据请求
type TheaterCategoryMapGetReq struct {
	g.Meta `path:"/get" tags:"短剧分类绑定表" method:"get" summary:"获取短剧分类绑定表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// TheaterCategoryMapGetRes 获取一条数据结果
type TheaterCategoryMapGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterCategoryMapInfoRes
}

// TheaterCategoryMapDeleteReq 删除数据请求
type TheaterCategoryMapDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧分类绑定表" method:"delete" summary:"删除短剧分类绑定表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// TheaterCategoryMapDeleteRes 删除数据返回
type TheaterCategoryMapDeleteRes struct {
	commonApi.EmptyRes
}
