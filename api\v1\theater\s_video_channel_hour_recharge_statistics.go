// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-19 14:46:51
// 生成路径: api/v1/theater/s_video_channel_hour_recharge_statistics.go
// 生成人：lx
// desc:剧+渠道+广告渠道  小时维度统计充值数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SVideoChannelHourRechargeStatisticsSearchReq 分页请求参数
type SVideoChannelHourRechargeStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"get" summary:"剧+渠道+广告渠道  小时维度统计充值数据列表"`
	commonApi.Author
	model.SVideoChannelHourRechargeStatisticsSearchReq
}

// SVideoChannelHourRechargeStatisticsSearchRes 列表返回结果
type SVideoChannelHourRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SVideoChannelHourRechargeStatisticsSearchRes
}

// SVideoChannelHourRechargeStatisticsExportReq 导出请求
type SVideoChannelHourRechargeStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"get" summary:"剧+渠道+广告渠道  小时维度统计充值数据导出"`
	commonApi.Author
	model.SVideoChannelHourRechargeStatisticsSearchReq
}

// SVideoChannelHourRechargeStatisticsExportRes 导出响应
type SVideoChannelHourRechargeStatisticsExportRes struct {
	commonApi.EmptyRes
}

// SVideoChannelHourRechargeStatisticsAddReq 添加操作请求参数
type SVideoChannelHourRechargeStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"post" summary:"剧+渠道+广告渠道  小时维度统计充值数据添加"`
	commonApi.Author
	*model.SVideoChannelHourRechargeStatisticsAddReq
}

// SVideoChannelHourRechargeStatisticsAddRes 添加操作返回结果
type SVideoChannelHourRechargeStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SVideoChannelHourRechargeStatisticsEditReq 修改操作请求参数
type SVideoChannelHourRechargeStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"put" summary:"剧+渠道+广告渠道  小时维度统计充值数据修改"`
	commonApi.Author
	*model.SVideoChannelHourRechargeStatisticsEditReq
}

// SVideoChannelHourRechargeStatisticsEditRes 修改操作返回结果
type SVideoChannelHourRechargeStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SVideoChannelHourRechargeStatisticsGetReq 获取一条数据请求
type SVideoChannelHourRechargeStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"get" summary:"获取剧+渠道+广告渠道  小时维度统计充值数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SVideoChannelHourRechargeStatisticsGetRes 获取一条数据结果
type SVideoChannelHourRechargeStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SVideoChannelHourRechargeStatisticsInfoRes
}

// SVideoChannelHourRechargeStatisticsDeleteReq 删除数据请求
type SVideoChannelHourRechargeStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"delete" summary:"删除剧+渠道+广告渠道  小时维度统计充值数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SVideoChannelHourRechargeStatisticsDeleteRes 删除数据返回
type SVideoChannelHourRechargeStatisticsDeleteRes struct {
	commonApi.EmptyRes
}

// SVideoChannelHourRechargeGetByTheaterReq 获取剧的小时统计
type SVideoChannelHourRechargeGetByTheaterReq struct {
	g.Meta `path:"/getByTheaterId" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"post" summary:"获取剧+渠道+广告渠道  小时维度统计充值数据信息"`
	commonApi.Author
	TheaterId  int    `p:"theaterId" v:"required#主键必须"`  //通过主键获取
	CreateDate string `p:"createDate" v:"required#时间必须"` //通过主键获取
}

// SVideoChannelHourRechargeStatisticsGetByTheaterRes 获取一条数据结果
type SVideoChannelHourRechargeStatisticsGetByTheaterRes struct {
	g.Meta `mime:"application/json"`

	List []*model.SVideoChannelHourRecharge `json:"list"`
}

// SVideoChannelHourRechargeStatisticsTaskReq 分页请求参数
type SVideoChannelHourRechargeStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"剧+渠道+广告渠道  小时维度统计充值数据" method:"post" summary:"剧+渠道+广告渠道  小时维度统计充值数据任务"`
	commonApi.Author
	model.SVideoChannelHourRechargeStatisticsTaskReq
}

// SVideoChannelHourRechargeStatisticsTaskRes 列表返回结果
type SVideoChannelHourRechargeStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}
