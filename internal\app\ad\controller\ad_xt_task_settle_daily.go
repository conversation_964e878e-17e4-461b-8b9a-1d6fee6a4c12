// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-21 14:30:52
// 生成路径: internal/app/ad/controller/ad_xt_task_settle_daily.go
// 生成人：cyao
// desc:星图结算数据分天
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type adXtTaskSettleDailyController struct {
	systemController.BaseController
}

var AdXtTaskSettleDaily = new(adXtTaskSettleDailyController)

// List 列表
func (c *adXtTaskSettleDailyController) List(ctx context.Context, req *ad.AdXtTaskSettleDailySearchReq) (res *ad.AdXtTaskSettleDailySearchRes, err error) {
	res = new(ad.AdXtTaskSettleDailySearchRes)
	res.AdXtTaskSettleDailySearchRes, err = service.AdXtTaskSettleDaily().List(ctx, &req.AdXtTaskSettleDailySearchReq)
	return
}

// Export 导出excel
func (c *adXtTaskSettleDailyController) Export(ctx context.Context, req *ad.AdXtTaskSettleDailyExportReq) (res *ad.AdXtTaskSettleDailyExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdXtTaskSettleDailyListRes
		//表头
		tableHead = []interface{}{"ID", "账户ID", "账户名", "任务ID", "任务名", "视频唯一ID", "视频标题", "作者ID", "用户平台ID", "内容提供方ID", "日期", "当天产生的预估付费流水金额", "当天发放的达人付费分佣金额", "当天产生的预估广告消耗金额", "当天发放的达人广告分成金额", "创建时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		//listData, err = service.AdXtTaskSettleDaily().GetExportData(ctx, &req.AdXtTaskSettleDailySearchReq)
		listRs, _ := service.AdXtTaskSettleDaily().List(ctx, &req.AdXtTaskSettleDailySearchReq)
		if listRs == nil || len(listRs.List) == 0 {
			break
		} else {
			listData = listRs.List
		}
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.Id,
				v.AdvertiserId,
				v.AdvertiserNick,
				v.TaskId,
				v.TaskName,
				v.ItemId,
				v.Title,
				v.AuthorId,
				gconv.String(v.Uid),
				gconv.String(v.ProviderId),
				v.PDate.Format("Y-m-d H:i:s"),
				libUtils.ToRound(float64(v.EstSales)/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(float64(v.SettleCps)/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(float64(v.EstAdCost)/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(float64(v.SettleAdShare)/100, 2, libUtils.RoundHalfEven),
				v.CreatedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("星图结算数据分天")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取星图结算数据分天
func (c *adXtTaskSettleDailyController) Get(ctx context.Context, req *ad.AdXtTaskSettleDailyGetReq) (res *ad.AdXtTaskSettleDailyGetRes, err error) {
	res = new(ad.AdXtTaskSettleDailyGetRes)
	res.AdXtTaskSettleDailyInfoRes, err = service.AdXtTaskSettleDaily().GetById(ctx, req.Id)
	return
}

// Add 添加星图结算数据分天
func (c *adXtTaskSettleDailyController) Add(ctx context.Context, req *ad.AdXtTaskSettleDailyAddReq) (res *ad.AdXtTaskSettleDailyAddRes, err error) {
	err = service.AdXtTaskSettleDaily().Add(ctx, req.AdXtTaskSettleDailyAddReq)
	return
}

// Edit 修改星图结算数据分天
func (c *adXtTaskSettleDailyController) Edit(ctx context.Context, req *ad.AdXtTaskSettleDailyEditReq) (res *ad.AdXtTaskSettleDailyEditRes, err error) {
	err = service.AdXtTaskSettleDaily().Edit(ctx, req.AdXtTaskSettleDailyEditReq)
	return
}

// Delete 删除星图结算数据分天
func (c *adXtTaskSettleDailyController) Delete(ctx context.Context, req *ad.AdXtTaskSettleDailyDeleteReq) (res *ad.AdXtTaskSettleDailyDeleteRes, err error) {
	err = service.AdXtTaskSettleDaily().Delete(ctx, req.Ids)
	return
}
