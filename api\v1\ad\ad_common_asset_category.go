// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: api/v1/ad/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdCommonAssetCategorySearchReq 分页请求参数
type AdCommonAssetCategorySearchReq struct {
	g.Meta `path:"/list" tags:"通用资产-标题分类" method:"post" summary:"通用资产-标题分类列表"`
	commonApi.Author
	model.AdCommonAssetCategorySearchReq
}

// AdCommonAssetCategorySearchRes 列表返回结果
type AdCommonAssetCategorySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetCategorySearchRes
}

// AdCommonAssetCategoryAddReq 添加操作请求参数
type AdCommonAssetCategoryAddReq struct {
	g.Meta `path:"/add" tags:"通用资产-标题分类" method:"post" summary:"通用资产-标题分类添加"`
	commonApi.Author
	*model.AdCommonAssetCategoryAddReq
}

// AdCommonAssetCategoryAddRes 添加操作返回结果
type AdCommonAssetCategoryAddRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetCategoryEditReq 修改操作请求参数
type AdCommonAssetCategoryEditReq struct {
	g.Meta `path:"/edit" tags:"通用资产-标题分类" method:"put" summary:"通用资产-标题分类修改"`
	commonApi.Author
	*model.AdCommonAssetCategoryEditReq
}

// AdCommonAssetCategoryEditRes 修改操作返回结果
type AdCommonAssetCategoryEditRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetCategoryGetReq 获取一条数据请求
type AdCommonAssetCategoryGetReq struct {
	g.Meta `path:"/get" tags:"通用资产-标题分类" method:"get" summary:"获取通用资产-标题分类信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdCommonAssetCategoryGetRes 获取一条数据结果
type AdCommonAssetCategoryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetCategoryInfoRes
}

// AdCommonAssetCategoryDeleteReq 删除数据请求
type AdCommonAssetCategoryDeleteReq struct {
	g.Meta `path:"/delete" tags:"通用资产-标题分类" method:"post" summary:"删除通用资产-标题分类"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdCommonAssetCategoryDeleteRes 删除数据返回
type AdCommonAssetCategoryDeleteRes struct {
	commonApi.EmptyRes
}
