// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: internal/app/ad/model/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdXtAccountInfoRes is the golang structure for table ad_xt_account.
type AdXtAccountInfoRes struct {
	gmeta.Meta     `orm:"table:ad_xt_account"`
	Id             int64       `orm:"id,primary" json:"id" dc:"ID"`                                                                    // ID
	AdvertiserId   string      `orm:"advertiser_id" json:"advertiserId" dc:"账户ID 对应 star_id "`                                         // 账户ID 对应 star_id
	AdvertiserNick string      `orm:"advertiser_nick" json:"advertiserNick" dc:"账户名称"`                                                 // 账户名称
	RoleName       string      `orm:"role_name" json:"roleName" dc:"角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图"`                               // 角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图
	AuthTime       *gtime.Time `orm:"auth_time" json:"authTime" dc:"授权时间"`                                                             // 授权时间
	AppId          int         `orm:"app_id" json:"appId" dc:"授权的app_id"`                                                              // 授权的app_id
	AccessToken    string      `orm:"access_token" json:"accessToken" dc:"用于验证权限的token"`                                               // 用于验证权限的token
	RefreshToken   string      `orm:"refresh_token" json:"refreshToken" dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"` // 刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间

	RefreshTokenExpiresIn *gtime.Time `orm:"refresh_token_expires_in"  dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"`
	//expires_in
	ExpiresIn *gtime.Time `orm:"expires_in"  dc:"expires_in"`

	Status    string      `orm:"status" json:"status" dc:"unauthorized未授权 authorized 已经授权"` // unauthorized未授权 authorized 已经授权
	CreatedAt *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                     // 创建时间
	UpdatedAt *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                     // 更新时间
	DeletedAt *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                     // 删除时间
}

type AdXtAccountListRes struct {
	Id             int64       `json:"id" dc:"ID"`
	AdvertiserId   string      `json:"advertiserId" dc:"账户ID 对应 star_id "`
	AdvertiserNick string      `json:"advertiserNick" dc:"账户名称"`
	RoleName       string      `json:"roleName" dc:"角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图"`
	AuthTime       *gtime.Time `json:"authTime" dc:"授权时间"`
	AppId          int         `json:"appId" dc:"授权的app_id"`
	AccessToken    string      `json:"accessToken" dc:"用于验证权限的token"`
	RefreshToken   string      `json:"refreshToken" dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"`
	Status         string      `json:"status" dc:"unauthorized未授权 authorized 已经授权"`
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdXtAccountSearchReq 分页请求参数
type AdXtAccountSearchReq struct {
	comModel.PageReq
	Id             string `p:"id" dc:"ID"`                                                                 //ID
	AdvertiserId   string `p:"advertiserId" dc:"账户ID 对应 star_id "`                                         //账户ID 对应 star_id
	AdvertiserNick string `p:"advertiserNick" dc:"账户名称"`                                                   //账户名称
	RoleName       string `p:"roleName" dc:"角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图"`                           //角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图
	AuthTime       string `p:"authTime" v:"authTime@datetime#授权时间需为YYYY-MM-DD hh:mm:ss格式" dc:"授权时间"`       //授权时间
	AppId          string `p:"appId" v:"appId@integer#授权的app_id需为整数" dc:"授权的app_id"`                       //授权的app_id
	AccessToken    string `p:"accessToken" dc:"用于验证权限的token"`                                              //用于验证权限的token
	RefreshToken   string `p:"refreshToken" dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"` //刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间
	Status         string `p:"status" dc:"unauthorized未授权 authorized 已经授权"`                                //unauthorized未授权 authorized 已经授权
	CreatedAt      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`     //创建时间
}

// AdXtAccountSearchRes 列表返回结果
type AdXtAccountSearchRes struct {
	comModel.ListRes
	List []*AdXtAccountListRes `json:"list"`
}

// AdXtAccountAddReq 添加操作请求参数
type AdXtAccountAddReq struct {
	AdvertiserId   string      `p:"advertiserId"  dc:"账户ID 对应 star_id "`
	AdvertiserNick string      `p:"advertiserNick"  dc:"账户名称"`
	RoleName       string      `p:"roleName" v:"required#角色枚举 账户角色PLATFORM_ROLE_STAR 对应星图不能为空" dc:"角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图"`
	AuthTime       *gtime.Time `p:"authTime"  dc:"授权时间"`
	AppId          int64       `p:"appId"  dc:"授权的app_id"`
	AccessToken    string      `p:"accessToken"  dc:"用于验证权限的token"`
	RefreshToken   string      `p:"refreshToken"  dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"`
	//refresh_token_expires_in
	RefreshTokenExpiresIn *gtime.Time `p:"refreshTokenExpiresIn"  dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"`
	//expires_in
	ExpiresIn *gtime.Time `p:"expiresIn"  dc:"expires_in"`
	Status    string      `p:"status" v:"required#unauthorized未授权 authorized 已经授权不能为空" dc:"unauthorized未授权 authorized 已经授权"`
}

// AdXtAccountEditReq 修改操作请求参数
type AdXtAccountEditReq struct {
	Id             int64       `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AdvertiserId   string      `p:"advertiserId"  dc:"账户ID 对应 star_id "`
	AdvertiserNick string      `p:"advertiserNick"  dc:"账户名称"`
	RoleName       string      `p:"roleName" v:"required#角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图不能为空" dc:"角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图"`
	AuthTime       *gtime.Time `p:"authTime"  dc:"授权时间"`
	AppId          int         `p:"appId"  dc:"授权的app_id"`
	Status         string      `p:"status" v:"required#unauthorized未授权 authorized 已经授权不能为空" dc:"unauthorized未授权 authorized 已经授权"`
}
