// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/dao/internal/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdAccountDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdAccountDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns FqAdAccountColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdAccountColumns defines and stores column names for table fq_ad_account.
type FqAdAccountColumns struct {
	DistributorId string // 渠道ID
	SecretKey     string // 秘钥
	AccountName   string // 账号名
	DramaType     string // 抖小IAP	抖小IAA	微小IAP	微小IAA
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	DeletedAt     string // 删除时间
}

var fqAdAccountColumns = FqAdAccountColumns{
	DistributorId: "distributor_id",
	SecretKey:     "secret_key",
	AccountName:   "account_name",
	DramaType:     "drama_type",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewFqAdAccountDao creates and returns a new DAO object for table data access.
func NewFqAdAccountDao() *FqAdAccountDao {
	return &FqAdAccountDao{
		group:   "default",
		table:   "fq_ad_account",
		columns: fqAdAccountColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdAccountDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdAccountDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdAccountDao) Columns() FqAdAccountColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdAccountDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdAccountDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdAccountDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
