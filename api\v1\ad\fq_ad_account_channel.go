// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: api/v1/ad/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdAccountChannelSearchReq 分页请求参数
type FqAdAccountChannelSearchReq struct {
	g.Meta `path:"/list" tags:"番茄渠道" method:"post" summary:"番茄渠道列表"`
	//commonApi.Author
	model.FqAdAccountChannelSearchReq
}

// FqAdAccountChannelSearchRes 列表返回结果
type FqAdAccountChannelSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountChannelSearchRes
}

type FqChannelStaticReq struct {
	g.Meta `path:"/channelStatic" tags:"番茄渠道" method:"post" summary:"账号数据统计"`
	commonApi.Author
	model.FqChannelStaticReq
}

type FqChannelStaticRes struct {
	g.Meta `mime:"application/json"`
	*model.FqChannelStaticRes
}

// 导出
type FqChannelStaticExportReq struct {
	g.Meta `path:"/channelStatic/export" tags:"番茄渠道" method:"post" summary:"导出账号数据统计"`
	commonApi.Author
	model.FqChannelStaticReq
}

type FqChannelStaticExportRes struct {
	commonApi.EmptyRes
}

// FqAdAccountChannelAddReq 添加操作请求参数
type FqAdAccountChannelAddReq struct {
	g.Meta `path:"/add" tags:"番茄渠道" method:"post" summary:"番茄渠道添加"`
	commonApi.Author
	*model.FqAdAccountChannelAddReq
}

// FqAdAccountChannelAddRes 添加操作返回结果
type FqAdAccountChannelAddRes struct {
	commonApi.EmptyRes
}

// FqAdAccountChannelEditReq 修改操作请求参数
type FqAdAccountChannelEditReq struct {
	g.Meta `path:"/edit" tags:"番茄渠道" method:"put" summary:"番茄渠道修改"`
	commonApi.Author
	*model.FqAdAccountChannelEditReq
}

// FqAdAccountChannelEditRes 修改操作返回结果
type FqAdAccountChannelEditRes struct {
	commonApi.EmptyRes
}

// FqAdAccountChannelGetReq 获取一条数据请求
type FqAdAccountChannelGetReq struct {
	g.Meta `path:"/get" tags:"番茄渠道" method:"get" summary:"获取番茄渠道信息"`
	commonApi.Author
	ChannelDistributorId int64 `p:"channelDistributorId" v:"required#主键必须"` //通过主键获取
}

// FqAdAccountChannelGetRes 获取一条数据结果
type FqAdAccountChannelGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountChannelInfoRes
}

// FqAdAccountChannelDeleteReq 删除数据请求
type FqAdAccountChannelDeleteReq struct {
	g.Meta `path:"/delete" tags:"番茄渠道" method:"post" summary:"删除番茄渠道"`
	commonApi.Author
	ChannelDistributorIds []int64 `p:"channelDistributorIds" v:"required#主键必须"` //通过主键删除
}

// FqAdAccountChannelDeleteRes 删除数据返回
type FqAdAccountChannelDeleteRes struct {
	commonApi.EmptyRes
}

// SyncFqAdAccountChannelReq 同步番茄渠道请求
type SyncFqAdAccountChannelReq struct {
	g.Meta `path:"/sync" tags:"番茄渠道" method:"post" summary:"同步番茄渠道"`
	commonApi.Author
}

// SyncFqAdAccountChannelRes 同步番茄渠道数据返回
type SyncFqAdAccountChannelRes struct {
	commonApi.EmptyRes
}
