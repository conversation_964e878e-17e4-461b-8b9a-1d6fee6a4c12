// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: internal/app/adx/service/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxTaskItem interface {
	List(ctx context.Context, req *model.AdxTaskItemSearchReq) (res *model.AdxTaskItemSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxTaskItemSearchReq) (listRes []*model.AdxTaskItemInfoRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdxTaskItemInfoRes, err error)
	GetByTaskId(ctx context.Context, id int64) (res []*model.AdxTaskItemInfoRes, err error)
	Add(ctx context.Context, req *model.AdxTaskItemAddReq) (err error)
	AddBatch(ctx context.Context, req []*model.AdxTaskItemAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxTaskItemEditReq) (err error)
	GetUrlsByTaskId(ctx context.Context, taskId int64) (urls []string, err error)
	Delete(ctx context.Context, Id []int64) (err error)
	DownloadStatus(ctx context.Context, id int64, status string, reason string) error
}

var localAdxTaskItem IAdxTaskItem

func AdxTaskItem() IAdxTaskItem {
	if localAdxTaskItem == nil {
		panic("implement not found for interface IAdxTaskItem, forgot register?")
	}
	return localAdxTaskItem
}

func RegisterAdxTaskItem(i IAdxTaskItem) {
	localAdxTaskItem = i
}
