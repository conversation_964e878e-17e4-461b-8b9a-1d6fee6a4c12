// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:15
// 生成路径: api/v1/oceanengine/ad_strategy_task.go
// 生成人：gfast
// desc:巨量广告搭建-任务相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyTaskSearchReq 分页请求参数
type AdStrategyTaskSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-任务" method:"get" summary:"巨量广告搭建-任务列表"`
	commonApi.Author
	model.AdStrategyTaskSearchReq
}

// AdStrategyTaskSearchRes 列表返回结果
type AdStrategyTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTaskSearchRes
}

// AdStrategyTaskAddReq 添加操作请求参数
type AdStrategyTaskAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-任务" method:"post" summary:"巨量广告搭建-任务添加"`
	commonApi.Author
	*model.AdStrategyTaskAddReq
}

// AdStrategyTaskAddRes 添加操作返回结果
type AdStrategyTaskAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyTaskEditReq 修改操作请求参数
type AdStrategyTaskEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-任务" method:"put" summary:"巨量广告搭建-任务修改"`
	commonApi.Author
	*model.AdStrategyTaskEditReq
}

// AdStrategyTaskEditRes 修改操作返回结果
type AdStrategyTaskEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyTaskGetReq 获取一条数据请求
type AdStrategyTaskGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-任务" method:"get" summary:"获取巨量广告搭建-任务信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyTaskGetRes 获取一条数据结果
type AdStrategyTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTaskGet
}

// AdStrategyTaskDeleteReq 删除数据请求
type AdStrategyTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-任务" method:"delete" summary:"删除巨量广告搭建-任务"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyTaskDeleteRes 删除数据返回
type AdStrategyTaskDeleteRes struct {
	commonApi.EmptyRes
}
