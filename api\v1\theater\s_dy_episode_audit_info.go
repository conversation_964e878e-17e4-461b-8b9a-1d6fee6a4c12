// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-19 11:11:08
// 生成路径: api/v1/theater/s_dy_episode_audit_info.go
// 生成人：cq
// desc:抖音剧集信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyEpisodeAuditInfoSearchReq 分页请求参数
type SDyEpisodeAuditInfoSearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"get" summary:"剧集列表"`
	commonApi.Author
	model.SDyEpisodeAuditInfoSearchReq
}

// SDyEpisodeAuditInfoSearchRes 列表返回结果
type SDyEpisodeAuditInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyEpisodeAuditInfoSearchRes
}

// SDyEpisodeAuditInfoAddReq 添加操作请求参数
type SDyEpisodeAuditInfoAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"添加剧集"`
	commonApi.Author
	*model.SDyEpisodeAuditInfoAddReq
}

// SDyEpisodeAuditInfoAddRes 添加操作返回结果
type SDyEpisodeAuditInfoAddRes struct {
	commonApi.EmptyRes
}

// SDyEpisodeAuditInfoEditReq 编辑操作请求参数
type SDyEpisodeAuditInfoEditReq struct {
	g.Meta `path:"/edit" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"put" summary:"编辑剧集"`
	commonApi.Author
	*model.SDyEpisodeAuditInfoEditReq
}

// SDyEpisodeAuditInfoEditRes 编辑操作返回结果
type SDyEpisodeAuditInfoEditRes struct {
	commonApi.EmptyRes
}
