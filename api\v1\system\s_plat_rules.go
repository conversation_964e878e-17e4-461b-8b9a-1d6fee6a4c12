// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-23 18:40:45
// 生成路径: api/v1/system/s_plat_rules.go
// 生成人：gfast
// desc:系统平台规则表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// SPlatRulesSearchReq 分页请求参数
type SPlatRulesSearchReq struct {
	g.Meta `path:"/list" tags:"系统平台规则表" method:"get" summary:"系统平台规则表列表"`
	commonApi.Author
	model.SPlatRulesSearchReq
}

// SPlatRulesSearchRes 列表返回结果
type SPlatRulesSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SPlatRulesSearchRes
	comModel.PageReq
}

type GetMiniInfoReq struct {
	g.Meta `path:"/getMiniInfo" tags:"系统平台规则表" method:"get" summary:"获取小程序列表  添加分页和 keyword 查询"`
	commonApi.Author
	comModel.PageReq
	KeyWard string //关键字搜索
}

type GetMiniInfoRes struct {
	g.Meta `mime:"application/json"`
	*model.GetMiniInfoRes
}

type GetMiniInfoAllReq struct {
	g.Meta `path:"/getMiniInfo/all" tags:"系统平台规则表" method:"get" summary:"获取小程序列表  添加分页all"`
	commonApi.Author
	comModel.PageReq
	KeyWard string //关键字搜索
}

type GetMiniInfoAllRes struct {
	g.Meta `mime:"application/json"`
	*model.GetMiniInfoRes
}

type GetMiniInfoByTypeReq struct {
	g.Meta `path:"/GetMiniInfoByType" tags:"系统平台规则表" method:"get" summary:"根据小程序类型获取小程序列表"`
	commonApi.Author
	ChannelType int    `p:"channelType" dc:"小程序类型 1：微信 2：抖音"`
	VirtualPay  string `p:"virtualPay" dc:"是否支持虚拟支付 1：支持 0：不支持"`
}

type GetMiniInfoByTypeRes struct {
	g.Meta `mime:"application/json"`
	*model.GetMiniInfoByTypeRes
}

// SPlatRulesAddReq 添加操作请求参数
type SPlatRulesAddReq struct {
	g.Meta `path:"/add" tags:"系统平台规则表" method:"post" summary:"系统平台规则表添加"`
	commonApi.Author
	*model.SPlatRulesAddReq
}

// SPlatRulesAddRes 添加操作返回结果
type SPlatRulesAddRes struct {
	commonApi.EmptyRes
}

// SPlatRulesEditReq 修改操作请求参数
type SPlatRulesEditReq struct {
	g.Meta `path:"/edit" tags:"系统平台规则表" method:"put" summary:"系统平台规则表修改"`
	commonApi.Author
	*model.SPlatRulesEditReq
}

// SPlatRulesEditRes 修改操作返回结果
type SPlatRulesEditRes struct {
	commonApi.EmptyRes
}

// SPlatRulesGetReq 获取一条数据请求
type SPlatRulesGetReq struct {
	g.Meta `path:"/get" tags:"系统平台规则表" method:"get" summary:"获取系统平台规则表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SPlatRulesGetRes 获取一条数据结果
type SPlatRulesGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SPlatRulesInfoRes
}

// SPlatRulesDeleteReq 删除数据请求
type SPlatRulesDeleteReq struct {
	g.Meta `path:"/delete" tags:"系统平台规则表" method:"post" summary:"删除系统平台规则表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SPlatRulesDeleteRes 删除数据返回
type SPlatRulesDeleteRes struct {
	commonApi.EmptyRes
}
