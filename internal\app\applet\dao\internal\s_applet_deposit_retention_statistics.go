// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-08-28 15:12:15
// 生成路径: internal/app/applet/dao/internal/s_applet_deposit_retention_statistics.go
// 生成人：cyao
// desc:小程序维度用户/充值留存数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SAppletDepositRetentionStatisticsDao is the manager for logic model data accessing and custom defined data operations functions management.
type SAppletDepositRetentionStatisticsDao struct {
	table   string                                   // Table is the underlying table name of the DAO.
	group   string                                   // Group is the database configuration group name of current DAO.
	columns SAppletDepositRetentionStatisticsColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SAppletDepositRetentionStatisticsColumns defines and stores column names for table s_applet_deposit_retention_statistics.
type SAppletDepositRetentionStatisticsColumns struct {
	Id                  string //
	CreateTime          string // 日期
	AppId               string // appId
	AppName             string // app名称
	TotalNumberUsers    string // 平台用户：该日期的当前的平台用户总数
	NewUserNums         string // 新增用户：当天新增用户数量
	UserGrowthRate      string // 用户增长率：新增用户/平台用户*100%
	ActiveUserNums      string // 活跃人数：当天登陆人数
	RechargeNums        string // 付费人数：当天付费人数
	NewUserRechargeNums string // 新增付费人数：新增用户付费人数
	TotalAmount         string // 付费金额：当天付费金额
	NewUserAmount       string // 新增付费金额：当天新增用户付费金额
	SameDayRate         string // 当天付费率：付费人数/活跃人数*100%
	IncrementalFeeRate  string // 新增付费率：新增付费人数/新增用户*100%
	UserRetention       string // 用户次留：当天注册用户在第2天的活跃人数
	RetentionRate       string // 留存率：用户次留/新增用户*100%
	NextDayPayment      string // 次日付费：当天注册用户两天内的付费金额
	UserRetention3Day   string // 用户次留：3day
	RetentionRate3Day   string // 留存率：3day
	NextDayPayment3Day  string // 次日付费：3day
	UserRetention7Day   string // 用户次留：7day
	RetentionRate7Day   string // 留存率：7day
	NextDayPayment7Day  string // 次日付费：7day
	UserRetention30Day  string // 用户次留：30day
	RetentionRate30Day  string // 留存率：30day
	NextDayPayment30Day string // 次日付费：30day
	AppType             string //
}

var sAppletDepositRetentionStatisticsColumns = SAppletDepositRetentionStatisticsColumns{
	Id:                  "id",
	CreateTime:          "create_time",
	AppId:               "app_id",
	AppName:             "app_name",
	TotalNumberUsers:    "total_number_users",
	NewUserNums:         "new_user_nums",
	UserGrowthRate:      "user_growth_rate",
	ActiveUserNums:      "active_user_nums",
	RechargeNums:        "recharge_nums",
	NewUserRechargeNums: "new_user_recharge_nums",
	TotalAmount:         "total_amount",
	NewUserAmount:       "new_user_amount",
	SameDayRate:         "same_day_rate",
	IncrementalFeeRate:  "incremental_fee_rate",
	UserRetention:       "user_retention",
	RetentionRate:       "retention_rate",
	NextDayPayment:      "next_day_payment",
	UserRetention3Day:   "user_retention_3day",
	RetentionRate3Day:   "retention_rate_3day",
	NextDayPayment3Day:  "next_day_payment_3day",
	UserRetention7Day:   "user_retention_7day",
	RetentionRate7Day:   "retention_rate_7day",
	NextDayPayment7Day:  "next_day_payment_7day",
	UserRetention30Day:  "user_retention_30day",
	RetentionRate30Day:  "retention_rate_30day",
	NextDayPayment30Day: "next_day_payment_30day",
	AppType:             "app_type",
}

// NewSAppletDepositRetentionStatisticsDao creates and returns a new DAO object for table data access.
func NewSAppletDepositRetentionStatisticsDao() *SAppletDepositRetentionStatisticsDao {
	return &SAppletDepositRetentionStatisticsDao{
		group:   "default",
		table:   "s_applet_deposit_retention_statistics",
		columns: sAppletDepositRetentionStatisticsColumns,
	}
}

func NewSAppletDepositRetentionStatisticsAnalyticDao() *SAppletDepositRetentionStatisticsDao {
	return &SAppletDepositRetentionStatisticsDao{
		group:   "analyticDB",
		table:   "s_applet_deposit_retention_statistics",
		columns: sAppletDepositRetentionStatisticsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SAppletDepositRetentionStatisticsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SAppletDepositRetentionStatisticsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SAppletDepositRetentionStatisticsDao) Columns() SAppletDepositRetentionStatisticsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SAppletDepositRetentionStatisticsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SAppletDepositRetentionStatisticsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SAppletDepositRetentionStatisticsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
