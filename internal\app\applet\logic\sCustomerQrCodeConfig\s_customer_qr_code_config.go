// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-03-07 15:31:00
// 生成路径: internal/app/applet/logic/s_customer_qr_code_config.go
// 生成人：cyao
// desc:微信二维码配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterSCustomerQrCodeConfig(New())
}

func New() service.ISCustomerQrCodeConfig {
	return &sSCustomerQrCodeConfig{}
}

type sSCustomerQrCodeConfig struct{}

func (s *sSCustomerQrCodeConfig) List(ctx context.Context, req *model.SCustomerQrCodeConfigSearchReq) (listRes *model.SCustomerQrCodeConfigSearchRes, err error) {
	listRes = new(model.SCustomerQrCodeConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SCustomerQrCodeConfig.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.SCustomerQrCodeConfig.Columns().Id+" = ?", req.Id)
		}
		if req.AppId != "" {
			m = m.Where(dao.SCustomerQrCodeConfig.Columns().AppId+" = ?", req.AppId)
		}
		if req.QrCodeUrl != "" {
			m = m.Where(dao.SCustomerQrCodeConfig.Columns().QrCodeUrl+" = ?", req.QrCodeUrl)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.SCustomerQrCodeConfig.Columns().Createtime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.SCustomerQrCodeConfig.Columns().Updatetime+" = ?", gconv.Time(req.UpdateTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SCustomerQrCodeConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = res
		var ids []string
		for _, item := range res {
			ids = append(ids, item.AppId)
		}
		minList, _ := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, ids)
		//赋值appName
		for _, item := range listRes.List {
			for _, info := range minList {
				if info.AppId == item.AppId {
					item.AppName = info.AppName
					break
				}
			}
		}
	})
	return
}

func (s *sSCustomerQrCodeConfig) GetById(ctx context.Context, id int64) (res *model.SCustomerQrCodeConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SCustomerQrCodeConfig.Ctx(ctx).WithAll().Where(dao.SCustomerQrCodeConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSCustomerQrCodeConfig) Add(ctx context.Context, req *model.SCustomerQrCodeConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		qrCodeConfig := do.SCustomerQrCodeConfig{
			AppId:      req.AppId,
			QrCodeUrl:  req.QrCodeUrl,
			Remark:     req.Remark,
			Createtime: gtime.Now(),
			Updatetime: gtime.Now(),
		}
		result, err2 := dao.SCustomerQrCodeConfig.Ctx(ctx).Insert(qrCodeConfig)
		liberr.ErrIsNil(ctx, err2, "添加失败")
		lId, err2 := result.LastInsertId()
		qrCodeConfig.Id = lId
		str, innerError := libUtils.SerializeQuoteStruct(qrCodeConfig)
		if innerError != nil {
			liberr.ErrIsNil(ctx, innerError, "SerializeQuoteStruct SCustomerQrCodeConfig失败")
		} else {
			_, err = commonService.RedisCache().Set(ctx, fmt.Sprintf("%s%s", commonConsts.PlatQrCodeConfig, req.AppId), str)
			liberr.ErrIsNil(ctx, err, " RedisCache Set Error key :"+fmt.Sprintf("%s%s", commonConsts.PlatQrCodeConfig, req.AppId))
		}
	})
	return
}

func (s *sSCustomerQrCodeConfig) Edit(ctx context.Context, req *model.SCustomerQrCodeConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var entity do.SCustomerQrCodeConfig
		err = dao.SCustomerQrCodeConfig.Ctx(ctx).Where(dao.SCustomerQrCodeConfig.Columns().Id+" = ? ", req.Id).Scan(&entity)
		entity.AppId = req.AppId
		entity.QrCodeUrl = req.QrCodeUrl
		entity.Updatetime = gtime.Now()
		entity.Remark = req.Remark
		_, err = dao.SCustomerQrCodeConfig.Ctx(ctx).WherePri(req.Id).Update(entity)
		liberr.ErrIsNil(ctx, err, "修改失败")
		str, innerError := libUtils.SerializeQuoteStruct(entity)
		if innerError != nil {
			liberr.ErrIsNil(ctx, innerError, "SerializeQuoteStruct SCustomerQrCodeConfig失败")
		} else {
			_, err = commonService.RedisCache().Set(ctx, fmt.Sprintf("%s%s", commonConsts.PlatQrCodeConfig, req.AppId), str)
			liberr.ErrIsNil(ctx, err, " RedisCache Set Error key :"+fmt.Sprintf("%s%s", commonConsts.PlatQrCodeConfig, req.AppId))
		}
	})
	return
}

func (s *sSCustomerQrCodeConfig) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []do.SCustomerQrCodeConfig
		err = dao.SCustomerQrCodeConfig.Ctx(ctx).Where(dao.SCustomerQrCodeConfig.Columns().Id+" in (?)", ids).Scan(&list)
		liberr.ErrIsNil(ctx, err, "未找到任何数据")
		for _, config := range list {
			_, err = commonService.RedisCache().Del(ctx, fmt.Sprintf("%s%s", commonConsts.PlatQrCodeConfig, config.AppId))
			liberr.ErrIsNil(ctx, err, "RedisCache Del error key : "+fmt.Sprintf("%s%s", commonConsts.PlatQrCodeConfig, config.AppId))
		}
		_, err = dao.SCustomerQrCodeConfig.Ctx(ctx).Delete(dao.SCustomerQrCodeConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")

	})
	return
}
