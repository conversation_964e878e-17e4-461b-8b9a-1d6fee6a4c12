// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-11 13:50:11
// 生成路径: internal/app/ad/model/ad_common_asset_title.go
// 生成人：cq
// desc:通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdCommonAssetTitleInfoRes is the golang structure for table ad_common_asset_title.
type AdCommonAssetTitleInfoRes struct {
	gmeta.Meta        `orm:"table:ad_common_asset_title"`
	Id                int                             `orm:"id,primary" json:"id" dc:""`                                 //
	Title             string                          `orm:"title" json:"title" dc:"标题"`                                 // 标题
	CategoryIds       string                          `orm:"category_ids" json:"categoryIds" dc:"标题分类ID列表，以|分隔"`         // 标题分类ID列表，以|分隔
	UserId            int                             `orm:"user_id" json:"userId" dc:"创建者"`                             // 创建者
	Last3DayClickRate float64                         `orm:"last_3_day_click_rate" json:"last3DayClickRate" dc:"近3日点击率"` // 近3日点击率
	Last3DayCost      float64                         `orm:"last_3_day_cost" json:"last3DayCost" dc:"近3日消耗"`             // 近3日消耗
	HistoryClickRate  float64                         `orm:"history_click_rate" json:"historyClickRate" dc:"历史点击率"`      // 历史点击率
	HistoryCost       float64                         `orm:"history_cost" json:"historyCost" dc:"历史消耗"`                  // 历史消耗
	AdCount           int                             `orm:"ad_count" json:"adCount" dc:"关联广告数"`                         // 关联广告数
	CreatedAt         *gtime.Time                     `orm:"created_at" json:"createdAt" dc:"创建时间"`                      // 创建时间
	UpdatedAt         *gtime.Time                     `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                      // 更新时间
	DeletedAt         *gtime.Time                     `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                      // 删除时间
	CategoryList      []*AdCommonAssetCategoryListRes `orm:"-" json:"categoryList" dc:"标题分类列表"`
}

type AdCommonAssetTitleListRes struct {
	Id                int                             `json:"id" dc:""`
	Title             string                          `json:"title" dc:"标题"`
	CategoryIds       string                          `json:"categoryIds" dc:"标题分类ID列表，以|分隔"`
	CategoryList      []*AdCommonAssetCategoryListRes `json:"categoryList" dc:"标题分类列表"`
	UserId            int                             `json:"userId" dc:"创建者ID"`
	UserName          string                          `json:"userName" dc:"创建者"`
	Last3DayClickRate float64                         `json:"last3DayClickRate" dc:"近3日点击率"`
	Last3DayCost      float64                         `json:"last3DayCost" dc:"近3日消耗"`
	HistoryClickRate  float64                         `json:"historyClickRate" dc:"历史点击率"`
	HistoryCost       float64                         `json:"historyCost" dc:"历史消耗"`
	AdCount           int                             `json:"adCount" dc:"关联广告数"`
	CreatedAt         *gtime.Time                     `json:"createdAt" dc:"创建时间"`
}

// AdCommonAssetTitleSearchReq 分页请求参数
type AdCommonAssetTitleSearchReq struct {
	comModel.PageReq
	Title       string `p:"title" dc:"标题"`             //标题
	CategoryIds []int  `p:"categoryIds" dc:"标题分类ID列表"` //标题分类ID列表
	TitleIds    []int  `p:"titleIds" dc:"标题ID列表"`
	UserIds     []int  `p:"userIds" dc:"创建者"`
}

type AdCommonAssetTitleGetRandomTitlesReq struct {
	comModel.PageReq
	//category
	CategoryId int `p:"categoryId" dc:"标题分类ID"`
	//limit_num
	LimitNum int `p:"limitNum" dc:"限制数量"`
	//exclude_today_is_used
	ExcludeTodayIsUsed bool `p:"excludeTodayIsUsed" dc:"排除今天已使用的标题"`
	//start_date
	StartDate string `p:"startDate" dc:"开始日期"`
	//end_date
	EndDate string `p:"endDate" dc:"结束日期"`
}

// AdCommonAssetTitleSearchRes 列表返回结果
type AdCommonAssetTitleSearchRes struct {
	comModel.ListRes
	List []*AdCommonAssetTitleListRes `json:"list"`
}

// AdCommonAssetTitleAddReq 添加操作请求参数
type AdCommonAssetTitleAddReq struct {
	Titles      []string `p:"titles"  dc:"标题列表"`
	CategoryIds []int    `p:"categoryIds"  dc:"标题分类ID列表"`
	UserId      int      `p:"userId"  dc:"创建者"`
}

// AdCommonAssetTitleEditReq 修改操作请求参数
type AdCommonAssetTitleEditReq struct {
	Ids         []int    `p:"ids" v:"required#主键ID不能为空" dc:""`
	Titles      []string `p:"titles"  dc:"标题"`
	CategoryIds []int    `p:"categoryIds"  dc:"标题分类ID列表"`
}
