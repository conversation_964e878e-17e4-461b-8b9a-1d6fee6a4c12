// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: internal/app/ad/controller/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type adXtTaskController struct {
	systemController.BaseController
}

var AdXtTask = new(adXtTaskController)

// List 列表
func (c *adXtTaskController) List(ctx context.Context, req *ad.AdXtTaskSearchReq) (res *ad.AdXtTaskSearchRes, err error) {
	res = new(ad.AdXtTaskSearchRes)
	res.AdXtTaskSearchRes, err = service.AdXtTask().List(ctx, &req.AdXtTaskSearchReq)
	return
}

// Pull 获取星图任务列表和任务详情
func (c *adXtTaskController) Pull(ctx context.Context, req *ad.AdXtTaskPullReq) (res *ad.AdXtTaskPullRes, err error) {
	res = new(ad.AdXtTaskPullRes)
	res.Count, err = service.AdXtTask().Pull(ctx)
	return
}

// AdXtTaskAsyncReq
func (c *adXtTaskController) Async(ctx context.Context, req *ad.AdXtTaskAsyncReq) (res *ad.AdXtTaskAsyncRes, err error) {
	res = new(ad.AdXtTaskAsyncRes)
	res.Count, err = service.AdXtTask().Async(ctx, req.StartTime, req.EndTime)
	return
}

// Export 导出excel
func (c *adXtTaskController) Export(ctx context.Context, req *ad.AdXtTaskExportReq) (res *ad.AdXtTaskExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdXtTaskInfoRes
		//表头
		tableHead = []interface{}{"ID", "账户ID 对应 star_id ", "任务ID", "任务名称", "参考素材", "达人侧任务名称", "任务图标", "任务头图", "投稿开始时间", "任务截止时间", "示例视频", "小程序ID", "小程序落地页地址", "组件标题", "结算方式", "达人定向范围", "服务商定向范围", "分佣比例", "广告分成比例", "付费分佣比例", "最长分账周期", "任务介绍", "任务状态", "", "创建时间", "更新时间", "删除时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.AdXtTask().GetExportData(ctx, &req.AdXtTaskSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.Id,
				v.AdvertiserId,
				v.TaskId,
				v.TaskName,
				v.UniversalSettlementType,
				v.Attachments,
				v.AuthorTaskName,
				v.TaskIcon,
				v.TaskHeadImage,
				v.StartTime.Format("Y-m-d H:i:s"),
				v.EndTime.Format("Y-m-d H:i:s"),
				v.SampleVideo,
				v.MicroAppId,
				v.StartPage,
				v.AnchorTitle,
				v.CommissionType,
				v.AuthorScope,
				v.ProviderScope,
				v.CommissionRate,
				v.AdCommissionRate,
				v.PayCommissionRate,
				v.AccountDivideDay,
				v.DemandDesc,
				v.OmTaskStatus,
				v.OmTaskTag,
				v.AuthorList,
				v.CreatedAt.Format("Y-m-d H:i:s"),
				v.UpdatedAt.Format("Y-m-d H:i:s"),
				v.DeletedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("星图任务列表和任务详情")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取星图任务列表和任务详情
func (c *adXtTaskController) Get(ctx context.Context, req *ad.AdXtTaskGetReq) (res *ad.AdXtTaskGetRes, err error) {
	res = new(ad.AdXtTaskGetRes)
	res.AdXtTaskInfoRes, err = service.AdXtTask().GetById(ctx, req.Id)
	return
}

// Add 添加星图任务列表和任务详情
func (c *adXtTaskController) Add(ctx context.Context, req *ad.AdXtTaskAddReq) (res *ad.AdXtTaskAddRes, err error) {
	err = service.AdXtTask().Add(ctx, req.AdXtTaskAddReq)
	return
}

// Edit 修改星图任务列表和任务详情
func (c *adXtTaskController) Edit(ctx context.Context, req *ad.AdXtTaskEditReq) (res *ad.AdXtTaskEditRes, err error) {
	err = service.AdXtTask().Edit(ctx, req.AdXtTaskEditReq)
	return
}

// Delete 删除星图任务列表和任务详情
func (c *adXtTaskController) Delete(ctx context.Context, req *ad.AdXtTaskDeleteReq) (res *ad.AdXtTaskDeleteRes, err error) {
	err = service.AdXtTask().Delete(ctx, req.Ids)
	return
}
