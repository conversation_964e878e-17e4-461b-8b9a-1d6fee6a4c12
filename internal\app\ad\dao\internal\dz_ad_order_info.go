// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:37
// 生成路径: internal/app/ad/dao/internal/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdOrderInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdOrderInfoDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns DzAdOrderInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdOrderInfoColumns defines and stores column names for table dz_ad_order_info.
type DzAdOrderInfoColumns struct {
	Id            string // 商户单号
	Ver           string // 小程序版本号
	OutTradeNo    string // 交易单号
	Discount      string // 订单金额（单位元）
	Type          string // 订单类型（1.看点充值 2.VIP）
	StatusNotify  string // 订单状态（0.待支付 1.支付成功）
	Ctime         string // 下单时间（秒）
	FinishTime    string // 完成时间（秒）
	UserId        string // 用户ID
	ChannelId     string // 渠道ID
	Domain        string // 业务线（14抖音 16微信 17快手）
	SourceInfo    string // 短剧ID
	ChapterId     string // 剧集ID
	SourceDesc    string // 短剧名称
	RegisterDate  string // 注册时间（秒）
	OpenId        string // 小程序openId
	Os            string // 机型（0:Android 1:iOS）
	ReferralId    string // 推广链接ID
	Adid          string // 计划ID
	FromDrId      string // 达人ID
	Platform      string // 达人平台
	Scene         string // 进入场景
	ThirdCorpId   string // 三方来源企微主体
	ThirdWxId     string // 三方企微唯一标识
	KdrId         string // 挂载达人ID
	SelfReturn    string // 自归因
	ProjectId     string // 头条2.0参数 - 项目ID
	PromotionId   string // 头条2.0参数 - 推广ID
	SchannelTime  string // 抖音快手注册时间
	DyeTime       string // 抖音快手推广链接染色时间
	XuniPay       string // 虚拟支付订单（1=是）
	MoneyBenefit  string // 渠道分成金额
	Mid1          string // 素材ID - 图片
	Mid2          string // 素材ID - 标题
	Mid3          string // 素材ID - 视频
	UnionId       string // 小程序unionId
	From          string // 投放媒体（如bd/dy/ks等）
	OrderSubType  string // 订单业务类型（如整本购）
	WxFinderId    string // 视频号ID
	WxExportId    string // 视频ID
	WxPromotionId string // 加热订单ID
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
}

var dzAdOrderInfoColumns = DzAdOrderInfoColumns{
	Id:            "id",
	Ver:           "ver",
	OutTradeNo:    "out_trade_no",
	Discount:      "discount",
	Type:          "type",
	StatusNotify:  "status_notify",
	Ctime:         "ctime",
	FinishTime:    "finish_time",
	UserId:        "user_id",
	ChannelId:     "channel_id",
	Domain:        "domain",
	SourceInfo:    "source_info",
	ChapterId:     "chapter_id",
	SourceDesc:    "source_desc",
	RegisterDate:  "register_date",
	OpenId:        "open_id",
	Os:            "os",
	ReferralId:    "referral_id",
	Adid:          "adid",
	FromDrId:      "from_dr_id",
	Platform:      "platform",
	Scene:         "scene",
	ThirdCorpId:   "third_corp_id",
	ThirdWxId:     "third_wx_id",
	KdrId:         "kdr_id",
	SelfReturn:    "self_return",
	ProjectId:     "project_id",
	PromotionId:   "promotion_id",
	SchannelTime:  "schannel_time",
	DyeTime:       "dye_time",
	XuniPay:       "xuni_pay",
	MoneyBenefit:  "money_benefit",
	Mid1:          "mid1",
	Mid2:          "mid2",
	Mid3:          "mid3",
	UnionId:       "union_id",
	From:          "from",
	OrderSubType:  "order_sub_type",
	WxFinderId:    "wx_finder_id",
	WxExportId:    "wx_export_id",
	WxPromotionId: "wx_promotion_id",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewDzAdOrderInfoDao creates and returns a new DAO object for table data access.
func NewDzAdOrderInfoDao() *DzAdOrderInfoDao {
	return &DzAdOrderInfoDao{
		group:   "default",
		table:   "dz_ad_order_info",
		columns: dzAdOrderInfoColumns,
	}
}

func NewDzAdOrderInfoAnalyticDao() *DzAdOrderInfoDao {
	return &DzAdOrderInfoDao{
		group:   "analyticDB",
		table:   "dz_ad_order_info",
		columns: dzAdOrderInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdOrderInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdOrderInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdOrderInfoDao) Columns() DzAdOrderInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdOrderInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdOrderInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdOrderInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
