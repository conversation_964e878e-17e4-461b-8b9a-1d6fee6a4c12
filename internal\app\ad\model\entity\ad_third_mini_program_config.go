// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: internal/app/ad/model/entity/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdThirdMiniProgramConfig is the golang structure for table ad_third_mini_program_config.
type AdThirdMiniProgramConfig struct {
	gmeta.Meta        `orm:"table:ad_third_mini_program_config"`
	Id                int64       `orm:"id,primary" json:"id"`                        //
	AppId             string      `orm:"app_id" json:"appId"`                         // 小程序ID
	AppName           string      `orm:"app_name" json:"appName"`                     // 小程序名称
	OriginalId        string      `orm:"original_id" json:"originalId"`               // 小程序原始ID
	AppType           string      `orm:"app_type" json:"appType"`                     // 小程序类型：微信 抖音
	MonetizationModel string      `orm:"monetization_model" json:"monetizationModel"` // 变现模式：IAP IAA
	Platform          int         `orm:"platform" json:"platform"`                    // 平台： 2: 番茄 3: 点众
	AccountId         int64       `orm:"account_id" json:"accountId"`                 // 点众账号ID
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt"`                 // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt"`                 // 更新时间
}
