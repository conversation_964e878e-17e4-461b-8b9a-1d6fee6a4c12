// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: internal/app/ad/model/entity/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterialFile is the golang structure for table ad_material_file.
type AdMaterialFile struct {
	gmeta.Meta  `orm:"table:ad_material_file"`
	FileId      int         `orm:"file_id,primary" json:"fileId"`            // id
	FileName    string      `orm:"file_name" json:"fileName"`                // 文件夹名称
	UserId      int         `orm:"user_id" json:"userId"`                    // 创建人
	AlbumId     int         `orm:"album_id" json:"albumId"`                  // 专辑id
	ParentId    int         `orm:"parent_id" json:"parentId"`                // 父级文件夹的id
	Remark      string      `orm:"remark" json:"remark"`                     // 备注
	AllPath     string      `orm:"all_path" json:"allPath"`                  // fileId 全路径 方便反查以下划线分隔
	Level       int         `orm:"level" json:"level"`                       // 层级
	Preview     string      `orm:"preview" json:"preview" dc:"缩略图 使用| 进行分割"` // 缩略图 使用| 进行分割
	MaterialNum int         `orm:"material_num" json:"materialNum" dc:"素材数量"`
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
	DeletedAt   *gtime.Time `orm:"deleted_at" json:"deletedAt"` // 删除时间
}
