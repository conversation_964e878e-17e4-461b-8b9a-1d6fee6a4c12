// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: internal/app/ad/dao/internal/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMaterialFileDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdMaterialFileDao struct {
	table   string                // Table is the underlying table name of the DAO.
	group   string                // Group is the database configuration group name of current DAO.
	columns AdMaterialFileColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdMaterialFileColumns defines and stores column names for table ad_material_file.
type AdMaterialFileColumns struct {
	FileId      string // id
	FileName    string // 文件夹名称
	UserId      string // 创建人
	AlbumId     string // 专辑id
	ParentId    string // 父级文件夹的id
	Remark      string // 备注
	AllPath     string // fileId 全路径 方便反查以下划线分隔
	CreatedAt   string // 创建时间
	Level       string
	UpdatedAt   string
	DeletedAt   string
	Preview     string
	MaterialNum string
}

var adMaterialFileColumns = AdMaterialFileColumns{
	FileId:      "file_id",
	FileName:    "file_name",
	UserId:      "user_id",
	AlbumId:     "album_id",
	ParentId:    "parent_id",
	Remark:      "remark",
	AllPath:     "all_path",
	CreatedAt:   "created_at",
	Level:       "level",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
	Preview:     "preview",
	MaterialNum: "material_num",
}

// NewAdMaterialFileDao creates and returns a new DAO object for table data access.
func NewAdMaterialFileDao() *AdMaterialFileDao {
	return &AdMaterialFileDao{
		group:   "default",
		table:   "ad_material_file",
		columns: adMaterialFileColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdMaterialFileDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdMaterialFileDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdMaterialFileDao) Columns() AdMaterialFileColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdMaterialFileDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdMaterialFileDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdMaterialFileDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
