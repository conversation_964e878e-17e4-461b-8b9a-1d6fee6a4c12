// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/controller/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adToolsSiteController struct {
	systemController.BaseController
}

var AdToolsSite = new(adToolsSiteController)

// List 列表
func (c *adToolsSiteController) List(ctx context.Context, req *ad.AdToolsSiteSearchReq) (res *ad.AdToolsSiteSearchRes, err error) {
	res = new(ad.AdToolsSiteSearchRes)
	res.AdToolsSiteSearchRes, err = service.AdToolsSite().List(ctx, &req.AdToolsSiteSearchReq)
	return
}

// GetPreviewUrl 获取橙子建站站点预览地址
func (c *adToolsSiteController) GetPreviewUrl(ctx context.Context, req *ad.GetPreviewUrlReq) (res *ad.GetPreviewUrlRes, err error) {
	res = new(ad.GetPreviewUrlRes)
	res.Url, err = service.AdToolsSite().GetPreviewUrl(ctx, req.AdvertiserId, req.SiteId)
	return
}

// 下线 删除 UpdateStatus
func (c *adToolsSiteController) UpdateStatus(ctx context.Context, req *ad.UpdateStatusReq) (res *ad.UpdateStatusRes, err error) {
	res, err = service.AdToolsSite().UpdateStatus(ctx, req)
	return
}

func (c *adToolsSiteController) SynchronizeSite(ctx context.Context, req *ad.SynchronizeSiteReq) (res *ad.SynchronizeSiteRes, err error) {
	err = service.AdToolsSite().SynchronizeSite(ctx, &req.SynchronizeSiteReq)
	return
}

func (c *adToolsSiteController) ToolsSiteHandsel(ctx context.Context, req *ad.ToolsSiteHandselReq) (res *ad.ToolsSiteHandselRes, err error) {
	res, err = service.AdToolsSite().ToolsSiteHandsel(ctx, req)
	return
}

// Get 获取广告落地页（工具站点）表
func (c *adToolsSiteController) Get(ctx context.Context, req *ad.AdToolsSiteGetReq) (res *ad.AdToolsSiteGetRes, err error) {
	res = new(ad.AdToolsSiteGetRes)
	res.AdToolsSiteInfoRes, err = service.AdToolsSite().GetById(ctx, req.Id)
	return
}

// Add 添加广告落地页（工具站点）表
func (c *adToolsSiteController) Add(ctx context.Context, req *ad.AdToolsSiteAddReq) (res *ad.AdToolsSiteAddRes, err error) {
	err = service.AdToolsSite().Add(ctx, req.AdToolsSiteAddReq)
	return
}

// Edit 修改广告落地页（工具站点）表
func (c *adToolsSiteController) Edit(ctx context.Context, req *ad.AdToolsSiteEditReq) (res *ad.AdToolsSiteEditRes, err error) {
	err = service.AdToolsSite().Edit(ctx, req.AdToolsSiteEditReq)
	return
}

// Delete 删除广告落地页（工具站点）表
func (c *adToolsSiteController) Delete(ctx context.Context, req *ad.AdToolsSiteDeleteReq) (res *ad.AdToolsSiteDeleteRes, err error) {
	err = service.AdToolsSite().Delete(ctx, req.Ids)
	return
}
