/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletCreateV30MinPaymentTierRange
type ToolsWechatAppletCreateV30MinPaymentTierRange string

// List of tools_wechat_applet_create_v3.0_min_payment_tier_range
const (
	SIX_TEN_ToolsWechatAppletCreateV30MinPaymentTierRange      ToolsWechatAppletCreateV30MinPaymentTierRange = "SIX_TEN"
	TEN_TWENTY_ToolsWechatAppletCreateV30MinPaymentTierRange   ToolsWechatAppletCreateV30MinPaymentTierRange = "TEN_TWENTY"
	TWENTY_FIFTY_ToolsWechatAppletCreateV30MinPaymentTierRange ToolsWechatAppletCreateV30MinPaymentTierRange = "TWENTY_FIFTY"
	ZERO_FIVE_ToolsWechatAppletCreateV30MinPaymentTierRange    ToolsWechatAppletCreateV30MinPaymentTierRange = "ZERO_FIVE"
)

// Ptr returns reference to tools_wechat_applet_create_v3.0_min_payment_tier_range value
func (v ToolsWechatAppletCreateV30MinPaymentTierRange) Ptr() *ToolsWechatAppletCreateV30MinPaymentTierRange {
	return &v
}
