// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-11 13:50:44
// 生成路径: internal/app/ad/dao/internal/ad_common_asset_package.go
// 生成人：cq
// desc:通用资产-标题包
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdCommonAssetPackageDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdCommonAssetPackageDao struct {
	table   string                      // Table is the underlying table name of the DAO.
	group   string                      // Group is the database configuration group name of current DAO.
	columns AdCommonAssetPackageColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdCommonAssetPackageColumns defines and stores column names for table ad_common_asset_package.
type AdCommonAssetPackageColumns struct {
	Id                string //
	PackageName       string // 标题包名称
	TitleIds          string // 标题ID列表，以|分隔
	UserId            string // 创建者
	Last3DayClickRate string // 近3日点击率
	Last3DayCost      string // 近3日消耗
	HistoryClickRate  string // 历史点击率
	HistoryCost       string // 历史消耗
	AdCount           string // 关联广告数
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 删除时间
}

var adCommonAssetPackageColumns = AdCommonAssetPackageColumns{
	Id:                "id",
	PackageName:       "package_name",
	TitleIds:          "title_ids",
	UserId:            "user_id",
	Last3DayClickRate: "last_3_day_click_rate",
	Last3DayCost:      "last_3_day_cost",
	HistoryClickRate:  "history_click_rate",
	HistoryCost:       "history_cost",
	AdCount:           "ad_count",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
}

// NewAdCommonAssetPackageDao creates and returns a new DAO object for table data access.
func NewAdCommonAssetPackageDao() *AdCommonAssetPackageDao {
	return &AdCommonAssetPackageDao{
		group:   "default",
		table:   "ad_common_asset_package",
		columns: adCommonAssetPackageColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdCommonAssetPackageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdCommonAssetPackageDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdCommonAssetPackageDao) Columns() AdCommonAssetPackageColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdCommonAssetPackageDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdCommonAssetPackageDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdCommonAssetPackageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
