// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-09-02 14:44:05
// 生成路径: api/v1/applet/sys_active_pop_up_window.go
// 生成人：cyao
// desc:活动弹出窗口相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SysActivePopUpWindowSearchReq 分页请求参数
type SysActivePopUpWindowSearchReq struct {
	g.Meta `path:"/list" tags:"活动弹出窗口" method:"get" summary:"活动弹出窗口列表"`
	commonApi.Author
	model.SysActivePopUpWindowSearchReq
}

// SysActivePopUpWindowSearchRes 列表返回结果
type SysActivePopUpWindowSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SysActivePopUpWindowSearchRes
}

// SysActivePopUpWindowAddReq 添加操作请求参数
type SysActivePopUpWindowAddReq struct {
	g.Meta `path:"/add" tags:"活动弹出窗口" method:"post" summary:"活动弹出窗口添加"`
	commonApi.Author
	*model.SysActivePopUpWindowAddReq
}

// SysActivePopUpWindowAddRes 添加操作返回结果
type SysActivePopUpWindowAddRes struct {
	commonApi.EmptyRes
}

// SysActivePopUpWindowEditReq 修改操作请求参数
type SysActivePopUpWindowEditReq struct {
	g.Meta `path:"/edit" tags:"活动弹出窗口" method:"put" summary:"活动弹出窗口修改"`
	commonApi.Author
	*model.SysActivePopUpWindowEditReq
}

// SysActivePopUpWindowEditRes 修改操作返回结果
type SysActivePopUpWindowEditRes struct {
	commonApi.EmptyRes
}

// SysActivePopUpWindowGetReq 获取一条数据请求
type SysActivePopUpWindowGetReq struct {
	g.Meta `path:"/get" tags:"活动弹出窗口" method:"get" summary:"获取活动弹出窗口信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SysActivePopUpWindowGetRes 获取一条数据结果
type SysActivePopUpWindowGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SysActivePopUpWindowInfoRes
}

// SysActivePopUpWindowDeleteReq 删除数据请求
type SysActivePopUpWindowDeleteReq struct {
	g.Meta `path:"/delete" tags:"活动弹出窗口" method:"delete" summary:"删除活动弹出窗口"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SysActivePopUpWindowDeleteRes 删除数据返回
type SysActivePopUpWindowDeleteRes struct {
	commonApi.EmptyRes
}
