// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: internal/app/ad/controller/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type ksAccountSeriesController struct {
	systemController.BaseController
}

var KsAccountSeries = new(ksAccountSeriesController)

// PullKsAccountSeries 拉取短剧
func (c *ksAccountSeriesController) PullKsAccountSeries(ctx context.Context, req *ad.PullKsAccountSeriesReq) (res *ad.PullKsAccountSeriesRes, err error) {
	res = new(ad.PullKsAccountSeriesRes)
	res.List, err = service.KsAccountSeries().GetKsAdAccountSeriesList(ctx, "", req.AdvertiserId, req.InDb)
	return
}

// List 列表
func (c *ksAccountSeriesController) List(ctx context.Context, req *ad.KsAccountSeriesSearchReq) (res *ad.KsAccountSeriesSearchRes, err error) {
	res = new(ad.KsAccountSeriesSearchRes)
	res.KsAccountSeriesSearchRes, err = service.KsAccountSeries().List(ctx, &req.KsAccountSeriesSearchReq)
	return
}

// Export 导出excel
func (c *ksAccountSeriesController) Export(ctx context.Context, req *ad.KsAccountSeriesExportReq) (res *ad.KsAccountSeriesExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.KsAccountSeriesInfoRes
		//表头
		tableHead = []interface{}{"用户快手号id/广告主id", "短剧id", "短剧名称"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.KsAccountSeries().GetExportData(ctx, &req.KsAccountSeriesSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.AdvertiserId,
				v.SeriesId,
				v.SeriesName,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("短剧信息列表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Add 添加短剧信息列表
func (c *ksAccountSeriesController) Add(ctx context.Context, req *ad.KsAccountSeriesAddReq) (res *ad.KsAccountSeriesAddRes, err error) {
	err = service.KsAccountSeries().Add(ctx, req.KsAccountSeriesAddReq)
	return
}

// Edit 修改短剧信息列表
func (c *ksAccountSeriesController) Edit(ctx context.Context, req *ad.KsAccountSeriesEditReq) (res *ad.KsAccountSeriesEditRes, err error) {
	err = service.KsAccountSeries().Edit(ctx, req.KsAccountSeriesEditReq)
	return
}

// Delete 删除短剧信息列表
func (c *ksAccountSeriesController) Delete(ctx context.Context, req *ad.KsAccountSeriesDeleteReq) (res *ad.KsAccountSeriesDeleteRes, err error) {
	err = service.KsAccountSeries().Delete(ctx, req.AdvertiserIds)
	return
}
