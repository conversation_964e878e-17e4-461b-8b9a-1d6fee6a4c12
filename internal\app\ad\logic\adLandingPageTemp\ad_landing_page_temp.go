// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: internal/app/ad/logic/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	oceanengineModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdLandingPageTemp(New())
}

func New() service.IAdLandingPageTemp {
	return &sAdLandingPageTemp{}
}

type sAdLandingPageTemp struct{}

func (s *sAdLandingPageTemp) List(ctx context.Context, req *model.AdLandingPageTempSearchReq) (listRes *model.AdLandingPageTempSearchRes, err error) {
	listRes = new(model.AdLandingPageTempSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdLandingPageTemp.Ctx(ctx).WithAll()
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdLandingPageTemp.Columns().MainUserId, userIds)
		}
		if req.Id != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().Id+" = ?", req.Id)
		}
		if len(req.Ids) > 0 {
			m = m.WhereIn(dao.AdLandingPageTemp.Columns().Id, req.Ids)
		}
		if req.MainUserId != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().MainUserId+" = ?", gconv.Int(req.MainUserId))
		}
		if len(req.UserIds) > 0 {
			m = m.WhereIn(dao.AdLandingPageTemp.Columns().MainUserId, req.UserIds)
		}
		if req.TempleName != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().TempleName+" like ?", "%"+req.TempleName+"%")
		}
		if len(req.TempleNames) > 0 {
			m = m.WhereIn(dao.AdLandingPageTemp.Columns().TempleName, req.TempleNames)
		}
		if req.TempleType != nil {
			m = m.Where(dao.AdLandingPageTemp.Columns().TempleType, *req.TempleType)
		}
		if req.AdNum != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().AdNum+" = ?", gconv.Int(req.AdNum))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdLandingPageTempListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdLandingPageTempListRes, len(res))
		aidList := make([]uint64, 0)
		for k, v := range res {
			aidList = append(aidList, gconv.Uint64(v.MainUserId))
			listRes.List[k] = &model.AdLandingPageTempListRes{
				Id:         v.Id,
				MainUserId: v.MainUserId,
				TempleName: v.TempleName,
				TempleType: v.TempleType,
				Bricks:     v.Bricks,
				AdNum:      v.AdNum,
				CreateTime: v.CreateTime,
				UpdateTime: v.UpdateTime,
			}
		}
		userList, _ := sysService.SysUser().GetUserByIds(ctx, aidList)
		for _, item := range listRes.List {
			for _, userInfo := range userList {
				if item.MainUserId == userInfo.Id {
					item.UserName = userInfo.UserName
				}
			}
		}
	})
	return
}

func (s *sAdLandingPageTemp) GetExportData(ctx context.Context, req *model.AdLandingPageTempSearchReq) (listRes []*model.AdLandingPageTempInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdLandingPageTemp.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().Id+" = ?", req.Id)
		}
		if len(req.Ids) > 0 {
			m = m.WhereIn(dao.AdLandingPageTemp.Columns().Id, req.Ids)
		}
		if len(req.TempleNames) > 0 {
			m = m.WhereIn(dao.AdLandingPageTemp.Columns().TempleName, req.TempleNames)
		}
		if req.MainUserId != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().MainUserId+" = ?", gconv.Int(req.MainUserId))
		}
		if req.TempleName != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().TempleName+" like ?", "%"+req.TempleName+"%")
		}
		if req.TempleType != nil {
			m = m.Where(dao.AdLandingPageTemp.Columns().TempleType, *req.TempleType)
		}
		if req.AdNum != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().AdNum+" = ?", gconv.Int(req.AdNum))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.AdLandingPageTemp.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdLandingPageTemp) GetById(ctx context.Context, id int64) (res *model.AdLandingPageTempInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdLandingPageTemp.Ctx(ctx).WithAll().Where(dao.AdLandingPageTemp.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetByIds
func (s *sAdLandingPageTemp) GetByIds(ctx context.Context, ids []int64) (res []*model.AdLandingPageTempInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdLandingPageTemp.Ctx(ctx).WithAll().WhereIn(dao.AdLandingPageTemp.Columns().Id, ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// CreateByLandingPageTempCreate
func (s *sAdLandingPageTemp) CreateByLandingPageTempCreate(ctx context.Context, req []*oceanengineModel.LandingPageTempCreate) (res []*oceanengineModel.LandingPageTempCreate, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		ids := make([]int64, len(req))
		for _, item := range req {
			ids = append(ids, item.TemplateID)
		}
		list, innerError := s.GetByIds(ctx, ids)
		liberr.ErrIsNil(ctx, innerError, "获取信息失败")
		for _, item := range req {
			for _, infoRes := range list {
				if item.TemplateID == infoRes.Id {
					bricks := make([]*toutiaoModels.ToolsSiteCreateV2RequestBricksInner, 0)
					// 将   infoRes.Bricks 序列化成 bricks
					err = json.Unmarshal([]byte(infoRes.Bricks), &bricks)
					if err != nil {
						liberr.ErrIsNil(ctx, err, "json.Unmarshal err:"+err.Error())
					}
					for _, brick := range bricks {
						if brick.Name == "XrWechatApplet" || brick.Name == "XrWechatGame" {
							brick.InstanceId = gconv.Int64(item.InstanceId)
							if brick.Background != nil {
								brick.Background.Image = libUtils.EncodeURL(brick.Background.Image)
							}
							if brick.Setting != nil && brick.Setting.Image != nil {
								brick.Setting.Image.ImageUrl = libUtils.EncodeURL(brick.Setting.Image.ImageUrl)
							}
							if brick.Setting != nil && brick.Setting.Avatar != nil {
								brick.Setting.Avatar.ImageUrl = libUtils.EncodeURL(brick.Setting.Avatar.ImageUrl)
							}
							if brick.Setting != nil && brick.Setting.Background != nil && brick.Setting.Background.Image != nil {
								brick.Setting.Background.Image.ImageUrl = libUtils.EncodeURL(brick.Setting.Background.Image.ImageUrl)
							}

						}
						brick.ImageUrl = libUtils.EncodeURL(brick.ImageUrl)
					}
					//json.Unmarshal(infoRes.Bricks, &bricks)
					//err1 := gjson.DecodeTo(infoRes.Bricks, &bricks)
					tokenRes, err1 := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, item.AdvertiserID)
					liberr.ErrIsNil(ctx, err1, "获取access_token失败")
					advertiserIdInt64 := gconv.Int64(item.AdvertiserID)
					createRes, err2 := advertiser.GetToutiaoApiClient().ToolsSiteCreateV2ApiService.AccessToken(tokenRes.AccessToken).
						SetRequest(toutiaoModels.ToolsSiteCreateV2Request{
							AdvertiserId: advertiserIdInt64,
							Bricks:       bricks,
							Name:         infoRes.TempleName,
							//SiteType:     nil, 默认
						}).Do()
					liberr.ErrIsNil(ctx, err2)
					item.SiteID = createRes.Data.SiteId
					// 发布站点
					_, err = advertiser.GetToutiaoApiClient().ToolsSiteUpdateStatusV2ApiService.AccessToken(tokenRes.AccessToken).SetRequest(toutiaoModels.ToolsSiteUpdateStatusV2Request{
						AdvertiserId: advertiserIdInt64,
						SiteIds:      []string{createRes.Data.SiteId},
						Status:       "published",
					}).Do()
					liberr.ErrIsNil(ctx, err)

				}
			}
		}
		res = req
	})
	return
}

func (s *sAdLandingPageTemp) Add(ctx context.Context, req *model.AdLandingPageTempAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		loginUserId := sysService.Context().GetUserId(ctx)
		_, err = dao.AdLandingPageTemp.Ctx(ctx).Insert(do.AdLandingPageTemp{
			MainUserId: loginUserId,
			TempleName: req.TempleName,
			TempleType: req.TempleType,
			Bricks:     req.Bricks,
			AdNum:      req.AdNum,
			CreateTime: gtime.Now(),
			UpdateTime: gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdLandingPageTemp) Edit(ctx context.Context, req *model.AdLandingPageTempEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		adLandingPageTemp := do.AdLandingPageTemp{
			UpdateTime: gtime.Now(),
		}
		if req.TempleName != "" {
			adLandingPageTemp.TempleName = req.TempleName
		}
		if req.TempleType >= 0 {
			adLandingPageTemp.TempleType = req.TempleType
		}
		if req.Bricks != "" {
			adLandingPageTemp.Bricks = req.Bricks
		}
		if req.AdNum >= 0 {
			adLandingPageTemp.AdNum = req.AdNum
		}
		_, err = dao.AdLandingPageTemp.Ctx(ctx).WherePri(req.Id).Update(adLandingPageTemp)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdLandingPageTemp) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdLandingPageTemp.Ctx(ctx).Delete(dao.AdLandingPageTemp.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
