/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AccountFundGetV3ApiService AccountFundGetV3Api service
type AccountFundGetV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *model.AccountFundGetV3Request
}

func (r *AccountFundGetV3ApiService) SetCfg(cfg *conf.Configuration) *AccountFundGetV3ApiService {
	r.cfg = cfg
	return r
}

func (r *AccountFundGetV3ApiService) AdvertiserInfoV2Request(accountFundGetV3Request model.AccountFundGetV3Request) *AccountFundGetV3ApiService {
	r.Request = &accountFundGetV3Request
	return r
}

func (r *AccountFundGetV3ApiService) AccessToken(accessToken string) *AccountFundGetV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AccountFundGetV3ApiService) Do() (data *model.AccountFundGetV3Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/account/fund/get/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "account_ids", r.Request.AccountIds)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "account_type", r.Request.AccountType)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&model.AccountFundGetV3Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(model.AccountFundGetV3Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/account/fund/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
