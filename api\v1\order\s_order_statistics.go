// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-23 14:34:37
// 生成路径: api/v1/system/s_order_statistics.go
// 生成人：gfast
// desc:订单统计表格相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
)

// SOrderStatisticsSearchReq 分页请求参数
type SOrderStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"订单统计表格" method:"get" summary:"订单统计表格列表"`
	commonApi.Author
	model.SOrderStatisticsSearchReq
}

// SOrderStatisticsSearchRes 列表返回结果
type SOrderStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SOrderStatisticsSearchRes
}

// SOrderStatisticsExportReq 导出请求
type SOrderStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"订单统计表格" method:"get" summary:"订单统计表格导出"`
	commonApi.Author
	model.SOrderStatisticsSearchReq
}

// SOrderStatisticsExportRes 导出响应
type SOrderStatisticsExportRes struct {
	commonApi.EmptyRes
}

// SUserStatisticsSearchReq 分页请求参数
type SUserStatisticsSearchReq struct {
	g.Meta `path:"/user/list" tags:"用户统计表格" method:"get" summary:"用户统计表格列表"`
	commonApi.Author
	model.SUserStatisticsSearchReq
}

// SUserStatisticsSearchRes 列表返回结果
type SUserStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SUserStatisticsSearchRes
}

// SUserStatisticsExportReq 导出请求
type SUserStatisticsExportReq struct {
	g.Meta `path:"/user/export" tags:"用户统计表格" method:"get" summary:"用户统计表格导出"`
	commonApi.Author
	model.SUserStatisticsSearchReq
}

// SUserStatisticsExportRes 导出响应
type SUserStatisticsExportRes struct {
	commonApi.EmptyRes
}

// IndexOrderStaticsSearchReq 个人中心首页订单统计
type IndexOrderStaticsSearchReq struct {
	g.Meta `path:"/indexOrderStatics" tags:"个人中心" method:"post" summary:"订单充值统计"`
	commonApi.Author
	*model.IndexOrderStaticsSearchReq
}

// IndexOrderStatisticsSearchRes 列表返回结果
type IndexOrderStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.IndexOrderStatisticsSearchRes
}
