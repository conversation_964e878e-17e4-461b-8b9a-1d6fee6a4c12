// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-14 16:10:56
// 生成路径: api/v1/channel/m_member_ad_callback_wx_pitcher_statistics.go
// 生成人：cq
// desc:投手微小广告统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdCallbackWxPitcherStatisticsSearchReq 分页请求参数
type MMemberAdCallbackWxPitcherStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"投手微小广告统计" method:"post" summary:"投手微小广告统计列表"`
	commonApi.Author
	model.MMemberAdCallbackWxPitcherStatisticsSearchReq
}

// MMemberAdCallbackWxPitcherStatisticsSearchRes 列表返回结果
type MMemberAdCallbackWxPitcherStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackWxPitcherStatisticsSearchRes
}

// MMemberAdCallbackWxPitcherStatisticsExportReq 导出请求
type MMemberAdCallbackWxPitcherStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"投手微小广告统计" method:"post" summary:"投手微小广告统计导出"`
	commonApi.Author
	model.MMemberAdCallbackWxPitcherStatisticsSearchReq
}

// MMemberAdCallbackWxPitcherStatisticsExportRes 导出响应
type MMemberAdCallbackWxPitcherStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackWxPitcherStatisticsAddReq 添加操作请求参数
type MMemberAdCallbackWxPitcherStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"投手微小广告统计" method:"post" summary:"投手微小广告统计添加"`
	commonApi.Author
	*model.MMemberAdCallbackWxPitcherStatisticsAddReq
}

// MMemberAdCallbackWxPitcherStatisticsAddRes 添加操作返回结果
type MMemberAdCallbackWxPitcherStatisticsAddRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackWxPitcherStatisticsEditReq 修改操作请求参数
type MMemberAdCallbackWxPitcherStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"投手微小广告统计" method:"put" summary:"投手微小广告统计修改"`
	commonApi.Author
	*model.MMemberAdCallbackWxPitcherStatisticsEditReq
}

// MMemberAdCallbackWxPitcherStatisticsEditRes 修改操作返回结果
type MMemberAdCallbackWxPitcherStatisticsEditRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackWxPitcherStatisticsGetReq 获取一条数据请求
type MMemberAdCallbackWxPitcherStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"投手微小广告统计" method:"get" summary:"获取投手微小广告统计信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberAdCallbackWxPitcherStatisticsGetRes 获取一条数据结果
type MMemberAdCallbackWxPitcherStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackWxPitcherStatisticsInfoRes
}

// MMemberAdCallbackWxPitcherStatisticsDeleteReq 删除数据请求
type MMemberAdCallbackWxPitcherStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"投手微小广告统计" method:"post" summary:"删除投手微小广告统计"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MMemberAdCallbackWxPitcherStatisticsDeleteRes 删除数据返回
type MMemberAdCallbackWxPitcherStatisticsDeleteRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackWxPitcherStatisticsTaskReq 小程序广告回传统计请求参数
type MMemberAdCallbackWxPitcherStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"小程序广告统计" method:"post" summary:"投手微小广告统计任务"`
	commonApi.Author
	model.MMemberAdCallbackWxPitcherStatisticsSearchReq
}

// MMemberAdCallbackWxPitcherStatisticsTaskRes 小程序广告回传统计返回结果
type MMemberAdCallbackWxPitcherStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}
