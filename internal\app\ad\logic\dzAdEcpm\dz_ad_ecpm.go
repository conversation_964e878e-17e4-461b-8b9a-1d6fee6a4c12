// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/logic/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	channelEntity "github.com/tiger1103/gfast/v3/internal/app/channel/model/entity"
	orderModel "github.com/tiger1103/gfast/v3/internal/app/order/model"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzAdEcpm(New())
}

func New() service.IDzAdEcpm {
	return &sDzAdEcpm{}
}

type sDzAdEcpm struct{}

func (s *sDzAdEcpm) List(ctx context.Context, req *model.DzAdEcpmSearchReq) (listRes *model.DzAdEcpmSearchRes, err error) {
	listRes = new(model.DzAdEcpmSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdEcpm.Ctx(ctx).WithAll().As("dzdata")
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin {
			m = m.LeftJoin(dao.DzAdAccountDepts.Table(), "d", " dzdata.channel_id = d.channel_id").
				LeftJoin(dao.DzAdAccountUsers.Table(), "user", " dzdata.channel_id = user.channel_id").
				Where("d.detp_id = ? or user.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if len(req.AccountIds) > 0 {
			list, _ := service.DzAdAccountChannel().GetByAccountIds(ctx, gconv.SliceInt64(req.AccountIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("dzdata."+dao.DzAdEcpm.Columns().ChannelId, ids)
				}
			}

		}

		if len(req.ChannelIds) > 0 {
			m = m.WhereIn("dzdata."+dao.DzAdEcpm.Columns().ChannelId, req.ChannelIds)
		}

		if req.StartTime != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().EventTime+" >= ?", libUtils.GetSecByDateTime(req.StartTime))
		}
		if req.EndTime != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().EventTime+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.EndTime, 1)))
		}
		m = m.InnerJoin(dao.DzAdUserInfo.Table(), "u", "u."+dao.DzAdUserInfo.Columns().UserId+" = dzdata.user_id ")
		// 用户注册时间
		if req.RegisterStartTime != "" {
			m = m.Where("u."+dao.DzAdUserInfo.Columns().UserId+" >= ?", libUtils.GetSecByDateTime(req.RegisterStartTime))
			if req.RegisterEndTime != "" {
				m = m.Where("u."+dao.DzAdUserInfo.Columns().UserId+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.RegisterEndTime, 1)))
			}
		}

		if req.DyeStartTime != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().DyeTime+" >= ?", libUtils.GetSecByDateTime(req.DyeStartTime))
		}
		if req.DyeEndTime != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().DyeTime+" < ?", libUtils.GetSecByDateTime(libUtils.StringTimeAddDay(req.DyeEndTime, 1)))
		}

		if req.PitcherId > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().DzReferralId+" = dzdata.promotion_id ").InnerJoin(systemDao.SysUser.Table(), "su", fmt.Sprintf("su.id = sc.user_id and su.id = %v", req.PitcherId))
		} else {
			m = m.LeftJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().DzReferralId+" = dzdata.promotion_id ").LeftJoin(systemDao.SysUser.Table(), "su", "su.id = sc.user_id ")
		}

		if req.DistributorId > 0 {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
		} else {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
		}

		if len(req.DeptIds) > 0 {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id")
			m = m.Where(fmt.Sprintf("de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds)))
		}

		if req.Id != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().Id+" = ?", req.Id)
		}
		if req.Time != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().Time+" = ?", gconv.Int64(req.Time))
		}
		if req.UserId != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().UserId+" = ?", req.UserId)
		}
		if req.ChannelId != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.AppId != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().AppId+" = ?", req.AppId)
		}
		if req.PromotionId != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OpenId != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.EcpmId != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().EcpmId+" = ?", req.EcpmId)
		}
		if req.EcpmCost != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().EcpmCost+" = ?", req.EcpmCost)
		}
		if req.AdType != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().AdType+" = ?", req.AdType)
		}
		if req.EventTime != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().EventTime+" = ?", gconv.Int64(req.EventTime))
		}
		if req.DyeTime != "" {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().DyeTime+" = ?", gconv.Int64(req.DyeTime))
		}
		if len(req.DateRange) != 0 {
			m = m.Where("dzdata."+dao.DzAdEcpm.Columns().CreatedAt+" >=? AND "+"dzdata."+dao.DzAdEcpm.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		var summary = new(model.DzAdEcpmSummary)

		err = m.FieldSum("dzdata."+dao.DzAdEcpm.Columns().EcpmCost, "sumCost").Scan(&summary)
		summary.SumCost = summary.SumCost / 100000
		listRes.Summary = summary
		listRes.Total, err = m.Group("dzdata.create_date ,dzdata.user_id ,dzdata.ad_type ").Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "dzdata.create_date desc"
		if req.OrderBy != "" {
			//order = req.OrderBy
			order = "dzdata." + libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
		}
		var res []*model.DzAdEcpmListRes
		err = m.Fields("dzdata.create_date as createDate ,dzdata.user_id as userId,ANY_VALUE(dzdata.promotion_id) as promotionId ,dzdata.ad_type as adType,ANY_VALUE(dzdata.channel_id) as channelId,FROM_UNIXTIME(ANY_VALUE(u.register_time)/1000, '%Y-%m-%d %H:%i:%s') as registerTime,FROM_UNIXTIME(ANY_VALUE(dzdata.dye_time), '%Y-%m-%d %H:%i:%s') as dyeTime,SUM(ecpm_cost) as sumCost ,ANY_VALUE(su.user_name)  as userName ,ANY_VALUE(de.leader)  as distributorName , ANY_VALUE(channel_code) as channelCode").Group("dzdata.create_date ,dzdata.user_id ,dzdata.ad_type ").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdEcpmListRes, len(res))
		cIds := make([]int64, 0)
		for k, v := range res {
			cIds = append(cIds, gconv.Int64(v.ChannelId))
			listRes.List[k] = &model.DzAdEcpmListRes{
				CreateDate:      v.CreateDate,
				DistributorName: v.DistributorName,
				UserName:        v.UserName,
				ChannelCode:     v.ChannelCode,
				RegisterTime:    v.RegisterTime,
				DyeTime:         v.DyeTime,
				UserId:          v.UserId,
				PromotionId:     v.PromotionId,
				ChannelId:       v.ChannelId,
				AdType:          v.AdType,
				SumCost:         v.SumCost / 100000,

				//AppId:           v.AppId,
				//OpenId:          v.OpenId,
				//EcpmId:          v.EcpmId,
				//EcpmCost:        v.EcpmCost,
				//Id:              v.Id,
				//Time:            v.Time,
				//EventTime:       v.EventTime,
				//CreatedAt:       v.CreatedAt,
			}
		}

		var accountInfo = make([]model.DzAdAccountInfoRes, 0)
		channelList, _ := service.DzAdAccountChannel().GetByChannelIds(ctx, cIds)
		accountIds := make([]int64, 0)
		for _, item := range channelList {
			accountIds = append(accountIds, item.AccountId)
		}
		err = dao.DzAdAccount.Ctx(ctx).WhereIn("account_id", accountIds).Scan(&accountInfo)

		for _, item := range listRes.List {
			for _, channelInfo := range channelList {
				if gconv.Int64(item.ChannelId) == channelInfo.ChannelId {
					item.DzChannel = channelInfo.NickName
					for _, accountInfoRes := range accountInfo {
						if gconv.String(channelInfo.AccountId) == accountInfoRes.AccountId {
							item.DzAccount = accountInfoRes.AccountName
							break
						}
					}
					break
				}
			}
		}
	})
	return
}

func (s *sDzAdEcpm) GetExportData(ctx context.Context, req *model.DzAdEcpmSearchReq) (listRes []*model.DzAdEcpmInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdEcpm.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.DzAdEcpm.Columns().Id+" = ?", req.Id)
		}
		if req.Time != "" {
			m = m.Where(dao.DzAdEcpm.Columns().Time+" = ?", gconv.Int64(req.Time))
		}
		if req.UserId != "" {
			m = m.Where(dao.DzAdEcpm.Columns().UserId+" = ?", req.UserId)
		}
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdEcpm.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.AppId != "" {
			m = m.Where(dao.DzAdEcpm.Columns().AppId+" = ?", req.AppId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.DzAdEcpm.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OpenId != "" {
			m = m.Where(dao.DzAdEcpm.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.EcpmId != "" {
			m = m.Where(dao.DzAdEcpm.Columns().EcpmId+" = ?", req.EcpmId)
		}
		if req.EcpmCost != "" {
			m = m.Where(dao.DzAdEcpm.Columns().EcpmCost+" = ?", req.EcpmCost)
		}
		if req.AdType != "" {
			m = m.Where(dao.DzAdEcpm.Columns().AdType+" = ?", req.AdType)
		}
		if req.EventTime != "" {
			m = m.Where(dao.DzAdEcpm.Columns().EventTime+" = ?", gconv.Int64(req.EventTime))
		}
		if req.DyeTime != "" {
			m = m.Where(dao.DzAdEcpm.Columns().DyeTime+" = ?", gconv.Int64(req.DyeTime))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.DzAdEcpm.Columns().CreatedAt+" >=? AND "+dao.DzAdEcpm.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sDzAdEcpm) GetById(ctx context.Context, id uint64) (res *model.DzAdEcpmInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdEcpm.Ctx(ctx).WithAll().Where(dao.DzAdEcpm.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdEcpm) Add(ctx context.Context, req *model.DzAdEcpmAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdEcpm.Ctx(ctx).Insert(do.DzAdEcpm{
			Time:        req.Time,
			UserId:      req.UserId,
			ChannelId:   req.ChannelId,
			AppId:       req.AppId,
			PromotionId: req.PromotionId,
			OpenId:      req.OpenId,
			EcpmId:      req.EcpmId,
			EcpmCost:    req.EcpmCost,
			AdType:      req.AdType,
			EventTime:   req.EventTime,
			DyeTime:     req.DyeTime,
			CreateDate:  req.CreateDate,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzAdEcpm) Edit(ctx context.Context, req *model.DzAdEcpmEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdEcpm.Ctx(ctx).WherePri(req.Id).Update(do.DzAdEcpm{
			Time:        req.Time,
			UserId:      req.UserId,
			ChannelId:   req.ChannelId,
			AppId:       req.AppId,
			PromotionId: req.PromotionId,
			OpenId:      req.OpenId,
			EcpmId:      req.EcpmId,
			EcpmCost:    req.EcpmCost,
			AdType:      req.AdType,
			EventTime:   req.EventTime,
			DyeTime:     req.DyeTime,
			CreateDate:  libUtils.GetDateBySec(req.EventTime),
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzAdEcpm) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdEcpm.Ctx(ctx).Delete(dao.DzAdEcpm.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sDzAdEcpm) CalcDzEcpmCostStat(ctx context.Context, statDate string) (ecpmCostStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdEcpmAnalytic.Ctx(ctx).As("dz").
			InnerJoin("s_channel s", "s.dz_referral_id = dz.promotion_id").
			Fields("IFNULL(sum(dz.ecpm_cost/100000), 0.00) as totalAdUp").
			Fields("s.channel_code as account").
			WhereGTE("dz."+dao.DzAdEcpm.Columns().EventTime, startTime).
			WhereLTE("dz."+dao.DzAdEcpm.Columns().EventTime, endTime).
			Group("s.channel_code").
			Scan(&ecpmCostStat)
	})
	return
}

func (s *sDzAdEcpm) CalcDzNewUserEcpmCostStat(ctx context.Context, statDate string) (newUserEcpmCostStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdEcpmAnalytic.Ctx(ctx).As("dz").
			InnerJoin("s_channel s", "s.dz_referral_id = dz.promotion_id").
			Fields("IFNULL(sum(dz.ecpm_cost/100000), 0.00) as newUserAdUp").
			Fields("s.channel_code as account").
			WhereGTE("dz."+dao.DzAdEcpm.Columns().EventTime, startTime).
			WhereLTE("dz."+dao.DzAdEcpm.Columns().EventTime, endTime).
			WhereGTE("dz."+dao.DzAdEcpm.Columns().DyeTime, startTime).
			WhereLTE("dz."+dao.DzAdEcpm.Columns().DyeTime, endTime).
			Group("s.channel_code").
			Scan(&newUserEcpmCostStat)
	})
	return
}

func (s *sDzAdEcpm) CalcDzDistributorEcpmCostStat(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdEcpmAnalytic.Ctx(ctx).
			Fields("IFNULL(sum(ecpm_cost/100000), 0.00) as dayAdUp").
			Fields("channel_id as dzChannelId").
			WhereGTE(dao.DzAdEcpm.Columns().EventTime, startTime).
			WhereLTE(dao.DzAdEcpm.Columns().EventTime, endTime).
			Group("channel_id").
			Scan(&res)
	})
	return
}

func (s *sDzAdEcpm) CalcDzDistributorNewUserEcpmCostStat(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps3(statDate)
		err = dao.DzAdEcpmAnalytic.Ctx(ctx).
			Fields("IFNULL(sum(ecpm_cost/100000), 0.00) as dayNewAdUp").
			Fields("channel_id as dzChannelId").
			WhereGTE(dao.DzAdEcpm.Columns().EventTime, startTime).
			WhereLTE(dao.DzAdEcpm.Columns().EventTime, endTime).
			WhereGTE(dao.DzAdEcpm.Columns().DyeTime, startTime).
			WhereLTE(dao.DzAdEcpm.Columns().DyeTime, endTime).
			Group("channel_id").
			Scan(&res)
	})
	return
}
