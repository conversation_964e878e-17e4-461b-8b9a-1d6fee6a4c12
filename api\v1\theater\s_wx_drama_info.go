// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-27 14:58:26
// 生成路径: api/v1/theater/s_wx_drama_info.go
// 生成人：cq
// desc:微信剧名信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SWxDramaInfoSearchReq 分页请求参数
type SWxDramaInfoSearchReq struct {
	g.Meta `path:"/list" tags:"微信剧名信息" method:"get" summary:"微信剧名信息列表"`
	commonApi.Author
	model.SWxDramaInfoSearchReq
}

// SWxDramaInfoSearchRes 列表返回结果
type SWxDramaInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SWxDramaInfoSearchRes
}

// SWxDramaInfoEditReq 修改操作请求参数
type SWxDramaInfoEditReq struct {
	g.Meta `path:"/edit" tags:"微信剧名信息" method:"put" summary:"微信剧名信息修改"`
	commonApi.Author
	*model.SWxDramaInfoEditReq
}

// SWxDramaInfoEditRes 修改操作返回结果
type SWxDramaInfoEditRes struct {
	commonApi.EmptyRes
}

// SWxDramaInfoGetReq 获取一条数据请求
type SWxDramaInfoGetReq struct {
	g.Meta `path:"/get" tags:"微信剧名信息" method:"get" summary:"获取微信剧名信息信息"`
	commonApi.Author
	DramaId string `p:"dramaId" v:"required#剧名ID必须"`
}

// SWxDramaInfoGetRes 获取一条数据结果
type SWxDramaInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SWxDramaInfoInfoRes
}

// SWxMediaUploadReq 新增临时素材请求参数
type SWxMediaUploadReq struct {
	g.Meta `path:"/media/upload" tags:"微信剧名信息" method:"post" summary:"新增临时素材"`
	commonApi.Author
	*model.SWxMediaUploadReq
}

// SWxMediaUploadRes 修改操作返回结果
type SWxMediaUploadRes struct {
	commonApi.EmptyRes
	*model.SWxMediaUploadRes
}
