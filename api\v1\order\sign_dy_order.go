// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-28 17:21:05
// 生成路径: api/v1/order/sign_dy_order.go
// 生成人：cq
// desc:抖音签约订单相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
)

// SignDyOrderSearchReq 分页请求参数
type SignDyOrderSearchReq struct {
	g.Meta `path:"/list" tags:"抖音签约订单" method:"get" summary:"抖音签约订单列表"`
	commonApi.Author
	model.SignDyOrderSearchReq
}

// SignDyOrderSearchRes 列表返回结果
type SignDyOrderSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SignDyOrderSearchRes
}

// SignDyOrderAddReq 添加操作请求参数
type SignDyOrderAddReq struct {
	g.Meta `path:"/add" tags:"抖音签约订单" method:"post" summary:"抖音签约订单添加"`
	commonApi.Author
	*model.SignDyOrderAddReq
}

// SignDyOrderAddRes 添加操作返回结果
type SignDyOrderAddRes struct {
	commonApi.EmptyRes
}

// SignDyOrderEditReq 修改操作请求参数
type SignDyOrderEditReq struct {
	g.Meta `path:"/edit" tags:"抖音签约订单" method:"put" summary:"抖音签约订单修改"`
	commonApi.Author
	*model.SignDyOrderEditReq
}

// SignDyOrderEditRes 修改操作返回结果
type SignDyOrderEditRes struct {
	commonApi.EmptyRes
}

// SignDyOrderGetReq 获取一条数据请求
type SignDyOrderGetReq struct {
	g.Meta `path:"/get" tags:"抖音签约订单" method:"get" summary:"获取抖音签约订单信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SignDyOrderGetRes 获取一条数据结果
type SignDyOrderGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SignDyOrderInfoRes
}

// SignDyOrderDeleteReq 删除数据请求
type SignDyOrderDeleteReq struct {
	g.Meta `path:"/delete" tags:"抖音签约订单" method:"delete" summary:"删除抖音签约订单"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SignDyOrderDeleteRes 删除数据返回
type SignDyOrderDeleteRes struct {
	commonApi.EmptyRes
}
