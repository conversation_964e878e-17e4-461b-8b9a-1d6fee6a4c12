// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-28 17:21:21
// 生成路径: api/v1/order/sign_dy_pay.go
// 生成人：cq
// desc:抖音代扣订单相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
)

// SignDyPaySearchReq 分页请求参数
type SignDyPaySearchReq struct {
	g.Meta `path:"/list" tags:"抖音代扣订单" method:"get" summary:"抖音代扣订单列表"`
	commonApi.Author
	model.SignDyPaySearchReq
}

// SignDyPaySearchRes 列表返回结果
type SignDyPaySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SignDyPaySearchRes
}

// SignDyPayAddReq 添加操作请求参数
type SignDyPayAddReq struct {
	g.Meta `path:"/add" tags:"抖音代扣订单" method:"post" summary:"抖音代扣订单添加"`
	commonApi.Author
	*model.SignDyPayAddReq
}

// SignDyPayAddRes 添加操作返回结果
type SignDyPayAddRes struct {
	commonApi.EmptyRes
}

// SignDyPayEditReq 修改操作请求参数
type SignDyPayEditReq struct {
	g.Meta `path:"/edit" tags:"抖音代扣订单" method:"put" summary:"抖音代扣订单修改"`
	commonApi.Author
	*model.SignDyPayEditReq
}

// SignDyPayEditRes 修改操作返回结果
type SignDyPayEditRes struct {
	commonApi.EmptyRes
}

// SignDyPayGetReq 获取一条数据请求
type SignDyPayGetReq struct {
	g.Meta `path:"/get" tags:"抖音代扣订单" method:"get" summary:"获取抖音代扣订单信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SignDyPayGetRes 获取一条数据结果
type SignDyPayGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SignDyPayInfoRes
}

// SignDyPayDeleteReq 删除数据请求
type SignDyPayDeleteReq struct {
	g.Meta `path:"/delete" tags:"抖音代扣订单" method:"post" summary:"删除抖音代扣订单"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SignDyPayDeleteRes 删除数据返回
type SignDyPayDeleteRes struct {
	commonApi.EmptyRes
}

// CreateSignPayReq 发起代扣请求
type CreateSignPayReq struct {
	g.Meta `path:"/createSignPay" tags:"抖音代扣订单" method:"post" summary:"发起代扣"`
	commonApi.Author
}

// CreateSignPayRes 发起代扣返回
type CreateSignPayRes struct {
	commonApi.EmptyRes
}
