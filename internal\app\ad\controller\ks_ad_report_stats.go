// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-07 14:26:18
// 生成路径: internal/app/ad/controller/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type ksAdReportStatsController struct {
	systemController.BaseController
}

var KsAdReportStats = new(ksAdReportStatsController)

// GetAdDataReq
func (c *ksAdReportStatsController) GetAdData(ctx context.Context, req *ad.GetAdDataReq) (res *ad.GetAdDataRes, err error) {
	res = new(ad.GetAdDataRes)
	res.Data, err = service.KsAdReportStats().GetAdData(ctx, req.QueryAdDataReq)
	return
}

// Export 导出excel
func (c *ksAdReportStatsController) Export(ctx context.Context, req *ad.KsAdReportStatsExportReq) (res *ad.KsAdReportStatsExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.KsAdReportStatsInfoRes
		//表头
		tableHead = []interface{}{"账户ID", "", "点赞数", "分享数", "封面点击数", "封面曝光数", "付费次数", "付费次数(计费时间)", "付费金额", "付费金额(计费时间)", "广告曝光", "消耗", "唤起应用数", "激活当日付费金额", "激活后24h付费金额(激活时间)", "激活后七日付费金额", "激活后三日付费金额", "激活数", "激活数(计费时间)", "减少此类作品数", "举报数", "拉黑数", "评论数", "首日付费次数", "素材曝光数", "3s播放数", "75%播放进度数", "完播数", "新增粉丝数", "新增付费人数", "行为数", "7日累计付费次数", "7日累计付费金额", "转化数(计费时间)", "深度转化数(计费时间)", "转化数(回传时间)", "深度转化数", "当日累计付费次数", "当日累计付费金额", "3s播放率", "75%进度播放率", "7日累计ROI", "当日累计ROI", "封面点击率", "付费次数成本", "付费ROI", "唤起应用成本", "唤起应用率", "激活单价", "激活当日ROI", "激活后24h-ROI(激活时间)", "激活后3日ROI", "激活后7日ROI", "平均封面点击单价（元）", "平均千次封面曝光花费（元）", "平均千次素材曝光花费（元）", "平均行为单价（元）", "深度转化成本(计费时间)，单位元", "深度转化率(计费时间)", "首日付费次数成本，单位元", "素材点击率", "完播率", "新增付费人数成本，单位元", "新增付费人数率", "行为率", "转化成本(计费时间)，单位元", "转化率(计费时间)", "日期，格式：yyyy-MM-dd HH:mm:ss", "关键行为数", "75%进度播放数", "账号ID", "短剧ID", "充值几率", "IAA广告变现ROI", "IAA广告变现LTV（元）", "统计日期"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.KsAdReportStats().GetExportData(ctx, &req.KsAdReportStatsSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.AdvertiserId,
				v.Id,
				v.Likes,
				v.Share,
				v.PhotoClick,
				v.Impression,
				v.EventPay,
				v.T0DirectPaiedCnt,
				v.EventPayPurchaseAmount,
				v.T0DirectPaiedAmt,
				v.AdShow,
				v.TotalCharge,
				v.EventAppInvoked,
				v.EventPayPurchaseAmountFirstDay,
				v.EventPayPurchaseAmountOneDayByConversion,
				v.EventPayPurchaseAmountWeekByConversion,
				v.EventPayPurchaseAmountThreeDayByConversion,
				v.Conversion,
				v.T0DirectConversionCnt,
				v.Negative,
				v.Report,
				v.Block,
				v.Comment,
				v.EventPayFirstDay,
				v.PlayedNum,
				v.PlayedThreeSeconds,
				v.AdPhotoPlayed75Percent,
				v.PlayedEnd,
				v.Follow,
				v.EventNewUserPay,
				v.AdItemClick,
				v.T7PaiedCnt,
				v.T7PaiedAmt,
				v.ConversionNumByImpression7D,
				v.DeepConversionNumByImpression7D,
				v.ConversionNum,
				v.DeepConversionNum,
				v.T0PaiedCnt,
				v.T0PaiedAmt,
				v.Play3SRatio,
				v.AdPhotoPlayed75PercentRatio,
				v.T7PaiedRoi,
				v.T0PaiedRoi,
				v.PhotoClickRatio,
				v.EventPayCost,
				v.EventPayRoi,
				v.EventAppInvokedCost,
				v.EventAppInvokedRatio,
				v.ConversionCost,
				v.EventPayFirstDayRoi,
				v.EventPayPurchaseAmountOneDayByConversionRoi,
				v.EventPayPurchaseAmountThreeDayByConversionRoi,
				v.EventPayPurchaseAmountWeekByConversionRoi,
				v.PhotoClickCost,
				v.Impression1KCost,
				v.Click1KCost,
				v.ActionCost,
				v.DeepConversionCostByImpression7D,
				v.DeepConversionRatioByImpression7D,
				v.EventPayFirstDayCost,
				v.ActionRatio,
				v.PlayEndRatio,
				v.EventNewUserPayCost,
				v.EventNewUserPayRatio,
				v.ActionNewRatio,
				v.ConversionCostByImpression7D,
				v.ConversionRatioByImpression7D,
				v.Date.Format("Y-m-d H:i:s"),
				v.KeyAction,
				v.AdPhotoPlayed75PercentRatio,
				v.AccountId,
				v.SeriesId,
				v.RechargeRate,
				v.MiniGameIaaRoi,
				v.MiniGameIaaPurchaseAmount,
				v.CreateTime,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("短剧广告报表明细表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// GetKsReportStats
func (c *ksAdReportStatsController) GetKsReportStats(ctx context.Context, req *ad.GetKsReportStatsReq) (res *ad.GetKsReportStatsRes, err error) {
	res = new(ad.GetKsReportStatsRes)
	res.Count, err = service.KsAdReportStats().GetKsReportStats(ctx, req.AdvertiserId, req.AppId, req.StatTime)
	return
}

// KsAdReportStatsSyncReq
func (c *ksAdReportStatsController) KsAdReportStatsSync(ctx context.Context, req *ad.KsAdReportStatsSyncReq) (res *ad.KsAdReportStatsSyncRes, err error) {
	res = new(ad.KsAdReportStatsSyncRes)
	res.Count, err = service.KsAdReportStats().KsAdReportStatsSync(ctx, req.StartTime, req.EndTime)
	return
}

// List 列表
func (c *ksAdReportStatsController) List(ctx context.Context, req *ad.KsAdReportStatsSearchReq) (res *ad.KsAdReportStatsSearchRes, err error) {
	res = new(ad.KsAdReportStatsSearchRes)
	res.KsAdReportStatsSearchRes, err = service.KsAdReportStats().List(ctx, &req.KsAdReportStatsSearchReq)
	return
}

// Get 获取短剧广告报表明细表
func (c *ksAdReportStatsController) Get(ctx context.Context, req *ad.KsAdReportStatsGetReq) (res *ad.KsAdReportStatsGetRes, err error) {
	res = new(ad.KsAdReportStatsGetRes)
	res.KsAdReportStatsInfoRes, err = service.KsAdReportStats().GetById(ctx, req.AdvertiserId)
	return
}

// Add 添加短剧广告报表明细表
func (c *ksAdReportStatsController) Add(ctx context.Context, req *ad.KsAdReportStatsAddReq) (res *ad.KsAdReportStatsAddRes, err error) {
	err = service.KsAdReportStats().Add(ctx, req.KsAdReportStatsAddReq)
	return
}

// Edit 修改短剧广告报表明细表
func (c *ksAdReportStatsController) Edit(ctx context.Context, req *ad.KsAdReportStatsEditReq) (res *ad.KsAdReportStatsEditRes, err error) {
	err = service.KsAdReportStats().Edit(ctx, req.KsAdReportStatsEditReq)
	return
}

// Delete 删除短剧广告报表明细表
func (c *ksAdReportStatsController) Delete(ctx context.Context, req *ad.KsAdReportStatsDeleteReq) (res *ad.KsAdReportStatsDeleteRes, err error) {
	err = service.KsAdReportStats().Delete(ctx, req.AdvertiserIds)
	return
}
