// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-12 11:45:31
// 生成路径: api/v1/theater/s_dy_cover_library.go
// 生成人：cq
// desc:抖音封面库表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyCoverLibrarySearchReq 分页请求参数
type SDyCoverLibrarySearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广 - 抖音内容库 - 封面库" method:"get" summary:"抖音封面库表列表"`
	commonApi.Author
	model.SDyCoverLibrarySearchReq
}

// SDyCoverLibrarySearchRes 列表返回结果
type SDyCoverLibrarySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyCoverLibrarySearchRes
}

// SDyCoverLibraryAddReq 添加操作请求参数
type SDyCoverLibraryAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广 - 抖音内容库 - 封面库" method:"post" summary:"抖音封面库表添加"`
	commonApi.Author
	*model.SDyCoverLibraryAddReq
}

// SDyCoverLibraryAddRes 添加操作返回结果
type SDyCoverLibraryAddRes struct {
	commonApi.EmptyRes
	OssUrl    string `json:"ossUrl" dc:"视频oss url"`
	OpenPicId string `json:"openPicId" dc:"封面ID"`
}

// SDyCoverLibraryEditReq 修改操作请求参数
type SDyCoverLibraryEditReq struct {
	g.Meta `path:"/edit" tags:"短剧推广 - 抖音内容库 - 封面库" method:"put" summary:"抖音封面库表修改"`
	commonApi.Author
	*model.SDyCoverLibraryEditReq
}

// SDyCoverLibraryEditRes 修改操作返回结果
type SDyCoverLibraryEditRes struct {
	commonApi.EmptyRes
}

// SDyCoverLibraryGetReq 获取一条数据请求
type SDyCoverLibraryGetReq struct {
	g.Meta `path:"/get" tags:"短剧推广 - 抖音内容库 - 封面库" method:"get" summary:"获取抖音封面库表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SDyCoverLibraryGetRes 获取一条数据结果
type SDyCoverLibraryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyCoverLibraryInfoRes
}

// SDyCoverLibraryDeleteReq 删除数据请求
type SDyCoverLibraryDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧推广 - 抖音内容库 - 封面库" method:"post" summary:"删除抖音封面库表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SDyCoverLibraryDeleteRes 删除数据返回
type SDyCoverLibraryDeleteRes struct {
	commonApi.EmptyRes
}
