// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: internal/app/ad/model/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAccountSeriesInfoRes is the golang structure for table ks_account_series.
type KsAccountSeriesInfoRes struct {
	gmeta.Meta   `orm:"table:ks_account_series"`
	AdvertiserId int64  `orm:"advertiser_id,primary" json:"advertiserId" dc:"用户快手号id/广告主id"` // 用户快手号id/广告主id
	SeriesId     int64  `orm:"series_id,primary" json:"seriesId" dc:"短剧id"`                  // 短剧id
	SeriesName   string `orm:"series_name" json:"seriesName" dc:"短剧名称"`                      // 短剧名称
}

type KsAccountSeriesListRes struct {
	AdvertiserId int64  `json:"advertiserId" dc:"用户快手号id/广告主id"`
	SeriesId     int64  `json:"seriesId" dc:"短剧id"`
	SeriesName   string `json:"seriesName" dc:"短剧名称"`
}

// KsAccountSeriesSearchReq 分页请求参数
type KsAccountSeriesSearchReq struct {
	comModel.PageReq
	AdvertiserId string `p:"advertiserId" dc:"用户快手号id/广告主id"` //用户快手号id/广告主id
	SeriesId     int64  `p:"seriesId" dc:"短剧id"`              //短剧id
	SeriesName   string `p:"seriesName" dc:"短剧名称"`            //短剧名称
}

// KsAccountSeriesSearchRes 列表返回结果
type KsAccountSeriesSearchRes struct {
	comModel.ListRes
	List []*KsAccountSeriesListRes `json:"list"`
}

// KsAccountSeriesAddReq 添加操作请求参数
type KsAccountSeriesAddReq struct {
	AdvertiserId int64  `p:"advertiserId" v:"required#主键ID不能为空" dc:"用户快手号id/广告主id"`
	SeriesId     int64  `p:"seriesId" dc:"短剧id"` //短剧id
	SeriesName   string `p:"seriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
}

// KsAccountSeriesEditReq 修改操作请求参数
type KsAccountSeriesEditReq struct {
	AdvertiserId int64  `p:"advertiserId" v:"required#主键ID不能为空" dc:"用户快手号id/广告主id"`
	SeriesId     int64  `p:"seriesId" dc:"短剧id"` //短剧id
	SeriesName   string `p:"seriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
}
