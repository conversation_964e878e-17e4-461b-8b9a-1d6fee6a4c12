// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/logic/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoApi "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdToolsSite(New())
}

func New() service.IAdToolsSite {
	return &sAdToolsSite{}
}

type sAdToolsSite struct{}

func (s *sAdToolsSite) List(ctx context.Context, req *model.AdToolsSiteSearchReq) (listRes *model.AdToolsSiteSearchRes, err error) {
	listRes = new(model.AdToolsSiteSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdToolsSite.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdToolsSite.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdToolsSite.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if req.SiteId != "" {
			m = m.Where(dao.AdToolsSite.Columns().SiteId+" = ?", req.SiteId)
		}
		if req.SiteName != "" {
			m = m.Where(dao.AdToolsSite.Columns().SiteName+" like ?", "%"+req.SiteName+"%")
		}
		if req.Status != "" {
			m = m.Where(dao.AdToolsSite.Columns().Status+" = ?", req.Status)
		}
		if req.SiteType != "" {
			m = m.Where(dao.AdToolsSite.Columns().SiteType+" = ?", req.SiteType)
		}
		if req.FunctionType != "" {
			m = m.Where(dao.AdToolsSite.Columns().FunctionType+" = ?", req.FunctionType)
		}
		if req.Thumbnail != "" {
			m = m.Where(dao.AdToolsSite.Columns().Thumbnail+" = ?", req.Thumbnail)
		}
		if req.MainUserId != "" {
			m = m.Where(dao.AdToolsSite.Columns().MainUserId+" = ?", gconv.Int(req.MainUserId))
		}
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdToolsSite.Columns().MainUserId, userIds)
		}

		if req.CreateTime != "" {
			m = m.Where(dao.AdToolsSite.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.AdToolsSite.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdToolsSiteListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdToolsSiteListRes, len(res))
		ids := make([]string, len(res))
		for i, item := range res {
			ids[i] = gconv.String(item.AdvertiserId)
		}
		nameMap, _ := oceanengineService.AdAdvertiserAccount().GetAdAccountName(ctx, ids)
		for k, v := range res {
			listRes.List[k] = &model.AdToolsSiteListRes{
				Id:             v.Id,
				AdvertiserId:   v.AdvertiserId,
				AdvertiserName: nameMap[gconv.String(v.AdvertiserId)],
				SiteId:         v.SiteId,
				SiteName:       v.SiteName,
				Status:         v.Status,
				SiteType:       v.SiteType,
				FunctionType:   v.FunctionType,
				Thumbnail:      v.Thumbnail,
				MainUserId:     v.MainUserId,
				SiteUrl:        fmt.Sprintf("https://www.chengzijianzhan.com/tetris/page/%s/", v.SiteId),
				CreateTime:     v.CreateTime,
				UpdateTime:     v.UpdateTime,
			}
		}
	})
	return
}

// SynchronizeSite 同步站点数据
func (s *sAdToolsSite) SynchronizeSite(ctx context.Context, req *model.SynchronizeSiteReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, id := range req.AdvertiserIds {
			err = s.SyncSiteByAccountId(ctx, id)
		}
	})
	return
}

func (s *sAdToolsSite) ToolsSiteHandsel(ctx context.Context, req *ad.ToolsSiteHandselReq) (res *ad.ToolsSiteHandselRes, err error) {
	// 首先获取当前account下的广告id
	token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, gconv.String(req.AdvertiserId))
	liberr.ErrIsNil(ctx, err, "获取accessToken失败")

	result, err := advertiser.GetToutiaoApiClient().ToolsSiteHandselV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoModels.ToolsSiteHandselV2Request{
		AdvertiserId:        req.AdvertiserId,
		SiteIds:             req.SiteIds,
		TargetAdvertiserIds: req.TargetAdvertiserIds,
	}).Do()
	liberr.ErrIsNil(ctx, err, "转赠失败")
	res = new(ad.ToolsSiteHandselRes)
	if result.Data != nil {
		for _, item := range result.Data.ErrorList {
			res.ErrorList = append(res.ErrorList, model.ToolsSiteHandselError{
				OriginSiteID:       *item.OriginSiteId,
				TargetAdvertiserID: *item.TargetAdvertiserId,
				ErrorReason:        *item.ErrorReason,
			})
		}
		for _, item := range result.Data.SuccessList {
			res.SuccessList = append(res.SuccessList, model.ToolsSiteHandselSuccess{
				SiteID:             *item.SiteId,
				OriginSiteID:       *item.OriginSiteId,
				TargetAdvertiserID: *item.TargetAdvertiserId,
			})
		}
	}
	return
}

func (s *sAdToolsSite) SyncSiteByAccountId(ctx context.Context, advertiserId int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		// 首先获取当前account下的广告id
		token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, gconv.String(advertiserId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		page, size := 1, 100
		for {
			result, err := advertiser.GetToutiaoApiClient().ToolsSiteGetV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoApi.ApiOpenApi2ToolsSiteGetGetRequest{
				AdvertiserId: advertiserId,
				Page:         page,
				PageSize:     size,
			}).Do()
			if err != nil {
				return
			}
			addList := make([]*do.AdToolsSite, 0)
			for _, item := range result.Data.List {
				addList = append(addList, &do.AdToolsSite{
					AdvertiserId: advertiserId,
					SiteId:       item.SiteId,
					SiteName:     item.Name,
					Status:       item.Status,
					SiteType:     item.SiteType,
					FunctionType: item.FunctionType,
					Thumbnail:    item.Thumbnail,
					MainUserId:   userInfo.Id,
					CreateTime:   gtime.Now(),
				})
			}
			if len(addList) > 0 {
				err = s.BatchAdd(ctx, addList)
			} else {
				break
			}
			if len(result.Data.List) != size {
				break
			}
			page++
		}
	})
	return
}

func (s *sAdToolsSite) BatchAdd(ctx context.Context, req []*do.AdToolsSite) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdToolsSite.Ctx(ctx).Save(req)
		liberr.ErrIsNil(ctx, err, "AdToolsSite添加失败")
	})
	return
}

func (s *sAdToolsSite) GetById(ctx context.Context, id int64) (res *model.AdToolsSiteInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdToolsSite.Ctx(ctx).WithAll().Where(dao.AdToolsSite.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdToolsSite) Add(ctx context.Context, req *model.AdToolsSiteAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdToolsSite.Ctx(ctx).Insert(do.AdToolsSite{
			AdvertiserId: req.AdvertiserId,
			SiteId:       req.SiteId,
			SiteName:     req.SiteName,
			Status:       req.Status,
			SiteType:     req.SiteType,
			FunctionType: req.FunctionType,
			Thumbnail:    req.Thumbnail,
			MainUserId:   req.MainUserId,
			CreateTime:   req.CreateTime,
			UpdateTime:   req.UpdateTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdToolsSite) Edit(ctx context.Context, req *model.AdToolsSiteEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdToolsSite.Ctx(ctx).WherePri(req.Id).Update(do.AdToolsSite{
			AdvertiserId: req.AdvertiserId,
			SiteId:       req.SiteId,
			SiteName:     req.SiteName,
			Status:       req.Status,
			SiteType:     req.SiteType,
			FunctionType: req.FunctionType,
			Thumbnail:    req.Thumbnail,
			MainUserId:   req.MainUserId,
			CreateTime:   req.CreateTime,
			UpdateTime:   req.UpdateTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdToolsSite) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdToolsSite.Ctx(ctx).Delete(dao.AdToolsSite.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdToolsSite) GetPreviewUrl(ctx context.Context, advertiserId int64, siteId string) (string, error) {
	// 首先获取当前account下的广告id
	token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, gconv.String(advertiserId))
	liberr.ErrIsNil(ctx, err, "获取accessToken失败")
	result, err := advertiser.GetToutiaoApiClient().ToolsSitePreviewV2ApiService.AccessToken(token.AccessToken).AdvertiserId(advertiserId).SiteId(siteId).Do()
	if err != nil {
		return "", err
	}
	return *result.Data.Url, nil
}

// 操作状态
func (s *sAdToolsSite) UpdateStatus(ctx context.Context, req *ad.UpdateStatusReq) (res *ad.UpdateStatusRes, err error) {
	token, err := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, gconv.String(req.AdvertiserId))
	liberr.ErrIsNil(ctx, err, "获取accessToken失败")
	result, err := advertiser.GetToutiaoApiClient().ToolsSiteUpdateStatusV2ApiService.AccessToken(token.AccessToken).SetRequest(toutiaoModels.ToolsSiteUpdateStatusV2Request{
		AdvertiserId: req.AdvertiserId,
		SiteIds:      req.SiteIds,
		Status:       req.Status,
	}).Do()
	liberr.ErrIsNil(ctx, err)
	res = new(ad.UpdateStatusRes)
	for _, item := range result.Data.Fail {
		res.Fail = append(res.Fail, model.UpdateStatusFail{
			Message: *item.Message,
			SiteID:  *item.SiteId,
		})
	}
	successList := make([]string, 0)
	for _, id := range req.SiteIds {
		have := false
		for _, fail := range res.Fail {
			if fail.SiteID == id {
				have = true
				break
			}
		}
		if !have {
			successList = append(successList, id)
		}
	}
	_, err = dao.AdToolsSite.Ctx(ctx).Data(g.Map{"status": req.Status}).Where("advertiser_id", req.AdvertiserId).WhereIn("site_id", successList).Update()
	liberr.ErrIsNil(ctx, err, "删除失败")
	res.Success = result.Data.Success
	return

}
