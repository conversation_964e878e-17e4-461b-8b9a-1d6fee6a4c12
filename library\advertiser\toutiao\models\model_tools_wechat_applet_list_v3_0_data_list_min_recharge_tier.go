/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListMinRechargeTier
type ToolsWechatAppletListV30DataListMinRechargeTier string

// List of tools_wechat_applet_list_v3.0_data_list_min_recharge_tier
const (
	FROM_100_TO_200_ToolsWechatAppletListV30DataListMinRechargeTier       ToolsWechatAppletListV30DataListMinRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletListV30DataListMinRechargeTier ToolsWechatAppletListV30DataListMinRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletListV30DataListMinRechargeTier     ToolsWechatAppletListV30DataListMinRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_min_recharge_tier value
func (v ToolsWechatAppletListV30DataListMinRechargeTier) Ptr() *ToolsWechatAppletListV30DataListMinRechargeTier {
	return &v
}
