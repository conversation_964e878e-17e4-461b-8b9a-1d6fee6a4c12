// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: internal/app/ad/dao/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdUserPaymentRecordDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdUserPaymentRecordDao struct {
	*internal.FqAdUserPaymentRecordDao
}

var (
	// FqAdUserPaymentRecord is globally public accessible object for table tools_gen_table operations.
	FqAdUserPaymentRecord = fqAdUserPaymentRecordDao{
		internal.NewFqAdUserPaymentRecordDao(),
	}

	FqAdUserPaymentRecordAnalytic = fqAdUserPaymentRecordDao{
		internal.NewFqAdUserPaymentRecordAnalyticDao(),
	}
)

// Fill with you ideas below.
