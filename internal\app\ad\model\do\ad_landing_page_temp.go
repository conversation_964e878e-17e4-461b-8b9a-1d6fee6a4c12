// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: internal/app/ad/model/entity/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdLandingPageTemp is the golang structure for table ad_landing_page_temp.
type AdLandingPageTemp struct {
	gmeta.Meta `orm:"table:ad_landing_page_temp, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`           // ID
	MainUserId interface{} `orm:"main_user_id" json:"mainUserId"` // 创建用户id
	TempleName interface{} `orm:"temple_name" json:"templeName"`  // 模板名称
	TempleType interface{} `orm:"temple_type" json:"templeType"`  // 模板类型 0 应用下载  1 微信小程序  2微信小游戏
	Bricks     interface{} `orm:"bricks" json:"bricks"`           // json的结构体详情结构见文档
	AdNum      interface{} `orm:"ad_num" json:"adNum"`            // 广告数量
	CreateTime *gtime.Time `orm:"create_time" json:"createTime"`  // 创建时间
	UpdateTime *gtime.Time `orm:"update_time" json:"updateTime"`  // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt"`    // 删除时间
}
