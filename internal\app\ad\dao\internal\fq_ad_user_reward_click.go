// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: internal/app/ad/dao/internal/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdUserRewardClickDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdUserRewardClickDao struct {
	table   string                     // Table is the underlying table name of the DAO.
	group   string                     // Group is the database configuration group name of current DAO.
	columns FqAdUserRewardClickColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdUserRewardClickColumns defines and stores column names for table fq_ad_user_reward_click.
type FqAdUserRewardClickColumns struct {
	EcpmNo        string // 激励收益的唯一键，对齐抖开接口的req_id
	DistributorId string // 快应用/公众号对应distributor_id
	AppId         string // 公众号/快应用/小程序id（分销平台id）【v1.2】
	AppName       string // 快应用/公众号/小程序名称【v1.2】
	DeviceId      string // 脱敏后的用户设备ID
	PromotionId   string // 推广链id
	EcpmCost      string // 激励点击金额，单位十万分之一元
	EventTime     string // 激励点击的时间戳
	RegisterTime  string // 用户染色时间戳
	BookId        string // 染色推广链的短剧ID
	BookName      string // 染色推广链的短剧名称
	BookGender    string // 染色推广链短剧性别(0女生、1男生、2无性别)
	BookCategory  string // 染色推广链的短剧类型
	CreatedAt     string // 创建时间
}

var fqAdUserRewardClickColumns = FqAdUserRewardClickColumns{
	EcpmNo:        "ecpm_no",
	DistributorId: "distributor_id",
	AppId:         "app_id",
	AppName:       "app_name",
	DeviceId:      "device_id",
	PromotionId:   "promotion_id",
	EcpmCost:      "ecpm_cost",
	EventTime:     "event_time",
	RegisterTime:  "register_time",
	BookId:        "book_id",
	BookName:      "book_name",
	BookGender:    "book_gender",
	BookCategory:  "book_category",
	CreatedAt:     "created_at",
}

// NewFqAdUserRewardClickDao creates and returns a new DAO object for table data access.
func NewFqAdUserRewardClickDao() *FqAdUserRewardClickDao {
	return &FqAdUserRewardClickDao{
		group:   "default",
		table:   "fq_ad_user_reward_click",
		columns: fqAdUserRewardClickColumns,
	}
}

func NewFqAdUserRewardClickAnalyticDao() *FqAdUserRewardClickDao {
	return &FqAdUserRewardClickDao{
		group:   "analyticDB",
		table:   "fq_ad_user_reward_click",
		columns: fqAdUserRewardClickColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdUserRewardClickDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdUserRewardClickDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdUserRewardClickDao) Columns() FqAdUserRewardClickColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdUserRewardClickDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdUserRewardClickDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdUserRewardClickDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
