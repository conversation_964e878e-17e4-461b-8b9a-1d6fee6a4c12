// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-11-13 10:42:38
// 生成路径: internal/app/ad/dao/internal/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAppConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdAppConfigDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns AdAppConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdAppConfigColumns defines and stores column names for table ad_app_config.
type AdAppConfigColumns struct {
	Id           string // ID
	AppId        string //
	Secret       string //
	Type         string // 应用类型：1：巨量 2：广点通
	AuthNums     string // 已授权账号数
	AuthUserType string // 授权用户类型： 1：纵横组织 2：方舟
	CreatedAt    string // 创建时间
	UpdatedAt    string // 更新时间
	DeletedAt    string // 删除时间
}

var adAppConfigColumns = AdAppConfigColumns{
	Id:           "id",
	AppId:        "app_id",
	Secret:       "secret",
	Type:         "type",
	AuthNums:     "auth_nums",
	AuthUserType: "auth_user_type",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewAdAppConfigDao creates and returns a new DAO object for table data access.
func NewAdAppConfigDao() *AdAppConfigDao {
	return &AdAppConfigDao{
		group:   "default",
		table:   "ad_app_config",
		columns: adAppConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdAppConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdAppConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdAppConfigDao) Columns() AdAppConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdAppConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdAppConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdAppConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
