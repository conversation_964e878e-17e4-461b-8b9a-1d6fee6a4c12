package model

// MajordomoAdvertiserSelectV2Response struct for MajordomoAdvertiserSelectV2Response
type MajordomoAdvertiserSelectV2Response struct {
	//
	Code int64                                    `json:"code,omitempty"`
	Data *MajordomoAdvertiserSelectV2ResponseData `json:"data,omitempty"`
	//
	Message string `json:"message,omitempty"`
	//
	RequestId string `json:"request_id,omitempty"`
}

type MajordomoAdvertiserSelectV2Request struct {
	AdvertiserId int64
}

// MajordomoAdvertiserSelectV2ResponseData
type MajordomoAdvertiserSelectV2ResponseData struct {
	//
	List []MajordomoAdvertiserSelectV2ResponseDataListInner `json:"list,omitempty"`
}

// MajordomoAdvertiserSelectV2ResponseDataListInner struct for MajordomoAdvertiserSelectV2ResponseDataListInner
type MajordomoAdvertiserSelectV2ResponseDataListInner struct {
	//
	AdvertiserId int64 `json:"advertiser_id,omitempty"`
	//
	AdvertiserName string `json:"advertiser_name,omitempty"`
}
