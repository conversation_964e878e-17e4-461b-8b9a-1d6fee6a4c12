// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: internal/app/ad/service/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IDzTask interface {
	List(ctx context.Context, req *model.DzTaskSearchReq) (res *model.DzTaskSearchRes, err error)
	GetByTaskId(ctx context.Context, TaskId string) (res *model.DzTaskInfoRes, err error)
	Add(ctx context.Context, req *model.DzTaskAddReq) (err error)
	Edit(ctx context.Context, req *model.DzTaskEditReq) (err error)
	Delete(ctx context.Context, TaskId []string) (err error)
}

var localDzTask IDzTask

func DzTask() IDzTask {
	if localDzTask == nil {
		panic("implement not found for interface IDzTask, forgot register?")
	}
	return localDzTask
}

func RegisterDzTask(i IDzTask) {
	localDzTask = i
}
