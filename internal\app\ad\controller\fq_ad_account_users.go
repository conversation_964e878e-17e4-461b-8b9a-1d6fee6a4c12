// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-16 11:16:21
// 生成路径: internal/app/ad/controller/fq_ad_account_users.go
// 生成人：gfast
// desc:番茄账号权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdAccountUsersController struct {
	systemController.BaseController
}

var FqAdAccountUsers = new(fqAdAccountUsersController)

// List 列表
func (c *fqAdAccountUsersController) List(ctx context.Context, req *ad.FqAdAccountUsersSearchReq) (res *ad.FqAdAccountUsersSearchRes, err error) {
	res = new(ad.FqAdAccountUsersSearchRes)
	res.FqAdAccountUsersSearchRes, err = service.FqAdAccountUsers().List(ctx, &req.FqAdAccountUsersSearchReq)
	return
}

// Export 导出excel
func (c *fqAdAccountUsersController) Export(ctx context.Context, req *ad.FqAdAccountUsersExportReq) (res *ad.FqAdAccountUsersExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdAccountUsersInfoRes
		//表头
		tableHead = []interface{}{"渠道ID", "用户ID"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.FqAdAccountUsers().GetExportData(ctx, &req.FqAdAccountUsersSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.DistributorId,
				v.SpecifyUserId,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("番茄账号权限用户关联")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取番茄账号权限用户关联
func (c *fqAdAccountUsersController) Get(ctx context.Context, req *ad.FqAdAccountUsersGetReq) (res *ad.FqAdAccountUsersGetRes, err error) {
	res = new(ad.FqAdAccountUsersGetRes)
	res.FqAdAccountUsersInfoRes, err = service.FqAdAccountUsers().GetBySpecifyUserId(ctx, req.DistributorId)
	return
}

// Add 添加番茄账号权限用户关联
func (c *fqAdAccountUsersController) Add(ctx context.Context, req *ad.FqAdAccountUsersAddReq) (res *ad.FqAdAccountUsersAddRes, err error) {
	err = service.FqAdAccountUsers().Add(ctx, req.FqAdAccountUsersAddReq)
	return
}

// Edit 修改番茄账号权限用户关联
func (c *fqAdAccountUsersController) Edit(ctx context.Context, req *ad.FqAdAccountUsersEditReq) (res *ad.FqAdAccountUsersEditRes, err error) {
	err = service.FqAdAccountUsers().Edit(ctx, req.FqAdAccountUsersEditReq)
	return
}

// Delete 删除番茄账号权限用户关联
func (c *fqAdAccountUsersController) Delete(ctx context.Context, req *ad.FqAdAccountUsersDeleteReq) (res *ad.FqAdAccountUsersDeleteRes, err error) {
	err = service.FqAdAccountUsers().Delete(ctx, req.DistributorIds)
	return
}
