/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// PromotionCreateV30ApiService PromotionCreateV30Api service
type PromotionCreateV30ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.PromotionCreateV30Request
}

func (r *PromotionCreateV30ApiService) SetCfg(cfg *conf.Configuration) *PromotionCreateV30ApiService {
	r.cfg = cfg
	return r
}

func (r *PromotionCreateV30ApiService) SetRequest(req models.PromotionCreateV30Request) *PromotionCreateV30ApiService {
	r.Request = &req
	return r
}

func (r *PromotionCreateV30ApiService) AccessToken(accessToken string) *PromotionCreateV30ApiService {
	r.token = accessToken
	return r
}



// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *PromotionCreateV30ApiService) Do() (data *models.PromotionCreateV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/promotion/create/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.PromotionCreateV30Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.PromotionCreateV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/promotion/create/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}


// Execute executes the request
//
//	@return PromotionCreateV30Response
//func (a *PromotionCreateV30ApiService) postExecute(r *ApiOpenApiV30PromotionCreatePostRequest) (*PromotionCreateV30Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodPost
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *PromotionCreateV30Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/v3.0/promotion/create/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{"application/json"}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	// body params
//	localVarPostBody = r.promotionCreateV30Request
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
