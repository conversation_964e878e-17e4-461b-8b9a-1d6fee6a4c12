/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameCreateV30ScheduleCards
type ToolsWechatGameCreateV30ScheduleCards string

// List of tools_wechat_game_create_v3.0_schedule_cards
const (
	ANNUAL_ToolsWechatGameCreateV30ScheduleCards    ToolsWechatGameCreateV30ScheduleCards = "Annual"
	LIFETIME_ToolsWechatGameCreateV30ScheduleCards  ToolsWechatGameCreateV30ScheduleCards = "Lifetime"
	MONTHLY_ToolsWechatGameCreateV30ScheduleCards   ToolsWechatGameCreateV30ScheduleCards = "Monthly"
	NONE_ToolsWechatGameCreateV30ScheduleCards      ToolsWechatGameCreateV30ScheduleCards = "None"
	QUARTERLY_ToolsWechatGameCreateV30ScheduleCards ToolsWechatGameCreateV30ScheduleCards = "Quarterly"
	WEEKLY_ToolsWechatGameCreateV30ScheduleCards    ToolsWechatGameCreateV30ScheduleCards = "Weekly"
)

// Ptr returns reference to tools_wechat_game_create_v3.0_schedule_cards value
func (v ToolsWechatGameCreateV30ScheduleCards) Ptr() *ToolsWechatGameCreateV30ScheduleCards {
	return &v
}
