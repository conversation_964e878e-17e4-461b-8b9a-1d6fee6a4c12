// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-31 15:18:06
// 生成路径: api/v1/channel/m_member_ad_callback_video_statistics.go
// 生成人：cq
// desc:短剧广告统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdCallbackVideoStatisticsSearchReq 分页请求参数
type MMemberAdCallbackVideoStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"短剧广告统计" method:"post" summary:"短剧广告统计列表"`
	commonApi.Author
	model.MMemberAdCallbackVideoStatisticsSearchReq
}

// MMemberAdCallbackVideoStatisticsSearchRes 列表返回结果
type MMemberAdCallbackVideoStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackVideoStatisticsSearchRes
}

// MMemberAdCallbackVideoStatisticsExportReq 导出请求
type MMemberAdCallbackVideoStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"短剧广告统计" method:"post" summary:"短剧广告统计导出"`
	commonApi.Author
	model.MMemberAdCallbackVideoStatisticsSearchReq
}

// MMemberAdCallbackVideoStatisticsExportRes 导出响应
type MMemberAdCallbackVideoStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackVideoStatisticsTaskReq 短剧广告统计请求参数
type MMemberAdCallbackVideoStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"短剧广告统计" method:"post" summary:"短剧广告统计任务"`
	commonApi.Author
	model.MMemberAdCallbackVideoStatisticsSearchReq
}

// MMemberAdCallbackVideoStatisticsTaskRes 短剧广告统计返回结果
type MMemberAdCallbackVideoStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}
