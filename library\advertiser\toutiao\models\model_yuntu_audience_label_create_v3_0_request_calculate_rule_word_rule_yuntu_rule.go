/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30RequestCalculateRuleWordRuleYuntuRule 云图的关键词规则协议。
type YuntuAudienceLabelCreateV30RequestCalculateRuleWordRuleYuntuRule struct {
	// 关键词池列表。二维数组，列表中每个元素为一个字符串数组，每个字符串数组代表一个关键词池。
	KeywordsList []*[]string `json:"keywords_list"`
	// 关键词池间合并规则，&代表池间交集，~代表池间差集，交差关系不存在层级。数字下标由0开始，单调递增至 keywords_list 长度 - 1。
	KeywordsMergeRule string `json:"keywords_merge_rule"`
}
