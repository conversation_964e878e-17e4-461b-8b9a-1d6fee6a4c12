/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AdvertiserBudgetUpdateV2ApiService AdvertiserBudgetUpdateV2Api service
type AdvertiserBudgetUpdateV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.AdvertiserUpdateBudgetV2Request
}

func (r *AdvertiserBudgetUpdateV2ApiService) SetCfg(cfg *conf.Configuration) *AdvertiserBudgetUpdateV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AdvertiserBudgetUpdateV2ApiService) AdvertiserUpdateBudgetV2Request(advertiserUpdateBudgetV2Request models.AdvertiserUpdateBudgetV2Request) *AdvertiserBudgetUpdateV2ApiService {
	r.Request = &advertiserUpdateBudgetV2Request
	return r
}

func (r *AdvertiserBudgetUpdateV2ApiService) AccessToken(accessToken string) *AdvertiserBudgetUpdateV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AdvertiserBudgetUpdateV2ApiService) Do() (data *models.AdvertiserUpdateBudgetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/advertiser/update/budget/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.AdvertiserUpdateBudgetV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AdvertiserUpdateBudgetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/update/budget/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
