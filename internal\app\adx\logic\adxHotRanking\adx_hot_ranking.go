// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/logic/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"time"
)

func init() {
	service.RegisterAdxHotRanking(New())
}

func New() service.IAdxHotRanking {
	return &sAdxHotRanking{}
}

type sAdxHotRanking struct{}

func (s *sAdxHotRanking) List(ctx context.Context, req *model.AdxHotRankingSearchReq) (listRes *model.AdxHotRankingSearchRes, err error) {
	listRes = new(model.AdxHotRankingSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {

		m := dao.AdxHotRanking.Ctx(ctx).WithAll().Fields(`playlet_id,
  MAX(playlet_name) AS playletName,
  SUM(consume_num) AS consumeNum,               
  MAX(total_consume_num) AS totalConsumeNum,   
  MAX(new_flag) AS newFlag,
  MAX(playlet_tags) AS playletTags,
  MAX(contractor_list) AS contractorList,
 MAX(copyright_holder_list) AS copyrightHolderList,
  MAX(related_party_list) AS relatedPartyList`).Group(dao.AdxHotRanking.Columns().PlayletId)
		if req.PlayletId != "" {
			m = m.Where(dao.AdxHotRanking.Columns().PlayletId+" = ?", req.PlayletId)
		}
		if req.PlayletName != "" {
			m = m.Where(dao.AdxHotRanking.Columns().PlayletName+" like ?", "%"+req.PlayletName+"%")
		}
		if req.NewFlag != "" {
			m = m.Where(dao.AdxHotRanking.Columns().NewFlag+" = ?", gconv.Int(req.NewFlag))
		}
		if len(req.StartTime) > 0 {
			m = m.Where(dao.AdxHotRanking.Columns().BusinessDate+" >= ?", req.StartTime)
		}
		if len(req.EndTime) > 0 {
			m = m.Where(dao.AdxHotRanking.Columns().BusinessDate+" <= ?", req.EndTime)
		}
		if len(req.PlayletTags) > 0 {
			josnStr := ""
			if len(req.PlayletTags) == 1 {
				josnStr += fmt.Sprintf("JSON_CONTAINS(playlet_tags, '\\\"%s\\\"')", req.PlayletTags[0])
			} else {
				for i, tag := range req.PlayletTags {
					if i == 0 {
						josnStr += fmt.Sprintf("( JSON_CONTAINS(playlet_tags, '\\\"%s\\\"')", tag)
						continue
					}
					if i == len(req.PlayletTags)-1 {
						josnStr += fmt.Sprintf(" or JSON_CONTAINS(playlet_tags, '\\\"%s\\\"') )", tag)
						continue
					}
					josnStr += fmt.Sprintf(" or JSON_CONTAINS(playlet_tags, '\\\"%s\\\"')", tag)
				}
			}
			m = m.Where(josnStr)
		}

		if len(req.CopyrightHolderList) > 0 {
			m = m.WhereNotNull(dao.AdxHotRanking.Columns().CopyrightHolderList)
			josnStr := ""
			if len(req.CopyrightHolderList) == 1 {
				josnStr += fmt.Sprintf("JSON_CONTAINS(copyright_holder_list, '%s')", req.CopyrightHolderList[0])
			} else {
				for i, tag := range req.CopyrightHolderList {
					if i == 0 {
						josnStr += fmt.Sprintf("( JSON_CONTAINS(copyright_holder_list, '%s')", tag)
						continue
					}
					if i == len(req.CopyrightHolderList)-1 {
						josnStr += fmt.Sprintf(" or JSON_CONTAINS(copyright_holder_list, '%s') )", tag)
						continue
					}
					josnStr += fmt.Sprintf(" or JSON_CONTAINS(copyright_holder_list, '%s')", tag)
				}
			}
			m = m.Where(josnStr)
		}

		// req.ContractorList
		if len(req.ContractorList) > 0 {
			//josnStr := ""
			//if len(req.ContractorList) == 1 {
			//	josnStr += fmt.Sprintf("JSON_CONTAINS(contractor_list, '{\"publisherName\":\"%s\"}')", req.ContractorList[0].PublisherName)
			//} else {
			//	for i, item := range req.ContractorList {
			//		if i == 0 {
			//			josnStr += fmt.Sprintf("( JSON_CONTAINS(contractor_list, '{\"publisherName\":\"%s\"}')", item.PublisherName)
			//			continue
			//		}
			//		if i == len(req.ContractorList)-1 {
			//			josnStr += fmt.Sprintf(" or JSON_CONTAINS(contractor_list, '{\"publisherName\":\"%s\"}') )", item.PublisherName)
			//		}
			//		josnStr += fmt.Sprintf(" or JSON_CONTAINS(contractor_list, '{\"publisherName\":\"%s\"}') ", item.PublisherName)
			//	}
			//}

			if len(req.ContractorList) == 1 {
				//josnStr += fmt.Sprintf("contractor_list like '%" + req.ContractorList[0].PublisherName + "%' ")
				m = m.WhereLike(dao.AdxHotRanking.Columns().ContractorList, "%"+req.ContractorList[0].PublisherName+"%")
			} else {
				for i, item := range req.ContractorList {
					if i == 0 {
						//josnStr += fmt.Sprintf("( contractor_list like '%"+req.ContractorList[0].PublisherName+"%')", item.PublisherName)
						m = m.WhereLike(dao.AdxHotRanking.Columns().ContractorList, "%"+item.PublisherName+"%")
						continue
					}
					m = m.WhereOrLike(dao.AdxHotRanking.Columns().ContractorList, "%"+item.PublisherName+"%")
					//if i == len(req.ContractorList)-1 {
					//	josnStr += fmt.Sprintf(" or contractor_list like '%"+req.ContractorList[0].PublisherName+"%' ) )", item.PublisherName)
					//}
					//josnStr += fmt.Sprintf(" or contractor_list like '%"+req.ContractorList[0].PublisherName+"%' ) ", item.PublisherName)
				}
			}

			//m = m.Where(josnStr)
		}
		// req.RelatedPartyList
		if len(req.RelatedPartyList) > 0 {

			//if len(req.RelatedPartyList) == 1 {
			//	josnStr += fmt.Sprintf("JSON_CONTAINS(related_party_list, '{\"publisherName\":\"%s\"}')", req.RelatedPartyList[0].PublisherName)
			//} else {
			//	for i, item := range req.RelatedPartyList {
			//		if i == 0 {
			//			josnStr += fmt.Sprintf("( JSON_CONTAINS(related_party_list, '{\"publisherName\":\"%s\"}')", item.PublisherName)
			//		}
			//		if i == len(req.RelatedPartyList)-1 {
			//			josnStr += fmt.Sprintf(" or JSON_CONTAINS(related_party_list, '{\"publisherName\":\"%s\"}') )", item.PublisherName)
			//		} else {
			//			josnStr += fmt.Sprintf(" or JSON_CONTAINS(related_party_list, '{\"publisherName\":\"%s\"}') ", item.PublisherName)
			//		}
			//	}
			//}
			//m = m.Where(josnStr)
			if len(req.RelatedPartyList) == 1 {
				//josnStr += fmt.Sprintf("contractor_list like '%" + req.ContractorList[0].PublisherName + "%' ")
				m = m.WhereLike(dao.AdxHotRanking.Columns().RelatedPartyList, "%"+req.RelatedPartyList[0].PublisherName+"%")
			} else {
				for i, item := range req.RelatedPartyList {
					if i == 0 {
						//josnStr += fmt.Sprintf("( contractor_list like '%"+req.ContractorList[0].PublisherName+"%')", item.PublisherName)
						m = m.WhereLike(dao.AdxHotRanking.Columns().RelatedPartyList, "%"+item.PublisherName+"%")
						continue
					}
					m = m.WhereOrLike(dao.AdxHotRanking.Columns().RelatedPartyList, "%"+item.PublisherName+"%")
					//if i == len(req.ContractorList)-1 {
					//	josnStr += fmt.Sprintf(" or contractor_list like '%"+req.ContractorList[0].PublisherName+"%' ) )", item.PublisherName)
					//}
					//josnStr += fmt.Sprintf(" or contractor_list like '%"+req.ContractorList[0].PublisherName+"%' ) ", item.PublisherName)
				}
			}
		}

		//err = m.Count()
		total := 0
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "consumeNum DESC"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		} else {
			req.OrderBy = "consumeNum"
			req.OrderType = "DESC"
		}
		m = m.Fields(fmt.Sprintf("RANK() OVER (ORDER BY  SUM(%s) %s) AS ranking", libUtils.CamelToSnake(req.OrderBy), req.OrderType))
		var res []*model.AdxHotRankingListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).ScanAndCount(&res, &total, false)
		listRes.Total = total
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxHotRankingListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxHotRankingListRes{
				Ranking:             v.Ranking,
				PlayletId:           v.PlayletId,
				PlayletName:         v.PlayletName,
				ConsumeNum:          v.ConsumeNum,
				TotalConsumeNum:     v.TotalConsumeNum,
				NewFlag:             v.NewFlag,
				PlayletTags:         v.PlayletTags,
				CopyrightHolderList: v.CopyrightHolderList,
				ContractorList:      v.ContractorList,
				RelatedPartyList:    v.RelatedPartyList,
			}
		}
	})
	return
}

// 拉取全量数据
func (s *sAdxHotRanking) PullAllData(ctx context.Context, startTime, endTime string) (err error) {
	if libUtils.CompareDateString(startTime, endTime) > 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		if libUtils.CompareDateString(endTime, "2023-01-01") >= 0 {
			for stateDate := startTime; stateDate != libUtils.StringTimeAddDay(endTime, 1); stateDate = libUtils.StringTimeAddDay(stateDate, 1) {
				_ = s.PullIncrementalData(ctx, stateDate)
			}
		}
	})
	return
}

// 拉取增量数据
func (s *sAdxHotRanking) PullIncrementalData(ctx context.Context, date string) (err error) {
	channelRechargeStatKey := consts.ADXHotRankingData + date
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullIncrementalData ")
		return
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = g.Try(innerCtx, func(innerCtx context.Context) {
		response, err := api.GetAdxApiClient().HotRankingService.SetReq(api.HotRankingRequest{
			Day: date,
		}).Do()
		if err != nil {
			g.Log().Errorf(innerCtx, "PullIncrementalData失败: %v", err)
		}
		if response.Msg == "Success" && len(response.Content) > 0 {
			listRes := make([]*model.AdxHotRankingAddReq, 0, len(response.Content))
			for _, item := range response.Content {
				newFlag := 0
				if item.NewFlag {
					newFlag = 1
				}
				contractorList := make([]model.Contractor, 0, len(item.ContractorList))
				for _, item2 := range item.ContractorList {
					contractorList = append(contractorList, model.Contractor{
						PublisherName: item2.PublisherName,
					})
				}
				relatedPartyList := make([]model.RelatedParty, 0, len(item.RelatedPartyList))
				for _, item3 := range item.RelatedPartyList {
					relatedPartyList = append(relatedPartyList, model.RelatedParty{
						PublisherName: item3.PublisherName,
					})
				}

				copyrightHolderList := make([]string, 0, len(item.CopyrightHolderList))
				for _, item4 := range item.CopyrightHolderList {
					copyrightHolderList = append(copyrightHolderList, item4.PublisherName)
				}

				listRes = append(listRes, &model.AdxHotRankingAddReq{
					PlayletId:           uint64(item.PlayletId),
					PlayletName:         item.PlayletName,
					BusinessDate:        date,
					Ranking:             uint(item.Ranking),
					ConsumeNum:          item.ConsumeNum,
					TotalConsumeNum:     item.TotalConsumeNum,
					NewFlag:             newFlag,
					PlayletTags:         item.PlayletTags,
					CopyrightHolderList: copyrightHolderList,
					ContractorList:      contractorList,
					RelatedPartyList:    relatedPartyList,
				})
			}
			err = s.Save(innerCtx, listRes)
		}
	})

	return
}

func (s *sAdxHotRanking) GetExportData(ctx context.Context, req *model.AdxHotRankingSearchReq) (listRes []*model.AdxHotRankingInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxHotRanking.Ctx(ctx).WithAll()
		if req.PlayletId != "" {
			m = m.Where(dao.AdxHotRanking.Columns().PlayletId+" = ?", req.PlayletId)
		}
		if req.PlayletName != "" {
			m = m.Where(dao.AdxHotRanking.Columns().PlayletName+" like ?", "%"+req.PlayletName+"%")
		}
		if req.Ranking != "" {
			m = m.Where(dao.AdxHotRanking.Columns().Ranking+" = ?", gconv.Uint(req.Ranking))
		}
		if req.ConsumeNum != "" {
			m = m.Where(dao.AdxHotRanking.Columns().ConsumeNum+" = ?", gconv.Int64(req.ConsumeNum))
		}
		if req.TotalConsumeNum != "" {
			m = m.Where(dao.AdxHotRanking.Columns().TotalConsumeNum+" = ?", gconv.Int64(req.TotalConsumeNum))
		}
		if req.NewFlag != "" {
			m = m.Where(dao.AdxHotRanking.Columns().NewFlag+" = ?", gconv.Int(req.NewFlag))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "playlet_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxHotRanking) GetByPlayletId(ctx context.Context, playletId uint64) (res *model.AdxHotRankingInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxHotRanking.Ctx(ctx).WithAll().Where(dao.AdxHotRanking.Columns().PlayletId, playletId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxHotRanking) Add(ctx context.Context, req *model.AdxHotRankingAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxHotRanking.Ctx(ctx).Insert(do.AdxHotRanking{
			PlayletId:        req.PlayletId,
			BusinessDate:     req.BusinessDate,
			PlayletName:      req.PlayletName,
			Ranking:          req.Ranking,
			ConsumeNum:       req.ConsumeNum,
			TotalConsumeNum:  req.TotalConsumeNum,
			NewFlag:          req.NewFlag,
			PlayletTags:      req.PlayletTags,
			ContractorList:   req.ContractorList,
			RelatedPartyList: req.RelatedPartyList,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxHotRanking) Save(ctx context.Context, req []*model.AdxHotRankingAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxHotRanking.Ctx(ctx).Save(&req)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxHotRanking) Edit(ctx context.Context, req *model.AdxHotRankingEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxHotRanking.Ctx(ctx).WherePri(req.PlayletId).Update(do.AdxHotRanking{
			PlayletName:      req.PlayletName,
			Ranking:          req.Ranking,
			BusinessDate:     req.BusinessDate,
			ConsumeNum:       req.ConsumeNum,
			TotalConsumeNum:  req.TotalConsumeNum,
			NewFlag:          req.NewFlag,
			PlayletTags:      req.PlayletTags,
			ContractorList:   req.ContractorList,
			RelatedPartyList: req.RelatedPartyList,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxHotRanking) Delete(ctx context.Context, playletIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxHotRanking.Ctx(ctx).Delete(dao.AdxHotRanking.Columns().PlayletId+" in (?)", playletIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
