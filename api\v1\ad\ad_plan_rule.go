// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-27 11:18:34
// 生成路径: api/v1/ad/ad_plan_rule.go
// 生成人：cq
// desc:广告计划规则设置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdPlanRuleSearchReq 分页请求参数
type AdPlanRuleSearchReq struct {
	g.Meta `path:"/list" tags:"广告计划规则设置" method:"get" summary:"广告计划规则设置列表"`
	commonApi.Author
	model.AdPlanRuleSearchReq
}

// AdPlanRuleSearchRes 列表返回结果
type AdPlanRuleSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanRuleSearchRes
}

// AdPlanRuleAddReq 添加操作请求参数
type AdPlanRuleAddReq struct {
	g.Meta `path:"/add" tags:"广告计划规则设置" method:"post" summary:"广告计划规则设置添加"`
	commonApi.Author
	*model.AdPlanRuleAddReq
}

// AdPlanRuleAddRes 添加操作返回结果
type AdPlanRuleAddRes struct {
	commonApi.EmptyRes
}

// AdPlanRuleEditReq 修改操作请求参数
type AdPlanRuleEditReq struct {
	g.Meta `path:"/edit" tags:"广告计划规则设置" method:"put" summary:"广告计划规则设置修改"`
	commonApi.Author
	*model.AdPlanRuleEditReq
}

// AdPlanRuleEditRes 修改操作返回结果
type AdPlanRuleEditRes struct {
	commonApi.EmptyRes
}

// AdPlanRuleGetReq 获取一条数据请求
type AdPlanRuleGetReq struct {
	g.Meta `path:"/get" tags:"广告计划规则设置" method:"get" summary:"获取广告计划规则设置信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPlanRuleGetRes 获取一条数据结果
type AdPlanRuleGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanRuleInfoRes
}

// AdPlanRuleDeleteReq 删除数据请求
type AdPlanRuleDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告计划规则设置" method:"post" summary:"删除广告计划规则设置"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPlanRuleDeleteRes 删除数据返回
type AdPlanRuleDeleteRes struct {
	commonApi.EmptyRes
}
