// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: internal/app/ad/controller/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type ksAdOrderDetailController struct {
	systemController.BaseController
}

var KsAdOrderDetail = new(ksAdOrderDetailController)

// List 列表
func (c *ksAdOrderDetailController) List(ctx context.Context, req *ad.KsAdOrderDetailSearchReq) (res *ad.KsAdOrderDetailSearchRes, err error) {
	res = new(ad.KsAdOrderDetailSearchRes)
	res.KsAdOrderDetailSearchRes, err = service.KsAdOrderDetail().List(ctx, &req.KsAdOrderDetailSearchReq)
	return
}

// Export 导出excel
func (c *ksAdOrderDetailController) Export(ctx context.Context, req *ad.KsAdOrderDetailExportReq) (res *ad.KsAdOrderDetailExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.KsAdOrderDetailListRes
		//表头
		tableHead = []interface{}{"快手账号", "快手账号id", "订单ID", "订单类型", "购买时间", "结算时间", "版权商UID", "版权商", "支付渠道", "短剧名称",
			"总收入(元)", "订单金额(元)", "分成比例", "分销分成比例", "分成金额(元)", "总支出(元)", "退款金额(元)", "佣金(元)"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.KsAdOrderDetail().GetExportData(ctx, &req.KsAdOrderDetailSearchReq)
		if err != nil {
			return
		}
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.AdvertiserName,
				v.AdvertiserId,
				v.OrderId,
				v.OrderType,
				v.PayDate,
				v.SettleDate,
				v.CopyrightUid,
				v.CopyrightName,
				v.PayProvider,
				v.SeriesName,
				v.Income,
				v.PayAmt,
				v.SalerRateStr,
				v.SettleRate,
				v.SettleAmt,
				v.Expenditure,
				v.RedundPrice,
				v.CommissionPrice,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("快手订单结算明细")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取快手订单结算明细
func (c *ksAdOrderDetailController) Get(ctx context.Context, req *ad.KsAdOrderDetailGetReq) (res *ad.KsAdOrderDetailGetRes, err error) {
	res = new(ad.KsAdOrderDetailGetRes)
	res.KsAdOrderDetailInfoRes, err = service.KsAdOrderDetail().GetById(ctx, req.Id)
	return
}

// Add 添加快手订单结算明细
func (c *ksAdOrderDetailController) Add(ctx context.Context, req *ad.KsAdOrderDetailAddReq) (res *ad.KsAdOrderDetailAddRes, err error) {
	err = service.KsAdOrderDetail().Add(ctx, req.KsAdOrderDetailAddReq)
	return
}

// Edit 修改快手订单结算明细
func (c *ksAdOrderDetailController) Edit(ctx context.Context, req *ad.KsAdOrderDetailEditReq) (res *ad.KsAdOrderDetailEditRes, err error) {
	err = service.KsAdOrderDetail().Edit(ctx, req.KsAdOrderDetailEditReq)
	return
}

// Delete 删除快手订单结算明细
func (c *ksAdOrderDetailController) Delete(ctx context.Context, req *ad.KsAdOrderDetailDeleteReq) (res *ad.KsAdOrderDetailDeleteRes, err error) {
	err = service.KsAdOrderDetail().Delete(ctx, req.Ids)
	return
}

// RunCalcAdOrderDetailStat 快手订单结算明细任务
func (c *ksAdOrderDetailController) RunCalcAdOrderDetailStat(ctx context.Context, req *ad.KsAdOrderDetailTaskReq) (res *ad.KsAdOrderDetailTaskRes, err error) {
	err = service.KsAdOrderDetail().RunCalcAdOrderDetailStat(ctx, &req.KsAdOrderDetailSearchReq)
	return
}
