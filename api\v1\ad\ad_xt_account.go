// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: api/v1/ad/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdXtAccountSearchReq 分页请求参数
type AdXtAccountSearchReq struct {
	g.Meta `path:"/list" tags:"广告星图账户表格" method:"get" summary:"广告星图账户表格列表"`
	commonApi.Author
	model.AdXtAccountSearchReq
}

// AdXtAccountSearchRes 列表返回结果
type AdXtAccountSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtAccountSearchRes
}

// AdXtAccountExportReq 导出请求
type AdXtAccountExportReq struct {
	g.Meta `path:"/export" tags:"广告星图账户表格" method:"get" summary:"广告星图账户表格导出"`
	commonApi.Author
	model.AdXtAccountSearchReq
}

// AdXtAccountExportRes 导出响应
type AdXtAccountExportRes struct {
	commonApi.EmptyRes
}

// AdXtAccountAddReq 添加操作请求参数
type AdXtAccountAddReq struct {
	g.Meta `path:"/add" tags:"广告星图账户表格" method:"post" summary:"广告星图账户表格添加"`
	commonApi.Author
	*model.AdXtAccountAddReq
}

// AdXtAccountAddRes 添加操作返回结果
type AdXtAccountAddRes struct {
	commonApi.EmptyRes
}

// AdXtAccountEditReq 修改操作请求参数
type AdXtAccountEditReq struct {
	g.Meta `path:"/edit" tags:"广告星图账户表格" method:"put" summary:"广告星图账户表格修改"`
	commonApi.Author
	*model.AdXtAccountEditReq
}

// AdXtAccountEditRes 修改操作返回结果
type AdXtAccountEditRes struct {
	commonApi.EmptyRes
}

// AdXtAccountGetReq 获取一条数据请求
type AdXtAccountGetReq struct {
	g.Meta `path:"/get" tags:"广告星图账户表格" method:"get" summary:"获取广告星图账户表格信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdXtAccountGetRes 获取一条数据结果
type AdXtAccountGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtAccountInfoRes
}

// AdXtAccountDeleteReq 删除数据请求
type AdXtAccountDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告星图账户表格" method:"delete" summary:"删除广告星图账户表格"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdXtAccountDeleteRes 删除数据返回
type AdXtAccountDeleteRes struct {
	commonApi.EmptyRes
}
