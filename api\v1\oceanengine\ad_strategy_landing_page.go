// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:25
// 生成路径: api/v1/oceanengine/ad_strategy_landing_page.go
// 生成人：gfast
// desc:巨量广告搭建-落地页相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyLandingPageSearchReq 分页请求参数
type AdStrategyLandingPageSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-落地页" method:"get" summary:"巨量广告搭建-落地页列表"`
	commonApi.Author
	model.AdStrategyLandingPageSearchReq
}

// AdStrategyLandingPageSearchRes 列表返回结果
type AdStrategyLandingPageSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyLandingPageSearchRes
}

// AdStrategyLandingPageAddReq 添加操作请求参数
type AdStrategyLandingPageAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-落地页" method:"post" summary:"巨量广告搭建-落地页添加"`
	commonApi.Author
	*model.AdStrategyLandingPageAddReq
}

// AdStrategyLandingPageAddRes 添加操作返回结果
type AdStrategyLandingPageAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyLandingPageEditReq 修改操作请求参数
type AdStrategyLandingPageEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-落地页" method:"put" summary:"巨量广告搭建-落地页修改"`
	commonApi.Author
	*model.AdStrategyLandingPageEditReq
}

// AdStrategyLandingPageEditRes 修改操作返回结果
type AdStrategyLandingPageEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyLandingPageGetReq 获取一条数据请求
type AdStrategyLandingPageGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-落地页" method:"get" summary:"获取巨量广告搭建-落地页信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyLandingPageGetRes 获取一条数据结果
type AdStrategyLandingPageGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyLandingPageInfoRes
}

// AdStrategyLandingPageDeleteReq 删除数据请求
type AdStrategyLandingPageDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-落地页" method:"delete" summary:"删除巨量广告搭建-落地页"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyLandingPageDeleteRes 删除数据返回
type AdStrategyLandingPageDeleteRes struct {
	commonApi.EmptyRes
}

// AdStrategyGetOrangeSiteReq 通过优化目标获取橙子落地页站点信息请求
type AdStrategyGetOrangeSiteReq struct {
	g.Meta `path:"/getOrangeSite" tags:"巨量广告搭建-落地页" method:"post" summary:"巨量广告搭建-通过优化目标获取橙子落地页站点信息"`
	commonApi.Author
	*model.AdStrategyGetOrangeSiteReq
}

// AdStrategyGetOrangeSiteRes 通过优化目标获取橙子落地页站点信息返回
type AdStrategyGetOrangeSiteRes struct {
	commonApi.EmptyRes
	*model.AdStrategyGetOrangeSiteRes
}
