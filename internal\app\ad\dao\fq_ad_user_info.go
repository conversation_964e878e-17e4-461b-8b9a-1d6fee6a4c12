// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: internal/app/ad/dao/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdUserInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdUserInfoDao struct {
	*internal.FqAdUserInfoDao
}

var (
	// FqAdUserInfo is globally public accessible object for table tools_gen_table operations.
	FqAdUserInfo = fqAdUserInfoDao{
		internal.NewFqAdUserInfoDao(),
	}
	FqAdUserInfoAnalytic = fqAdUserInfoDao{
		internal.NewFqAdUserInfoAnalyticDao(),
	}
)

// Fill with you ideas below.
