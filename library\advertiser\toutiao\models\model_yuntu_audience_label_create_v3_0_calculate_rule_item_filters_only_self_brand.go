/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand
type YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand int64

// List of yuntu_audience_label_create_v3.0_calculate_rule_item_filters_only_self_brand
const (
	Enum_0_YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand = 0
	Enum_1_YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand = 1
)

// Ptr returns reference to yuntu_audience_label_create_v3.0_calculate_rule_item_filters_only_self_brand value
func (v YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand) Ptr() *YuntuAudienceLabelCreateV30CalculateRuleItemFiltersOnlySelfBrand {
	return &v
}
