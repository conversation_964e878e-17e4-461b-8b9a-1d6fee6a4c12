// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: internal/app/ad/model/entity/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAccountSeries is the golang structure for table ks_account_series.
type KsAccountSeries struct {
	gmeta.Meta   `orm:"table:ks_account_series"`
	AdvertiserId int64  `orm:"advertiser_id,primary" json:"advertiserId"` // 用户快手号id/广告主id
	SeriesId     int64  `orm:"series_id,primary" json:"seriesId"`         // 短剧id
	SeriesName   string `orm:"series_name" json:"seriesName"`             // 短剧名称
}
