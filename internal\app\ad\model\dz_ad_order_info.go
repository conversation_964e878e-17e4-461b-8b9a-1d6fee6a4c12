// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:38
// 生成路径: internal/app/ad/model/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdOrderInfoInfoRes is the golang structure for table dz_ad_order_info.
type DzAdOrderInfoInfoRes struct {
	gmeta.Meta    `orm:"table:dz_ad_order_info"`
	Id            uint64      `orm:"id,primary" json:"id" dc:"商户单号"`                                   // 商户单号
	Ver           string      `orm:"ver" json:"ver" dc:"小程序版本号"`                                     // 小程序版本号
	OutTradeNo    string      `orm:"out_trade_no" json:"outTradeNo" dc:"交易单号"`                         // 交易单号
	Discount      float64     `orm:"discount" json:"discount" dc:"订单金额（单位元）"`                       // 订单金额（单位元）
	Type          int         `orm:"type" json:"type" dc:"订单类型（1.看点充值 2.VIP）"`                     // 订单类型（1.看点充值 2.VIP）
	StatusNotify  int         `orm:"status_notify" json:"statusNotify" dc:"订单状态（0.待支付 1.支付成功）"` // 订单状态（0.待支付 1.支付成功）
	Ctime         int64       `orm:"ctime" json:"ctime" dc:"下单时间（秒）"`                                 // 下单时间（秒）
	FinishTime    int64       `orm:"finish_time" json:"finishTime" dc:"完成时间（秒）"`                      // 完成时间（秒）
	UserId        int64       `orm:"user_id" json:"userId" dc:"用户ID"`                                    // 用户ID
	ChannelId     string      `orm:"channel_id" json:"channelId" dc:"渠道ID"`                              // 渠道ID
	Domain        int         `orm:"domain" json:"domain" dc:"业务线（14抖音 16微信 17快手）"`               // 业务线（14抖音 16微信 17快手）
	SourceInfo    string      `orm:"source_info" json:"sourceInfo" dc:"短剧ID"`                            // 短剧ID
	ChapterId     string      `orm:"chapter_id" json:"chapterId" dc:"剧集ID"`                              // 剧集ID
	SourceDesc    string      `orm:"source_desc" json:"sourceDesc" dc:"短剧名称"`                          // 短剧名称
	RegisterDate  string      `orm:"register_date" json:"registerDate" dc:"注册时间（秒）"`                  // 注册时间（秒）
	OpenId        string      `orm:"open_id" json:"openId" dc:"小程序openId"`                              // 小程序openId
	Os            string      `orm:"os" json:"os" dc:"机型（0:Android 1:iOS）"`                              // 机型（0:Android 1:iOS）
	ReferralId    string      `orm:"referral_id" json:"referralId" dc:"推广链接ID"`                        // 推广链接ID
	Adid          string      `orm:"adid" json:"adid" dc:"计划ID"`                                         // 计划ID
	FromDrId      string      `orm:"from_dr_id" json:"fromDrId" dc:"达人ID"`                               // 达人ID
	Platform      string      `orm:"platform" json:"platform" dc:"达人平台"`                               // 达人平台
	Scene         string      `orm:"scene" json:"scene" dc:"进入场景"`                                     // 进入场景
	ThirdCorpId   string      `orm:"third_corp_id" json:"thirdCorpId" dc:"三方来源企微主体"`               // 三方来源企微主体
	ThirdWxId     string      `orm:"third_wx_id" json:"thirdWxId" dc:"三方企微唯一标识"`                   // 三方企微唯一标识
	KdrId         string      `orm:"kdr_id" json:"kdrId" dc:"挂载达人ID"`                                  // 挂载达人ID
	SelfReturn    string      `orm:"self_return" json:"selfReturn" dc:"自归因"`                            // 自归因
	ProjectId     string      `orm:"project_id" json:"projectId" dc:"头条2.0参数 - 项目ID"`                // 头条2.0参数 - 项目ID
	PromotionId   string      `orm:"promotion_id" json:"promotionId" dc:"头条2.0参数 - 推广ID"`            // 头条2.0参数 - 推广ID
	SchannelTime  string      `orm:"schannel_time" json:"schannelTime" dc:"抖音快手注册时间"`              // 抖音快手注册时间
	DyeTime       string      `orm:"dye_time" json:"dyeTime" dc:"抖音快手推广链接染色时间"`                // 抖音快手推广链接染色时间
	XuniPay       string      `orm:"xuni_pay" json:"xuniPay" dc:"虚拟支付订单（1=是）"`                      // 虚拟支付订单（1=是）
	MoneyBenefit  string      `orm:"money_benefit" json:"moneyBenefit" dc:"渠道分成金额"`                  // 渠道分成金额
	Mid1          string      `orm:"mid1" json:"mid1" dc:"素材ID - 图片"`                                  // 素材ID - 图片
	Mid2          string      `orm:"mid2" json:"mid2" dc:"素材ID - 标题"`                                  // 素材ID - 标题
	Mid3          string      `orm:"mid3" json:"mid3" dc:"素材ID - 视频"`                                  // 素材ID - 视频
	UnionId       string      `orm:"union_id" json:"unionId" dc:"小程序unionId"`                           // 小程序unionId
	From          string      `orm:"from" json:"from" dc:"投放媒体（如bd/dy/ks等）"`                         // 投放媒体（如bd/dy/ks等）
	OrderSubType  string      `orm:"order_sub_type" json:"orderSubType" dc:"订单业务类型（如整本购）"`       // 订单业务类型（如整本购）
	WxFinderId    string      `orm:"wx_finder_id" json:"wxFinderId" dc:"视频号ID"`                         // 视频号ID
	WxExportId    string      `orm:"wx_export_id" json:"wxExportId" dc:"视频ID"`                           // 视频ID
	WxPromotionId string      `orm:"wx_promotion_id" json:"wxPromotionId" dc:"加热订单ID"`                 // 加热订单ID
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                            // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                            // 更新时间
}

type DzAdOrderInfoListRes struct {
	CreateTime      string  `json:"createTime" dc:"下单时间"`
	PayTime         string  `json:"payTime" dc:"支付时间"`
	DzAccount       string  `json:"dzAccount" dc:"点众账号"`
	DzChannel       string  `json:"dzChannel" dc:"点众渠道"`
	DistributorName string  `json:"distributorName" dc:"分销"`
	UserName        string  `json:"userName" dc:"投手"`
	OutTradeNo      string  `json:"outTradeNo" dc:"交易单号"`
	Type            int     `json:"type" dc:"订单类型（1.看点充值 2.VIP）"`
	StatusNotify    int     `json:"statusNotify" dc:"订单状态（0.待支付 1.支付成功）"`
	Discount        float64 `json:"discount" dc:"订单金额（单位元）"`
	MoneyBenefit    string  `json:"moneyBenefit" dc:"渠道分成金额"`
	ReferralId      string  `json:"referralId" dc:"广告ID（推广链接id）"`
	UserId          int64   `json:"userId" dc:"用户ID"`
	AppType         string  `json:"appType" dc:"小程序类型"`
	RegisterDate    string  `json:"registerDate" dc:"注册时间"`
	DyeTime         string  `json:"dyeTime" dc:"染色时间"`
	From            string  `json:"from" dc:"投放媒体（如bd/dy/ks等）"`
	SourceDesc      string  `json:"sourceDesc" dc:"来源短剧"`
	Os              string  `json:"os" dc:"机型（0:Android 1:iOS）"`
	FromDrId        string  `json:"fromDrId" dc:"达人ID"`

	ChannelId string `json:"channelId" dc:"渠道ID"`

	//Id            uint64      `json:"id" dc:"商户单号"`
	//Ver           string      `json:"ver" dc:"小程序版本号"`
	Ctime      int64 `json:"ctime" dc:"下单时间（秒）"`
	FinishTime int64 `json:"finishTime" dc:"完成时间（秒）"`

	//Domain        int         `json:"domain" dc:"业务线（14抖音 16微信 17快手）"`
	//SourceInfo    string      `json:"sourceInfo" dc:"短剧ID"`
	//ChapterId     string      `json:"chapterId" dc:"剧集ID"`
	OpenId string `json:"openId" dc:"小程序openId"`
	//Adid          string      `json:"adid" dc:"计划ID"`
	//Platform      string      `json:"platform" dc:"达人平台"`
	//Scene         string      `json:"scene" dc:"进入场景"`
	//ThirdCorpId   string      `json:"thirdCorpId" dc:"三方来源企微主体"`
	//ThirdWxId     string      `json:"thirdWxId" dc:"三方企微唯一标识"`
	//KdrId         string      `json:"kdrId" dc:"挂载达人ID"`
	//SelfReturn    string      `json:"selfReturn" dc:"自归因"`
	//ProjectId     string      `json:"projectId" dc:"头条2.0参数 - 项目ID"`
	//PromotionId   string      `json:"promotionId" dc:"头条2.0参数 - 推广ID"`
	//SchannelTime  string      `json:"schannelTime" dc:"抖音快手注册时间"`
	//XuniPay       string         `json:"xuniPay" dc:"虚拟支付订单（1=是）"`
	//Mid1          string      `json:"mid1" dc:"素材ID - 图片"`
	//Mid2          string      `json:"mid2" dc:"素材ID - 标题"`
	//Mid3          string      `json:"mid3" dc:"素材ID - 视频"`
	//UnionId       string      `json:"unionId" dc:"小程序unionId"`
	//OrderSubType  string      `json:"orderSubType" dc:"订单业务类型（如整本购）"`
	//WxFinderId    string      `json:"wxFinderId" dc:"视频号ID"`
	//WxExportId    string      `json:"wxExportId" dc:"视频ID"`
	//WxPromotionId string      `json:"wxPromotionId" dc:"加热订单ID"`
	//CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
	//ChannelCode   string      `orm:"channel_code" json:"channelCode" dc:"渠道号"`
}

// DzAdOrderInfoSearchReq 分页请求参数
type DzAdOrderInfoSearchReq struct {
	comModel.PageReq

	AccountIds    []int64 `p:"accountIds" dc:"账户IDs"`
	ChannelIds    []int64 `p:"channelIds" dc:"渠道IDs"`
	DistributorId int     `p:"distributorId" dc:"分销id"`
	//部门id
	DeptIds           []int  `p:"deptIds"`
	PitcherId         int    `p:"pitcherId" dc:"投手id"`
	StartTime         string `p:"startTime" dc:"下单时间"`
	EndTime           string `p:"endTime" dc:"结束下单时间 YYYY-MM-DD格式"`
	PayStartTime      string `p:"payStartTime" dc:"付费开始时间"`
	PayEndTime        string `p:"payEndTime" dc:"付费结束时间"`
	RegisterStartTime string `p:"registerStartTime" dc:"用户注册时间"`
	RegisterEndTime   string `p:"registerEndTime" dc:"用户注册时间"`
	DyeStartTime      string `p:"dyeStartTime" dc:"染色时间"`
	DyeEndTime        string `p:"dyeEndTime" dc:"染色时间"`

	Id           string `p:"id" dc:"商户单号"`                                                                                               //商户单号
	Ver          string `p:"ver" dc:"小程序版本号"`                                                                                          //小程序版本号
	OutTradeNo   string `p:"outTradeNo" dc:"交易单号"`                                                                                       //交易单号
	Discount     string `p:"discount" v:"discount@float#订单金额（单位元）需为浮点数" dc:"订单金额（单位元）"`                                   //订单金额（单位元）
	Type         string `p:"type" v:"type@integer#订单类型（1.看点充值 2.VIP）需为整数" dc:"订单类型（1.看点充值 2.VIP）"`                       //订单类型（1.看点充值 2.VIP）
	StatusNotify string `p:"statusNotify" v:"statusNotify@integer#订单状态（0.待支付 1.支付成功）需为整数" dc:"订单状态（0.待支付 1.支付成功）"` //订单状态（0.待支付 1.支付成功）
	Ctime        string `p:"ctime" v:"ctime@integer#下单时间（秒）需为整数" dc:"下单时间（秒）"`                                                 //下单时间（秒）
	FinishTime   string `p:"finishTime" v:"finishTime@integer#完成时间（秒）需为整数" dc:"完成时间（秒）"`                                       //完成时间（秒）
	UserId       string `p:"userId" v:"userId@integer#用户ID需为整数" dc:"用户ID"`                                                           //用户ID
	ChannelId    string `p:"channelId" dc:"渠道ID"`                                                                                          //渠道ID
	Domain       string `p:"domain" v:"domain@integer#业务线（14抖音 16微信 17快手）需为整数" dc:"业务线（14抖音 16微信 17快手）"`               //业务线（14抖音 16微信 17快手）
	SourceInfo   string `p:"sourceInfo" dc:"短剧ID"`                                                                                         //短剧ID
	ChapterId    string `p:"chapterId" dc:"剧集ID"`                                                                                          //剧集ID
	SourceDesc   string `p:"sourceDesc" dc:"短剧名称"`                                                                                       //短剧名称
	//RegisterDate      string  `p:"registerDate" dc:"注册时间（秒）"`                                                            //注册时间（秒）
	OpenId        string `p:"openId" dc:"小程序openId"`                                                           //小程序openId
	Os            string `p:"os" dc:"机型（0:Android 1:iOS）"`                                                      //机型（0:Android 1:iOS）
	ReferralId    string `p:"referralId" dc:"推广链接ID"`                                                         //推广链接ID
	Adid          string `p:"adid" dc:"计划ID"`                                                                   //计划ID
	FromDrId      string `p:"fromDrId" dc:"达人ID"`                                                               //达人ID
	Platform      string `p:"platform" dc:"达人平台"`                                                             //达人平台
	Scene         string `p:"scene" dc:"进入场景"`                                                                //进入场景
	ThirdCorpId   string `p:"thirdCorpId" dc:"三方来源企微主体"`                                                  //三方来源企微主体
	ThirdWxId     string `p:"thirdWxId" dc:"三方企微唯一标识"`                                                    //三方企微唯一标识
	KdrId         string `p:"kdrId" dc:"挂载达人ID"`                                                              //挂载达人ID
	SelfReturn    string `p:"selfReturn" dc:"自归因"`                                                             //自归因
	ProjectId     string `p:"projectId" dc:"头条2.0参数 - 项目ID"`                                                //头条2.0参数 - 项目ID
	PromotionId   string `p:"promotionId" dc:"头条2.0参数 - 推广ID"`                                              //头条2.0参数 - 推广ID
	SchannelTime  string `p:"schannelTime" dc:"抖音快手注册时间"`                                                 //抖音快手注册时间
	DyeTime       string `p:"dyeTime" dc:"抖音快手推广链接染色时间"`                                              //抖音快手推广链接染色时间
	XuniPay       string `p:"xuniPay" v:"xuniPay@integer#虚拟支付订单（1=是）需为整数" dc:"虚拟支付订单（1=是）"`     //虚拟支付订单（1=是）
	MoneyBenefit  string `p:"moneyBenefit" dc:"渠道分成金额"`                                                     //渠道分成金额
	Mid1          string `p:"mid1" dc:"素材ID - 图片"`                                                            //素材ID - 图片
	Mid2          string `p:"mid2" dc:"素材ID - 标题"`                                                            //素材ID - 标题
	Mid3          string `p:"mid3" dc:"素材ID - 视频"`                                                            //素材ID - 视频
	UnionId       string `p:"unionId" dc:"小程序unionId"`                                                         //小程序unionId
	From          string `p:"from" dc:"投放媒体（如bd/dy/ks等）"`                                                   //投放媒体（如bd/dy/ks等）
	OrderSubType  string `p:"orderSubType" dc:"订单业务类型（如整本购）"`                                           //订单业务类型（如整本购）
	WxFinderId    string `p:"wxFinderId" dc:"视频号ID"`                                                           //视频号ID
	WxExportId    string `p:"wxExportId" dc:"视频ID"`                                                             //视频ID
	WxPromotionId string `p:"wxPromotionId" dc:"加热订单ID"`                                                      //加热订单ID
	CreatedAt     string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// DzAdOrderInfoSearchRes 列表返回结果
type DzAdOrderInfoSearchRes struct {
	comModel.ListRes
	List    []*DzAdOrderInfoListRes `json:"list"`
	Summary *DzAdOrderInfoSummary   `json:"summary"`
}
type DzAdOrderInfoSummary struct {
	Discount float64 `p:"discount"  dc:"订单金额（单位元）"`
}

// DzAdOrderInfoAddReq 添加操作请求参数
type DzAdOrderInfoAddReq struct {
	Id            uint64  `p:"id" v:"required#主键ID不能为空" dc:"商户单号"`
	Ver           string  `p:"ver"  dc:"小程序版本号"`
	OutTradeNo    string  `p:"outTradeNo"  dc:"交易单号"`
	Discount      float64 `p:"discount"  dc:"订单金额（单位元）"`
	Type          int     `p:"type"  dc:"订单类型（1.看点充值 2.VIP）"`
	StatusNotify  int     `p:"statusNotify" v:"required#订单状态（0.待支付 1.支付成功）不能为空" dc:"订单状态（0.待支付 1.支付成功）"`
	Ctime         int64   `p:"ctime"  dc:"下单时间（秒）"`
	FinishTime    int64   `p:"finishTime"  dc:"完成时间（秒）"`
	UserId        int64   `p:"userId"  dc:"用户ID"`
	ChannelId     string  `p:"channelId"  dc:"渠道ID"`
	Domain        int     `p:"domain"  dc:"业务线（14抖音 16微信 17快手）"`
	SourceInfo    string  `p:"sourceInfo"  dc:"短剧ID"`
	ChapterId     string  `p:"chapterId"  dc:"剧集ID"`
	SourceDesc    string  `p:"sourceDesc"  dc:"短剧名称"`
	RegisterDate  string  `p:"registerDate"  dc:"注册时间（秒）"`
	OpenId        string  `p:"openId"  dc:"小程序openId"`
	Os            string  `p:"os"  dc:"机型（0:Android 1:iOS）"`
	ReferralId    string  `p:"referralId"  dc:"推广链接ID"`
	Adid          string  `p:"adid"  dc:"计划ID"`
	FromDrId      string  `p:"fromDrId"  dc:"达人ID"`
	Platform      string  `p:"platform"  dc:"达人平台"`
	Scene         string  `p:"scene"  dc:"进入场景"`
	ThirdCorpId   string  `p:"thirdCorpId"  dc:"三方来源企微主体"`
	ThirdWxId     string  `p:"thirdWxId"  dc:"三方企微唯一标识"`
	KdrId         string  `p:"kdrId"  dc:"挂载达人ID"`
	SelfReturn    string  `p:"selfReturn"  dc:"自归因"`
	ProjectId     string  `p:"projectId"  dc:"头条2.0参数 - 项目ID"`
	PromotionId   string  `p:"promotionId"  dc:"头条2.0参数 - 推广ID"`
	SchannelTime  string  `p:"schannelTime"  dc:"抖音快手注册时间"`
	DyeTime       string  `p:"dyeTime"  dc:"抖音快手推广链接染色时间"`
	XuniPay       string  `p:"xuniPay"  dc:"虚拟支付订单（1=是）"`
	MoneyBenefit  string  `p:"moneyBenefit"  dc:"渠道分成金额"`
	Mid1          string  `p:"mid1"  dc:"素材ID - 图片"`
	Mid2          string  `p:"mid2"  dc:"素材ID - 标题"`
	Mid3          string  `p:"mid3"  dc:"素材ID - 视频"`
	UnionId       string  `p:"unionId"  dc:"小程序unionId"`
	From          string  `p:"from"  dc:"投放媒体（如bd/dy/ks等）"`
	OrderSubType  string  `p:"orderSubType"  dc:"订单业务类型（如整本购）"`
	WxFinderId    string  `p:"wxFinderId"  dc:"视频号ID"`
	WxExportId    string  `p:"wxExportId"  dc:"视频ID"`
	WxPromotionId string  `p:"wxPromotionId"  dc:"加热订单ID"`
}

type DzAdOrderInfoCallbackReq struct {
	//taskId
	TaskId string `p:"taskId" v:"required#任务ID不能为空" dc:"任务ID"`
	//xurl
	Xurl string `p:"xurl" v:"required#回调地址不能为空" dc:"回调地址"`
}

// DzAdOrderInfoEditReq 修改操作请求参数
type DzAdOrderInfoEditReq struct {
	Id            uint64  `p:"id" v:"required#主键ID不能为空" dc:"商户单号"`
	Ver           string  `p:"ver"  dc:"小程序版本号"`
	OutTradeNo    string  `p:"outTradeNo"  dc:"交易单号"`
	Discount      float64 `p:"discount"  dc:"订单金额（单位元）"`
	Type          int     `p:"type"  dc:"订单类型（1.看点充值 2.VIP）"`
	StatusNotify  int     `p:"statusNotify" v:"required#订单状态（0.待支付 1.支付成功）不能为空" dc:"订单状态（0.待支付 1.支付成功）"`
	Ctime         int64   `p:"ctime"  dc:"下单时间（秒）"`
	FinishTime    int64   `p:"finishTime"  dc:"完成时间（秒）"`
	UserId        int64   `p:"userId"  dc:"用户ID"`
	ChannelId     string  `p:"channelId"  dc:"渠道ID"`
	Domain        int     `p:"domain"  dc:"业务线（14抖音 16微信 17快手）"`
	SourceInfo    string  `p:"sourceInfo"  dc:"短剧ID"`
	ChapterId     string  `p:"chapterId"  dc:"剧集ID"`
	SourceDesc    string  `p:"sourceDesc"  dc:"短剧名称"`
	RegisterDate  string  `p:"registerDate"  dc:"注册时间（秒）"`
	OpenId        string  `p:"openId"  dc:"小程序openId"`
	Os            string  `p:"os"  dc:"机型（0:Android 1:iOS）"`
	ReferralId    string  `p:"referralId"  dc:"推广链接ID"`
	Adid          string  `p:"adid"  dc:"计划ID"`
	FromDrId      string  `p:"fromDrId"  dc:"达人ID"`
	Platform      string  `p:"platform"  dc:"达人平台"`
	Scene         string  `p:"scene"  dc:"进入场景"`
	ThirdCorpId   string  `p:"thirdCorpId"  dc:"三方来源企微主体"`
	ThirdWxId     string  `p:"thirdWxId"  dc:"三方企微唯一标识"`
	KdrId         string  `p:"kdrId"  dc:"挂载达人ID"`
	SelfReturn    string  `p:"selfReturn"  dc:"自归因"`
	ProjectId     string  `p:"projectId"  dc:"头条2.0参数 - 项目ID"`
	PromotionId   string  `p:"promotionId"  dc:"头条2.0参数 - 推广ID"`
	SchannelTime  string  `p:"schannelTime"  dc:"抖音快手注册时间"`
	DyeTime       string  `p:"dyeTime"  dc:"抖音快手推广链接染色时间"`
	XuniPay       string  `p:"xuniPay"  dc:"虚拟支付订单（1=是）"`
	MoneyBenefit  string  `p:"moneyBenefit"  dc:"渠道分成金额"`
	Mid1          string  `p:"mid1"  dc:"素材ID - 图片"`
	Mid2          string  `p:"mid2"  dc:"素材ID - 标题"`
	Mid3          string  `p:"mid3"  dc:"素材ID - 视频"`
	UnionId       string  `p:"unionId"  dc:"小程序unionId"`
	From          string  `p:"from"  dc:"投放媒体（如bd/dy/ks等）"`
	OrderSubType  string  `p:"orderSubType"  dc:"订单业务类型（如整本购）"`
	WxFinderId    string  `p:"wxFinderId"  dc:"视频号ID"`
	WxExportId    string  `p:"wxExportId"  dc:"视频ID"`
	WxPromotionId string  `p:"wxPromotionId"  dc:"加热订单ID"`
}
