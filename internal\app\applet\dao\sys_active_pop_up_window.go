// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-09-02 14:44:05
// 生成路径: internal/app/applet/dao/sys_active_pop_up_window.go
// 生成人：cyao
// desc:活动弹出窗口
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sysActivePopUpWindowDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sysActivePopUpWindowDao struct {
	*internal.SysActivePopUpWindowDao
}

var (
	// SysActivePopUpWindow is globally public accessible object for table tools_gen_table operations.
	SysActivePopUpWindow = sysActivePopUpWindowDao{
		internal.NewSysActivePopUpWindowDao(),
	}
)

// Fill with you ideas below.
