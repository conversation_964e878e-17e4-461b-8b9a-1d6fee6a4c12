// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-12 16:58:16
// 生成路径: internal/app/ad/dao/internal/ad_material.go
// 生成人：cyao
// desc:素材主表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMaterialDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdMaterialDao struct {
	table   string            // Table is the underlying table name of the DAO.
	group   string            // Group is the database configuration group name of current DAO.
	columns AdMaterialColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdMaterialColumns defines and stores column names for table ad_material.
type AdMaterialColumns struct {
	MaterialId    string // 素材id
	AlbumId       string // 专辑id
	FileId        string // 文件夹id
	MaterialName  string // 素材名
	MaterialType  string // material_type 素材类型 video image
	UserId        string // 创建人
	FileUri       string // 文件url地址
	ThumbnailUri  string // 缩略图url / 封面图
	Labels        string // 字符串数组以| 分隔
	FileFormat    string // 文件格式 MP4 JPG 等
	FileSize      string // 文件大小 eg: 413.87M
	Width         string // 尺寸宽
	Height        string // 尺寸高
	Remark        string // 备注
	VideoDuration string // 视频时长
	CreatedAt     string // 创建时间
	DesignUserId  string
	UpdatedAt     string
	DeletedAt     string
}

var adMaterialColumns = AdMaterialColumns{
	MaterialId:    "material_id",
	AlbumId:       "album_id",
	FileId:        "file_id",
	MaterialName:  "material_name",
	MaterialType:  "material_type",
	UserId:        "user_id",
	FileUri:       "file_uri",
	ThumbnailUri:  "thumbnail_uri",
	Labels:        "labels",
	FileFormat:    "file_format",
	FileSize:      "file_size",
	Width:         "width",
	Height:        "height",
	Remark:        "remark",
	VideoDuration: "video_duration",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
	DesignUserId:  "design_user_id",
}

// NewAdMaterialDao creates and returns a new DAO object for table data access.
func NewAdMaterialDao() *AdMaterialDao {
	return &AdMaterialDao{
		group:   "default",
		table:   "ad_material",
		columns: adMaterialColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdMaterialDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdMaterialDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdMaterialDao) Columns() AdMaterialColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdMaterialDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdMaterialDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdMaterialDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
