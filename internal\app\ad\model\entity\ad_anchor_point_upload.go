// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/model/entity/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAnchorPointUpload is the golang structure for table ad_anchor_point_upload.
type AdAnchorPointUpload struct {
	gmeta.Meta `orm:"table:ad_anchor_point_upload"`
	Id         int    `orm:"id,primary" json:"id"`          // ID
	AnchorType string `orm:"anchor_type" json:"anchorType"` // 可选值:
	//APP_GAME             游戏锚点
	//APP_INTERNET_SERVICE 网服锚点
	//APP_SHOP             电商锚点
	//PRIVATE_CHAT         咨询锚点
	//SHOPPING_CART        购物锚点
	AnchorPointId int         `orm:"anchor_point_id" json:"anchorPointId"` // 业务的字段锚点id
	AnchorId      string      `orm:"anchor_id" json:"anchorId"`            // 原生锚点id
	CreateTime    *gtime.Time `orm:"create_time" json:"createTime"`        // 创建时间
}
