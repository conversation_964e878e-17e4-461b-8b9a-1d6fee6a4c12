// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:10
// 生成路径: api/v1/oceanengine/ad_strategy_task_promotion.go
// 生成人：gfast
// desc:巨量广告搭建-任务-广告相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyTaskPromotionSearchReq 分页请求参数
type AdStrategyTaskPromotionSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-任务-广告" method:"get" summary:"巨量广告搭建-任务-广告列表"`
	commonApi.Author
	model.AdStrategyTaskPromotionSearchReq
}

// AdStrategyTaskPromotionSearchRes 列表返回结果
type AdStrategyTaskPromotionSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTaskPromotionSearchRes
}

// AdStrategyTaskPromotionAddReq 添加操作请求参数
type AdStrategyTaskPromotionAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-任务-广告" method:"post" summary:"巨量广告搭建-任务-广告添加"`
	commonApi.Author
	*model.AdStrategyTaskPromotionAddReq
}

// AdStrategyTaskPromotionAddRes 添加操作返回结果
type AdStrategyTaskPromotionAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyTaskPromotionEditReq 修改操作请求参数
type AdStrategyTaskPromotionEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-任务-广告" method:"put" summary:"巨量广告搭建-任务-广告修改"`
	commonApi.Author
	*model.AdStrategyTaskPromotionEditReq
}

// AdStrategyTaskPromotionEditRes 修改操作返回结果
type AdStrategyTaskPromotionEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyTaskPromotionGetReq 获取一条数据请求
type AdStrategyTaskPromotionGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-任务-广告" method:"get" summary:"获取巨量广告搭建-任务-广告信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyTaskPromotionGetRes 获取一条数据结果
type AdStrategyTaskPromotionGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTaskPromotionInfoRes
}

// AdStrategyTaskPromotionDeleteReq 删除数据请求
type AdStrategyTaskPromotionDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-任务-广告" method:"delete" summary:"删除巨量广告搭建-任务-广告"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyTaskPromotionDeleteRes 删除数据返回
type AdStrategyTaskPromotionDeleteRes struct {
	commonApi.EmptyRes
}
