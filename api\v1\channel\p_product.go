// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-07 16:20:39
// 生成路径: api/v1/channel/p_product.go
// 生成人：len
// desc:商品信息主表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
	dyModel "github.com/tiger1103/gfast/v3/library/libDy/model"
)

// PProductSearchReq 分页请求参数
type PProductSearchReq struct {
	g.Meta `path:"/getRecharge" tags:"小程序设置" method:"post" summary:"查询商品充值信息主表列表"`
	commonApi.Author
	model.PProductSearchReq
}

// PProductSearchRes 列表返回结果
type PProductSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.PProductSearchRes
}

// PProductAddReq 添加操作请求参数
type PProductAddReq struct {
	g.Meta `path:"/add" tags:"商品信息主表" method:"post" summary:"商品信息主表添加"`
	commonApi.Author
	*model.PProductAddReq
}

// PProductAddRes 添加操作返回结果
type PProductAddRes struct {
	commonApi.EmptyRes
}

// PProductBatchSettingReq 批量设置操作请求参数
type PProductBatchSettingReq struct {
	g.Meta `path:"/batchSettingRecharge" tags:"小程序设置" method:"post" summary:"充值信息修改"`
	commonApi.Author
	//	list  *model.PProductEditReq
	EditList   []*model.PProductEditReq `p:"editList" `
	AddList    []*model.PProductAddReq  `p:"addList" `
	DeleteList []int                    `p:"deleteList"` //id串 `p:"delList" `

	TemplateName string `p:"templateName"` //模板名称
	TemplateId   int    `p:"templateId"`   //模版id
	Type         int    `p:"type" dc:"模板类型 1：统一模板 2：区分付费"`
}

// PProductEditRes 修改操作返回结果
type PProductEditRes struct {
	commonApi.EmptyRes
	Id           int    `p:"id"` //模版id
	TemplateName string `json:"templateName" dc:"模板名称"`
}

// PProductGetReq 获取一条数据请求
type PProductGetReq struct {
	g.Meta `path:"/get" tags:"商品信息主表" method:"get" summary:"获取商品信息主表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// PProductGetRes 获取一条数据结果
type PProductGetRes struct {
	g.Meta `mime:"application/json"`
	*model.PProductInfoRes
}

// PProductDeleteReq 删除数据请求
type PProductDeleteReq struct {
	g.Meta `path:"/delete" tags:"商品信息主表" method:"post" summary:"删除商品信息主表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// PProductDeleteRes 删除数据返回
type PProductDeleteRes struct {
	commonApi.EmptyRes
}

// SyncPanelInfoToDyReq 同步面板到抖音请求
type SyncPanelInfoToDyReq struct {
	g.Meta `path:"/syncToDy" tags:"商品信息主表" method:"post" summary:"同步面板到抖音"`
	commonApi.Author
	Account string `p:"account"`
}

// SyncPanelInfoToDyRes 同步面板到抖音返回
type SyncPanelInfoToDyRes struct {
	commonApi.EmptyRes
	*dyModel.PanelQueryRes
}

// GetDyPanelInfoReq 获取抖音面板信息请求
type GetDyPanelInfoReq struct {
	g.Meta `path:"/getDyPanelInfo" tags:"商品信息主表" method:"post" summary:"获取抖音面板信息"`
	commonApi.Author
	PanelId string `p:"panelId"`
	AppId   string `p:"appId"`
}

// GetDyPanelInfoRes 获取抖音面板信息返回
type GetDyPanelInfoRes struct {
	commonApi.EmptyRes
}

// SyncPanelInfoToDyByTimeReq 同步面板到抖音请求
type SyncPanelInfoToDyByTimeReq struct {
	g.Meta `path:"/syncToDyByTime" tags:"商品信息主表" method:"post" summary:"同步面板到抖音"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"格式YYYY-MM-DD HH:mm:ss"`
	EndTime   string `p:"endTime" dc:"格式YYYY-MM-DD HH:mm:ss"`
	Account   string `p:"account"`
}

// SyncPanelInfoToDyByTimeRes 同步面板到抖音返回
type SyncPanelInfoToDyByTimeRes struct {
	commonApi.EmptyRes
}
