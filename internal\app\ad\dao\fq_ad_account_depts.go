// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-16 11:16:19
// 生成路径: internal/app/ad/dao/fq_ad_account_depts.go
// 生成人：gfast
// desc:番茄账号权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdAccountDeptsDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdAccountDeptsDao struct {
	*internal.FqAdAccountDeptsDao
}

var (
	// FqAdAccountDepts is globally public accessible object for table tools_gen_table operations.
	FqAdAccountDepts = fqAdAccountDeptsDao{
		internal.NewFqAdAccountDeptsDao(),
	}
)

// Fill with you ideas below.
