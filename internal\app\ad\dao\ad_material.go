// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-12 16:58:16
// 生成路径: internal/app/ad/dao/ad_material.go
// 生成人：cyao
// desc:素材主表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adMaterialDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adMaterialDao struct {
	*internal.AdMaterialDao
}

var (
	// AdMaterial is globally public accessible object for table tools_gen_table operations.
	AdMaterial = adMaterialDao{
		internal.NewAdMaterialDao(),
	}
)

// Fill with you ideas below.
