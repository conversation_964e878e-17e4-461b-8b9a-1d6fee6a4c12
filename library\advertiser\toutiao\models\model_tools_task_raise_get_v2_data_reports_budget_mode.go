/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsTaskRaiseGetV2DataReportsBudgetMode
type ToolsTaskRaiseGetV2DataReportsBudgetMode string

// List of tools_task_raise_get_v2_data_reports_budget_mode
const (
	LIMIT_ToolsTaskRaiseGetV2DataReportsBudgetMode    ToolsTaskRaiseGetV2DataReportsBudgetMode = "LIMIT"
	NO_LIMIT_ToolsTaskRaiseGetV2DataReportsBudgetMode ToolsTaskRaiseGetV2DataReportsBudgetMode = "NO_LIMIT"
)

// Ptr returns reference to tools_task_raise_get_v2_data_reports_budget_mode value
func (v ToolsTaskRaiseGetV2DataReportsBudgetMode) Ptr() *ToolsTaskRaiseGetV2DataReportsBudgetMode {
	return &v
}
