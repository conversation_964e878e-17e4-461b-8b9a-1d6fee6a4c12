/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// FileImageAdV2ApiService FileImageAdV2Api service

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service  上传广告图片
type ToolsSiteGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2ToolsSiteGetGetRequest
}

type ApiOpenApi2ToolsSiteGetGetRequest struct {
	AdvertiserId int64
	Page         int
	PageSize     int
	Status       models.ToolsSiteGetV2Status
	ShareType    models.ToolsSiteGetV2ShareType
	Filtering    models.ToolsSiteGetV2Filtering
}

func (r *ToolsSiteGetV2ApiService) SetCfg(cfg *conf.Configuration) *ToolsSiteGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsSiteGetV2ApiService) SetRequest(req ApiOpenApi2ToolsSiteGetGetRequest) *ToolsSiteGetV2ApiService {
	r.Request = &req
	return r
}

func (r *ToolsSiteGetV2ApiService) AccessToken(accessToken string) *ToolsSiteGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsSiteGetV2ApiService) Do() (data *models.ToolsSiteGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/tools/site/get/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.ToolsSiteGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsSiteGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/info/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
