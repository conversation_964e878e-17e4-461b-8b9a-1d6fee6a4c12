// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: internal/app/ad/controller/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"fmt"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adPlanLogController struct {
	systemController.BaseController
}

var AdPlanLog = new(adPlanLogController)

// List 列表
func (c *adPlanLogController) List(ctx context.Context, req *ad.AdPlanLogSearchReq) (res *ad.AdPlanLogSearchRes, err error) {
	res = new(ad.AdPlanLogSearchRes)
	res.AdPlanLogSearchRes, err = service.AdPlanLog().List(ctx, &req.AdPlanLogSearchReq)
	idsMap := make(map[int][]int64)
	for _, v := range res.AdPlanLogSearchRes.List {
		if _, ok := idsMap[v.ObjectType]; ok {
			idsMap[v.ObjectType] = append(idsMap[v.ObjectType], v.ScopeEntityId)
		} else {
			idsMap[v.ObjectType] = []int64{v.ScopeEntityId}
		}
	}

	for sType, ids := range idsMap {
		switch sType {
		case 1:
			nameMap, _ := oceanengineService.AdAdvertiserAccount().GetAdAccountName(ctx, libUtils.IntToStringSlice2(ids))
			for _, item := range res.AdPlanLogSearchRes.List {
				item.ScopeEntityName = nameMap[fmt.Sprintf("%d", item.ScopeEntityId)]
			}
		case 2:
			nameMap, _ := oceanengineService.AdProject().GetAdProjectName(ctx, libUtils.IntToStringSlice2(ids))
			for _, item := range res.AdPlanLogSearchRes.List {
				item.ScopeEntityName = nameMap[fmt.Sprintf("%d", item.ScopeEntityId)]
			}
		case 3:
			nameMap, _ := oceanengineService.AdPromotion().GetAdPromotionName(ctx, libUtils.IntToStringSlice2(ids))
			for _, item := range res.AdPlanLogSearchRes.List {
				item.ScopeEntityName = nameMap[fmt.Sprintf("%d", item.ScopeEntityId)]
			}
		}
	}

	return
}

// Get 获取广告计划执行日志
func (c *adPlanLogController) Get(ctx context.Context, req *ad.AdPlanLogGetReq) (res *ad.AdPlanLogGetRes, err error) {
	res = new(ad.AdPlanLogGetRes)
	res.AdPlanLogInfoRes, err = service.AdPlanLog().GetById(ctx, req.Id)
	return
}

// Add 添加广告计划执行日志
func (c *adPlanLogController) Add(ctx context.Context, req *ad.AdPlanLogAddReq) (res *ad.AdPlanLogAddRes, err error) {
	err = service.AdPlanLog().Add(ctx, req.AdPlanLogAddReq)
	return
}

// Edit 修改广告计划执行日志
func (c *adPlanLogController) Edit(ctx context.Context, req *ad.AdPlanLogEditReq) (res *ad.AdPlanLogEditRes, err error) {
	err = service.AdPlanLog().Edit(ctx, req.AdPlanLogEditReq)
	return
}

// Delete 删除广告计划执行日志
func (c *adPlanLogController) Delete(ctx context.Context, req *ad.AdPlanLogDeleteReq) (res *ad.AdPlanLogDeleteRes, err error) {
	err = service.AdPlanLog().Delete(ctx, req.Ids)
	return
}
