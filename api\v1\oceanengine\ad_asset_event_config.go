// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-16 15:36:20
// 生成路径: api/v1/oceanengine/ad_asset_event_config.go
// 生成人：cq
// desc:资产-事件-配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetEventConfigSearchReq 分页请求参数
type AdAssetEventConfigSearchReq struct {
	g.Meta `path:"/list" tags:"资产-事件-配置" method:"post" summary:"资产-事件-配置列表"`
	commonApi.Author
	model.AdAssetEventConfigSearchReq
}

// AdAssetEventConfigSearchRes 列表返回结果
type AdAssetEventConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetEventConfigSearchRes
}

// AdAssetEventConfigAddReq 添加操作请求参数
type AdAssetEventConfigAddReq struct {
	g.Meta `path:"/add" tags:"资产-事件-配置" method:"post" summary:"资产-事件-配置添加"`
	commonApi.Author
	*model.AdAssetEventConfigAddReq
}

// AdAssetEventConfigAddRes 添加操作返回结果
type AdAssetEventConfigAddRes struct {
	commonApi.EmptyRes
}

// AdAssetEventConfigEditReq 修改操作请求参数
type AdAssetEventConfigEditReq struct {
	g.Meta `path:"/edit" tags:"资产-事件-配置" method:"put" summary:"资产-事件-配置修改"`
	commonApi.Author
	*model.AdAssetEventConfigEditReq
}

// AdAssetEventConfigEditRes 修改操作返回结果
type AdAssetEventConfigEditRes struct {
	commonApi.EmptyRes
}

// AdAssetEventConfigGetReq 获取一条数据请求
type AdAssetEventConfigGetReq struct {
	g.Meta `path:"/get" tags:"资产-事件-配置" method:"get" summary:"获取资产-事件-配置信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetEventConfigGetRes 获取一条数据结果
type AdAssetEventConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetEventConfigInfoRes
}

// AdAssetEventConfigDeleteReq 删除数据请求
type AdAssetEventConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-事件-配置" method:"post" summary:"删除资产-事件-配置"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetEventConfigDeleteRes 删除数据返回
type AdAssetEventConfigDeleteRes struct {
	commonApi.EmptyRes
}
