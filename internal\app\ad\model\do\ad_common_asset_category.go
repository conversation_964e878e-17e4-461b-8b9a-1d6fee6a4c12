// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: internal/app/ad/model/entity/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdCommonAssetCategory is the golang structure for table ad_common_asset_category.
type AdCommonAssetCategory struct {
	gmeta.Meta `orm:"table:ad_common_asset_category, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`        //
	Category   interface{} `orm:"category" json:"category"`    // 分类
	UserId     interface{} `orm:"user_id" json:"userId"`       // 创建者
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt"` // 删除时间
}
