// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: internal/app/adx/model/entity/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxTaskItem is the golang structure for table adx_task_item.
type AdxTaskItem struct {
	gmeta.Meta    `orm:"table:adx_task_item"`
	Id            int64       `orm:"id,primary" json:"id"`  // 主键ID
	TaskId        int64       `orm:"task_id" json:"taskId"` // 关联任务ID（外键）
	ImgUrl        string      `orm:"img_url" json:"imgUrl"`
	MaterialUrl   string      `orm:"material_url" json:"materialUrl"`     // 素材文件地址
	MaterialType  string      `orm:"material_type" json:"materialType"`   // 素材类型（图片、视频）
	MaterialSize  string      `orm:"material_size" json:"materialSize"`   // 素材尺寸，如1080*1940
	RelatedScript string      `orm:"related_script" json:"relatedScript"` // 关联短剧名称
	OperationTime *gtime.Time `orm:"operation_time" json:"operationTime"` // 操作时间
	Result        string      `orm:"result" json:"result"`                // 执行结果（成功、失败）
	FailReason    string      `orm:"fail_reason" json:"failReason"`       // 失败原因（仅在 result = 失败 时有值）
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`         // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt"`         // 更新时间
}
