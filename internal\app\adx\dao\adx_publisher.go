// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-06-05 11:34:01
// 生成路径: internal/app/adx/dao/adx_publisher.go
// 生成人：cq
// desc:ADX公司信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao/internal"
)

// adxPublisherDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adxPublisherDao struct {
	*internal.AdxPublisherDao
}

var (
	// AdxPublisher is globally public accessible object for table tools_gen_table operations.
	AdxPublisher = adxPublisherDao{
		internal.NewAdxPublisherDao(),
	}
)

// Fill with you ideas below.
