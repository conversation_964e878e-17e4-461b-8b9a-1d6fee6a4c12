// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-03-07 15:31:00
// 生成路径: internal/app/applet/controller/s_customer_qr_code_config.go
// 生成人：cyao
// desc:微信二维码配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sCustomerQrCodeConfigController struct {
	systemController.BaseController
}

var SCustomerQrCodeConfig = new(sCustomerQrCodeConfigController)

// List 列表
func (c *sCustomerQrCodeConfigController) List(ctx context.Context, req *applet.SCustomerQrCodeConfigSearchReq) (res *applet.SCustomerQrCodeConfigSearchRes, err error) {
	res = new(applet.SCustomerQrCodeConfigSearchRes)
	res.SCustomerQrCodeConfigSearchRes, err = service.SCustomerQrCodeConfig().List(ctx, &req.SCustomerQrCodeConfigSearchReq)
	return
}

// Get 获取微信二维码配置
func (c *sCustomerQrCodeConfigController) Get(ctx context.Context, req *applet.SCustomerQrCodeConfigGetReq) (res *applet.SCustomerQrCodeConfigGetRes, err error) {
	res = new(applet.SCustomerQrCodeConfigGetRes)
	res.SCustomerQrCodeConfigInfoRes, err = service.SCustomerQrCodeConfig().GetById(ctx, req.Id)
	return
}

// Add 添加微信二维码配置
func (c *sCustomerQrCodeConfigController) Add(ctx context.Context, req *applet.SCustomerQrCodeConfigAddReq) (res *applet.SCustomerQrCodeConfigAddRes, err error) {
	err = service.SCustomerQrCodeConfig().Add(ctx, req.SCustomerQrCodeConfigAddReq)
	return
}

// Edit 修改微信二维码配置
func (c *sCustomerQrCodeConfigController) Edit(ctx context.Context, req *applet.SCustomerQrCodeConfigEditReq) (res *applet.SCustomerQrCodeConfigEditRes, err error) {
	err = service.SCustomerQrCodeConfig().Edit(ctx, req.SCustomerQrCodeConfigEditReq)
	return
}

// Delete 删除微信二维码配置
func (c *sCustomerQrCodeConfigController) Delete(ctx context.Context, req *applet.SCustomerQrCodeConfigDeleteReq) (res *applet.SCustomerQrCodeConfigDeleteRes, err error) {
	err = service.SCustomerQrCodeConfig().Delete(ctx, req.Ids)
	return
}
