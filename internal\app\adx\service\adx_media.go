// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: internal/app/adx/service/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxMedia interface {
	List(ctx context.Context, req *model.AdxMediaSearchReq) (res *model.AdxMediaSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxMediaSearchReq) (listRes []*model.AdxMediaInfoRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.AdxMediaInfoRes, err error)
	GetByIds(ctx context.Context, ids []int) (res []*model.AdxMediaInfoRes, err error)
	Add(ctx context.Context, req *model.AdxMediaAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.AdxMediaAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxMediaEditReq) (err error)
	Delete(ctx context.Context, Id []uint) (err error)
	RunSyncAdxMediaTask(ctx context.Context, req *model.AdxMediaSearchReq) (err error)
	SyncAdxMediaTask(ctx context.Context)
}

var localAdxMedia IAdxMedia

func AdxMedia() IAdxMedia {
	if localAdxMedia == nil {
		panic("implement not found for interface IAdxMedia, forgot register?")
	}
	return localAdxMedia
}

func RegisterAdxMedia(i IAdxMedia) {
	localAdxMedia = i
}
