/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListMembershipType
type ToolsWechatAppletListV30DataListMembershipType string

// List of tools_wechat_applet_list_v3.0_data_list_membership_type
const (
	ANNUAL_ToolsWechatAppletListV30DataListMembershipType       ToolsWechatAppletListV30DataListMembershipType = "ANNUAL"
	LIFETIME_ToolsWechatAppletListV30DataListMembershipType     ToolsWechatAppletListV30DataListMembershipType = "LIFETIME"
	MONTHLY_ToolsWechatAppletListV30DataListMembershipType      ToolsWechatAppletListV30DataListMembershipType = "MONTHLY"
	NONE_ToolsWechatAppletListV30DataListMembershipType         ToolsWechatAppletListV30DataListMembershipType = "NONE"
	WEEKLY_DAILY_ToolsWechatAppletListV30DataListMembershipType ToolsWechatAppletListV30DataListMembershipType = "WEEKLY_DAILY"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_membership_type value
func (v ToolsWechatAppletListV30DataListMembershipType) Ptr() *ToolsWechatAppletListV30DataListMembershipType {
	return &v
}
