/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletCreateV30MembershipType
type ToolsWechatAppletCreateV30MembershipType string

// List of tools_wechat_applet_create_v3.0_membership_type
const (
	ANNUAL_ToolsWechatAppletCreateV30MembershipType       ToolsWechatAppletCreateV30MembershipType = "ANNUAL"
	LIFETIME_ToolsWechatAppletCreateV30MembershipType     ToolsWechatAppletCreateV30MembershipType = "LIFETIME"
	MONTHLY_ToolsWechatAppletCreateV30MembershipType      ToolsWechatAppletCreateV30MembershipType = "MONTHLY"
	NONE_ToolsWechatAppletCreateV30MembershipType         ToolsWechatAppletCreateV30MembershipType = "NONE"
	WEEKLY_DAILY_ToolsWechatAppletCreateV30MembershipType ToolsWechatAppletCreateV30MembershipType = "WEEKLY_DAILY"
)

// Ptr returns reference to tools_wechat_applet_create_v3.0_membership_type value
func (v ToolsWechatAppletCreateV30MembershipType) Ptr() *ToolsWechatAppletCreateV30MembershipType {
	return &v
}
