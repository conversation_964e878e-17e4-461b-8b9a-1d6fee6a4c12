// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-23 14:36:18
// 生成路径: api/v1/applet/s_coupon_code.go
// 生成人：lx
// desc:兑换码表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SCouponCodeSearchReq 分页请求参数
type SCouponCodeSearchReq struct {
	g.Meta `path:"/list" tags:"兑换码表" method:"get" summary:"兑换码表列表"`
	commonApi.Author
	model.SCouponCodeSearchReq
}

// SCouponCodeSearchRes 列表返回结果
type SCouponCodeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SCouponCodeSearchRes
}

// SCouponCodeAddReq 添加操作请求参数
type SCouponCodeAddReq struct {
	g.Meta `path:"/add" tags:"兑换码表" method:"post" summary:"兑换码表添加"`
	commonApi.Author
	*model.SCouponCodeAddReq
}

// SCouponCodeAddRes 添加操作返回结果
type SCouponCodeAddRes struct {
	commonApi.EmptyRes
}

// SCouponCodeEditReq 修改操作请求参数
type SCouponCodeEditReq struct {
	g.Meta `path:"/edit" tags:"兑换码表" method:"put" summary:"兑换码表修改"`
	commonApi.Author
	*model.SCouponCodeEditReq
}

// SCouponCodeEditRes 修改操作返回结果
type SCouponCodeEditRes struct {
	commonApi.EmptyRes
}

// SCouponCodeGetReq 获取一条数据请求
type SCouponCodeGetReq struct {
	g.Meta `path:"/get" tags:"兑换码表" method:"get" summary:"获取兑换码表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SCouponCodeGetRes 获取一条数据结果
type SCouponCodeGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SCouponCodeInfoRes
}

// SCouponCodeDeleteReq 删除数据请求
type SCouponCodeDeleteReq struct {
	g.Meta `path:"/delete" tags:"兑换码表" method:"delete" summary:"删除兑换码表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SCouponCodeDeleteRes 删除数据返回
type SCouponCodeDeleteRes struct {
	commonApi.EmptyRes
}
