// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-13 10:42:38
// 生成路径: internal/app/ad/model/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdAppConfigInfoRes is the golang structure for table ad_app_config.
type AdAppConfigInfoRes struct {
	gmeta.Meta   `orm:"table:ad_app_config"`
	Id           int         `orm:"id,primary" json:"id" dc:"ID"`                                // ID
	AppId        string      `orm:"app_id" json:"appId" dc:""`                                   //
	Secret       string      `orm:"secret" json:"secret" dc:""`                                  //
	Type         int         `orm:"type" json:"type" dc:"应用类型：1：巨量 2：广点通"`                       // 应用类型：1：巨量 2：广点通
	AuthNums     int         `orm:"auth_nums" json:"authNums" dc:"已授权账号数"`                       // 已授权账号数
	AuthUserType int         `orm:"auth_user_type" json:"authUserType" dc:"授权用户类型： 1：纵横组织 2：方舟"` // 授权用户类型： 1：纵横组织 2：方舟
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                       // 创建时间
	UpdatedAt    *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                       // 更新时间
	DeletedAt    *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                       // 删除时间
}

type AdAppConfigListRes struct {
	Id           int         `json:"id" dc:"ID"`
	AppId        string      `json:"appId" dc:""`
	Secret       string      `json:"secret" dc:""`
	Type         int         `json:"type" dc:"应用类型：1：巨量 2：广点通"`
	AuthNums     int         `json:"authNums" dc:"已授权账号数"`
	AuthUserType int         `json:"authUserType" dc:"授权用户类型： 1：纵横组织 2：方舟"`
	CreatedAt    *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdAppConfigSearchReq 分页请求参数
type AdAppConfigSearchReq struct {
	comModel.PageReq
	Id    string `p:"id" dc:"ID"`  //ID
	AppId string `p:"appId" dc:""` //
}

// AdAppConfigSearchRes 列表返回结果
type AdAppConfigSearchRes struct {
	comModel.ListRes
	List []*AdAppConfigListRes `json:"list"`
}

// AdAppConfigAddReq 添加操作请求参数
type AdAppConfigAddReq struct {
	AppId        string `p:"appId" v:"required#不能为空" dc:""`
	Secret       string `p:"secret" v:"required#不能为空" dc:""`
	Type         int    `p:"type" v:"required#应用类型：1：巨量 2：广点通不能为空" dc:"应用类型：1：巨量 2：广点通"`
	AuthNums     int    `p:"authNums" v:"required#已授权账号数不能为空" dc:"已授权账号数"`
	AuthUserType int    `p:"authUserType" dc:"授权用户类型： 1：纵横组织 2：方舟"`
}

// AdAppConfigEditReq 修改操作请求参数
type AdAppConfigEditReq struct {
	Id           int    `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AppId        string `p:"appId" v:"required#不能为空" dc:""`
	Secret       string `p:"secret" v:"required#不能为空" dc:""`
	Type         int    `p:"type" v:"required#应用类型：1：巨量 2：广点通不能为空" dc:"应用类型：1：巨量 2：广点通"`
	AuthNums     int    `p:"authNums" v:"required#已授权账号数不能为空" dc:"已授权账号数"`
	AuthUserType int    `p:"authUserType" dc:"授权用户类型： 1：纵横组织 2：方舟"`
}
