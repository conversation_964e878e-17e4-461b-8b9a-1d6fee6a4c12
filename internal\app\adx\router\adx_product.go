// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-06-05 11:33:58
// 生成路径: internal/app/adx/router/adx_product.go
// 生成人：cq
// desc:ADX产品信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/adx/controller"
)

func (router *Router) BindAdxProductController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adxProduct", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdxProduct,
		)
	})
}
