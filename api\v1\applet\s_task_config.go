// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-16 14:26:31
// 生成路径: api/v1/applet/s_task_config.go
// 生成人：cq
// desc:任务配置表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// STaskConfigSearchReq 分页请求参数
type STaskConfigSearchReq struct {
	g.Meta `path:"/list" tags:"任务配置表" method:"post" summary:"任务配置表列表"`
	commonApi.Author
	model.STaskConfigSearchReq
}

// STaskConfigSearchRes 列表返回结果
type STaskConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.STaskConfigSearchRes
}

// STaskConfigAddReq 添加操作请求参数
type STaskConfigAddReq struct {
	g.Meta `path:"/add" tags:"任务配置表" method:"post" summary:"任务配置表添加"`
	commonApi.Author
	*model.STaskConfigAddReq
}

// STaskConfigAddRes 添加操作返回结果
type STaskConfigAddRes struct {
	commonApi.EmptyRes
}

// STaskConfigEditReq 修改操作请求参数
type STaskConfigEditReq struct {
	g.Meta `path:"/edit" tags:"任务配置表" method:"put" summary:"任务配置表修改"`
	commonApi.Author
	*model.STaskConfigEditReq
}

// STaskConfigEditRes 修改操作返回结果
type STaskConfigEditRes struct {
	commonApi.EmptyRes
}

// STaskConfigGetReq 获取一条数据请求
type STaskConfigGetReq struct {
	g.Meta `path:"/get" tags:"任务配置表" method:"get" summary:"获取任务配置表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// STaskConfigGetRes 获取一条数据结果
type STaskConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.STaskConfigInfoRes
}

// STaskConfigDeleteReq 删除数据请求
type STaskConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"任务配置表" method:"post" summary:"删除任务配置表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// STaskConfigDeleteRes 删除数据返回
type STaskConfigDeleteRes struct {
	commonApi.EmptyRes
}
