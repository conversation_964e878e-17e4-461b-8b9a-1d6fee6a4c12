/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuBrandInfoGetV30ResponseData
type YuntuBrandInfoGetV30ResponseData struct {
	// 云图品牌虚拟 adv_id
	AdvertiserId *int64 `json:"advertiser_id,omitempty"`
	// 行业信息列表，创建人群包及创建人群标签时使用。
	IndustryInfos []*YuntuBrandInfoGetV30ResponseDataIndustryInfosInner `json:"industry_infos,omitempty"`
	// 用户名称，创建人群标签时使用。
	UserDisplayName *string `json:"user_display_name,omitempty"`
}
