/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DmpCustomAudienceReadV2ApiService DmpCustomAudienceReadV2Api service
type DmpCustomAudienceReadV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DmpCustomAudienceReadV2Request
}

type DmpCustomAudienceReadV2Request struct {
	AdvertiserId      *int64
	CustomAudienceIds *[]int64
}

func (r *DmpCustomAudienceReadV2ApiService) SetCfg(cfg *conf.Configuration) *DmpCustomAudienceReadV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DmpCustomAudienceReadV2ApiService) DmpCustomAudienceReadV2Request(dmpCustomAudienceReadV2Request DmpCustomAudienceReadV2Request) *DmpCustomAudienceReadV2ApiService {
	r.Request = &dmpCustomAudienceReadV2Request
	return r
}

func (r *DmpCustomAudienceReadV2ApiService) AccessToken(accessToken string) *DmpCustomAudienceReadV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DmpCustomAudienceReadV2ApiService) Do() (data *models.DmpCustomAudienceReadV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dmp/custom_audience/read/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.CustomAudienceIds == nil {
		return nil, errors.New("customAudienceIds is required and must be specified")
	}
	if len(*r.Request.CustomAudienceIds) > 100 {
		return nil, errors.New("customAudienceIds must have less than 100 elements")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "custom_audience_ids", r.Request.CustomAudienceIds)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.DmpCustomAudienceReadV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DmpCustomAudienceReadV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dmp/custom_audience/read/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
