// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: internal/app/ad/dao/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// dzTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzTaskDao struct {
	*internal.DzTaskDao
}

var (
	// DzTask is globally public accessible object for table tools_gen_table operations.
	DzTask = dzTaskDao{
		internal.NewDzTaskDao(),
	}
)

// Fill with you ideas below.
