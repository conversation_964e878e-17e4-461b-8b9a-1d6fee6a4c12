// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/controller/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adBatchTaskDetailController struct {
	systemController.BaseController
}

var AdBatchTaskDetail = new(adBatchTaskDetailController)

// List 列表
func (c *adBatchTaskDetailController) List(ctx context.Context, req *ad.AdBatchTaskDetailSearchReq) (res *ad.AdBatchTaskDetailSearchRes, err error) {
	res = new(ad.AdBatchTaskDetailSearchRes)
	res.AdBatchTaskDetailSearchRes, err = service.AdBatchTaskDetail().List(ctx, &req.AdBatchTaskDetailSearchReq)
	return
}

// Get 获取广告批量操作任务详情
func (c *adBatchTaskDetailController) Get(ctx context.Context, req *ad.AdBatchTaskDetailGetReq) (res *ad.AdBatchTaskDetailGetRes, err error) {
	res = new(ad.AdBatchTaskDetailGetRes)
	res.AdBatchTaskDetailInfoRes, err = service.AdBatchTaskDetail().GetById(ctx, req.Id)
	return
}

// Add 添加广告批量操作任务详情
func (c *adBatchTaskDetailController) Add(ctx context.Context, req *ad.AdBatchTaskDetailAddReq) (res *ad.AdBatchTaskDetailAddRes, err error) {
	err = service.AdBatchTaskDetail().Add(ctx, req.AdBatchTaskDetailAddReq)
	return
}

// Edit 修改广告批量操作任务详情
func (c *adBatchTaskDetailController) Edit(ctx context.Context, req *ad.AdBatchTaskDetailEditReq) (res *ad.AdBatchTaskDetailEditRes, err error) {
	err = service.AdBatchTaskDetail().Edit(ctx, req.AdBatchTaskDetailEditReq)
	return
}

// Delete 删除广告批量操作任务详情
func (c *adBatchTaskDetailController) Delete(ctx context.Context, req *ad.AdBatchTaskDetailDeleteReq) (res *ad.AdBatchTaskDetailDeleteRes, err error) {
	err = service.AdBatchTaskDetail().Delete(ctx, req.Ids)
	return
}
