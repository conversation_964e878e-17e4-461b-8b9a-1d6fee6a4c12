// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: internal/app/ad/dao/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adXtAccountDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adXtAccountDao struct {
	*internal.AdXtAccountDao
}

var (
	// AdXtAccount is globally public accessible object for table tools_gen_table operations.
	AdXtAccount = adXtAccountDao{
		internal.NewAdXtAccountDao(),
	}
)

// Fill with you ideas below.
