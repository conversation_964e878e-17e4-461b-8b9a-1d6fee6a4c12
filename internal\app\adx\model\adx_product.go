// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:33:58
// 生成路径: internal/app/adx/model/adx_product.go
// 生成人：cq
// desc:ADX产品信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxProductInfoRes is the golang structure for table adx_product.
type AdxProductInfoRes struct {
	gmeta.Meta  `orm:"table:adx_product"`
	Id          uint64 `orm:"id,primary" json:"id" dc:"产品ID"`                 // 产品ID
	ProductName string `orm:"product_name" json:"productName" dc:"产品名称"`      // 产品名称
	Industry    string `orm:"industry" json:"industry" dc:"所属行业"`             // 所属行业
	Icon        string `orm:"icon" json:"icon" dc:"产品图标URL"`                  // 产品图标URL
	ChargesType uint   `orm:"charges_type" json:"chargesType" dc:"资费类型(枚举值)"` // 资费类型(枚举值)
	Type        uint   `orm:"type" json:"type" dc:"产品类型(枚举值)"`                // 产品类型(枚举值)
}

type AdxProductListRes struct {
	Id          uint64 `json:"id" dc:"产品ID"`
	ProductName string `json:"productName" dc:"产品名称"`
	Industry    string `json:"industry" dc:"所属行业"`
	Icon        string `json:"icon" dc:"产品图标URL"`
	ChargesType uint   `json:"chargesType" dc:"资费类型(枚举值)"`
	Type        uint   `json:"type" dc:"产品类型(枚举值)"`
}

// AdxProductSearchReq 分页请求参数
type AdxProductSearchReq struct {
	comModel.PageReq
	Id          string `p:"id" dc:"产品ID"`                                                     //产品ID
	ProductName string `p:"productName" dc:"产品名称"`                                            //产品名称
	Industry    string `p:"industry" dc:"所属行业"`                                               //所属行业
	Icon        string `p:"icon" dc:"产品图标URL"`                                                //产品图标URL
	ChargesType string `p:"chargesType" v:"chargesType@integer#资费类型(枚举值)需为整数" dc:"资费类型(枚举值)"` //资费类型(枚举值)
	Type        string `p:"type" v:"type@integer#产品类型(枚举值)需为整数" dc:"产品类型(枚举值)"`               //产品类型(枚举值)
	StartTime   string `p:"startTime" dc:"开始时间"`
	EndTime     string `p:"endTime" dc:"结束时间"`
}

// AdxProductSearchRes 列表返回结果
type AdxProductSearchRes struct {
	comModel.ListRes
	List []*AdxProductListRes `json:"list"`
}

// AdxProductAddReq 添加操作请求参数
type AdxProductAddReq struct {
	Id          uint64 `p:"id" v:"required#主键ID不能为空" dc:"产品ID"`
	ProductName string `p:"productName" v:"required#产品名称不能为空" dc:"产品名称"`
	Industry    string `p:"industry"  dc:"所属行业"`
	Icon        string `p:"icon"  dc:"产品图标URL"`
	ChargesType uint   `p:"chargesType"  dc:"资费类型(枚举值)"`
	Type        uint   `p:"type"  dc:"产品类型(枚举值)"`
}

// AdxProductEditReq 修改操作请求参数
type AdxProductEditReq struct {
	Id          uint64 `p:"id" v:"required#主键ID不能为空" dc:"产品ID"`
	ProductName string `p:"productName" v:"required#产品名称不能为空" dc:"产品名称"`
	Industry    string `p:"industry"  dc:"所属行业"`
	Icon        string `p:"icon"  dc:"产品图标URL"`
	ChargesType uint   `p:"chargesType"  dc:"资费类型(枚举值)"`
	Type        uint   `p:"type"  dc:"产品类型(枚举值)"`
}
