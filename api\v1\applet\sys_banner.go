// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-03 11:14:03
// 生成路径: api/v1/applet/sys_banner.go
// 生成人：cyao
// desc:新banner表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SysBannerSearchReq 分页请求参数
type SysBannerSearchReq struct {
	g.Meta `path:"/list" tags:"小程序设置-轮播图管理" method:"get" summary:"轮播图管理列表"`
	commonApi.Author
	model.SysBannerSearchReq
}

// SysBannerSearchRes 列表返回结果
type SysBannerSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SysBannerSearchRes
}

// 相关连表查询数据
type LinkedSysBannerDataSearchReq struct {
	g.Meta `path:"/linkedData" tags:"小程序设置-轮播图管理" method:"get" summary:"轮播图管理关联表数据"`
	commonApi.Author
}

// 相关连表查询数据
type LinkedSysBannerDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.LinkedSysBannerDataSearchRes
}

// SysBannerAddReq 添加操作请求参数
type SysBannerAddReq struct {
	g.Meta `path:"/add" tags:"小程序设置-轮播图管理" method:"post" summary:"轮播图管理添加"`
	commonApi.Author
	*model.SysBannerAddReq
}

// SysBannerAddRes 添加操作返回结果
type SysBannerAddRes struct {
	commonApi.EmptyRes
}

// SysBannerEditReq 修改操作请求参数
type SysBannerEditReq struct {
	g.Meta `path:"/edit" tags:"小程序设置-轮播图管理" method:"put" summary:"轮播图管理修改"`
	commonApi.Author
	*model.SysBannerEditReq
}

// SysBannerEditRes 修改操作返回结果
type SysBannerEditRes struct {
	commonApi.EmptyRes
}

// SysBannerGetReq 获取一条数据请求
type SysBannerGetReq struct {
	g.Meta `path:"/get" tags:"小程序设置-轮播图管理" method:"get" summary:"获取轮播图根据Id"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SysBannerGetRes 获取一条数据结果
type SysBannerGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SysBannerInfoRes
}

// SysBannerDeleteReq 删除数据请求
type SysBannerDeleteReq struct {
	g.Meta `path:"/delete" tags:"小程序设置-轮播图管理" method:"post" summary:"删除轮播图"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SysBannerDeleteRes 删除数据返回
type SysBannerDeleteRes struct {
	commonApi.EmptyRes
}
