// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/dao/internal/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAnchorPointUploadDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdAnchorPointUploadDao struct {
	table   string                     // Table is the underlying table name of the DAO.
	group   string                     // Group is the database configuration group name of current DAO.
	columns AdAnchorPointUploadColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdAnchorPointUploadColumns defines and stores column names for table ad_anchor_point_upload.
type AdAnchorPointUploadColumns struct {
	Id            string // ID
	AnchorType    string //
	AnchorPointId string // 业务的字段锚点id
	AnchorId      string // 原生锚点id
	CreateTime    string // 创建时间
}

var adAnchorPointUploadColumns = AdAnchorPointUploadColumns{
	Id:            "id",
	AnchorType:    "anchor_type",
	AnchorPointId: "anchor_point_id",
	AnchorId:      "anchor_id",
	CreateTime:    "create_time",
}

// NewAdAnchorPointUploadDao creates and returns a new DAO object for table data access.
func NewAdAnchorPointUploadDao() *AdAnchorPointUploadDao {
	return &AdAnchorPointUploadDao{
		group:   "default",
		table:   "ad_anchor_point_upload",
		columns: adAnchorPointUploadColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdAnchorPointUploadDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdAnchorPointUploadDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdAnchorPointUploadDao) Columns() AdAnchorPointUploadColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdAnchorPointUploadDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdAnchorPointUploadDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdAnchorPointUploadDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
