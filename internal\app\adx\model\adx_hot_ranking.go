// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/model/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxHotRankingInfoRes is the golang structure for table adx_hot_ranking.
type AdxHotRankingInfoRes struct {
	gmeta.Meta       `orm:"table:adx_hot_ranking"`
	PlayletId        uint64         `orm:"playlet_id,primary" json:"playletId" dc:"短剧ID"` // 短剧ID
	BusinessDate     string         `orm:"business_date,primary" json:"businessDate" dc:"业务日期"`
	PlayletName      string         `orm:"playlet_name" json:"playletName" dc:"短剧名称"`             // 短剧名称
	Ranking          uint           `orm:"ranking" json:"ranking" dc:"排名"`                        // 排名
	ConsumeNum       int64          `orm:"consume_num" json:"consumeNum" dc:"当日热力值"`              // 当日热力值
	TotalConsumeNum  int64          `orm:"total_consume_num" json:"totalConsumeNum" dc:"累计热力值"`   // 累计热力值
	NewFlag          int            `orm:"new_flag" json:"newFlag" dc:"是否有new标识"`                 // 是否有new标识
	PlayletTags      []string       `orm:"playlet_tags" json:"playletTags" dc:"短剧标签"`             // 短剧标签
	ContractorList   []Contractor   `orm:"contractor_list" json:"contractorList" dc:"承制方列表"`      // 承制方列表
	RelatedPartyList []RelatedParty `orm:"related_party_list" json:"relatedPartyList" dc:"关联方列表"` // 关联方列表
	// copyright_holder_list
	CopyrightHolderList []string `orm:"copyright_holder_list" json:"copyrightHolderList" dc:"版权方列表"`
}

type AdxHotRankingListRes struct {
	Ranking             uint           `json:"ranking" dc:"排名"`
	PlayletId           uint64         `json:"playletId" dc:"短剧ID"`
	PlayletName         string         `json:"playletName" dc:"短剧名称"`
	ConsumeNum          int64          `json:"consumeNum" dc:"当日热力值"`
	TotalConsumeNum     int64          `json:"totalConsumeNum" dc:"累计热力值"`
	NewFlag             int            `json:"newFlag" dc:"是否有new标识"`
	PlayletTags         []string       `json:"playletTags" dc:"短剧标签"`
	ContractorList      []Contractor   `json:"contractorList" dc:"承制方列表"`
	RelatedPartyList    []RelatedParty `json:"relatedPartyList" dc:"关联方列表"`
	CopyrightHolderList []string       `json:"copyrightHolderList" dc:"版权方列表"`
}

// AdxHotRankingSearchReq 分页请求参数
type AdxHotRankingSearchReq struct {
	comModel.PageReq
	PlayletId           string         `p:"playletId" dc:"短剧ID"` //短剧ID
	StartTime           string         `p:"startTime"  dc:"开始时间"`
	EndTime             string         `p:"endTime"  dc:"结束时间"`
	PlayletName         string         `p:"playletName" dc:"短剧名称"`                                            //短剧名称
	Ranking             string         `p:"ranking" v:"ranking@integer#排名需为整数" dc:"排名"`                       //排名
	ConsumeNum          string         `p:"consumeNum" v:"consumeNum@integer#当日热力值需为整数" dc:"当日热力值"`           //当日热力值
	TotalConsumeNum     string         `p:"totalConsumeNum" v:"totalConsumeNum@integer#累计热力值需为整数" dc:"累计热力值"` //累计热力值
	NewFlag             string         `p:"newFlag" v:"newFlag@integer#是否有new标识需为整数" dc:"是否有new标识"`           //是否有new标识
	PlayletTags         []string       `p:"playletTags" dc:"短剧标签"`                                            //短剧标签
	ContractorList      []Contractor   `p:"contractorList" dc:"承制方列表"`                                        //承制方列表
	RelatedPartyList    []RelatedParty `p:"relatedPartyList" dc:"关联方列表"`                                      //关联方列表
	CopyrightHolderList []string       `p:"copyrightHolderList" dc:"版权方列表"`
}

// AdxHotRankingSearchRes 列表返回结果
type AdxHotRankingSearchRes struct {
	comModel.ListRes
	List []*AdxHotRankingListRes `json:"list"`
}

// AdxHotRankingAddReq 添加操作请求参数
type AdxHotRankingAddReq struct {
	PlayletId           uint64         `p:"playletId" v:"required#主键ID不能为空" dc:"短剧ID"`
	BusinessDate        string         `p:"businessDate" v:"required#业务日期不能为空" dc:"业务日期"`
	PlayletName         string         `p:"playletName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	Ranking             uint           `p:"ranking"  dc:"排名"`
	ConsumeNum          int64          `p:"consumeNum"  dc:"当日热力值"`
	TotalConsumeNum     int64          `p:"totalConsumeNum"  dc:"累计热力值"`
	NewFlag             int            `p:"newFlag"  dc:"是否有new标识"`
	PlayletTags         []string       `p:"playletTags" v:"required#短剧标签不能为空" dc:"短剧标签"`
	ContractorList      []Contractor   `p:"contractorList" v:"required#承制方列表不能为空" dc:"承制方列表"`
	RelatedPartyList    []RelatedParty `p:"relatedPartyList" v:"required#关联方列表不能为空" dc:"关联方列表"`
	CopyrightHolderList []string       `p:"copyrightHolderList" dc:"版权方列表"`
}

// AdxHotRankingEditReq 修改操作请求参数
type AdxHotRankingEditReq struct {
	PlayletId           uint64         `p:"playletId" v:"required#主键ID不能为空" dc:"短剧ID"`
	BusinessDate        string         `p:"businessDate" v:"required#业务日期不能为空" dc:"业务日期"`
	PlayletName         string         `p:"playletName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	Ranking             uint           `p:"ranking"  dc:"排名"`
	ConsumeNum          int64          `p:"consumeNum"  dc:"当日热力值"`
	TotalConsumeNum     int64          `p:"totalConsumeNum"  dc:"累计热力值"`
	NewFlag             int            `p:"newFlag"  dc:"是否有new标识"`
	PlayletTags         []string       `p:"playletTags" v:"required#短剧标签不能为空" dc:"短剧标签"`
	ContractorList      []Contractor   `p:"contractorList" v:"required#承制方列表不能为空" dc:"承制方列表"`
	RelatedPartyList    []RelatedParty `p:"relatedPartyList" v:"required#关联方列表不能为空" dc:"关联方列表"`
	CopyrightHolderList []string       `p:"copyrightHolderList" dc:"版权方列表"`
}

type Contractor struct {
	PublisherName string `json:"publisherName"`
}

type RelatedParty struct {
	PublisherName string `json:"publisherName"`
}
