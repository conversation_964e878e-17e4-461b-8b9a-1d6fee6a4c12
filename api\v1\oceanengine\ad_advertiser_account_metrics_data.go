// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-15 16:27:19
// 生成路径: api/v1/oceanengine/ad_advertiser_account_metrics_data.go
// 生成人：cyao
// desc:广告账户的指标数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAdvertiserAccountMetricsDataSearchReq 分页请求参数
type AdAdvertiserAccountMetricsDataSearchReq struct {
	g.Meta `path:"/list" tags:"广告账户的指标数据" method:"get" summary:"广告账户的指标数据列表"`
	commonApi.Author
	model.AdAdvertiserAccountMetricsDataSearchReq
}

// AdAdvertiserAccountReportDataSearchReq 获取报表数据
type AdAdvertiserAccountReportDataSearchReq struct {
	g.Meta `path:"/getReport" tags:"广告账户的指标数据" method:"get" summary:"广告账户的指标数据列表"`
	commonApi.Author
	model.AdAdvertiserAccountReportDataSearch
}

type AdAdvertiserAccountReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountReportDataSearchRes
}

// AdAdvertiserAccountReportSubTaskReq 刷账户的报表数据
type AdAdvertiserAccountReportSubTaskReq struct {
	g.Meta `path:"/getReport/sub/task" tags:"Task" method:"post" summary:"adAdvertiserAccountMetricsData"`
	commonApi.Author
	StatTime string `p:"statTime" dc:"开始时间 YYYY-MM-DD格式"`
	AdId     string `p:"adId" v:"required#项目id必须"`
}

// AdAdvertiserAccountReportSubTaskRes 返回
type AdAdvertiserAccountReportSubTaskRes struct {
	g.Meta `mime:"application/json"`
}

// AdAdvertiserAccountReportStatTaskReq 刷账户的报表数据
type AdAdvertiserAccountReportStatTaskReq struct {
	g.Meta `path:"/getReport/stat/task" tags:"Task" method:"post" summary:"adAdvertiserAccountMetricsData"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

// AdAdvertiserAccountReportStatTaskRes 返回
type AdAdvertiserAccountReportStatTaskRes struct {
	g.Meta `mime:"application/json"`
}

// AdAdvertiserAccountMetricsDataSearchRes 列表返回结果
type AdAdvertiserAccountMetricsDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountMetricsDataSearchRes
}

// AdAdvertiserAccountMetricsDataAddReq 添加操作请求参数
type AdAdvertiserAccountMetricsDataAddReq struct {
	g.Meta `path:"/add" tags:"广告账户的指标数据" method:"post" summary:"广告账户的指标数据添加"`
	commonApi.Author
	*model.AdAdvertiserAccountMetricsDataAddReq
}

// AdAdvertiserAccountMetricsDataAddRes 添加操作返回结果
type AdAdvertiserAccountMetricsDataAddRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountMetricsDataEditReq 修改操作请求参数
type AdAdvertiserAccountMetricsDataEditReq struct {
	g.Meta `path:"/edit" tags:"广告账户的指标数据" method:"put" summary:"广告账户的指标数据修改"`
	commonApi.Author
	*model.AdAdvertiserAccountMetricsDataEditReq
}

// AdAdvertiserAccountMetricsDataEditRes 修改操作返回结果
type AdAdvertiserAccountMetricsDataEditRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountMetricsDataGetReq 获取一条数据请求
type AdAdvertiserAccountMetricsDataGetReq struct {
	g.Meta `path:"/get" tags:"广告账户的指标数据" method:"get" summary:"获取广告账户的指标数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAdvertiserAccountMetricsDataGetRes 获取一条数据结果
type AdAdvertiserAccountMetricsDataGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountMetricsDataInfoRes
}

// AdAdvertiserAccountMetricsDataDeleteReq 删除数据请求
type AdAdvertiserAccountMetricsDataDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告账户的指标数据" method:"delete" summary:"删除广告账户的指标数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAdvertiserAccountMetricsDataDeleteRes 删除数据返回
type AdAdvertiserAccountMetricsDataDeleteRes struct {
	commonApi.EmptyRes
}

// AccountSubjectDataStatisticsReq 账户主体数据统计
type AccountSubjectDataStatisticsReq struct {
	g.Meta `path:"/accountSubjectDataStat" tags:"广告账户的指标数据" method:"post" summary:"账户主体数据统计"`
	commonApi.Author
	model.AccountSubjectDataStatisticsReq
}

type AccountSubjectDataStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.AccountSubjectDataStatisticsRes
}
