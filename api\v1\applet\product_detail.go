// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-28 15:47:10
// 生成路径: api/v1/applet/product_detail.go
// 生成人：cq
// desc:商品详情表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// ProductDetailSearchReq 分页请求参数
type ProductDetailSearchReq struct {
	g.Meta `path:"/list" tags:"商品详情表" method:"post" summary:"商品详情表列表"`
	commonApi.Author
	model.ProductDetailSearchReq
}

// ProductDetailSearchRes 列表返回结果
type ProductDetailSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.ProductDetailSearchRes
}

// ProductDetailAddReq 添加操作请求参数
type ProductDetailAddReq struct {
	g.Meta `path:"/add" tags:"商品详情表" method:"post" summary:"商品详情表添加"`
	commonApi.Author
	*model.ProductDetailAddReq
}

// ProductDetailAddRes 添加操作返回结果
type ProductDetailAddRes struct {
	commonApi.EmptyRes
}

// ProductDetailEditReq 修改操作请求参数
type ProductDetailEditReq struct {
	g.Meta `path:"/edit" tags:"商品详情表" method:"put" summary:"商品详情表修改"`
	commonApi.Author
	*model.ProductDetailEditReq
}

// ProductDetailEditRes 修改操作返回结果
type ProductDetailEditRes struct {
	commonApi.EmptyRes
}

// ProductDetailGetReq 获取一条数据请求
type ProductDetailGetReq struct {
	g.Meta `path:"/get" tags:"商品详情表" method:"get" summary:"获取商品详情表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// ProductDetailGetRes 获取一条数据结果
type ProductDetailGetRes struct {
	g.Meta `mime:"application/json"`
	*model.ProductDetailInfoRes
}

// ProductDetailDeleteReq 删除数据请求
type ProductDetailDeleteReq struct {
	g.Meta `path:"/delete" tags:"商品详情表" method:"post" summary:"删除商品详情表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// ProductDetailDeleteRes 删除数据返回
type ProductDetailDeleteRes struct {
	commonApi.EmptyRes
}
