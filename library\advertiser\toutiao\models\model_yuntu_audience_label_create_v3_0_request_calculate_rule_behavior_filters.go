/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30RequestCalculateRuleBehaviorFilters 计算规则行为条件。
type YuntuAudienceLabelCreateV30RequestCalculateRuleBehaviorFilters struct {
	// 行为类别，请对照所需创建的标签类别（商品/内容/搜索）参照枚举包进行枚举选择。
	Behaviors []*YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors `json:"behaviors"`
	// 行为条件结束日期。
	DateEnd string `json:"date_end"`
	// 行为条件开始日期。
	DateStart string `json:"date_start"`
	// 最大行为频次。仅当创建商品人群标签时，如果数据范围为全网、且行为类别包含购买，需按照固定区间进行传递。固定区间阶梯规则如下： (0,5): min_times=0, max_times=5 [5,10): min_times=5, max_times=10 [10,20): min_times=10, max_times=20 [20,30): min_times=20, max_times=30 [30,40): min_times=30, max_times=40  [40,50): min_times=40, max_times=50  [50,100): min_times=50, max_times=100  [100,∞): min_times=100, max_times=100000000
	MaxTimes *int64 `json:"max_times,omitempty"`
	// 最小行为频次。不限时为0。仅当创建商品人群标签时，如果数据范围为全网、且行为类别包含购买，需按照固定区间进行传递。固定区间阶梯规则如下： (0,5): min_times=0, max_times=5 [5,10): min_times=5, max_times=10 [10,20): min_times=10, max_times=20 [20,30): min_times=20, max_times=30 [30,40): min_times=30, max_times=40  [40,50): min_times=40, max_times=50  [50,100): min_times=50, max_times=100  [100,∞): min_times=100, max_times=100000000
	MinTimes int64 `json:"min_times"`
}
