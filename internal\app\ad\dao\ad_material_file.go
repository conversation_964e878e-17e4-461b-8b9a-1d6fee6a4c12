// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: internal/app/ad/dao/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adMaterialFileDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adMaterialFileDao struct {
	*internal.AdMaterialFileDao
}

var (
	// AdMaterialFile is globally public accessible object for table tools_gen_table operations.
	AdMaterialFile = adMaterialFileDao{
		internal.NewAdMaterialFileDao(),
	}
)

// Fill with you ideas below.
