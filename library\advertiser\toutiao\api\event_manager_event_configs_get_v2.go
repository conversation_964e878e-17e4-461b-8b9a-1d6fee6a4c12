/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// EventManagerEventConfigsGetV2ApiService 获取资产下已创建事件列表
type EventManagerEventConfigsGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *EventManagerEventConfigsGetV2Request
}

type EventManagerEventConfigsGetV2Request struct {
	AdvertiserId *int64
	AssetId      *int64
	SortType     *models.EventManagerEventConfigsGetV2SortType
}

func (r *EventManagerEventConfigsGetV2ApiService) SetCfg(cfg *conf.Configuration) *EventManagerEventConfigsGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *EventManagerEventConfigsGetV2ApiService) EventManagerEventConfigsGetV2Request(eventManagerEventConfigsGetV2Request EventManagerEventConfigsGetV2Request) *EventManagerEventConfigsGetV2ApiService {
	r.Request = &eventManagerEventConfigsGetV2Request
	return r
}

func (r *EventManagerEventConfigsGetV2ApiService) AccessToken(accessToken string) *EventManagerEventConfigsGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *EventManagerEventConfigsGetV2ApiService) Do() (data *models.EventManagerEventConfigsGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/event_manager/event_configs/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.AssetId == nil {
		return nil, errors.New("assetId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "asset_id", r.Request.AssetId)
	if r.Request.SortType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "sort_type", r.Request.SortType)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.EventManagerEventConfigsGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.EventManagerEventConfigsGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/event_manager/event_configs/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
