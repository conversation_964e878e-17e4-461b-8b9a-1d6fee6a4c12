// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: internal/app/ad/model/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdInfoInfoRes is the golang structure for table ks_ad_info.
type KsAdInfoInfoRes struct {
	gmeta.Meta      `orm:"table:ks_ad_info"`
	Id              int         `orm:"id,primary" json:"id" dc:"ID"`                         // ID
	AdvertiserId    int64       `orm:"advertiser_id" json:"advertiserId" dc:"用户快手号id/广告主id"` // 用户快手号id/广告主id
	AppId           int         `orm:"app_id" json:"appId" dc:"授权的app_id"`                   // 授权的app_id
	AdAccountName   string      `orm:"ad_account_name" json:"adAccountName" dc:"快手经营者账号名称"`  // 快手经营者账号名称
	AccountMainName string      `orm:"account_main_name" json:"accountMainName" dc:"账号主体名称"` // 账号主体名称
	Status          string      `orm:"status" json:"status" dc:""`                           //
	AuthTime        *gtime.Time `orm:"auth_time" json:"authTime" dc:"授权时间"`                  // 授权时间
	AuthUrl         string      `orm:"auth_url" json:"authUrl" dc:"授权链接地址"`                  // 授权链接地址
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                // 创建时间
}

type KsAdInfoListRes struct {
	Id              int         `json:"id" dc:"ID"`
	AdvertiserId    int64       `json:"advertiserId" dc:"用户快手号id/广告主id"`
	AppId           int         `json:"appId" dc:"授权的app_id"`
	AdAccountName   string      `json:"adAccountName" dc:"快手经营者账号名称"`
	AccountMainName string      `json:"accountMainName" dc:"账号主体名称"`
	Status          string      `json:"status" dc:""`
	AuthTime        *gtime.Time `json:"authTime" dc:"授权时间"`
	AuthUrl         string      `json:"authUrl" dc:"授权链接地址"`
	CreatedAt       *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdInfoSearchReq 分页请求参数
type KsAdInfoSearchReq struct {
	comModel.PageReq
	Id              string `p:"id" dc:"ID"`                                                                 //ID
	AdvertiserId    string `p:"advertiserId" v:"advertiserId@integer#用户快手号id/广告主id需为整数" dc:"用户快手号id/广告主id"` //用户快手号id/广告主id
	AppId           string `p:"appId" v:"appId@integer#授权的app_id需为整数" dc:"授权的app_id"`                       //授权的app_id
	AdAccountName   string `p:"adAccountName" dc:"快手经营者账号名称"`                                               //快手经营者账号名称
	AccountMainName string `p:"accountMainName" dc:"账号主体名称"`                                                //账号主体名称
	Status          string `p:"status" dc:"1未授权 2 已经授权"`                                                    //
	AuthTime        string `p:"authTime" v:"authTime@datetime#授权时间需为YYYY-MM-DD hh:mm:ss格式" dc:"授权时间"`       //授权时间
	AuthUrl         string `p:"authUrl" dc:"授权链接地址"`                                                        //授权链接地址
	CreatedAt       string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`     //创建时间
}

// KsAdInfoSearchRes 列表返回结果
type KsAdInfoSearchRes struct {
	comModel.ListRes
	List []*KsAdInfoListRes `json:"list"`
}

// KsAdInfoAddReq 添加操作请求参数
type KsAdInfoAddReq struct {
	AdvertiserId    int64       `p:"advertiserId" v:"required#用户快手号id/广告主id不能为空" dc:"用户快手号id/广告主id"`
	AppId           int         `p:"appId"  dc:"授权的app_id"`
	AdAccountName   string      `p:"adAccountName" v:"required#快手经营者账号名称不能为空" dc:"快手经营者账号名称"`
	AccountMainName string      `p:"accountMainName" v:"required#账号主体名称不能为空" dc:"账号主体名称"`
	Status          string      `p:"status" v:"required#不能为空" dc:"1未授权 2 已经授权"`
	AuthTime        *gtime.Time `p:"authTime"  dc:"授权时间"`
	AuthUrl         string      `p:"authUrl"  dc:"授权链接地址"`
}

// KsAdInfoEditReq 修改操作请求参数
type KsAdInfoEditReq struct {
	Id              int         `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AdvertiserId    int64       `p:"advertiserId" v:"required#用户快手号id/广告主id不能为空" dc:"用户快手号id/广告主id"`
	AppId           int         `p:"appId"  dc:"授权的app_id"`
	AdAccountName   string      `p:"adAccountName" v:"required#快手经营者账号名称不能为空" dc:"快手经营者账号名称"`
	AccountMainName string      `p:"accountMainName" v:"required#账号主体名称不能为空" dc:"账号主体名称"`
	Status          string      `p:"status" v:"required#不能为空" dc:""`
	AuthTime        *gtime.Time `p:"authTime"  dc:"授权时间"`
	AuthUrl         string      `p:"authUrl"  dc:"授权链接地址"`
}
