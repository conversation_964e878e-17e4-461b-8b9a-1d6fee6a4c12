// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-07-23 14:36:19
// 生成路径: internal/app/applet/controller/s_coupon_code.go
// 生成人：lx
// desc:兑换码表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sCouponCodeController struct {
	systemController.BaseController
}

var SCouponCode = new(sCouponCodeController)

// List 列表
func (c *sCouponCodeController) List(ctx context.Context, req *applet.SCouponCodeSearchReq) (res *applet.SCouponCodeSearchRes, err error) {
	res = new(applet.SCouponCodeSearchRes)
	res.SCouponCodeSearchRes, err = service.SCouponCode().List(ctx, &req.SCouponCodeSearchReq)
	return
}

// Get 获取兑换码表
func (c *sCouponCodeController) Get(ctx context.Context, req *applet.SCouponCodeGetReq) (res *applet.SCouponCodeGetRes, err error) {
	res = new(applet.SCouponCodeGetRes)
	res.SCouponCodeInfoRes, err = service.SCouponCode().GetById(ctx, req.Id)
	return
}

// Add 添加兑换码表
func (c *sCouponCodeController) Add(ctx context.Context, req *applet.SCouponCodeAddReq) (res *applet.SCouponCodeAddRes, err error) {
	err = service.SCouponCode().Add(ctx, req.SCouponCodeAddReq)
	return
}

// Edit 修改兑换码表
func (c *sCouponCodeController) Edit(ctx context.Context, req *applet.SCouponCodeEditReq) (res *applet.SCouponCodeEditRes, err error) {
	err = service.SCouponCode().Edit(ctx, req.SCouponCodeEditReq)
	return
}

// Delete 删除兑换码表
func (c *sCouponCodeController) Delete(ctx context.Context, req *applet.SCouponCodeDeleteReq) (res *applet.SCouponCodeDeleteRes, err error) {
	err = service.SCouponCode().Delete(ctx, req.Ids)
	return
}
