// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-13 15:31:10
// 生成路径: internal/app/ad/dao/ad_material_album_users.go
// 生成人：cyao
// desc:广告素材专辑和用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adMaterialAlbumUsersDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adMaterialAlbumUsersDao struct {
	*internal.AdMaterialAlbumUsersDao
}

var (
	// AdMaterialAlbumUsers is globally public accessible object for table tools_gen_table operations.
	AdMaterialAlbumUsers = adMaterialAlbumUsersDao{
		internal.NewAdMaterialAlbumUsersDao(),
	}
)

// Fill with you ideas below.
