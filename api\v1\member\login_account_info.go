// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-24 14:14:38
// 生成路径: api/v1/system/login_account_info.go
// 生成人：gfast
// desc:用户账户信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// LoginAccountInfoSearchReq 分页请求参数
type LoginAccountInfoSearchReq struct {
	g.Meta `path:"/list" tags:"用户账户信息表" method:"get" summary:"用户账户信息表列表"`
	commonApi.Author
	model.LoginAccountInfoSearchReq
}

// LoginAccountInfoSearchRes 列表返回结果
type LoginAccountInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.LoginAccountInfoSearchRes
}

// 刷新视频号数据和订单
type RefreshVideoOrderReq struct {
	g.Meta `path:"/refresh/video/order" tags:"用户账户信息表" method:"post" summary:"刷新视频号数据和订单"`
	commonApi.Author
	model.RefreshVideoOrderReq
}

type RefreshVideoOrderRes struct {
	commonApi.EmptyRes
}

// UserStatSearchReq 分页请求参数
type UserStatSearchReq struct {
	g.Meta `path:"/userStat" tags:"分销统计" method:"get" summary:"分销用户管理"`
	commonApi.Author
	model.UserStatSearchReq
}

// UserStatSearchRes 返回参数
type UserStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.UserStatSearchRes
}

// LoginAccountInfoAddReq 添加操作请求参数
type LoginAccountInfoAddReq struct {
	g.Meta `path:"/add" tags:"用户账户信息表" method:"post" summary:"用户账户信息表添加"`
	commonApi.Author
	*model.LoginAccountInfoAddReq
}

// LoginAccountInfoAddRes 添加操作返回结果
type LoginAccountInfoAddRes struct {
	commonApi.EmptyRes
}

// LoginAccountInfoEditReq 修改操作请求参数
type LoginAccountInfoEditReq struct {
	g.Meta `path:"/edit" tags:"用户账户信息表" method:"put" summary:"用户账户信息表修改"`
	commonApi.Author
	*model.LoginAccountInfoEditReq
}

// LoginAccountInfoEditRes 修改操作返回结果
type LoginAccountInfoEditRes struct {
	commonApi.EmptyRes
}

// LoginAccountInfoGetReq 获取一条数据请求
type LoginAccountInfoGetReq struct {
	g.Meta `path:"/get" tags:"用户账户信息表" method:"get" summary:"获取用户账户信息表信息"`
	commonApi.Author
	Uid string `p:"uid" v:"required#主键必须"` //通过主键获取
}

// LoginAccountInfoGetRes 获取一条数据结果
type LoginAccountInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.LoginAccountInfoInfoRes
}

// LoginAccountInfoDeleteReq 删除数据请求
type LoginAccountInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户账户信息表" method:"post" summary:"删除用户账户信息表"`
	commonApi.Author
	Uids []string `p:"uids" v:"required#主键必须"` //通过主键删除
}

// LoginAccountInfoDeleteRes 删除数据返回
type LoginAccountInfoDeleteRes struct {
	commonApi.EmptyRes
}

// UserListSearchReq 分页请求参数
type UserListSearchReq struct {
	g.Meta `path:"/userList" tags:"用户管理" method:"post" summary:"用户列表"`
	commonApi.Author
	model.UserListSearchReq
}
type UserLockInfoSearchReq struct {
	g.Meta `path:"/userLockInfo" tags:"用户管理" method:"post" summary:"用户锁定信息"`
	commonApi.Author
	model.UserListSearchReq
}

// UserListSearchRes 返回参数
type UserListSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.UserListSearchRes
}
type LoginLockInfoRes struct {
	g.Meta `mime:"application/json"`
	*model.LoginLockInfoRes
}

// RefreshUserAdCacheReq 刷新用户广告缓存请求参数
type RefreshUserAdCacheReq struct {
	g.Meta `path:"/refresh/ad/cache" tags:"用户管理" method:"post" summary:"刷新用户广告缓存"`
	commonApi.Author
	model.RefreshUserAdCacheReq
}

// RefreshUserAdCacheRes 返回参数
type RefreshUserAdCacheRes struct {
	g.Meta `mime:"application/json"`
}

// DelOldUserAdCacheReq 刷新用户广告缓存请求参数
type DelOldUserAdCacheReq struct {
	g.Meta `path:"/delete/ad/cache" tags:"用户管理" method:"post" summary:"删除用户广告缓存"`
	commonApi.Author
	model.DelOldUserAdCacheReq
}

// DelOldUserAdCacheRes 返回参数
type DelOldUserAdCacheRes struct {
	g.Meta `mime:"application/json"`
}
