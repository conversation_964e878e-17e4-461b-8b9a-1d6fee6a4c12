// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:33:13
// 生成路径: internal/app/adx/logic/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdxCreative(New())
}

func New() service.IAdxCreative {
	return &sAdxCreative{}
}

type sAdxCreative struct{}

func (s *sAdxCreative) List(ctx context.Context, req *model.AdxCreativeSearchReq) (listRes *model.AdxCreativeSearchRes, err error) {
	listRes = new(model.AdxCreativeSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxCreative.Ctx(ctx).WithAll()
		if req.CreativeId != "" {
			m = m.Where(dao.AdxCreative.Columns().CreativeId+" = ?", req.CreativeId)
		}
		if req.FirstSeen != "" {
			m = m.Where(dao.AdxCreative.Columns().FirstSeen+" = ?", req.FirstSeen)
		}
		if req.LastSeen != "" {
			m = m.Where(dao.AdxCreative.Columns().LastSeen+" = ?", req.LastSeen)
		}
		if req.MaterialId != "" {
			m = m.Where(dao.AdxCreative.Columns().MaterialId+" = ?", gconv.Uint64(req.MaterialId))
		}
		if req.MediaId != "" {
			m = m.Where(dao.AdxCreative.Columns().MediaId+" = ?", gconv.Uint(req.MediaId))
		}
		if req.MobileType != "" {
			m = m.Where(dao.AdxCreative.Columns().MobileType+" = ?", gconv.Uint(req.MobileType))
		}
		if req.PositionId != "" {
			m = m.Where(dao.AdxCreative.Columns().PositionId+" = ?", gconv.Uint(req.PositionId))
		}
		if req.ProductId != "" {
			m = m.Where(dao.AdxCreative.Columns().ProductId+" = ?", gconv.Uint64(req.ProductId))
		}
		if req.PublisherId != "" {
			m = m.Where(dao.AdxCreative.Columns().PublisherId+" = ?", gconv.Uint64(req.PublisherId))
		}
		if req.TalentAccount != "" {
			m = m.Where(dao.AdxCreative.Columns().TalentAccount+" = ?", req.TalentAccount)
		}
		if req.TalentName != "" {
			m = m.Where(dao.AdxCreative.Columns().TalentName+" like ?", "%"+req.TalentName+"%")
		}
		if req.TargetUrl != "" {
			m = m.Where(dao.AdxCreative.Columns().TargetUrl+" = ?", req.TargetUrl)
		}
		if req.TargetUrlId != "" {
			m = m.Where(dao.AdxCreative.Columns().TargetUrlId+" = ?", gconv.Uint64(req.TargetUrlId))
		}
		if req.Title1 != "" {
			m = m.Where(dao.AdxCreative.Columns().Title1+" = ?", req.Title1)
		}
		if req.Title2 != "" {
			m = m.Where(dao.AdxCreative.Columns().Title2+" = ?", req.Title2)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "creative_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxCreativeListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxCreativeListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxCreativeListRes{
				CreativeId:    v.CreativeId,
				FirstSeen:     v.FirstSeen,
				LastSeen:      v.LastSeen,
				MaterialId:    v.MaterialId,
				MediaId:       v.MediaId,
				MobileType:    v.MobileType,
				NumDate:       v.NumDate,
				PositionId:    v.PositionId,
				ProductId:     v.ProductId,
				PublisherId:   v.PublisherId,
				TalentAccount: v.TalentAccount,
				TalentName:    v.TalentName,
				TargetUrl:     v.TargetUrl,
				TargetUrlId:   v.TargetUrlId,
				Title1:        v.Title1,
				Title2:        v.Title2,
			}
		}
	})
	return
}

func (s *sAdxCreative) GetExportData(ctx context.Context, req *model.AdxCreativeSearchReq) (listRes []*model.AdxCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxCreative.Ctx(ctx).WithAll()
		if req.CreativeId != "" {
			m = m.Where(dao.AdxCreative.Columns().CreativeId+" = ?", req.CreativeId)
		}
		if req.FirstSeen != "" {
			m = m.Where(dao.AdxCreative.Columns().FirstSeen+" = ?", req.FirstSeen)
		}
		if req.LastSeen != "" {
			m = m.Where(dao.AdxCreative.Columns().LastSeen+" = ?", req.LastSeen)
		}
		if req.MaterialId != "" {
			m = m.Where(dao.AdxCreative.Columns().MaterialId+" = ?", gconv.Uint64(req.MaterialId))
		}
		if req.MediaId != "" {
			m = m.Where(dao.AdxCreative.Columns().MediaId+" = ?", gconv.Uint(req.MediaId))
		}
		if req.MobileType != "" {
			m = m.Where(dao.AdxCreative.Columns().MobileType+" = ?", gconv.Uint(req.MobileType))
		}
		if req.PositionId != "" {
			m = m.Where(dao.AdxCreative.Columns().PositionId+" = ?", gconv.Uint(req.PositionId))
		}
		if req.ProductId != "" {
			m = m.Where(dao.AdxCreative.Columns().ProductId+" = ?", gconv.Uint64(req.ProductId))
		}
		if req.PublisherId != "" {
			m = m.Where(dao.AdxCreative.Columns().PublisherId+" = ?", gconv.Uint64(req.PublisherId))
		}
		if req.TalentAccount != "" {
			m = m.Where(dao.AdxCreative.Columns().TalentAccount+" = ?", req.TalentAccount)
		}
		if req.TalentName != "" {
			m = m.Where(dao.AdxCreative.Columns().TalentName+" like ?", "%"+req.TalentName+"%")
		}
		if req.TargetUrl != "" {
			m = m.Where(dao.AdxCreative.Columns().TargetUrl+" = ?", req.TargetUrl)
		}
		if req.TargetUrlId != "" {
			m = m.Where(dao.AdxCreative.Columns().TargetUrlId+" = ?", gconv.Uint64(req.TargetUrlId))
		}
		if req.Title1 != "" {
			m = m.Where(dao.AdxCreative.Columns().Title1+" = ?", req.Title1)
		}
		if req.Title2 != "" {
			m = m.Where(dao.AdxCreative.Columns().Title2+" = ?", req.Title2)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "creative_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxCreative) GetByCreativeId(ctx context.Context, creativeId uint64) (res *model.AdxCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxCreative.Ctx(ctx).WithAll().Where(dao.AdxCreative.Columns().CreativeId, creativeId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxCreative) GetMinByProductIds(ctx context.Context, productIds []int64) (res *model.AdxCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxCreative.Ctx(ctx).WithAll().
			FieldMin("creative_id", "creativeId").
			WhereIn(dao.AdxCreative.Columns().ProductId, productIds).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxCreative) GetMinByPublisherIds(ctx context.Context, publisherIds []int64) (res *model.AdxCreativeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxCreative.Ctx(ctx).WithAll().
			FieldMin("creative_id", "creativeId").
			WhereIn(dao.AdxCreative.Columns().PublisherId, publisherIds).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxCreative) Add(ctx context.Context, req *model.AdxCreativeAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxCreative.Ctx(ctx).Insert(do.AdxCreative{
			CreativeId:    req.CreativeId,
			FirstSeen:     req.FirstSeen,
			LastSeen:      req.LastSeen,
			MaterialId:    req.MaterialId,
			MediaId:       req.MediaId,
			MobileType:    req.MobileType,
			NumDate:       req.NumDate,
			PositionId:    req.PositionId,
			ProductId:     req.ProductId,
			PublisherId:   req.PublisherId,
			TalentAccount: req.TalentAccount,
			TalentName:    req.TalentName,
			TargetUrl:     req.TargetUrl,
			TargetUrlId:   req.TargetUrlId,
			Title1:        req.Title1,
			Title2:        req.Title2,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxCreative) BatchAdd(ctx context.Context, batchReq []*model.AdxCreativeAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdxCreative, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdxCreative{
				CreativeId:    v.CreativeId,
				FirstSeen:     v.FirstSeen,
				LastSeen:      v.LastSeen,
				MaterialId:    v.MaterialId,
				MediaId:       v.MediaId,
				MobileType:    v.MobileType,
				NumDate:       v.NumDate,
				PositionId:    v.PositionId,
				ProductId:     v.ProductId,
				PublisherId:   v.PublisherId,
				TalentAccount: v.TalentAccount,
				TalentName:    v.TalentName,
				TargetUrl:     v.TargetUrl,
				TargetUrlId:   v.TargetUrlId,
				Title1:        v.Title1,
				Title2:        v.Title2,
			}
		}
		_, err = dao.AdxCreative.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxCreative) Edit(ctx context.Context, req *model.AdxCreativeEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxCreative.Ctx(ctx).WherePri(req.CreativeId).Update(do.AdxCreative{
			FirstSeen:     req.FirstSeen,
			LastSeen:      req.LastSeen,
			CreativeId:    req.CreativeId,
			MediaId:       req.MediaId,
			MobileType:    req.MobileType,
			NumDate:       req.NumDate,
			PositionId:    req.PositionId,
			ProductId:     req.ProductId,
			PublisherId:   req.PublisherId,
			TalentAccount: req.TalentAccount,
			TalentName:    req.TalentName,
			TargetUrl:     req.TargetUrl,
			TargetUrlId:   req.TargetUrlId,
			Title1:        req.Title1,
			Title2:        req.Title2,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxCreative) Delete(ctx context.Context, creativeIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxCreative.Ctx(ctx).Delete(dao.AdxCreative.Columns().CreativeId+" in (?)", creativeIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdxCreative) RunSyncAdxCreativeTask(ctx context.Context, req *model.AdxCreativeSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		startTime := req.StartTime
		endTime := req.EndTime
		for {
			if startTime > endTime {
				break
			}
			errors := s.SyncAdxCreative(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdxCreative) SyncAdxCreativeTask(ctx context.Context) {
	err := service.AdxMaterial().ExecuteWithTimeout(
		ctx,
		commonConsts.PlatAdxCreativeLock,
		59*time.Minute,
		"同步昨天ADX广告计划",
		"SyncAdxCreativeTask",
		func(ctx context.Context) error {
			yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
			return s.SyncAdxCreative(ctx, yesterday)
		},
	)
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxCreative) SyncTodayAdxCreativeTask(ctx context.Context) {
	err := service.AdxMaterial().ExecuteWithTimeout(
		ctx,
		commonConsts.PlatAdxCreativeTodayLock,
		(15*60-10)*time.Second,
		"同步当天ADX广告计划",
		"SyncTodayAdxCreativeTask",
		func(ctx context.Context) error {
			today := gtime.Now().Format("Y-m-d")
			return s.SyncAdxCreative(ctx, today)
		},
	)
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxCreative) SyncAdxCreative(ctx context.Context, updateDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var id int64 = 0
		var pageSize = 500
		var pullType = 2
		if updateDate == "" {
			pullType = 1
		}
		var pageIdKey string
		var ttlInSeconds int64
		if pullType == 1 {
			pageIdKey = commonConsts.PlatAdxCreativePageId
		} else {
			pageIdKey = fmt.Sprintf("%s%s", commonConsts.PlatAdxCreativeDayPageId, updateDate)
			ttlInSeconds = commonConsts.PlatAdxPageIdTtlSeconds
		}
		data, _ := commonService.RedisCache().Get(ctx, pageIdKey)
		if !data.IsNil() {
			id = data.Int64()
		}
		maxRetries := 5
		retryCount := 0
		waitTime := 10 * time.Second
		for {
			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				g.Log().Info(ctx, "SyncAdxCreative任务被取消或超时")
				return
			default:
				// 继续执行
			}
			creativeRes, err1 := api.GetAdxApiClient().CreativeService.SetReq(api.CreativeRequest{
				Id:         id,
				PageSize:   pageSize,
				PullType:   pullType,
				UpdateDate: updateDate,
			}).Do()
			if err1 != nil && err1.Error() == "Error limiting" {
				retryCount++
				if retryCount > maxRetries {
					g.Log().Error(ctx, "SyncAdxCreative Maximum retries reached for Error limiting")
					break
				}
				g.Log().Infof(ctx, "SyncAdxCreative Rate limit hit, retrying in %v (attempt %d/%d)", waitTime, retryCount, maxRetries)
				time.Sleep(waitTime)
				continue
			}
			retryCount = 0
			if err1 != nil {
				g.Log().Errorf(ctx, "SyncAdxCreative异常：%v", err1)
				break
			}
			var batchReq []*model.AdxCreativeAddReq
			for _, v := range creativeRes.Content {
				batchReq = append(batchReq, &model.AdxCreativeAddReq{
					CreativeId:    uint64(v.CreativeId),
					FirstSeen:     v.FirstSeen,
					LastSeen:      v.LastSeen,
					MaterialId:    uint64(v.MaterialId),
					MediaId:       uint(v.MediaId),
					MobileType:    uint(v.MobileType),
					NumDate:       v.NumDate,
					PositionId:    uint(v.PositionId),
					ProductId:     uint64(v.ProductId),
					PublisherId:   uint64(v.PublisherId),
					TalentAccount: v.TalentAccount,
					TalentName:    v.TalentName,
					TargetUrl:     v.TargetUrl,
					TargetUrlId:   uint64(v.TargetUrlId),
					Title1:        v.Title1,
					Title2:        v.Title2,
				})
			}
			if len(batchReq) > 0 {
				err = s.BatchAdd(ctx, batchReq)
				if err != nil {
					g.Log().Error(ctx, err)
					break
				}
			}
			if creativeRes.Page.PageId == id {
				break
			}
			id = creativeRes.Page.PageId
			if ttlInSeconds > 0 {
				_ = commonService.RedisCache().SetEX(ctx, pageIdKey, id, ttlInSeconds)
			} else {
				_, _ = commonService.RedisCache().Set(ctx, pageIdKey, id)
			}
		}
	})
	return
}
