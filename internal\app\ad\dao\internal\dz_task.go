// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: internal/app/ad/dao/internal/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzTaskDao struct {
	table   string        // Table is the underlying table name of the DAO.
	group   string        // Group is the database configuration group name of current DAO.
	columns DzTaskColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzTaskColumns defines and stores column names for table dz_task.
type DzTaskColumns struct {
	TaskId    string // 任务id
	QueryUrl  string // 查询url
	QueryJson string // 查询参数
	CreatedAt string // 创建时间
}

var dzTaskColumns = DzTaskColumns{
	TaskId:    "task_id",
	QueryUrl:  "query_url",
	QueryJson: "query_json",
	CreatedAt: "created_at",
}

// NewDzTaskDao creates and returns a new DAO object for table data access.
func NewDzTaskDao() *DzTaskDao {
	return &DzTaskDao{
		group:   "default",
		table:   "dz_task",
		columns: dzTaskColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzTaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzTaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzTaskDao) Columns() DzTaskColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzTaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzTaskDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzTaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
