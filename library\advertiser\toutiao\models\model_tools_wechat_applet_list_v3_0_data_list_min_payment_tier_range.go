/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListMinPaymentTierRange
type ToolsWechatAppletListV30DataListMinPaymentTierRange string

// List of tools_wechat_applet_list_v3.0_data_list_min_payment_tier_range
const (
	SIX_TEN_ToolsWechatAppletListV30DataListMinPaymentTierRange      ToolsWechatAppletListV30DataListMinPaymentTierRange = "SIX_TEN"
	TEN_TWENTY_ToolsWechatAppletListV30DataListMinPaymentTierRange   ToolsWechatAppletListV30DataListMinPaymentTierRange = "TEN_TWENTY"
	TWENTY_FIFTY_ToolsWechatAppletListV30DataListMinPaymentTierRange ToolsWechatAppletListV30DataListMinPaymentTierRange = "TWENTY_FIFTY"
	ZERO_FIVE_ToolsWechatAppletListV30DataListMinPaymentTierRange    ToolsWechatAppletListV30DataListMinPaymentTierRange = "ZERO_FIVE"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_min_payment_tier_range value
func (v ToolsWechatAppletListV30DataListMinPaymentTierRange) Ptr() *ToolsWechatAppletListV30DataListMinPaymentTierRange {
	return &v
}
