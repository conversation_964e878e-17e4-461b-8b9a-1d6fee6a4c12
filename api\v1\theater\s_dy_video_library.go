// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-15 10:45:24
// 生成路径: api/v1/theater/s_dy_video_library.go
// 生成人：cq
// desc:抖音视频库表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyVideoLibrarySearchReq 分页请求参数
type SDyVideoLibrarySearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广 - 抖音内容库 - 视频库" method:"get" summary:"抖音视频库表列表"`
	commonApi.Author
	model.SDyVideoLibrarySearchReq
}

// SDyVideoLibrarySearchRes 列表返回结果
type SDyVideoLibrarySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyVideoLibrarySearchRes
}

// SDyVideoLibraryAddReq 添加操作请求参数
type SDyVideoLibraryAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广 - 抖音内容库 - 视频库" method:"post" summary:"抖音视频库表添加"`
	commonApi.Author
	*model.SDyVideoLibraryAddReq
}

// SDyVideoLibraryAddRes 添加操作返回结果
type SDyVideoLibraryAddRes struct {
	commonApi.EmptyRes
}

// SDyVideoLibraryEditReq 修改操作请求参数
type SDyVideoLibraryEditReq struct {
	g.Meta `path:"/edit" tags:"短剧推广 - 抖音内容库 - 视频库" method:"put" summary:"抖音视频库表修改"`
	commonApi.Author
	*model.SDyVideoLibraryEditReq
}

// SDyVideoLibraryEditRes 修改操作返回结果
type SDyVideoLibraryEditRes struct {
	commonApi.EmptyRes
}

// SDyVideoLibraryGetReq 获取一条数据请求
type SDyVideoLibraryGetReq struct {
	g.Meta `path:"/get" tags:"短剧推广 - 抖音内容库 - 视频库" method:"get" summary:"获取抖音视频库表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SDyVideoLibraryGetRes 获取一条数据结果
type SDyVideoLibraryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyVideoLibraryInfoRes
}

// SDyVideoLibraryDeleteReq 删除数据请求
type SDyVideoLibraryDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧推广 - 抖音内容库 - 视频库" method:"post" summary:"删除抖音视频库表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SDyVideoLibraryDeleteRes 删除数据返回
type SDyVideoLibraryDeleteRes struct {
	commonApi.EmptyRes
}
