// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-13 15:31:10
// 生成路径: internal/app/ad/model/entity/ad_material_album_users.go
// 生成人：cyao
// desc:广告素材专辑和用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterialAlbumUsers is the golang structure for table ad_material_album_users.
type AdMaterialAlbumUsers struct {
	gmeta.Meta    `orm:"table:ad_material_album_users, do:true"`
	AlbumId       interface{} `orm:"album_id,primary" json:"albumId"`              // 专辑ID
	SpecifyUserId interface{} `orm:"specify_user_id,primary" json:"specifyUserId"` // 用户ID
}
