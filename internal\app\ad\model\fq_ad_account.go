// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/model/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdAccountInfoRes is the golang structure for table fq_ad_account.
type FqAdAccountInfoRes struct {
	gmeta.Meta    `orm:"table:fq_ad_account"`
	DistributorId int64       `orm:"distributor_id,primary" json:"distributorId" dc:"渠道ID"` // 渠道ID
	SecretKey     string      `orm:"secret_key" json:"secretKey" dc:"秘钥"`                   // 秘钥
	AccountName   string      `orm:"account_name" json:"accountName" dc:"账号名"`             // 账号名
	DramaType     string      `orm:"drama_type" json:"dramaType" dc:"抖小IAP 抖小IAA 微小IAP 微小IAA"`
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
	DeletedAt     *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"` // 删除时间
}

type FqAdAccountListRes struct {
	DistributorId int64       `json:"distributorId" dc:"渠道ID"`
	SecretKey     string      `json:"secretKey" dc:"秘钥"`
	AccountName   string      `json:"accountName" dc:"账号名"`
	DramaType     string      `json:"dramaType" dc:"抖小IAP 抖小IAA 微小IAP 微小IAA"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
	UserList      []int64     `json:"userList" dc:"用户列表"`
	DeptList      []int64     `json:"deptList" dc:"部门列表"`
}

// FqAdAccountSearchReq 分页请求参数
type FqAdAccountSearchReq struct {
	comModel.PageReq
	DistributorId string   `p:"distributorId" dc:"渠道ID"`                                                          //渠道ID
	SecretKey     string   `p:"secretKey" dc:"秘钥"`                                                                //秘钥
	AccountName   string   `p:"accountName" dc:"账号名"`                                                            //账号名
	AccountNames  []string `p:"accountNames" dc:"账号名"`                                                           //账号名
	DramaType     string   `p:"dramaType" dc:"抖小IAP 抖小IAA 微小IAP 微小IAA"`                                     //短剧类型
	DramaTypes    []string `p:"dramaTypes" dc:"抖小IAP 抖小IAA 微小IAP 微小IAA"`                                    //短剧类型
	CreatedAt     string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// FqAdAccountSearchRes 列表返回结果
type FqAdAccountSearchRes struct {
	comModel.ListRes
	List []*FqAdAccountListRes `json:"list"`
}
type FqAdAddAuthReq struct {
	DistributorId  int64   `p:"distributorId"   dc:"渠道ID"`
	DistributorIds []int64 `p:"distributorIds"   dc:"渠道ID"`
	DeptIds        []int   `p:"deptIds"  dc:"指定部门id "`
	SpecifyUserIds []int   `p:"specifyUserIds"  dc:"指定用户id"`
}

// FqAdAccountAddReq 添加操作请求参数
type FqAdAccountAddReq struct {
	DistributorId int64  `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
	SecretKey     string `p:"secretKey"  dc:"秘钥"`
	AccountName   string `p:"accountName" v:"required#账号名不能为空" dc:"账号名"`
	DramaType     string `p:"dramaType"  dc:"抖小IAP 抖小IAA 微小IAP 微小IAA"`
}

// FqAdAccountEditReq 修改操作请求参数
type FqAdAccountEditReq struct {
	DistributorId       int64  `p:"distributorId" v:"required#主键ID不能为空" dc:"番茄账号ID"`
	BeforeDistributorId int64  `p:"beforeDistributorId"   dc:"之前的番茄账号ID"`
	SecretKey           string `p:"secretKey"  dc:"秘钥"`
	AccountName         string `p:"accountName" v:"required#账号名不能为空" dc:"账号名"`
	DramaType           string `p:"dramaType"  dc:"抖小IAP 抖小IAA 微小IAP 微小IAA"`
}
