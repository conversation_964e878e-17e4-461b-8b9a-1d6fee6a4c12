/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListMaxRechargeTier
type ToolsWechatAppletListV30DataListMaxRechargeTier string

// List of tools_wechat_applet_list_v3.0_data_list_max_recharge_tier
const (
	ABOVE_200_ToolsWechatAppletListV30DataListMaxRechargeTier             ToolsWechatAppletListV30DataListMaxRechargeTier = "ABOVE_200"
	FROM_100_TO_200_ToolsWechatAppletListV30DataListMaxRechargeTier       ToolsWechatAppletListV30DataListMaxRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletListV30DataListMaxRechargeTier ToolsWechatAppletListV30DataListMaxRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletListV30DataListMaxRechargeTier     ToolsWechatAppletListV30DataListMaxRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_max_recharge_tier value
func (v ToolsWechatAppletListV30DataListMaxRechargeTier) Ptr() *ToolsWechatAppletListV30DataListMaxRechargeTier {
	return &v
}
