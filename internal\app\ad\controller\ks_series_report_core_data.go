// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-10 14:40:38
// 生成路径: internal/app/ad/controller/ks_series_report_core_data.go
// 生成人：cyao
// desc:短剧核心总览数据报表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type ksSeriesReportCoreDataController struct {
	systemController.BaseController
}

var KsSeriesReportCoreData = new(ksSeriesReportCoreDataController)

// GetKsReportCoreData 通过api的形式获取短剧核心数据
func (c *ksSeriesReportCoreDataController) GetKsReportCoreData(ctx context.Context, req *ad.GetKsReportCoreDataReq) (res *ad.GetKsReportCoreDataRes, err error) {
	res = new(ad.GetKsReportCoreDataRes)
	res.SeriesCorePageDataSnake, err = service.KsSeriesReportCoreData().GetKsReportCoreData(ctx, req.QueryCoreDataReq)
	return
}

func (c *ksSeriesReportCoreDataController) PullKsSeriesReportCoreData(ctx context.Context, req *ad.PullKsSeriesReportCoreDataReq) (res *ad.PullKsSeriesReportCoreDataRes, err error) {
	res = new(ad.PullKsSeriesReportCoreDataRes)
	res.Count, err = service.KsSeriesReportCoreData().GetKsReportCoreDataStats(ctx, req.AdvertiserId, req.AppId, req.StatTime)
	return
}

// List 列表
func (c *ksSeriesReportCoreDataController) List(ctx context.Context, req *ad.KsSeriesReportCoreDataSearchReq) (res *ad.KsSeriesReportCoreDataSearchRes, err error) {
	res = new(ad.KsSeriesReportCoreDataSearchRes)
	res.KsSeriesReportCoreDataSearchRes, err = service.KsSeriesReportCoreData().List(ctx, &req.KsSeriesReportCoreDataSearchReq)
	return
}

// Export 导出excel
func (c *ksSeriesReportCoreDataController) Export(ctx context.Context, req *ad.KsSeriesReportCoreDataExportReq) (res *ad.KsSeriesReportCoreDataExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.KsSeriesReportCoreDataInfoRes
		//表头
		tableHead = []interface{}{"id自增", "账户ID", "短剧id", "消耗", "粉丝峰值量", "粉丝净增量", "商业化ROI", "全域ROI", "付费人数", "付费订单数", "付费金额", "是否粉丝", "播放数", "点赞数", "评论数", "收藏数", "IAA广告变现ROI（含返货）", "IAA广告变现LTV（含返货，元）", "IAA广告变现LTV（不含返货，元）", "IAA广告变现ROI（不含返货）", "日期（yyyy-MM-dd hh:mm:ss）"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.KsSeriesReportCoreData().GetExportData(ctx, &req.KsSeriesReportCoreDataSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.Id,
				v.AdvertiserId,
				v.SeriesId,
				v.TotalCharge,
				v.AccuFansUserNum,
				v.FansUserNum,
				v.EventPayRoi,
				v.EventPayRoiAll,
				v.PayUserCount,
				v.PayCount,
				v.PayAmt,
				v.IsFansUser,
				v.DisplayPlayCnt,
				v.DisplayLikeCnt,
				v.DisplayCommentCnt,
				v.DisplayCollectCnt,
				v.MiniGameIaaRoi,
				v.MiniGameIaaPurchaseAmount,
				v.MiniGameIaaPurchaseAmountDivided,
				v.MiniGameIaaDividedRoi,
				v.Date.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("短剧核心总览数据报表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取短剧核心总览数据报表
func (c *ksSeriesReportCoreDataController) Get(ctx context.Context, req *ad.KsSeriesReportCoreDataGetReq) (res *ad.KsSeriesReportCoreDataGetRes, err error) {
	res = new(ad.KsSeriesReportCoreDataGetRes)
	res.KsSeriesReportCoreDataInfoRes, err = service.KsSeriesReportCoreData().GetById(ctx, req.Id)
	return
}

// Add 添加短剧核心总览数据报表
func (c *ksSeriesReportCoreDataController) Add(ctx context.Context, req *ad.KsSeriesReportCoreDataAddReq) (res *ad.KsSeriesReportCoreDataAddRes, err error) {
	err = service.KsSeriesReportCoreData().Add(ctx, req.KsSeriesReportCoreDataAddReq)
	return
}

// Edit 修改短剧核心总览数据报表
func (c *ksSeriesReportCoreDataController) Edit(ctx context.Context, req *ad.KsSeriesReportCoreDataEditReq) (res *ad.KsSeriesReportCoreDataEditRes, err error) {
	err = service.KsSeriesReportCoreData().Edit(ctx, req.KsSeriesReportCoreDataEditReq)
	return
}

// Delete 删除短剧核心总览数据报表
func (c *ksSeriesReportCoreDataController) Delete(ctx context.Context, req *ad.KsSeriesReportCoreDataDeleteReq) (res *ad.KsSeriesReportCoreDataDeleteRes, err error) {
	err = service.KsSeriesReportCoreData().Delete(ctx, req.Ids)
	return
}
