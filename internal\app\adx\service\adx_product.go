// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:33:58
// 生成路径: internal/app/adx/service/adx_product.go
// 生成人：cq
// desc:ADX产品信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxProduct interface {
	List(ctx context.Context, req *model.AdxProductSearchReq) (res *model.AdxProductSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxProductSearchReq) (listRes []*model.AdxProductInfoRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.AdxProductInfoRes, err error)
	Add(ctx context.Context, req *model.AdxProductAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.AdxProductAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxProductEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
	RunSyncAdxProductTask(ctx context.Context, req *model.AdxProductSearchReq) (err error)
	SyncAdxProductTask(ctx context.Context)
	GetByName(ctx context.Context, name string, likeQuery bool) (res []*model.AdxProductInfoRes, err error)
}

var localAdxProduct IAdxProduct

func AdxProduct() IAdxProduct {
	if localAdxProduct == nil {
		panic("implement not found for interface IAdxProduct, forgot register?")
	}
	return localAdxProduct
}

func RegisterAdxProduct(i IAdxProduct) {
	localAdxProduct = i
}
