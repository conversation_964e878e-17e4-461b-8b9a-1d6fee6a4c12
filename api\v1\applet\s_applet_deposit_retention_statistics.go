// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-28 15:16:41
// 生成路径: api/v1/applet/s_applet_deposit_retention_statistics.go
// 生成人：cyao
// desc:小程序维度用户/充值留存数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SAppletDepositRetentionStatisticsSearchReq 分页请求参数
type SAppletDepositRetentionStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"小程序维度留存数据" method:"post" summary:"小程序维度用户充值留存数据列表"`
	commonApi.Author
	model.SAppletDepositRetentionStatisticsSearchReq
}

// SAppletDepositRetentionStatisticsSearchRes 列表返回结果
type SAppletDepositRetentionStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SAppletDepositRetentionStatisticsSearchRes
}

// RechargeStatExportReq 导出请求
type RechargeStatExportReq struct {
	g.Meta `path:"/applet/retention/stat/export" tags:"小程序维度留存数据" method:"post" summary:"小程序维度留存数据导出"`
	commonApi.Author
	model.SAppletDepositRetentionStatisticsSearchReq
}

type StatisticsReq struct {
	g.Meta `path:"/stat/task" tags:"Task" method:"post" summary:"SAppletDepositRetentionStatisticsSearch"`
	commonApi.Author
	StatDate  string
	StartTime string
	BeforeDay bool
}

type StatisticsRes struct {
	commonApi.EmptyRes
}

// SAppletDepositRetentionStatisticsAddReq 添加操作请求参数
type SAppletDepositRetentionStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"小程序维度留存数据" method:"post" summary:"小程序维度用户充值留存数据添加"`
	commonApi.Author
	*model.SAppletDepositRetentionStatisticsAddReq
}

// SAppletDepositRetentionStatisticsAddRes 添加操作返回结果
type SAppletDepositRetentionStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SAppletDepositRetentionStatisticsEditReq 修改操作请求参数
type SAppletDepositRetentionStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"小程序维度留存数据" method:"put" summary:"小程序维度留存数据修改"`
	commonApi.Author
	*model.SAppletDepositRetentionStatisticsEditReq
}

// SAppletDepositRetentionStatisticsEditRes 修改操作返回结果
type SAppletDepositRetentionStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SAppletDepositRetentionStatisticsGetReq 获取一条数据请求
type SAppletDepositRetentionStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"小程序维度留存数据" method:"get" summary:"获取小程序维度用户充值留存数据信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SAppletDepositRetentionStatisticsGetRes 获取一条数据结果
type SAppletDepositRetentionStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SAppletDepositRetentionStatisticsInfoRes
}

// SAppletDepositRetentionStatisticsDeleteReq 删除数据请求
type SAppletDepositRetentionStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"小程序维度留存数据" method:"delete" summary:"删除小程序维度用户充值留存数据"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SAppletDepositRetentionStatisticsDeleteRes 删除数据返回
type SAppletDepositRetentionStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
