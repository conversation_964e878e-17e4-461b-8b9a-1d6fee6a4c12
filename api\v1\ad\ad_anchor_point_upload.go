// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: api/v1/ad/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdAnchorPointUploadSearchReq 分页请求参数
type AdAnchorPointUploadSearchReq struct {
	g.Meta `path:"/list" tags:"推送到巨量的原生锚点" method:"get" summary:"推送到巨量的原生锚点列表"`
	commonApi.Author
	model.AdAnchorPointUploadSearchReq
}

// AdAnchorPointUploadSearchRes 列表返回结果
type AdAnchorPointUploadSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAnchorPointUploadSearchRes
}

// AdAnchorPointUploadAddReq 添加操作请求参数
type AdAnchorPointUploadAddReq struct {
	g.Meta `path:"/add" tags:"推送到巨量的原生锚点" method:"post" summary:"推送到巨量的原生锚点添加"`
	commonApi.Author
	*model.AdAnchorPointUploadAddReq
}

// AdAnchorPointUploadAddRes 添加操作返回结果
type AdAnchorPointUploadAddRes struct {
	commonApi.EmptyRes
}

// AdAnchorPointUploadEditReq 修改操作请求参数
type AdAnchorPointUploadEditReq struct {
	g.Meta `path:"/edit" tags:"推送到巨量的原生锚点" method:"put" summary:"推送到巨量的原生锚点修改"`
	commonApi.Author
	*model.AdAnchorPointUploadEditReq
}

// AdAnchorPointUploadEditRes 修改操作返回结果
type AdAnchorPointUploadEditRes struct {
	commonApi.EmptyRes
}

// AdAnchorPointUploadGetReq 获取一条数据请求
type AdAnchorPointUploadGetReq struct {
	g.Meta `path:"/get" tags:"推送到巨量的原生锚点" method:"get" summary:"获取推送到巨量的原生锚点信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAnchorPointUploadGetRes 获取一条数据结果
type AdAnchorPointUploadGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAnchorPointUploadInfoRes
}

// AdAnchorPointUploadDeleteReq 删除数据请求
type AdAnchorPointUploadDeleteReq struct {
	g.Meta `path:"/delete" tags:"推送到巨量的原生锚点" method:"delete" summary:"删除推送到巨量的原生锚点"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAnchorPointUploadDeleteRes 删除数据返回
type AdAnchorPointUploadDeleteRes struct {
	commonApi.EmptyRes
}
