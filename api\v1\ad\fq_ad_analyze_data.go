// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: api/v1/ad/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdAnalyzeDataSearchReq 分页请求参数
type FqAdAnalyzeDataSearchReq struct {
	g.Meta `path:"/list" tags:" 获取回本统计-汇总数据" method:"post" summary:" 获取回本统计-汇总数据列表"`
	commonApi.Author
	model.FqAdAnalyzeDataSearchReq
}

// FqAdAnalyzeDataSearchRes 列表返回结果
type FqAdAnalyzeDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAnalyzeDataSearchRes
}

// FqAdAnalyzeDataPullReq
type FqAdAnalyzeDataPullReq struct {
	g.Meta `path:"/analyzeData/pull" tags:"获取回本统计-汇总数据" method:"get" summary:"pull"`
	commonApi.Author
}

// FqAdAnalyzeDataPullRes
type FqAdAnalyzeDataPullRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// FqAdAnalyzeDataExportReq 导出请求
type FqAdAnalyzeDataExportReq struct {
	g.Meta `path:"/export" tags:" 获取回本统计-汇总数据" method:"get" summary:" 获取回本统计-汇总数据导出"`
	commonApi.Author
	model.FqAdAnalyzeDataSearchReq
}

// FqAdAnalyzeDataExportRes 导出响应
type FqAdAnalyzeDataExportRes struct {
	commonApi.EmptyRes
}

// FqAdAnalyzeDataAddReq 添加操作请求参数
type FqAdAnalyzeDataAddReq struct {
	g.Meta `path:"/add" tags:" 获取回本统计-汇总数据" method:"post" summary:" 获取回本统计-汇总数据添加"`
	commonApi.Author
	*model.FqAdAnalyzeDataAddReq
}

// FqAdAnalyzeDataAddRes 添加操作返回结果
type FqAdAnalyzeDataAddRes struct {
	commonApi.EmptyRes
}

// FqAdAnalyzeDataEditReq 修改操作请求参数
type FqAdAnalyzeDataEditReq struct {
	g.Meta `path:"/edit" tags:" 获取回本统计-汇总数据" method:"put" summary:" 获取回本统计-汇总数据修改"`
	commonApi.Author
	*model.FqAdAnalyzeDataEditReq
}

// FqAdAnalyzeDataEditRes 修改操作返回结果
type FqAdAnalyzeDataEditRes struct {
	commonApi.EmptyRes
}

// FqAdAnalyzeDataGetReq 获取一条数据请求
type FqAdAnalyzeDataGetReq struct {
	g.Meta `path:"/get" tags:" 获取回本统计-汇总数据" method:"get" summary:"获取 获取回本统计-汇总数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// FqAdAnalyzeDataGetRes 获取一条数据结果
type FqAdAnalyzeDataGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAnalyzeDataInfoRes
}

// FqAdAnalyzeDataDeleteReq 删除数据请求
type FqAdAnalyzeDataDeleteReq struct {
	g.Meta `path:"/delete" tags:" 获取回本统计-汇总数据" method:"delete" summary:"删除 获取回本统计-汇总数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// FqAdAnalyzeDataDeleteRes 删除数据返回
type FqAdAnalyzeDataDeleteRes struct {
	commonApi.EmptyRes
}
