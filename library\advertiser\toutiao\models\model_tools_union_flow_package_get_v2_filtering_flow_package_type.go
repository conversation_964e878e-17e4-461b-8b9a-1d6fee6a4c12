/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsUnionFlowPackageGetV2FilteringFlowPackageType
type ToolsUnionFlowPackageGetV2FilteringFlowPackageType string

// List of tools_union_flow_package_get_v2_filtering_flow_package_type
const (
	CUSTOMIZE_ToolsUnionFlowPackageGetV2FilteringFlowPackageType ToolsUnionFlowPackageGetV2FilteringFlowPackageType = "CUSTOMIZE"
	FEATURED_ToolsUnionFlowPackageGetV2FilteringFlowPackageType  ToolsUnionFlowPackageGetV2FilteringFlowPackageType = "FEATURED"
	SYSTEM_ToolsUnionFlowPackageGetV2FilteringFlowPackageType    ToolsUnionFlowPackageGetV2FilteringFlowPackageType = "SYSTEM"
)

// Ptr returns reference to tools_union_flow_package_get_v2_filtering_flow_package_type value
func (v ToolsUnionFlowPackageGetV2FilteringFlowPackageType) Ptr() *ToolsUnionFlowPackageGetV2FilteringFlowPackageType {
	return &v
}
