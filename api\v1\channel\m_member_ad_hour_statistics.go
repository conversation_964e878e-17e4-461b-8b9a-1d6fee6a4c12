// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-16 13:48:53
// 生成路径: api/v1/channel/m_member_ad_hour_statistics.go
// 生成人：cq
// desc:广告分时统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdHourStatisticsLineChartReq 广告分时统计折线图请求参数
type MMemberAdHourStatisticsLineChartReq struct {
	g.Meta `path:"/line/chart" tags:"广告分时统计" method:"post" summary:"广告分时统计折线图"`
	commonApi.Author
	model.MMemberAdHourStatisticsLineChartReq
}

// MMemberAdHourStatisticsLineChartRes 广告分时统计折线图返回结果
type MMemberAdHourStatisticsLineChartRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdHourStatisticsLineChartRes
}

// MMemberAdHourStatisticsSearchReq 广告分时统计请求参数
type MMemberAdHourStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"广告分时统计" method:"post" summary:"广告分时统计列表"`
	commonApi.Author
	model.MMemberAdHourStatisticsSearchReq
}

// MMemberAdHourStatisticsSearchRes 广告分时统计返回结果
type MMemberAdHourStatisticsSearchRes struct {
	commonApi.EmptyRes
	*model.MMemberAdHourStatisticsSearchRes
}

// MMemberAdHourStatisticsTaskReq 广告分时统计任务请求参数
type MMemberAdHourStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"广告分时统计" method:"post" summary:"广告分时统计任务"`
	commonApi.Author
	model.MMemberAdHourStatisticsTaskReq
}

// MMemberAdHourStatisticsTaskRes 广告分时统计任务返回结果
type MMemberAdHourStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}
