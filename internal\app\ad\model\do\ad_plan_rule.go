// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-11-27 11:18:34
// 生成路径: internal/app/ad/model/entity/ad_plan_rule.go
// 生成人：cq
// desc:广告计划规则设置
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdPlanRule is the golang structure for table ad_plan_rule.
type AdPlanRule struct {
	gmeta.Meta  `orm:"table:ad_plan_rule, do:true"`
	Id          interface{} `orm:"id,primary" json:"id"`            // ID
	PlanId      interface{} `orm:"plan_id" json:"planId"`           // 计划ID
	TimeScope   interface{} `orm:"time_scope" json:"timeScope"`     // 时间范围，0 表示当天，3 表示过去三天，依次类推
	MetricsName interface{} `orm:"metrics_name" json:"metricsName"` // 指标名，根据指标筛选数据是否达标
	Operator    interface{} `orm:"operator" json:"operator"`        // 运算符，例如 >、<、>= 等
	Unit        interface{} `orm:"unit" json:"unit"`                // 单位，例如元、%
	TargetValue interface{} `orm:"target_value" json:"targetValue"` // 运算的最终目标值
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt"`     // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt"`     // 更新时间
	DeletedAt   *gtime.Time `orm:"deleted_at" json:"deletedAt"`     // 删除时间
}
