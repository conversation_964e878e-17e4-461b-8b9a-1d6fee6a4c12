// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: api/v1/ad/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdAccountSearchReq 分页请求参数
type FqAdAccountSearchReq struct {
	g.Meta `path:"/list" tags:"番茄账号" method:"post" summary:"番茄账号列表"`
	commonApi.Author
	model.FqAdAccountSearchReq
}

// FqAdAccountSearchRes 列表返回结果
type FqAdAccountSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountSearchRes
}

type AddAuthReq struct {
	g.Meta `path:"/add/auth" tags:"番茄账号" method:"post" summary:"番茄账号添加授权"`
	commonApi.Author
	model.FqAdAddAuthReq
}

// AddAuthRes 添加操作返回结果
type AddAuthRes struct {
	commonApi.EmptyRes
}

type FqAdAccountReq struct {
	g.Meta `path:"/select/list" tags:"番茄账号" method:"get" summary:"获取当前账号下番茄账号列表"`
	commonApi.Author
}

// FqAdAccountRes 列表返回结果
type FqAdAccountRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountSearchRes
}

// FqAdAccountExportReq 导出请求
type FqAdAccountExportReq struct {
	g.Meta `path:"/export" tags:"番茄账号" method:"get" summary:"番茄账号导出"`
	commonApi.Author
	model.FqAdAccountSearchReq
}

// FqAdAccountExportRes 导出响应
type FqAdAccountExportRes struct {
	commonApi.EmptyRes
}

// FqAdAccountAddReq 添加操作请求参数
type FqAdAccountAddReq struct {
	g.Meta `path:"/add" tags:"番茄账号" method:"post" summary:"番茄账号添加"`
	commonApi.Author
	*model.FqAdAccountAddReq
}

// FqAdAccountAddRes 添加操作返回结果
type FqAdAccountAddRes struct {
	commonApi.EmptyRes
}

// FqAdAccountEditReq 修改操作请求参数
type FqAdAccountEditReq struct {
	g.Meta `path:"/edit" tags:"番茄账号" method:"put" summary:"番茄账号修改"`
	commonApi.Author
	*model.FqAdAccountEditReq
}

// FqAdAccountEditRes 修改操作返回结果
type FqAdAccountEditRes struct {
	commonApi.EmptyRes
}

// FqAdAccountGetReq 获取一条数据请求
type FqAdAccountGetReq struct {
	g.Meta `path:"/get" tags:"番茄账号" method:"get" summary:"获取番茄账号信息"`
	commonApi.Author
	DistributorId int64 `p:"distributorId" v:"required#主键必须"` //通过主键获取
}

// FqAdAccountGetRes 获取一条数据结果
type FqAdAccountGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountInfoRes
}

// FqAdAccountDeleteReq 删除数据请求
type FqAdAccountDeleteReq struct {
	g.Meta `path:"/delete" tags:"番茄账号" method:"delete" summary:"删除番茄账号"`
	commonApi.Author
	DistributorIds []int64 `p:"distributorIds" v:"required#主键必须"` //通过主键删除
}

// FqAdAccountDeleteRes 删除数据返回
type FqAdAccountDeleteRes struct {
	commonApi.EmptyRes
}
