// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-13 15:31:10
// 生成路径: api/v1/ad/ad_material_album_users.go
// 生成人：cyao
// desc:广告素材专辑和用户关联相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdMaterialAlbumUsersSearchReq 分页请求参数
type AdMaterialAlbumUsersSearchReq struct {
	g.Meta `path:"/list" tags:"广告素材专辑和用户关联" method:"get" summary:"广告素材专辑和用户关联列表"`
	commonApi.Author
	model.AdMaterialAlbumUsersSearchReq
}

// AdMaterialAlbumUsersSearchRes 列表返回结果
type AdMaterialAlbumUsersSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialAlbumUsersSearchRes
}

// AdMaterialAlbumUsersAddReq 添加操作请求参数
type AdMaterialAlbumUsersAddReq struct {
	g.Meta `path:"/add" tags:"广告素材专辑和用户关联" method:"post" summary:"广告素材专辑和用户关联添加"`
	commonApi.Author
	*model.AdMaterialAlbumUsersAddReq
}

// AdMaterialAlbumUsersAddRes 添加操作返回结果
type AdMaterialAlbumUsersAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialAlbumUsersEditReq 修改操作请求参数
type AdMaterialAlbumUsersEditReq struct {
	g.Meta `path:"/edit" tags:"广告素材专辑和用户关联" method:"put" summary:"广告素材专辑和用户关联修改"`
	commonApi.Author
	*model.AdMaterialAlbumUsersEditReq
}

// AdMaterialAlbumUsersEditRes 修改操作返回结果
type AdMaterialAlbumUsersEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialAlbumUsersGetReq 获取一条数据请求
type AdMaterialAlbumUsersGetReq struct {
	g.Meta `path:"/get" tags:"广告素材专辑和用户关联" method:"get" summary:"获取广告素材专辑和用户关联信息"`
	commonApi.Author
	AlbumId int `p:"albumId" v:"required#主键必须"` //通过主键获取
}

// AdMaterialAlbumUsersGetRes 获取一条数据结果
type AdMaterialAlbumUsersGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialAlbumUsersInfoRes
}

// AdMaterialAlbumUsersDeleteReq 删除数据请求
type AdMaterialAlbumUsersDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告素材专辑和用户关联" method:"delete" summary:"删除广告素材专辑和用户关联"`
	commonApi.Author
	AlbumIds []int `p:"albumIds" v:"required#主键必须"` //通过主键删除
}

// AdMaterialAlbumUsersDeleteRes 删除数据返回
type AdMaterialAlbumUsersDeleteRes struct {
	commonApi.EmptyRes
}
