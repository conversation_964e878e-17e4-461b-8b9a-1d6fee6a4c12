package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxCreative"
	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxHotRanking"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxMaterial"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxMedia"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxPlaylet"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxProduct"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxPublisher"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxTask"

	_ "github.com/tiger1103/gfast/v3/internal/app/adx/logic/adxTaskItem"
)
