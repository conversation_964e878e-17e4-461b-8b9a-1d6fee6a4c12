// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:25
// 生成路径: internal/app/ad/model/dz_ad_account.go
// 生成人：cyao
// desc:点众账号管理表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdAccountInfoRes is the golang structure for table dz_ad_account.
type DzAdAccountInfoRes struct {
	gmeta.Meta  `orm:"table:dz_ad_account"`
	Id          uint64      `orm:"id,primary" json:"id" dc:"主键ID"`              // 主键ID
	AccountName string      `orm:"account_name" json:"accountName" dc:"番茄账号名称"` // 番茄账号名称
	AccountId   string      `orm:"account_id" json:"accountId" dc:"账号ID"`       // 账号ID
	Token       string      `orm:"token" json:"token" dc:"接口token"`             // 接口token
	Remark      string      `orm:"remark" json:"remark" dc:"备注"`                // 备注
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`       // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`       // 更新时间
}

type DzAdAccountListRes struct {
	Id          uint64      `json:"id" dc:"主键ID"`
	AccountName string      `json:"accountName" dc:"番茄账号名称"`
	AccountId   string      `json:"accountId" dc:"账号ID"`
	Token       string      `json:"token" dc:"接口token"`
	Remark      string      `json:"remark" dc:"备注"`
	CreatedAt   *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// DzAdAccountSearchReq 分页请求参数
type DzAdAccountSearchReq struct {
	comModel.PageReq
	Id          string   `p:"id" dc:"主键ID"`                                                           //主键ID
	AccountName string   `p:"accountName" dc:"番茄账号名称"`                                                //番茄账号名称
	AccountId   string   `p:"accountId" dc:"账号ID"`                                                    //账号ID
	AccountIds  []string `p:"accountIds" dc:"账号ID"`                                                   //账号ID
	Token       string   `p:"token" dc:"接口token"`                                                     //接口token
	CreatedAt   string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// DzAdAccountSearchRes 列表返回结果
type DzAdAccountSearchRes struct {
	comModel.ListRes
	List []*DzAdAccountListRes `json:"list"`
}

// DzAdAccountAddReq 添加操作请求参数
type DzAdAccountAddReq struct {
	AccountName string `p:"accountName" v:"required#番茄账号名称不能为空" dc:"番茄账号名称"`
	AccountId   string `p:"accountId" v:"required#账号ID不能为空" dc:"账号ID"`
	Token       string `p:"token" v:"required#接口token不能为空" dc:"接口token"`
	Remark      string `p:"remark"  dc:"备注"`
}

// DzAdAccountEditReq 修改操作请求参数
type DzAdAccountEditReq struct {
	Id          uint64 `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	AccountName string `p:"accountName" v:"required#番茄账号名称不能为空" dc:"番茄账号名称"`
	AccountId   string `p:"accountId" v:"required#账号ID不能为空" dc:"账号ID"`
	Token       string `p:"token" v:"required#接口token不能为空" dc:"接口token"`
	Remark      string `p:"remark"  dc:"备注"`
}
