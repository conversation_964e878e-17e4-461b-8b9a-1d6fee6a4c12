// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/router/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/adx/controller"
)

func (router *Router) BindAdxMaterialController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adxMaterial", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdxMaterial,
		)
	})
}
