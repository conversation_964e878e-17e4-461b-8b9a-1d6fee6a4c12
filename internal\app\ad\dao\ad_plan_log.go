// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: internal/app/ad/dao/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adPlanLogDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adPlanLogDao struct {
	*internal.AdPlanLogDao
}

var (
	// AdPlanLog is globally public accessible object for table tools_gen_table operations.
	AdPlanLog = adPlanLogDao{
		internal.NewAdPlanLogDao(),
	}
)

// Fill with you ideas below.
