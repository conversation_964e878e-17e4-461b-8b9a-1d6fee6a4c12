// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: api/v1/ad/dz_task.go
// 生成人：cyao
// desc:记录任务日志相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzTaskSearchReq 分页请求参数
type DzTaskSearchReq struct {
	g.Meta `path:"/list" tags:"记录任务日志" method:"get" summary:"记录任务日志列表"`
	commonApi.Author
	model.DzTaskSearchReq
}

// DzTaskSearchRes 列表返回结果
type DzTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzTaskSearchRes
}

// DzTaskAddReq 添加操作请求参数
type DzTaskAddReq struct {
	g.Meta `path:"/add" tags:"记录任务日志" method:"post" summary:"记录任务日志添加"`
	commonApi.Author
	*model.DzTaskAddReq
}

// DzTaskAddRes 添加操作返回结果
type DzTaskAddRes struct {
	commonApi.EmptyRes
}

// DzTaskEditReq 修改操作请求参数
type DzTaskEditReq struct {
	g.Meta `path:"/edit" tags:"记录任务日志" method:"put" summary:"记录任务日志修改"`
	commonApi.Author
	*model.DzTaskEditReq
}

// DzTaskEditRes 修改操作返回结果
type DzTaskEditRes struct {
	commonApi.EmptyRes
}

// DzTaskGetReq 获取一条数据请求
type DzTaskGetReq struct {
	g.Meta `path:"/get" tags:"记录任务日志" method:"get" summary:"获取记录任务日志信息"`
	commonApi.Author
	TaskId string `p:"taskId" v:"required#主键必须"` //通过主键获取
}

// DzTaskGetRes 获取一条数据结果
type DzTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzTaskInfoRes
}

// DzTaskDeleteReq 删除数据请求
type DzTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"记录任务日志" method:"delete" summary:"删除记录任务日志"`
	commonApi.Author
	TaskIds []string `p:"taskIds" v:"required#主键必须"` //通过主键删除
}

// DzTaskDeleteRes 删除数据返回
type DzTaskDeleteRes struct {
	commonApi.EmptyRes
}
