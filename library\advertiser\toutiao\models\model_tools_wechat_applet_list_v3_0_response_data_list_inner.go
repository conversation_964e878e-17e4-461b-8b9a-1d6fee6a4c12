/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30ResponseDataListInner struct for ToolsWechatAppletListV30ResponseDataListInner
type ToolsWechatAppletListV30ResponseDataListInner struct {
	//
	AdvertiserId *int64                                       `json:"advertiser_id,omitempty"`
	AuditStatus  *ToolsWechatAppletListV30DataListAuditStatus `json:"audit_status,omitempty"`
	//
	CreateTime *string `json:"create_time,omitempty"`
	//
	DiscountRate *int64 `json:"discount_rate,omitempty"`
	//
	GuideText *string `json:"guide_text,omitempty"`
	//
	HasDiscount *bool `json:"has_discount,omitempty"`
	//
	HasOnlineEarning *bool `json:"has_online_earning,omitempty"`
	//
	HeaderImageUrl *string `json:"header_image_url,omitempty"`
	//
	IconImageUrl *string `json:"icon_image_url,omitempty"`
	//
	ImagesHorizontalUrl []string `json:"images_horizontal_url,omitempty"`
	//
	ImagesVerticalUrl []string `json:"images_vertical_url,omitempty"`
	//
	InstanceId *int64 `json:"instance_id,omitempty"`
	//
	Introduction *string `json:"introduction,omitempty"`
	//
	Labels              []string                                             `json:"labels,omitempty"`
	MaxPaymentTierRange *ToolsWechatAppletListV30DataListMaxPaymentTierRange `json:"max_payment_tier_range,omitempty"`
	MaxRechargeTier     *ToolsWechatAppletListV30DataListMaxRechargeTier     `json:"max_recharge_tier,omitempty"`
	MembershipType      *ToolsWechatAppletListV30DataListMembershipType      `json:"membership_type,omitempty"`
	MidPaymentTierRange *ToolsWechatAppletListV30DataListMidPaymentTierRange `json:"mid_payment_tier_range,omitempty"`
	MinPaymentTierRange *ToolsWechatAppletListV30DataListMinPaymentTierRange `json:"min_payment_tier_range,omitempty"`
	MinRechargeTier     *ToolsWechatAppletListV30DataListMinRechargeTier     `json:"min_recharge_tier,omitempty"`
	//
	ModifyTime *string `json:"modify_time,omitempty"`
	//
	Name *string `json:"name,omitempty"`
	//
	Path        *string                                      `json:"path,omitempty"`
	PaymentForm *ToolsWechatAppletListV30DataListPaymentForm `json:"payment_form,omitempty"`
	//
	PropName *string `json:"prop_name,omitempty"`
	//
	Reason                  *string                                                  `json:"reason,omitempty"`
	RecommendedRechargeTier *ToolsWechatAppletListV30DataListRecommendedRechargeTier `json:"recommended_recharge_tier,omitempty"`
	//
	RemarkMessage *string                                       `json:"remark_message,omitempty"`
	RevenueModel  *ToolsWechatAppletListV30DataListRevenueModel `json:"revenue_model,omitempty"`
	//
	TagInfo *string `json:"tag_info,omitempty"`
	//
	UserName *string `json:"user_name,omitempty"`
}
