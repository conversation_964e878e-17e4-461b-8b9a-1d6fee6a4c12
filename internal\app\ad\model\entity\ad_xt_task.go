// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: internal/app/ad/model/entity/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdXtTask is the golang structure for table ad_xt_task.
type AdXtTask struct {
	gmeta.Meta        `orm:"table:ad_xt_task"`
	Id                int64       `orm:"id,primary" json:"id"`                         // ID
	AdvertiserId      string      `orm:"advertiser_id" json:"advertiserId"`            // 账户ID 对应 star_id
	TaskId            string      `orm:"task_id" json:"taskId"`                        // 任务ID
	TaskName          string      `orm:"task_name" json:"taskName"`                    // 任务名称
	Attachments       string      `orm:"attachments" json:"attachments"`               // 参考素材
	AuthorTaskName    string      `orm:"author_task_name" json:"authorTaskName"`       // 达人侧任务名称
	TaskIcon          string      `orm:"task_icon" json:"taskIcon"`                    // 任务图标
	TaskHeadImage     string      `orm:"task_head_image" json:"taskHeadImage"`         // 任务头图
	StartTime         *gtime.Time `orm:"start_time" json:"startTime"`                  // 投稿开始时间
	EndTime           *gtime.Time `orm:"end_time" json:"endTime"`                      // 任务截止时间
	SampleVideo       string      `orm:"sample_video" json:"sampleVideo"`              // 示例视频
	MicroAppId        string      `orm:"micro_app_id" json:"microAppId"`               // 小程序ID
	StartPage         string      `orm:"start_page" json:"startPage"`                  // 小程序落地页地址
	AnchorTitle       string      `orm:"anchor_title" json:"anchorTitle"`              // 组件标题
	CommissionType    string      `orm:"commission_type" json:"commissionType"`        // 结算方式	0unkonw 1广告分成 2cps支付分佣 3cps绑定分佣 7混合分成
	AuthorScope       string      `orm:"author_scope" json:"authorScope"`              // 达人定向范围，任务定向类型判断规则参考
	ProviderScope     string      `orm:"provider_scope" json:"providerScope"`          // 服务商定向范围，任务定向类型判断规则参考
	CommissionRate    string      `orm:"commission_rate" json:"commissionRate"`        // 分佣比例，适用于付费分佣结算和广告分成结算
	AdCommissionRate  float64     `orm:"ad_commission_rate" json:"adCommissionRate"`   // 广告分成比例
	PayCommissionRate float64     `orm:"pay_commission_rate" json:"payCommissionRate"` // 付费分佣比例
	AccountDivideDay  string      `orm:"account_divide_day" json:"accountDivideDay"`   // 最长分账周期
	DemandDesc        string      `orm:"demand_desc" json:"demandDesc"`                // 任务介绍
	OmTaskStatus      int         `orm:"om_task_status" json:"omTaskStatus"`           // 任务状态：1审核中 2审核失败 3进行中 4下架 4计费中 6已取消 7已结束 8已关闭
	OmTaskTag         string      `orm:"om_task_tag" json:"omTaskTag"`                 // 任务标签
	AuthorList        string      `orm:"author_list" json:"authorList"`                //
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt"`                  // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt"`                  // 更新时间
	DeletedAt         *gtime.Time `orm:"deleted_at" json:"deletedAt"`                  // 删除时间
}
