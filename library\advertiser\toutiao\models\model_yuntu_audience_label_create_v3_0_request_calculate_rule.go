/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30RequestCalculateRule 计算规则
type YuntuAudienceLabelCreateV30RequestCalculateRule struct {
	BehaviorFilters YuntuAudienceLabelCreateV30RequestCalculateRuleBehaviorFilters `json:"behavior_filters"`
	// 匹配方式。其中内容人群标签下，title 必选，额外可多选 ocr_content 及 asr_content；搜索人群标签固定为 query；商品人群标签固定为 product_name。 title = 内容标题ocr,asr, query,product_name(商品名称)
	FieldFilters []*YuntuAudienceLabelCreateV30CalculateRuleFieldFilters    `json:"field_filters"`
	ItemFilters  YuntuAudienceLabelCreateV30RequestCalculateRuleItemFilters `json:"item_filters"`
	WordRule     YuntuAudienceLabelCreateV30RequestCalculateRuleWordRule    `json:"word_rule"`
}
