// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-25 17:09:57
// 生成路径: internal/app/ad/logic/ad_designer_material_report.go
// 生成人：cyao
// desc:设计师素材数据统计
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"time"
)

func init() {
	service.RegisterAdDesignerMaterialReport(New())
}

func New() service.IAdDesignerMaterialReport {
	return &sAdDesignerMaterialReport{}
}

type sAdDesignerMaterialReport struct{}

func (s *sAdDesignerMaterialReport) List(ctx context.Context, req *model.AdDesignerMaterialReportSearchReq) (listRes *model.AdDesignerMaterialSearchRes, err error) {
	listRes = new(model.AdDesignerMaterialSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {

		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})

		m := dao.AdDesignerMaterialReport.Ctx(ctx).WithAll().As("b").LeftJoin("sys_user", "u", "b.designer_id = u.id").
			LeftJoin("sys_dept", "d", "d.dept_id = u.dept_id")

		if !admin && len(userIds) > 0 {
			m = m.WhereIn("b.designer_id ", userIds)
		}

		if req.StartTime != "" {
			m = m.Where("b."+dao.AdDesignerMaterialReport.Columns().StatDate+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("b."+dao.AdDesignerMaterialReport.Columns().StatDate+" <= ?", req.EndTime)
		}

		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u."+sysDao.SysUser.Columns().DeptId, req.DeptIds)
		}

		if len(req.DesignerManager) > 0 {
			m = m.WhereLike("d."+sysDao.SysDept.Columns().Leader, req.DesignerManager)
		}

		if len(req.Designer) > 0 {
			m = m.WhereLike("u."+sysDao.SysUser.Columns().UserName, req.Designer)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "stat_date asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdDesignerMaterialReportListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		fields := make([]string, 0)
		fields = append(fields, "SUM(b.new_material_count) as newMaterialCount")
		fields = append(fields, "SUM(b.exposed_material_count) as exposedMaterialCount")
		fields = append(fields, "SUM(b.new_material_cost) as newMaterialCost")
		fields = append(fields, "SUM(b.total_cost) as totalCost")
		fields = append(fields, "SUM(b.impressions) as impressions")
		//平均千次展现费用：计算公式为 消耗/展示数*1000
		fields = append(fields, "SUM(b.total_cost)/SUM(b.impressions)*1000 as avgCpm")
		fields = append(fields, "SUM(b.clicks) as clicks")
		//点击数/展示数*100%
		fields = append(fields, "SUM(b.clicks)/SUM(b.impressions)*100 as ctr")
		fields = append(fields, "SUM(b.conversions) as conversions")
		// 转化成本：计算公式为 消耗/转化数
		fields = append(fields, "SUM(b.total_cost)/SUM(b.conversions) as cpa")
		// 为 转化数/点击数*100%
		fields = append(fields, "SUM(b.conversions)/SUM(b.clicks)*100 as conversionRate")
		fields = append(fields, "SUM(b.actives) as actives")
		// 激活成本：计算公式为 消耗/激活数，保留两位小数
		fields = append(fields, "SUM(b.total_cost)/SUM(b.actives) as activeCost")
		//激活率：计算公式为 激活数/点击数*100%
		fields = append(fields, "SUM(b.actives)/SUM(b.clicks)*100 as activeRate")
		fields = append(fields, "SUM(b.first_pay_count) as firstPayCount")
		// 首次付费率：计算公式为 首次付费数/激活数*100%
		fields = append(fields, "SUM(b.first_pay_count)/SUM(b.actives)*100 as firstPayRate")

		err = m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")

		listRes.List = make([]*model.AdDesignerMaterialReportListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdDesignerMaterialReportListRes{
				StatDate:             v.StatDate,
				DesignerName:         v.DesignerName,
				DesignerId:           v.DesignerId,
				NewMaterialCount:     v.NewMaterialCount,
				ExposedMaterialCount: v.ExposedMaterialCount,
				NewMaterialCost:      v.NewMaterialCost,
				TotalCost:            v.TotalCost,
				Impressions:          v.Impressions,
				AvgCpm:               libUtils.ToRound(v.AvgCpm, 2, libUtils.RoundHalfEven),
				Clicks:               v.Clicks,
				Ctr:                  libUtils.ToRound(v.Ctr, 2, libUtils.RoundHalfEven),
				Conversions:          v.Conversions,
				Cpa:                  libUtils.ToRound(v.Cpa, 2, libUtils.RoundHalfEven),
				ConversionRate:       libUtils.ToRound(v.ConversionRate, 2, libUtils.RoundHalfEven),
				Actives:              v.Actives,
				ActiveCost:           libUtils.ToRound(v.ActiveCost, 2, libUtils.RoundHalfEven),
				ActiveRate:           libUtils.ToRound(v.ActiveRate, 2, libUtils.RoundHalfEven),
				FirstPayCount:        v.FirstPayCount,
				FirstPayRate:         libUtils.ToRound(v.FirstPayRate, 2, libUtils.RoundHalfEven),
			}
		}

		listRes.Summary.AvgCpm = libUtils.ToRound(listRes.Summary.AvgCpm, 2, libUtils.RoundHalfEven)
		listRes.Summary.Ctr = libUtils.ToRound(listRes.Summary.Ctr, 2, libUtils.RoundHalfEven)
		listRes.Summary.Cpa = libUtils.ToRound(listRes.Summary.Cpa, 2, libUtils.RoundHalfEven)
		listRes.Summary.ConversionRate = libUtils.ToRound(listRes.Summary.ConversionRate, 2, libUtils.RoundHalfEven)
		listRes.Summary.ActiveCost = libUtils.ToRound(listRes.Summary.ActiveCost, 2, libUtils.RoundHalfEven)
		listRes.Summary.ActiveRate = libUtils.ToRound(listRes.Summary.ActiveRate, 2, libUtils.RoundHalfEven)
		listRes.Summary.FirstPayRate = libUtils.ToRound(listRes.Summary.FirstPayRate, 2, libUtils.RoundHalfEven)
	})
	return
}

// AdDesignerMaterialReport
func (s *sAdDesignerMaterialReport) AdDesignerMaterialReport(ctx context.Context, req *model.AdDesignerMaterialReportReq) (listRes *model.AdDesignerMaterialReportRes, err error) {
	listRes = new(model.AdDesignerMaterialReportRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})

		m := dao.AdDesignerMaterialReport.Ctx(ctx).WithAll().As("b").LeftJoin("sys_user", "u", "b.designer_id = u.id").
			LeftJoin("sys_dept", "d", "d.dept_id = u.dept_id")

		if !admin && len(userIds) > 0 {
			m = m.WhereIn("b.designer_id", userIds)
		}
		if req.StartTime != "" {
			m = m.Where("b."+dao.AdDesignerMaterialReport.Columns().StatDate+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where("b."+dao.AdDesignerMaterialReport.Columns().StatDate+" <= ?", req.EndTime)
		}

		if len(req.DeptIds) > 0 {
			m = m.WhereIn("u."+sysDao.SysUser.Columns().DeptId, req.DeptIds)
		}

		if len(req.DesignerManager) > 0 {
			m = m.WhereLike("d."+sysDao.SysDept.Columns().Leader, req.DesignerManager)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "stat_date asc"
		group := "b.stat_date ,d.leader"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		fields := make([]string, 0)
		fields = append(fields, "SUM(b.new_material_count) as newMaterialCount")
		fields = append(fields, "SUM(b.exposed_material_count) as exposedMaterialCount")
		fields = append(fields, "SUM(b.new_material_cost) as newMaterialCost")
		fields = append(fields, "SUM(b.total_cost) as totalCost")
		fields = append(fields, "SUM(b.impressions) as impressions")
		//平均千次展现费用：计算公式为 消耗/展示数*1000
		fields = append(fields, "SUM(b.total_cost)/SUM(b.impressions)*1000 as avgCpm")
		fields = append(fields, "SUM(b.clicks) as clicks")
		//点击数/展示数*100%
		fields = append(fields, "SUM(b.clicks)/SUM(b.impressions)*100 as ctr")
		fields = append(fields, "SUM(b.conversions) as conversions")
		// 转化成本：计算公式为 消耗/转化数
		fields = append(fields, "SUM(b.total_cost)/SUM(b.conversions) as cpa")
		// 为 转化数/点击数*100%
		fields = append(fields, "SUM(b.conversions)/SUM(b.clicks)*100 as conversionRate")
		fields = append(fields, "SUM(b.actives) as actives")
		// 激活成本：计算公式为 消耗/激活数，保留两位小数
		fields = append(fields, "SUM(b.total_cost)/SUM(b.actives) as activeCost")
		//激活率：计算公式为 激活数/点击数*100%
		fields = append(fields, "SUM(b.actives)/SUM(b.clicks)*100 as activeRate")
		fields = append(fields, "SUM(b.first_pay_count) as firstPayCount")
		// 首次付费率：计算公式为 首次付费数/激活数*100%
		fields = append(fields, "SUM(b.first_pay_count)/SUM(b.actives)*100 as firstPayRate")
		err = m.Fields(fields).Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")
		fields = append(fields, "b.stat_date as statDate")
		fields = append(fields, "d.leader as designerManager")

		var res []*model.AdDesignerMaterialReportListRes
		err = m.Group(group).Fields(fields).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdDesignerMaterialReportListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdDesignerMaterialReportListRes{
				StatDate:             v.StatDate,
				DesignerName:         v.DesignerName,
				DesignerId:           v.DesignerId,
				NewMaterialCount:     v.NewMaterialCount,
				ExposedMaterialCount: v.ExposedMaterialCount,
				NewMaterialCost:      v.NewMaterialCost,
				TotalCost:            v.TotalCost,
				Impressions:          v.Impressions,
				AvgCpm:               libUtils.ToRound(v.AvgCpm, 2, libUtils.RoundHalfEven),
				Clicks:               v.Clicks,
				Ctr:                  libUtils.ToRound(v.Ctr, 2, libUtils.RoundHalfEven),
				Conversions:          v.Conversions,
				Cpa:                  libUtils.ToRound(v.Cpa, 2, libUtils.RoundHalfEven),
				ConversionRate:       libUtils.ToRound(v.ConversionRate, 2, libUtils.RoundHalfEven),
				Actives:              v.Actives,
				ActiveCost:           libUtils.ToRound(v.ActiveCost, 2, libUtils.RoundHalfEven),
				ActiveRate:           libUtils.ToRound(v.ActiveRate, 2, libUtils.RoundHalfEven),
				FirstPayCount:        v.FirstPayCount,
				FirstPayRate:         libUtils.ToRound(v.FirstPayRate, 2, libUtils.RoundHalfEven),
			}
		}
		listRes.Summary.AvgCpm = libUtils.ToRound(listRes.Summary.AvgCpm, 2, libUtils.RoundHalfEven)
		listRes.Summary.Ctr = libUtils.ToRound(listRes.Summary.Ctr, 2, libUtils.RoundHalfEven)
		listRes.Summary.Cpa = libUtils.ToRound(listRes.Summary.Cpa, 2, libUtils.RoundHalfEven)
		listRes.Summary.ConversionRate = libUtils.ToRound(listRes.Summary.ConversionRate, 2, libUtils.RoundHalfEven)
		listRes.Summary.ActiveCost = libUtils.ToRound(listRes.Summary.ActiveCost, 2, libUtils.RoundHalfEven)
		listRes.Summary.ActiveRate = libUtils.ToRound(listRes.Summary.ActiveRate, 2, libUtils.RoundHalfEven)
		listRes.Summary.FirstPayRate = libUtils.ToRound(listRes.Summary.FirstPayRate, 2, libUtils.RoundHalfEven)

	})
	return
}

func (s *sAdDesignerMaterialReport) Add(ctx context.Context, req *model.AdDesignerMaterialReportAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdDesignerMaterialReport.Ctx(ctx).Insert(do.AdDesignerMaterialReport{
			StatDate:             req.StatDate,
			DesignerId:           req.DesignerId,
			NewMaterialCount:     req.NewMaterialCount,
			ExposedMaterialCount: req.ExposedMaterialCount,
			NewMaterialCost:      req.NewMaterialCost,
			TotalCost:            req.TotalCost,
			Impressions:          req.Impressions,
			AvgCpm:               req.AvgCpm,
			Clicks:               req.Clicks,
			Ctr:                  req.Ctr,
			Conversions:          req.Conversions,
			Cpa:                  req.Cpa,
			ConversionRate:       req.ConversionRate,
			Actives:              req.Actives,
			ActiveCost:           req.ActiveCost,
			ActiveRate:           req.ActiveRate,
			FirstPayCount:        req.FirstPayCount,
			FirstPayRate:         req.FirstPayRate,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// BatchAdd
func (s *sAdDesignerMaterialReport) BatchAdd(ctx context.Context, req []*model.AdDesignerMaterialReportAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdDesignerMaterialReport.Ctx(ctx).Batch(len(req)).Insert(req)
		liberr.ErrIsNil(ctx, err, "批量添加失败")
	})
	return
}

func (s *sAdDesignerMaterialReport) Edit(ctx context.Context, req *model.AdDesignerMaterialReportEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdDesignerMaterialReport.Ctx(ctx).WherePri(req.StatDate).Update(do.AdDesignerMaterialReport{
			DesignerId:           req.DesignerId,
			NewMaterialCount:     req.NewMaterialCount,
			ExposedMaterialCount: req.ExposedMaterialCount,
			NewMaterialCost:      req.NewMaterialCost,
			TotalCost:            req.TotalCost,
			Impressions:          req.Impressions,
			AvgCpm:               req.AvgCpm,
			Clicks:               req.Clicks,
			Ctr:                  req.Ctr,
			Conversions:          req.Conversions,
			Cpa:                  req.Cpa,
			ConversionRate:       req.ConversionRate,
			Actives:              req.Actives,
			ActiveCost:           req.ActiveCost,
			ActiveRate:           req.ActiveRate,
			FirstPayCount:        req.FirstPayCount,
			FirstPayRate:         req.FirstPayRate,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdDesignerMaterialReport) PullData(ctx context.Context, req *ad.AdDesignerMaterialReportPullDataReq) (res *ad.AdDesignerMaterialReportPullDataRes, err error) {
	// 修改ctx 不超时
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	for {
		if req.StartTime > req.EndTime {
			break
		}
		err = s.Async(innerCtx, req.StartTime)
		if err != nil {
			g.Log().Error(ctx, err)
		}

		req.StartTime = libUtils.PlusDays(req.StartTime, 1)
	}
	return
}

// 统计列表数据
func (s *sAdDesignerMaterialReport) Async(ctx context.Context, stateDate string) (err error) {
	channelRechargeStatKey := model.AdDesignerMaterialReport + ":" + stateDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName : sDzAdOrderInfo TimedPull ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取设计师role id
		//roleList, err := sysService.SysRole().GetRoleList(ctx)
		//liberr.ErrIsNil(ctx, err)
		//roleIds := make([]uint, 0)
		//for _, role := range roleList {
		//	if role.Name == "设计师" || role.Name == "高级设计师" {
		//		roleIds = append(roleIds, role.Id)
		//	}
		//}
		//
		//// 获取所有设计师的用户列表
		//_, designerList, err := sysService.SysUser().List(ctx, &system.UserSearchReq{
		//	RoleIds: roleIds,
		//	PageReq: commonApi.PageReq{
		//		PageReq: comModel.PageReq{
		//			PageNum:  1,
		//			PageSize: 1000,
		//		},
		//	},
		//})
		//
		//for _, designer := range designerList {
		// 统计当前设计师
		//new_material_count INT DEFAULT 0 COMMENT '上新素材数',
		//	exposed_material_count INT DEFAULT 0 COMMENT '曝光大于0的素材数',
		//	new_material_cost DECIMAL(18,2) DEFAULT 0 COMMENT '上新素材消耗',
		//-- 当天上新素材情况
		//SELECT
		//m.user_id,
		//	COUNT(DISTINCT m.material_id) AS new_material_count,  -- 当天新上架的素材数量
		//COUNT(DISTINCT CASE WHEN d.show_cnt > 0 THEN m.material_id END) AS exposed_material_count,  -- 曝光数大于 0 的素材数
		//SUM(IFNULL(d.stat_cost, 0)) AS new_material_cost -- 当天上新的素材当日消耗之和
		//FROM ad_material m
		//LEFT JOIN ad_material_promotion mp ON m.material_id = mp.material_id
		//LEFT JOIN ad_promotion_metrics_data d
		//ON mp.promotion_id = d.promotion_id AND d.create_date = CURDATE()
		//WHERE DATE(m.created_at) = CURDATE()
		//GROUP BY m.user_id;
		start, end, _ := libUtils.GetDayStartEnd(stateDate)
		var req = make([]*model.AdDesignerMaterialReportAddReq, 0)

		var todayList []*model.AdDesignerMaterialReportListRes
		err = dao.AdMaterial.Ctx(ctx).As("m").
			LeftJoin(" ad_material_promotion as  mp", "m.material_id = mp.material_id").
			LeftJoin(" ad_promotion_metrics_data as  d", "mp.promotion_id = d.promotion_id AND d.create_date = "+"'"+stateDate+"'").
			LeftJoin(" sys_user as  u", "m.user_id = u.id").
			Where(" m.created_at >= ? AND m.created_at < ?", start, end).Group("m.user_id").
			Fields("m.user_id AS designerId", "u.user_name as designerName",
				"COUNT(DISTINCT m.material_id) AS newMaterialCount",
				"COUNT(DISTINCT CASE WHEN d.show_cnt > 0 THEN m.material_id END) AS exposedMaterialCount",
				"SUM(IFNULL(d.stat_cost, 0)) AS newMaterialCost").Scan(&todayList)
		// 记录错误日志
		if err != nil {
			g.Log().Error(ctx, err)
		}

		//-- 总体投放数据（素材只要关联推广即可，不限是否今天新建）
		//SELECT
		//m.user_id,
		//	SUM(d.stat_cost) AS total_cost,
		//	SUM(d.show_cnt) AS impressions, --  展示数
		//SUM(d.click_cnt) AS clicks,
		//	SUM(d.convert_cnt) AS conversions,
		//	SUM(d.active) AS actives,
		//	SUM(d.active_pay) AS first_pay_count
		//FROM ad_material m
		//JOIN ad_material_promotion mp ON m.material_id = mp.material_id
		//JOIN ad_promotion_metrics_data d ON mp.promotion_id = d.promotion_id
		//WHERE d.create_date = CURDATE()
		//GROUP BY m.user_id;
		//
		var list []*model.AdDesignerMaterialReportListRes
		err = dao.AdMaterial.Ctx(ctx).As("m").
			InnerJoin(" ad_material_promotion as  mp", "m.material_id = mp.material_id").
			InnerJoin(" ad_promotion_metrics_data as  d", "mp.promotion_id = d.promotion_id AND d.create_date = "+"'"+stateDate+"'").
			LeftJoin(" sys_user as  u", "m.user_id = u.id").
			Group("m.user_id").
			Fields("m.user_id as  designerId", "u.user_name as designerName",
				"SUM(d.stat_cost) AS totalCost",
				"SUM(d.show_cnt) AS impressions",
				"SUM(d.click_cnt) AS clicks",
				"SUM(d.convert_cnt) AS conversions",
				"SUM(d.active) AS actives",
				"SUM(d.active_pay) AS firstPayCount",
			).Scan(&list)
		if err != nil {
			g.Log().Error(ctx, err)
		}

		for _, item := range todayList {
			req = append(req, &model.AdDesignerMaterialReportAddReq{
				StatDate:             stateDate,
				DesignerId:           item.DesignerId,
				DesignerName:         item.DesignerName,
				NewMaterialCount:     item.NewMaterialCount,
				ExposedMaterialCount: item.ExposedMaterialCount,
				NewMaterialCost:      item.NewMaterialCost,
			})
		}

		for _, item := range list {
			var have = false
			for _, addReq := range req {
				if item.DesignerId == addReq.DesignerId {
					addReq.DesignerName = item.DesignerName
					addReq.TotalCost = item.TotalCost
					addReq.Impressions = item.Impressions
					addReq.Conversions = item.Conversions
					addReq.Clicks = item.Clicks
					addReq.Actives = item.Actives
					addReq.FirstPayCount = item.FirstPayCount
					//平均千次展现费用：计算公式为 消耗/展示数*1000
					addReq.AvgCpm = libUtils.DivideAndRound(item.TotalCost*1000, gconv.Float64(item.Impressions), 2, libUtils.RoundHalfEven)
					addReq.Ctr = libUtils.DivideAndRound(gconv.Float64(item.Clicks)*100, gconv.Float64(item.Impressions), 2, libUtils.RoundHalfEven)
					addReq.Cpa = libUtils.DivideAndRound(item.TotalCost, gconv.Float64(item.Conversions), 2, libUtils.RoundHalfEven)
					addReq.ConversionRate = libUtils.DivideAndRound(gconv.Float64(item.Conversions)*100, gconv.Float64(item.Clicks), 2, libUtils.RoundHalfEven)
					addReq.ActiveCost = libUtils.DivideAndRound(item.TotalCost, gconv.Float64(item.Actives), 2, libUtils.RoundHalfEven)
					addReq.ActiveRate = libUtils.DivideAndRound(gconv.Float64(item.Actives)*100, gconv.Float64(item.Clicks), 2, libUtils.RoundHalfEven)
					// 首次付费率：计算公式为 首次付费数/激活数*100%
					addReq.FirstPayRate = libUtils.DivideAndRound(gconv.Float64(item.FirstPayCount)*100, gconv.Float64(item.Actives), 2, libUtils.RoundHalfEven)
					have = true
					break
				}
			}
			if !have {

				req = append(req, &model.AdDesignerMaterialReportAddReq{
					StatDate:      stateDate,
					DesignerId:    item.DesignerId,
					DesignerName:  item.DesignerName,
					TotalCost:     item.TotalCost,
					Impressions:   item.Impressions,
					Conversions:   item.Conversions,
					Clicks:        item.Clicks,
					Actives:       item.Actives,
					FirstPayCount: item.FirstPayCount,
					//平均千次展现费用：计算公式为 消耗/展示数*1000
					AvgCpm: libUtils.DivideAndRound(item.TotalCost*1000, gconv.Float64(item.Impressions), 2, libUtils.RoundHalfEven),
					//点击数/展示数*100%
					Ctr: libUtils.DivideAndRound(gconv.Float64(item.Clicks)*100, gconv.Float64(item.Impressions), 2, libUtils.RoundHalfEven),
					// 转化成本：计算公式为 消耗/转化数
					Cpa: libUtils.DivideAndRound(item.TotalCost, gconv.Float64(item.Conversions), 2, libUtils.RoundHalfEven),
					// 为 转化数/点击数*100%
					ConversionRate: libUtils.DivideAndRound(gconv.Float64(item.Conversions)*100, gconv.Float64(item.Clicks), 2, libUtils.RoundHalfEven),
					// 激活成本：计算公式为 消耗/激活数，保留两位小数
					ActiveCost: libUtils.DivideAndRound(item.TotalCost, gconv.Float64(item.Actives), 2, libUtils.RoundHalfEven),
					//激活率：计算公式为 激活数/点击数*100%
					ActiveRate: libUtils.DivideAndRound(gconv.Float64(item.Actives)*100, gconv.Float64(item.Clicks), 2, libUtils.RoundHalfEven),
					// 首次付费率：计算公式为 首次付费数/激活数*100%
					FirstPayRate: libUtils.DivideAndRound(gconv.Float64(item.FirstPayCount)*100, gconv.Float64(item.Actives), 2, libUtils.RoundHalfEven),
				})
			}
		}

		err = s.BatchAdd(ctx, req)
		//记录错误日志
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("DzAdDesignerMaterialReportCallBackRes 添加失败: %v \n err:%v", req, err))
		}

	})
	return
}
