// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-11 13:50:44
// 生成路径: internal/app/ad/model/entity/ad_common_asset_package.go
// 生成人：cq
// desc:通用资产-标题包
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdCommonAssetPackage is the golang structure for table ad_common_asset_package.
type AdCommonAssetPackage struct {
	gmeta.Meta        `orm:"table:ad_common_asset_package, do:true"`
	Id                interface{} `orm:"id,primary" json:"id"`                           //
	PackageName       interface{} `orm:"package_name" json:"packageName"`                // 标题包名称
	TitleIds          interface{} `orm:"title_ids" json:"titleIds"`                      // 标题ID列表，以|分隔
	UserId            interface{} `orm:"user_id" json:"userId"`                          // 创建者
	Last3DayClickRate interface{} `orm:"last_3_day_click_rate" json:"last3DayClickRate"` // 近3日点击率
	Last3DayCost      interface{} `orm:"last_3_day_cost" json:"last3DayCost"`            // 近3日消耗
	HistoryClickRate  interface{} `orm:"history_click_rate" json:"historyClickRate"`     // 历史点击率
	HistoryCost       interface{} `orm:"history_cost" json:"historyCost"`                // 历史消耗
	AdCount           interface{} `orm:"ad_count" json:"adCount"`                        // 关联广告数
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
	DeletedAt         *gtime.Time `orm:"deleted_at" json:"deletedAt"`                    // 删除时间
}
