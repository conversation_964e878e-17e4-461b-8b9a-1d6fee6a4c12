// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: internal/app/ad/logic/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdOrderDetail(New())
}

func New() service.IKsAdOrderDetail {
	return &sKsAdOrderDetail{}
}

type sKsAdOrderDetail struct{}

func (s *sKsAdOrderDetail) List(ctx context.Context, req *model.KsAdOrderDetailSearchReq) (listRes *model.KsAdOrderDetailSearchRes, err error) {
	listRes = new(model.KsAdOrderDetailSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdOrderDetail.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if req.OrderId != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().OrderId+" = ?", req.OrderId)
		}
		if req.OrderType != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().OrderType+" = ?", req.OrderType)
		}
		if req.CopyrightUid != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().CopyrightUid+" = ?", req.CopyrightUid)
		}
		if req.CopyrightName != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().CopyrightName+" like ?", "%"+req.CopyrightName+"%")
		}
		if len(req.SeriesNameMap) > 0 {
			var condition string
			var index int
			for k, v := range req.SeriesNameMap {
				var seriesNames = ""
				for innerIndex, seriesName := range v {
					if innerIndex == 0 {
						seriesNames += fmt.Sprintf(`'%s'`, seriesName)
					} else {
						seriesNames += fmt.Sprintf(`,'%s'`, seriesName)
					}
				}
				conditionInner := fmt.Sprintf("(advertiser_id = %s and series_name in (%s))", k, seriesNames)
				if index == 0 {
					condition += conditionInner
				} else {
					condition += " OR " + conditionInner
				}
				index++
			}
			m = m.Where("(" + condition + ")")
		}
		if req.PayProvider != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().PayProvider+" = ?", req.PayProvider)
		}
		if req.RefundStatus == 1 {
			m = m.Where(dao.KsAdOrderDetail.Columns().RedundPrice+" = ?", 0)
		} else if req.RefundStatus == 2 {
			m = m.Where(dao.KsAdOrderDetail.Columns().RedundPrice+" > ?", 0)
		}
		if req.StartPayDate != "" && req.EndPayDate != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().PayDate+" >= ?", req.StartPayDate)
			m = m.Where(dao.KsAdOrderDetail.Columns().PayDate+" <= ?", req.EndPayDate)
		}
		if req.StartSettleDate != "" && req.EndSettleDate != "" {
			m = m.Where(dao.KsAdOrderDetail.Columns().SettleDate+" >= ?", req.StartSettleDate)
			m = m.Where(dao.KsAdOrderDetail.Columns().SettleDate+" <= ?", req.EndSettleDate)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "pay_date desc, settle_date desc, id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdOrderDetailListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 查询账户名称
		var advertiserIds = make([]int64, 0)
		for _, v := range res {
			advertiserIds = append(advertiserIds, v.AdvertiserId)
		}
		ksAdInfos, _ := service.KsAdInfo().GetByAdvertiserIds(ctx, advertiserIds)
		listRes.List = make([]*model.KsAdOrderDetailListRes, len(res))
		for k, v := range res {
			for _, adInfo := range ksAdInfos {
				if adInfo.AdvertiserId == v.AdvertiserId {
					v.AdvertiserName = adInfo.AdAccountName
					break
				}
			}
			listRes.List[k] = v
		}
		// 计算汇总数据
		var summary *model.KsAdOrderDetailListRes
		err = m.FieldSum("pay_amt", "payAmt").
			FieldSum("redund_price", "redundPrice").
			FieldSum("commission_price", "commissionPrice").
			FieldSum("settle_price", "settlePrice").
			FieldSum("settle_amt", "settleAmt").
			FieldSum("income", "income").
			FieldSum("expenditure", "expenditure").
			Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")
		listRes.Summary = summary
	})
	return
}

func (s *sKsAdOrderDetail) GetExportData(ctx context.Context, req *model.KsAdOrderDetailSearchReq) (listRes []*model.KsAdOrderDetailListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res, err1 := s.List(ctx, req)
		liberr.ErrIsNil(ctx, err1, "获取数据失败")
		listRes = res.List
	})
	return
}

func (s *sKsAdOrderDetail) GetById(ctx context.Context, id int64) (res *model.KsAdOrderDetailInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdOrderDetail.Ctx(ctx).WithAll().Where(dao.KsAdOrderDetail.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdOrderDetail) Add(ctx context.Context, req *model.KsAdOrderDetailAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdOrderDetail.Ctx(ctx).Insert(do.KsAdOrderDetail{
			AdvertiserId:    req.AdvertiserId,
			PayDate:         req.PayDate,
			SettleDate:      req.SettleDate,
			OrderId:         req.OrderId,
			OrderType:       req.OrderType,
			CopyrightUid:    req.CopyrightUid,
			CopyrightName:   req.CopyrightName,
			SeriesName:      req.SeriesName,
			SubAccountUid:   req.SubAccountUid,
			SubAccountName:  req.SubAccountName,
			PayProvider:     req.PayProvider,
			PayAmt:          req.PayAmt,
			RedundPrice:     req.RedundPrice,
			CommissionPrice: req.CommissionPrice,
			SettlePrice:     req.SettlePrice,
			SettleAmt:       req.SettleAmt,
			SalerRateStr:    req.SalerRateStr,
			Expenditure:     req.Expenditure,
			Income:          req.Income,
			SettleRate:      req.SettleRate,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdOrderDetail) BatchAdd(ctx context.Context, batchAddReq []*model.KsAdOrderDetailAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		list := make([]do.KsAdOrderDetail, 0)
		for _, req := range batchAddReq {
			list = append(list, do.KsAdOrderDetail{
				AdvertiserId:    req.AdvertiserId,
				PayDate:         req.PayDate,
				SettleDate:      req.SettleDate,
				OrderId:         req.OrderId,
				OrderType:       req.OrderType,
				CopyrightUid:    req.CopyrightUid,
				CopyrightName:   req.CopyrightName,
				SeriesName:      req.SeriesName,
				SubAccountUid:   req.SubAccountUid,
				SubAccountName:  req.SubAccountName,
				PayProvider:     req.PayProvider,
				PayAmt:          req.PayAmt,
				RedundPrice:     req.RedundPrice,
				CommissionPrice: req.CommissionPrice,
				SettlePrice:     req.SettlePrice,
				SettleAmt:       req.SettleAmt,
				SalerRateStr:    req.SalerRateStr,
				Expenditure:     req.Expenditure,
				Income:          req.Income,
				SettleRate:      req.SettleRate,
			})
		}
		_, err = dao.KsAdOrderDetail.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdOrderDetail) Edit(ctx context.Context, req *model.KsAdOrderDetailEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdOrderDetail.Ctx(ctx).WherePri(req.Id).Update(do.KsAdOrderDetail{
			AdvertiserId:    req.AdvertiserId,
			PayDate:         req.PayDate,
			SettleDate:      req.SettleDate,
			OrderId:         req.OrderId,
			OrderType:       req.OrderType,
			CopyrightUid:    req.CopyrightUid,
			CopyrightName:   req.CopyrightName,
			SeriesName:      req.SeriesName,
			SubAccountUid:   req.SubAccountUid,
			SubAccountName:  req.SubAccountName,
			PayProvider:     req.PayProvider,
			PayAmt:          req.PayAmt,
			RedundPrice:     req.RedundPrice,
			CommissionPrice: req.CommissionPrice,
			SettlePrice:     req.SettlePrice,
			SettleAmt:       req.SettleAmt,
			SalerRateStr:    req.SalerRateStr,
			Expenditure:     req.Expenditure,
			Income:          req.Income,
			SettleRate:      req.SettleRate,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdOrderDetail) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdOrderDetail.Ctx(ctx).Delete(dao.KsAdOrderDetail.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdOrderDetail) RunCalcAdOrderDetailStat(ctx context.Context, req *model.KsAdOrderDetailSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()
		startTime := req.StartSettleDate
		endTime := req.EndSettleDate
		for {
			if startTime > endTime {
				break
			}
			errors := s.CalcAdOrderDetailStat(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sKsAdOrderDetail) CalcAdOrderDetailStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatKsAdOrderDetailStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatKsAdOrderDetailStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -5).Format("Y-m-d")
		err = s.CalcAdOrderDetailStat(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "快手订单结算明细统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcAdOrderDetailStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "快手订单结算明细统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sKsAdOrderDetail) CalcAdOrderDetailStat(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询所有快手账号
		advertiserList, err1 := service.KsAdAccountInfo().GetAllAdvertiserIds(ctx)
		liberr.ErrIsNil(ctx, err1, "获取快手账号失败")
		for _, advertiser := range advertiserList {
			advertiserId := advertiser.AdvertiserId
			accessToken := api.GetAccessTokenByAppIdCache(advertiserId)
			if accessToken == "" {
				g.Log().Error(ctx, fmt.Sprintf("------------- 获取快手token失败, advertiserId: %v --------------------", advertiserId))
				continue
			}
			startTimestamps, endTimestamps, _ := libUtils.GetDayTimestamps(statDate)
			s.QueryAdOrderDetail(ctx, advertiserId, accessToken, startTimestamps, endTimestamps)
		}
	})
	return
}

func (s *sKsAdOrderDetail) QueryAdOrderDetail(ctx context.Context, advertiserId int64, accessToken string, payBeginDate int64, payEndDate int64) {
	var financeQueryTypes = []int64{2, 3, 4}
	for _, financeQueryType := range financeQueryTypes {
		var pageNum = 1
		var pageSize = 500
		for {
			queryOrderDetailRes, err1 := api.GetKSApiClient().QueryOrderDetailService.
				AccessToken(accessToken).
				SetReq(api.QueryOrderDetailReq{
					DateType:         1,
					AdvertiserID:     advertiserId,
					PageNum:          pageNum,
					PageSize:         pageSize,
					FinanceQueryType: financeQueryType,
					BeginDate:        payBeginDate,
					EndDate:          payEndDate,
				}).Do()
			if err1 != nil {
				g.Log().Errorf(ctx, "查询订单结算明细失败: %v", err1)
				break
			}
			if queryOrderDetailRes.Data == nil || len(queryOrderDetailRes.Data.CoreDataList) == 0 {
				break
			}
			batchAddReq := make([]*model.KsAdOrderDetailAddReq, 0)
			for _, item := range queryOrderDetailRes.Data.CoreDataList {
				batchAddReq = append(batchAddReq, &model.KsAdOrderDetailAddReq{
					AdvertiserId:    advertiserId,
					PayDate:         item.PayDate,
					SettleDate:      item.SettleDate,
					OrderId:         item.OrderID,
					OrderType:       item.OrderType,
					CopyrightUid:    item.CopyrightUID,
					CopyrightName:   item.CopyrightName,
					SeriesName:      item.SeriesName,
					SubAccountUid:   item.SubAccountUID,
					SubAccountName:  item.SubAccountName,
					PayProvider:     item.PayProvider,
					PayAmt:          item.PayAmt,
					RedundPrice:     item.RedundPrice,
					CommissionPrice: item.CommissionPrice,
					SettlePrice:     item.SettlePrice,
					SettleAmt:       item.SettleAmt,
					SalerRateStr:    item.SalerRateStr,
					Expenditure:     item.Expenditure,
					Income:          item.Income,
					SettleRate:      item.SettleRate,
				})
			}
			_ = s.BatchAdd(ctx, batchAddReq)
			if pageNum*pageSize >= queryOrderDetailRes.Data.TotalCount {
				break
			}
			pageNum++
		}
	}
}
