// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-27 10:06:43
// 生成路径: api/v1/order/order_info.go
// 生成人：gfast
// desc:订单主表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
)

// SDistributionStatisticsSearchReq 分页请求参数
type SDistributionStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"分销统计表信息" method:"get" summary:"分销统计表信息列表"`
	commonApi.Author
	model.SDistributionStatisticsSearchReq
}

// SDistributionStatisticsSearchRes 列表返回结果
type SDistributionStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDistributionStatisticsSearchRes
}

// SDistributionStatisticsExportReq 导出请求
type SDistributionStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"分销统计表信息" method:"get" summary:"分销统计信息导出"`
	commonApi.Author
	model.SDistributionStatisticsSearchReq
}

// SDistributionStatisticsExportRes 导出响应
type SDistributionStatisticsExportRes struct {
	commonApi.EmptyRes
}

// SDistributionStatisticsTaskReq 分销数据统计请求参数
type SDistributionStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"分销统计表信息" method:"post" summary:"分销数据统计"`
	commonApi.Author
	model.SDistributionStatisticsSearchReq
}

// SDistributionStatisticsTaskRes 列表返回结果
type SDistributionStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}

// SDistributionStatisticsSearchReq 分页请求参数
type SDistributionStatisticsGroupTimeSearchReq struct {
	g.Meta `path:"/GetGroupTimeStatics" tags:"首页" method:"post" summary:"充值信息，按时间统计"`
	commonApi.Author
	model.SDistributionStatisticsGroupTimeSearchReq
}
type SDistributionStatisticsGroupTimeRes struct {
	g.Meta `mime:"application/json"`
	*model.SDistributionStatisticsGroupTimeRes
}

// SDistributionStatisticsFqTaskReq 番茄分销数据统计请求参数
type SDistributionStatisticsFqTaskReq struct {
	g.Meta `path:"/fqTask" tags:"分销统计表信息" method:"post" summary:"番茄分销数据统计"`
	commonApi.Author
	model.SDistributionStatisticsSearchReq
}

// SDistributionStatisticsDzTaskReq 点众分销数据统计请求参数
type SDistributionStatisticsDzTaskReq struct {
	g.Meta `path:"/dzTask" tags:"分销统计表信息" method:"post" summary:"点众分销数据统计"`
	commonApi.Author
	model.SDistributionStatisticsSearchReq
}
