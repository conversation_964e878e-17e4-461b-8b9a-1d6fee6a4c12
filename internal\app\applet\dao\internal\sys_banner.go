// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-04-03 11:14:03
// 生成路径: internal/app/applet/dao/internal/sys_banner.go
// 生成人：cyao
// desc:新banner表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysBannerDao is the manager for logic model data accessing and custom defined data operations functions management.
type SysBannerDao struct {
	table   string           // Table is the underlying table name of the DAO.
	group   string           // Group is the database configuration group name of current DAO.
	columns SysBannerColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SysBannerColumns defines and stores column names for table sys_banner.
type SysBannerColumns struct {
	Id         string //
	ImgUrl     string // 轮播图
	AppId      string // 小程序Id
	LinkType   string // 跳转类型
	LinkUrl    string // 跳转Url
	ParentId   string // 剧Id
	Remark     string // 备注
	Sort       string // 排序
	Status     string // 状态
	UpdateTime string // 修改时间
	CreateTime string // 创建时间
}

var sysBannerColumns = SysBannerColumns{
	Id:         "id",
	ImgUrl:     "img_url",
	AppId:      "app_id",
	LinkType:   "link_type",
	LinkUrl:    "link_url",
	ParentId:   "parent_id",
	Remark:     "remark",
	Sort:       "sort",
	Status:     "status",
	UpdateTime: "update_time",
	CreateTime: "create_time",
}

// NewSysBannerDao creates and returns a new DAO object for table data access.
func NewSysBannerDao() *SysBannerDao {
	return &SysBannerDao{
		group:   "default",
		table:   "sys_banner",
		columns: sysBannerColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysBannerDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysBannerDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysBannerDao) Columns() SysBannerColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysBannerDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysBannerDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysBannerDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
