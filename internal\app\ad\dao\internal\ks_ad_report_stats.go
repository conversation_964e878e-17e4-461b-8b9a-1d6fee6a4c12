// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-07 14:26:18
// 生成路径: internal/app/ad/dao/internal/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdReportStatsD<PERSON> is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdReportStatsDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns KsAdReportStatsColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdReportStatsColumns defines and stores column names for table ks_ad_report_stats.
type KsAdReportStatsColumns struct {
	AdvertiserId                                  string // 账户ID
	Id                                            string //
	Likes                                         string // 点赞数
	Share                                         string // 分享数
	PhotoClick                                    string // 封面点击数
	Impression                                    string // 封面曝光数
	EventPay                                      string // 付费次数
	T0DirectPaiedCnt                              string // 付费次数(计费时间)
	EventPayPurchaseAmount                        string // 付费金额
	T0DirectPaiedAmt                              string // 付费金额(计费时间)
	AdShow                                        string // 广告曝光
	TotalCharge                                   string // 消耗
	EventAppInvoked                               string // 唤起应用数
	EventPayPurchaseAmountFirstDay                string // 激活当日付费金额
	EventPayPurchaseAmountOneDayByConversion      string // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountWeekByConversion        string // 激活后七日付费金额
	EventPayPurchaseAmountThreeDayByConversion    string // 激活后三日付费金额
	Conversion                                    string // 激活数
	T0DirectConversionCnt                         string // 激活数(计费时间)
	Negative                                      string // 减少此类作品数
	Report                                        string // 举报数
	Block                                         string // 拉黑数
	Comment                                       string // 评论数
	EventPayFirstDay                              string // 首日付费次数
	PlayedNum                                     string // 素材曝光数
	PlayedThreeSeconds                            string // 3s播放数
	AdPhotoPlayed75Percent                        string // 75%播放进度数
	PlayedEnd                                     string // 完播数
	Follow                                        string // 新增粉丝数
	EventNewUserPay                               string // 新增付费人数
	AdItemClick                                   string // 行为数
	T7PaiedCnt                                    string // 7日累计付费次数
	T7PaiedAmt                                    string // 7日累计付费金额
	ConversionNumByImpression7D                   string // 转化数(计费时间)
	DeepConversionNumByImpression7D               string // 深度转化数(计费时间)
	ConversionNum                                 string // 转化数(回传时间)
	DeepConversionNum                             string // 深度转化数
	T0PaiedCnt                                    string // 当日累计付费次数
	T0PaiedAmt                                    string // 当日累计付费金额
	Play3SRatio                                   string // 3s播放率
	AdPhotoPlayed75PercentRatio                   string // 75%进度播放率
	T7PaiedRoi                                    string // 7日累计ROI
	T0PaiedRoi                                    string // 当日累计ROI
	PhotoClickRatio                               string // 封面点击率
	EventPayCost                                  string // 付费次数成本
	EventPayRoi                                   string // 付费ROI
	EventAppInvokedCost                           string // 唤起应用成本
	EventAppInvokedRatio                          string // 唤起应用率
	ConversionCost                                string // 激活单价
	EventPayFirstDayRoi                           string // 激活当日ROI
	EventPayPurchaseAmountOneDayByConversionRoi   string // 激活后24h-ROI(激活时间)
	EventPayPurchaseAmountThreeDayByConversionRoi string // 激活后3日ROI
	EventPayPurchaseAmountWeekByConversionRoi     string // 激活后7日ROI
	PhotoClickCost                                string // 平均封面点击单价（元）
	Impression1KCost                              string // 平均千次封面曝光花费（元）
	Click1KCost                                   string // 平均千次素材曝光花费（元）
	ActionCost                                    string // 平均行为单价（元）
	DeepConversionCostByImpression7D              string // 深度转化成本(计费时间)，单位元
	DeepConversionRatioByImpression7D             string // 深度转化率(计费时间)
	EventPayFirstDayCost                          string // 首日付费次数成本，单位元
	ActionRatio                                   string // 素材点击率
	PlayEndRatio                                  string // 完播率
	EventNewUserPayCost                           string // 新增付费人数成本，单位元
	EventNewUserPayRatio                          string // 新增付费人数率
	ActionNewRatio                                string // 行为率
	ConversionCostByImpression7D                  string // 转化成本(计费时间)，单位元
	ConversionRatioByImpression7D                 string // 转化率(计费时间)
	Date                                          string // 日期，格式：yyyy-MM-dd HH:mm:ss
	KeyAction                                     string // 关键行为数
	AdPhotoPlayed75PercentRatio2                  string // 75%进度播放数
	AccountId                                     string // 账号ID
	SeriesId                                      string // 短剧ID
	RechargeRate                                  string // 充值几率
	MiniGameIaaRoi                                string // IAA广告变现ROI
	MiniGameIaaPurchaseAmount                     string // IAA广告变现LTV（元）
	CreateTime                                    string // 统计日期
}

var ksAdReportStatsColumns = KsAdReportStatsColumns{
	AdvertiserId:                             "advertiser_id",
	Id:                                       "id",
	Likes:                                    "likes",
	Share:                                    "share",
	PhotoClick:                               "photo_click",
	Impression:                               "impression",
	EventPay:                                 "event_pay",
	T0DirectPaiedCnt:                         "t0_direct_paied_cnt",
	EventPayPurchaseAmount:                   "event_pay_purchase_amount",
	T0DirectPaiedAmt:                         "t0_direct_paied_amt",
	AdShow:                                   "ad_show",
	TotalCharge:                              "total_charge",
	EventAppInvoked:                          "event_app_invoked",
	EventPayPurchaseAmountFirstDay:           "event_pay_purchase_amount_first_day",
	EventPayPurchaseAmountOneDayByConversion: "event_pay_purchase_amount_one_day_by_conversion",
	EventPayPurchaseAmountWeekByConversion:   "event_pay_purchase_amount_week_by_conversion",
	EventPayPurchaseAmountThreeDayByConversion: "event_pay_purchase_amount_three_day_by_conversion",
	Conversion:                      "conversion",
	T0DirectConversionCnt:           "t0_direct_conversion_cnt",
	Negative:                        "negative",
	Report:                          "report",
	Block:                           "block",
	Comment:                         "comment",
	EventPayFirstDay:                "event_pay_first_day",
	PlayedNum:                       "played_num",
	PlayedThreeSeconds:              "played_three_seconds",
	AdPhotoPlayed75Percent:          "ad_photo_played75percent",
	PlayedEnd:                       "played_end",
	Follow:                          "follow",
	EventNewUserPay:                 "event_new_user_pay",
	AdItemClick:                     "ad_item_click",
	T7PaiedCnt:                      "t7_paied_cnt",
	T7PaiedAmt:                      "t7_paied_amt",
	ConversionNumByImpression7D:     "conversion_num_by_impression7d",
	DeepConversionNumByImpression7D: "deep_conversion_num_by_impression7d",
	ConversionNum:                   "conversion_num",
	DeepConversionNum:               "deep_conversion_num",
	T0PaiedCnt:                      "t0_paied_cnt",
	T0PaiedAmt:                      "t0_paied_amt",
	Play3SRatio:                     "play3s_ratio",
	AdPhotoPlayed75PercentRatio:     "ad_photo_played_75percent_ratio",
	T7PaiedRoi:                      "t7_paied_roi",
	T0PaiedRoi:                      "t0_paied_roi",
	PhotoClickRatio:                 "photo_click_ratio",
	EventPayCost:                    "event_pay_cost",
	EventPayRoi:                     "event_pay_roi",
	EventAppInvokedCost:             "event_app_invoked_cost",
	EventAppInvokedRatio:            "event_app_invoked_ratio",
	ConversionCost:                  "conversion_cost",
	EventPayFirstDayRoi:             "event_pay_first_day_roi",
	EventPayPurchaseAmountOneDayByConversionRoi:   "event_pay_purchase_amount_one_day_by_conversion_roi",
	EventPayPurchaseAmountThreeDayByConversionRoi: "event_pay_purchase_amount_three_day_by_conversion_roi",
	EventPayPurchaseAmountWeekByConversionRoi:     "event_pay_purchase_amount_week_by_conversion_roi",
	PhotoClickCost:                    "photo_click_cost",
	Impression1KCost:                  "impression1k_cost",
	Click1KCost:                       "click1k_cost",
	ActionCost:                        "action_cost",
	DeepConversionCostByImpression7D:  "deep_conversion_cost_by_impression7d",
	DeepConversionRatioByImpression7D: "deep_conversion_ratio_by_impression7d",
	EventPayFirstDayCost:              "event_pay_first_day_cost",
	ActionRatio:                       "action_ratio",
	PlayEndRatio:                      "play_end_ratio",
	EventNewUserPayCost:               "event_new_user_pay_cost",
	EventNewUserPayRatio:              "event_new_user_pay_ratio",
	ActionNewRatio:                    "action_new_ratio",
	ConversionCostByImpression7D:      "conversion_cost_by_impression7d",
	ConversionRatioByImpression7D:     "conversion_ratio_by_impression7d",
	Date:                              "date",
	KeyAction:                         "key_action",
	AdPhotoPlayed75PercentRatio2:      "ad_photo_played75percent_ratio",
	AccountId:                         "account_id",
	SeriesId:                          "series_id",
	RechargeRate:                      "recharge_rate",
	MiniGameIaaRoi:                    "mini_game_iaa_roi",
	MiniGameIaaPurchaseAmount:         "mini_game_iaa_purchase_amount",
	CreateTime:                        "create_time",
}

// NewKsAdReportStatsDao creates and returns a new DAO object for table data access.
func NewKsAdReportStatsDao() *KsAdReportStatsDao {
	return &KsAdReportStatsDao{
		group:   "default",
		table:   "ks_ad_report_stats",
		columns: ksAdReportStatsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdReportStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdReportStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdReportStatsDao) Columns() KsAdReportStatsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdReportStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdReportStatsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdReportStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
