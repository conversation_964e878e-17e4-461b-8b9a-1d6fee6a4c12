// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-16 11:16:21
// 生成路径: internal/app/ad/model/fq_ad_account_users.go
// 生成人：gfast
// desc:番茄账号权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdAccountUsersInfoRes is the golang structure for table fq_ad_account_users.
type FqAdAccountUsersInfoRes struct {
	gmeta.Meta     `orm:"table:fq_ad_account_users"`
	DistributorId  int64   `orm:"distributor_id,primary" json:"distributorId" dc:"渠道ID"`  // 渠道ID
	DistributorIds []int64 `orm:"distributor_id,primary" json:"distributorId" dc:"渠道IDs"` // 渠道ID
	SpecifyUserId  int     `orm:"specify_user_id,primary" json:"specifyUserId" dc:"用户ID"` // 用户ID
}

type FqAdAccountUsersListRes struct {
	DistributorId int64 `json:"distributorId" dc:"渠道ID"`
	SpecifyUserId int   `json:"specifyUserId" dc:"用户ID"`
}

// FqAdAccountUsersSearchReq 分页请求参数
type FqAdAccountUsersSearchReq struct {
	comModel.PageReq
	DistributorId  string  `p:"distributorId" dc:"渠道ID"`  //渠道ID
	DistributorIds []int64 `p:"distributorIds" dc:"渠道ID"` //渠道ID
	SpecifyUserId  string  `p:"specifyUserId" dc:"用户ID"`  //用户ID
}

// FqAdAccountUsersSearchRes 列表返回结果
type FqAdAccountUsersSearchRes struct {
	comModel.ListRes
	List []*FqAdAccountUsersListRes `json:"list"`
}

// FqAdAccountUsersAddReq 添加操作请求参数
type FqAdAccountUsersAddReq struct {
	DistributorId int64 `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
}

// FqAdAccountUsersEditReq 修改操作请求参数
type FqAdAccountUsersEditReq struct {
	DistributorId int64 `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
}
