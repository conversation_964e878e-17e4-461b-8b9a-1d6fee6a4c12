// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-23 15:45:03
// 生成路径: api/v1/system/ad_setting.go
// 生成人：cyao
// desc:广告配置表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// AdSettingSearchReq 分页请求参数
type AdSettingSearchReq struct {
	g.Meta `path:"/list" tags:"广告配置表信息" method:"get" summary:"广告配置表信息列表"`
	commonApi.Author
	model.AdSettingSearchReq
}

// AdSettingSearchRes 列表返回结果
type AdSettingSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingSearchRes
}

// AdSettingExportReq 导出请求
type AdSettingExportReq struct {
	g.Meta `path:"/export" tags:"广告配置表信息" method:"get" summary:"广告配置表信息导出"`
	commonApi.Author
	model.AdSettingSearchReq
}

// AdSettingExportRes 导出响应
type AdSettingExportRes struct {
	commonApi.EmptyRes
}
type AdSettingExcelTemplateReq struct {
	g.Meta `path:"/excelTemplate" tags:"广告配置表信息" method:"get" summary:"导出模板文件"`
	commonApi.Author
}
type AdSettingExcelTemplateRes struct {
	commonApi.EmptyRes
}
type AdSettingImportReq struct {
	g.Meta `path:"/import" tags:"广告配置表信息" method:"post" summary:"广告配置表信息导入"`
	commonApi.Author
	File *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}
type AdSettingImportRes struct {
	commonApi.EmptyRes
}

// AdSettingAddReq 添加操作请求参数
type AdSettingAddReq struct {
	g.Meta `path:"/add" tags:"广告配置表信息" method:"post" summary:"广告配置表信息添加"`
	commonApi.Author
	*model.AdSettingEditReq
}

// AdSettingAddRes 添加操作返回结果
type AdSettingAddRes struct {
	commonApi.EmptyRes
}

// AdSettingEditReq 修改操作请求参数
type AdSettingEditReq struct {
	g.Meta `path:"/edit" tags:"广告配置表信息" method:"put" summary:"修改渠道导流链接配置"`
	commonApi.Author
	*model.AdSettingEditReq
}

// AdSettingEditRes 修改操作返回结果
type AdSettingEditRes struct {
	commonApi.EmptyRes
}

// AdSettingGetReq 获取一条数据请求
type AdSettingGetReq struct {
	g.Meta `path:"/get" tags:"广告配置表信息" method:"get" summary:"获取广告配置表信息信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdSettingGetRes 获取一条数据结果
type AdSettingGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingInfoRes
}

// AdSettingDeleteReq 删除数据请求
type AdSettingDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告配置表信息" method:"post" summary:"删除广告配置表信息"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdSettingDeleteRes 删除数据返回
type AdSettingDeleteRes struct {
	commonApi.EmptyRes
}

// AdSettingCommonInfoReq 获取广告公共配置信息请求
type AdSettingCommonInfoReq struct {
	g.Meta `path:"/common/info" tags:"广告配置表信息" method:"get" summary:"获取渠道广告配置公共信息"`
	commonApi.Author
	*model.AdSettingCommonInfoReq
}

// AdSettingCommonInfoRes 获取广告公共配置信息返回
type AdSettingCommonInfoRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingCommonInfoRes
}

// AdSettingMonitoringLinkReq 获取监测链接信息请求
type AdSettingMonitoringLinkReq struct {
	g.Meta `path:"/monitoring/link" tags:"广告配置表信息" method:"get" summary:"获取监测链接"`
	commonApi.Author
	*model.AdSettingMonitoringLinkReq
}

// AdSettingMonitoringLinkRes 获取监测链接信息返回
type AdSettingMonitoringLinkRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingMonitoringLinkRes
}

// AdSettingGetByChannelReq 根据渠道号获取一条数据请求
type AdSettingGetByChannelReq struct {
	g.Meta `path:"/get/channel" tags:"广告配置表信息" method:"get" summary:"获取渠道导流链接配置"`
	commonApi.Author
	SubChannel string `p:"subChannel" dc:"渠道号"` //通过渠道号获取
}

// AdSettingGetByChannelRes 根据渠道号获取一条数据结果
type AdSettingGetByChannelRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingListRes
}

// AdSettingLinkConfigReq 保存导流链接配置请求
type AdSettingLinkConfigReq struct {
	g.Meta `path:"/link/config/save" tags:"广告配置表信息" method:"post" summary:"保存导流链接配置"`
	commonApi.Author
	*model.AdSettingLinkConfigReq
}

// AdSettingLinkConfigRes 保存导流链接配置请求返回
type AdSettingLinkConfigRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingLinkConfigRes
}

// AdSettingLinkConfigGetReq 获取上次保存的导流链接配置请求
type AdSettingLinkConfigGetReq struct {
	g.Meta `path:"/link/config/get" tags:"广告配置表信息" method:"get" summary:"获取上次保存的导流链接配置"`
	commonApi.Author
	*model.AdSettingLinkConfigGetReq
}

// AdSettingLinkConfigGetRes 获取上次保存的导流链接配置请求返回
type AdSettingLinkConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingLinkConfigRes
}

// AdSettingEditAdvertiserIdsReq 修改广告账号id
type AdSettingEditAdvertiserIdsReq struct {
	g.Meta `path:"/editAdvertiserIds" tags:"小程序设置" method:"post" summary:"修改广告ids"`
	commonApi.Author
	*model.AdSettingEditAdverserIdsReq
}

// AdSettingLinkConfigExportZipReq 获取上次保存的导流链接配置请求
type AdSettingLinkConfigExportZipReq struct {
	g.Meta `path:"/link/config/exportZip" tags:"广告配置表信息" method:"post" summary:"导出上次保存的导流链接配置"`
	commonApi.Author
	*model.AdSettingLinkConfigExportZipReq
}
type BaiduAdDebugReq struct {
	g.Meta `path:"/baiduAdDebug" tags:"小程序设置" method:"post" summary:"百度联调"`
	commonApi.Author
	*model.BaiduAdDebugReq
}

// AdSettingDeleteAdvertiserIdReq 删除数据请求
type AdSettingDeleteAdvertiserIdReq struct {
	g.Meta `path:"/deleteAdvertiserId" tags:"广告配置表信息" method:"post" summary:"删除广告账号id"`
	commonApi.Author
	AdvertiserId string `p:"advertiserId" v:"required#广告id必须"`
	SubChannel   string `p:"subChannel" v:"required#渠道"` //通过主键删除
}
