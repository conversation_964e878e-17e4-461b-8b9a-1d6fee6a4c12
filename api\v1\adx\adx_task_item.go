// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: api/v1/adx/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxTaskItemSearchReq 分页请求参数
type AdxTaskItemSearchReq struct {
	g.Meta `path:"/list" tags:"ADX任务素材明细表" method:"get" summary:"ADX任务素材明细表列表"`
	commonApi.Author
	model.AdxTaskItemSearchReq
}

// AdxTaskItemSearchRes 列表返回结果
type AdxTaskItemSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxTaskItemSearchRes
}

// 下载完成or 失败标记
type AdxTaskItemDownloadStatusReq struct {
	g.Meta `path:"/downloadStatus" tags:"ADX任务素材明细表" method:"get" summary:"下载完成or 失败标记"`
	commonApi.Author
	model.AdxTaskItemDownloadStatusReq
}
type AdxTaskItemDownloadStatusRes struct {
	commonApi.EmptyRes
}

// AdxTaskItemExportReq 导出请求
type AdxTaskItemExportReq struct {
	g.Meta `path:"/export" tags:"ADX任务素材明细表" method:"get" summary:"ADX任务素材明细表导出"`
	commonApi.Author
	model.AdxTaskItemSearchReq
}

// AdxTaskItemExportRes 导出响应
type AdxTaskItemExportRes struct {
	commonApi.EmptyRes
}

// AdxTaskItemAddReq 添加操作请求参数
type AdxTaskItemAddReq struct {
	g.Meta `path:"/add" tags:"ADX任务素材明细表" method:"post" summary:"ADX任务素材明细表添加"`
	commonApi.Author
	*model.AdxTaskItemAddReq
}

// AdxTaskItemAddRes 添加操作返回结果
type AdxTaskItemAddRes struct {
	commonApi.EmptyRes
}

// AdxTaskItemEditReq 修改操作请求参数
type AdxTaskItemEditReq struct {
	g.Meta `path:"/edit" tags:"ADX任务素材明细表" method:"put" summary:"ADX任务素材明细表修改"`
	commonApi.Author
	*model.AdxTaskItemEditReq
}

// AdxTaskItemEditRes 修改操作返回结果
type AdxTaskItemEditRes struct {
	commonApi.EmptyRes
}

// AdxTaskItemGetReq 获取一条数据请求
type AdxTaskItemGetReq struct {
	g.Meta `path:"/get" tags:"ADX任务素材明细表" method:"get" summary:"获取ADX任务素材明细表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdxTaskItemGetRes 获取一条数据结果
type AdxTaskItemGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxTaskItemInfoRes
}

// AdxTaskItemDeleteReq 删除数据请求
type AdxTaskItemDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX任务素材明细表" method:"delete" summary:"删除ADX任务素材明细表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdxTaskItemDeleteRes 删除数据返回
type AdxTaskItemDeleteRes struct {
	commonApi.EmptyRes
}
