// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/service/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	"time"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxMaterial interface {
	List(ctx context.Context, req *model.AdxMaterialSearchReq) (res *model.AdxMaterialSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxMaterialSearchReq) (listRes []*model.AdxMaterialInfoRes, err error)
	GetMinByPlayletIds(ctx context.Context, playletIds []int64) (res *model.AdxMaterialInfoRes, err error)
	GetByMaterialId(ctx context.Context, MaterialId uint64) (res *model.AdxMaterialInfoRes, err error)
	GetByMaterialIds(ctx context.Context, MaterialIds []uint64) (res []*model.AdxMaterialInfoRes, err error)
	Add(ctx context.Context, req *model.AdxMaterialAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.AdxMaterialAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxMaterialEditReq) (err error)
	Delete(ctx context.Context, MaterialId []uint64) (err error)
	RunSyncAdxMaterialTask(ctx context.Context, req *model.AdxMaterialSearchReq) (err error)
	SyncAdxMaterialTask(ctx context.Context)
	SyncTodayAdxMaterialTask(ctx context.Context)
	ExecuteWithTimeout(ctx context.Context, lockName string, timeout time.Duration, taskName string, taskFuncName string, taskFunc func(context.Context) error) error
}

var localAdxMaterial IAdxMaterial

func AdxMaterial() IAdxMaterial {
	if localAdxMaterial == nil {
		panic("implement not found for interface IAdxMaterial, forgot register?")
	}
	return localAdxMaterial
}

func RegisterAdxMaterial(i IAdxMaterial) {
	localAdxMaterial = i
}
