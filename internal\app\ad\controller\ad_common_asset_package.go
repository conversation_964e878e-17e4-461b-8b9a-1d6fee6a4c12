// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-11 13:50:44
// 生成路径: internal/app/ad/controller/ad_common_asset_package.go
// 生成人：cq
// desc:通用资产-标题包
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adCommonAssetPackageController struct {
	systemController.BaseController
}

var AdCommonAssetPackage = new(adCommonAssetPackageController)

// List 列表
func (c *adCommonAssetPackageController) List(ctx context.Context, req *ad.AdCommonAssetPackageSearchReq) (res *ad.AdCommonAssetPackageSearchRes, err error) {
	res = new(ad.AdCommonAssetPackageSearchRes)
	res.AdCommonAssetPackageSearchRes, err = service.AdCommonAssetPackage().List(ctx, &req.AdCommonAssetPackageSearchReq)
	return
}

// Export 导出excel
func (c *adCommonAssetPackageController) Export(ctx context.Context, req *ad.AdCommonAssetPackageExportReq) (res *ad.AdCommonAssetPackageExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdCommonAssetPackageListRes
		//表头
		tableHead = []interface{}{"标题包名称", "标题数量", "创建者", "近3日点击率", "近3日消耗", "历史点击率", "历史消耗", "关联广告数"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.AdCommonAssetPackage().GetExportData(ctx, &req.AdCommonAssetPackageSearchReq)
		if err != nil {
			return
		}
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.PackageName,
				v.TitleCount,
				v.UserName,
				v.Last3DayClickRate,
				v.Last3DayCost,
				v.HistoryClickRate,
				v.HistoryCost,
				v.AdCount,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("标题包")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取通用资产-标题包
func (c *adCommonAssetPackageController) Get(ctx context.Context, req *ad.AdCommonAssetPackageGetReq) (res *ad.AdCommonAssetPackageGetRes, err error) {
	res = new(ad.AdCommonAssetPackageGetRes)
	res.AdCommonAssetPackageInfoRes, err = service.AdCommonAssetPackage().GetById(ctx, req.Id)
	return
}

// Add 添加通用资产-标题包
func (c *adCommonAssetPackageController) Add(ctx context.Context, req *ad.AdCommonAssetPackageAddReq) (res *ad.AdCommonAssetPackageAddRes, err error) {
	err = service.AdCommonAssetPackage().Add(ctx, req.AdCommonAssetPackageAddReq)
	return
}

// Edit 修改通用资产-标题包
func (c *adCommonAssetPackageController) Edit(ctx context.Context, req *ad.AdCommonAssetPackageEditReq) (res *ad.AdCommonAssetPackageEditRes, err error) {
	err = service.AdCommonAssetPackage().Edit(ctx, req.AdCommonAssetPackageEditReq)
	return
}

// Delete 删除通用资产-标题包
func (c *adCommonAssetPackageController) Delete(ctx context.Context, req *ad.AdCommonAssetPackageDeleteReq) (res *ad.AdCommonAssetPackageDeleteRes, err error) {
	err = service.AdCommonAssetPackage().Delete(ctx, req.Ids)
	return
}
