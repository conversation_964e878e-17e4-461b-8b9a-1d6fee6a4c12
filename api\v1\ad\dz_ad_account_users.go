// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:33
// 生成路径: api/v1/ad/dz_ad_account_users.go
// 生成人：cyao
// desc:权限用户关联相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdAccountUsersSearchReq 分页请求参数
type DzAdAccountUsersSearchReq struct {
	g.Meta `path:"/list" tags:"权限用户关联" method:"get" summary:"权限用户关联列表"`
	commonApi.Author
	model.DzAdAccountUsersSearchReq
}

// DzAdAccountUsersSearchRes 列表返回结果
type DzAdAccountUsersSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountUsersSearchRes
}

// DzAdAccountUsersAddReq 添加操作请求参数
type DzAdAccountUsersAddReq struct {
	g.Meta `path:"/add" tags:"权限用户关联" method:"post" summary:"权限用户关联添加"`
	commonApi.Author
	*model.DzAdAccountUsersAddReq
}

// DzAdAccountUsersAddRes 添加操作返回结果
type DzAdAccountUsersAddRes struct {
	commonApi.EmptyRes
}

// DzAdAccountUsersEditReq 修改操作请求参数
type DzAdAccountUsersEditReq struct {
	g.Meta `path:"/edit" tags:"权限用户关联" method:"put" summary:"权限用户关联修改"`
	commonApi.Author
	*model.DzAdAccountUsersEditReq
}

// DzAdAccountUsersEditRes 修改操作返回结果
type DzAdAccountUsersEditRes struct {
	commonApi.EmptyRes
}

// DzAdAccountUsersGetReq 获取一条数据请求
type DzAdAccountUsersGetReq struct {
	g.Meta `path:"/get" tags:"权限用户关联" method:"get" summary:"获取权限用户关联信息"`
	commonApi.Author
	ChannelId int64 `p:"channelId" v:"required#主键必须"` //通过主键获取
}

// DzAdAccountUsersGetRes 获取一条数据结果
type DzAdAccountUsersGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountUsersInfoRes
}

// DzAdAccountUsersDeleteReq 删除数据请求
type DzAdAccountUsersDeleteReq struct {
	g.Meta `path:"/delete" tags:"权限用户关联" method:"delete" summary:"删除权限用户关联"`
	commonApi.Author
	ChannelIds []int64 `p:"channelIds" v:"required#主键必须"` //通过主键删除
}

// DzAdAccountUsersDeleteRes 删除数据返回
type DzAdAccountUsersDeleteRes struct {
	commonApi.EmptyRes
}
