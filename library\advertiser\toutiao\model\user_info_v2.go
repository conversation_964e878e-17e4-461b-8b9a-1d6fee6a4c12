/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

// UserInfoV2Response struct for UserInfoV2Response
type UserInfoV2Response struct {
	//
	Code *int64                  `json:"code,omitempty"`
	Data *UserInfoV2ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// UserInfoV2ResponseData
type UserInfoV2ResponseData struct {
	// 授权的应用id
	AppId *int64 `json:"app_id,omitempty"`
	//
	DisplayName *string `json:"display_name,omitempty"`
	// 邮箱（已经脱敏处理）
	Email *string `json:"email,omitempty"`
	// 用户id
	Id *int64 `json:"id,omitempty"`
	// 是否敏感物料授权, true 已敏感物料授权, false 未敏感物料授权
	MaterialAuthStatus *bool `json:"material_auth_status,omitempty"`
	// 当前token可操作的api接口列表
	TokenApiList []string `json:"token_api_list,omitempty"`
	// 权限点list
	TokenScopeList []int64 `json:"token_scope_list,omitempty"`
}
