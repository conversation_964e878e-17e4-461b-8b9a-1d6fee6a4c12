// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-08 18:20:26
// 生成路径: api/v1/applet/wx_product_config.go
// 生成人：lx
// desc:产品配置（充值）相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// WxProductConfigSearchReq 分页请求参数
type WxProductConfigSearchReq struct {
	g.Meta `path:"/list" tags:"微信虚拟道具管理" method:"get" summary:"获取虚拟道具列表"`
	commonApi.Author
	model.WxProductConfigSearchReq
}

// WxProductConfigSearchRes 列表返回结果
type WxProductConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.WxProductConfigSearchRes
}

// WxProductConfigAddReq 添加操作请求参数
type WxProductConfigAddReq struct {
	g.Meta `path:"/add" tags:"微信虚拟道具管理" method:"post" summary:"新增虚拟道具"`
	commonApi.Author
	*model.WxProductConfigAddReq
}

// WxProductConfigAddRes 添加操作返回结果
type WxProductConfigAddRes struct {
	commonApi.EmptyRes
}

// WxProductConfigPublishReq 发布虚拟道具请求参数
type WxProductConfigPublishReq struct {
	g.Meta `path:"/publish" tags:"微信虚拟道具管理" method:"post" summary:"发布虚拟道具"`
	commonApi.Author
	Ids []int `p:"ids" dc:"道具id列表"`
}

// WxProductConfigPublishRes 发布虚拟道具返回结果
type WxProductConfigPublishRes struct {
	commonApi.EmptyRes
}

// WxProductConfigSetStatusReq 启用禁用道具请求参数
type WxProductConfigSetStatusReq struct {
	g.Meta `path:"/set/status" tags:"微信虚拟道具管理" method:"post" summary:"启用禁用道具"`
	commonApi.Author
	Ids    []int `p:"ids" dc:"道具id列表"`
	Status int   `p:"status" dc:"启用禁用  1：启用 0：禁用"`
}

// WxProductConfigSetStatusRes 启用禁用道具返回结果
type WxProductConfigSetStatusRes struct {
	commonApi.EmptyRes
}

// WxProductConfigEditReq 修改操作请求参数
type WxProductConfigEditReq struct {
	g.Meta `path:"/edit" tags:"产品配置（充值）" method:"put" summary:"产品配置（充值）修改"`
	commonApi.Author
	*model.WxProductConfigEditReq
}

// WxProductConfigEditRes 修改操作返回结果
type WxProductConfigEditRes struct {
	commonApi.EmptyRes
}

// WxProductConfigGetReq 获取一条数据请求
type WxProductConfigGetReq struct {
	g.Meta `path:"/get" tags:"产品配置（充值）" method:"get" summary:"获取产品配置（充值）信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// WxProductConfigGetRes 获取一条数据结果
type WxProductConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.WxProductConfigInfoRes
}

// WxProductConfigDeleteReq 删除数据请求
type WxProductConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"产品配置（充值）" method:"post" summary:"删除产品配置（充值）"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// WxProductConfigDeleteRes 删除数据返回
type WxProductConfigDeleteRes struct {
	commonApi.EmptyRes
}
type WxProductConfigGetPriceReq struct {
	g.Meta `path:"/getPriceList" tags:"小程序设置" method:"post" summary:"查询价格列表"`
	commonApi.Author
}
