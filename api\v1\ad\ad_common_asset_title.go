// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 13:50:11
// 生成路径: api/v1/ad/ad_common_asset_title.go
// 生成人：cq
// desc:通用资产-标题库相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// getRandomTitles
type AdCommonAssetTitleGetRandomTitlesReq struct {
	g.Meta `path:"/getRandomTitles" tags:"通用资产-标题库" method:"get" summary:"获取随机标题"`
	commonApi.Author
	model.AdCommonAssetTitleGetRandomTitlesReq
}

type AdCommonAssetTitleGetRandomTitlesRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetTitleSearchRes
}

// AdCommonAssetTitleSearchReq 分页请求参数
type AdCommonAssetTitleSearchReq struct {
	g.Meta `path:"/list" tags:"通用资产-标题库" method:"post" summary:"通用资产-标题库列表"`
	commonApi.Author
	model.AdCommonAssetTitleSearchReq
}

// AdCommonAssetTitleSearchRes 列表返回结果
type AdCommonAssetTitleSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetTitleSearchRes
}

// AdCommonAssetTitleExportReq 导出请求
type AdCommonAssetTitleExportReq struct {
	g.Meta `path:"/export" tags:"通用资产-标题库" method:"post" summary:"通用资产-标题库导出"`
	commonApi.Author
	model.AdCommonAssetTitleSearchReq
}

// AdCommonAssetTitleExportRes 导出响应
type AdCommonAssetTitleExportRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetTitleAddReq 添加操作请求参数
type AdCommonAssetTitleAddReq struct {
	g.Meta `path:"/add" tags:"通用资产-标题库" method:"post" summary:"通用资产-标题库添加"`
	commonApi.Author
	*model.AdCommonAssetTitleAddReq
}

// AdCommonAssetTitleAddRes 添加操作返回结果
type AdCommonAssetTitleAddRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetTitleEditReq 修改操作请求参数
type AdCommonAssetTitleEditReq struct {
	g.Meta `path:"/edit" tags:"通用资产-标题库" method:"put" summary:"通用资产-标题库修改"`
	commonApi.Author
	*model.AdCommonAssetTitleEditReq
}

// AdCommonAssetTitleEditRes 修改操作返回结果
type AdCommonAssetTitleEditRes struct {
	commonApi.EmptyRes
}

// AdCommonAssetTitleGetReq 获取一条数据请求
type AdCommonAssetTitleGetReq struct {
	g.Meta `path:"/get" tags:"通用资产-标题库" method:"get" summary:"获取通用资产-标题库信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdCommonAssetTitleGetRes 获取一条数据结果
type AdCommonAssetTitleGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdCommonAssetTitleInfoRes
}

// AdCommonAssetTitleDeleteReq 删除数据请求
type AdCommonAssetTitleDeleteReq struct {
	g.Meta `path:"/delete" tags:"通用资产-标题库" method:"post" summary:"删除通用资产-标题库"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdCommonAssetTitleDeleteRes 删除数据返回
type AdCommonAssetTitleDeleteRes struct {
	commonApi.EmptyRes
}
