// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: api/v1/ad/ad_anchor_point.go
// 生成人：cyao
// desc:锚点表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdAnchorPointSearchReq 分页请求参数
type AdAnchorPointSearchReq struct {
	g.Meta `path:"/list" tags:"锚点表" method:"get" summary:"锚点表列表"`
	commonApi.Author
	model.AdAnchorPointSearchReq
}

// AdAnchorPointSearchRes 列表返回结果
type AdAnchorPointSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAnchorPointSearchRes
}

// AdAnchorPointAddReq 添加操作请求参数
type AdAnchorPointAddReq struct {
	g.Meta `path:"/add" tags:"锚点表" method:"post" summary:"锚点表添加"`
	commonApi.Author
	*model.AdAnchorPointAddReq
}

// AdAnchorPointAddRes 添加操作返回结果
type AdAnchorPointAddRes struct {
	commonApi.EmptyRes
}

// AdAnchorPointEditReq 修改操作请求参数
type AdAnchorPointEditReq struct {
	g.Meta `path:"/edit" tags:"锚点表" method:"put" summary:"锚点表修改"`
	commonApi.Author
	*model.AdAnchorPointEditReq
}

// AdAnchorPointEditRes 修改操作返回结果
type AdAnchorPointEditRes struct {
	commonApi.EmptyRes
}

// AdAnchorPointGetReq 获取一条数据请求
type AdAnchorPointGetReq struct {
	g.Meta `path:"/get" tags:"锚点表" method:"get" summary:"获取锚点表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAnchorPointGetRes 获取一条数据结果
type AdAnchorPointGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAnchorPointInfoRes
}

// AdAnchorPointDeleteReq 删除数据请求
type AdAnchorPointDeleteReq struct {
	g.Meta `path:"/delete" tags:"锚点表" method:"delete" summary:"删除锚点表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAnchorPointDeleteRes 删除数据返回
type AdAnchorPointDeleteRes struct {
	commonApi.EmptyRes
}
