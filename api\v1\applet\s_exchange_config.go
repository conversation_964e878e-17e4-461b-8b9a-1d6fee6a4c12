// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-16 17:18:05
// 生成路径: api/v1/applet/s_exchange_config.go
// 生成人：cq
// desc:兑换配置表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SExchangeConfigSearchReq 分页请求参数
type SExchangeConfigSearchReq struct {
	g.Meta `path:"/list" tags:"兑换配置表" method:"post" summary:"兑换配置表列表"`
	commonApi.Author
	model.SExchangeConfigSearchReq
}

// SExchangeConfigSearchRes 列表返回结果
type SExchangeConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SExchangeConfigSearchRes
}

// SExchangeConfigAddReq 添加操作请求参数
type SExchangeConfigAddReq struct {
	g.Meta `path:"/add" tags:"兑换配置表" method:"post" summary:"兑换配置表添加"`
	commonApi.Author
	*model.SExchangeConfigAddReq
}

// SExchangeConfigAddRes 添加操作返回结果
type SExchangeConfigAddRes struct {
	commonApi.EmptyRes
}

// SExchangeConfigEditReq 修改操作请求参数
type SExchangeConfigEditReq struct {
	g.Meta `path:"/edit" tags:"兑换配置表" method:"put" summary:"兑换配置表修改"`
	commonApi.Author
	*model.SExchangeConfigEditReq
}

// SExchangeConfigEditRes 修改操作返回结果
type SExchangeConfigEditRes struct {
	commonApi.EmptyRes
}

// SExchangeConfigGetReq 获取一条数据请求
type SExchangeConfigGetReq struct {
	g.Meta `path:"/get" tags:"兑换配置表" method:"get" summary:"获取兑换配置表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SExchangeConfigGetRes 获取一条数据结果
type SExchangeConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SExchangeConfigInfoRes
}

// SExchangeConfigDeleteReq 删除数据请求
type SExchangeConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"兑换配置表" method:"post" summary:"删除兑换配置表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SExchangeConfigDeleteRes 删除数据返回
type SExchangeConfigDeleteRes struct {
	commonApi.EmptyRes
}
