/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameListV30DataListMidPaymentTierRange
type ToolsWechatGameListV30DataListMidPaymentTierRange string

// List of tools_wechat_game_list_v3.0_data_list_mid_payment_tier_range
const (
	ABOVE_500_ToolsWechatGameListV30DataListMidPaymentTierRange       ToolsWechatGameListV30DataListMidPaymentTierRange = "ABOVE_500"
	BELOW_100_ToolsWechatGameListV30DataListMidPaymentTierRange       ToolsWechatGameListV30DataListMidPaymentTierRange = "BELOW_100"
	FROM_100_TO_500_ToolsWechatGameListV30DataListMidPaymentTierRange ToolsWechatGameListV30DataListMidPaymentTierRange = "FROM_100_TO_500"
)

// Ptr returns reference to tools_wechat_game_list_v3.0_data_list_mid_payment_tier_range value
func (v ToolsWechatGameListV30DataListMidPaymentTierRange) Ptr() *ToolsWechatGameListV30DataListMidPaymentTierRange {
	return &v
}
