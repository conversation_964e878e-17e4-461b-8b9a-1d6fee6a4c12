// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-31 15:46:24
// 生成路径: api/v1/oceanengine/ad_advertiser_account_subscribe_record.go
// 生成人：cq
// desc:巨量广告主报表订阅记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAdvertiserAccountSubscribeRecordSearchReq 分页请求参数
type AdAdvertiserAccountSubscribeRecordSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告主报表订阅记录" method:"get" summary:"巨量广告主报表订阅记录列表"`
	commonApi.Author
	model.AdAdvertiserAccountSubscribeRecordSearchReq
}

// AdAdvertiserAccountSubscribeRecordSearchRes 列表返回结果
type AdAdvertiserAccountSubscribeRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountSubscribeRecordSearchRes
}

// AdAdvertiserAccountSubscribeRecordAddReq 添加操作请求参数
type AdAdvertiserAccountSubscribeRecordAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告主报表订阅记录" method:"post" summary:"巨量广告主报表订阅记录添加"`
	commonApi.Author
	*model.AdAdvertiserAccountSubscribeRecordAddReq
}

// AdAdvertiserAccountSubscribeRecordAddRes 添加操作返回结果
type AdAdvertiserAccountSubscribeRecordAddRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountSubscribeRecordEditReq 修改操作请求参数
type AdAdvertiserAccountSubscribeRecordEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告主报表订阅记录" method:"put" summary:"巨量广告主报表订阅记录修改"`
	commonApi.Author
	*model.AdAdvertiserAccountSubscribeRecordEditReq
}

// AdAdvertiserAccountSubscribeRecordEditRes 修改操作返回结果
type AdAdvertiserAccountSubscribeRecordEditRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountSubscribeRecordGetReq 获取一条数据请求
type AdAdvertiserAccountSubscribeRecordGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告主报表订阅记录" method:"get" summary:"获取巨量广告主报表订阅记录信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAdvertiserAccountSubscribeRecordGetRes 获取一条数据结果
type AdAdvertiserAccountSubscribeRecordGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountSubscribeRecordInfoRes
}

// AdAdvertiserAccountSubscribeRecordDeleteReq 删除数据请求
type AdAdvertiserAccountSubscribeRecordDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告主报表订阅记录" method:"delete" summary:"删除巨量广告主报表订阅记录"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAdvertiserAccountSubscribeRecordDeleteRes 删除数据返回
type AdAdvertiserAccountSubscribeRecordDeleteRes struct {
	commonApi.EmptyRes
}
