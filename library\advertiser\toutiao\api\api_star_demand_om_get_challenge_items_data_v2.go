/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// StarDemandOmGetChallengeItemsDataV2ApiService 获取任务结算数据
type StarDemandOmGetChallengeItemsDataV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest
}

func (r *StarDemandOmGetChallengeItemsDataV2ApiService) SetCfg(cfg *conf.Configuration) *StarDemandOmGetChallengeItemsDataV2ApiService {
	r.cfg = cfg
	return r
}

func (r *StarDemandOmGetChallengeItemsDataV2ApiService) SetRequest(accountFundGetV3Request ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest) *StarDemandOmGetChallengeItemsDataV2ApiService {
	r.Request = &accountFundGetV3Request
	return r
}

func (r *StarDemandOmGetChallengeItemsDataV2ApiService) AccessToken(accessToken string) *StarDemandOmGetChallengeItemsDataV2ApiService {
	r.token = accessToken
	return r
}

type ApiOpenApi2StarDemandOmGetChallengeItemsDataGetRequest struct {
	//ctx             context.Context
	//ApiService      *StarDemandOmGetChallengeItemsDataV2ApiService
	StarId          int64
	ChallengeTaskId int64
	Page            int32
	Limit           int32
	DeveloperId     int64
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *StarDemandOmGetChallengeItemsDataV2ApiService) Do() (data *models.StarDemandOmGetChallengeItemsDataV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/star/demand/om_get_challenge_items_data/"
	localVarQueryParams := map[string]string{}

	if r.Request.StarId == 0 {
		return nil, errors.New("starId is required and must be specified")
	}
	if r.Request.ChallengeTaskId == 0 {
		return nil, errors.New("challengeTaskId is required and must be specified")
	}
	if r.Request.Page == 0 {
		return nil, errors.New("page is required and must be specified")
	}
	if r.Request.Limit == 0 {
		return nil, errors.New("limit is required and must be specified")
	}
	if r.Request.Limit < 0 {
		return nil, errors.New("limit must be greater than 0")
	}
	if r.Request.Limit > 100 {
		return nil, errors.New("limit must be less than 100")
	}

	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "star_id", r.Request.StarId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "challenge_task_id", r.Request.ChallengeTaskId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "limit", r.Request.Limit)
	if r.Request.DeveloperId > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "developer_id", r.Request.DeveloperId)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.StarDemandOmGetChallengeItemsDataV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.StarDemandOmGetChallengeItemsDataV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/star/demand/om_get_challenge_items_data/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
