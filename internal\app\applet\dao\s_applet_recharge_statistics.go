// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-08-02 18:25:26
// 生成路径: internal/app/applet/dao/s_applet_rechare_statistics.go
// 生成人：len
// desc:小程序充值统计
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sAppletRechareStatisticsDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sAppletRechargeStatisticsDao struct {
	*internal.SAppletRechargeStatisticsDao
}

var (
	// SAppletRechargeStatistics is globally public accessible object for table tools_gen_table operations.
	SAppletRechargeStatistics = sAppletRechargeStatisticsDao{
		internal.NewSAppletRechargeStatisticsDao(),
	}
)

// Fill with you ideas below.
