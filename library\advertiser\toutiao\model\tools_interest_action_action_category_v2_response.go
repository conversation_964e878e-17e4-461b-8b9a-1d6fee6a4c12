/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

// ToolsInterestActionActionCategoryV2Response struct for ToolsInterestActionActionCategoryV2Response
type ToolsInterestActionActionCategoryV2Response struct {
	//
	Code *int64                                             `json:"code,omitempty"`
	Data []*ToolsInterestActionActionCategoryV2ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

type ToolsInterestActionActionCategoryV2ResponseData struct {
	Id       *string                                            `json:"id"`
	Name     *string                                            `json:"name"`
	Num      *string                                            `json:"num"`
	Children []*ToolsInterestActionActionCategoryV2ResponseData `json:"children"`
}
