// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: api/v1/ad/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdOrderDetailSearchReq 分页请求参数
type KsAdOrderDetailSearchReq struct {
	g.Meta `path:"/list" tags:"快手订单结算明细" method:"post" summary:"快手订单结算明细列表"`
	commonApi.Author
	model.KsAdOrderDetailSearchReq
}

// KsAdOrderDetailSearchRes 列表返回结果
type KsAdOrderDetailSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdOrderDetailSearchRes
}

// KsAdOrderDetailExportReq 导出请求
type KsAdOrderDetailExportReq struct {
	g.Meta `path:"/export" tags:"快手订单结算明细" method:"post" summary:"快手订单结算明细导出"`
	commonApi.Author
	model.KsAdOrderDetailSearchReq
}

// KsAdOrderDetailExportRes 导出响应
type KsAdOrderDetailExportRes struct {
	commonApi.EmptyRes
}

// KsAdOrderDetailAddReq 添加操作请求参数
type KsAdOrderDetailAddReq struct {
	g.Meta `path:"/add" tags:"快手订单结算明细" method:"post" summary:"快手订单结算明细添加"`
	commonApi.Author
	*model.KsAdOrderDetailAddReq
}

// KsAdOrderDetailAddRes 添加操作返回结果
type KsAdOrderDetailAddRes struct {
	commonApi.EmptyRes
}

// KsAdOrderDetailEditReq 修改操作请求参数
type KsAdOrderDetailEditReq struct {
	g.Meta `path:"/edit" tags:"快手订单结算明细" method:"put" summary:"快手订单结算明细修改"`
	commonApi.Author
	*model.KsAdOrderDetailEditReq
}

// KsAdOrderDetailEditRes 修改操作返回结果
type KsAdOrderDetailEditRes struct {
	commonApi.EmptyRes
}

// KsAdOrderDetailGetReq 获取一条数据请求
type KsAdOrderDetailGetReq struct {
	g.Meta `path:"/get" tags:"快手订单结算明细" method:"get" summary:"获取快手订单结算明细信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdOrderDetailGetRes 获取一条数据结果
type KsAdOrderDetailGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdOrderDetailInfoRes
}

// KsAdOrderDetailDeleteReq 删除数据请求
type KsAdOrderDetailDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手订单结算明细" method:"post" summary:"删除快手订单结算明细"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdOrderDetailDeleteRes 删除数据返回
type KsAdOrderDetailDeleteRes struct {
	commonApi.EmptyRes
}

// KsAdOrderDetailTaskReq 分页请求参数
type KsAdOrderDetailTaskReq struct {
	g.Meta `path:"/task" tags:"快手订单结算明细" method:"post" summary:"快手订单结算明细任务"`
	commonApi.Author
	model.KsAdOrderDetailSearchReq
}

// KsAdOrderDetailTaskRes 列表返回结果
type KsAdOrderDetailTaskRes struct {
	g.Meta `mime:"application/json"`
}
