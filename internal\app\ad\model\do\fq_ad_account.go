// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/model/entity/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdAccount is the golang structure for table fq_ad_account.
type FqAdAccount struct {
	gmeta.Meta    `orm:"table:fq_ad_account, do:true"`
	DistributorId interface{} `orm:"distributor_id,primary" json:"distributorId"` // 渠道ID
	SecretKey     interface{} `orm:"secret_key" json:"secretKey"`                 // 秘钥
	AccountName   interface{} `orm:"account_name" json:"accountName"`             // 账号名
	DramaType     interface{} `orm:"drama_type" json:"dramaType"`                 // 抖小IAP	抖小IAA	微小IAP	微小IAA
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`                 // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt"`                 // 更新时间
	DeletedAt     *gtime.Time `orm:"deleted_at" json:"deletedAt"`                 // 删除时间
}
