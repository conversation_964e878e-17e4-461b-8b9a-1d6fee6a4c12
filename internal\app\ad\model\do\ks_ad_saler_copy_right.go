// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: internal/app/ad/model/entity/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdSalerCopyRight is the golang structure for table ks_ad_saler_copy_right.
type KsAdSalerCopyRight struct {
	gmeta.Meta                                `orm:"table:ks_ad_saler_copy_right, do:true"`
	Id                                        interface{} `orm:"id,primary" json:"id"`                                                                             // id
	AdvertiserId                              interface{} `orm:"advertiser_id" json:"advertiserId"`                                                                // 用户快手号id
	ReportDate                                interface{} `orm:"report_date" json:"reportDate"`                                                                    // 日期
	CopyrightSeriesName                       interface{} `orm:"copyright_series_name" json:"copyrightSeriesName"`                                                 // 短剧名称
	SalerEntityName                           interface{} `orm:"saler_entity_name" json:"salerEntityName"`                                                         // 分销商机构主体名称
	SalerRateStr                              interface{} `orm:"saler_rate_str" json:"salerRateStr"`                                                               // 版权方: 10.0%, 分销商: 90.0%
	PayAmt                                    interface{} `orm:"pay_amt" json:"payAmt"`                                                                            // 付费金额 (seriesType为2时特有)
	CopyrightEventPayPurchaseAmount           interface{} `orm:"copyright_event_pay_purchase_amount" json:"copyrightEventPayPurchaseAmount"`                       // 版权方分成金额 (seriesType为2时特有)
	MiniGameIaaPurchaseAmount                 interface{} `orm:"mini_game_iaa_purchase_amount" json:"miniGameIaaPurchaseAmount"`                                   // IAA广告含返货LTV(元) (seriesType为1时特有)
	MiniGameIaaPurchaseAmountDivided          interface{} `orm:"mini_game_iaa_purchase_amount_divided" json:"miniGameIaaPurchaseAmountDivided"`                    // IAA广告不含返货LTV(元) (seriesType为1时特有)
	CopyrightMiniGameIaaPurchaseAmountDivided interface{} `orm:"copyright_mini_game_iaa_purchase_amount_divided" json:"copyrightMiniGameIaaPurchaseAmountDivided"` // 版权方分成LTV(元) (seriesType为1时特有)
	SeriesType                                interface{} `orm:"series_type" json:"seriesType"`                                                                    // 短剧类型: 1->IAA短剧，2->IAP短剧
	CreatedAt                                 *gtime.Time `orm:"created_at" json:"createdAt"`                                                                      // 创建时间
	UpdatedAt                                 *gtime.Time `orm:"updated_at" json:"updatedAt"`                                                                      // 更新时间
}
