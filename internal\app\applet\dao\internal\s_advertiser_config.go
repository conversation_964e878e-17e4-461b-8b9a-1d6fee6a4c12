// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-03-06 17:53:48
// 生成路径: internal/app/applet/dao/internal/s_advertiser_config.go
// 生成人：cq
// desc:广告配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SAdvertiserConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type SAdvertiserConfigDao struct {
	table   string                   // Table is the underlying table name of the DAO.
	group   string                   // Group is the database configuration group name of current DAO.
	columns SAdvertiserConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SAdvertiserConfigColumns defines and stores column names for table s_advertiser_config.
type SAdvertiserConfigColumns struct {
	Id                      string //
	AppId                   string // 小程序ID
	OpenScreenAd            string // 开屏广告 0：关闭 1：打开
	OpenInterstitialAd      string // 插屏广告 0：关闭 1：打开
	DayPopUpNum             string // 插屏广告每天弹出次数
	Intervals               string // 插屏广告间隔时间(分钟)
	OpenRewardedAd          string // 激励广告 0：关闭 1：打开
	DayViewsNum             string // 激励广告每日观看次数
	DayUnlockEpisodesNum    string // 激励广告每日解锁集数
	Createtime              string // 创建时间
	Updatetime              string // 更新时间
	AdPositionId            string // 广告位id
	OpenRetainWindow        string // 挽留弹窗 0：关闭 1：打开
	RetainAdPositionId      string // 挽留广告位id
	RetainTitle             string // 挽留弹窗标题
	RetainUnlockEpisodesNum string // 挽留广告每次解锁集数
	RetainDayViewsNum       string // 挽留广告每日触发次数
	RetainWindowSeconds     string // 挽留弹窗倒计时(秒)
}

var sAdvertiserConfigColumns = SAdvertiserConfigColumns{
	Id:                      "id",
	AppId:                   "app_id",
	OpenScreenAd:            "open_screen_ad",
	OpenInterstitialAd:      "open_interstitial_ad",
	DayPopUpNum:             "day_pop_up_num",
	Intervals:               "intervals",
	OpenRewardedAd:          "open_rewarded_ad",
	DayViewsNum:             "day_views_num",
	DayUnlockEpisodesNum:    "day_unlock_episodes_num",
	Createtime:              "createtime",
	Updatetime:              "updatetime",
	AdPositionId:            "ad_position_id",
	OpenRetainWindow:        "open_retain_window",
	RetainAdPositionId:      "retain_ad_position_id",
	RetainTitle:             "retain_title",
	RetainUnlockEpisodesNum: "retain_unlock_episodes_num",
	RetainDayViewsNum:       "retain_day_views_num",
	RetainWindowSeconds:     "retain_window_seconds",
}

// NewSAdvertiserConfigDao creates and returns a new DAO object for table data access.
func NewSAdvertiserConfigDao() *SAdvertiserConfigDao {
	return &SAdvertiserConfigDao{
		group:   "default",
		table:   "s_advertiser_config",
		columns: sAdvertiserConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SAdvertiserConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SAdvertiserConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SAdvertiserConfigDao) Columns() SAdvertiserConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SAdvertiserConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SAdvertiserConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SAdvertiserConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
