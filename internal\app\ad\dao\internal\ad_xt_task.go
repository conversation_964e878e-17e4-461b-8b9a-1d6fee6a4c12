// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: internal/app/ad/dao/internal/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdXtTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdXtTaskDao struct {
	table   string          // Table is the underlying table name of the DAO.
	group   string          // Group is the database configuration group name of current DAO.
	columns AdXtTaskColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdXtTaskColumns defines and stores column names for table ad_xt_task.
type AdXtTaskColumns struct {
	Id                      string // ID
	AdvertiserId            string // 账户ID 对应 star_id
	TaskId                  string // 任务ID
	UniversalSettlementType string
	TaskName                string // 任务名称
	Attachments             string // 参考素材
	AuthorTaskName          string // 达人侧任务名称
	TaskIcon                string // 任务图标
	TaskHeadImage           string // 任务头图
	StartTime               string // 投稿开始时间
	EndTime                 string // 任务截止时间
	SampleVideo             string // 示例视频
	MicroAppId              string // 小程序ID
	StartPage               string // 小程序落地页地址
	AnchorTitle             string // 组件标题
	CommissionType          string // 结算方式0unkonw 1广告分成 2cps支付分佣 3cps绑定分佣 7混合分成
	AuthorScope             string // 达人定向范围，任务定向类型判断规则参考
	ProviderScope           string // 服务商定向范围，任务定向类型判断规则参考
	CommissionRate          string // 分佣比例，适用于付费分佣结算和广告分成结算
	AdCommissionRate        string // 广告分成比例
	PayCommissionRate       string // 付费分佣比例
	AccountDivideDay        string // 最长分账周期
	DemandDesc              string // 任务介绍
	OmTaskStatus            string // 任务状态：1审核中 2审核失败 3进行中 4下架 4计费中 6已取消 7已结束 8已关闭
	OmTaskTag               string // 任务标签
	AuthorList              string // unique_id - 达人抖音号 participate_status - 合作状态，1-已发布视频 2-待发布视频 3-合作已取消 cancel_role - 取消方：1-开发者，2-达人，3-平台
	CreatedAt               string // 创建时间
	UpdatedAt               string // 更新时间
	DeletedAt               string // 删除时间
}

var adXtTaskColumns = AdXtTaskColumns{
	Id:                      "id",
	AdvertiserId:            "advertiser_id",
	TaskId:                  "task_id",
	TaskName:                "task_name",
	Attachments:             "attachments",
	AuthorTaskName:          "author_task_name",
	TaskIcon:                "task_icon",
	TaskHeadImage:           "task_head_image",
	StartTime:               "start_time",
	EndTime:                 "end_time",
	SampleVideo:             "sample_video",
	MicroAppId:              "micro_app_id",
	StartPage:               "start_page",
	AnchorTitle:             "anchor_title",
	CommissionType:          "commission_type",
	AuthorScope:             "author_scope",
	ProviderScope:           "provider_scope",
	CommissionRate:          "commission_rate",
	AdCommissionRate:        "ad_commission_rate",
	PayCommissionRate:       "pay_commission_rate",
	AccountDivideDay:        "account_divide_day",
	DemandDesc:              "demand_desc",
	OmTaskStatus:            "om_task_status",
	OmTaskTag:               "om_task_tag",
	AuthorList:              "author_list",
	CreatedAt:               "created_at",
	UpdatedAt:               "updated_at",
	DeletedAt:               "deleted_at",
	UniversalSettlementType: "universal_settlement_type",
}

// NewAdXtTaskDao creates and returns a new DAO object for table data access.
func NewAdXtTaskDao() *AdXtTaskDao {
	return &AdXtTaskDao{
		group:   "default",
		table:   "ad_xt_task",
		columns: adXtTaskColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdXtTaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdXtTaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdXtTaskDao) Columns() AdXtTaskColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdXtTaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdXtTaskDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdXtTaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
