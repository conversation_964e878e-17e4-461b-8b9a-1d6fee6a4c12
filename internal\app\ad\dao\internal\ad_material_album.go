// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: internal/app/ad/dao/internal/ad_material_album.go
// 生成人：cyao
// desc:广告素材专辑
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMaterialAlbumDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdMaterialAlbumDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns AdMaterialAlbumColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdMaterialAlbumColumns defines and stores column names for table ad_material_album.
type AdMaterialAlbumColumns struct {
	AlbumId        string // id
	AlbumName      string // 专辑名称
	UserId         string // 创建人
	ScopeAuthority string // 1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限
	CreatedAt      string // 创建时间
	Remark         string // 备注
	UpdatedAt      string
	DeletedAt      string
}

var adMaterialAlbumColumns = AdMaterialAlbumColumns{
	AlbumId:        "album_id",
	AlbumName:      "album_name",
	UserId:         "user_id",
	ScopeAuthority: "scope_authority",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	Remark:         "remark",
	DeletedAt:      "deleted_at"}

// NewAdMaterialAlbumDao creates and returns a new DAO object for table data access.
func NewAdMaterialAlbumDao() *AdMaterialAlbumDao {
	return &AdMaterialAlbumDao{
		group:   "default",
		table:   "ad_material_album",
		columns: adMaterialAlbumColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdMaterialAlbumDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdMaterialAlbumDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdMaterialAlbumDao) Columns() AdMaterialAlbumColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdMaterialAlbumDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdMaterialAlbumDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdMaterialAlbumDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
