// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-09 17:30:59
// 生成路径: api/v1/channel/s_channel_push_task_detail.go
// 生成人：cq
// desc:渠道推送任务详情相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelPushTaskDetailSearchReq 分页请求参数
type SChannelPushTaskDetailSearchReq struct {
	g.Meta `path:"/list" tags:"渠道推送任务详情" method:"post" summary:"渠道推送任务详情列表"`
	commonApi.Author
	model.SChannelPushTaskDetailSearchReq
}

// SChannelPushTaskDetailSearchRes 列表返回结果
type SChannelPushTaskDetailSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelPushTaskDetailSearchRes
}

// SChannelPushTaskDetailAddReq 添加操作请求参数
type SChannelPushTaskDetailAddReq struct {
	g.Meta `path:"/add" tags:"渠道推送任务详情" method:"post" summary:"渠道推送任务详情添加"`
	commonApi.Author
	*model.SChannelPushTaskDetailAddReq
}

// SChannelPushTaskDetailAddRes 添加操作返回结果
type SChannelPushTaskDetailAddRes struct {
	commonApi.EmptyRes
}

// SChannelPushTaskDetailEditReq 修改操作请求参数
type SChannelPushTaskDetailEditReq struct {
	g.Meta `path:"/edit" tags:"渠道推送任务详情" method:"put" summary:"渠道推送任务详情修改"`
	commonApi.Author
	*model.SChannelPushTaskDetailEditReq
}

// SChannelPushTaskDetailEditRes 修改操作返回结果
type SChannelPushTaskDetailEditRes struct {
	commonApi.EmptyRes
}

// SChannelPushTaskDetailGetReq 获取一条数据请求
type SChannelPushTaskDetailGetReq struct {
	g.Meta `path:"/get" tags:"渠道推送任务详情" method:"get" summary:"获取渠道推送任务详情信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelPushTaskDetailGetRes 获取一条数据结果
type SChannelPushTaskDetailGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelPushTaskDetailInfoRes
}

// SChannelPushTaskDetailDeleteReq 删除数据请求
type SChannelPushTaskDetailDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道推送任务详情" method:"post" summary:"删除渠道推送任务详情"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelPushTaskDetailDeleteRes 删除数据返回
type SChannelPushTaskDetailDeleteRes struct {
	commonApi.EmptyRes
}
