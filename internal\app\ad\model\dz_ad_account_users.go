// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:33
// 生成路径: internal/app/ad/model/dz_ad_account_users.go
// 生成人：cyao
// desc:权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdAccountUsersInfoRes is the golang structure for table dz_ad_account_users.
type DzAdAccountUsersInfoRes struct {
	gmeta.Meta    `orm:"table:dz_ad_account_users"`
	ChannelId     int64 `orm:"channel_id,primary" json:"channelId" dc:"渠道id"`          // 渠道id
	SpecifyUserId int   `orm:"specify_user_id,primary" json:"specifyUserId" dc:"用户ID"` // 用户ID
}

type DzAdAccountUsersListRes struct {
	ChannelId     int64 `json:"channelId" dc:"渠道id"`
	SpecifyUserId int   `json:"specifyUserId" dc:"用户ID"`
}

// DzAdAccountUsersSearchReq 分页请求参数
type DzAdAccountUsersSearchReq struct {
	comModel.PageReq
	ChannelId     string  `p:"channelId" dc:"渠道id"` //渠道id
	ChannelIds    []int64 `p:"channelIds" dc:"渠道id"`
	SpecifyUserId string  `p:"specifyUserId" dc:"用户ID"` //用户ID
}

// DzAdAccountUsersSearchRes 列表返回结果
type DzAdAccountUsersSearchRes struct {
	comModel.ListRes
	List []*DzAdAccountUsersListRes `json:"list"`
}

// DzAdAccountUsersAddReq 添加操作请求参数
type DzAdAccountUsersAddReq struct {
	ChannelId int64 `p:"channelId" v:"required#主键ID不能为空" dc:"渠道id"`
}

// DzAdAccountUsersEditReq 修改操作请求参数
type DzAdAccountUsersEditReq struct {
	ChannelId int64 `p:"channelId" v:"required#主键ID不能为空" dc:"渠道id"`
}
