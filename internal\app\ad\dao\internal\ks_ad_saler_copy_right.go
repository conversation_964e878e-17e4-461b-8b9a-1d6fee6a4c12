// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: internal/app/ad/dao/internal/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdSalerCopyRightDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdSalerCopyRightDao struct {
	table   string                    // Table is the underlying table name of the DAO.
	group   string                    // Group is the database configuration group name of current DAO.
	columns KsAdSalerCopyRightColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdSalerCopyRightColumns defines and stores column names for table ks_ad_saler_copy_right.
type KsAdSalerCopyRightColumns struct {
	Id                                        string // id
	AdvertiserId                              string // 用户快手号id
	ReportDate                                string // 日期
	CopyrightSeriesName                       string // 短剧名称
	SalerEntityName                           string // 分销商机构主体名称
	SalerRateStr                              string // 版权方: 10.0%, 分销商: 90.0%
	PayAmt                                    string // 付费金额 (seriesType为2时特有)
	CopyrightEventPayPurchaseAmount           string // 版权方分成金额 (seriesType为2时特有)
	MiniGameIaaPurchaseAmount                 string // IAA广告含返货LTV(元) (seriesType为1时特有)
	MiniGameIaaPurchaseAmountDivided          string // IAA广告不含返货LTV(元) (seriesType为1时特有)
	CopyrightMiniGameIaaPurchaseAmountDivided string // 版权方分成LTV(元) (seriesType为1时特有)
	SeriesType                                string // 短剧类型: 1->IAA短剧，2->IAP短剧
	CreatedAt                                 string // 创建时间
	UpdatedAt                                 string // 更新时间
}

var ksAdSalerCopyRightColumns = KsAdSalerCopyRightColumns{
	Id:                               "id",
	AdvertiserId:                     "advertiser_id",
	ReportDate:                       "report_date",
	CopyrightSeriesName:              "copyright_series_name",
	SalerEntityName:                  "saler_entity_name",
	SalerRateStr:                     "saler_rate_str",
	PayAmt:                           "pay_amt",
	CopyrightEventPayPurchaseAmount:  "copyright_event_pay_purchase_amount",
	MiniGameIaaPurchaseAmount:        "mini_game_iaa_purchase_amount",
	MiniGameIaaPurchaseAmountDivided: "mini_game_iaa_purchase_amount_divided",
	CopyrightMiniGameIaaPurchaseAmountDivided: "copyright_mini_game_iaa_purchase_amount_divided",
	SeriesType: "series_type",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// NewKsAdSalerCopyRightDao creates and returns a new DAO object for table data access.
func NewKsAdSalerCopyRightDao() *KsAdSalerCopyRightDao {
	return &KsAdSalerCopyRightDao{
		group:   "default",
		table:   "ks_ad_saler_copy_right",
		columns: ksAdSalerCopyRightColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdSalerCopyRightDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdSalerCopyRightDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdSalerCopyRightDao) Columns() KsAdSalerCopyRightColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdSalerCopyRightDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdSalerCopyRightDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdSalerCopyRightDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
