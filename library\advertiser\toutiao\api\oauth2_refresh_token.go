package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	. "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

type Oauth2RefreshTokenApiService struct {
	ctx     context.Context
	cfg     *Configuration
	Request *Oauth2RefreshTokenRequest
}

func (r *Oauth2RefreshTokenApiService) SetCfg(cfg *Configuration) *Oauth2RefreshTokenApiService {
	r.cfg = cfg
	return r
}

func (r *Oauth2RefreshTokenApiService) SetRequest(oauth2RefreshTokenRequest Oauth2RefreshTokenRequest) *Oauth2RefreshTokenApiService {
	r.Request = &oauth2RefreshTokenRequest
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *Oauth2RefreshTokenApiService) Do() (data *Oauth2RefreshTokenResponseData, err error) {
	localBasePath := r.cfg.GetBasePath()
	if r.Request.AppId == 0 || libUtils.IsNullOrEmpty(r.Request.Secret) || libUtils.IsNullOrEmpty(r.Request.RefreshToken) {
		return nil, errors.New("appid secret  refresh_token 不能为空")
	}
	localVarPath := localBasePath + "/open_api/oauth2/refresh_token/"
	response, err := r.cfg.HTTPClient.R().SetHeader("Content-Type", "application/json").
		SetResult(&Oauth2RefreshTokenResponse{}).
		SetBody(r.Request).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(Oauth2RefreshTokenResponse)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp.Data, nil
	} else {
		return resp.Data, errors.New(resp.Message)
	}
}
