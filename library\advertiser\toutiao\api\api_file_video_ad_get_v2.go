/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)
// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

// FileVideoAdGetV2ApiService
type FileVideoAdGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2FileVideoAdGetGetRequest
}

type ApiOpenApi2FileVideoAdGetGetRequest struct {
	AdvertiserId *int64
	VideoIds     *[]string
}

func (r *FileVideoAdGetV2ApiService) SetCfg(cfg *conf.Configuration) *FileVideoAdGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileVideoAdGetV2ApiService) SetRequest(advertiserInfoV2Request ApiOpenApi2FileVideoAdGetGetRequest) *FileVideoAdGetV2ApiService {
	r.Request = &advertiserInfoV2Request
	return r
}

func (r *FileVideoAdGetV2ApiService) AccessToken(accessToken string) *FileVideoAdGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *FileVideoAdGetV2ApiService) Do() (data *models.FileVideoAdGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/video/ad/get/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "video_ids", r.Request.VideoIds)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.FileVideoAdGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.FileVideoAdGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/video/ad/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
