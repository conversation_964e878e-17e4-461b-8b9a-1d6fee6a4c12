// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-07 15:25:30
// 生成路径: internal/app/ad/dao/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// dzAdAccountDeptsDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzAdAccountDeptsDao struct {
	*internal.DzAdAccountDeptsDao
}

var (
	// DzAdAccountDepts is globally public accessible object for table tools_gen_table operations.
	DzAdAccountDepts = dzAdAccountDeptsDao{
		internal.NewDzAdAccountDeptsDao(),
	}
)

// Fill with you ideas below.
