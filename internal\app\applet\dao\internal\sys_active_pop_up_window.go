// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-09-02 14:44:05
// 生成路径: internal/app/applet/dao/internal/sys_active_pop_up_window.go
// 生成人：cyao
// desc:活动弹出窗口
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysActivePopUpWindowDao is the manager for logic model data accessing and custom defined data operations functions management.
type SysActivePopUpWindowDao struct {
	table   string                      // Table is the underlying table name of the DAO.
	group   string                      // Group is the database configuration group name of current DAO.
	columns SysActivePopUpWindowColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SysActivePopUpWindowColumns defines and stores column names for table sys_active_pop_up_window.
type SysActivePopUpWindowColumns struct {
	Id          string //
	ImgUrl      string // 图片url
	AppId       string // 小程序ID
	LinkType    string // 链接类型：1 不跳转 2.页面 3.短剧 4.链接
	LinkUrl     string // 如果是link_type = 2，3 的时候使用linkUrl 进行跳转
	ParentId    string // 剧集父Id
	Remark      string // 备注
	Sort        string // 排序越大越靠前
	Status      string // 0失效 1有效
	UpdateTime  string // 修改时间
	CreateTime  string // 创建时间
	ExpiredTime string // 过期时间
}

var sysActivePopUpWindowColumns = SysActivePopUpWindowColumns{
	Id:          "id",
	ImgUrl:      "img_url",
	AppId:       "app_id",
	LinkType:    "link_type",
	LinkUrl:     "link_url",
	ParentId:    "parent_id",
	Remark:      "remark",
	Sort:        "sort",
	Status:      "status",
	UpdateTime:  "update_time",
	CreateTime:  "create_time",
	ExpiredTime: "expired_time",
}

// NewSysActivePopUpWindowDao creates and returns a new DAO object for table data access.
func NewSysActivePopUpWindowDao() *SysActivePopUpWindowDao {
	return &SysActivePopUpWindowDao{
		group:   "default",
		table:   "sys_active_pop_up_window",
		columns: sysActivePopUpWindowColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysActivePopUpWindowDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysActivePopUpWindowDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysActivePopUpWindowDao) Columns() SysActivePopUpWindowColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysActivePopUpWindowDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysActivePopUpWindowDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysActivePopUpWindowDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
