/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30CalculateRuleFieldFilters
type YuntuAudienceLabelCreateV30CalculateRuleFieldFilters string

// List of yuntu_audience_label_create_v3.0_calculate_rule_field_filters
const (
	ASR_CONTENT_YuntuAudienceLabelCreateV30CalculateRuleFieldFilters  YuntuAudienceLabelCreateV30CalculateRuleFieldFilters = "asr_content"
	OCR_CONTENT_YuntuAudienceLabelCreateV30CalculateRuleFieldFilters  YuntuAudienceLabelCreateV30CalculateRuleFieldFilters = "ocr_content"
	PRODUCT_NAME_YuntuAudienceLabelCreateV30CalculateRuleFieldFilters YuntuAudienceLabelCreateV30CalculateRuleFieldFilters = "product_name"
	QUERY_YuntuAudienceLabelCreateV30CalculateRuleFieldFilters        YuntuAudienceLabelCreateV30CalculateRuleFieldFilters = "query"
	TITLE_YuntuAudienceLabelCreateV30CalculateRuleFieldFilters        YuntuAudienceLabelCreateV30CalculateRuleFieldFilters = "title"
)

// Ptr returns reference to yuntu_audience_label_create_v3.0_calculate_rule_field_filters value
func (v YuntuAudienceLabelCreateV30CalculateRuleFieldFilters) Ptr() *YuntuAudienceLabelCreateV30CalculateRuleFieldFilters {
	return &v
}
