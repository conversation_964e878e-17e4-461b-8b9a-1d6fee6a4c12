// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/model/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdUserInfoInfoRes is the golang structure for table dz_ad_user_info.
type DzAdUserInfoInfoRes struct {
	gmeta.Meta   `orm:"table:dz_ad_user_info"`
	UserId       string      `orm:"user_id,primary" json:"userId" dc:"用户ID"`             // 用户ID
	Time         int64       `orm:"time" json:"time" dc:"请求时间戳，单位毫秒"`                    // 请求时间戳，单位毫秒
	ChannelId    string      `orm:"channel_id" json:"channelId" dc:"渠道ID"`               // 渠道ID
	AppId        string      `orm:"app_id" json:"appId" dc:"小程序ID"`                      // 小程序ID
	PromotionId  string      `orm:"promotion_id" json:"promotionId" dc:"推广班组ID"`         // 推广班组ID
	OpenId       string      `orm:"open_id" json:"openId" dc:"openID"`                   // openID
	AdId         string      `orm:"ad_id" json:"adId" dc:"广告ID"`                         // 广告ID
	BookId       string      `orm:"book_id" json:"bookId" dc:"书籍ID"`                     // 书籍ID
	ProjectId    string      `orm:"project_id" json:"projectId" dc:"广告计划ID"`             // 广告计划ID
	ClickId      string      `orm:"click_id" json:"clickId" dc:"广告点击ID"`                 // 广告点击ID
	UnionId      string      `orm:"union_id" json:"unionId" dc:"unionID"`                // unionID
	RegisterTime int64       `orm:"register_time" json:"registerTime" dc:"用户注册时间戳，单位毫秒"` // 用户注册时间戳，单位毫秒
	DyeTime      int64       `orm:"dye_time" json:"dyeTime" dc:"用户染色时间戳，单位毫秒"`           // 用户染色时间戳，单位毫秒
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`               // 创建时间
}

type DzAdUserInfoListRes struct {
	UserId       string      `json:"userId" dc:"用户ID"`
	Time         int64       `json:"time" dc:"请求时间戳，单位毫秒"`
	ChannelId    string      `json:"channelId" dc:"渠道ID"`
	AppId        string      `json:"appId" dc:"小程序ID"`
	PromotionId  string      `json:"promotionId" dc:"推广班组ID"`
	OpenId       string      `json:"openId" dc:"openID"`
	AdId         string      `json:"adId" dc:"广告ID"`
	BookId       string      `json:"bookId" dc:"书籍ID"`
	ProjectId    string      `json:"projectId" dc:"广告计划ID"`
	ClickId      string      `json:"clickId" dc:"广告点击ID"`
	UnionId      string      `json:"unionId" dc:"unionID"`
	RegisterTime int64       `json:"registerTime" dc:"用户注册时间戳，单位毫秒"`
	DyeTime      int64       `json:"dyeTime" dc:"用户染色时间戳，单位毫秒"`
	CreatedAt    *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// DzAdUserInfoSearchReq 分页请求参数
type DzAdUserInfoSearchReq struct {
	comModel.PageReq
	UserId       string `p:"userId" dc:"用户ID"`                                                         //用户ID
	Time         string `p:"time" v:"time@integer#请求时间戳，单位毫秒需为整数" dc:"请求时间戳，单位毫秒"`                     //请求时间戳，单位毫秒
	ChannelId    string `p:"channelId" dc:"渠道ID"`                                                      //渠道ID
	AppId        string `p:"appId" dc:"小程序ID"`                                                         //小程序ID
	PromotionId  string `p:"promotionId" dc:"推广班组ID"`                                                  //推广班组ID
	OpenId       string `p:"openId" dc:"openID"`                                                       //openID
	AdId         string `p:"adId" dc:"广告ID"`                                                           //广告ID
	BookId       string `p:"bookId" dc:"书籍ID"`                                                         //书籍ID
	ProjectId    string `p:"projectId" dc:"广告计划ID"`                                                    //广告计划ID
	ClickId      string `p:"clickId" dc:"广告点击ID"`                                                      //广告点击ID
	UnionId      string `p:"unionId" dc:"unionID"`                                                     //unionID
	RegisterTime string `p:"registerTime" v:"registerTime@integer#用户注册时间戳，单位毫秒需为整数" dc:"用户注册时间戳，单位毫秒"` //用户注册时间戳，单位毫秒
	DyeTime      string `p:"dyeTime" v:"dyeTime@integer#用户染色时间戳，单位毫秒需为整数" dc:"用户染色时间戳，单位毫秒"`           //用户染色时间戳，单位毫秒
	CreatedAt    string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`   //创建时间
}

// DzAdUserInfoSearchRes 列表返回结果
type DzAdUserInfoSearchRes struct {
	comModel.ListRes
	List []*DzAdUserInfoListRes `json:"list"`
}

// DzAdUserInfoAddReq 添加操作请求参数
type DzAdUserInfoAddReq struct {
	UserId       string `p:"userId" v:"required#主键ID不能为空" dc:"用户ID"`
	Time         int64  `p:"time" v:"required#请求时间戳，单位毫秒不能为空" dc:"请求时间戳，单位毫秒"`
	ChannelId    string `p:"channelId" v:"required#渠道ID不能为空" dc:"渠道ID"`
	AppId        string `p:"appId"  dc:"小程序ID"`
	PromotionId  string `p:"promotionId"  dc:"推广班组ID"`
	OpenId       string `p:"openId"  dc:"openID"`
	AdId         string `p:"adId"  dc:"广告ID"`
	BookId       string `p:"bookId"  dc:"书籍ID"`
	ProjectId    string `p:"projectId"  dc:"广告计划ID"`
	ClickId      string `p:"clickId"  dc:"广告点击ID"`
	UnionId      string `p:"unionId"  dc:"unionID"`
	RegisterTime int64  `p:"registerTime"  dc:"用户注册时间戳，单位毫秒"`
	DyeTime      int64  `p:"dyeTime"  dc:"用户染色时间戳，单位毫秒"`
}

// DzAdUserInfoEditReq 修改操作请求参数
type DzAdUserInfoEditReq struct {
	UserId       string `p:"userId" v:"required#主键ID不能为空" dc:"用户ID"`
	Time         int64  `p:"time" v:"required#请求时间戳，单位毫秒不能为空" dc:"请求时间戳，单位毫秒"`
	ChannelId    string `p:"channelId" v:"required#渠道ID不能为空" dc:"渠道ID"`
	AppId        string `p:"appId"  dc:"小程序ID"`
	PromotionId  string `p:"promotionId"  dc:"推广班组ID"`
	OpenId       string `p:"openId"  dc:"openID"`
	AdId         string `p:"adId"  dc:"广告ID"`
	BookId       string `p:"bookId"  dc:"书籍ID"`
	ProjectId    string `p:"projectId"  dc:"广告计划ID"`
	ClickId      string `p:"clickId"  dc:"广告点击ID"`
	UnionId      string `p:"unionId"  dc:"unionID"`
	RegisterTime int64  `p:"registerTime"  dc:"用户注册时间戳，单位毫秒"`
	DyeTime      int64  `p:"dyeTime"  dc:"用户染色时间戳，单位毫秒"`
}
