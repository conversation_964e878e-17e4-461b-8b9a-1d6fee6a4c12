// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: internal/app/theater/logic/dz_native_link.go
// 生成人：cq
// desc:点众原生链接
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"net/url"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/theater/dao"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/theater/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzNativeLink(New())
}

func New() service.IDzNativeLink {
	return &sDzNativeLink{}
}

type sDzNativeLink struct{}

// generatePHPSESSID 生成随机PHPSESSID
func generatePHPSESSID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func (s *sDzNativeLink) List(ctx context.Context, req *model.DzNativeLinkSearchReq) (listRes *model.DzNativeLinkSearchRes, err error) {
	listRes = new(model.DzNativeLinkSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzNativeLink.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.DzNativeLink.Columns().Id+" = ?", req.Id)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.DzNativeLink.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.BookId != "" {
			m = m.Where(dao.DzNativeLink.Columns().BookId+" = ?", req.BookId)
		}
		if req.BookName != "" {
			m = m.Where(dao.DzNativeLink.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.DyBookId != "" {
			m = m.Where(dao.DzNativeLink.Columns().DyBookId+" = ?", req.DyBookId)
		}
		if req.AccountUserName != "" {
			m = m.Where(dao.DzNativeLink.Columns().AccountUserName+" like ?", "%"+req.AccountUserName+"%")
		}
		if req.DistributorId != "" {
			m = m.Where(dao.DzNativeLink.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.DistributorName != "" {
			m = m.Where(dao.DzNativeLink.Columns().DistributorName+" like ?", "%"+req.DistributorName+"%")
		}
		if req.ChannelId != "" {
			m = m.Where(dao.DzNativeLink.Columns().ChannelId+" = ?", gconv.Int64(req.ChannelId))
		}
		if req.PurchasePanelId != "" {
			m = m.Where(dao.DzNativeLink.Columns().PurchasePanelId+" = ?", req.PurchasePanelId)
		}
		if req.PurchasePanelName != "" {
			m = m.Where(dao.DzNativeLink.Columns().PurchasePanelName+" like ?", "%"+req.PurchasePanelName+"%")
		}
		if req.AdvertiseLink != "" {
			m = m.Where(dao.DzNativeLink.Columns().AdvertiseLink+" = ?", req.AdvertiseLink)
		}
		if req.TaskStatus != "" {
			m = m.Where(dao.DzNativeLink.Columns().TaskStatus+" = ?", gconv.Int(req.TaskStatus))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.DzNativeLink.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.DzNativeLink.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		if req.BookStatus != "" {
			m = m.Where(dao.DzNativeLink.Columns().BookStatus+" = ?", gconv.Int(req.BookStatus))
		}
		if req.BookType != "" {
			m = m.Where(dao.DzNativeLink.Columns().BookType+" = ?", gconv.Int(req.BookType))
		}
		if req.Connect != "" {
			m = m.Where(dao.DzNativeLink.Columns().Connect+" = ?", gconv.Int(req.Connect))
		}
		if req.PutTimeText != "" {
			m = m.Where(dao.DzNativeLink.Columns().PutTimeText+" = ?", req.PutTimeText)
		}
		if req.Type != "" {
			m = m.Where(dao.DzNativeLink.Columns().Type+" = ?", gconv.Int(req.Type))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzNativeLinkListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzNativeLinkListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzNativeLinkListRes{
				Id:                v.Id,
				ProjectId:         v.ProjectId,
				BookId:            v.BookId,
				BookName:          v.BookName,
				DyBookId:          v.DyBookId,
				AccountUserName:   v.AccountUserName,
				DistributorId:     v.DistributorId,
				DistributorName:   v.DistributorName,
				ChannelId:         v.ChannelId,
				PurchasePanelId:   v.PurchasePanelId,
				PurchasePanelName: v.PurchasePanelName,
				PurchasePanelJson: v.PurchasePanelJson,
				AdvertiseLink:     v.AdvertiseLink,
				TaskStatus:        v.TaskStatus,
				Response:          v.Response,
				CreateTime:        v.CreateTime,
				UpdateTime:        v.UpdateTime,
				BookStatus:        v.BookStatus,
				BookType:          v.BookType,
				Connect:           v.Connect,
				PutTimeText:       v.PutTimeText,
				Type:              v.Type,
			}
		}
	})
	return
}

func (s *sDzNativeLink) GetById(ctx context.Context, id uint64) (res *model.DzNativeLinkInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzNativeLink.Ctx(ctx).WithAll().Where(dao.DzNativeLink.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzNativeLink) Add(ctx context.Context, req *model.DzNativeLinkAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzNativeLink.Ctx(ctx).Insert(do.DzNativeLink{
			Id:                req.Id,
			ProjectId:         req.ProjectId,
			BookId:            req.BookId,
			BookName:          req.BookName,
			DyBookId:          req.DyBookId,
			AccountUserName:   req.AccountUserName,
			DistributorId:     req.DistributorId,
			DistributorName:   req.DistributorName,
			ChannelId:         req.ChannelId,
			PurchasePanelId:   req.PurchasePanelId,
			PurchasePanelName: req.PurchasePanelName,
			PurchasePanelJson: req.PurchasePanelJson,
			AdvertiseLink:     req.AdvertiseLink,
			TaskStatus:        req.TaskStatus,
			Response:          req.Response,
			CreateTime:        req.CreateTime,
			UpdateTime:        req.UpdateTime,
			BookStatus:        req.BookStatus,
			BookType:          req.BookType,
			Connect:           req.Connect,
			PutTimeText:       req.PutTimeText,
			Type:              req.Type,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzNativeLink) Edit(ctx context.Context, req *model.DzNativeLinkEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzNativeLink.Ctx(ctx).WherePri(req.Id).Update(do.DzNativeLink{
			ProjectId:         req.ProjectId,
			BookId:            req.BookId,
			BookName:          req.BookName,
			DyBookId:          req.DyBookId,
			AccountUserName:   req.AccountUserName,
			DistributorId:     req.DistributorId,
			DistributorName:   req.DistributorName,
			ChannelId:         req.ChannelId,
			PurchasePanelId:   req.PurchasePanelId,
			PurchasePanelName: req.PurchasePanelName,
			PurchasePanelJson: req.PurchasePanelJson,
			AdvertiseLink:     req.AdvertiseLink,
			TaskStatus:        req.TaskStatus,
			Response:          req.Response,
			CreateTime:        req.CreateTime,
			UpdateTime:        req.UpdateTime,
			BookStatus:        req.BookStatus,
			BookType:          req.BookType,
			Connect:           req.Connect,
			PutTimeText:       req.PutTimeText,
			Type:              req.Type,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzNativeLink) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzNativeLink.Ctx(ctx).Delete(dao.DzNativeLink.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// DzNativeLinkCrawlTask 点众原生链接爬取定时任务
// 调度规则：
// - 10:00-10:30, 14:00-14:30, 16:00-16:30 每5分钟执行一次
// - 其他时间每30分钟执行一次
func (s *sDzNativeLink) DzNativeLinkCrawlTask(ctx context.Context) {
	now := gtime.Now()
	// 检查是否应该执行任务
	if !shouldExecuteDzNativeLinkCrawl(now) {
		return
	}
	err := s.DzNativeLinkCrawl(ctx)
	if err != nil {
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "DzNativeLinkCrawlTask",
			CreatedAt:  now,
			Result:     "点众原生链接爬取任务失败，Err：" + err.Error(),
		})
	} else {
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "DzNativeLinkCrawlTask",
			CreatedAt:  now,
			Result:     "点众原生链接爬取任务执行成功",
		})
	}
}

// shouldExecuteDzNativeLinkCrawl 判断是否应该执行点众原生链接爬取任务
func shouldExecuteDzNativeLinkCrawl(now *gtime.Time) bool {
	hour := now.Hour()
	minute := now.Minute()
	// 定义高频时间段：10:00-10:30, 14:00-14:30, 16:00-16:30
	isHighFrequencyPeriod := (hour == 10 && minute <= 30) ||
		(hour == 14 && minute <= 30) ||
		(hour == 16 && minute <= 30)
	if isHighFrequencyPeriod {
		// 高频时间段：每5分钟执行一次（分钟数能被5整除）
		return minute%5 == 0
	} else {
		// 低频时间段：每30分钟执行一次（分钟数为0或30）
		return minute == 0 || minute == 30
	}
}

// DzNativeLinkCrawl 点众原生链接爬取
func (s *sDzNativeLink) DzNativeLinkCrawl(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取爬取配置
		configs := s.getCrawlConfigs()
		// 遍历每个账号配置
		for _, config := range configs {
			// 登录获取PHPSESSID
			phpSessionId, err := s.AdminLogin(ctx, &model.LoginRequest{
				Username: config.Username,
				Password: config.Password,
			})
			if err != nil {
				g.Log().Errorf(ctx, "账号 %s 登录失败: %v", config.Username, err)
				continue
			}
			// 爬取数据
			err = s.crawlAndNotify(ctx, config, phpSessionId)
			if err != nil {
				g.Log().Errorf(ctx, "账号 %s 数据爬取失败: %v", config.Username, err)
			}
		}
	})
	return
}

// GetTaskListWithParse 循环获取所有任务列表数据
func (s *sDzNativeLink) GetTaskListWithParse(ctx context.Context, phpsessid string, isPaid bool) (allRows []*model.DzNativeLinkInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		allRows = make([]*model.DzNativeLinkInfoRes, 0)
		currentOffset := 0
		pageSize := 100
		totalFetched := 0
		for {
			// 创建当前页的请求参数
			currentReq := &model.TaskListRequest{
				Offset:    currentOffset,
				Limit:     pageSize,
				PHPSESSID: phpsessid,
				Sort:      "id",
				Order:     "desc",
			}
			// 获取当前页数据
			pageResult, err := s.GetTaskList(ctx, currentReq, isPaid)
			if err != nil {
				return
			}
			// 检查是否有数据
			if len(pageResult.Rows) == 0 {
				break
			}
			err = s.BatchAdd(ctx, pageResult.Rows, isPaid)
			if err != nil {
				return
			}
			// 合并数据
			allRows = append(allRows, pageResult.Rows...)
			totalFetched += len(pageResult.Rows)
			// 检查是否已获取完所有数据
			if len(pageResult.Rows) < pageSize || totalFetched >= pageResult.Total {
				break
			}
			// 更新offset到下一页
			currentOffset += pageSize
		}
	})
	return
}

// GetTaskList 获取任务列表API并反序列化
func (s *sDzNativeLink) GetTaskList(ctx context.Context, req *model.TaskListRequest, isPaid bool) (result *model.TaskListResponse, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 创建HTTP客户端
		httpClient := libUtils.NewHTTPClientWithTransport(5 * time.Second)

		// 设置默认值
		sort := "id"
		if req.Sort != "" {
			sort = req.Sort
		}
		order := "desc"
		if req.Order != "" {
			order = req.Order
		}

		// 构建查询参数
		params := map[string]interface{}{
			"sort":   sort,
			"order":  order,
			"offset": req.Offset,
			"limit":  req.Limit,
			"_":      time.Now().UnixMilli(), // 时间戳防缓存
		}

		// 添加filter参数（需要URL编码）
		if req.Filter != "" {
			params["filter"] = req.Filter
			// 添加op参数，通常与filter一起使用
			params["op"] = `{"create_time":"RANGE"}`
		}

		// 构建请求头
		headers := map[string]string{
			"Accept":             "application/json, text/javascript, */*; q=0.01",
			"Accept-Language":    "zh-CN,zh;q=0.9",
			"Content-Type":       "application/json",
			"Cookie":             fmt.Sprintf("PHPSESSID=%s", req.PHPSESSID),
			"Priority":           "u=1, i",
			"Referer":            "https://admin.wqxsw.com/admin/endnative/task/channel_iapindex?addtabs=1",
			"Sec-Ch-Ua":          `"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"`,
			"Sec-Ch-Ua-Mobile":   "?0",
			"Sec-Ch-Ua-Platform": `"Windows"`,
			"Sec-Fetch-Dest":     "empty",
			"Sec-Fetch-Mode":     "cors",
			"Sec-Fetch-Site":     "same-origin",
			"User-Agent":         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			"X-Requested-With":   "XMLHttpRequest",
		}

		// 发送GET请求
		var reqUrl = "https://admin.wqxsw.com/admin/endnative/task/index"
		if isPaid {
			reqUrl = "https://admin.wqxsw.com/admin/endnative/task/iapindex"
		}
		responseBody, err := httpClient.GetWithHeader(
			reqUrl,
			params,
			headers)
		if err != nil {
			g.Log().Errorf(ctx, "发送HTTP请求失败: %v", err)
			return
		}

		// 解析JSON响应
		result = &model.TaskListResponse{}
		err = json.Unmarshal(responseBody, result)
		if err != nil {
			g.Log().Errorf(ctx, "解析响应JSON失败: %v, 原始响应: %s", err, string(responseBody))
			return
		}
	})
	return
}

// AdminLogin 管理员登录API
func (s *sDzNativeLink) AdminLogin(ctx context.Context, req *model.LoginRequest) (phpSessionId string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 创建HTTP客户端
		httpClient := libUtils.NewHTTPClientWithTransport(30 * time.Second)

		// 构建请求参数
		formData := url.Values{}
		formData.Set("username", req.Username)
		formData.Set("password", req.Password)
		formData.Set("keeplogin", "1")

		// 生成随机PHPSESSID
		phpSessionId = generatePHPSESSID()

		// 构建请求头
		headers := map[string]string{
			"Accept":             "application/json, text/javascript, */*; q=0.01",
			"Accept-Language":    "zh-CN,zh;q=0.9",
			"Content-Type":       "application/x-www-form-urlencoded; charset=UTF-8",
			"Cookie":             fmt.Sprintf("PHPSESSID=%s", phpSessionId),
			"Origin":             "https://admin.wqxsw.com",
			"Priority":           "u=1, i",
			"Referer":            "https://admin.wqxsw.com/admin/index/login",
			"Sec-Ch-Ua":          `"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"`,
			"Sec-Ch-Ua-Mobile":   "?0",
			"Sec-Ch-Ua-Platform": `"Windows"`,
			"Sec-Fetch-Dest":     "empty",
			"Sec-Fetch-Mode":     "cors",
			"Sec-Fetch-Site":     "same-origin",
			"User-Agent":         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			"X-Requested-With":   "XMLHttpRequest",
		}

		// 发送POST请求
		_, err := httpClient.PostWithHeader(
			"https://admin.wqxsw.com/admin/index/login",
			[]byte(formData.Encode()),
			headers)
		if err != nil {
			g.Log().Errorf(ctx, "点众登录失败: %v", err)
			return
		}

		// 记录请求日志
		g.Log().Infof(ctx, "登录请求发送成功，用户名: %s, PHPSESSID: %s", req.Username, phpSessionId)
	})
	return
}

// getCrawlConfigs 获取爬取配置
func (s *sDzNativeLink) getCrawlConfigs() []*model.CrawlConfig {
	return []*model.CrawlConfig{
		{
			Username: "msdysff1",
			Password: "DYSmansen666",
			Url:      "https://admin.wqxsw.com/admin/endnative/task/iapindex",
			IsPaid:   true,
		},
		{
			Username: "msdys1",
			Password: "DYSmansen666",
			Url:      "https://admin.wqxsw.com/admin/endnative/task/index",
			IsPaid:   false,
		},
	}
}

// crawlAndNotify 爬取数据并发送通知
func (s *sDzNativeLink) crawlAndNotify(ctx context.Context, config *model.CrawlConfig, phpSessionId string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取数据库中现有数据
		var dbData []*model.DzNativeLinkInfoRes
		m := dao.DzNativeLink.Ctx(ctx).WithAll()
		if config.IsPaid {
			m = m.Where(dao.DzNativeLink.Columns().Type, 1)
		} else {
			m = m.Where(dao.DzNativeLink.Columns().Type, 2)
		}
		err = m.Scan(&dbData)
		if err != nil {
			g.Log().Errorf(ctx, "查询数据库数据失败: %v", err)
			return
		}

		// 获取所有数据
		allData, err := s.GetTaskListWithParse(ctx, phpSessionId, config.IsPaid)
		if err != nil {
			return
		}

		if len(dbData) == 0 {
			return
		}

		dbDataMap := make(map[uint64]*model.DzNativeLinkInfoRes)
		for _, item := range dbData {
			dbDataMap[item.Id] = item
		}

		var newItems []*model.DzNativeLinkInfoRes     // 新增数据
		var updatedItems []*model.DzNativeLinkInfoRes // 更新数据
		for _, item := range allData {
			if dbItem, exists := dbDataMap[item.Id]; exists {
				// 检查更新时间是否变化
				if item.UpdateTime != nil &&
					dbItem.UpdateTime != nil &&
					item.UpdateTime.String() != dbItem.UpdateTime.String() {
					updatedItems = append(updatedItems, item)
				}
			} else {
				// 新增数据
				newItems = append(newItems, item)
			}
		}

		// 过滤需要推送的数据
		var notifyItems []*model.DzNativeLinkInfoRes
		if config.IsPaid {
			// 付费账号：只推送超超小额面板的数据
			for _, item := range append(newItems, updatedItems...) {
				if item.PurchasePanelName == "超超小额" {
					notifyItems = append(notifyItems, item)
				}
			}
		} else {
			// 免费账号：推送全部数据
			notifyItems = append(newItems, updatedItems...)
		}

		// 发送通知
		if len(notifyItems) > 0 {
			_ = s.sendNotification(ctx, config, notifyItems)
		}
	})
	return
}

// sendNotification 发送通知
func (s *sDzNativeLink) sendNotification(ctx context.Context, config *model.CrawlConfig, items []*model.DzNativeLinkInfoRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取通知模板
		template := s.getNotificationTemplate(config.IsPaid)

		// 构建消息内容
		var messageBuilder strings.Builder
		for _, item := range items {
			if config.IsPaid {
				// 付费模板
				messageBuilder.WriteString(fmt.Sprintf("<font color='orange'>**%s**</font>\n", item.BookName))
				messageBuilder.WriteString(fmt.Sprintf("- 付费面板：%s\n", item.PurchasePanelName))
				messageBuilder.WriteString(fmt.Sprintf("- 创建时间：%s\n", item.CreateTime))
				messageBuilder.WriteString(fmt.Sprintf("- 更新时间：%s\n", item.UpdateTime))
				messageBuilder.WriteString(fmt.Sprintf("- 推广链接：%s\n\n", item.AdvertiseLink))
			} else {
				// 免费模板
				messageBuilder.WriteString(fmt.Sprintf("<font color='orange'>**%s**</font>\n", item.BookName))
				messageBuilder.WriteString(fmt.Sprintf("- 创建时间：%s\n", item.CreateTime))
				messageBuilder.WriteString(fmt.Sprintf("- 更新时间：%s\n", item.UpdateTime))
				messageBuilder.WriteString(fmt.Sprintf("- 推广链接：%s\n\n", item.AdvertiseLink))
			}
		}

		// 发送飞书消息
		if messageBuilder.Len() > 0 {
			libUtils.SendFeiShuAppMsg(template.Title, messageBuilder.String(), template.ChatId)
		}
	})
	return
}

// getNotificationTemplate 获取通知模板
func (s *sDzNativeLink) getNotificationTemplate(isPaid bool) *model.NotificationTemplate {
	if isPaid {
		return &model.NotificationTemplate{
			Title:  "（付费）点众抖音端原生链接更新通知",
			ChatId: g.Cfg().MustGet(context.Background(), "fs.card.dzPaidChatId").String(),
			IsPaid: true,
		}
	} else {
		return &model.NotificationTemplate{
			Title:  "（免费）点众抖音端原生链接更新通知",
			ChatId: g.Cfg().MustGet(context.Background(), "fs.card.dzFreeChatId").String(),
			IsPaid: false,
		}
	}
}

// BatchAdd 批量更新数据库
func (s *sDzNativeLink) BatchAdd(ctx context.Context, items []*model.DzNativeLinkInfoRes, isPaid bool) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(items) == 0 {
			return
		}
		var insertData []do.DzNativeLink
		for _, item := range items {
			var typeVal = 2
			if isPaid {
				typeVal = 1
			}
			data := do.DzNativeLink{
				Id:                item.Id,
				ProjectId:         item.ProjectId,
				BookId:            item.BookId,
				BookName:          item.BookName,
				DyBookId:          item.DyBookId,
				AccountUserName:   item.AccountUserName,
				DistributorId:     item.DistributorId,
				DistributorName:   item.DistributorName,
				ChannelId:         item.ChannelId,
				PurchasePanelId:   item.PurchasePanelId,
				PurchasePanelName: item.PurchasePanelName,
				PurchasePanelJson: item.PurchasePanelJson,
				AdvertiseLink:     item.AdvertiseLink,
				TaskStatus:        item.TaskStatus,
				Response:          item.Response,
				CreateTime:        item.CreateTime,
				UpdateTime:        item.UpdateTime,
				BookStatus:        item.BookStatus,
				BookType:          item.BookType,
				Connect:           item.Connect,
				PutTimeText:       item.PutTimeText,
				Type:              typeVal,
			}
			if item.PurchasePanelJson == "" {
				data.PurchasePanelJson = "{}"
			}
			if item.Response == "" {
				data.Response = "{}"
			}
			insertData = append(insertData, data)
		}
		_, err = dao.DzNativeLink.Ctx(ctx).Save(insertData)
		liberr.ErrIsNil(ctx, err)
	})
	return
}
