// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: internal/app/ad/controller/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdAnalyzeDataController struct {
	systemController.BaseController
}

var FqAdAnalyzeData = new(fqAdAnalyzeDataController)

// List 列表
func (c *fqAdAnalyzeDataController) List(ctx context.Context, req *ad.FqAdAnalyzeDataSearchReq) (res *ad.FqAdAnalyzeDataSearchRes, err error) {
	res = new(ad.FqAdAnalyzeDataSearchRes)
	res.FqAdAnalyzeDataSearchRes, err = service.FqAdAnalyzeData().List(ctx, &req.FqAdAnalyzeDataSearchReq)
	return
}

// FqAdAnalyzeDataPull
func (c *fqAdAnalyzeDataController) FqAdAnalyzeDataPull(ctx context.Context, req *ad.FqAdAnalyzeDataPullReq) (res *ad.FqAdAnalyzeDataPullRes, err error) {
	res = new(ad.FqAdAnalyzeDataPullRes)
	res.Count, err = service.FqAdAnalyzeData().Pull(ctx)
	return
}

// Export 导出excel
func (c *fqAdAnalyzeDataController) Export(ctx context.Context, req *ad.FqAdAnalyzeDataExportReq) (res *ad.FqAdAnalyzeDataExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdAnalyzeDataListRes
		listRes  *model.FqAdAnalyzeDataSearchRes
		//表头
		tableHead = []interface{}{"番茄账号", "渠道号", "投手上级分销", "投手名", "番茄渠道ID", "推广链id", "加桌人数", "付费率", "充值人数，时间-投放当日", "累积充值", "激活人数"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.FqAdAnalyzeData().List(ctx, &req.FqAdAnalyzeDataSearchReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.FqAccount,
				v.ChannelCode,
				v.DistributorName,
				v.UserName,
				gconv.String(v.DistributorId),
				gconv.String(v.PromotionId),
				v.AddDesktopUserNum,
				v.PaidRate,
				v.PaidUserNum,
				v.RechargeAmount,
				v.Uv,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("获取回本统计-汇总数据")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取 获取回本统计-汇总数据
func (c *fqAdAnalyzeDataController) Get(ctx context.Context, req *ad.FqAdAnalyzeDataGetReq) (res *ad.FqAdAnalyzeDataGetRes, err error) {
	res = new(ad.FqAdAnalyzeDataGetRes)
	res.FqAdAnalyzeDataInfoRes, err = service.FqAdAnalyzeData().GetById(ctx, req.Id)
	return
}

// Add 添加 获取回本统计-汇总数据
func (c *fqAdAnalyzeDataController) Add(ctx context.Context, req *ad.FqAdAnalyzeDataAddReq) (res *ad.FqAdAnalyzeDataAddRes, err error) {
	err = service.FqAdAnalyzeData().Add(ctx, req.FqAdAnalyzeDataAddReq)
	return
}

// Edit 修改 获取回本统计-汇总数据
func (c *fqAdAnalyzeDataController) Edit(ctx context.Context, req *ad.FqAdAnalyzeDataEditReq) (res *ad.FqAdAnalyzeDataEditRes, err error) {
	err = service.FqAdAnalyzeData().Edit(ctx, req.FqAdAnalyzeDataEditReq)
	return
}

// Delete 删除 获取回本统计-汇总数据
func (c *fqAdAnalyzeDataController) Delete(ctx context.Context, req *ad.FqAdAnalyzeDataDeleteReq) (res *ad.FqAdAnalyzeDataDeleteRes, err error) {
	err = service.FqAdAnalyzeData().Delete(ctx, req.Ids)
	return
}
