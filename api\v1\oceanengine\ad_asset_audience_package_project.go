// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-19 10:35:18
// 生成路径: api/v1/oceanengine/ad_asset_audience_package_project.go
// 生成人：cq
// desc:资产-定向包-关联项目相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetAudiencePackageProjectSearchReq 分页请求参数
type AdAssetAudiencePackageProjectSearchReq struct {
	g.Meta `path:"/list" tags:"资产-定向包-关联项目" method:"post" summary:"资产-定向包-关联项目列表"`
	commonApi.Author
	model.AdAssetAudiencePackageProjectSearchReq
}

// AdAssetAudiencePackageProjectSearchRes 列表返回结果
type AdAssetAudiencePackageProjectSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetAudiencePackageProjectSearchRes
}

// AdAssetAudiencePackageProjectAddReq 添加操作请求参数
type AdAssetAudiencePackageProjectAddReq struct {
	g.Meta `path:"/add" tags:"资产-定向包-关联项目" method:"post" summary:"资产-定向包-关联项目添加"`
	commonApi.Author
	*model.AdAssetAudiencePackageProjectAddReq
}

// AdAssetAudiencePackageProjectAddRes 添加操作返回结果
type AdAssetAudiencePackageProjectAddRes struct {
	commonApi.EmptyRes
}

// AdAssetAudiencePackageProjectEditReq 修改操作请求参数
type AdAssetAudiencePackageProjectEditReq struct {
	g.Meta `path:"/edit" tags:"资产-定向包-关联项目" method:"put" summary:"资产-定向包-关联项目修改"`
	commonApi.Author
	*model.AdAssetAudiencePackageProjectEditReq
}

// AdAssetAudiencePackageProjectEditRes 修改操作返回结果
type AdAssetAudiencePackageProjectEditRes struct {
	commonApi.EmptyRes
}

// AdAssetAudiencePackageProjectGetReq 获取一条数据请求
type AdAssetAudiencePackageProjectGetReq struct {
	g.Meta `path:"/get" tags:"资产-定向包-关联项目" method:"get" summary:"获取资产-定向包-关联项目信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetAudiencePackageProjectGetRes 获取一条数据结果
type AdAssetAudiencePackageProjectGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetAudiencePackageProjectInfoRes
}

// AdAssetAudiencePackageProjectDeleteReq 删除数据请求
type AdAssetAudiencePackageProjectDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-定向包-关联项目" method:"post" summary:"删除资产-定向包-关联项目"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetAudiencePackageProjectDeleteRes 删除数据返回
type AdAssetAudiencePackageProjectDeleteRes struct {
	commonApi.EmptyRes
}
