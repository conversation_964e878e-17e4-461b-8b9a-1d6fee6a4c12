/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AgentAdvertiserSelectV2ApiService AgentAdvertiserSelectV2Api service
type AgentAdvertiserSelectV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *AgentAdvertiserSelectV2Request
}

type AgentAdvertiserSelectV2Request struct {
	AdvertiserId *int64
	CompanyIds   *[]int64
	Count        *int64
	Cursor       *int64
	Filtering    *models.AgentAdvertiserSelectV2Filtering
	Page         *int64
	PageSize     *int64
}

func (r *AgentAdvertiserSelectV2ApiService) SetCfg(cfg *conf.Configuration) *AgentAdvertiserSelectV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AgentAdvertiserSelectV2ApiService) AgentAdvertiserSelectV2Request(agentAdvertiserSelectV2Request AgentAdvertiserSelectV2Request) *AgentAdvertiserSelectV2ApiService {
	r.Request = &agentAdvertiserSelectV2Request
	return r
}

func (r *AgentAdvertiserSelectV2ApiService) AccessToken(accessToken string) *AgentAdvertiserSelectV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AgentAdvertiserSelectV2ApiService) Do() (data *models.AgentAdvertiserSelectV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/agent/advertiser/select/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	}
	if r.Request.CompanyIds != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "company_ids", r.Request.CompanyIds)
	}
	if r.Request.Count != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "count", r.Request.Count)
	}
	if r.Request.Cursor != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "cursor", r.Request.Cursor)
	}
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.Request.Filtering)
	}
	if r.Request.Page != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AgentAdvertiserSelectV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AgentAdvertiserSelectV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/agent/advertiser/select/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
