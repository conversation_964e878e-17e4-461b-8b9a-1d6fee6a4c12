// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-01 14:21:11
// 生成路径: api/v1/channel/m_member_ad_callback_applet_statistics.go
// 生成人：cq
// desc:小程序广告统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdCallbackAppletStatisticsSearchReq 分页请求参数
type MMemberAdCallbackAppletStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"小程序广告统计" method:"post" summary:"小程序广告统计列表"`
	commonApi.Author
	model.MMemberAdCallbackAppletStatisticsSearchReq
}

// MMemberAdCallbackAppletStatisticsSearchRes 列表返回结果
type MMemberAdCallbackAppletStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackAppletStatisticsSearchRes
}

// MMemberAdCallbackAppletStatisticsExportReq 导出请求
type MMemberAdCallbackAppletStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"小程序广告统计" method:"post" summary:"小程序广告统计导出"`
	commonApi.Author
	model.MMemberAdCallbackAppletStatisticsSearchReq
}

// MMemberAdCallbackAppletStatisticsExportRes 导出响应
type MMemberAdCallbackAppletStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackAppletStatisticsTaskReq 小程序广告回传统计请求参数
type MMemberAdCallbackAppletStatisticsTaskReq struct {
	g.Meta `path:"/task" tags:"小程序广告统计" method:"post" summary:"小程序广告统计任务"`
	commonApi.Author
	model.MMemberAdCallbackAppletStatisticsSearchReq
}

// MMemberAdCallbackAppletStatisticsTaskRes 小程序广告回传统计返回结果
type MMemberAdCallbackAppletStatisticsTaskRes struct {
	g.Meta `mime:"application/json"`
}
