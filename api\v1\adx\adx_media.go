// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: api/v1/adx/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表相关参数
// company:云南奇讯科技有限媒体
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxMediaSearchReq 分页请求参数
type AdxMediaSearchReq struct {
	g.Meta `path:"/list" tags:"ADX媒体信息表" method:"post" summary:"ADX媒体信息表列表"`
	commonApi.Author
	model.AdxMediaSearchReq
}

// AdxMediaSearchRes 列表返回结果
type AdxMediaSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxMediaSearchRes
}

// AdxMediaExportReq 导出请求
type AdxMediaExportReq struct {
	g.Meta `path:"/export" tags:"ADX媒体信息表" method:"post" summary:"ADX媒体信息表导出"`
	commonApi.Author
	model.AdxMediaSearchReq
}

// AdxMediaExportRes 导出响应
type AdxMediaExportRes struct {
	commonApi.EmptyRes
}

// AdxMediaAddReq 添加操作请求参数
type AdxMediaAddReq struct {
	g.Meta `path:"/add" tags:"ADX媒体信息表" method:"post" summary:"ADX媒体信息表添加"`
	commonApi.Author
	*model.AdxMediaAddReq
}

// AdxMediaAddRes 添加操作返回结果
type AdxMediaAddRes struct {
	commonApi.EmptyRes
}

// AdxMediaEditReq 修改操作请求参数
type AdxMediaEditReq struct {
	g.Meta `path:"/edit" tags:"ADX媒体信息表" method:"put" summary:"ADX媒体信息表修改"`
	commonApi.Author
	*model.AdxMediaEditReq
}

// AdxMediaEditRes 修改操作返回结果
type AdxMediaEditRes struct {
	commonApi.EmptyRes
}

// AdxMediaGetReq 获取一条数据请求
type AdxMediaGetReq struct {
	g.Meta `path:"/get" tags:"ADX媒体信息表" method:"get" summary:"获取ADX媒体信息表信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdxMediaGetRes 获取一条数据结果
type AdxMediaGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxMediaInfoRes
}

// AdxMediaDeleteReq 删除数据请求
type AdxMediaDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX媒体信息表" method:"post" summary:"删除ADX媒体信息表"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdxMediaDeleteRes 删除数据返回
type AdxMediaDeleteRes struct {
	commonApi.EmptyRes
}

// AdxMaterialSyncReq 同步ADX媒体请求参数
type AdxMediaSyncReq struct {
	g.Meta `path:"/sync" tags:"ADX媒体信息表" method:"post" summary:"同步ADX媒体"`
	commonApi.Author
	model.AdxMediaSearchReq
}

// AdxMediaSyncRes 同步ADX产品返回结果
type AdxMediaSyncRes struct {
	g.Meta `mime:"application/json"`
}
