// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 19:44:49
// 生成路径: api/v1/oceanengine/ad_asset_wechat_applet.go
// 生成人：cq
// desc:资产-微信小程序相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetWechatAppletSearchReq 分页请求参数
type AdAssetWechatAppletSearchReq struct {
	g.Meta `path:"/list" tags:"资产-微信小程序" method:"post" summary:"资产-微信小程序列表"`
	commonApi.Author
	model.AdAssetWechatAppletSearchReq
}

// AdAssetWechatAppletSearchRes 列表返回结果
type AdAssetWechatAppletSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetWechatAppletSearchRes
}

// AdAssetWechatAppletAddReq 添加操作请求参数
type AdAssetWechatAppletAddReq struct {
	g.Meta `path:"/add" tags:"资产-微信小程序" method:"post" summary:"资产-微信小程序添加"`
	commonApi.Author
	File       *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
	CategoryId string            `p:"categoryId" v:"required#分类id必须"`
}

// AdAssetWechatAppletAddRes 添加操作返回结果
type AdAssetWechatAppletAddRes struct {
	commonApi.EmptyRes
}

// AdAssetWechatAppletSyncReq 同步微信小程序请求参数
type AdAssetWechatAppletSyncReq struct {
	g.Meta `path:"/sync" tags:"资产-微信小程序" method:"post" summary:"资产-同步微信小程序"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds" dc:"广告主ID列表"`
}

// AdAssetWechatAppletSyncRes 同步微信小程序返回结果
type AdAssetWechatAppletSyncRes struct {
	commonApi.EmptyRes
}

// AdAssetWechatAppletChannelImportReq 渠道导入请求参数
type AdAssetWechatAppletChannelImportReq struct {
	g.Meta `path:"/channel/import" tags:"资产-微信小程序" method:"post" summary:"资产-渠道导入"`
	commonApi.Author
	*model.AdAssetChannelImportReq
}

// AdAssetWechatAppletChannelImportRes 渠道导入返回结果
type AdAssetWechatAppletChannelImportRes struct {
	commonApi.EmptyRes
}

// AdAssetWechatAppletEditReq 修改操作请求参数
type AdAssetWechatAppletEditReq struct {
	g.Meta `path:"/edit" tags:"资产-微信小程序" method:"put" summary:"资产-微信小程序修改"`
	commonApi.Author
	*model.AdAssetWechatAppletEditReq
}

// AdAssetWechatAppletEditRes 修改操作返回结果
type AdAssetWechatAppletEditRes struct {
	commonApi.EmptyRes
}

// AdAssetWechatAppletGetReq 获取一条数据请求
type AdAssetWechatAppletGetReq struct {
	g.Meta `path:"/get" tags:"资产-微信小程序" method:"get" summary:"获取资产-微信小程序信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetWechatAppletGetRes 获取一条数据结果
type AdAssetWechatAppletGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetWechatAppletInfoRes
}

// AdAssetWechatAppletDeleteReq 删除数据请求
type AdAssetWechatAppletDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-微信小程序" method:"delete" summary:"删除资产-微信小程序"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetWechatAppletDeleteRes 删除数据返回
type AdAssetWechatAppletDeleteRes struct {
	commonApi.EmptyRes
}
