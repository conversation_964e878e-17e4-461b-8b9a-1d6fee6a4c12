// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: internal/app/adx/service/adx_task.go
// 生成人：cyao
// desc:ADX任务主表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxTask interface {
	List(ctx context.Context, req *model.AdxTaskSearchReq) (res *model.AdxTaskSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxTaskSearchReq) (listRes []*model.AdxTaskInfoRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdxTaskInfoRes, err error)
	CreateByMaterial(ctx context.Context, req *model.CreateByMaterialReq) (res *model.CreateByMaterialRes, err error)
	Add(ctx context.Context, req *model.AdxTaskAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxTaskEditReq) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
	UpdateStatus(ctx context.Context, req *model.UpdateStatusReq) error
}

var localAdxTask IAdxTask

func AdxTask() IAdxTask {
	if localAdxTask == nil {
		panic("implement not found for interface IAdxTask, forgot register?")
	}
	return localAdxTask
}

func RegisterAdxTask(i IAdxTask) {
	localAdxTask = i
}
