// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: internal/app/ad/model/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// MpAdEventInfoRes is the golang structure for table mp_ad_event.
type MpAdEventInfoRes struct {
	gmeta.Meta `orm:"table:mp_ad_event"`
	Id         int64       `orm:"id,primary" json:"id" dc:"记录唯一标识"`                       // 记录唯一标识
	MpId       string      `orm:"mp_id" json:"mpId" dc:"小程序ID"`                              // 小程序ID
	Cost       string      `orm:"cost" json:"cost" dc:"广告消耗，单位为十万分之一元"`            // 广告消耗，单位为十万分之一元
	OpenId     string      `orm:"open_id" json:"openId" dc:"用户OpenID"`                        // 用户OpenID
	EventTime  string      `orm:"event_time" json:"eventTime" dc:"广告计费发生时间戳，单位秒"`   // 广告计费发生时间戳，单位秒
	AdType     string      `orm:"ad_type" json:"adType" dc:"小程序广告类型"`                    // 小程序广告类型
	CreateDate string      `orm:"create_date" json:"createDate" dc:"创建日期，按照event_time来"` // 创建日期，按照event_time来
	CreateTime *gtime.Time `orm:"create_time" json:"createTime" dc:"根据event_time来的时间"`    // 根据event_time来的时间
}

type MpAdEventListRes struct {
	Id         int64       `json:"id" dc:"记录唯一标识"`
	MpId       string      `json:"mpId" dc:"小程序ID"`
	Cost       float64     `json:"cost" dc:"广告消耗，单位为十万分之一元"`
	OpenId     string      `json:"openId" dc:"用户OpenID"`
	EventTime  string      `json:"eventTime" dc:"广告计费发生时间戳，单位秒"`
	AdType     string      `json:"adType" dc:"小程序广告类型"`
	CreateDate string      `json:"createDate" dc:"创建日期，按照event_time来"`
	CreateTime *gtime.Time `json:"createTime" dc:"根据event_time来的时间"`
}

// MpAdEventSearchReq 分页请求参数
type MpAdEventSearchReq struct {
	comModel.PageReq
	Id         string `p:"id" dc:"记录唯一标识"`                                                                                             //记录唯一标识
	MpId       string `p:"mpId" dc:"小程序ID"`                                                                                               //小程序ID
	Cost       string `p:"cost" dc:"广告消耗，单位为十万分之一元"`                                                                            //广告消耗，单位为十万分之一元
	OpenId     string `p:"openId" dc:"用户OpenID"`                                                                                           //用户OpenID
	EventTime  string `p:"eventTime" dc:"广告计费发生时间戳，单位秒"`                                                                         //广告计费发生时间戳，单位秒
	AdType     string `p:"adType" dc:"小程序广告类型"`                                                                                       //小程序广告类型
	CreateDate string `p:"createDate" dc:"创建日期，按照event_time来"`                                                                        //创建日期，按照event_time来
	CreateTime string `p:"createTime" v:"createTime@datetime#根据event_time来的时间需为YYYY-MM-DD hh:mm:ss格式" dc:"根据event_time来的时间"` //根据event_time来的时间
}

type MpAdEventPullDataReq struct {
	// 开始时间
	StartTime string `p:"startTime"   dc:"开始时间"`
	// 结束时间
	EndTime string `p:"endTime"  dc:"结束时间"`
	// appid
	AppId string `p:"appId"  dc:"appid"`
}

// MpAdEventSearchRes 列表返回结果
type MpAdEventSearchRes struct {
	comModel.ListRes
	List []*MpAdEventListRes `json:"list"`
}

// MpAdEventAddReq 添加操作请求参数
type MpAdEventAddReq struct {
	MpId       string      `p:"mpId" v:"required#小程序ID不能为空" dc:"小程序ID"`
	Cost       float64     `p:"cost" v:"required#广告消耗，单位为十万分之一元不能为空" dc:"广告消耗，单位为十万分之一元"`
	OpenId     string      `p:"openId" v:"required#用户OpenID不能为空" dc:"用户OpenID"`
	EventTime  string      `p:"eventTime" v:"required#广告计费发生时间戳，单位秒不能为空" dc:"广告计费发生时间戳，单位秒"`
	AdType     string      `p:"adType"  dc:"小程序广告类型"`
	CreateDate string      `p:"createDate" v:"required#创建日期，按照event_time来不能为空" dc:"创建日期，按照event_time来"`
	CreateTime *gtime.Time `p:"createTime" v:"required#根据event_time来的时间不能为空" dc:"根据event_time来的时间"`
}

// MpAdEventEditReq 修改操作请求参数
type MpAdEventEditReq struct {
	Id         int64       `p:"id" v:"required#主键ID不能为空" dc:"记录唯一标识"`
	MpId       string      `p:"mpId" v:"required#小程序ID不能为空" dc:"小程序ID"`
	Cost       float64     `p:"cost" v:"required#广告消耗，单位为十万分之一元不能为空" dc:"广告消耗，单位为十万分之一元"`
	OpenId     string      `p:"openId" v:"required#用户OpenID不能为空" dc:"用户OpenID"`
	EventTime  string      `p:"eventTime" v:"required#广告计费发生时间戳，单位秒不能为空" dc:"广告计费发生时间戳，单位秒"`
	AdType     string      `p:"adType"  dc:"小程序广告类型"`
	CreateDate string      `p:"createDate" v:"required#创建日期，按照event_time来不能为空" dc:"创建日期，按照event_time来"`
	CreateTime *gtime.Time `p:"createTime" v:"required#根据event_time来的时间不能为空" dc:"根据event_time来的时间"`
}
