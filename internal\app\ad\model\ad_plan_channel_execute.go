// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-27 11:19:17
// 生成路径: internal/app/ad/model/ad_plan_channel_execute.go
// 生成人：cq
// desc:广告渠道执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdPlanChannelExecuteInfoRes is the golang structure for table ad_plan_channel_execute.
type AdPlanChannelExecuteInfoRes struct {
	gmeta.Meta  `orm:"table:ad_plan_channel_execute"`
	Id          int         `orm:"id,primary" json:"id" dc:""`                     //
	PlanId      int         `orm:"plan_id" json:"planId" dc:"计划ID"`                // 计划ID
	ChannelList string      `orm:"channel_list" json:"channelList" dc:"渠道列表，用|分隔"` // 渠道列表，用|分隔
	TemplateId  int         `orm:"template_id" json:"templateId" dc:"充值模板ID"`      // 充值模板ID
	SinglePrice float64     `orm:"single_price" json:"singlePrice" dc:"单集价格"`      // 单集价格
	LockNum     int         `orm:"lock_num" json:"lockNum" dc:"付费集数"`              // 付费集数
	RewardId    int         `orm:"reward_id" json:"rewardId" dc:"激励广告配置ID"`        // 激励广告配置ID
	AdSettingId int         `orm:"ad_setting_id" json:"adSettingId" dc:"广告回传配置ID"` // 广告回传配置ID
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`          // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`          // 更新时间
}

type AdPlanChannelExecuteListRes struct {
	Id          int         `json:"id" dc:""`
	PlanId      int         `json:"planId" dc:"计划ID"`
	ChannelList string      `json:"channelList" dc:"渠道列表，用|分隔"`
	TemplateId  int         `json:"templateId" dc:"充值模板ID"`
	SinglePrice float64     `json:"singlePrice" dc:"单集价格"`
	LockNum     int         `json:"lockNum" dc:"付费集数"`
	RewardId    int         `json:"rewardId" dc:"激励广告配置ID"`
	AdSettingId int         `json:"adSettingId" dc:"广告回传配置ID"`
	CreatedAt   *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdPlanChannelExecuteSearchReq 分页请求参数
type AdPlanChannelExecuteSearchReq struct {
	comModel.PageReq
	Id          string `p:"id" dc:""`                                                               //
	PlanId      string `p:"planId" v:"planId@integer#计划ID需为整数" dc:"计划ID"`                           //计划ID
	ChannelList string `p:"channelList" dc:"渠道列表，用|分隔"`                                             //渠道列表，用|分隔
	TemplateId  string `p:"templateId" v:"templateId@integer#充值模板ID需为整数" dc:"充值模板ID"`               //充值模板ID
	SinglePrice string `p:"singlePrice" v:"singlePrice@float#单集价格需为浮点数" dc:"单集价格"`                  //单集价格
	LockNum     string `p:"lockNum" v:"lockNum@integer#付费集数需为整数" dc:"付费集数"`                         //付费集数
	RewardId    string `p:"rewardId" v:"rewardId@integer#激励广告配置ID需为整数" dc:"激励广告配置ID"`               //激励广告配置ID
	AdSettingId string `p:"adSettingId" v:"adSettingId@integer#广告回传配置ID需为整数" dc:"广告回传配置ID"`         //广告回传配置ID
	CreatedAt   string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// AdPlanChannelExecuteSearchRes 列表返回结果
type AdPlanChannelExecuteSearchRes struct {
	comModel.ListRes
	List []*AdPlanChannelExecuteListRes `json:"list"`
}

// AdPlanChannelExecuteAddReq 添加操作请求参数
type AdPlanChannelExecuteAddReq struct {
	PlanId      int     `p:"planId"  dc:"计划ID"`
	ChannelList string  `p:"channelList"  dc:"渠道列表，用|分隔"`
	TemplateId  int     `p:"templateId"  dc:"充值模板ID"`
	SinglePrice float64 `p:"singlePrice"  dc:"单集价格"`
	LockNum     int     `p:"lockNum"  dc:"付费集数"`
	RewardId    int     `p:"rewardId"  dc:"激励广告配置ID"`
	AdSettingId int     `p:"adSettingId"  dc:"广告回传配置ID"`
}

// AdPlanChannelExecuteEditReq 修改操作请求参数
type AdPlanChannelExecuteEditReq struct {
	Id          int     `p:"id" v:"required#主键ID不能为空" dc:""`
	PlanId      int     `p:"planId"  dc:"计划ID"`
	ChannelList string  `p:"channelList"  dc:"渠道列表，用|分隔"`
	TemplateId  int     `p:"templateId"  dc:"充值模板ID"`
	SinglePrice float64 `p:"singlePrice"  dc:"单集价格"`
	LockNum     int     `p:"lockNum"  dc:"付费集数"`
	RewardId    int     `p:"rewardId"  dc:"激励广告配置ID"`
	AdSettingId int     `p:"adSettingId"  dc:"广告回传配置ID"`
}
