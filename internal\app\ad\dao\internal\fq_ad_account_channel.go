// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: internal/app/ad/dao/internal/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdAccountChannelDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdAccountChannelDao struct {
	table   string                    // Table is the underlying table name of the DAO.
	group   string                    // Group is the database configuration group name of current DAO.
	columns FqAdAccountChannelColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdAccountChannelColumns defines and stores column names for table fq_ad_account_channel.
type FqAdAccountChannelColumns struct {
	ChannelDistributorId string // 渠道ID
	DistributorId        string // 父级渠道ID
	NickName             string // 渠道名称
	AppId                string // 小程序ID
	AppName              string // 小程序名称
	AppType              string // 业务类型
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
	DeletedAt            string // 删除时间
}

var fqAdAccountChannelColumns = FqAdAccountChannelColumns{
	ChannelDistributorId: "channel_distributor_id",
	DistributorId:        "distributor_id",
	NickName:             "nick_name",
	AppId:                "app_id",
	AppName:              "app_name",
	AppType:              "app_type",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	DeletedAt:            "deleted_at",
}

// NewFqAdAccountChannelDao creates and returns a new DAO object for table data access.
func NewFqAdAccountChannelDao() *FqAdAccountChannelDao {
	return &FqAdAccountChannelDao{
		group:   "default",
		table:   "fq_ad_account_channel",
		columns: fqAdAccountChannelColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdAccountChannelDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdAccountChannelDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdAccountChannelDao) Columns() FqAdAccountChannelColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdAccountChannelDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdAccountChannelDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdAccountChannelDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
