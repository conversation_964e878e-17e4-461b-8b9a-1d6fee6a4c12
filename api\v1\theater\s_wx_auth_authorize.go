// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-02 16:43:32
// 生成路径: api/v1/theater/s_wx_auth_authorize.go
// 生成人：cq
// desc:微信短剧授权表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SWxAuthAuthorizeSearchReq 分页请求参数
type SWxAuthAuthorizeSearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广" method:"get" summary:"获取微信短剧授权列表"`
	commonApi.Author
	model.SWxAuthAuthorizeSearchReq
}

// SWxAuthAuthorizeSearchRes 列表返回结果
type SWxAuthAuthorizeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SWxAuthAuthorizeSearchRes
}

// SWxAuthAuthorizeAddReq 添加操作请求参数
type SWxAuthAuthorizeAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广" method:"post" summary:"添加微信短剧授权"`
	commonApi.Author
	*model.SWxAuthAuthorizeAddReq
}

// SWxAuthAuthorizeAddRes 添加操作返回结果
type SWxAuthAuthorizeAddRes struct {
	commonApi.EmptyRes
}

// SWxAuthAuthorizeDeleteReq 删除数据请求
type SWxAuthAuthorizeDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧推广" method:"post" summary:"删除微信短剧授权"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SWxAuthAuthorizeDeleteRes 删除数据返回
type SWxAuthAuthorizeDeleteRes struct {
	commonApi.EmptyRes
}
