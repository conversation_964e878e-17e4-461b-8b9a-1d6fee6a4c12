// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: internal/app/ad/model/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdPlanExecuteInfoRes is the golang structure for table ad_plan_execute.
type AdPlanExecuteInfoRes struct {
	gmeta.Meta     `orm:"table:ad_plan_execute"`
	Id             int         `orm:"id,primary" json:"id" dc:"ID"`                                       // ID
	PlanId         int         `orm:"plan_id" json:"planId" dc:"计划ID"`                                    // 计划ID
	ExecuteType    int         `orm:"execute_type" json:"executeType" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"` // 执行类型，1 更新预算，2 更新出价（广告），3 暂停计划
	AdjustmentType int         `orm:"adjustment_type" json:"adjustmentType" dc:"调整方式，1 调整至目标值，2 增加，3 减少"` // 调整方式，1 调整至目标值，2 增加，3 减少
	ExecuteValue   int         `orm:"execute_value" json:"executeValue" dc:"调整的目标值，增加或者减少的值"`             // 调整的目标值，增加或者减少的值
	ValueType      int         `orm:"value_type" json:"valueType"`                                        // 1 元 2 %
	LimitValue     int         `orm:"limit_value" json:"limitValue" dc:"最低/最高调整至"`                        // 最低/最高调整至
	TimeDimension  int         `orm:"time_dimension" json:"timeDimension" dc:"时间维度，1 天"`                  // 时间维度，1 天
	ExecuteTimes   int         `orm:"execute_times" json:"executeTimes" dc:"执行次数，1、2、3、4 等"`              // 执行次数，1、2、3、4 等
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                              // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                              // 更新时间
}

type AdPlanExecuteListRes struct {
	Id             int         `json:"id" dc:"ID"`
	PlanId         int         `json:"planId" dc:"计划ID"`
	ExecuteType    int         `json:"executeType" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"`
	AdjustmentType int         `json:"adjustmentType" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`
	ValueType      int         `orm:"value_type" json:"valueType"` // 1 元 2 %
	ExecuteValue   int         `json:"executeValue" dc:"调整的目标值，增加或者减少的值"`
	LimitValue     int         `json:"limitValue" dc:"最低/最高调整至"`
	TimeDimension  int         `json:"timeDimension" dc:"时间维度，1 天"`
	ExecuteTimes   int         `json:"executeTimes" dc:"执行次数，1、2、3、4 等"`
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdPlanExecuteSearchReq 分页请求参数
type AdPlanExecuteSearchReq struct {
	comModel.PageReq
	Id             string `p:"id" dc:"ID"`                                                                                               //ID
	PlanId         string `p:"planId" v:"planId@integer#计划ID需为整数" dc:"计划ID"`                                                             //计划ID
	ExecuteType    string `p:"executeType" v:"executeType@integer#执行类型，1 更新预算，2 更新出价（广告），3 暂停计划需为整数" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"` //执行类型，1 更新预算，2 更新出价（广告），3 暂停计划
	AdjustmentType string `p:"adjustmentType" v:"adjustmentType@integer#调整方式，1 调整至目标值，2 增加，3 减少需为整数" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`       //调整方式，1 调整至目标值，2 增加，3 减少
	ExecuteValue   string `p:"executeValue" v:"executeValue@integer#调整的目标值，增加或者减少的值需为整数" dc:"调整的目标值，增加或者减少的值"`                           //调整的目标值，增加或者减少的值
	LimitValue     string `p:"limitValue" v:"limitValue@integer#最低/最高调整至需为整数" dc:"最低/最高调整至"`                                             //最低/最高调整至
	TimeDimension  string `p:"timeDimension" v:"timeDimension@integer#时间维度，1 天需为整数" dc:"时间维度，1 天"`                                       //时间维度，1 天
	ExecuteTimes   string `p:"executeTimes" v:"executeTimes@integer#执行次数，1、2、3、4 等需为整数" dc:"执行次数，1、2、3、4 等"`                             //执行次数，1、2、3、4 等
	CreatedAt      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                   //创建时间
}

// AdPlanExecuteSearchRes 列表返回结果
type AdPlanExecuteSearchRes struct {
	comModel.ListRes
	List []*AdPlanExecuteListRes `json:"list"`
}

// AdPlanExecuteAddReq 添加操作请求参数
type AdPlanExecuteAddReq struct {
	PlanId         int `p:"planId" dc:"计划ID"`
	ExecuteType    int `p:"executeType" v:"required#执行类型，1 更新预算，2 更新出价（广告），3 暂停计划不能为空" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"`
	ValueType      int `orm:"value_type" p:"valueType"` // 1 元 2 %
	AdjustmentType int `p:"adjustmentType" v:"required#调整方式，1 调整至目标值，2 增加，3 减少不能为空" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`
	ExecuteValue   int `p:"executeValue"  dc:"调整的目标值，增加或者减少的值"`
	LimitValue     int `p:"limitValue"  dc:"最低/最高调整至"`
	TimeDimension  int `p:"timeDimension" v:"required#时间维度，1 天不能为空" dc:"时间维度，1 天"`
	ExecuteTimes   int `p:"executeTimes"  dc:"执行次数，1、2、3、4 等"`
}

// AdPlanExecuteEditReq 修改操作请求参数
type AdPlanExecuteEditReq struct {
	Id             int `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	PlanId         int `p:"planId" v:"required#计划ID不能为空" dc:"计划ID"`
	ExecuteType    int `p:"executeType" v:"required#执行类型，1 更新预算，2 更新出价（广告），3 暂停计划不能为空" dc:"执行类型，1 更新预算，2 更新出价（广告），3 暂停计划"`
	AdjustmentType int `p:"adjustmentType" v:"required#调整方式，1 调整至目标值，2 增加，3 减少不能为空" dc:"调整方式，1 调整至目标值，2 增加，3 减少"`
	ValueType      int `p:"valueType"` // 1 元 2 %
	ExecuteValue   int `p:"executeValue"  dc:"调整的目标值，增加或者减少的值"`
	LimitValue     int `p:"limitValue"  dc:"最低/最高调整至"`
	TimeDimension  int `p:"timeDimension" v:"required#时间维度，1 天不能为空" dc:"时间维度，1 天"`
	ExecuteTimes   int `p:"executeTimes"  dc:"执行次数，1、2、3、4 等"`
}
