// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: internal/app/ad/logic/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model"
	channelEntity "github.com/tiger1103/gfast/v3/internal/app/channel/model/entity"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	orderModel "github.com/tiger1103/gfast/v3/internal/app/order/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterModel "github.com/tiger1103/gfast/v3/internal/app/theater/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/fq/api"
	fqModel "github.com/tiger1103/gfast/v3/library/advertiser/fq/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
	"time"
)

func init() {
	service.RegisterFqAdUserPaymentRecord(New())
}

func New() service.IFqAdUserPaymentRecord {
	return &sFqAdUserPaymentRecord{}
}

type sFqAdUserPaymentRecord struct{}

func GetType(userAgent string) string {
	if len(userAgent) == 0 {
		return ""
	}
	if strings.Contains(userAgent, "iPhone") || strings.Contains(userAgent, "iPad") {
		return "IOS"
	} else if strings.Contains(userAgent, "Android") {
		return "Android"
	} else {
		return ""
	}
}
func (s *sFqAdUserPaymentRecord) List(ctx context.Context, req *model.FqAdUserPaymentRecordSearchReq) (listRes *model.FqAdUserPaymentRecordSearchRes, err error) {
	listRes = new(model.FqAdUserPaymentRecordSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).WithAll().As("fqdata")
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " fqdata.distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " fqdata.distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if req.TradeNo != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().TradeNo+" = ?", req.TradeNo)
		}
		//if req.FqDistributorId != "" {
		//	m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().DistributorId+" = ?", req.FqDistributorId)
		//}
		//if len(req.FqDistributorIds) > 0 {
		//	m = m.WhereIn("fqdata."+dao.FqAdUserPaymentRecord.Columns().DistributorId, req.FqDistributorId)
		//}

		if req.FqDistributorId != "" {
			list, _ := service.FqAdAccountChannel().GetByDistributorId(ctx, gconv.Int64(req.FqDistributorId))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdUserPaymentRecord.Columns().DistributorId, ids)
				}
			}
		}
		if len(req.FqDistributorIds) > 0 {
			list, _ := service.FqAdAccountChannel().GetByDistributorIds(ctx, gconv.SliceInt64(req.FqChannelDistributorIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdUserPaymentRecord.Columns().DistributorId, ids)
				}
			}

		}

		if len(req.FqChannelDistributorId) > 0 {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().DistributorId+" = ?", gconv.Int64(req.FqChannelDistributorId))
		}
		if len(req.FqChannelDistributorIds) > 0 {
			m = m.WhereIn("fqdata."+dao.FqAdUserPaymentRecord.Columns().DistributorId, req.FqChannelDistributorIds)
		}

		if req.AppId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().AppId+" = ?", req.AppId)
		}
		if req.AppName != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().AppName+" like ?", "%"+req.AppName+"%")
		}
		if req.PromotionId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OutTradeNo != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().OutTradeNo+" = ?", req.OutTradeNo)
		}
		if req.DeviceId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().DeviceId+" = ?", req.DeviceId)
		}
		if req.OpenId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.WxOaOpenId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxOaOpenId+" = ?", req.WxOaOpenId)
		}
		if req.WxOaName != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxOaName+" like ?", "%"+req.WxOaName+"%")
		}
		if req.PayAmount != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().PayAmount+" = ?", gconv.Int64(req.PayAmount))
		}
		if req.Ip != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().Ip+" = ?", req.Ip)
		}
		if req.UserAgent != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().UserAgent+" = ?", req.UserAgent)
		}
		if req.Oaid != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().Oaid+" = ?", req.Oaid)
		}
		if req.AndroidId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().AndroidId+" = ?", req.AndroidId)
		}

		if req.RegisterStartTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().RegisterTime+" >= ?", libUtils.GetSecByDateTime(req.RegisterStartTime))
		}

		if req.RegisterEndTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().RegisterTime+" <= ?", libUtils.GetSecByDateTime(req.RegisterEndTime))
		}

		if req.WxPlatformAppKey != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxPlatformAppKey+" = ?", req.WxPlatformAppKey)
		}
		if req.BookId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().BookId+" = ?", req.BookId)
		}
		if req.BookName != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.BookGender != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().BookGender+" = ?", req.BookGender)
		}
		if req.BookCategory != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().BookCategory+" = ?", req.BookCategory)
		}
		if req.Activity != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().Activity+" = ?", req.Activity)
		}
		if req.RecentReadBookId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().RecentReadBookId+" = ?", req.RecentReadBookId)
		}
		if req.ExternalId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().ExternalId+" = ?", req.ExternalId)
		}
		if req.OrderType != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().OrderType+" = ?", gconv.Int64(req.OrderType))
		}
		if req.AdvertiserId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdgroupId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().AdgroupId+" = ?", req.AdgroupId)
		}
		if req.AdId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().AdId+" = ?", req.AdId)
		}
		if req.UnionId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().UnionId+" = ?", req.UnionId)
		}
		if req.WxVideoId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxVideoId+" = ?", req.WxVideoId)
		}
		if req.WxVcSourceType != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxVcSourceType+" = ?", gconv.Int(req.WxVcSourceType))
		}
		if req.WxPromotionId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxPromotionId+" = ?", req.WxPromotionId)
		}
		if req.WxSourceType != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxSourceType+" = ?", req.WxSourceType)
		}
		if req.WxVideoChannelId != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().WxVideoChannelId+" = ?", req.WxVideoChannelId)
		}
		if req.Status != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.PayWay != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().PayWay+" = ?", gconv.Int(req.PayWay))
		}
		if req.PayStartTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().PayTimestamp+" >= ?", libUtils.GetSecByDateTime(req.PayStartTime))
		}
		if req.PayEndTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().PayTimestamp+" <= ?", libUtils.GetSecByDateTime(req.PayEndTime))
		}

		if req.StartTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().CreateTime+" >= ?", gconv.Time(req.StartTime))
		}
		if req.EndTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserPaymentRecord.Columns().CreateTime+" < ?", gconv.Time(libUtils.StringTimeAddDay(req.EndTime, 1)))
		}
		//if req.CreateTime != "" {
		//	m = m.Where(dao.FqAdUserPaymentRecord.Columns().CreateTime+" = ?", req.CreateTime)
		//}

		if len(req.ChannelCode) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code = "+fmt.Sprintf("'%s'", req.ChannelCode))
		} else if len(req.ChannelCodes) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code in "+fmt.Sprintf("(%s)", libUtils.BuildSqlInStr(req.ChannelCodes)))
		} else {
			m = m.LeftJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id ")
		}
		if req.PitcherId > 0 {
			m = m.InnerJoin(systemDao.SysUser.Table(), "su", fmt.Sprintf("su.id = sc.user_id and su.id = %v", req.PitcherId))
		} else {
			m = m.LeftJoin(systemDao.SysUser.Table(), "su", "su.id = sc.user_id")
		}
		if req.DistributorId > 0 {
			//if len(req.DeptIds) > 0 {
			//	m = m.LeftJoin("sys_dept as de", fmt.Sprintf("de.dept_id = su.dept_id and de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds))).
			//		InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
			//} else {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
			//}

		} else {
			//if len(req.DeptIds) > 0 {
			//	m = m.LeftJoin("sys_dept as de", fmt.Sprintf("de.dept_id = su.dept_id and de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds))).
			//		LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
			//} else {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
			//}
		}
		if len(req.DeptIds) > 0 {
			m = m.Where(fmt.Sprintf("de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds)))
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "trade_no asc"
		if req.OrderBy != "" {
			order = "fqdata." + libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
		}
		var summary = new(model.FqAdUserPaymentRecordSummary)
		err = m.FieldSum("fqdata."+dao.FqAdUserPaymentRecord.Columns().PayAmount, "payAmount").Where("fqdata.status", 0).
			Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取统计数据失败")
		listRes.Summary = *summary
		listRes.Summary.PayAmount = float64(summary.PayAmount) / 100
		listRes.Summary.PayAmountCount = gconv.Int64(listRes.Total)

		var res []*model.FqAdUserPaymentRecordListRes
		err = m.Fields("fqdata.*, su.user_name as userName , su.id as userId ,de.leader as distributorName  , fq_promotion_id, channel_code").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		disIds := make([]int64, 0)
		listRes.List = make([]*model.FqAdUserPaymentRecordListRes, len(res))
		for k, v := range res {
			disIds = append(disIds, gconv.Int64(v.DistributorId))
			payTime := v.PayTimestamp
			if strings.Contains(v.PayTimestamp, "1970-01-01") {
				payTime = ""
			}
			addItem := &model.FqAdUserPaymentRecordListRes{
				FqChannelDistributorId: v.DistributorId,
				CreateTime:             v.CreateTime,
				CreateDate:             libUtils.GetDateString(v.CreateTime),
				TradeNo:                v.TradeNo,
				OutTradeNo:             v.OutTradeNo,
				PayWay:                 v.PayWay,
				PayAmount:              float64(v.PayAmount) / 100,
				Status:                 v.Status,
				PayTimestamp:           payTime,
				OrderType:              v.OrderType,
				Activity:               v.Activity,
				BookId:                 v.BookId,
				BookName:               v.BookName,
				BookGender:             v.BookGender,
				BookCategory:           v.BookCategory,
				UserId:                 v.UserId,
				DistributorName:        v.DistributorName,
				FqAccount:              v.FqAccount,
				UserName:               v.UserName,
				ChannelCode:            v.ChannelCode,
				RegisterTime:           libUtils.GetDateBySec2(gconv.Int64(v.RegisterTime)),
				DeviceId:               v.DeviceId,
				OpenId:                 v.OpenId,
				Ip:                     v.Ip,
				WxOaOpenId:             v.WxOaOpenId,
				WxOaName:               v.WxOaName,
				AppId:                  v.AppId,
				AppName:                v.AppName,
				PromotionId:            v.PromotionId,
				UserAgent:              GetType(v.UserAgent),
				Oaid:                   v.Oaid,
				AndroidId:              v.AndroidId,
				WxPlatformAppKey:       v.WxPlatformAppKey,
				RecentReadBookId:       v.RecentReadBookId,
				ExternalId:             v.ExternalId,
				AdvertiserId:           v.AdvertiserId,
				AdgroupId:              v.AdgroupId,
				AdId:                   v.AdId,
				UnionId:                v.UnionId,
				WxVideoId:              v.WxVideoId,
				WxVcSourceType:         v.WxVcSourceType,
				WxPromotionId:          v.WxPromotionId,
				WxSourceType:           v.WxSourceType,
				WxVideoChannelId:       v.WxVideoChannelId,
			}
			if addItem.CreateTime == "" {
				addItem.CreateTime = libUtils.GetDateBySec2(gconv.Int64(v.PayTimestamp))
			}
			listRes.List[k] = addItem
		}
		// 根据分销id获取分销信息
		//var distributorInfo = make([]model.FqAdAccountInfoRes, 0)
		//err = dao.FqAdAccount.Ctx(ctx).WhereIn("distributor_id", disIds).Scan(&distributorInfo)
		//liberr.ErrIsNil(ctx, err, "获取信息失败")
		//if len(distributorInfo) > 0 {
		//	for _, infoRes := range distributorInfo {
		//		for i, dataListRes := range listRes.List {
		//			if gconv.Int64(dataListRes.DistributorId) == infoRes.DistributorId {
		//				listRes.List[i].FqAccount = infoRes.AccountName
		//			}
		//		}
		//	}
		//}
		fqAdAccountChannels, _ := service.FqAdAccountChannel().GetChannelList(ctx, &model.FqAdAccountChannelSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: 10000,
			},
		})
		var distributorInfo = make([]model.FqAdAccountInfoRes, 0)
		channelList, _ := service.FqAdAccountChannel().GetByChannelDistributorIds(ctx, disIds)
		disIds = make([]int64, 0)
		for _, item := range channelList {
			disIds = append(disIds, item.DistributorId)
		}
		err = dao.FqAdAccount.Ctx(ctx).WhereIn("distributor_id", disIds).Scan(&distributorInfo)

		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if len(channelList) > 0 {
			for i, dataListRes := range listRes.List {
				// 获取书本信息
				if len(dataListRes.BookName) == 0 && gconv.Int64(dataListRes.BookId) > 0 {
					secretKey := ""
					for _, channel := range fqAdAccountChannels {
						if gconv.String(channel.ChannelDistributorId) == dataListRes.FqChannelDistributorId {
							secretKey = channel.SecretKey
							break
						}
					}
					bookInfo, _ := channelService.SChannel().GetFqBookInfo(ctx, &channelModel.GetFqBookInfoReq{
						BookId:        gconv.String(dataListRes.BookId),
						DistributorId: dataListRes.FqChannelDistributorId,
						SecretKey:     secretKey,
					})
					if bookInfo != nil {
						dataListRes.BookName = bookInfo.BookName
					}
				}

				for _, infoRes := range channelList {
					if dataListRes != nil && infoRes != nil && infoRes.DistributorId > 0 && infoRes.ChannelDistributorId > 0 {
						if gconv.Int64(dataListRes.FqChannelDistributorId) == infoRes.ChannelDistributorId {
							listRes.List[i].FqChannelDistributorId = gconv.String(infoRes.ChannelDistributorId)
							listRes.List[i].FqChannel = infoRes.NickName
							listRes.List[i].AppId = gconv.String(infoRes.AppId)
							listRes.List[i].AppName = infoRes.AppName
							listRes.List[i].DistributorId = gconv.String(infoRes.DistributorId)
							for _, item := range distributorInfo {
								if item.DistributorId == infoRes.DistributorId {
									listRes.List[i].FqAccount = item.AccountName
								}
							}
						}
					}
				}
			}
		}

	})
	return
}

// Pull
func (s *sFqAdUserPaymentRecord) Pull(ctx context.Context, statDate string) (count int, err error) {
	count = 0
	err = g.Try(ctx, func(ctx context.Context) {
		//var fqAccount = make([]model.FqAdAccountInfoRes, 0)
		//err = dao.FqAdAccount.Ctx(ctx).WithAll().Scan(&fqAccount)
		//liberr.ErrIsNil(ctx, err, "获取信息失败")
		startTimestamps, endTimestamps, _ := libUtils.GetDayTimestamps(statDate)
		startTimestamps, endTimestamps = startTimestamps/1000, endTimestamps/1000
		var pageNo = 1
		var pageSize = 100
		for {
			fqAdAccountChannels, _ := service.FqAdAccountChannel().GetChannelList(ctx, &model.FqAdAccountChannelSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})
			if len(fqAdAccountChannels) == 0 {
				break
			}
			for _, accountInfoRes := range fqAdAccountChannels {

				nextOffSet := int64(0)
				for {
					var fqAdUserPaymentRecordAdds = make([]*model.FqAdUserPaymentRecordAddReq, 0)
					rechargeRes, err := api.GetAdFQIClient().GetUserRechargeService.SetReq(fqModel.GetUserRechargeReq{
						Limit:  500,
						Offset: nextOffSet,
						Begin:  startTimestamps, End: endTimestamps,
					}).SetBase(api.FqBaseReq{
						DistributorID: accountInfoRes.ChannelDistributorId,
						Secret:        accountInfoRes.SecretKey,
					}).Do()
					if err != nil {
						g.Log().Error(ctx, fmt.Sprintf("------------- GetAdFQIClient GetUserRechargeService err：%v --------------------", err))
					}
					// 数据存储
					if len(rechargeRes.Result) > 0 {
						orderIds := make([]string, 0)
						alist := make([]*model.FqAdUserPaymentRecordAddReq, 0)
						for _, result := range rechargeRes.Result {
							orderIds = append(orderIds, fmt.Sprintf("%v", result.TradeNo))
						}
						_ = dao.FqAdUserPaymentRecord.Ctx(ctx).WhereIn("trade_no", orderIds).Scan(&alist)
						for _, result := range rechargeRes.Result {
							count++
							var have = false
							for _, item := range alist {
								if item.TradeNo == gconv.String(result.TradeNo) {
									have = true
									item.TradeNo = gconv.String(result.TradeNo)
									item.DistributorId = gconv.String(accountInfoRes.ChannelDistributorId)
									item.PromotionId = gconv.String(result.PromotionId)
									item.OutTradeNo = result.OutTradeNo
									item.DeviceId = result.DeviceId
									item.OpenId = result.OpenId
									item.PayAmount = result.PayFee
									item.RegisterTime = result.RegisterTime
									item.BookId = gconv.String(result.BookId)
									info, _ := channelService.SChannel().GetFqBookInfo(ctx, &channelModel.GetFqBookInfoReq{
										BookId:        item.BookId,
										DistributorId: gconv.String(accountInfoRes.ChannelDistributorId),
										SecretKey:     accountInfoRes.SecretKey,
									})
									//if info != nil {
									//	if len(info.BookName) > 0 {
									//		item.BookName = info.BookName
									//	}
									//}
									if info != nil {
										if info.GetBookMetaResult != nil && len(info.BookName) > 0 {
											item.BookName = info.BookName
										}
									}
									item.Activity = gconv.String(result.IsActivity)
									item.ExternalId = result.ExternalId
									item.Status = result.Status
									item.PayWay = result.PayWay
									item.CreateTime = result.CreateTime
									item.PayTimestamp = result.PayTime
								}
								fqAdUserPaymentRecordAdds = append(fqAdUserPaymentRecordAdds, item)
								break
							}
							if !have {
								info, _ := channelService.SChannel().GetFqBookInfo(ctx, &channelModel.GetFqBookInfoReq{
									BookId:        gconv.String(result.BookId),
									DistributorId: gconv.String(accountInfoRes.ChannelDistributorId),
									SecretKey:     accountInfoRes.SecretKey,
								})
								bookName := ""
								if info != nil {
									if info.GetBookMetaResult != nil && len(info.BookName) > 0 {
										bookName = info.BookName
									}
								}

								fqAdUserPaymentRecordAdds = append(fqAdUserPaymentRecordAdds, &model.FqAdUserPaymentRecordAddReq{
									TradeNo:       gconv.String(result.TradeNo),
									DistributorId: gconv.String(accountInfoRes.ChannelDistributorId),
									PromotionId:   gconv.String(result.PromotionId),
									OutTradeNo:    result.OutTradeNo,
									DeviceId:      result.DeviceId,
									OpenId:        result.OpenId,
									PayAmount:     result.PayFee,
									RegisterTime:  result.RegisterTime,
									BookId:        gconv.String(result.BookId),
									BookName:      bookName,
									Activity:      gconv.String(result.IsActivity),
									ExternalId:    result.ExternalId,
									Status:        result.Status,
									PayWay:        result.PayWay,
									CreateTime:    result.CreateTime,
									PayTimestamp:  result.PayTime,
									//OrderType:        result.OrderType,
									//AdvertiserId:     result.AdvertiserId,
									//AdgroupId:        result.AdgroupId,
									//AdId:             result.AdId,
									//UnionId:          result.UnionId,
									//WxVideoId:        result.WxVideoId,
									//WxVcSourceType:   result.WxVcSourceType,
									//WxSourceType:     result.WxSourceType,
									//WxVideoChannelId: result.WxVideoChannelId,
									//AppId:            result.AppId,
									//AppName:          result.AppName,
									//WxOaOpenId:       result.WxOaOpenId,
									//WxOaName:         result.WxOaName,
									//Ip:               result.Ip,
									//UserAgent:        result.UserAgent,
									//Oaid:             result.Oaid,
									//AndroidId:        result.AndroidId,
									//WxPlatformAppKey: result.WxPlatformAppKey,
									//BookName:         result.BookName,
									//BookGender:       result.BookGender,
									//BookCategory:     result.BookCategory,
									//RecentReadBookId: result.RecentReadBookId,
									//PayTimestamp:     result.PayTimestamp,
								})
							}
						}
						err = s.BatchAdd(ctx, fqAdUserPaymentRecordAdds)
						liberr.ErrIsNil(ctx, err, "批量添加失败")
					}

					if !rechargeRes.HasMore {
						break
					}
					nextOffSet = rechargeRes.NextOffset
				}

				if nextOffSet >= 10000 {
					// 按照半天来统计
					nextOffSet = 0
					c := 0
					c, err = s.SaveDataByTimeStamp(ctx, startTimestamps, startTimestamps+(endTimestamps-startTimestamps)/2, accountInfoRes)
					count += c
					c, err = s.SaveDataByTimeStamp(ctx, startTimestamps+(endTimestamps-startTimestamps)/2, endTimestamps, accountInfoRes)
					count += c
				}
			}
			pageNo++
		}

	})
	return
}

func (s *sFqAdUserPaymentRecord) SaveDataByTimeStamp(ctx context.Context, startTimestamps int64, endTimestamps int64, accountInfoRes *model.FqAdAccountChannelRes) (count int, err error) {
	var nextOffSet = int64(0)
	count = 0
	err = g.Try(ctx, func(ctx context.Context) {
		for {
			var fqAdUserPaymentRecordAdds = make([]*model.FqAdUserPaymentRecordAddReq, 0)
			rechargeRes, err := api.GetAdFQIClient().GetUserRechargeService.SetReq(fqModel.GetUserRechargeReq{
				Limit:  500,
				Offset: nextOffSet,
				Begin:  startTimestamps, End: endTimestamps,
			}).SetBase(api.FqBaseReq{
				DistributorID: accountInfoRes.ChannelDistributorId,
				Secret:        accountInfoRes.SecretKey,
			}).Do()
			if err != nil {
				g.Log().Error(ctx, fmt.Sprintf("------------- GetAdFQIClient GetUserRechargeService err：%v --------------------", err))
			}
			// 数据存储
			if len(rechargeRes.Result) > 0 {
				orderIds := make([]string, 0)
				alist := make([]*model.FqAdUserPaymentRecordAddReq, 0)
				for _, result := range rechargeRes.Result {
					orderIds = append(orderIds, fmt.Sprintf("%v", result.TradeNo))
				}
				_ = dao.FqAdUserPaymentRecord.Ctx(ctx).WhereIn("trade_no", orderIds).Scan(&alist)
				for _, result := range rechargeRes.Result {
					count++
					var have = false
					for _, item := range alist {
						if item.TradeNo == gconv.String(result.TradeNo) {
							have = true
							item.TradeNo = gconv.String(result.TradeNo)
							item.DistributorId = gconv.String(accountInfoRes.ChannelDistributorId)
							item.PromotionId = gconv.String(result.PromotionId)
							item.OutTradeNo = result.OutTradeNo
							item.DeviceId = result.DeviceId
							item.OpenId = result.OpenId
							item.PayAmount = result.PayFee
							item.RegisterTime = result.RegisterTime
							item.BookId = gconv.String(result.BookId)
							item.Activity = gconv.String(result.IsActivity)
							item.ExternalId = result.ExternalId
							item.Status = result.Status
							item.PayWay = result.PayWay
							item.CreateTime = result.CreateTime
							item.PayTimestamp = result.PayTime
						}
						fqAdUserPaymentRecordAdds = append(fqAdUserPaymentRecordAdds, item)
						break
					}
					if !have {
						fqAdUserPaymentRecordAdds = append(fqAdUserPaymentRecordAdds, &model.FqAdUserPaymentRecordAddReq{
							TradeNo:       gconv.String(result.TradeNo),
							DistributorId: gconv.String(accountInfoRes.ChannelDistributorId),
							PromotionId:   gconv.String(result.PromotionId),
							OutTradeNo:    result.OutTradeNo,
							DeviceId:      result.DeviceId,
							OpenId:        result.OpenId,
							PayAmount:     result.PayFee,
							RegisterTime:  result.RegisterTime,
							BookId:        gconv.String(result.BookId),
							Activity:      gconv.String(result.IsActivity),
							ExternalId:    result.ExternalId,
							Status:        result.Status,
							PayWay:        result.PayWay,
							CreateTime:    result.CreateTime,
							PayTimestamp:  result.PayTime,
							//OrderType:        result.OrderType,
							//AdvertiserId:     result.AdvertiserId,
							//AdgroupId:        result.AdgroupId,
							//AdId:             result.AdId,
							//UnionId:          result.UnionId,
							//WxVideoId:        result.WxVideoId,
							//WxVcSourceType:   result.WxVcSourceType,
							//WxSourceType:     result.WxSourceType,
							//WxVideoChannelId: result.WxVideoChannelId,
							//AppId:            result.AppId,
							//AppName:          result.AppName,
							//WxOaOpenId:       result.WxOaOpenId,
							//WxOaName:         result.WxOaName,
							//Ip:               result.Ip,
							//UserAgent:        result.UserAgent,
							//Oaid:             result.Oaid,
							//AndroidId:        result.AndroidId,
							//WxPlatformAppKey: result.WxPlatformAppKey,
							//BookName:         result.BookName,
							//BookGender:       result.BookGender,
							//BookCategory:     result.BookCategory,
							//RecentReadBookId: result.RecentReadBookId,
							//PayTimestamp:     result.PayTimestamp,
						})
					}
				}
				err = s.BatchAdd(ctx, fqAdUserPaymentRecordAdds)
				liberr.ErrIsNil(ctx, err, "批量添加失败")
			}

			if !rechargeRes.HasMore {
				break
			}
			nextOffSet = rechargeRes.NextOffset
		}
	})
	return
}

// TimedPull 定时任务拉取
func (s *sFqAdUserPaymentRecord) TimedPull(ctx context.Context, statDate string) (count int, err error) {
	channelRechargeStatKey := model.FqAdUserPaymentDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullDate ")
		return 0, err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	count, err = s.Pull(innerCtx, statDate)
	if err != nil {
		g.Log().Error(ctx, "Pull err：", err)
	}
	return

}

func (s *sFqAdUserPaymentRecord) GetExportData(ctx context.Context, req *model.FqAdUserPaymentRecordSearchReq) (listRes []*model.FqAdUserPaymentRecordInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdUserPaymentRecord.Ctx(ctx).WithAll()
		if req.TradeNo != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().TradeNo+" = ?", req.TradeNo)
		}
		if req.FqDistributorId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().DistributorId+" = ?", req.FqDistributorId)
		}
		if req.AppId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().AppId+" = ?", req.AppId)
		}
		if req.AppName != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().AppName+" like ?", "%"+req.AppName+"%")
		}
		if req.PromotionId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OutTradeNo != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().OutTradeNo+" = ?", req.OutTradeNo)
		}
		if req.DeviceId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().DeviceId+" = ?", req.DeviceId)
		}
		if req.OpenId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.WxOaOpenId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxOaOpenId+" = ?", req.WxOaOpenId)
		}
		if req.WxOaName != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxOaName+" like ?", "%"+req.WxOaName+"%")
		}
		if req.PayAmount != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().PayAmount+" = ?", gconv.Int64(req.PayAmount))
		}
		if req.Ip != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().Ip+" = ?", req.Ip)
		}
		if req.UserAgent != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().UserAgent+" = ?", req.UserAgent)
		}
		if req.Oaid != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().Oaid+" = ?", req.Oaid)
		}
		if req.AndroidId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().AndroidId+" = ?", req.AndroidId)
		}

		if req.WxPlatformAppKey != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxPlatformAppKey+" = ?", req.WxPlatformAppKey)
		}
		if req.BookId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().BookId+" = ?", req.BookId)
		}
		if req.BookName != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.BookGender != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().BookGender+" = ?", req.BookGender)
		}
		if req.BookCategory != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().BookCategory+" = ?", req.BookCategory)
		}
		if req.Activity != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().Activity+" = ?", req.Activity)
		}
		if req.RecentReadBookId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().RecentReadBookId+" = ?", req.RecentReadBookId)
		}
		if req.ExternalId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().ExternalId+" = ?", req.ExternalId)
		}
		if req.OrderType != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().OrderType+" = ?", gconv.Int64(req.OrderType))
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.AdgroupId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().AdgroupId+" = ?", req.AdgroupId)
		}
		if req.AdId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().AdId+" = ?", req.AdId)
		}
		if req.UnionId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().UnionId+" = ?", req.UnionId)
		}
		if req.WxVideoId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxVideoId+" = ?", req.WxVideoId)
		}
		if req.WxVcSourceType != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxVcSourceType+" = ?", gconv.Int(req.WxVcSourceType))
		}
		if req.WxPromotionId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxPromotionId+" = ?", req.WxPromotionId)
		}
		if req.WxSourceType != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxSourceType+" = ?", req.WxSourceType)
		}
		if req.WxVideoChannelId != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().WxVideoChannelId+" = ?", req.WxVideoChannelId)
		}
		if req.Status != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.PayWay != "" {
			m = m.Where(dao.FqAdUserPaymentRecord.Columns().PayWay+" = ?", gconv.Int(req.PayWay))
		}

		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "trade_no asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdUserPaymentRecord) GetByTradeNo(ctx context.Context, tradeNo string) (res *model.FqAdUserPaymentRecordInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdUserPaymentRecord.Ctx(ctx).WithAll().Where(dao.FqAdUserPaymentRecord.Columns().TradeNo, tradeNo).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdUserPaymentRecord) Add(ctx context.Context, req *model.FqAdUserPaymentRecordAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserPaymentRecord.Ctx(ctx).Insert(do.FqAdUserPaymentRecord{
			TradeNo:          req.TradeNo,
			DistributorId:    req.DistributorId,
			AppId:            req.AppId,
			AppName:          req.AppName,
			PromotionId:      req.PromotionId,
			OutTradeNo:       req.OutTradeNo,
			DeviceId:         req.DeviceId,
			OpenId:           req.OpenId,
			WxOaOpenId:       req.WxOaOpenId,
			WxOaName:         req.WxOaName,
			PayAmount:        req.PayAmount,
			Ip:               req.Ip,
			UserAgent:        req.UserAgent,
			Oaid:             req.Oaid,
			AndroidId:        req.AndroidId,
			RegisterTime:     req.RegisterTime,
			WxPlatformAppKey: req.WxPlatformAppKey,
			BookId:           req.BookId,
			BookName:         req.BookName,
			BookGender:       req.BookGender,
			BookCategory:     req.BookCategory,
			Activity:         req.Activity,
			RecentReadBookId: req.RecentReadBookId,
			ExternalId:       req.ExternalId,
			OrderType:        req.OrderType,
			AdvertiserId:     req.AdvertiserId,
			AdgroupId:        req.AdgroupId,
			AdId:             req.AdId,
			UnionId:          req.UnionId,
			WxVideoId:        req.WxVideoId,
			WxVcSourceType:   req.WxVcSourceType,
			WxPromotionId:    req.WxPromotionId,
			WxSourceType:     req.WxSourceType,
			WxVideoChannelId: req.WxVideoChannelId,
			Status:           req.Status,
			PayWay:           req.PayWay,
			PayTimestamp:     req.PayTimestamp,
			CreateTime:       req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdUserPaymentRecord) Save(ctx context.Context, req *model.FqAdUserPaymentRecordSaveReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Info(ctx, fmt.Sprintf("收到数据----------- %v ------------", req))
		var existModel = new(do.FqAdUserPaymentRecord)
		err = dao.FqAdUserPaymentRecord.Ctx(ctx).Where(dao.FqAdUserPaymentRecord.Columns().TradeNo, req.TradeNo).Scan(existModel)
		cTime := libUtils.GetDateBySec2(gconv.Int64(req.PayTimestamp))
		addItem := do.FqAdUserPaymentRecord{
			CreateTime:       cTime,
			TradeNo:          req.TradeNo,
			DistributorId:    req.DistributorId,
			AppId:            req.AppId,
			AppName:          req.AppName,
			PromotionId:      req.PromotionId,
			OutTradeNo:       req.OutTradeNo,
			DeviceId:         req.DeviceId,
			OpenId:           req.OpenId,
			WxOaOpenId:       req.WxOaOpenId,
			WxOaName:         req.WxOaName,
			PayAmount:        req.PayAmount,
			Ip:               req.Ip,
			UserAgent:        req.UserAgent,
			Oaid:             req.Oaid,
			AndroidId:        req.AndroidId,
			RegisterTime:     req.RegisterTime,
			WxPlatformAppKey: req.WxPlatformAppKey,
			BookId:           req.BookId,
			BookName:         req.BookName,
			BookGender:       req.BookGender,
			BookCategory:     req.BookCategory,
			Activity:         req.Activity,
			RecentReadBookId: req.RecentReadBookId,
			ExternalId:       req.ExternalId,
			OrderType:        req.OrderType,
			AdvertiserId:     req.AdvertiserId,
			AdgroupId:        req.AdgroupId,
			AdId:             req.AdId,
			UnionId:          req.UnionId,
			Status:           0, // 进来的就是已经支付的
			PayWay:           existModel.PayWay,
			PayTimestamp:     req.PayTimestamp, // ？
		}
		if req.WxVideoInfo != nil {
			addItem.WxVideoId = req.WxVideoInfo.WxVideoId
			addItem.WxPromotionId = req.WxVideoInfo.WxPromotionId
			addItem.WxSourceType = req.WxVideoInfo.WxSourceType
			addItem.WxVideoChannelId = req.WxVideoInfo.WxVideoChannelId
			_, err = dao.FqAdUserPaymentRecord.Ctx(ctx).Save(addItem)
			liberr.ErrIsNil(ctx, err, "添加失败")
		} else {
			_, err = dao.FqAdUserPaymentRecord.Ctx(ctx).Save(addItem)
			liberr.ErrIsNil(ctx, err, "添加失败")
		}

	})
	return
}

func (s *sFqAdUserPaymentRecord) BatchAdd(ctx context.Context, reqs []*model.FqAdUserPaymentRecordAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []do.FqAdUserPaymentRecord
		for _, req := range reqs {
			list = append(list, do.FqAdUserPaymentRecord{
				TradeNo:          req.TradeNo,
				DistributorId:    req.DistributorId,
				AppId:            req.AppId,
				AppName:          req.AppName,
				PromotionId:      req.PromotionId,
				OutTradeNo:       req.OutTradeNo,
				DeviceId:         req.DeviceId,
				OpenId:           req.OpenId,
				WxOaOpenId:       req.WxOaOpenId,
				WxOaName:         req.WxOaName,
				PayAmount:        req.PayAmount,
				Ip:               req.Ip,
				UserAgent:        req.UserAgent,
				Oaid:             req.Oaid,
				AndroidId:        req.AndroidId,
				RegisterTime:     req.RegisterTime,
				WxPlatformAppKey: req.WxPlatformAppKey,
				BookId:           req.BookId,
				BookName:         req.BookName,
				BookGender:       req.BookGender,
				BookCategory:     req.BookCategory,
				Activity:         req.Activity,
				RecentReadBookId: req.RecentReadBookId,
				ExternalId:       req.ExternalId,
				OrderType:        req.OrderType,
				AdvertiserId:     req.AdvertiserId,
				AdgroupId:        req.AdgroupId,
				AdId:             req.AdId,
				UnionId:          req.UnionId,
				WxVideoId:        req.WxVideoId,
				WxVcSourceType:   req.WxVcSourceType,
				WxPromotionId:    req.WxPromotionId,
				WxSourceType:     req.WxSourceType,
				WxVideoChannelId: req.WxVideoChannelId,
				Status:           req.Status,
				PayWay:           req.PayWay,
				PayTimestamp:     req.PayTimestamp,
				CreateTime:       req.CreateTime,
			})
		}
		_, err = dao.FqAdUserPaymentRecord.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdUserPaymentRecord) Edit(ctx context.Context, req *model.FqAdUserPaymentRecordEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserPaymentRecord.Ctx(ctx).WherePri(req.TradeNo).Update(do.FqAdUserPaymentRecord{
			DistributorId:    req.DistributorId,
			AppId:            req.AppId,
			AppName:          req.AppName,
			PromotionId:      req.PromotionId,
			OutTradeNo:       req.OutTradeNo,
			DeviceId:         req.DeviceId,
			OpenId:           req.OpenId,
			WxOaOpenId:       req.WxOaOpenId,
			WxOaName:         req.WxOaName,
			PayAmount:        req.PayAmount,
			Ip:               req.Ip,
			UserAgent:        req.UserAgent,
			Oaid:             req.Oaid,
			AndroidId:        req.AndroidId,
			RegisterTime:     req.RegisterTime,
			WxPlatformAppKey: req.WxPlatformAppKey,
			BookId:           req.BookId,
			BookName:         req.BookName,
			BookGender:       req.BookGender,
			BookCategory:     req.BookCategory,
			Activity:         req.Activity,
			RecentReadBookId: req.RecentReadBookId,
			ExternalId:       req.ExternalId,
			OrderType:        req.OrderType,
			AdvertiserId:     req.AdvertiserId,
			AdgroupId:        req.AdgroupId,
			AdId:             req.AdId,
			UnionId:          req.UnionId,
			WxVideoId:        req.WxVideoId,
			WxVcSourceType:   req.WxVcSourceType,
			WxPromotionId:    req.WxPromotionId,
			WxSourceType:     req.WxSourceType,
			WxVideoChannelId: req.WxVideoChannelId,
			Status:           req.Status,
			PayWay:           req.PayWay,
			PayTimestamp:     req.PayTimestamp,
			CreateTime:       req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdUserPaymentRecord) Delete(ctx context.Context, tradeNos []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserPaymentRecord.Ctx(ctx).Delete(dao.FqAdUserPaymentRecord.Columns().TradeNo+" in (?)", tradeNos)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqTotalAmount(ctx context.Context, statDate string) (totalAmountStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).As("fq").
			InnerJoin("s_channel s", "s.fq_promotion_id = fq.promotion_id").
			Fields("IFNULL(sum(fq.pay_amount/100), 0.00) as totalAmount").
			Fields("count(distinct fq.device_id) as rechargeNums").
			Fields("count(*) as totalRechargeTimes").
			Fields("s.channel_code as account").
			WhereGTE("fq."+dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE("fq."+dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			Where("fq."+dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("s.channel_code").
			Scan(&totalAmountStat)
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqNewUserAmount(ctx context.Context, statDate string) (newUserAmountStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).As("fq").
			InnerJoin("s_channel s", "s.fq_promotion_id = fq.promotion_id").
			Fields("IFNULL(sum(fq.pay_amount/100), 0.00) as newUserAmount").
			Fields("count(distinct fq.device_id) as newUserRechargeNums").
			Fields("s.channel_code as account").
			WhereGTE("fq."+dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE("fq."+dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			WhereGTE("fq."+dao.FqAdUserPaymentRecord.Columns().RegisterTime, startTime).
			WhereLTE("fq."+dao.FqAdUserPaymentRecord.Columns().RegisterTime, endTime).
			Where("fq."+dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("s.channel_code").
			Scan(&newUserAmountStat)
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqAmountByPayWayAndOrderType(ctx context.Context, statDate string) (detailAmountStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).As("fq").
			InnerJoin("s_channel s", "s.fq_promotion_id = fq.promotion_id").
			Fields("IFNULL(sum(fq.pay_amount/100), 0.00) as totalAmount").
			Fields("s.channel_code as account").
			Fields("fq.pay_way as fqPayWay").
			Fields("fq.order_type as fqOrderType").
			WhereGTE("fq."+dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE("fq."+dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			Where("fq."+dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("s.channel_code, fq.pay_way, fq.order_type").
			Scan(&detailAmountStat)
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqDistributorRechargeAmount(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).
			Fields("count(distinct device_id) as rechargeNums").
			Fields("count(*) as totalRechargeTimes").
			Fields("IFNULL(sum(pay_amount/100), 0.00) as dayTotalAmount").
			Fields("distributor_id as fqDistributorId").
			WhereGTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			Where(dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("distributor_id").
			Scan(&res)
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqDistributorAmountByPayWayAndOrderType(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).
			Fields("IFNULL(sum(pay_amount/100), 0.00) as dayTotalAmount").
			Fields("distributor_id as fqDistributorId").
			Fields("pay_way as fqPayWay").
			Fields("order_type as fqOrderType").
			WhereGTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			Where(dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("distributor_id, pay_way, order_type").
			Scan(&res)
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqVideoTotalAmount(ctx context.Context, statDate string) (totalAmountStat []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).
			Fields("promotion_id as fqPromotionId").
			Fields("book_id as videoId").
			Fields("ANY_VALUE(book_name) as videoName").
			Fields("ANY_VALUE(distributor_id) as fqChannelDistributorId").
			Fields("IFNULL(sum(pay_amount/100), 0.00) as totalAmount").
			Fields("count(distinct device_id) as rechargeNums").
			Fields("count(*) as totalRechargeTimes").
			Fields("ROUND(IFNULL(sum(pay_amount/100), 0.00) / IFNULL(count(distinct device_id), 0.00), 2) as customerPrice").
			Fields("ROUND(IFNULL(count(*), 0.00) / IFNULL(count(distinct device_id), 0.00), 2) as avgRechargeTimes").
			WhereGTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			Where(dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("promotion_id, book_id").
			Scan(&totalAmountStat)
	})
	return
}

func (s *sFqAdUserPaymentRecord) CalcFqVideoNewUserAmount(ctx context.Context, statDate string) (newUserAmountStat []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime := libUtils.GetDayStartAndEnd(statDate, statDate)
		err = dao.FqAdUserPaymentRecordAnalytic.Ctx(ctx).
			Fields("promotion_id as fqPromotionId").
			Fields("book_id as videoId").
			Fields("ANY_VALUE(book_name) as videoName").
			Fields("ANY_VALUE(distributor_id) as fqChannelDistributorId").
			Fields("IFNULL(sum(pay_amount/100), 0.00) as newUserAmount").
			WhereGTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, startTime).
			WhereLTE(dao.FqAdUserPaymentRecord.Columns().CreateTime, endTime).
			WhereGTE(dao.FqAdUserPaymentRecord.Columns().RegisterTime, startTime).
			WhereLTE(dao.FqAdUserPaymentRecord.Columns().RegisterTime, endTime).
			Where(dao.FqAdUserPaymentRecord.Columns().Status, 0).
			Group("promotion_id, book_id").
			Scan(&newUserAmountStat)
	})
	return
}
