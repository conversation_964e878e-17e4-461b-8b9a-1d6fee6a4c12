/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletUpdateV30RecommendedRechargeTier
type ToolsWechatAppletUpdateV30RecommendedRechargeTier string

// List of tools_wechat_applet_update_v3.0_recommended_recharge_tier
const (
	ABOVE_200_ToolsWechatAppletUpdateV30RecommendedRechargeTier             ToolsWechatAppletUpdateV30RecommendedRechargeTier = "ABOVE_200"
	FROM_100_TO_200_ToolsWechatAppletUpdateV30RecommendedRechargeTier       ToolsWechatAppletUpdateV30RecommendedRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletUpdateV30RecommendedRechargeTier ToolsWechatAppletUpdateV30RecommendedRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletUpdateV30RecommendedRechargeTier     ToolsWechatAppletUpdateV30RecommendedRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_update_v3.0_recommended_recharge_tier value
func (v ToolsWechatAppletUpdateV30RecommendedRechargeTier) Ptr() *ToolsWechatAppletUpdateV30RecommendedRechargeTier {
	return &v
}
