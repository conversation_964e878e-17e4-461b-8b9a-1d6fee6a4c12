/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AdvertiserBudgetUpdateV2ApiService AdvertiserBudgetUpdateV2Api service
type AdvertiserAvatarSubmitV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.AdvertiserAvatarSubmitV2Request
}

func (r *AdvertiserAvatarSubmitV2ApiService) SetCfg(cfg *conf.Configuration) *AdvertiserAvatarSubmitV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AdvertiserAvatarSubmitV2ApiService) AdvertiserUpdateBudgetV2Request(advertiserUpdateBudgetV2Request models.AdvertiserAvatarSubmitV2Request) *AdvertiserAvatarSubmitV2ApiService {
	r.Request = &advertiserUpdateBudgetV2Request
	return r
}

func (r *AdvertiserAvatarSubmitV2ApiService) AccessToken(accessToken string) *AdvertiserAvatarSubmitV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AdvertiserAvatarSubmitV2ApiService) Do() (data *models.AdvertiserAvatarSubmitV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/advertiser/avatar/submit/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.AdvertiserAvatarSubmitV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AdvertiserAvatarSubmitV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/avatar/submit解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
