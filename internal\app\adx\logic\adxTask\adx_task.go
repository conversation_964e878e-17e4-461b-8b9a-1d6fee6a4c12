// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: internal/app/adx/logic/adx_task.go
// 生成人：cyao
// desc:ADX任务主表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"time"
)

func init() {
	service.RegisterAdxTask(New())
}

func New() service.IAdxTask {
	return &sAdxTask{}
}

type sAdxTask struct{}

func (s *sAdxTask) List(ctx context.Context, req *model.AdxTaskSearchReq) (listRes *model.AdxTaskSearchRes, err error) {
	listRes = new(model.AdxTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxTask.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxTask.Columns().Id+" = ?", req.Id)
		}
		if req.TaskName != "" {
			m = m.Where(dao.AdxTask.Columns().TaskName+" like ?", "%"+req.TaskName+"%")
		}
		if req.OperationType != "" {
			m = m.Where(dao.AdxTask.Columns().OperationType+" = ?", req.OperationType)
		}
		if req.OperationCount != "" {
			m = m.Where(dao.AdxTask.Columns().OperationCount+" = ?", gconv.Int(req.OperationCount))
		}
		if req.OperationTime != "" {
			m = m.Where(dao.AdxTask.Columns().OperationTime+" = ?", gconv.Time(req.OperationTime))
		}
		if req.Status != "" {
			m = m.Where(dao.AdxTask.Columns().Status+" = ?", req.Status)
		}
		if req.Operator != "" {
			m = m.Where(dao.AdxTask.Columns().Operator+" = ?", req.Operator)
		}
		if req.DownloadUrl != "" {
			m = m.Where(dao.AdxTask.Columns().DownloadUrl+" = ?", req.DownloadUrl)
		}
		if req.FailLog != "" {
			m = m.Where(dao.AdxTask.Columns().FailLog+" = ?", req.FailLog)
		}
		if req.TotalFileSize != "" {
			m = m.Where(dao.AdxTask.Columns().TotalFileSize+" = ?", gconv.Int64(req.TotalFileSize))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdxTask.Columns().CreatedAt+" >=? AND "+dao.AdxTask.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if len(req.StartTime) > 0 {
			m = m.Where(dao.AdxTask.Columns().CreatedAt+" >=? ", req.StartTime)
		}
		if len(req.EndTime) > 0 {
			m = m.Where(dao.AdxTask.Columns().CreatedAt+" <=?", req.EndTime)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "created_at desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxTaskListRes
		err = m.Fields("`id`,`task_name`,`operation_type`,`operation_count`,`operation_time`,`status`,`operator`,`fail_log`,`total_file_size`,`created_at`").Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxTaskListRes, len(res))
		for k, v := range res {
			// 根据taskId 获取items url 数据
			array, _ := service.AdxTaskItem().GetByTaskId(ctx, v.Id)
			itemList := make([]*model.DownloadInfo, 0)
			for _, info := range array {
				itemList = append(itemList, &model.DownloadInfo{
					ItemId: info.Id,
					Url:    info.MaterialUrl,
				})
			}
			// 根据task id 获取
			listRes.List[k] = &model.AdxTaskListRes{
				Id:             v.Id,
				TaskName:       v.TaskName,
				OperationType:  v.OperationType,
				OperationCount: v.OperationCount,
				OperationTime:  v.OperationTime,
				Status:         v.Status,
				Operator:       v.Operator,
				DownloadUrl:    itemList,
				//DownloadUrl:    v.DownloadUrl,
				FailLog:       v.FailLog,
				TotalFileSize: v.TotalFileSize,
				CreatedAt:     v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdxTask) GetExportData(ctx context.Context, req *model.AdxTaskSearchReq) (listRes []*model.AdxTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxTask.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxTask.Columns().Id+" = ?", req.Id)
		}
		if req.TaskName != "" {
			m = m.Where(dao.AdxTask.Columns().TaskName+" like ?", "%"+req.TaskName+"%")
		}
		if req.OperationType != "" {
			m = m.Where(dao.AdxTask.Columns().OperationType+" = ?", req.OperationType)
		}
		if req.OperationCount != "" {
			m = m.Where(dao.AdxTask.Columns().OperationCount+" = ?", gconv.Int(req.OperationCount))
		}
		if req.OperationTime != "" {
			m = m.Where(dao.AdxTask.Columns().OperationTime+" = ?", gconv.Time(req.OperationTime))
		}
		if req.Status != "" {
			m = m.Where(dao.AdxTask.Columns().Status+" = ?", req.Status)
		}
		if req.Operator != "" {
			m = m.Where(dao.AdxTask.Columns().Operator+" = ?", req.Operator)
		}
		if req.DownloadUrl != "" {
			m = m.Where(dao.AdxTask.Columns().DownloadUrl+" = ?", req.DownloadUrl)
		}
		if req.FailLog != "" {
			m = m.Where(dao.AdxTask.Columns().FailLog+" = ?", req.FailLog)
		}
		if req.TotalFileSize != "" {
			m = m.Where(dao.AdxTask.Columns().TotalFileSize+" = ?", gconv.Int64(req.TotalFileSize))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdxTask.Columns().CreatedAt+" >=? AND "+dao.AdxTask.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxTask) GetById(ctx context.Context, id int64) (res *model.AdxTaskInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxTask.Ctx(ctx).WithAll().Where(dao.AdxTask.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// CreateByMaterial 	MaterialIds []uint64 `p:"materialIds" v:"required#素材ID不能为空" dc:"素材ID"`
func (s *sAdxTask) CreateByMaterial(ctx context.Context, req *model.CreateByMaterialReq) (res *model.CreateByMaterialRes, err error) {
	if len(req.MaterialIds) == 0 {
		return
	}
	res = new(model.CreateByMaterialRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		taskName, operationType := getTaskName(req.MaterialIds)
		result, err := dao.AdxTask.Ctx(ctx).Insert(do.AdxTask{
			TaskName:       taskName,
			OperationType:  operationType,
			OperationCount: len(req.MaterialIds),
			OperationTime:  gtime.Now(),
			Status:         consts.TaskStatusInit,
			Operator:       userInfo.UserName,
			DownloadUrl:    "",
			FailLog:        "",
			TotalFileSize:  0,
			CreatedAt:      gtime.Now(),
			UpdatedAt:      gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "创建任务失败")
		taskId, _ := result.LastInsertId()
		res.TaskId = taskId
		res.TaskName = taskName
		// 根据素材id 构建taskitem
		// 根据素材id获取素材详情信息
		materialInfo, err := service.AdxMaterial().GetByMaterialIds(ctx, req.MaterialIds)
		liberr.ErrIsNil(ctx, err, "获取素材详情失败")
		playletIds := make([]int64, 0)
		for _, v := range materialInfo {
			if v.PlayletId > 0 {
				playletIds = append(playletIds, v.PlayletId)
			}
		}
		// 根据playletId 获取短剧详情
		playletInfo, err := service.AdxPlaylet().GetByPlayletIds(ctx, playletIds)
		taskItemList := make([]*model.AdxTaskItemAddReq, 0, len(materialInfo))
		for _, v := range materialInfo {
			relatedScript := ""
			if v.PlayletId > 0 {
				for _, vv := range playletInfo {
					if vv.PlayletId == uint64(v.PlayletId) {
						relatedScript = vv.PlayletName
						break
					}
				}
			}
			taskItemList = append(taskItemList, &model.AdxTaskItemAddReq{
				TaskId:        taskId,
				MaterialSize:  fmt.Sprintf("%d*%d", v.MaterialWidth, v.MaterialHeight),
				ImgUrl:        getImgUrl(v),
				MaterialType:  getMaterialType(v.MaterialType),
				MaterialUrl:   getMaterialUrl(v),
				OperationTime: gtime.Now(),
				RelatedScript: relatedScript,
				Result:        "下载中",
				FailReason:    "",
			})
		}
		// 添加 taskItemList
		materialListRes := make([]model.MaterialListRes, 0)
		for _, addReq := range taskItemList {
			last, err := dao.AdxTaskItem.Ctx(ctx).Insert(addReq)
			if err != nil {
				g.Log().Error(ctx, err)
				liberr.ErrIsNil(ctx, err, "添加任务失败")
			}
			taskItemId, _ := last.LastInsertId()
			for _, infoRes := range materialInfo {
				if getMaterialUrl(infoRes) == addReq.MaterialUrl {
					materialListRes = append(materialListRes, model.MaterialListRes{
						ItemTaskId: taskItemId,
						MaterialId: infoRes.MaterialId,
					})
				}
			}

		}
		res.MaterialList = materialListRes
		//err = service.AdxTaskItem().AddBatch(ctx, taskItemList)
	})
	return
}

func getMaterialUrl(material *model.AdxMaterialInfoRes) string {
	switch material.MaterialType {
	case 1:
		if len(material.PicList) > 0 {
			return material.PicList[0]
		}
	case 2:
		if len(material.VideoList) > 0 {
			return material.VideoList[0]
		}
	}
	return ""
}

func getImgUrl(material *model.AdxMaterialInfoRes) string {
	if len(material.PicList) > 0 {
		return material.PicList[0]
	}
	return ""
}

func getMaterialType(MaterialType uint) string {
	switch MaterialType {
	case 1:
		return "图片"
	case 2:
		return "视频"

	}
	return "图片"
}

func getTaskName(MaterialIds []uint64) (taskName, operationType string) {
	taskName = "ADX"
	if len(MaterialIds) > 1 {
		taskName = "ADX_" + "批量下载"
		operationType = "批量下载"
	} else {
		taskName = "ADX_" + "单条下载"
		operationType = "单条下载"
	}
	taskName = taskName + "_" + time.Now().Format(time.DateTime)
	return
}

func (s *sAdxTask) Add(ctx context.Context, req *model.AdxTaskAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTask.Ctx(ctx).Insert(do.AdxTask{
			TaskName:       req.TaskName,
			OperationType:  req.OperationType,
			OperationCount: req.OperationCount,
			OperationTime:  req.OperationTime,
			Status:         req.Status,
			Operator:       req.Operator,
			DownloadUrl:    req.DownloadUrl,
			FailLog:        req.FailLog,
			TotalFileSize:  req.TotalFileSize,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxTask) Edit(ctx context.Context, req *model.AdxTaskEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTask.Ctx(ctx).WherePri(req.Id).Update(do.AdxTask{
			TaskName:       req.TaskName,
			OperationType:  req.OperationType,
			OperationCount: req.OperationCount,
			OperationTime:  req.OperationTime,
			Status:         req.Status,
			Operator:       req.Operator,
			DownloadUrl:    req.DownloadUrl,
			FailLog:        req.FailLog,
			TotalFileSize:  req.TotalFileSize,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxTask) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTask.Ctx(ctx).Delete(dao.AdxTask.Columns().Id+" in (?)", ids)
		// 同时删除taskItem
		_, err = dao.AdxTaskItem.Ctx(ctx).Delete(dao.AdxTaskItem.Columns().TaskId+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// UpdateStatus(ctx context.Context, req *model.UpdateStatusReq) error
func (s *sAdxTask) UpdateStatus(ctx context.Context, req *model.UpdateStatusReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxTask.Ctx(ctx).WherePri(req.Id).Update(do.AdxTask{
			Status:  req.Status,
			FailLog: req.FailReason,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}
