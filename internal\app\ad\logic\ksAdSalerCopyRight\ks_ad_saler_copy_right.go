// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: internal/app/ad/logic/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdSalerCopyRight(New())
}

func New() service.IKsAdSalerCopyRight {
	return &sKsAdSalerCopyRight{}
}

type sKsAdSalerCopyRight struct{}

func (s *sKsAdSalerCopyRight) List(ctx context.Context, req *model.KsAdSalerCopyRightSearchReq) (listRes *model.KsAdSalerCopyRightSearchRes, err error) {
	listRes = new(model.KsAdSalerCopyRightSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdSalerCopyRight.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdSalerCopyRight.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdSalerCopyRight.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if len(req.CopyrightSeriesNameMap) > 0 {
			var condition string
			var index int
			for k, v := range req.CopyrightSeriesNameMap {
				var copyrightSeriesNames = ""
				for innerIndex, seriesName := range v {
					if innerIndex == 0 {
						copyrightSeriesNames += fmt.Sprintf(`'%s'`, seriesName)
					} else {
						copyrightSeriesNames += fmt.Sprintf(`,'%s'`, seriesName)
					}
				}
				conditionInner := fmt.Sprintf("(advertiser_id = %s and copyright_series_name in (%s))", k, copyrightSeriesNames)
				if index == 0 {
					condition += conditionInner
				} else {
					condition += " OR " + conditionInner
				}
				index++
			}
			m = m.Where("(" + condition + ")")
		}
		if req.SalerEntityName != "" {
			m = m.Where(dao.KsAdSalerCopyRight.Columns().SalerEntityName+" like ?", "%"+req.SalerEntityName+"%")
		}
		if req.SeriesType > 0 {
			m = m.Where(dao.KsAdSalerCopyRight.Columns().SeriesType+" = ?", req.SeriesType)
		}
		if req.StartReportDate != "" && req.EndReportDate != "" {
			m = m.Where(dao.KsAdSalerCopyRight.Columns().ReportDate+" >= ?", req.StartReportDate)
			m = m.Where(dao.KsAdSalerCopyRight.Columns().ReportDate+" <= ?", req.EndReportDate)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "reportDate desc, id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdSalerCopyRightListRes
		err = m.Fields("id as id").
			Fields("advertiser_id as advertiserId").
			Fields("report_date as reportDate").
			Fields("copyright_series_name as copyrightSeriesName").
			Fields("saler_entity_name as salerEntityName").
			Fields("saler_rate_str as salerRateStr").
			Fields("pay_amt as payAmt").
			Fields("copyright_event_pay_purchase_amount as copyrightEventPayPurchaseAmount").
			Fields("(pay_amt - copyright_event_pay_purchase_amount) as distributorPurchaseAmount").
			Fields("mini_game_iaa_purchase_amount as miniGameIaaPurchaseAmount").
			Fields("mini_game_iaa_purchase_amount_divided as miniGameIaaPurchaseAmountDivided").
			Fields("copyright_mini_game_iaa_purchase_amount_divided as copyrightMiniGameIaaPurchaseAmountDivided").
			Fields("(mini_game_iaa_purchase_amount_divided - copyright_mini_game_iaa_purchase_amount_divided) as distributorPurchaseAmountDivided").
			Fields("series_type as seriesType").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 查询账户名称
		var advertiserIds = make([]int64, 0)
		for _, v := range res {
			advertiserIds = append(advertiserIds, v.AdvertiserId)
		}
		ksAdInfos, _ := service.KsAdInfo().GetByAdvertiserIds(ctx, advertiserIds)
		listRes.List = make([]*model.KsAdSalerCopyRightListRes, len(res))
		for k, v := range res {
			for _, adInfo := range ksAdInfos {
				if adInfo.AdvertiserId == v.AdvertiserId {
					v.AdvertiserName = adInfo.AdAccountName
					break
				}
			}
			listRes.List[k] = v
		}
		// 计算汇总数据
		var summary *model.KsAdSalerCopyRightListRes
		err = m.FieldSum("pay_amt", "payAmt").
			FieldSum("copyright_event_pay_purchase_amount", "copyrightEventPayPurchaseAmount").
			Fields("sum(pay_amt) - sum(copyright_event_pay_purchase_amount) as distributorPurchaseAmount").
			FieldSum("mini_game_iaa_purchase_amount", "miniGameIaaPurchaseAmount").
			FieldSum("mini_game_iaa_purchase_amount_divided", "miniGameIaaPurchaseAmountDivided").
			FieldSum("copyright_mini_game_iaa_purchase_amount_divided", "copyrightMiniGameIaaPurchaseAmountDivided").
			Fields("sum(mini_game_iaa_purchase_amount_divided) - sum(copyright_mini_game_iaa_purchase_amount_divided) as distributorPurchaseAmountDivided").
			Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")
		listRes.Summary = summary
	})
	return
}

func (s *sKsAdSalerCopyRight) GetExportData(ctx context.Context, req *model.KsAdSalerCopyRightSearchReq) (listRes []*model.KsAdSalerCopyRightListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res, err1 := s.List(ctx, req)
		liberr.ErrIsNil(ctx, err1, "获取数据失败")
		listRes = res.List
	})
	return
}

func (s *sKsAdSalerCopyRight) GetById(ctx context.Context, id int64) (res *model.KsAdSalerCopyRightInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdSalerCopyRight.Ctx(ctx).WithAll().Where(dao.KsAdSalerCopyRight.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdSalerCopyRight) Add(ctx context.Context, req *model.KsAdSalerCopyRightAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdSalerCopyRight.Ctx(ctx).Insert(do.KsAdSalerCopyRight{
			AdvertiserId:                     req.AdvertiserId,
			ReportDate:                       req.ReportDate,
			CopyrightSeriesName:              req.CopyrightSeriesName,
			SalerEntityName:                  req.SalerEntityName,
			SalerRateStr:                     req.SalerRateStr,
			PayAmt:                           req.PayAmt,
			CopyrightEventPayPurchaseAmount:  req.CopyrightEventPayPurchaseAmount,
			MiniGameIaaPurchaseAmount:        req.MiniGameIaaPurchaseAmount,
			MiniGameIaaPurchaseAmountDivided: req.MiniGameIaaPurchaseAmountDivided,
			CopyrightMiniGameIaaPurchaseAmountDivided: req.CopyrightMiniGameIaaPurchaseAmountDivided,
			SeriesType: req.SeriesType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdSalerCopyRight) BatchAdd(ctx context.Context, batchAddReq []*model.KsAdSalerCopyRightAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		list := make([]do.KsAdSalerCopyRight, 0)
		for _, req := range batchAddReq {
			list = append(list, do.KsAdSalerCopyRight{
				AdvertiserId:                     req.AdvertiserId,
				ReportDate:                       req.ReportDate,
				CopyrightSeriesName:              req.CopyrightSeriesName,
				SalerEntityName:                  req.SalerEntityName,
				SalerRateStr:                     req.SalerRateStr,
				PayAmt:                           req.PayAmt,
				CopyrightEventPayPurchaseAmount:  req.CopyrightEventPayPurchaseAmount,
				MiniGameIaaPurchaseAmount:        req.MiniGameIaaPurchaseAmount,
				MiniGameIaaPurchaseAmountDivided: req.MiniGameIaaPurchaseAmountDivided,
				CopyrightMiniGameIaaPurchaseAmountDivided: req.CopyrightMiniGameIaaPurchaseAmountDivided,
				SeriesType: req.SeriesType,
			})
		}
		_, err = dao.KsAdSalerCopyRight.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdSalerCopyRight) Edit(ctx context.Context, req *model.KsAdSalerCopyRightEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdSalerCopyRight.Ctx(ctx).WherePri(req.Id).Update(do.KsAdSalerCopyRight{
			AdvertiserId:                     req.AdvertiserId,
			ReportDate:                       req.ReportDate,
			CopyrightSeriesName:              req.CopyrightSeriesName,
			SalerEntityName:                  req.SalerEntityName,
			SalerRateStr:                     req.SalerRateStr,
			PayAmt:                           req.PayAmt,
			CopyrightEventPayPurchaseAmount:  req.CopyrightEventPayPurchaseAmount,
			MiniGameIaaPurchaseAmount:        req.MiniGameIaaPurchaseAmount,
			MiniGameIaaPurchaseAmountDivided: req.MiniGameIaaPurchaseAmountDivided,
			CopyrightMiniGameIaaPurchaseAmountDivided: req.CopyrightMiniGameIaaPurchaseAmountDivided,
			SeriesType: req.SeriesType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdSalerCopyRight) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdSalerCopyRight.Ctx(ctx).Delete(dao.KsAdSalerCopyRight.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdSalerCopyRight) RunCalcAdSalerCopyRightStat(ctx context.Context, req *model.KsAdSalerCopyRightSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()
		startTime := req.StartReportDate
		endTime := req.EndReportDate
		for {
			if startTime > endTime {
				break
			}
			errors := s.CalcAdSalerCopyRightStat(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sKsAdSalerCopyRight) CalcAdSalerCopyRightStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatKsAdSalerCopyRightStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatKsAdSalerCopyRightStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
		err = s.CalcAdSalerCopyRightStat(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "快手分销数据统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcAdSalerCopyRightStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "快手分销数据统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sKsAdSalerCopyRight) CalcAdSalerCopyRightStat(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询所有快手账号
		advertiserList, err1 := service.KsAdAccountInfo().GetAllAdvertiserIds(ctx)
		liberr.ErrIsNil(ctx, err1, "获取快手账号失败")
		for _, advertiser := range advertiserList {
			advertiserId := advertiser.AdvertiserId
			accessToken := api.GetAccessTokenByAppIdCache(advertiserId)
			if accessToken == "" {
				g.Log().Error(ctx, fmt.Sprintf("------------- 获取快手token失败, advertiserId: %v --------------------", advertiserId))
				continue
			}
			startTimestamps, endTimestamps, _ := libUtils.GetDayTimestamps(statDate)
			s.QueryAdSalerCopyRight(ctx, advertiserId, accessToken, startTimestamps, endTimestamps)
		}
	})
	return
}

func (s *sKsAdSalerCopyRight) QueryAdSalerCopyRight(ctx context.Context, advertiserId int64, accessToken string, startDate int64, endDate int64) {
	var seriesTypes = []int64{1, 2}
	for _, seriesType := range seriesTypes {
		var pageNum = 1
		var pageSize = 500
		for {
			querySalerCopyRightRes, err1 := api.GetKSApiClient().QuerySalerCopyRightDataService.
				AccessToken(accessToken).
				SetReq(api.QuerySalerCopyRightDataReq{
					AdvertiserID: advertiserId,
					StartDate:    startDate,
					EndDate:      endDate,
					RoleType:     1,
					CurrentPage:  pageNum,
					PageSize:     pageSize,
					SeriesType:   seriesType,
				}).Do()
			if err1 != nil {
				g.Log().Errorf(ctx, "advertiserId: %v查询版权商短剧分销数据失败: %v", advertiserId, err1)
				break
			}
			if querySalerCopyRightRes.Data == nil || len(querySalerCopyRightRes.Data.DataList) == 0 {
				break
			}
			batchAddReq := make([]*model.KsAdSalerCopyRightAddReq, 0)
			for _, item := range querySalerCopyRightRes.Data.DataList {
				batchAddReq = append(batchAddReq, &model.KsAdSalerCopyRightAddReq{
					AdvertiserId:                     advertiserId,
					ReportDate:                       item.ReportDate,
					CopyrightSeriesName:              item.CopyrightSeriesName,
					SalerEntityName:                  item.SalerEntityName,
					SalerRateStr:                     item.SalerRateStr,
					PayAmt:                           item.PayAmt,
					CopyrightEventPayPurchaseAmount:  item.CopyrightEventPayPurchaseAmount,
					MiniGameIaaPurchaseAmount:        item.MiniGameIaaPurchaseAmount,
					MiniGameIaaPurchaseAmountDivided: item.MiniGameIaaPurchaseAmountDivided,
					CopyrightMiniGameIaaPurchaseAmountDivided: item.CopyrightMiniGameIaaPurchaseAmountDivided,
					SeriesType: seriesType,
				})
			}
			_ = s.BatchAdd(ctx, batchAddReq)
			if pageNum*pageSize >= querySalerCopyRightRes.Data.TotalCount {
				break
			}
			pageNum++
		}
	}
}
