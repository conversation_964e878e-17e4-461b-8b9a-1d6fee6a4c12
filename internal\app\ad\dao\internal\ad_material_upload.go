// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-23 15:51:08
// 生成路径: internal/app/ad/dao/internal/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMaterialUploadDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdMaterialUploadDao struct {
	table   string                  // Table is the underlying table name of the DAO.
	group   string                  // Group is the database configuration group name of current DAO.
	columns AdMaterialUploadColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdMaterialUploadColumns defines and stores column names for table ad_material_upload.
type AdMaterialUploadColumns struct {
	Id           string // 自增ID
	MediaType    string // 媒体类型：图片或视频 1 图片 2视频
	AdMaterialId string // 素材id 关联ad_material 表格 material_id
	AdvertiserId string // 广告账户id
	MediaId      string // 图片/视频ID
	Size         string // 媒体文件大小
	Width        string // 宽度
	Height       string // 高度
	Url          string // 图片预览地址或视频地址
	Format       string // 图片格式
	Signature    string // 图片md5 或视频md5
	MaterialId   string // 素材id，即多合一报表中的素材id，唯一对应一个素材id
	Duration     string // 视频时长，如果是视频，则填充此字段
	MaterialName string // 素材名
	CreatedAt    string // 创建时间
	UpdatedAt    string // 更新时间
}

var adMaterialUploadColumns = AdMaterialUploadColumns{
	Id:           "id",
	MediaType:    "media_type",
	AdMaterialId: "ad_material_id",
	AdvertiserId: "advertiser_id",
	MediaId:      "media_id",
	Size:         "size",
	Width:        "width",
	Height:       "height",
	Url:          "url",
	Format:       "format",
	Signature:    "signature",
	MaterialId:   "material_id",
	Duration:     "duration",
	MaterialName: "materialName",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
}

// NewAdMaterialUploadDao creates and returns a new DAO object for table data access.
func NewAdMaterialUploadDao() *AdMaterialUploadDao {
	return &AdMaterialUploadDao{
		group:   "default",
		table:   "ad_material_upload",
		columns: adMaterialUploadColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdMaterialUploadDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdMaterialUploadDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdMaterialUploadDao) Columns() AdMaterialUploadColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdMaterialUploadDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdMaterialUploadDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdMaterialUploadDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
