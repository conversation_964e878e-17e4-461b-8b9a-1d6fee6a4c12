// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/dao/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adAnchorPointUploadDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adAnchorPointUploadDao struct {
	*internal.AdAnchorPointUploadDao
}

var (
	// AdAnchorPointUpload is globally public accessible object for table tools_gen_table operations.
	AdAnchorPointUpload = adAnchorPointUploadDao{
		internal.NewAdAnchorPointUploadDao(),
	}
)

// Fill with you ideas below.
