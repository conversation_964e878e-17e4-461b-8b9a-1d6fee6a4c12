// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:33:37
// 生成路径: api/v1/adx/adx_playlet.go
// 生成人：cq
// desc:ADX短剧基本信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxPlayletSearchReq 分页请求参数
type AdxPlayletSearchReq struct {
	g.Meta `path:"/list" tags:"ADX短剧基本信息表" method:"get" summary:"ADX短剧基本信息表列表"`
	commonApi.Author
	model.AdxPlayletSearchReq
}

// AdxPlayletSearchRes 列表返回结果
type AdxPlayletSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxPlayletSearchRes
}

// 获取短剧的tag
type AdxPlayletGetTagReq struct {
	g.Meta `path:"/getTag" tags:"ADX短剧基本信息表" method:"get" summary:"获取短剧的tag"`
	commonApi.Author
}

type AdxPlayletGetTagRes struct {
	Tags []string `json:"tags"`
}

// 全量拉取

type AdxPlayletPullAllDataReq struct {
	g.Meta `path:"/pullAllData" tags:"ADX短剧基本信息表" method:"get" summary:"ADX短剧基本信息表全量拉取"`
	commonApi.Author
}
type AdxPlayletPullAllDataRes struct {
	commonApi.EmptyRes
}

// 增量拉取
type AdxPlayletPullIncrementalDataReq struct {
	g.Meta `path:"/pullIncrementalData" tags:"ADX短剧基本信息表" method:"get" summary:"ADX短剧基本信息表增量拉取"`
	commonApi.Author
	Date string `p:"date" v:"required#时间不能为空"`
}

type AdxPlayletPullIncrementalDataRes struct {
	commonApi.EmptyRes
}

// AdxPlayletExportReq 导出请求
type AdxPlayletExportReq struct {
	g.Meta `path:"/export" tags:"ADX短剧基本信息表" method:"get" summary:"ADX短剧基本信息表导出"`
	commonApi.Author
	model.AdxPlayletSearchReq
}

// AdxPlayletExportRes 导出响应
type AdxPlayletExportRes struct {
	commonApi.EmptyRes
}

// AdxPlayletAddReq 添加操作请求参数
type AdxPlayletAddReq struct {
	g.Meta `path:"/add" tags:"ADX短剧基本信息表" method:"post" summary:"ADX短剧基本信息表添加"`
	commonApi.Author
	*model.AdxPlayletAddReq
}

// AdxPlayletAddRes 添加操作返回结果
type AdxPlayletAddRes struct {
	commonApi.EmptyRes
}

// AdxPlayletEditReq 修改操作请求参数
type AdxPlayletEditReq struct {
	g.Meta `path:"/edit" tags:"ADX短剧基本信息表" method:"put" summary:"ADX短剧基本信息表修改"`
	commonApi.Author
	*model.AdxPlayletEditReq
}

// AdxPlayletEditRes 修改操作返回结果
type AdxPlayletEditRes struct {
	commonApi.EmptyRes
}

// AdxPlayletGetReq 获取一条数据请求
type AdxPlayletGetReq struct {
	g.Meta `path:"/get" tags:"ADX短剧基本信息表" method:"get" summary:"获取ADX短剧基本信息表信息"`
	commonApi.Author
	PlayletId uint64 `p:"playletId" v:"required#主键必须"` //通过主键获取
}

// AdxPlayletGetRes 获取一条数据结果
type AdxPlayletGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxPlayletInfoRes
}

// GetDetail
type AdxPlayletGetDetailReq struct {
	g.Meta `path:"/getDetail" tags:"ADX短剧基本信息表" method:"get" summary:"获取ADX短剧基本信息表信息"`
	commonApi.Author
	PlayletId uint64 `p:"playletId" v:"required#主键必须"`
}

type AdxPlayletGetDetailRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxPlayletDetialRes
}

// GetDetail2
type AdxPlayletGetDetail2Req struct {
	g.Meta `path:"/getDetail2" tags:"ADX短剧基本信息表" method:"get" summary:"获取ADX短剧基本信息表信息2"`
	commonApi.Author
	PlayletId uint64 `p:"playletId" v:"required#主键必须"`
}

// AdxPlayletDeleteReq 删除数据请求
type AdxPlayletDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX短剧基本信息表" method:"delete" summary:"删除ADX短剧基本信息表"`
	commonApi.Author
	PlayletIds []uint64 `p:"playletIds" v:"required#主键必须"` //通过主键删除
}

// AdxPlayletDeleteRes 删除数据返回
type AdxPlayletDeleteRes struct {
	commonApi.EmptyRes
}
