// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-02-07 16:58:30
// 生成路径: api/v1/member/m_member_flow_record.go
// 生成人：len
// desc:用户流水记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberFlowRecordSearchReq 分页请求参数
type MMemberFlowRecordSearchReq struct {
	g.Meta `path:"/list" tags:"用户流水记录" method:"post" summary:"用户流水记录列表"`
	commonApi.Author
	model.MMemberFlowRecordSearchReq
}

// MMemberFlowRecordSearchRes 列表返回结果
type MMemberFlowRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberFlowRecordSearchRes
}

// MMemberFlowRecordExportReq 导出请求
type MMemberFlowRecordExportReq struct {
	g.Meta `path:"/export" tags:"用户流水记录" method:"get" summary:"用户流水记录导出"`
	commonApi.Author
	model.MMemberFlowRecordSearchReq
}

// MMemberFlowRecordExportRes 导出响应
type MMemberFlowRecordExportRes struct {
	commonApi.EmptyRes
}

// MMemberFlowRecordAddReq 添加操作请求参数
type MMemberFlowRecordAddReq struct {
	g.Meta `path:"/add" tags:"用户流水记录" method:"post" summary:"用户流水记录添加"`
	commonApi.Author
	*model.MMemberFlowRecordAddReq
}

// MMemberFlowRecordAddRes 添加操作返回结果
type MMemberFlowRecordAddRes struct {
	commonApi.EmptyRes
}

// MMemberFlowRecordEditReq 修改操作请求参数
type MMemberFlowRecordEditReq struct {
	g.Meta `path:"/edit" tags:"用户流水记录" method:"put" summary:"用户流水记录修改"`
	commonApi.Author
	*model.MMemberFlowRecordEditReq
}

// MMemberFlowRecordEditRes 修改操作返回结果
type MMemberFlowRecordEditRes struct {
	commonApi.EmptyRes
}

// MMemberFlowRecordGetReq 获取一条数据请求
type MMemberFlowRecordGetReq struct {
	g.Meta `path:"/get" tags:"用户流水记录" method:"get" summary:"获取用户流水记录信息"`
	commonApi.Author
	OrderNum string `p:"orderNum" v:"required#主键必须"` //通过主键获取
}

// MMemberFlowRecordGetRes 获取一条数据结果
type MMemberFlowRecordGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberFlowRecordInfoRes
}

// MMemberFlowRecordDeleteReq 删除数据请求
type MMemberFlowRecordDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户流水记录" method:"post" summary:"删除用户流水记录"`
	commonApi.Author
	OrderNums []string `p:"orderNums" v:"required#主键必须"` //通过主键删除
}

// MMemberFlowRecordDeleteRes 删除数据返回
type MMemberFlowRecordDeleteRes struct {
	commonApi.EmptyRes
}

// MMemberFlowRecordDeleteRepeatReq 删除重复数据请求
type MMemberFlowRecordDeleteRepeatReq struct {
	g.Meta `path:"/delete/repeat" tags:"用户流水记录" method:"post" summary:"删除重复用户流水记录"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

// MMemberFlowRecordDeleteRepeatRes 删除重复数据返回
type MMemberFlowRecordDeleteRepeatRes struct {
	commonApi.EmptyRes
}
