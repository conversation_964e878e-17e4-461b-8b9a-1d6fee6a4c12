// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: internal/app/ad/dao/internal/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdUserInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdUserInfoDao struct {
	table   string              // Table is the underlying table name of the DAO.
	group   string              // Group is the database configuration group name of current DAO.
	columns FqAdUserInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdUserInfoColumns defines and stores column names for table fq_ad_user_info.
type FqAdUserInfoColumns struct {
	DistributorId     string // 渠道ID
	EncryptedDeviceId string // 用户设备id
	OpenId            string // 微信openID
	RegisterTime      string // 注册时间（染色时间，注册时染色）
	PromotionId       string // 推广链id
	PromotionName     string // 推广链名称
	DeviceBrand       string // 用户手机厂商
	MediaSource       string // 投放来源。1：字节
	BookName          string // 短剧名称
	BookSource        string // 书本来源（短剧id）
	Clickid           string // 点击ID（仅巨量快应用一跳投放有该数据）
	Oaid              string // oaid
	Caid              string // caid
	Adid              string // adid（仅巨量快应用一跳投放有该数据）
	Creativeid        string // creativeid（仅巨量快应用一跳投放有该数据）
	Creativetype      string // creativetype（仅巨量快应用一跳投放有该数据）
	Ip                string // ip
	UserAgent         string // user_agent 可能会出现截断的和完整的两种，1.7 版本后新增的记录都为完整的 ua
	Timestamp         string // 最近加桌时间戳，时间为0则用户未加桌，H5书城口径为关注公众号
	OptimizerAccount  string // 优化师返回优化师账户邮箱，主管账户返回：RootOptimizerAccount
	EcpmAmount        string // 广告激励总收入,单位分
	EcpmCnt           string // 广告点击次数
	ExternalId        string // 企微用户企微id
	ProjectId         string // 巨量2.0广告计划组ID
	AdIdV2            string // 巨量2.0广告计划ID
	Mid               string // 素材id（分别代表图片、标题、视频、试玩、落地页）
	BalanceAmount     string // 余额，iaa不需要关注
	RechargeAmount    string // 充值金额，iaa不需要关注
	RechargeTimes     string // 充值次数，iaa不需要关注
}

var fqAdUserInfoColumns = FqAdUserInfoColumns{
	DistributorId:     "distributor_id",
	EncryptedDeviceId: "encrypted_device_id",
	OpenId:            "open_id",
	RegisterTime:      "register_time",
	PromotionId:       "promotion_id",
	PromotionName:     "promotion_name",
	DeviceBrand:       "device_brand",
	MediaSource:       "media_source",
	BookName:          "book_name",
	BookSource:        "book_source",
	Clickid:           "clickid",
	Oaid:              "oaid",
	Caid:              "caid",
	Adid:              "adid",
	Creativeid:        "creativeid",
	Creativetype:      "creativetype",
	Ip:                "ip",
	UserAgent:         "user_agent",
	Timestamp:         "timestamp",
	OptimizerAccount:  "optimizer_account",
	EcpmAmount:        "ecpm_amount",
	EcpmCnt:           "ecpm_cnt",
	ExternalId:        "external_id",
	ProjectId:         "project_id",
	AdIdV2:            "ad_id_v2",
	Mid:               "mid",
	BalanceAmount:     "balance_amount",
	RechargeAmount:    "recharge_amount",
	RechargeTimes:     "recharge_times",
}

// NewFqAdUserInfoDao creates and returns a new DAO object for table data access.
func NewFqAdUserInfoDao() *FqAdUserInfoDao {
	return &FqAdUserInfoDao{
		group:   "default",
		table:   "fq_ad_user_info",
		columns: fqAdUserInfoColumns,
	}
}

func NewFqAdUserInfoAnalyticDao() *FqAdUserInfoDao {
	return &FqAdUserInfoDao{
		group:   "analyticDB",
		table:   "fq_ad_user_info",
		columns: fqAdUserInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdUserInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdUserInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdUserInfoDao) Columns() FqAdUserInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdUserInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdUserInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdUserInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
