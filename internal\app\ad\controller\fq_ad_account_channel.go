// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: internal/app/ad/controller/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type fqAdAccountChannelController struct {
	systemController.BaseController
}

var FqAdAccountChannel = new(fqAdAccountChannelController)

// List 列表
func (c *fqAdAccountChannelController) List(ctx context.Context, req *ad.FqAdAccountChannelSearchReq) (res *ad.FqAdAccountChannelSearchRes, err error) {
	res = new(ad.FqAdAccountChannelSearchRes)
	res.FqAdAccountChannelSearchRes, err = service.FqAdAccountChannel().List(ctx, &req.FqAdAccountChannelSearchReq)
	return
}

// FqChannelStatic
func (c *fqAdAccountChannelController) FqChannelStatic(ctx context.Context, req *ad.FqChannelStaticReq) (res *ad.FqChannelStaticRes, err error) {
	res = new(ad.FqChannelStaticRes)
	res.FqChannelStaticRes, err = service.FqAdAccountChannel().FqChannelStatic(ctx, &req.FqChannelStaticReq)
	return
}

// Export 导出excel
func (c *fqAdAccountChannelController) FqChannelStaticExport(ctx context.Context, req *ad.FqChannelStaticExportReq) (res *ad.FqAdAccountExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqChannelStaticListRes
		listRes  *model.FqChannelStaticRes
		//表头
		tableHead = []interface{}{"日期", "番茄渠道", "当日新增用户充值", "当日新增付费人数", "消耗", "总充值金额", "广告收入", "当日新增广告收入", "ROI", "微小安卓充值", "微小iOS充值", "微小总充值", "抖小安卓充值", "抖小iOS充值", "抖小总充值", "充值人数", "客单价", "人均充值次数"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.FqAdAccountChannel().FqChannelStatic(ctx, &req.FqChannelStaticReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.CreateTime,
				v.NickName,
				v.NewUserAmount,
				v.NewUserRechargeNums,
				v.AccountCoinConsume,
				v.TotalAmount,
				v.TotalAdUp,
				v.NewUserAdUp,
				v.DayRoi,
				v.WechatAndroidRecharge,
				v.WechatIosRecharge,
				v.WechatRechargeAmount,
				v.DyAndroidRecharge,
				v.DyIosRecharge,
				v.DyRechargeAmount,
				v.RechargeNums,
				v.CustomerPrice,
				v.AvgRechargeTimes,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("账号数据统计")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取番茄渠道
func (c *fqAdAccountChannelController) Get(ctx context.Context, req *ad.FqAdAccountChannelGetReq) (res *ad.FqAdAccountChannelGetRes, err error) {
	res = new(ad.FqAdAccountChannelGetRes)
	res.FqAdAccountChannelInfoRes, err = service.FqAdAccountChannel().GetByChannelDistributorId(ctx, req.ChannelDistributorId)
	return
}

// Add 添加番茄渠道
func (c *fqAdAccountChannelController) Add(ctx context.Context, req *ad.FqAdAccountChannelAddReq) (res *ad.FqAdAccountChannelAddRes, err error) {
	err = service.FqAdAccountChannel().Add(ctx, req.FqAdAccountChannelAddReq)
	return
}

// Edit 修改番茄渠道
func (c *fqAdAccountChannelController) Edit(ctx context.Context, req *ad.FqAdAccountChannelEditReq) (res *ad.FqAdAccountChannelEditRes, err error) {
	err = service.FqAdAccountChannel().Edit(ctx, req.FqAdAccountChannelEditReq)
	return
}

// Delete 删除番茄渠道
func (c *fqAdAccountChannelController) Delete(ctx context.Context, req *ad.FqAdAccountChannelDeleteReq) (res *ad.FqAdAccountChannelDeleteRes, err error) {
	err = service.FqAdAccountChannel().Delete(ctx, req.ChannelDistributorIds)
	return
}

// SyncFqAdAccountChannel 同步番茄渠道
func (c *fqAdAccountChannelController) SyncFqAdAccountChannel(ctx context.Context, req *ad.SyncFqAdAccountChannelReq) (res *ad.SyncFqAdAccountChannelRes, err error) {
	err = service.FqAdAccountChannel().SyncFqAdAccountChannel(ctx)
	return
}
