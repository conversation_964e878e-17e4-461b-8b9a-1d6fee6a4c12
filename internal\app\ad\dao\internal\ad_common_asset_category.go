// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: internal/app/ad/dao/internal/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdCommonAssetCategoryDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdCommonAssetCategoryDao struct {
	table   string                       // Table is the underlying table name of the DAO.
	group   string                       // Group is the database configuration group name of current DAO.
	columns AdCommonAssetCategoryColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdCommonAssetCategoryColumns defines and stores column names for table ad_common_asset_category.
type AdCommonAssetCategoryColumns struct {
	Id        string //
	Category  string // 分类
	UserId    string // 创建者
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 删除时间
}

var adCommonAssetCategoryColumns = AdCommonAssetCategoryColumns{
	Id:        "id",
	Category:  "category",
	UserId:    "user_id",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewAdCommonAssetCategoryDao creates and returns a new DAO object for table data access.
func NewAdCommonAssetCategoryDao() *AdCommonAssetCategoryDao {
	return &AdCommonAssetCategoryDao{
		group:   "default",
		table:   "ad_common_asset_category",
		columns: adCommonAssetCategoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdCommonAssetCategoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdCommonAssetCategoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdCommonAssetCategoryDao) Columns() AdCommonAssetCategoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdCommonAssetCategoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdCommonAssetCategoryDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdCommonAssetCategoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
