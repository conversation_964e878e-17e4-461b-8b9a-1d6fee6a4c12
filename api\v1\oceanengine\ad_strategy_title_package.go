// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:08
// 生成路径: api/v1/oceanengine/ad_strategy_title_package.go
// 生成人：gfast
// desc:巨量广告搭建-标题包相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyTitlePackageSearchReq 分页请求参数
type AdStrategyTitlePackageSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-标题包" method:"get" summary:"巨量广告搭建-标题包列表"`
	commonApi.Author
	model.AdStrategyTitlePackageSearchReq
}

// AdStrategyTitlePackageSearchRes 列表返回结果
type AdStrategyTitlePackageSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTitlePackageSearchRes
}

// AdStrategyTitlePackageAddReq 添加操作请求参数
type AdStrategyTitlePackageAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-标题包" method:"post" summary:"巨量广告搭建-标题包添加"`
	commonApi.Author
	*model.AdStrategyTitlePackageAddReq
}

// AdStrategyTitlePackageAddRes 添加操作返回结果
type AdStrategyTitlePackageAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyTitlePackageEditReq 修改操作请求参数
type AdStrategyTitlePackageEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-标题包" method:"put" summary:"巨量广告搭建-标题包修改"`
	commonApi.Author
	*model.AdStrategyTitlePackageEditReq
}

// AdStrategyTitlePackageEditRes 修改操作返回结果
type AdStrategyTitlePackageEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyTitlePackageGetReq 获取一条数据请求
type AdStrategyTitlePackageGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-标题包" method:"get" summary:"获取巨量广告搭建-标题包信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyTitlePackageGetRes 获取一条数据结果
type AdStrategyTitlePackageGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTitlePackageInfoRes
}

// AdStrategyTitlePackageDeleteReq 删除数据请求
type AdStrategyTitlePackageDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-标题包" method:"delete" summary:"删除巨量广告搭建-标题包"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyTitlePackageDeleteRes 删除数据返回
type AdStrategyTitlePackageDeleteRes struct {
	commonApi.EmptyRes
}
