// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-13 10:42:38
// 生成路径: api/v1/ad/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdAppConfigSearchReq 分页请求参数
type AdAppConfigSearchReq struct {
	g.Meta `path:"/list" tags:"广告应用配置表" method:"get" summary:"广告应用配置表列表"`
	commonApi.Author
	model.AdAppConfigSearchReq
}

// AdAppConfigSearchRes 列表返回结果
type AdAppConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAppConfigSearchRes
}

// AdAppConfigAddReq 添加操作请求参数
type AdAppConfigAddReq struct {
	g.Meta `path:"/add" tags:"广告应用配置表" method:"post" summary:"广告应用配置表添加"`
	commonApi.Author
	*model.AdAppConfigAddReq
}

// AdAppConfigAddRes 添加操作返回结果
type AdAppConfigAddRes struct {
	commonApi.EmptyRes
}

// AdAppConfigEditReq 修改操作请求参数
type AdAppConfigEditReq struct {
	g.Meta `path:"/edit" tags:"广告应用配置表" method:"put" summary:"广告应用配置表修改"`
	commonApi.Author
	*model.AdAppConfigEditReq
}

// AdAppConfigEditRes 修改操作返回结果
type AdAppConfigEditRes struct {
	commonApi.EmptyRes
}

// AdAppConfigGetReq 获取一条数据请求
type AdAppConfigGetReq struct {
	g.Meta `path:"/get" tags:"广告应用配置表" method:"get" summary:"获取广告应用配置表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAppConfigGetRes 获取一条数据结果
type AdAppConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAppConfigInfoRes
}

// AdAppConfigDeleteReq 删除数据请求
type AdAppConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告应用配置表" method:"post" summary:"删除广告应用配置表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAppConfigDeleteRes 删除数据返回
type AdAppConfigDeleteRes struct {
	commonApi.EmptyRes
}

// GetAuthUrlReq
type GetAuthUrlReq struct {
	g.Meta `path:"/getAuthUrl" tags:"广告应用配置表" method:"get" summary:"获取授权链接"`
	commonApi.Author
	AppType           int32   `p:"appType" v:"required#应用类型不能为空" dc:"应用类型 1：巨量 2：广点通"`
	EvnType           int32   `p:"evnType"  dc:"环境类型 1：投放中心 2：后台管理系统"`
	AuthUserType      *int32  `p:"authUserType" dc:"授权用户类型： 1：纵横组织 2：方舟"`
	MajordomoUserName *string `p:"majordomoUserName" dc:"授权用户ID"`
}

// GetAuthUrlRes 删除数据返回
type GetAuthUrlRes struct {
	commonApi.EmptyRes
	AuthUrl string `json:"authUrl"`
}
