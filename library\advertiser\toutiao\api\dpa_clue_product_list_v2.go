/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DpaClueProductListV2ApiService DpaClueProductListV2Api service
type DpaClueProductListV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DpaClueProductListV2Request
}

type DpaClueProductListV2Request struct {
	AdvertiserId *int64
	Page         *int64
	PageSize     *int64
	Filtering    *models.DpaClueProductListV2Filtering
}

func (r *DpaClueProductListV2ApiService) SetCfg(cfg *conf.Configuration) *DpaClueProductListV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DpaClueProductListV2ApiService) DpaClueProductListV2Request(dpaClueProductListV2Request DpaClueProductListV2Request) *DpaClueProductListV2ApiService {
	r.Request = &dpaClueProductListV2Request
	return r
}

func (r *DpaClueProductListV2ApiService) AccessToken(accessToken string) *DpaClueProductListV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DpaClueProductListV2ApiService) Do() (data *models.DpaClueProductListV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dpa/clue_product/list/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.Page == nil {
		return nil, errors.New("page is required and must be specified")
	}
	if r.Request.PageSize == nil {
		return nil, errors.New("pageSize is required and must be specified")
	}
	if *r.Request.PageSize < 1 {
		return nil, errors.New("pageSize must be greater than 1")
	}
	if *r.Request.PageSize > 100 {
		return nil, errors.New("pageSize must be less than 100")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.Request.Filtering)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.DpaClueProductListV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DpaClueProductListV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dpa/detail/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
