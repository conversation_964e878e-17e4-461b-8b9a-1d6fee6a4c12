// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:34:02
// 生成路径: internal/app/adx/logic/adx_publisher.go
// 生成人：cq
// desc:ADX公司信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdxPublisher(New())
}

func New() service.IAdxPublisher {
	return &sAdxPublisher{}
}

type sAdxPublisher struct{}

func (s *sAdxPublisher) List(ctx context.Context, req *model.AdxPublisherSearchReq) (listRes *model.AdxPublisherSearchRes, err error) {
	listRes = new(model.AdxPublisherSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxPublisher.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxPublisher.Columns().Id+" = ?", req.Id)
		}
		if req.PublisherName != "" {
			m = m.Where(dao.AdxPublisher.Columns().PublisherName+" like ?", "%"+req.PublisherName+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxPublisherListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxPublisherListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxPublisherListRes{
				Id:            v.Id,
				PublisherName: v.PublisherName,
			}
		}
	})
	return
}

func (s *sAdxPublisher) GetExportData(ctx context.Context, req *model.AdxPublisherSearchReq) (listRes []*model.AdxPublisherInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxPublisher.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxPublisher.Columns().Id+" = ?", req.Id)
		}
		if req.PublisherName != "" {
			m = m.Where(dao.AdxPublisher.Columns().PublisherName+" like ?", "%"+req.PublisherName+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxPublisher) GetById(ctx context.Context, id uint64) (res *model.AdxPublisherInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxPublisher.Ctx(ctx).WithAll().Where(dao.AdxPublisher.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxPublisher) GetByName(ctx context.Context, name string, likeQuery bool) (res []*model.AdxPublisherInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxPublisher.Ctx(ctx).WithAll()
		if likeQuery {
			m = m.WhereLike(dao.AdxPublisher.Columns().PublisherName, "%"+name+"%")
		} else {
			m = m.Where(dao.AdxPublisher.Columns().PublisherName, name)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxPublisher) Add(ctx context.Context, req *model.AdxPublisherAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPublisher.Ctx(ctx).Insert(do.AdxPublisher{
			Id:            req.Id,
			PublisherName: req.PublisherName,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxPublisher) BatchAdd(ctx context.Context, batchReq []*model.AdxPublisherAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdxPublisher, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdxPublisher{
				Id:            v.Id,
				PublisherName: v.PublisherName,
			}
		}
		_, err = dao.AdxPublisher.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxPublisher) Edit(ctx context.Context, req *model.AdxPublisherEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPublisher.Ctx(ctx).WherePri(req.Id).Update(do.AdxPublisher{
			PublisherName: req.PublisherName,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxPublisher) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPublisher.Ctx(ctx).Delete(dao.AdxPublisher.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdxPublisher) RunSyncAdxPublisherTask(ctx context.Context, req *model.AdxPublisherSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		startTime := req.StartTime
		endTime := req.EndTime
		for {
			if startTime > endTime {
				break
			}
			errors := s.SyncAdxPublisher(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdxPublisher) SyncAdxPublisherTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdxPublisherLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdxPublisherLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
		err = s.SyncAdxPublisher(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "同步ADX公司失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncAdxPublisherTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步ADX公司，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxPublisher) SyncAdxPublisher(ctx context.Context, updateDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var id int64 = 0
		var pageSize = 500
		var pullType = 2
		if updateDate == "" {
			pullType = 1
		}
		var pageIdKey string
		var ttlInSeconds int64
		if pullType == 1 {
			pageIdKey = commonConsts.PlatAdxPublisherPageId
		} else {
			pageIdKey = fmt.Sprintf("%s%s", commonConsts.PlatAdxPublisherDayPageId, updateDate)
			ttlInSeconds = commonConsts.PlatAdxPageIdTtlSeconds
		}
		data, _ := commonService.RedisCache().Get(ctx, pageIdKey)
		if !data.IsNil() {
			id = data.Int64()
		}
		for {
			publisherRes, err1 := api.GetAdxApiClient().PublisherService.SetReq(api.PublisherRequest{
				Id:         id,
				PageSize:   pageSize,
				PullType:   pullType,
				UpdateDate: updateDate,
			}).Do()
			if err1 != nil {
				g.Log().Error(ctx, err1)
				break
			}
			if len(publisherRes.Content) == 0 {
				break
			}
			var batchReq []*model.AdxPublisherAddReq
			for _, v := range publisherRes.Content {
				batchReq = append(batchReq, &model.AdxPublisherAddReq{
					Id:            uint64(v.Id),
					PublisherName: v.PublisherName,
				})
			}
			err = s.BatchAdd(ctx, batchReq)
			if err != nil {
				g.Log().Error(ctx, err)
				break
			}
			if publisherRes.Page.PageId == id {
				break
			}
			id = publisherRes.Page.PageId
			if ttlInSeconds > 0 {
				_ = commonService.RedisCache().SetEX(ctx, pageIdKey, id, ttlInSeconds)
			} else {
				_, _ = commonService.RedisCache().Set(ctx, pageIdKey, id)
			}
		}
	})
	return
}
