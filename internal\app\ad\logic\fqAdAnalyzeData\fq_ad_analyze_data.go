// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: internal/app/ad/logic/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/fq/api"
	fqModel "github.com/tiger1103/gfast/v3/library/advertiser/fq/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"time"
)

func init() {
	service.RegisterFqAdAnalyzeData(New())
}

func New() service.IFqAdAnalyzeData {
	return &sFqAdAnalyzeData{}
}

type sFqAdAnalyzeData struct{}

func (s *sFqAdAnalyzeData) List(ctx context.Context, req *model.FqAdAnalyzeDataSearchReq) (listRes *model.FqAdAnalyzeDataSearchRes, err error) {
	listRes = new(model.FqAdAnalyzeDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAnalyzeData.Ctx(ctx).As("fqdata")
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " fqdata.distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " fqdata.distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)

		}

		if req.Id != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().Id+" = ?", req.Id)
		}
		if req.FqDistributorId != "" {
			list, _ := service.FqAdAccountChannel().GetByDistributorId(ctx, gconv.Int64(req.FqDistributorId))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId, ids)
				}
			}
		}
		if len(req.FqDistributorIds) > 0 {
			list, _ := service.FqAdAccountChannel().GetByDistributorIds(ctx, gconv.SliceInt64(req.FqChannelDistributorIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId, ids)
				}
			}
		}

		if len(req.FqChannelDistributorId) > 0 {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId+" = ?", gconv.Int64(req.FqChannelDistributorId))
		}
		if len(req.FqChannelDistributorIds) > 0 {
			m = m.WhereIn("fqdata."+dao.FqAdAnalyzeData.Columns().DistributorId, req.FqChannelDistributorIds)
		}

		if req.PromotionId != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().PromotionId+" = ?", gconv.Int64(req.PromotionId))
		}
		if req.AddDesktopUserNum != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().AddDesktopUserNum+" = ?", gconv.Int64(req.AddDesktopUserNum))
		}
		if req.PaidUserNum != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().PaidUserNum+" = ?", gconv.Int64(req.PaidUserNum))
		}
		if req.RechargeAmount != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().RechargeAmount+" = ?", gconv.Int64(req.RechargeAmount))
		}
		if req.Uv != "" {
			m = m.Where("fqdata."+dao.FqAdAnalyzeData.Columns().Uv+" = ?", gconv.Int64(req.Uv))
		}

		if len(req.ChannelCode) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code = "+fmt.Sprintf("'%s'", req.ChannelCode))
		} else if len(req.ChannelCodes) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code in "+fmt.Sprintf("(%s)", libUtils.BuildSqlInStr(req.ChannelCodes)))
		} else {
			m = m.LeftJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id ")
		}
		if req.PitcherId > 0 {
			m = m.InnerJoin(systemDao.SysUser.Table(), "su", fmt.Sprintf("su.id = sc.user_id and su.id = %v", req.PitcherId))
		} else {
			m = m.LeftJoin(systemDao.SysUser.Table(), "su", "su.id = sc.user_id")
		}
		if req.DistributorId > 0 {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
		} else {
			m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
				LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "fqdata.recharge_amount desc"

		if req.OrderBy != "" {
			order = "fqdata." + libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
		}

		var summary = new(model.FqAdAnalyzeDataSummary)
		err = m.FieldSum("fqdata."+dao.FqAdAnalyzeData.Columns().AddDesktopUserNum, "addDesktopUserNum").
			FieldSum("fqdata."+dao.FqAdAnalyzeData.Columns().Uv, "uv").
			FieldSum("fqdata."+dao.FqAdAnalyzeData.Columns().PaidUserNum, "paidUserNum").
			FieldSum("fqdata."+dao.FqAdAnalyzeData.Columns().RechargeAmount, "rechargeAmount").
			Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取统计数据失败")
		listRes.Summary = *summary
		listRes.Summary.RechargeAmount = gconv.Float64(summary.RechargeAmount) / 100

		m = m.Fields("fqdata.*, su.user_name as userName , su.id as userId ,de.leader as distributorName  , fq_promotion_id, channel_code")
		var res []*model.FqAdAnalyzeDataListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		disIds := make([]int64, 0)
		listRes.List = make([]*model.FqAdAnalyzeDataListRes, len(res))
		for k, v := range res {
			disIds = append(disIds, v.DistributorId)
			listRes.List[k] = &model.FqAdAnalyzeDataListRes{
				Id:                v.Id,
				DistributorId:     v.DistributorId,
				PromotionId:       v.PromotionId,
				AddDesktopUserNum: v.AddDesktopUserNum,
				PaidRate:          v.PaidRate,
				PaidUserNum:       v.PaidUserNum,
				RechargeAmount:    gconv.Float64(v.RechargeAmount) / 100,
				Uv:                v.Uv,
				ChannelCode:       v.ChannelCode,
				UserId:            v.UserId,
				UserName:          v.UserName,
				DistributorName:   v.DistributorName,
				CreatedAt:         v.CreatedAt,
			}
		}

		// 根据分销id获取分销信息
		var distributorInfo = make([]model.FqAdAccountInfoRes, 0)
		channelList, _ := service.FqAdAccountChannel().GetByChannelDistributorIds(ctx, disIds)
		disIds = make([]int64, 0)
		for _, item := range channelList {
			disIds = append(disIds, item.DistributorId)
		}
		err = dao.FqAdAccount.Ctx(ctx).WhereIn("distributor_id", disIds).Scan(&distributorInfo)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if len(distributorInfo) > 0 {
			for i, dataListRes := range listRes.List {
				for _, infoRes := range channelList {
					if dataListRes.DistributorId == infoRes.ChannelDistributorId {
						listRes.List[i].FqChannelDistributorId = gconv.String(infoRes.ChannelDistributorId)
						listRes.List[i].FqChannel = infoRes.NickName
						listRes.List[i].AppId = gconv.String(infoRes.AppId)
						listRes.List[i].AppName = infoRes.AppName
						for _, item := range distributorInfo {
							if item.DistributorId == infoRes.DistributorId {
								listRes.List[i].FqAccount = item.AccountName
							}
						}
					}
				}
			}
		}

	})

	return
}

// Pull
func (s *sFqAdAnalyzeData) Pull(ctx context.Context) (count int, err error) {
	count = 0
	err = g.Try(ctx, func(ctx context.Context) {
		var pageNo = 1
		var pageSize = 100
		for {
			fqAdAccountChannels, _ := service.FqAdAccountChannel().GetChannelList(ctx, &model.FqAdAccountChannelSearchReq{
				PageReq: comModel.PageReq{
					PageNum:  pageNo,
					PageSize: pageSize,
				},
			})
			if len(fqAdAccountChannels) == 0 {
				break
			}
			for _, accountInfoRes := range fqAdAccountChannels {
				nextOffSet := int64(0)
				for {
					var fqAdAnalyzeDataList = make([]*model.FqAdAnalyzeDataAddReq, 0)
					var fqAdAnalyzeDataDayList = make([]*model.FqAdAnalyzeDataDayAddReq, 0)
					roiRes, err := api.GetAdFQIClient().RoiAnalyzeService.SetReq(fqModel.RoiAnalyzeReq{
						Limit:  10,
						Offset: nextOffSet,
					}).SetBase(api.FqBaseReq{
						DistributorID: accountInfoRes.ChannelDistributorId,
						Secret:        accountInfoRes.SecretKey,
					}).Do()
					if err != nil {
						if err.Error() == "Internal Service Error" {
							time.Sleep(time.Second * 1)
							roiRes, err = api.GetAdFQIClient().RoiAnalyzeService.SetReq(fqModel.RoiAnalyzeReq{
								Limit:  10,
								Offset: nextOffSet,
							}).SetBase(api.FqBaseReq{
								DistributorID: accountInfoRes.ChannelDistributorId,
								Secret:        accountInfoRes.SecretKey,
							}).Do()
						} else {
							g.Log().Error(ctx, fmt.Sprintf("------------- GetAdFQIClient RoiAnalyzeService err：%v --------------------", err))
						}
						//g.Log().Error(ctx, fmt.Sprintf("------------- GetAdFQIClient RoiAnalyzeService err：%v --------------------", err))
					}
					// 数据存储
					if len(roiRes.Result) > 0 {

						for _, result := range roiRes.Result {
							count++
							fqAdAnalyzeDataList = append(fqAdAnalyzeDataList, &model.FqAdAnalyzeDataAddReq{
								DistributorId:     accountInfoRes.ChannelDistributorId,
								PromotionId:       fmt.Sprintf("%v", result.Overview.OBSOLETEPromotionId),
								AddDesktopUserNum: result.Overview.AddDesktopUserNum,
								PaidRate:          result.Overview.PaidRate,
								PaidUserNum:       result.Overview.PaidUserNum,
								RechargeAmount:    result.Overview.RechargeAmount,
								Uv:                result.Overview.Uv,
							})
							//if result.Overview.OBSOLETEPromotionId == **************** {
							//	g.Log().Info(ctx, "------------- GetAdFQ:")
							//}
							if len(result.Daily) > 0 {
								var rechargeAmount = int64(0)
								for _, daily := range result.Daily {
									roiDetail := "{}"
									if len(daily.RoiDetail) > 0 {
										for _, i := range daily.RoiDetail {
											if i.Date == 0 {
												rechargeAmount = i.RechargeAmount
												break
											}
										}
										byteArray, innerErr := json.Marshal(daily.RoiDetail)
										if innerErr != nil {
											g.Log().Error(ctx, "json.Marshal err：", innerErr)
										} else {
											roiDetail = string(byteArray)
										}
									}

									fqAdAnalyzeDataDayList = append(fqAdAnalyzeDataDayList, &model.FqAdAnalyzeDataDayAddReq{
										DistributorId:  accountInfoRes.ChannelDistributorId,
										PromotionId:    fmt.Sprintf("%v", result.Overview.OBSOLETEPromotionId),
										StatDate:       daily.Date,
										AddDesktopNum:  daily.AddDesktopNum,
										RechargeNum:    daily.RechargeNum,
										UserNum:        daily.UserNum,
										RechargeAmount: rechargeAmount,
										RoiDetail:      roiDetail,
									})
								}
							}

						}
					}
					if len(fqAdAnalyzeDataList) > 0 {
						err = service.FqAdAnalyzeData().BatchAdd(ctx, fqAdAnalyzeDataList)
						if err != nil {
							g.Log().Error(ctx, fmt.Sprintf("------------- FqAdAnalyzeData().BatchAdd err：%v --------------------", err))
						}
					}
					if len(fqAdAnalyzeDataDayList) > 0 {
						err = service.FqAdAnalyzeDataDay().BatchAdd(ctx, fqAdAnalyzeDataDayList)
						if err != nil {
							g.Log().Error(ctx, fmt.Sprintf("------------- FqAdAnalyzeDataDay().BatchAdd err：%v --------------------", err))
						}

					}
					if !roiRes.HasMore {
						break
					}
					nextOffSet = roiRes.NextOffset
				}
			}
			pageNo++
		}
		//var fqAccount = make([]model.FqAdAccountInfoRes, 0)
		//err = dao.FqAdAccount.Ctx(ctx).WithAll().Scan(&fqAccount)
		//liberr.ErrIsNil(ctx, err, "获取信息失败")
		//if len(fqAccount) > 0 {
		//	for _, accountInfoRes := range fqAccount {
		//		nextOffSet := int64(0)
		//		for {
		//			var fqAdAnalyzeDataList = make([]*model.FqAdAnalyzeDataAddReq, 0)
		//			var fqAdAnalyzeDataDayList = make([]*model.FqAdAnalyzeDataDayAddReq, 0)
		//			roiRes, err := api.GetAdFQIClient().RoiAnalyzeService.SetReq(fqModel.RoiAnalyzeReq{
		//				Limit:  10,
		//				Offset: nextOffSet,
		//			}).SetBase(api.FqBaseReq{
		//				DistributorID: accountInfoRes.DistributorId,
		//				Secret:        accountInfoRes.SecretKey,
		//			}).Do()
		//			if err != nil {
		//				g.Log().Error(ctx, fmt.Sprintf("------------- GetAdFQIClient RoiAnalyzeService err：%v --------------------", err))
		//			}
		//			// 数据存储
		//			if len(roiRes.Result) > 0 {
		//
		//				for _, result := range roiRes.Result {
		//					count++
		//					fqAdAnalyzeDataList = append(fqAdAnalyzeDataList, &model.FqAdAnalyzeDataAddReq{
		//						DistributorId:     accountInfoRes.DistributorId,
		//						PromotionId:       fmt.Sprintf("%v", result.Overview.OBSOLETEPromotionId),
		//						AddDesktopUserNum: result.Overview.AddDesktopUserNum,
		//						PaidRate:          result.Overview.PaidRate,
		//						PaidUserNum:       result.Overview.PaidUserNum,
		//						RechargeAmount:    result.Overview.RechargeAmount,
		//						Uv:                result.Overview.Uv,
		//					})
		//					//if result.Overview.OBSOLETEPromotionId == **************** {
		//					//	g.Log().Info(ctx, "------------- GetAdFQ:")
		//					//}
		//					if len(result.Daily) > 0 {
		//						var rechargeAmount = int64(0)
		//						for _, daily := range result.Daily {
		//							roiDetail := "{}"
		//							if len(daily.RoiDetail) > 0 {
		//								for _, i := range daily.RoiDetail {
		//									if i.Date == 0 {
		//										rechargeAmount = i.RechargeAmount
		//										break
		//									}
		//								}
		//								byteArray, innerErr := json.Marshal(daily.RoiDetail)
		//								if innerErr != nil {
		//									g.Log().Error(ctx, "json.Marshal err：", innerErr)
		//								} else {
		//									roiDetail = string(byteArray)
		//								}
		//							}
		//
		//							fqAdAnalyzeDataDayList = append(fqAdAnalyzeDataDayList, &model.FqAdAnalyzeDataDayAddReq{
		//								DistributorId:  accountInfoRes.DistributorId,
		//								PromotionId:    fmt.Sprintf("%v", result.Overview.OBSOLETEPromotionId),
		//								StatDate:       daily.Date,
		//								AddDesktopNum:  daily.AddDesktopNum,
		//								RechargeNum:    daily.RechargeNum,
		//								UserNum:        daily.UserNum,
		//								RechargeAmount: rechargeAmount,
		//								RoiDetail:      roiDetail,
		//							})
		//						}
		//					}
		//
		//				}
		//			}
		//			if len(fqAdAnalyzeDataList) > 0 {
		//				err = service.FqAdAnalyzeData().BatchAdd(ctx, fqAdAnalyzeDataList)
		//				if err != nil {
		//					g.Log().Error(ctx, fmt.Sprintf("------------- FqAdAnalyzeData().BatchAdd err：%v --------------------", err))
		//				}
		//			}
		//			if len(fqAdAnalyzeDataDayList) > 0 {
		//				err = service.FqAdAnalyzeDataDay().BatchAdd(ctx, fqAdAnalyzeDataDayList)
		//				if err != nil {
		//					g.Log().Error(ctx, fmt.Sprintf("------------- FqAdAnalyzeDataDay().BatchAdd err：%v --------------------", err))
		//				}
		//
		//			}
		//			if !roiRes.HasMore {
		//				break
		//			}
		//			nextOffSet = roiRes.NextOffset
		//		}
		//
		//	}
		//}

	})
	return
}

// TimedPull 定时任务拉取
func (s *sFqAdAnalyzeData) TimedPull(ctx context.Context, statDate string) (count int, err error) {
	channelRechargeStatKey := model.FqAdAnalyzeDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullDate ")
		return 0, err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	count, err = s.Pull(innerCtx)
	if err != nil {
		g.Log().Error(ctx, "Pull err：", err)
	}
	return

}

func (s *sFqAdAnalyzeData) GetExportData(ctx context.Context, req *model.FqAdAnalyzeDataSearchReq) (listRes []*model.FqAdAnalyzeDataInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAnalyzeData.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().Id+" = ?", req.Id)
		}
		if req.FqDistributorId != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().DistributorId+" = ?", gconv.Int64(req.DistributorId))
		}
		if len(req.FqDistributorIds) > 0 {
			m = m.WhereIn(dao.FqAdAnalyzeData.Columns().DistributorId, req.FqDistributorIds)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().PromotionId+" = ?", gconv.Int64(req.PromotionId))
		}
		if req.AddDesktopUserNum != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().AddDesktopUserNum+" = ?", gconv.Int64(req.AddDesktopUserNum))
		}
		if req.PaidUserNum != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().PaidUserNum+" = ?", gconv.Int64(req.PaidUserNum))
		}
		if req.RechargeAmount != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().RechargeAmount+" = ?", gconv.Int64(req.RechargeAmount))
		}
		if req.Uv != "" {
			m = m.Where(dao.FqAdAnalyzeData.Columns().Uv+" = ?", gconv.Int64(req.Uv))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.FqAdAnalyzeData.Columns().CreatedAt+" >=? AND "+dao.FqAdAnalyzeData.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdAnalyzeData) GetById(ctx context.Context, id int) (res *model.FqAdAnalyzeDataInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAnalyzeData.Ctx(ctx).WithAll().Where(dao.FqAdAnalyzeData.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAnalyzeData) Add(ctx context.Context, req *model.FqAdAnalyzeDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAnalyzeData.Ctx(ctx).Insert(do.FqAdAnalyzeData{
			DistributorId:     req.DistributorId,
			PromotionId:       req.PromotionId,
			AddDesktopUserNum: req.AddDesktopUserNum,
			PaidRate:          req.PaidRate,
			PaidUserNum:       req.PaidUserNum,
			RechargeAmount:    req.RechargeAmount,
			Uv:                req.Uv,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// BatchAdd
func (s *sFqAdAnalyzeData) BatchAdd(ctx context.Context, req []*model.FqAdAnalyzeDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var list []do.FqAdAnalyzeData
		for _, v := range req {
			list = append(list, do.FqAdAnalyzeData{
				DistributorId:     v.DistributorId,
				PromotionId:       v.PromotionId,
				AddDesktopUserNum: v.AddDesktopUserNum,
				PaidRate:          v.PaidRate,
				PaidUserNum:       v.PaidUserNum,
				RechargeAmount:    v.RechargeAmount,
				Uv:                v.Uv,
				CreatedAt:         gtime.Now(),
			})
		}
		_, err = dao.FqAdAnalyzeData.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdAnalyzeData) Edit(ctx context.Context, req *model.FqAdAnalyzeDataEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAnalyzeData.Ctx(ctx).WherePri(req.Id).Update(do.FqAdAnalyzeData{
			DistributorId:     req.DistributorId,
			PromotionId:       req.PromotionId,
			AddDesktopUserNum: req.AddDesktopUserNum,
			PaidRate:          req.PaidRate,
			PaidUserNum:       req.PaidUserNum,
			RechargeAmount:    req.RechargeAmount,
			Uv:                req.Uv,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdAnalyzeData) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAnalyzeData.Ctx(ctx).Delete(dao.FqAdAnalyzeData.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
