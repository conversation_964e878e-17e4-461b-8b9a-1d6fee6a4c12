// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: api/v1/ad/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdUserPaymentRecordSearchReq 分页请求参数
type FqAdUserPaymentRecordSearchReq struct {
	g.Meta `path:"/list" tags:"用户买入行为- 对应番茄用户买入接口" method:"post" summary:"用户买入行为- 对应番茄用户买入接口列表"`
	commonApi.Author
	model.FqAdUserPaymentRecordSearchReq
}

// FqAdUserPaymentRecordSearchRes 列表返回结果
type FqAdUserPaymentRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdUserPaymentRecordSearchRes
}

// FqAdUserPaymentPullReq
type FqAdUserPaymentPullReq struct {
	g.Meta `path:"/payment/pull" tags:"用户买入行为-对应番茄用户买入接口" method:"get" summary:"pull"`
	commonApi.Author
	StartTime string `p:"startTime" v:"required#开始时间不能为空"`
	EndTime   string `p:"endTime" v:"required#结束时间不能为空"`
}

// FqAdUserPaymentPullRes
type FqAdUserPaymentPullRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// FqAdUserPaymentRecordExportReq 导出请求
type FqAdUserPaymentRecordExportReq struct {
	g.Meta `path:"/export" tags:"用户买入行为- 对应番茄用户买入接口" method:"get" summary:"用户买入行为- 对应番茄用户买入接口导出"`
	commonApi.Author
	model.FqAdUserPaymentRecordSearchReq
}

// FqAdUserPaymentRecordExportRes 导出响应
type FqAdUserPaymentRecordExportRes struct {
	commonApi.EmptyRes
}

// FqAdUserPaymentRecordAddReq 添加操作请求参数
type FqAdUserPaymentRecordAddReq struct {
	g.Meta `path:"/add" tags:"用户买入行为- 对应番茄用户买入接口" method:"post" summary:"用户买入行为- 对应番茄用户买入接口添加"`
	commonApi.Author
	*model.FqAdUserPaymentRecordAddReq
}

// FqAdUserPaymentRecordAddRes 添加操作返回结果
type FqAdUserPaymentRecordAddRes struct {
	commonApi.EmptyRes
}

// FqAdUserPaymentRecordSaveReq 添加操作请求参数
type FqAdUserPaymentRecordSaveReq struct {
	g.Meta `path:"/save" tags:"用户买入行为-对应番茄用户买入接口" method:"post" summary:"用户买入行为- 对应番茄用户买入接口添加"`
	commonApi.Author
	*model.FqAdUserPaymentRecordSaveReq
}

// FqAdUserPaymentRecordSaveRes 添加操作返回结果
type FqAdUserPaymentRecordSaveRes struct {
	commonApi.EmptyRes
}

// FqAdUserPaymentRecordEditReq 修改操作请求参数
type FqAdUserPaymentRecordEditReq struct {
	g.Meta `path:"/edit" tags:"用户买入行为- 对应番茄用户买入接口" method:"put" summary:"用户买入行为- 对应番茄用户买入接口修改"`
	commonApi.Author
	*model.FqAdUserPaymentRecordEditReq
}

// FqAdUserPaymentRecordEditRes 修改操作返回结果
type FqAdUserPaymentRecordEditRes struct {
	commonApi.EmptyRes
}

// FqAdUserPaymentRecordGetReq 获取一条数据请求
type FqAdUserPaymentRecordGetReq struct {
	g.Meta `path:"/get" tags:"用户买入行为- 对应番茄用户买入接口" method:"get" summary:"获取用户买入行为- 对应番茄用户买入接口信息"`
	commonApi.Author
	TradeNo string `p:"tradeNo" v:"required#主键必须"` //通过主键获取
}

// FqAdUserPaymentRecordGetRes 获取一条数据结果
type FqAdUserPaymentRecordGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdUserPaymentRecordInfoRes
}

// FqAdUserPaymentRecordDeleteReq 删除数据请求
type FqAdUserPaymentRecordDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户买入行为- 对应番茄用户买入接口" method:"delete" summary:"删除用户买入行为- 对应番茄用户买入接口"`
	commonApi.Author
	TradeNos []string `p:"tradeNos" v:"required#主键必须"` //通过主键删除
}

// FqAdUserPaymentRecordDeleteRes 删除数据返回
type FqAdUserPaymentRecordDeleteRes struct {
	commonApi.EmptyRes
}
