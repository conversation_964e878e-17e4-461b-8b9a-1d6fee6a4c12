// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-27 11:19:17
// 生成路径: api/v1/ad/ad_plan_channel_execute.go
// 生成人：cq
// desc:广告渠道执行配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdPlanChannelExecuteSearchReq 分页请求参数
type AdPlanChannelExecuteSearchReq struct {
	g.Meta `path:"/list" tags:"广告渠道执行配置" method:"get" summary:"广告渠道执行配置列表"`
	commonApi.Author
	model.AdPlanChannelExecuteSearchReq
}

// AdPlanChannelExecuteSearchRes 列表返回结果
type AdPlanChannelExecuteSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanChannelExecuteSearchRes
}

// AdPlanChannelExecuteAddReq 添加操作请求参数
type AdPlanChannelExecuteAddReq struct {
	g.Meta `path:"/add" tags:"广告渠道执行配置" method:"post" summary:"广告渠道执行配置添加"`
	commonApi.Author
	*model.AdPlanChannelExecuteAddReq
}

// AdPlanChannelExecuteAddRes 添加操作返回结果
type AdPlanChannelExecuteAddRes struct {
	commonApi.EmptyRes
}

// AdPlanChannelExecuteEditReq 修改操作请求参数
type AdPlanChannelExecuteEditReq struct {
	g.Meta `path:"/edit" tags:"广告渠道执行配置" method:"put" summary:"广告渠道执行配置修改"`
	commonApi.Author
	*model.AdPlanChannelExecuteEditReq
}

// AdPlanChannelExecuteEditRes 修改操作返回结果
type AdPlanChannelExecuteEditRes struct {
	commonApi.EmptyRes
}

// AdPlanChannelExecuteGetReq 获取一条数据请求
type AdPlanChannelExecuteGetReq struct {
	g.Meta `path:"/get" tags:"广告渠道执行配置" method:"get" summary:"获取广告渠道执行配置信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPlanChannelExecuteGetRes 获取一条数据结果
type AdPlanChannelExecuteGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanChannelExecuteInfoRes
}

// AdPlanChannelExecuteDeleteReq 删除数据请求
type AdPlanChannelExecuteDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告渠道执行配置" method:"post" summary:"删除广告渠道执行配置"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPlanChannelExecuteDeleteRes 删除数据返回
type AdPlanChannelExecuteDeleteRes struct {
	commonApi.EmptyRes
}

// AdPlanChannelExecuteReq 删除数据请求
type AdPlanChannelExecuteReq struct {
	g.Meta `path:"/execute" tags:"广告渠道执行配置" method:"post" summary:"调整渠道模板/单集价格/付费集数/回传配置"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键删除
}

// AdPlanChannelExecuteRes 删除数据返回
type AdPlanChannelExecuteRes struct {
	commonApi.EmptyRes
}
