// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-07 16:04:14
// 生成路径: api/v1/theater/s_dy_album_auth.go
// 生成人：cq
// desc:UBMax*AIGC授权相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyAlbumAuthSearchReq 分页请求参数
type SDyAlbumAuthSearchReq struct {
	g.Meta `path:"/list" tags:"UBMax*AIGC授权" method:"get" summary:"UBMax*AIGC授权列表"`
	commonApi.Author
	model.SDyAlbumAuthSearchReq
}

// SDyAlbumAuthSearchRes 列表返回结果
type SDyAlbumAuthSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumAuthSearchRes
}

// SDyAlbumAuthAddReq 添加操作请求参数
type SDyAlbumAuthAddReq struct {
	g.Meta `path:"/add" tags:"UBMax*AIGC授权" method:"post" summary:"UBMax*AIGC授权添加"`
	commonApi.Author
	*model.SDyAlbumAuthAddReq
}

// SDyAlbumAuthAddRes 添加操作返回结果
type SDyAlbumAuthAddRes struct {
	commonApi.EmptyRes
}

// SDyAlbumAuthEditReq 修改操作请求参数
type SDyAlbumAuthEditReq struct {
	g.Meta `path:"/edit" tags:"UBMax*AIGC授权" method:"put" summary:"UBMax*AIGC授权修改"`
	commonApi.Author
	*model.SDyAlbumAuthEditReq
}

// SDyAlbumAuthEditRes 修改操作返回结果
type SDyAlbumAuthEditRes struct {
	commonApi.EmptyRes
}

// SDyAlbumAuthGetReq 获取一条数据请求
type SDyAlbumAuthGetReq struct {
	g.Meta `path:"/get" tags:"UBMax*AIGC授权" method:"get" summary:"获取UBMax*AIGC授权信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SDyAlbumAuthGetRes 获取一条数据结果
type SDyAlbumAuthGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumAuthInfoRes
}

// SDyAlbumAuthDeleteReq 删除数据请求
type SDyAlbumAuthDeleteReq struct {
	g.Meta `path:"/delete" tags:"UBMax*AIGC授权" method:"post" summary:"删除UBMax*AIGC授权"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SDyAlbumAuthDeleteRes 删除数据返回
type SDyAlbumAuthDeleteRes struct {
	commonApi.EmptyRes
}
