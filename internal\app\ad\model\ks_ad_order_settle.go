// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: internal/app/ad/model/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdOrderSettleInfoRes is the golang structure for table ks_ad_order_settle.
type KsAdOrderSettleInfoRes struct {
	gmeta.Meta                       `orm:"table:ks_ad_order_settle"`
	Id                               int64       `orm:"id,primary" json:"id" dc:"id"`                                                                        // id
	AdvertiserId                     int64       `orm:"advertiser_id" json:"advertiserId" dc:"用户快手号id"`                                                      // 用户快手号id
	SettleDate                       string      `orm:"settle_date" json:"settleDate" dc:"结算时间"`                                                             // 结算时间
	SeriesId                         int64       `orm:"series_id" json:"seriesId" dc:"短剧ID"`                                                                 // 短剧ID
	SeriesName                       string      `orm:"series_name" json:"seriesName" dc:"短剧名称"`                                                             // 短剧名称
	CopyrightUid                     string      `orm:"copyright_uid" json:"copyrightUid" dc:"版权商UID"`                                                       // 版权商UID
	CopyrightName                    string      `orm:"copyright_name" json:"copyrightName" dc:"版权商名称"`                                                      // 版权商名称
	SalerUid                         string      `orm:"saler_uid" json:"salerUid" dc:"分销商UID"`                                                               // 分销商UID
	SalerName                        string      `orm:"saler_name" json:"salerName" dc:"分销商名称"`                                                              // 分销商名称
	SubAccountUid                    string      `orm:"sub_account_uid" json:"subAccountUid" dc:"子账号UID"`                                                    // 子账号UID
	SubAccountName                   string      `orm:"sub_account_name" json:"subAccountName" dc:"子账号名称"`                                                   // 子账号名称
	PayProvider                      string      `orm:"pay_provider" json:"payProvider" dc:"支付渠道"`                                                           // 支付渠道
	PayAmt                           float64     `orm:"pay_amt" json:"payAmt" dc:"结算订单总金额（元）"`                                                               // 结算订单总金额（元）
	RedundPrice                      float64     `orm:"redund_price" json:"redundPrice" dc:"退款金额（元）"`                                                        // 退款金额（元）
	CommissionPrice                  float64     `orm:"commission_price" json:"commissionPrice" dc:"佣金（元）"`                                                  // 佣金（元）
	SettlePrice                      float64     `orm:"settle_price" json:"settlePrice" dc:"可提现金额（元）"`                                                       // 可提现金额（元）
	SubAccountOrderPayAmt            float64     `orm:"sub_account_order_pay_amt" json:"subAccountOrderPayAmt" dc:"子账号结算（元）"`                                // 子账号结算（元）
	CurAccountOrderPayAmt            float64     `orm:"cur_account_order_pay_amt" json:"curAccountOrderPayAmt" dc:"本账号结算（元）"`                                // 本账号结算（元）
	CopyrightDistributionOrderPayAmt float64     `orm:"copyright_distribution_order_pay_amt" json:"copyrightDistributionOrderPayAmt" dc:"分销分成前结算（元）（版权方视角）"` // 分销分成前结算（元）（版权方视角）
	SalerDistributionOrderPayAmt     float64     `orm:"saler_distribution_order_pay_amt" json:"salerDistributionOrderPayAmt" dc:"分销分成前结算（元）（分销视角）"`          // 分销分成前结算（元）（分销视角）
	Expenditure                      float64     `orm:"expenditure" json:"expenditure" dc:"总支出（元）"`                                                          // 总支出（元）
	PayDate                          string      `orm:"pay_date" json:"payDate" dc:"支付时间"`                                                                   // 支付时间
	OrderType                        string      `orm:"order_type" json:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`                                              // 订单类型 本账号售卖、子账户售卖、分销售卖
	CreatedAt                        *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                               // 创建时间
	UpdatedAt                        *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                               // 更新时间
}

type KsAdOrderSettleListRes struct {
	Id                               int64       `json:"id" dc:"id"`
	AdvertiserId                     int64       `json:"advertiserId" dc:"用户快手号id"`
	AdvertiserName                   string      `json:"advertiserName" dc:"用户快手号名称"`
	SettleDate                       string      `json:"settleDate" dc:"结算时间"`
	SeriesId                         int64       `json:"seriesId" dc:"短剧ID"`
	SeriesName                       string      `json:"seriesName" dc:"短剧名称"`
	CopyrightUid                     string      `json:"copyrightUid" dc:"版权商UID"`
	CopyrightName                    string      `json:"copyrightName" dc:"版权商名称"`
	SalerUid                         string      `json:"salerUid" dc:"分销商UID"`
	SalerName                        string      `json:"salerName" dc:"分销商名称"`
	SubAccountUid                    string      `json:"subAccountUid" dc:"子账号UID"`
	SubAccountName                   string      `json:"subAccountName" dc:"子账号名称"`
	PayProvider                      string      `json:"payProvider" dc:"支付渠道"`
	PayAmt                           float64     `json:"payAmt" dc:"结算订单总金额（元）"`
	RedundPrice                      float64     `json:"redundPrice" dc:"退款金额（元）"`
	CommissionPrice                  float64     `json:"commissionPrice" dc:"佣金（元）"`
	SettlePrice                      float64     `json:"settlePrice" dc:"可提现金额（元）"`
	SubAccountOrderPayAmt            float64     `json:"subAccountOrderPayAmt" dc:"子账号结算（元）"`
	CurAccountOrderPayAmt            float64     `json:"curAccountOrderPayAmt" dc:"本账号结算（元）"`
	CopyrightDistributionOrderPayAmt float64     `json:"copyrightDistributionOrderPayAmt" dc:"分销分成前结算（元）（版权方视角）"`
	SalerDistributionOrderPayAmt     float64     `json:"salerDistributionOrderPayAmt" dc:"分销分成前结算（元）（分销视角）"`
	Expenditure                      float64     `json:"expenditure" dc:"总支出（元）"`
	PayDate                          string      `json:"payDate" dc:"支付时间"`
	OrderType                        string      `json:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
	CreatedAt                        *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdOrderSettleSearchReq 分页请求参数
type KsAdOrderSettleSearchReq struct {
	comModel.PageReq
	Id            string   `p:"id" dc:"id"`
	AdvertiserId  string   `p:"advertiserId" v:"advertiserId@integer#用户快手号id需为整数" dc:"用户快手号id"`
	StartPayDate  string   `p:"startPayDate" dc:"开始购买时间"`
	EndPayDate    string   `p:"endPayDate" dc:"结束购买时间"`
	SeriesIds     []string `p:"seriesIds"  dc:"短剧ID列表"`
	SeriesNames   []string `p:"seriesNames" dc:"短剧名称列表"`
	CopyrightUid  string   `p:"copyrightUid" dc:"版权商UID"`
	CopyrightName string   `p:"copyrightName" dc:"版权商名称"`
	SalerUid      string   `p:"salerUid" dc:"分销商UID"`
	SalerName     string   `p:"salerName" dc:"分销商名称"`
	PayProvider   string   `p:"payProvider" dc:"支付渠道"`
	OrderType     string   `p:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
}

// KsAdOrderSettleSearchRes 列表返回结果
type KsAdOrderSettleSearchRes struct {
	comModel.ListRes
	List    []*KsAdOrderSettleListRes `json:"list"`
	Summary *KsAdOrderSettleListRes   `json:"summary"`
}

// KsAdOrderSettleAddReq 添加操作请求参数
type KsAdOrderSettleAddReq struct {
	AdvertiserId                     int64   `p:"advertiserId" v:"required#用户快手号id不能为空" dc:"用户快手号id"`
	SettleDate                       string  `p:"settleDate" v:"required#结算时间不能为空" dc:"结算时间"`
	SeriesId                         int64   `p:"seriesId" v:"required#短剧ID不能为空" dc:"短剧ID"`
	SeriesName                       string  `p:"seriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	CopyrightUid                     string  `p:"copyrightUid" v:"required#版权商UID不能为空" dc:"版权商UID"`
	CopyrightName                    string  `p:"copyrightName" v:"required#版权商名称不能为空" dc:"版权商名称"`
	SalerUid                         string  `p:"salerUid" v:"required#分销商UID不能为空" dc:"分销商UID"`
	SalerName                        string  `p:"salerName" v:"required#分销商名称不能为空" dc:"分销商名称"`
	SubAccountUid                    string  `p:"subAccountUid" v:"required#子账号UID不能为空" dc:"子账号UID"`
	SubAccountName                   string  `p:"subAccountName" v:"required#子账号名称不能为空" dc:"子账号名称"`
	PayProvider                      string  `p:"payProvider" v:"required#支付渠道不能为空" dc:"支付渠道"`
	PayAmt                           float64 `p:"payAmt"  dc:"结算订单总金额（元）"`
	RedundPrice                      float64 `p:"redundPrice"  dc:"退款金额（元）"`
	CommissionPrice                  float64 `p:"commissionPrice"  dc:"佣金（元）"`
	SettlePrice                      float64 `p:"settlePrice"  dc:"可提现金额（元）"`
	SubAccountOrderPayAmt            float64 `p:"subAccountOrderPayAmt"  dc:"子账号结算（元）"`
	CurAccountOrderPayAmt            float64 `p:"curAccountOrderPayAmt"  dc:"本账号结算（元）"`
	CopyrightDistributionOrderPayAmt float64 `p:"copyrightDistributionOrderPayAmt"  dc:"分销分成前结算（元）（版权方视角）"`
	SalerDistributionOrderPayAmt     float64 `p:"salerDistributionOrderPayAmt"  dc:"分销分成前结算（元）（分销视角）"`
	Expenditure                      float64 `p:"expenditure"  dc:"总支出（元）"`
	PayDate                          string  `p:"payDate"  dc:"支付时间"`
	OrderType                        string  `p:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
}

// KsAdOrderSettleEditReq 修改操作请求参数
type KsAdOrderSettleEditReq struct {
	Id                               int64   `p:"id" v:"required#主键ID不能为空" dc:"id"`
	AdvertiserId                     int64   `p:"advertiserId" v:"required#用户快手号id不能为空" dc:"用户快手号id"`
	SettleDate                       string  `p:"settleDate" v:"required#结算时间不能为空" dc:"结算时间"`
	SeriesId                         int64   `p:"seriesId" v:"required#短剧ID不能为空" dc:"短剧ID"`
	SeriesName                       string  `p:"seriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	CopyrightUid                     string  `p:"copyrightUid" v:"required#版权商UID不能为空" dc:"版权商UID"`
	CopyrightName                    string  `p:"copyrightName" v:"required#版权商名称不能为空" dc:"版权商名称"`
	SalerUid                         string  `p:"salerUid" v:"required#分销商UID不能为空" dc:"分销商UID"`
	SalerName                        string  `p:"salerName" v:"required#分销商名称不能为空" dc:"分销商名称"`
	SubAccountUid                    string  `p:"subAccountUid" v:"required#子账号UID不能为空" dc:"子账号UID"`
	SubAccountName                   string  `p:"subAccountName" v:"required#子账号名称不能为空" dc:"子账号名称"`
	PayProvider                      string  `p:"payProvider" v:"required#支付渠道不能为空" dc:"支付渠道"`
	PayAmt                           float64 `p:"payAmt"  dc:"结算订单总金额（元）"`
	RedundPrice                      float64 `p:"redundPrice"  dc:"退款金额（元）"`
	CommissionPrice                  float64 `p:"commissionPrice"  dc:"佣金（元）"`
	SettlePrice                      float64 `p:"settlePrice"  dc:"可提现金额（元）"`
	SubAccountOrderPayAmt            float64 `p:"subAccountOrderPayAmt"  dc:"子账号结算（元）"`
	CurAccountOrderPayAmt            float64 `p:"curAccountOrderPayAmt"  dc:"本账号结算（元）"`
	CopyrightDistributionOrderPayAmt float64 `p:"copyrightDistributionOrderPayAmt"  dc:"分销分成前结算（元）（版权方视角）"`
	SalerDistributionOrderPayAmt     float64 `p:"salerDistributionOrderPayAmt"  dc:"分销分成前结算（元）（分销视角）"`
	Expenditure                      float64 `p:"expenditure"  dc:"总支出（元）"`
	PayDate                          string  `p:"payDate"  dc:"支付时间"`
	OrderType                        string  `p:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
}
