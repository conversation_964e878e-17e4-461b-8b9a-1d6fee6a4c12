// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-13 10:00:48
// 生成路径: api/v1/oceanengine/ad_majordomo_advertiser_account.go
// 生成人：cq
// desc:管家账户表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdMajordomoAdvertiserAccountSearchReq 分页请求参数
type AdMajordomoAdvertiserAccountSearchReq struct {
	g.Meta `path:"/list" tags:"管家账户表" method:"get" summary:"管家账户表列表"`
	commonApi.Author
	model.AdMajordomoAdvertiserAccountSearchReq
}

// AdMajordomoAdvertiserAccountSearchRes 列表返回结果
type AdMajordomoAdvertiserAccountSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMajordomoAdvertiserAccountSearchRes
}

// AdMajordomoAdvertiserAccountAddReq 添加操作请求参数
type AdMajordomoAdvertiserAccountAddReq struct {
	g.Meta `path:"/add" tags:"管家账户表" method:"post" summary:"管家账户表添加"`
	commonApi.Author
	*model.AdMajordomoAdvertiserAccountAddReq
}

// AdMajordomoAdvertiserAccountAddRes 添加操作返回结果
type AdMajordomoAdvertiserAccountAddRes struct {
	commonApi.EmptyRes
}

// AdMajordomoAdvertiserAccountEditReq 修改操作请求参数
type AdMajordomoAdvertiserAccountEditReq struct {
	g.Meta `path:"/edit" tags:"管家账户表" method:"put" summary:"管家账户表修改"`
	commonApi.Author
	*model.AdMajordomoAdvertiserAccountEditReq
}

// AdMajordomoAdvertiserAccountEditRes 修改操作返回结果
type AdMajordomoAdvertiserAccountEditRes struct {
	commonApi.EmptyRes
}

// AdMajordomoAdvertiserAccountGetReq 获取一条数据请求
type AdMajordomoAdvertiserAccountGetReq struct {
	g.Meta `path:"/get" tags:"管家账户表" method:"get" summary:"获取管家账户表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdMajordomoAdvertiserAccountGetRes 获取一条数据结果
type AdMajordomoAdvertiserAccountGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMajordomoAdvertiserAccountInfoRes
}

// AdMajordomoAdvertiserAccountDeleteReq 删除数据请求
type AdMajordomoAdvertiserAccountDeleteReq struct {
	g.Meta `path:"/delete" tags:"管家账户表" method:"post" summary:"删除管家账户表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdMajordomoAdvertiserAccountDeleteRes 删除数据返回
type AdMajordomoAdvertiserAccountDeleteRes struct {
	commonApi.EmptyRes
}

// VerifyAdvertiserAccountReq 校验广告账户请求
type VerifyAdvertiserAccountReq struct {
	g.Meta `path:"/verifyAdvertiserAccount" tags:"管家账户表" method:"post" summary:"校验广告账户"`
	commonApi.Author
	*model.VerifyAdvertiserAccountReq
}

// VerifyAdvertiserAccountRes 校验广告账户返回
type VerifyAdvertiserAccountRes struct {
	commonApi.EmptyRes
	*model.VerifyAdvertiserAccountRes
}

// GetImportAdAdvertiserListReq 获取导入账户列表请求
type GetImportAdAdvertiserListReq struct {
	g.Meta `path:"/getAdAdvertiserList" tags:"管家账户表" method:"post" summary:"获取导入账户列表"`
	commonApi.Author
	Id          int64  `p:"id" dc:"管家账户账户ID"`
	MajordomoId string `p:"majordomoId" v:"required#管家账户Id必须"`
}

// GetImportAdAdvertiserListRes 获取导入账户列表返回
type GetImportAdAdvertiserListRes struct {
	commonApi.EmptyRes
	*model.GetImportAdAdvertiserListRes
}

// ImportAdAdvertiserReq 导入账户请求
type ImportAdAdvertiserReq struct {
	g.Meta `path:"/importAdvertiser" tags:"管家账户表" method:"post" summary:"导入账户"`
	commonApi.Author
	*model.ImportAdAdvertiserReq
}

// ImportAdAdvertiserRes 导入账户返回
type ImportAdAdvertiserRes struct {
	commonApi.EmptyRes
}

// GetCompanyListReq 获取账户主体请求
type GetCompanyListReq struct {
	g.Meta `path:"/getCompanyList" tags:"管家账户表" method:"post" summary:"获取账户主体"`
	commonApi.Author
	Company string `p:"company"`
}

// GetCompanyListRes 导入账户返回
type GetCompanyListRes struct {
	commonApi.EmptyRes
	*model.GetCompanyListRes
}

// GetAuthUserListReq 获取授权账户列表请求
type GetAuthUserListReq struct {
	g.Meta `path:"/getAuthUserList" tags:"管家账户表" method:"post" summary:"获取授权账户列表"`
	commonApi.Author
	AuthUserType int32  `p:"authUserType" dc:"授权用户类型： 1：纵横组织 2：方舟"`
	AuthUserId   string `p:"authUserId" dc:"授权用户ID"`
}

// GetAuthUserListRes 获取授权账户列表返回
type GetAuthUserListRes struct {
	commonApi.EmptyRes
	*model.GetAuthUserListRes
}

// RefreshTokenReq 刷新巨量token请求
type RefreshTokenReq struct {
	g.Meta `path:"/refresh/token/task" tags:"管家账户表" method:"post" summary:"刷新巨量token"`
	commonApi.Author
}

// RefreshTokenRes 刷新巨量token返回
type RefreshTokenRes struct {
	commonApi.EmptyRes
}
