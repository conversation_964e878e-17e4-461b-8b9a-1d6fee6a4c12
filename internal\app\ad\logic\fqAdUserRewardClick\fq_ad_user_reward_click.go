// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: internal/app/ad/logic/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	channelDao "github.com/tiger1103/gfast/v3/internal/app/channel/dao"
	channelModel "github.com/tiger1103/gfast/v3/internal/app/channel/model"
	channelEntity "github.com/tiger1103/gfast/v3/internal/app/channel/model/entity"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	orderModel "github.com/tiger1103/gfast/v3/internal/app/order/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	theaterModel "github.com/tiger1103/gfast/v3/internal/app/theater/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdUserRewardClick(New())
}

func New() service.IFqAdUserRewardClick {
	return &sFqAdUserRewardClick{}
}

type sFqAdUserRewardClick struct{}

func (s *sFqAdUserRewardClick) List(ctx context.Context, req *model.FqAdUserRewardClickSearchReq) (listRes *model.FqAdUserRewardClickSearchRes, err error) {
	listRes = new(model.FqAdUserRewardClickSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {

		m := dao.FqAdUserRewardClickAnalytic.Ctx(ctx).As("fqdata").
			LeftJoin(dao.FqAdUserInfo.Table(), "fu", "fu.encrypted_device_id = fqdata.device_id  and fu.distributor_id = fqdata.distributor_id")
		userInfo := sysService.Context().GetLoginUser(ctx)
		_, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		if !admin {
			m = m.LeftJoin(dao.FqAdAccountDepts.Table(), "d", " fqdata.distributor_id = d.distributor_id").
				LeftJoin(dao.FqAdAccountUsers.Table(), "u", " fqdata.distributor_id = u.distributor_id").
				Where("d.detp_id = ? or u.specify_user_id = ?", userInfo.DeptId, userInfo.Id)
		}

		if req.EcpmNo != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().EcpmNo+" = ?", req.EcpmNo)
		}
		//if req.FqDistributorId != "" {
		//	m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().DistributorId+" = ?", req.FqDistributorId)
		//}
		//if len(req.FqDistributorIds) > 0 {
		//	m = m.WhereIn("fqdata."+dao.FqAdUserRewardClick.Columns().DistributorId, req.FqDistributorIds)
		//}

		if req.FqDistributorId != "" {
			list, _ := service.FqAdAccountChannel().GetByDistributorId(ctx, gconv.Int64(req.FqDistributorId))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdUserRewardClick.Columns().DistributorId, ids)
				}
			}
		}
		if len(req.FqDistributorIds) > 0 {
			list, _ := service.FqAdAccountChannel().GetByDistributorIds(ctx, gconv.SliceInt64(req.FqChannelDistributorIds))
			if len(list) > 0 {
				ids := make([]int64, 0)
				for _, item := range list {
					ids = append(ids, item.ChannelDistributorId)
				}
				if len(ids) > 0 {
					m = m.WhereIn("fqdata."+dao.FqAdUserRewardClick.Columns().DistributorId, ids)
				}
			}

		}

		if len(req.FqChannelDistributorId) > 0 {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().DistributorId+" = ?", gconv.Int64(req.FqChannelDistributorId))
		}
		if len(req.FqChannelDistributorIds) > 0 {
			m = m.WhereIn("fqdata."+dao.FqAdUserRewardClick.Columns().DistributorId, req.FqChannelDistributorIds)
		}

		if req.AppId != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().AppId+" = ?", req.AppId)
		}
		if req.AppName != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().AppName+" like ?", "%"+req.AppName+"%")
		}
		if req.DeviceId != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().DeviceId+" = ?", req.DeviceId)
		}
		if req.PromotionId != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.EcpmCost != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().EcpmCost+" = ?", gconv.Int64(req.EcpmCost))
		}

		if req.StartTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().EventTime+" >= ?", libUtils.GetSecByDate(req.StartTime))
		}

		if req.EndTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().EventTime+" < ?", libUtils.GetSecByDate(libUtils.StringTimeAddDay(req.EndTime, 1)))
		}

		//if req.EventTime != "" {
		//	m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().EventTime+" = ?", gconv.Int64(req.EventTime))
		//}
		//if req.RegisterTime != "" {
		//	m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().RegisterTime+" = ?", gconv.Int64(req.RegisterTime))
		//}

		if req.RegisterStartTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().RegisterTime+" >= ?", libUtils.GetSecByDate(req.RegisterStartTime))
		}

		if req.RegisterEndTime != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().RegisterTime+" < ?", libUtils.GetSecByDate(libUtils.StringTimeAddDay(req.RegisterEndTime, 1)))
		}

		if req.BookId != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().BookId+" = ?", req.BookId)
		}
		if req.BookName != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.BookGender != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().BookGender+" = ?", gconv.Int(req.BookGender))
		}
		if req.BookCategory != "" {
			m = m.Where("fqdata."+dao.FqAdUserRewardClick.Columns().BookCategory+" = ?", req.BookCategory)
		}

		if len(req.ChannelCode) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code = "+fmt.Sprintf("'%s'", req.ChannelCode))
		} else if len(req.ChannelCodes) > 0 {
			m = m.InnerJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id And sc.channel_code in "+fmt.Sprintf("(%s)", libUtils.BuildSqlInStr(req.ChannelCodes)))
		} else {
			m = m.LeftJoin(channelDao.SChannel.Table(), "sc", "sc."+channelDao.SChannel.Columns().FqPromotionId+" = fqdata.promotion_id ")
		}

		if req.PitcherId > 0 {
			m = m.InnerJoin(systemDao.SysUser.Table(), "su", fmt.Sprintf("su.id = sc.user_id and su.id = %v", req.PitcherId))
		} else {
			m = m.LeftJoin(systemDao.SysUser.Table(), "su", "su.id = sc.user_id")
		}
		if req.DistributorId > 0 {
			if len(req.DeptIds) > 0 {
				m = m.LeftJoin("sys_dept as de", fmt.Sprintf("de.dept_id = su.dept_id and de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds))).
					InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
			} else {
				m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
					InnerJoin("sys_user as u1", fmt.Sprintf("de.leader =u1.user_name and u1.id = %v", req.DistributorId))
			}

		} else {
			if len(req.DeptIds) > 0 {
				m = m.LeftJoin("sys_dept as de", fmt.Sprintf("de.dept_id = su.dept_id and de.dept_id in %v", libUtils.BuildSqlIntArray(req.DeptIds))).
					LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
			} else {
				m = m.LeftJoin("sys_dept as de", "de.dept_id = su.dept_id").
					LeftJoin("sys_user as u1", "de.leader =u1.user_name ")
			}
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = libUtils.CamelToSnake(req.OrderBy) + " " + req.OrderType
		}
		var summary = new(model.FqAdUserRewardClickSummary)
		err = m.FieldSum("fqdata."+dao.FqAdUserRewardClick.Columns().EcpmCost, "ecpm_cost").Fields("COUNT(DISTINCT fqdata.device_id) as userCount").Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取统计数据失败")
		listRes.Summary = summary
		listRes.Summary.EcpmCount = gconv.Int64(listRes.Total)
		listRes.Summary.EcpmCost = gconv.Float64(summary.EcpmCost) / 100000
		listRes.Total = summary.UserCount

		var res []*model.FqAdUserRewardClickListRes

		err = m.Fields(`any_value(fqdata.ecpm_no) as ecpm_no,
		any_value(fqdata.distributor_id) as distributor_id ,
		any_value(fqdata.app_id) as app_id,
		any_value(fqdata.app_name) as app_name,
		device_id,
		any_value(fqdata.promotion_id) as promotion_id,
		SUM(ecpm_cost) as ecpm_cost,
		COUNT(device_id) as ecpm_count,
		any_value(fqdata.event_time) as event_time,
		any_value(fqdata.register_time) as register_time,
		any_value(fqdata.book_id) as book_id,
		any_value(fqdata.book_name) as book_name,
		any_value(fqdata.book_gender) as book_gender,
		any_value(fqdata.book_category) as book_category, 
		any_value(fqdata.event_time_date) as event_time_date,
		any_value(fu.ip) as ip	,
		any_value ( fu.open_id ) AS open_id,
		any_value ( fu.user_agent ) AS user_agent,
		any_value ( fu.device_brand ) AS device_brand,
		any_value ( fu.media_source ) AS media_source,
		any_value(fu.clickid) as clickid,
		any_value(fu.oaid) as oaid,
		any_value(fu.adid) as adid,
		any_value(fu.caid) as caid ,
		any_value(fu.optimizer_account) as optimizer_account,
		any_value(fu.external_id) as external_id,
		any_value(fu.project_id) as project_id,
		any_value(fu.ad_id_v2) as ad_id_v2,
		any_value(fu.mid) as mid,
		any_value(su.user_name ) AS userName,
		any_value(su.id ) AS userId,
		any_value(de.leader ) AS distributorName,
		any_value(fq_promotion_id) as fq_promotion_id,
		any_value(channel_code) as channel_code`).Page(req.PageNum, req.PageSize).Group("fqdata.event_time_date,fqdata.device_id").Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		disIds := make([]int64, 0)
		listRes.List = make([]*model.FqAdUserRewardClickListRes, 0)
		for _, v := range res {
			disIds = append(disIds, gconv.Int64(v.DistributorId))
			listRes.List = append(listRes.List, &model.FqAdUserRewardClickListRes{
				EventTime:        libUtils.GetDateBySec2(gconv.Int64(v.EventTime)),
				EventTimeDate:    v.EventTimeDate,
				DeviceId:         v.DeviceId,
				OpenId:           v.OpenId,
				RegisterTime:     libUtils.GetDateBySec2(gconv.Int64(v.RegisterTime)),
				UserId:           v.UserId,
				UserName:         v.UserName,
				DistributorName:  v.DistributorName,
				ChannelCode:      v.ChannelCode,
				FqAccount:        v.FqAccount,
				EcpmCost:         gconv.Float64(v.EcpmCost) / 100000,
				EcpmCount:        v.EcpmCount,
				Ip:               v.Ip,
				Clickid:          v.Clickid,
				Oaid:             v.Oaid,
				Caid:             v.Caid,
				Adid:             v.Adid,
				OptimizerAccount: v.OptimizerAccount,
				//EcpmNo:          v.EcpmNo,
				FqChannelDistributorId: v.DistributorId,
				AppId:                  v.AppId,
				AppName:                v.AppName,
				PromotionId:            v.PromotionId,
				BookId:                 v.BookId,
				BookName:               v.BookName,
				BookGender:             v.BookGender,
				BookCategory:           v.BookCategory,
				ExternalId:             v.ExternalId,
				ProjectId:              v.ProjectId,
				AdIdV2:                 v.AdIdV2,
				Mid:                    v.Mid,
				CreatedAt:              v.CreatedAt,
				MediaSource:            v.MediaSource,
				DeviceBrand:            v.DeviceBrand,
			})
		}
		// 根据分销id获取分销信息
		//var distributorInfo = make([]model.FqAdAccountInfoRes, 0)
		//err = dao.FqAdAccount.Ctx(ctx).WhereIn("distributor_id", disIds).Scan(&distributorInfo)
		//channelList, _ := service.FqAdAccountChannel().GetByChannelDistributorIds(ctx, disIds)
		//liberr.ErrIsNil(ctx, err, "获取信息失败")
		//if len(distributorInfo) > 0 {
		//	for i, dataListRes := range listRes.List {
		//		for _, infoRes := range channelList {
		//			if dataListRes != nil && infoRes != nil && infoRes.DistributorId > 0 && infoRes.ChannelDistributorId > 0 {
		//				if gconv.Int64(dataListRes.DistributorId) == infoRes.DistributorId {
		//					listRes.List[i].FqChannelDistributorId = gconv.String(infoRes.ChannelDistributorId)
		//					listRes.List[i].FqChannel = infoRes.NickName
		//					for _, item := range distributorInfo {
		//						if item.DistributorId == infoRes.DistributorId {
		//							listRes.List[i].FqAccount = item.AccountName
		//						}
		//					}
		//				}
		//			}
		//		}
		//	}
		//}

		fqAdAccountChannels, _ := service.FqAdAccountChannel().GetChannelList(ctx, &model.FqAdAccountChannelSearchReq{
			PageReq: comModel.PageReq{
				PageNum:  1,
				PageSize: 10000,
			},
		})
		var distributorInfo = make([]model.FqAdAccountInfoRes, 0)
		channelList, _ := service.FqAdAccountChannel().GetByChannelDistributorIds(ctx, disIds)
		disIds = make([]int64, 0)
		for _, item := range channelList {
			disIds = append(disIds, item.DistributorId)
		}
		err = dao.FqAdAccount.Ctx(ctx).WhereIn("distributor_id", disIds).Scan(&distributorInfo)

		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if len(channelList) > 0 {
			for i, dataListRes := range listRes.List {
				// 获取书本信息
				if len(dataListRes.BookName) == 0 && gconv.Int64(dataListRes.BookId) > 0 {
					secretKey := ""
					for _, channel := range fqAdAccountChannels {
						if gconv.String(channel.ChannelDistributorId) == dataListRes.FqChannelDistributorId {
							secretKey = channel.SecretKey
							break
						}
					}
					bookInfo, _ := channelService.SChannel().GetFqBookInfo(ctx, &channelModel.GetFqBookInfoReq{
						BookId:        gconv.String(dataListRes.BookId),
						DistributorId: dataListRes.FqChannelDistributorId,
						SecretKey:     secretKey,
					})
					if bookInfo != nil {
						dataListRes.BookName = bookInfo.BookName
					}
				}

				for _, infoRes := range channelList {
					if dataListRes != nil && infoRes != nil && infoRes.DistributorId > 0 && infoRes.ChannelDistributorId > 0 {
						if gconv.Int64(dataListRes.FqChannelDistributorId) == infoRes.ChannelDistributorId {
							listRes.List[i].FqChannelDistributorId = gconv.String(infoRes.ChannelDistributorId)
							listRes.List[i].FqChannel = infoRes.NickName
							listRes.List[i].AppId = gconv.String(infoRes.AppId)
							listRes.List[i].AppName = infoRes.AppName
							listRes.List[i].DistributorId = gconv.String(infoRes.DistributorId)
							for _, item := range distributorInfo {
								if item.DistributorId == infoRes.DistributorId {
									listRes.List[i].FqAccount = item.AccountName
								}
							}
						}
					}
				}
			}
		}

	})
	return
}

func (s *sFqAdUserRewardClick) GetExportData(ctx context.Context, req *model.FqAdUserRewardClickSearchReq) (listRes []*model.FqAdUserRewardClickInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdUserRewardClick.Ctx(ctx).WithAll()
		if req.EcpmNo != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().EcpmNo+" = ?", req.EcpmNo)
		}
		if req.FqDistributorId != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().DistributorId+" = ?", req.FqDistributorId)
		}

		if req.AppId != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().AppId+" = ?", req.AppId)
		}
		if req.AppName != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().AppName+" like ?", "%"+req.AppName+"%")
		}
		if req.DeviceId != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().DeviceId+" = ?", req.DeviceId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.EcpmCost != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().EcpmCost+" = ?", gconv.Int64(req.EcpmCost))
		}
		if req.EventTime != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().EventTime+" = ?", gconv.Int64(req.EventTime))
		}
		if req.RegisterTime != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().RegisterTime+" = ?", gconv.Int64(req.RegisterTime))
		}
		if req.BookId != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().BookId+" = ?", req.BookId)
		}
		if req.BookName != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().BookName+" like ?", "%"+req.BookName+"%")
		}
		if req.BookGender != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().BookGender+" = ?", gconv.Int(req.BookGender))
		}
		if req.BookCategory != "" {
			m = m.Where(dao.FqAdUserRewardClick.Columns().BookCategory+" = ?", req.BookCategory)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.FqAdUserRewardClick.Columns().CreatedAt+" >=? AND "+dao.FqAdUserRewardClick.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "ecpm_no asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdUserRewardClick) GetByEcpmNo(ctx context.Context, ecpmNo string) (res *model.FqAdUserRewardClickInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdUserRewardClick.Ctx(ctx).WithAll().Where(dao.FqAdUserRewardClick.Columns().EcpmNo, ecpmNo).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdUserRewardClick) Add(ctx context.Context, req *model.FqAdUserRewardClickInfoSaveRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		g.Log().Info(ctx, fmt.Sprintf("收到数据IAA----------- %v ------------", req))
		_, err = dao.FqAdUserRewardClick.Ctx(ctx).Save(do.FqAdUserRewardClick{
			EcpmNo:        req.EcpmNo,
			DistributorId: req.DistributorId,
			AppId:         req.AppId,
			AppName:       req.AppName,
			DeviceId:      req.DeviceId,
			PromotionId:   req.PromotionId,
			EcpmCost:      req.EcpmCost,
			EventTime:     req.EventTime,
			RegisterTime:  req.RegisterTime,
			BookId:        req.BookId,
			BookName:      req.BookName,
			BookGender:    req.BookGender,
			BookCategory:  req.BookCategory,
			EventTimeDate: libUtils.GetDateBySec(req.EventTime),
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdUserRewardClick) Edit(ctx context.Context, req *model.FqAdUserRewardClickEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserRewardClick.Ctx(ctx).WherePri(req.EcpmNo).Update(do.FqAdUserRewardClick{
			DistributorId: req.DistributorId,
			AppId:         req.AppId,
			AppName:       req.AppName,
			DeviceId:      req.DeviceId,
			PromotionId:   req.PromotionId,
			EcpmCost:      req.EcpmCost,
			EventTime:     req.EventTime,
			RegisterTime:  req.RegisterTime,
			BookId:        req.BookId,
			BookName:      req.BookName,
			BookGender:    req.BookGender,
			BookCategory:  req.BookCategory,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdUserRewardClick) Delete(ctx context.Context, ecpmNos []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdUserRewardClick.Ctx(ctx).Delete(dao.FqAdUserRewardClick.Columns().EcpmNo+" in (?)", ecpmNos)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sFqAdUserRewardClick) CalcFqEcpmCostStat(ctx context.Context, statDate string) (ecpmCostStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps(statDate)
		err = dao.FqAdUserRewardClick.Ctx(ctx).As("fq").
			InnerJoin("s_channel s", "s.fq_promotion_id = fq.promotion_id").
			Fields("IFNULL(sum(fq.ecpm_cost/100000), 0.00) as totalAdUp").
			Fields("s.channel_code as account").
			WhereGTE("fq."+dao.FqAdUserRewardClick.Columns().EventTime, startTime/1000).
			WhereLTE("fq."+dao.FqAdUserRewardClick.Columns().EventTime, endTime/1000).
			Group("s.channel_code").
			Scan(&ecpmCostStat)
	})
	return
}

func (s *sFqAdUserRewardClick) CalcFqNewUserEcpmCostStat(ctx context.Context, statDate string) (newUserEcpmCostStat []channelEntity.SChannelRechargeStatistics, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps(statDate)
		err = dao.FqAdUserRewardClick.Ctx(ctx).As("fq").
			InnerJoin("s_channel s", "s.fq_promotion_id = fq.promotion_id").
			Fields("IFNULL(sum(fq.ecpm_cost/100000), 0.00) as newUserAdUp").
			Fields("s.channel_code as account").
			WhereGTE("fq."+dao.FqAdUserRewardClick.Columns().EventTime, startTime/1000).
			WhereLTE("fq."+dao.FqAdUserRewardClick.Columns().EventTime, endTime/1000).
			WhereGTE("fq."+dao.FqAdUserRewardClick.Columns().RegisterTime, startTime/1000).
			WhereLTE("fq."+dao.FqAdUserRewardClick.Columns().RegisterTime, endTime/1000).
			Group("s.channel_code").
			Scan(&newUserEcpmCostStat)
	})
	return
}

func (s *sFqAdUserRewardClick) CalcFqDistributorEcpmCostStat(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps(statDate)
		err = dao.FqAdUserRewardClick.Ctx(ctx).
			Fields("IFNULL(sum(ecpm_cost/100000), 0.00) as dayAdUp").
			Fields("distributor_id as fqDistributorId").
			WhereGTE(dao.FqAdUserRewardClick.Columns().EventTime, startTime/1000).
			WhereLTE(dao.FqAdUserRewardClick.Columns().EventTime, endTime/1000).
			Group("distributor_id").
			Scan(&res)
	})
	return
}

func (s *sFqAdUserRewardClick) CalcFqDistributorNewUserEcpmCostStat(ctx context.Context, statDate string) (res []*orderModel.SDistributionStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps(statDate)
		err = dao.FqAdUserRewardClick.Ctx(ctx).
			Fields("IFNULL(sum(ecpm_cost/100000), 0.00) as dayNewAdUp").
			Fields("distributor_id as fqDistributorId").
			WhereGTE(dao.FqAdUserRewardClick.Columns().EventTime, startTime/1000).
			WhereLTE(dao.FqAdUserRewardClick.Columns().EventTime, endTime/1000).
			WhereGTE(dao.FqAdUserRewardClick.Columns().RegisterTime, startTime/1000).
			WhereLTE(dao.FqAdUserRewardClick.Columns().RegisterTime, endTime/1000).
			Group("distributor_id").
			Scan(&res)
	})
	return
}

func (s *sFqAdUserRewardClick) CalcFqVideoEcpmCostStat(ctx context.Context, statDate string) (ecpmCostStat []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps(statDate)
		err = dao.FqAdUserRewardClick.Ctx(ctx).
			Fields("promotion_id as fqPromotionId").
			Fields("book_id as videoId").
			Fields("ANY_VALUE(book_name) as videoName").
			Fields("ANY_VALUE(distributor_id) as fqChannelDistributorId").
			Fields("IFNULL(sum(ecpm_cost/100000), 0.00) as totalAdUp").
			WhereGTE(dao.FqAdUserRewardClick.Columns().EventTime, startTime/1000).
			WhereLTE(dao.FqAdUserRewardClick.Columns().EventTime, endTime/1000).
			Group("promotion_id, book_id").
			Scan(&ecpmCostStat)
	})
	return
}

func (s *sFqAdUserRewardClick) CalcFqVideoNewUserEcpmCostStat(ctx context.Context, statDate string) (newUserEcpmCostStat []*theaterModel.SPitcherVideoRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		startTime, endTime, _ := libUtils.GetDayTimestamps(statDate)
		err = dao.FqAdUserRewardClick.Ctx(ctx).
			Fields("promotion_id as fqPromotionId").
			Fields("book_id as videoId").
			Fields("ANY_VALUE(book_name) as videoName").
			Fields("ANY_VALUE(distributor_id) as fqChannelDistributorId").
			Fields("IFNULL(sum(ecpm_cost/100000), 0.00) as newUserAdUp").
			WhereGTE(dao.FqAdUserRewardClick.Columns().EventTime, startTime/1000).
			WhereLTE(dao.FqAdUserRewardClick.Columns().EventTime, endTime/1000).
			WhereGTE(dao.FqAdUserRewardClick.Columns().RegisterTime, startTime/1000).
			WhereLTE(dao.FqAdUserRewardClick.Columns().RegisterTime, endTime/1000).
			Group("promotion_id, book_id").
			Scan(&newUserEcpmCostStat)
	})
	return
}
