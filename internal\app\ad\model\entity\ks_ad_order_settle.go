// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: internal/app/ad/model/entity/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdOrderSettle is the golang structure for table ks_ad_order_settle.
type KsAdOrderSettle struct {
	gmeta.Meta                       `orm:"table:ks_ad_order_settle"`
	Id                               int64       `orm:"id,primary" json:"id"`                                                         // id
	AdvertiserId                     int64       `orm:"advertiser_id" json:"advertiserId"`                                            // 用户快手号id
	SettleDate                       string      `orm:"settle_date" json:"settleDate"`                                                // 结算时间
	SeriesId                         int64       `orm:"series_id" json:"seriesId"`                                                    // 短剧ID
	SeriesName                       string      `orm:"series_name" json:"seriesName"`                                                // 短剧名称
	CopyrightUid                     string      `orm:"copyright_uid" json:"copyrightUid"`                                            // 版权商UID
	CopyrightName                    string      `orm:"copyright_name" json:"copyrightName"`                                          // 版权商名称
	SalerUid                         string      `orm:"saler_uid" json:"salerUid"`                                                    // 分销商UID
	SalerName                        string      `orm:"saler_name" json:"salerName"`                                                  // 分销商名称
	SubAccountUid                    string      `orm:"sub_account_uid" json:"subAccountUid"`                                         // 子账号UID
	SubAccountName                   string      `orm:"sub_account_name" json:"subAccountName"`                                       // 子账号名称
	PayProvider                      string      `orm:"pay_provider" json:"payProvider"`                                              // 支付渠道
	PayAmt                           float64     `orm:"pay_amt" json:"payAmt"`                                                        // 结算订单总金额（元）
	RedundPrice                      float64     `orm:"redund_price" json:"redundPrice"`                                              // 退款金额（元）
	CommissionPrice                  float64     `orm:"commission_price" json:"commissionPrice"`                                      // 佣金（元）
	SettlePrice                      float64     `orm:"settle_price" json:"settlePrice"`                                              // 可提现金额（元）
	SubAccountOrderPayAmt            float64     `orm:"sub_account_order_pay_amt" json:"subAccountOrderPayAmt"`                       // 子账号结算（元）
	CurAccountOrderPayAmt            float64     `orm:"cur_account_order_pay_amt" json:"curAccountOrderPayAmt"`                       // 本账号结算（元）
	CopyrightDistributionOrderPayAmt float64     `orm:"copyright_distribution_order_pay_amt" json:"copyrightDistributionOrderPayAmt"` // 分销分成前结算（元）（版权方视角）
	SalerDistributionOrderPayAmt     float64     `orm:"saler_distribution_order_pay_amt" json:"salerDistributionOrderPayAmt"`         // 分销分成前结算（元）（分销视角）
	Expenditure                      float64     `orm:"expenditure" json:"expenditure"`                                               // 总支出（元）
	PayDate                          string      `orm:"pay_date" json:"payDate"`                                                      // 支付时间
	CreatedAt                        *gtime.Time `orm:"created_at" json:"createdAt"`                                                  // 创建时间
	UpdatedAt                        *gtime.Time `orm:"updated_at" json:"updatedAt"`                                                  // 更新时间
}
