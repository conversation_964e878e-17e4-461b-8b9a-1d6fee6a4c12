// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-07-23 14:36:18
// 生成路径: internal/app/applet/dao/s_coupon_code.go
// 生成人：lx
// desc:兑换码表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sCouponCodeDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sCouponCodeDao struct {
	*internal.SCouponCodeDao
}

var (
	// SCouponCode is globally public accessible object for table tools_gen_table operations.
	SCouponCode = sCouponCodeDao{
		internal.NewSCouponCodeDao(),
	}
)

// Fill with you ideas below.
