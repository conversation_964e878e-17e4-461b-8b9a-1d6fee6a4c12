// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/model/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdAnchorPointUploadInfoRes is the golang structure for table ad_anchor_point_upload.
type AdAnchorPointUploadInfoRes struct {
	gmeta.Meta    `orm:"table:ad_anchor_point_upload"`
	Id            int         `orm:"id,primary" json:"id" dc:"ID"` // ID
	AnchorType    string      `orm:"anchor_type" json:"anchorType"  dc:"可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点"`
	AnchorPointId int         `orm:"anchor_point_id" json:"anchorPointId" dc:"业务的字段锚点id"` // 业务的字段锚点id
	AnchorId      string      `orm:"anchor_id" json:"anchorId" dc:"原生锚点id"`               // 原生锚点id
	CreateTime    *gtime.Time `orm:"create_time" json:"createTime" dc:"创建时间"`             // 创建时间
}

type AdAnchorPointUploadListRes struct {
	Id            int         `json:"id" dc:"ID"`
	AnchorType    string      `json:"anchorType" dc:"可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点"`
	AnchorPointId int         `json:"anchorPointId" dc:"业务的字段锚点id"`
	AnchorId      string      `json:"anchorId" dc:"原生锚点id"`
	CreateTime    *gtime.Time `json:"createTime" dc:"创建时间"`
}

// AdAnchorPointUploadSearchReq 分页请求参数
type AdAnchorPointUploadSearchReq struct {
	comModel.PageReq
	Id            string `p:"id" dc:"ID"` //ID
	AnchorType    string `p:"anchorType" dc:"可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点"`
	AnchorPointId string `p:"anchorPointId" v:"anchorPointId@integer#业务的字段锚点id需为整数" dc:"业务的字段锚点id"`     //业务的字段锚点id
	AnchorId      string `p:"anchorId" dc:"原生锚点id"`                                                     //原生锚点id
	CreateTime    string `p:"createTime" v:"createTime@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// AdAnchorPointUploadSearchRes 列表返回结果
type AdAnchorPointUploadSearchRes struct {
	comModel.ListRes
	List []*AdAnchorPointUploadListRes `json:"list"`
}

// AdAnchorPointUploadAddReq 添加操作请求参数
type AdAnchorPointUploadAddReq struct {
	AnchorType    string      `p:"anchorType" v:"required#可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点不能为空" dc:"可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点"`
	AnchorPointId int         `p:"anchorPointId" v:"required#业务的字段锚点id不能为空" dc:"业务的字段锚点id"`
	AnchorId      string      `p:"anchorId" v:"required#原生锚点id不能为空" dc:"原生锚点id"`
	CreateTime    *gtime.Time `p:"createTime" v:"required#创建时间不能为空" dc:"创建时间"`
}

// AdAnchorPointUploadEditReq 修改操作请求参数
type AdAnchorPointUploadEditReq struct {
	Id            int         `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AnchorType    string      `p:"anchorType" v:"required#可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点不能为空" dc:"可选值:APP_GAME 游戏锚点APP_INTERNET_SERVICE 网服锚点APP_SHOP 电商锚点PRIVATE_CHAT 咨询锚点SHOPPING_CART 购物锚点"`
	AnchorPointId int         `p:"anchorPointId" v:"required#业务的字段锚点id不能为空" dc:"业务的字段锚点id"`
	AnchorId      string      `p:"anchorId" v:"required#原生锚点id不能为空" dc:"原生锚点id"`
	CreateTime    *gtime.Time `p:"createTime" v:"required#创建时间不能为空" dc:"创建时间"`
}
