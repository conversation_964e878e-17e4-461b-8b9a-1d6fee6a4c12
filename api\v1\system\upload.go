package system

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/common"
)

// UploadMultipleReq 分片上传
type UploadMultipleReq struct {
	g.Meta `path:"/upload/multipleImg" tags:"后台文件上传" method:"post" summary:"上传多图片"`
	File   ghttp.UploadFiles `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}

// 单图上传
type UploadSingleImgReq struct {
	g.Meta `path:"/upload/singleImg" tags:"后台文件上传" method:"post" summary:"上传图片"`
	File   *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件" v:"required#上传文件必须"`
}

// 单文件上传
type UploadSingleFileReq struct {
	g.Meta `path:"/upload/singleFile" tags:"后台文件上传" method:"post" summary:"上传文件"`
	File   *ghttp.UploadFile `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}

type UploadSingleRes struct {
	g.Meta `mime:"application/json"`
	UploadResponse
}

// 多图上传
type UploadMultipleImgReq struct {
	g.Meta `path:"/upload/multipleImg" tags:"后台文件上传" method:"post" summary:"上传多图片"`
	File   ghttp.UploadFiles `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}

// 多文件上传
type UploadMultipleFileReq struct {
	g.Meta `path:"/upload/multipleFile" tags:"后台文件上传" method:"post" summary:"上传多文件"`
	File   ghttp.UploadFiles `p:"file" type:"file" dc:"选择上传文件"  v:"required#上传文件必须"`
}

type UploadMultipleRes []*UploadResponse

type UploadResponse struct {
	Size     int64  `json:"size"   dc:"文件大小"`
	Path     string `json:"path" dc:"文件相对路径"`
	FullPath string `json:"fullPath" dc:"文件绝对路径"`
	Name     string `json:"name" dc:"文件名称"`
	Type     string `json:"type" dc:"文件类型"`
}

type MultipartUploadResponse struct {
	Bucket       string `json:"Bucket,omitempty"`
	Key          string `json:"Key,omitempty"`
	UploadID     string `json:"UploadID,omitempty"`
	EncodingType string `json:"EncodingType,omitempty"`
}

type GetStSReq struct {
	g.Meta `path:"/upload/getSts" tags:"后台文件上传" method:"get" summary:"获取上传凭证"`
	common.Author
}

type GetUpIdReq struct {
	g.Meta `path:"/get/uploadId" tags:"后台文件上传" method:"get" summary:"获取UploadId"`
	common.Author
	Ak, Sk, StToken string
}

type GetUpIdRes struct {
	g.Meta `mime:"application/json"`
	UpIdRes
}

type StSRes struct {
	g.Meta `mime:"application/json"`
	GetStSRes
}

type GetStSRes struct {
	CurrentTime     string
	ExpiredTime     string
	AccessKeyId     string
	SecretAccessKey string
	SessionToken    string
}

type UpIdRes struct {
	UploadID string `json:"UploadID,omitempty"`
}
