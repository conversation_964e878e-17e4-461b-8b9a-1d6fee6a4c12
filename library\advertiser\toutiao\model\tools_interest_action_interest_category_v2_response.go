/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

// ToolsInterestActionInterestCategoryV2Response struct for ToolsInterestActionInterestCategoryV2Response
type ToolsInterestActionInterestCategoryV2Response struct {
	//
	Code *int64                                               `json:"code,omitempty"`
	Data []*ToolsInterestActionInterestCategoryV2ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

type ToolsInterestActionInterestCategoryV2ResponseData struct {
	Id       *string                                              `json:"id"`
	Name     *string                                              `json:"name"`
	Num      *string                                              `json:"num"`
	Children []*ToolsInterestActionInterestCategoryV2ResponseData `json:"children"`
}
