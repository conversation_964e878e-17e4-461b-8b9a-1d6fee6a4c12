/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service  上传广告图片
type ToolsSiteHandselV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.ToolsSiteHandselV2Request
}

func (r *ToolsSiteHandselV2ApiService) SetCfg(cfg *conf.Configuration) *ToolsSiteHandselV2ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsSiteHandselV2ApiService) SetRequest(req models.ToolsSiteHandselV2Request) *ToolsSiteHandselV2ApiService {
	r.Request = &req
	return r
}

func (r *ToolsSiteHandselV2ApiService) AccessToken(accessToken string) *ToolsSiteHandselV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsSiteHandselV2ApiService) Do() (data *models.ToolsSiteHandselV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/tools/site/handsel/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.ToolsSiteHandselV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsSiteHandselV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/tools/site/handsel/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
