// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: internal/app/ad/controller/ad_material_album.go
// 生成人：cyao
// desc:广告素材专辑
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adMaterialAlbumController struct {
	systemController.BaseController
}

var AdMaterialAlbum = new(adMaterialAlbumController)

// List 列表
func (c *adMaterialAlbumController) List(ctx context.Context, req *ad.AdMaterialAlbumSearchReq) (res *ad.AdMaterialAlbumSearchRes, err error) {
	res = new(ad.AdMaterialAlbumSearchRes)
	res.AdMaterialAlbumSearchRes, err = service.AdMaterialAlbum().List(ctx, &req.AdMaterialAlbumSearchReq)
	return
}

// SpecialProductList 文件夹视图列表
func (c *adMaterialAlbumController) SpecialProductList(ctx context.Context, req *ad.SpecialProductListReq) (res *ad.SpecialProductListRes, err error) {
	res = new(ad.SpecialProductListRes)
	res.SpecialProductListRes, err = service.AdMaterialAlbum().SpecialProductList(ctx, &req.SpecialProductListReq)
	return
}

// Get 获取广告素材专辑
func (c *adMaterialAlbumController) Get(ctx context.Context, req *ad.AdMaterialAlbumGetReq) (res *ad.AdMaterialAlbumGetRes, err error) {
	res = new(ad.AdMaterialAlbumGetRes)
	res.AdMaterialAlbumInfoRes, err = service.AdMaterialAlbum().GetByAlbumId(ctx, req.AlbumId)
	return
}

// Add 添加广告素材专辑
func (c *adMaterialAlbumController) Add(ctx context.Context, req *ad.AdMaterialAlbumAddReq) (res *ad.AdMaterialAlbumAddRes, err error) {
	err = service.AdMaterialAlbum().Add(ctx, req.AdMaterialAlbumAddReq)
	return
}

// Edit 修改广告素材专辑
func (c *adMaterialAlbumController) Edit(ctx context.Context, req *ad.AdMaterialAlbumEditReq) (res *ad.AdMaterialAlbumEditRes, err error) {
	err = service.AdMaterialAlbum().Edit(ctx, req.AdMaterialAlbumEditReq)
	return
}

// Delete 删除广告素材专辑
func (c *adMaterialAlbumController) Delete(ctx context.Context, req *ad.AdMaterialAlbumDeleteReq) (res *ad.AdMaterialAlbumDeleteRes, err error) {
	err = service.AdMaterialAlbum().Delete(ctx, req.AlbumIds)
	return
}
