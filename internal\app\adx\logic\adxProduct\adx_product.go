// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:33:58
// 生成路径: internal/app/adx/logic/adx_product.go
// 生成人：cq
// desc:ADX产品信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdxProduct(New())
}

func New() service.IAdxProduct {
	return &sAdxProduct{}
}

type sAdxProduct struct{}

func (s *sAdxProduct) List(ctx context.Context, req *model.AdxProductSearchReq) (listRes *model.AdxProductSearchRes, err error) {
	listRes = new(model.AdxProductSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxProduct.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxProduct.Columns().Id+" = ?", req.Id)
		}
		if req.ProductName != "" {
			m = m.Where(dao.AdxProduct.Columns().ProductName+" like ?", "%"+req.ProductName+"%")
		}
		if req.Industry != "" {
			m = m.Where(dao.AdxProduct.Columns().Industry+" = ?", req.Industry)
		}
		if req.Icon != "" {
			m = m.Where(dao.AdxProduct.Columns().Icon+" = ?", req.Icon)
		}
		if req.ChargesType != "" {
			m = m.Where(dao.AdxProduct.Columns().ChargesType+" = ?", gconv.Uint(req.ChargesType))
		}
		if req.Type != "" {
			m = m.Where(dao.AdxProduct.Columns().Type+" = ?", gconv.Uint(req.Type))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxProductListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxProductListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxProductListRes{
				Id:          v.Id,
				ProductName: v.ProductName,
				Industry:    v.Industry,
				Icon:        v.Icon,
				ChargesType: v.ChargesType,
				Type:        v.Type,
			}
		}
	})
	return
}

func (s *sAdxProduct) GetExportData(ctx context.Context, req *model.AdxProductSearchReq) (listRes []*model.AdxProductInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxProduct.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxProduct.Columns().Id+" = ?", req.Id)
		}
		if req.ProductName != "" {
			m = m.Where(dao.AdxProduct.Columns().ProductName+" like ?", "%"+req.ProductName+"%")
		}
		if req.Industry != "" {
			m = m.Where(dao.AdxProduct.Columns().Industry+" = ?", req.Industry)
		}
		if req.Icon != "" {
			m = m.Where(dao.AdxProduct.Columns().Icon+" = ?", req.Icon)
		}
		if req.ChargesType != "" {
			m = m.Where(dao.AdxProduct.Columns().ChargesType+" = ?", gconv.Uint(req.ChargesType))
		}
		if req.Type != "" {
			m = m.Where(dao.AdxProduct.Columns().Type+" = ?", gconv.Uint(req.Type))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxProduct) GetById(ctx context.Context, id uint64) (res *model.AdxProductInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxProduct.Ctx(ctx).WithAll().Where(dao.AdxProduct.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxProduct) GetByName(ctx context.Context, name string, likeQuery bool) (res []*model.AdxProductInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxProduct.Ctx(ctx).WithAll()
		if likeQuery {
			m = m.WhereLike(dao.AdxProduct.Columns().ProductName, "%"+name+"%")
		} else {
			m = m.Where(dao.AdxProduct.Columns().ProductName, name)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxProduct) Add(ctx context.Context, req *model.AdxProductAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxProduct.Ctx(ctx).Insert(do.AdxProduct{
			Id:          req.Id,
			ProductName: req.ProductName,
			Industry:    req.Industry,
			Icon:        req.Icon,
			ChargesType: req.ChargesType,
			Type:        req.Type,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxProduct) BatchAdd(ctx context.Context, batchReq []*model.AdxProductAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdxProduct, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdxProduct{
				Id:          v.Id,
				ProductName: v.ProductName,
				Industry:    v.Industry,
				Icon:        v.Icon,
				ChargesType: v.ChargesType,
				Type:        v.Type,
			}
		}
		_, err = dao.AdxProduct.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxProduct) Edit(ctx context.Context, req *model.AdxProductEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxProduct.Ctx(ctx).WherePri(req.Id).Update(do.AdxProduct{
			ProductName: req.ProductName,
			Industry:    req.Industry,
			Icon:        req.Icon,
			ChargesType: req.ChargesType,
			Type:        req.Type,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxProduct) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxProduct.Ctx(ctx).Delete(dao.AdxProduct.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdxProduct) RunSyncAdxProductTask(ctx context.Context, req *model.AdxProductSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		startTime := req.StartTime
		endTime := req.EndTime
		for {
			if startTime > endTime {
				break
			}
			errors := s.SyncAdxProduct(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sAdxProduct) SyncAdxProductTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdxProductLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdxProductLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -1).Format("Y-m-d")
		err = s.SyncAdxProduct(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "同步ADX产品失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncAdxProductTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步ADX产品，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxProduct) SyncAdxProduct(ctx context.Context, updateDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var id int64 = 0
		var pageSize = 500
		var pullType = 2
		if updateDate == "" {
			pullType = 1
		}
		var pageIdKey string
		var ttlInSeconds int64
		if pullType == 1 {
			pageIdKey = commonConsts.PlatAdxProductPageId
		} else {
			pageIdKey = fmt.Sprintf("%s%s", commonConsts.PlatAdxProductDayPageId, updateDate)
			ttlInSeconds = commonConsts.PlatAdxPageIdTtlSeconds
		}
		data, _ := commonService.RedisCache().Get(ctx, pageIdKey)
		if !data.IsNil() {
			id = data.Int64()
		}
		for {
			productRes, err1 := api.GetAdxApiClient().ProductService.SetReq(api.ProductRequest{
				Id:         id,
				PageSize:   pageSize,
				PullType:   pullType,
				UpdateDate: updateDate,
			}).Do()
			if err1 != nil {
				g.Log().Error(ctx, err1)
				break
			}
			if len(productRes.Content) == 0 {
				break
			}
			var batchReq []*model.AdxProductAddReq
			for _, v := range productRes.Content {
				batchReq = append(batchReq, &model.AdxProductAddReq{
					Id:          uint64(v.Id),
					ProductName: v.ProductName,
					Industry:    v.Industry,
					Icon:        v.Icon,
					ChargesType: uint(v.ChargesType),
					Type:        uint(v.Type),
				})
			}
			err = s.BatchAdd(ctx, batchReq)
			if err != nil {
				g.Log().Error(ctx, err)
				break
			}
			if productRes.Page.PageId == id {
				break
			}
			id = productRes.Page.PageId
			if ttlInSeconds > 0 {
				_ = commonService.RedisCache().SetEX(ctx, pageIdKey, id, ttlInSeconds)
			} else {
				_, _ = commonService.RedisCache().Set(ctx, pageIdKey, id)
			}
		}
	})
	return
}
