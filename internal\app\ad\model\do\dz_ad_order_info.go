// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:38
// 生成路径: internal/app/ad/model/entity/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdOrderInfo is the golang structure for table dz_ad_order_info.
type DzAdOrderInfo struct {
	gmeta.Meta    `orm:"table:dz_ad_order_info, do:true"`
	Id            interface{} `orm:"id,primary" json:"id"`                 // 商户单号
	Ver           interface{} `orm:"ver" json:"ver"`                       // 小程序版本号
	OutTradeNo    interface{} `orm:"out_trade_no" json:"outTradeNo"`       // 交易单号
	Discount      interface{} `orm:"discount" json:"discount"`             // 订单金额（单位元）
	Type          interface{} `orm:"type" json:"type"`                     // 订单类型（1.看点充值 2.VIP）
	StatusNotify  interface{} `orm:"status_notify" json:"statusNotify"`    // 订单状态（0.待支付 1.支付成功）
	Ctime         interface{} `orm:"ctime" json:"ctime"`                   // 下单时间（秒）
	FinishTime    interface{} `orm:"finish_time" json:"finishTime"`        // 完成时间（秒）
	UserId        interface{} `orm:"user_id" json:"userId"`                // 用户ID
	ChannelId     interface{} `orm:"channel_id" json:"channelId"`          // 渠道ID
	Domain        interface{} `orm:"domain" json:"domain"`                 // 业务线（14抖音 16微信 17快手）
	SourceInfo    interface{} `orm:"source_info" json:"sourceInfo"`        // 短剧ID
	ChapterId     interface{} `orm:"chapter_id" json:"chapterId"`          // 剧集ID
	SourceDesc    interface{} `orm:"source_desc" json:"sourceDesc"`        // 短剧名称
	RegisterDate  interface{} `orm:"register_date" json:"registerDate"`    // 注册时间（秒）
	OpenId        interface{} `orm:"open_id" json:"openId"`                // 小程序openId
	Os            interface{} `orm:"os" json:"os"`                         // 机型（0:Android 1:iOS）
	ReferralId    interface{} `orm:"referral_id" json:"referralId"`        // 推广链接ID
	Adid          interface{} `orm:"adid" json:"adid"`                     // 计划ID
	FromDrId      interface{} `orm:"from_dr_id" json:"fromDrId"`           // 达人ID
	Platform      interface{} `orm:"platform" json:"platform"`             // 达人平台
	Scene         interface{} `orm:"scene" json:"scene"`                   // 进入场景
	ThirdCorpId   interface{} `orm:"third_corp_id" json:"thirdCorpId"`     // 三方来源企微主体
	ThirdWxId     interface{} `orm:"third_wx_id" json:"thirdWxId"`         // 三方企微唯一标识
	KdrId         interface{} `orm:"kdr_id" json:"kdrId"`                  // 挂载达人ID
	SelfReturn    interface{} `orm:"self_return" json:"selfReturn"`        // 自归因
	ProjectId     interface{} `orm:"project_id" json:"projectId"`          // 头条2.0参数 - 项目ID
	PromotionId   interface{} `orm:"promotion_id" json:"promotionId"`      // 头条2.0参数 - 推广ID
	SchannelTime  interface{} `orm:"schannel_time" json:"schannelTime"`    // 抖音快手注册时间
	DyeTime       interface{} `orm:"dye_time" json:"dyeTime"`              // 抖音快手推广链接染色时间
	XuniPay       interface{} `orm:"xuni_pay" json:"xuniPay"`              // 虚拟支付订单（1=是）
	MoneyBenefit  interface{} `orm:"money_benefit" json:"moneyBenefit"`    // 渠道分成金额
	Mid1          interface{} `orm:"mid1" json:"mid1"`                     // 素材ID - 图片
	Mid2          interface{} `orm:"mid2" json:"mid2"`                     // 素材ID - 标题
	Mid3          interface{} `orm:"mid3" json:"mid3"`                     // 素材ID - 视频
	UnionId       interface{} `orm:"union_id" json:"unionId"`              // 小程序unionId
	From          interface{} `orm:"from" json:"from"`                     // 投放媒体（如bd/dy/ks等）
	OrderSubType  interface{} `orm:"order_sub_type" json:"orderSubType"`   // 订单业务类型（如整本购）
	WxFinderId    interface{} `orm:"wx_finder_id" json:"wxFinderId"`       // 视频号ID
	WxExportId    interface{} `orm:"wx_export_id" json:"wxExportId"`       // 视频ID
	WxPromotionId interface{} `orm:"wx_promotion_id" json:"wxPromotionId"` // 加热订单ID
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`          // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt"`          // 更新时间
}
