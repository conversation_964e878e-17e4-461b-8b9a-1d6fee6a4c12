// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-07 11:44:22
// 生成路径: internal/app/ad/controller/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdAccountInfoController struct {
	systemController.BaseController
}

var KsAdAccountInfo = new(ksAdAccountInfoController)

// List 列表
func (c *ksAdAccountInfoController) List(ctx context.Context, req *ad.KsAdAccountInfoSearchReq) (res *ad.KsAdAccountInfoSearchRes, err error) {
	res = new(ad.KsAdAccountInfoSearchRes)
	res.KsAdAccountInfoSearchRes, err = service.KsAdAccountInfo().List(ctx, &req.KsAdAccountInfoSearchReq)
	return
}

// Get 获取广告主资质信息余额信息
func (c *ksAdAccountInfoController) Get(ctx context.Context, req *ad.KsAdAccountInfoGetReq) (res *ad.KsAdAccountInfoGetRes, err error) {
	res = new(ad.KsAdAccountInfoGetRes)
	res.KsAdAccountInfoInfoRes, err = service.KsAdAccountInfo().GetById(ctx, req.AdvertiserId)
	return
}

// Add 添加广告主资质信息余额信息
func (c *ksAdAccountInfoController) Add(ctx context.Context, req *ad.KsAdAccountInfoAddReq) (res *ad.KsAdAccountInfoAddRes, err error) {
	err = service.KsAdAccountInfo().Add(ctx, req.KsAdAccountInfoAddReq)
	return
}

// Edit 修改广告主资质信息余额信息
func (c *ksAdAccountInfoController) Edit(ctx context.Context, req *ad.KsAdAccountInfoEditReq) (res *ad.KsAdAccountInfoEditRes, err error) {
	err = service.KsAdAccountInfo().Edit(ctx, req.KsAdAccountInfoEditReq)
	return
}

// Delete 删除广告主资质信息余额信息
func (c *ksAdAccountInfoController) Delete(ctx context.Context, req *ad.KsAdAccountInfoDeleteReq) (res *ad.KsAdAccountInfoDeleteRes, err error) {
	err = service.KsAdAccountInfo().Delete(ctx, req.AdvertiserIds)
	return
}

// GetKsAdAccountList 获取快手账户列表
func (c *ksAdAccountInfoController) GetKsAdAccountList(ctx context.Context, req *ad.GetKsAdAccountListReq) (res *ad.GetKsAdAccountListRes, err error) {
	res = new(ad.GetKsAdAccountListRes)
	res.List, err = service.KsAdAccountInfo().GetKsAdAccountList(ctx, req.AccessToken, req.Aid, req.AppId)
	return
}
