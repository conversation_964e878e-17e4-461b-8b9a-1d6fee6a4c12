package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

// BatchDownloadReq 批量下载
type BatchDownloadReq struct {
	g.Meta  `path:"/batch/download" tags:"批量下载" method:"post" summary:"批量下载"`
	Urls    []string `json:"urls"`
	ZipName string   `json:"zipName"`
}

// BatchDownloadRes 批量下载返回
type BatchDownloadRes struct {
	commonApi.EmptyRes
}

// MiniCheckFileDownloadReq 小程序校验文件下载请求参数
type MiniCheckFileDownloadReq struct {
	g.Meta `path:"/mini/check/download" tags:"批量下载" method:"post" summary:"小程序校验文件下载"`
	commonApi.Author
}

// MiniCheckFileDownloadRes 小程序校验文件下载返回
type MiniCheckFileDownloadRes struct {
	commonApi.EmptyRes
}
