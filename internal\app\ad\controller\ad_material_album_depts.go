// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-13 15:31:08
// 生成路径: internal/app/ad/controller/ad_material_album_depts.go
// 生成人：cyao
// desc:广告素材专辑和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adMaterialAlbumDeptsController struct {
	systemController.BaseController
}

var AdMaterialAlbumDepts = new(adMaterialAlbumDeptsController)

// List 列表
func (c *adMaterialAlbumDeptsController) List(ctx context.Context, req *ad.AdMaterialAlbumDeptsSearchReq) (res *ad.AdMaterialAlbumDeptsSearchRes, err error) {
	res = new(ad.AdMaterialAlbumDeptsSearchRes)
	res.AdMaterialAlbumDeptsSearchRes, err = service.AdMaterialAlbumDepts().List(ctx, &req.AdMaterialAlbumDeptsSearchReq)
	return
}

// Get 获取广告素材专辑和部门关联
func (c *adMaterialAlbumDeptsController) Get(ctx context.Context, req *ad.AdMaterialAlbumDeptsGetReq) (res *ad.AdMaterialAlbumDeptsGetRes, err error) {
	res = new(ad.AdMaterialAlbumDeptsGetRes)
	res.AdMaterialAlbumDeptsInfoRes, err = service.AdMaterialAlbumDepts().GetByAlbumId(ctx, req.AlbumId)
	return
}

// Add 添加广告素材专辑和部门关联
func (c *adMaterialAlbumDeptsController) Add(ctx context.Context, req *ad.AdMaterialAlbumDeptsAddReq) (res *ad.AdMaterialAlbumDeptsAddRes, err error) {
	err = service.AdMaterialAlbumDepts().Add(ctx, req.AdMaterialAlbumDeptsAddReq)
	return
}

// Edit 修改广告素材专辑和部门关联
func (c *adMaterialAlbumDeptsController) Edit(ctx context.Context, req *ad.AdMaterialAlbumDeptsEditReq) (res *ad.AdMaterialAlbumDeptsEditRes, err error) {
	err = service.AdMaterialAlbumDepts().Edit(ctx, req.AdMaterialAlbumDeptsEditReq)
	return
}

// Delete 删除广告素材专辑和部门关联
func (c *adMaterialAlbumDeptsController) Delete(ctx context.Context, req *ad.AdMaterialAlbumDeptsDeleteReq) (res *ad.AdMaterialAlbumDeptsDeleteRes, err error) {
	err = service.AdMaterialAlbumDepts().Delete(ctx, req.AlbumIds)
	return
}
