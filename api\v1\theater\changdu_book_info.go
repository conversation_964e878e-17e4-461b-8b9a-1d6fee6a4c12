// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-11 14:01:12
// 生成路径: api/v1/theater/changdu_book_info.go
// 生成人：cq
// desc:常读短剧相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// ChangduBookInfoSearchReq 分页请求参数
type ChangduBookInfoSearchReq struct {
	g.Meta `path:"/list" tags:"常读短剧" method:"get" summary:"常读短剧列表"`
	commonApi.Author
	model.ChangduBookInfoSearchReq
}

// ChangduBookInfoSearchRes 列表返回结果
type ChangduBookInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.ChangduBookInfoSearchRes
}

// ChangduBookInfoAddReq 添加操作请求参数
type ChangduBookInfoAddReq struct {
	g.Meta `path:"/add" tags:"常读短剧" method:"post" summary:"常读短剧添加"`
	commonApi.Author
	*model.ChangduBookInfoAddReq
}

// ChangduBookInfoAddRes 添加操作返回结果
type ChangduBookInfoAddRes struct {
	commonApi.EmptyRes
}

// ChangduBookInfoEditReq 修改操作请求参数
type ChangduBookInfoEditReq struct {
	g.Meta `path:"/edit" tags:"常读短剧" method:"put" summary:"常读短剧修改"`
	commonApi.Author
	*model.ChangduBookInfoEditReq
}

// ChangduBookInfoEditRes 修改操作返回结果
type ChangduBookInfoEditRes struct {
	commonApi.EmptyRes
}

// ChangduBookInfoGetReq 获取一条数据请求
type ChangduBookInfoGetReq struct {
	g.Meta `path:"/get" tags:"常读短剧" method:"get" summary:"获取常读短剧信息"`
	commonApi.Author
	BookId string `p:"bookId" v:"required#主键必须"` //通过主键获取
}

// ChangduBookInfoGetRes 获取一条数据结果
type ChangduBookInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.ChangduBookInfoInfoRes
}

// ChangduBookInfoDeleteReq 删除数据请求
type ChangduBookInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"常读短剧" method:"delete" summary:"删除常读短剧"`
	commonApi.Author
	BookIds []string `p:"bookIds" v:"required#主键必须"` //通过主键删除
}

// ChangduBookInfoDeleteRes 删除数据返回
type ChangduBookInfoDeleteRes struct {
	commonApi.EmptyRes
}

// ChangduBookInfoSyncReq 同步数据请求
type ChangduBookInfoSyncReq struct {
	g.Meta `path:"/sync" tags:"常读短剧" method:"post" summary:"同步常读短剧数据"`
	commonApi.Author
}

// ChangduBookInfoSyncRes 同步数据返回
type ChangduBookInfoSyncRes struct {
	commonApi.EmptyRes
}

// ChangduBookInfoNotifyReq 常读短剧更新通知请求
type ChangduBookInfoNotifyReq struct {
	g.Meta `path:"/sendBookDeliveryStatusReport" tags:"常读短剧" method:"post" summary:"常读短剧更新通知"`
	commonApi.Author
}

// ChangduBookInfoNotifyRes 常读短剧更新通知返回
type ChangduBookInfoNotifyRes struct {
	commonApi.EmptyRes
}
