// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:22
// 生成路径: api/v1/oceanengine/ad_strategy_project_config.go
// 生成人：gfast
// desc:巨量广告搭建-新建项目相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	toutiaoApi "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/api"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// AdStrategyProjectConfigSearchReq 分页请求参数
type AdStrategyProjectConfigSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-新建项目" method:"post" summary:"巨量广告搭建-新建项目列表"`
	commonApi.Author
	model.AdStrategyProjectConfigSearchReq
}

// AdStrategyProjectConfigSearchRes 列表返回结果
type AdStrategyProjectConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyProjectConfigSearchRes
}

// AdStrategyProjectConfigAddReq 添加操作请求参数
type AdStrategyProjectConfigAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-新建项目" method:"post" summary:"巨量广告搭建-新建项目添加"`
	commonApi.Author
	*model.AdStrategyProjectConfigAddReq
}

// AdStrategyProjectConfigAddRes 添加操作返回结果
type AdStrategyProjectConfigAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyProjectConfigEditReq 修改操作请求参数
type AdStrategyProjectConfigEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-新建项目" method:"put" summary:"巨量广告搭建-新建项目修改"`
	commonApi.Author
	*model.AdStrategyProjectConfigEditReq
}

// AdStrategyProjectConfigEditRes 修改操作返回结果
type AdStrategyProjectConfigEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyProjectConfigGetReq 获取一条数据请求
type AdStrategyProjectConfigGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-新建项目" method:"get" summary:"获取巨量广告搭建-新建项目信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyProjectConfigGetRes 获取一条数据结果
type AdStrategyProjectConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyProjectConfigInfoRes
}

// AdStrategyProjectConfigDeleteReq 删除数据请求
type AdStrategyProjectConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-新建项目" method:"post" summary:"删除巨量广告搭建-新建项目"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyProjectConfigDeleteRes 删除数据返回
type AdStrategyProjectConfigDeleteRes struct {
	commonApi.EmptyRes
}

// GetDpaProductAvailableReq 获取商品库信息请求
type GetDpaProductAvailableReq struct {
	g.Meta `path:"/getDpaProductAvailable" tags:"巨量广告搭建-新建项目" method:"post" summary:"获取商品库信息"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds" v:"required#广告主ID列表必须" dc:"广告主ID列表"`
	Intersect     int      `p:"intersect" dc:"0: 并集 1：交集"`
}

// GetDpaProductAvailableRes 获取商品库信息返回
type GetDpaProductAvailableRes struct {
	commonApi.EmptyRes
	*model.GetDpaProductAvailableRes
}

// GetDpaProductListReq 获取商品列表请求
type GetDpaProductListReq struct {
	g.Meta `path:"/getDpaProductList" tags:"巨量广告搭建-新建项目" method:"post" summary:"获取商品列表"`
	commonApi.Author
	*model.GetDpaProductListReq
}

// GetDpaProductListRes 获取商品列表返回
type GetDpaProductListRes struct {
	commonApi.EmptyRes
	*model.GetDpaProductListRes
}

// GetDpaClueProductListReq 获取升级版商品列表请求
type GetDpaClueProductListReq struct {
	g.Meta `path:"/getDpaClueProductList" tags:"巨量广告搭建-新建项目" method:"post" summary:"获取升级版商品列表"`
	commonApi.Author
	*model.GetDpaClueProductListReq
}

// GetDpaClueProductListRes 获取商品列表返回
type GetDpaClueProductListRes struct {
	commonApi.EmptyRes
	*model.GetDpaClueProductListRes
}

// EventManagerOptimizedGoalGet
type EventManagerOptimizedGoalGetReq struct {
	g.Meta `path:"/eventManagerOptimizedGoalGet" tags:"巨量广告搭建-新建项目" method:"post" summary:"获取可用优化目标（巨量广告升级版）"`
	commonApi.Author
	Req toutiaoApi.ApiOpenApiV30EventManagerOptimizedGoalGetV2GetRequest
}

type EventManagerOptimizedGoalGetRes struct {
	commonApi.EmptyRes
	*toutiaoModels.EventManagerOptimizedGoalGetV2V30Response
}

// EventManagerDeepBidTypeGetReq
type EventManagerDeepBidTypeGetReq struct {
	g.Meta `path:"/eventManagerDeepBidTypeGet" tags:"巨量广告搭建-新建项目" method:"post" summary:"获取深度优化类型（巨量广告升级版）"`
	commonApi.Author
	Req toutiaoApi.ApiOpenApiV30EventManagerDeepBidTypeGetGetRequest
}

type EventManagerDeepBidTypeGetRes struct {
	commonApi.EmptyRes
	*toutiaoModels.EventManagerDeepBidTypeGetV30Response
}
