package model

// AccountMetricsData 广告主账户指标数据
type AccountMetricsData struct {
	//-------------------------------展现数据-----------------------
	// 表示广告在投放期内的预估花费金额。当天数据可能会有波动，次日稳定
	StatCost string `orm:"stat_cost" json:"statCost" dc:"消耗"`
	// 广告展示给用户的次数。计算方式：经平台判定有效且被计费的展示次数。
	ShowCnt string `orm:"show_cnt" json:"showCnt" dc:"展示数"`
	// 广告平均每一千次展现所付出的费用，计算公式是：总消耗/展示数*1000。
	CpmPlatform string `orm:"cpm_platform" json:"cpmPlatform" dc:"平均千次展现费用(元)"`
	// 当用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击。
	ClickCnt string `orm:"click_cnt" json:"clickCnt" dc:"点击数"`
	// 广告被点击的次数占展示次数的百分比。计算方法：点击数/展示数*100%
	Ctr string `orm:"ctr" json:"ctr" dc:"点击率"`
	// 广告主为每次点击付出的费用成本，计算公式是：总消耗/点击数。
	CpcPlatform string `orm:"cpc_platform" json:"cpcPlatform" dc:"平均点击单价(元)"`
	//-------------------转化数据-------------------
	// 按转化事件发生时间统计的转化数。建议广告主考核成本时参考“转化数据(计费时间)”，例如您的广告在早上8点进行了展示和点击，用户晚上19点发生了激活行为，我们会把激活数披露在晚上19点。
	ConvertCnt string `orm:"convert_cnt" json:"convertCnt" dc:"转化数"`
	// 广告主为每个转化所付出的平均成本，计算方式：总消耗/转化数。当天数据可能会有波动。
	ConversionCost string `orm:"conversion_cost" json:"conversionCost" dc:"平均转化成本"`
	// 广告被用户转化的次数占点击次数的百分比。计算方式：转化数/点击数*100%
	ConversionRate string `orm:"conversion_rate" json:"conversionRate" dc:"转化率"`
	// 将深度转化数记录在转化事件发生的时间上。建议广告主考核深度转化成本时参考“深度转化数（计费时间）”例如您的广告在早上8点进行了展示和点击，用户晚上19点发生了激活行为，我们会把激活数披露在晚上19点。
	DeepConvertCnt string `orm:"deep_convert_cnt" json:"deepConvertCnt" dc:"深度转化数"`
	// 广告主为每个深度转化所付出的平均成本，计算方法：总消耗/深度转化数。当天数据可能会有波动，次日早8点后稳定。
	DeepConvertCost string `orm:"deep_convert_cost" json:"deepConvertCost" dc:"深度转化成本"`
	// 广告被用户进行深度转化的次数占转化次数的百分比。计算方式：深度转化数/转化数*100%
	DeepConvertRate string `orm:"deep_convert_rate" json:"deepConvertRate" dc:"深度转化率"`
	// ----------------- 转化数据(计费时间)-------------------
	// 在转化行为发生（或回传）之后，将转化行为回记到过去30天内的扣费（消耗产生）时间上。 例如：广告在8月20日展示给用户，此时广告花费10元，用户点击广告后于8月23日产生1笔购买，则8月23日这笔购买将会展示在8月20日，8月23日没有转化数。
	AttributionConvertCnt string `orm:"attribution_convert_cnt" json:"attributionConvertCnt" dc:"转化数(计费时间)"`
	// 转化成本(计费时间) = 消耗 / 转化数(计费时间)。例如：广告在8月20日展示给用户，此时广告花费10元，用户点击广告后，于8月23日产生2笔购买，则8月20日的转化成本（计费时间） = 5元（即10元除以2笔）。成本考核和系统赔付以该指标为准。
	AttributionConvertCost string `orm:"attribution_convert_cost" json:"attributionConvertCost" dc:"转化成本(计费时间)"`
	// 所选时间范围内的点击产生转化的概率，归因到计费时间。  转化率(计费时间) = 转化数（计费时间） / 点击数
	//AttributionConversionRate string `orm:"attribution_conversion_rate" json:"attributionConversionRate" dc:"转化率(计费时间) "`
	// 在转化行为发生（或回传）之后，将转化行为回记到过去30天内的扣费（消耗产生）时间上。 例如：广告在8月20日展示给用户，此时广告花费10元，用户点击广告后于8月23日产生1笔购买，则8月23日这笔购买将会展示在8月20日。
	AttributionDeepConvertCnt string `orm:"attribution_deep_convert_cnt" json:"attributionDeepConvertCnt" dc:"深度转化数(计费时间)"`
	// 是一个准确的深度转化成本评估指标。计算方式：消耗 / 深度转化数(计费时间)。例如：广告在8月20日展示给用户，此时广告花费10元，用户点击广告后，于 8 月 23 日产生2笔购买，则8月20日的深度转化成本（计费时间） = 5元（即10元除以2笔）。成本考核和系统赔付以该指标为准。
	AttributionDeepConvertCost string `orm:"attribution_deep_convert_cost" json:"attributionDeepConvertCost" dc:"深度转化成本(计费时间)"`
	// 深度转化数（计费时间）/转化数（计费时间）*100%
	//AttributionDeepConvertRate string `orm:"attribution_deep_convert_rate" json:"attributionDeepConvertRate" dc:"深度转化率(计费时间)"`
	// 系统根据您所选时间范围内展示的广告所触达的人群，通过算法来预估未来将会产生的总转化数（会将当前尚未转化，但未来可能转化的数据统计进来）。当转化成本偏高时，建议参考预估转化成本。
	PreConvertCount string `orm:"pre_convert_count" json:"preConvertCount" dc:"预估转化数(计费时间)"`
	// 消耗/预估转化数(计费时间)
	PreConvertCost string `orm:"pre_convert_cost" json:"preConvertCost" dc:"预估转化成本(计费时间)"`
	// 预估转化数(计费时间)/点击数
	PreConvertRate string `orm:"pre_convert_rate" json:"preConvertRate" dc:"预估转化率(计费时间)"`

	//--------------------应用推广--------------------
	// 用户在落地页和应用商店中开始安装包下载进程的次数，仅安卓下载可以监测到该行为
	ClickStartCnt string `orm:"click_start_cnt" json:"clickStartCnt" dc:"安卓下载开始数"`
	// 计算方法：总花费/安卓下载开始数。
	ClickStartCost string `orm:"click_start_cost" json:"clickStartCost" dc:"安卓下载开始成本"`
	// 计算方法：安卓下载开始数/点击数
	ClickStartRate string `orm:"click_start_rate" json:"clickStartRate" dc:"安卓下载开始率"`
	// 用户在落地页和应用商店中下载安装包进程完成的次数，仅安卓下载可以监测到该行为
	DownloadFinishCnt string `orm:"download_finish_cnt" json:"downloadFinishCnt" dc:"安卓下载完成数"`
	// 计算方式：总花费/安卓下载完成数。
	DownloadFinishCost string `orm:"download_finish_cost" json:"downloadFinishCost" dc:"安卓下载完成成本"`
	// 计算方式：安卓下载完成数/安卓下载开始数
	DownloadFinishRate string `orm:"download_finish_rate" json:"downloadFinishRate" dc:"安卓下载完成率"`
	// 用户在落地页中将安装包安装完成的次数，仅安卓下载可以监测到该行为。如果您的计划开启了商店直投，则下载相关行为数据有部分无法被监测到，而安装数据不受影响，因此安装数可能会大于下载数。
	InstallFinishCnt string `orm:"install_finish_cnt" json:"installFinishCnt" dc:"安卓安装完成数"`
	// 计算方式：总花费/安卓安装完成数
	InstallFinishCost string `orm:"install_finish_cost" json:"installFinishCost" dc:"安卓安装完成成本"`
	// 计算方式：安卓安装完成数/安卓下载完成数
	InstallFinishRate string `orm:"install_finish_rate" json:"installFinishRate" dc:"安卓安装完成率"`
	// 如果您对接了API，激活数是您认可且回传成功的的激活数。如果您对接了SDK，则激活数是指用户下载您的APP后打开的次数。
	Active string `orm:"active" json:"active" dc:"激活数"`
	// 计算方式：总花费/激活数。
	ActiveCost string `orm:"active_cost" json:"activeCost" dc:"激活成本"`
	// 计算方式：激活数/点击数*100%
	ActiveRate string `orm:"active_rate" json:"activeRate" dc:"激活率"`
	// 如果您对接了API，注册数是您认可且回传成功的的注册数。如果您对接了SDK，则注册数是用户实现注册行为的次数
	ActiveRegister string `orm:"active_register" json:"activeRegister" dc:"注册数"`
	// 广告主为每个注册所付出的成本，计算公式是：总花费/注册数，当天数据可能会有波动，次日早8点后稳定。
	ActiveRegisterCost string `orm:"active_register_cost" json:"activeRegisterCost" dc:"注册成本"`
	// 注册用户占激活用户的比例
	ActiveRegisterRate string `orm:"active_register_rate" json:"activeRegisterRate" dc:"注册率"`
	// 有APP内关键行为的用户数量
	GameAddiction string `orm:"game_addiction" json:"gameAddiction" dc:"关键行为数"`
	// 广告主为每个有APP内关键行为的用户所付出的成本，计算公式是总花费/关键行为数。当天数据可能会有波动，次日早8点后稳定。
	GameAddictionCost string `orm:"game_addiction_cost" json:"gameAddictionCost" dc:"关键行为成本"`
	// 关键行为用户占激活用户的比例
	GameAddictionRate string `orm:"game_addiction_rate" json:"gameAddictionRate" dc:"关键行为率"`
	// 当日激活用户在第二天继续登录，则计为一个次留行为。该指标会将收到的次留回传数据，匹配到对应的激活时间上。 例如：8月1日有10个激活，其中5个在8月2日继续登录，则8月1日的次留数为5。
	AttributionNextDayOpenCnt string `orm:"attribution_next_day_open_cnt" json:"attributionNextDayOpenCnt" dc:"次留数"`
	// 次留成本=消耗/次留数
	AttributionNextDayOpenCost string `orm:"attribution_next_day_open_cost" json:"attributionNextDayOpenCost" dc:"次留成本"`
	// 次留率=次留数/激活数
	AttributionNextDayOpenRate string `orm:"attribution_next_day_open_rate" json:"attributionNextDayOpenRate" dc:"次留率"`
	// 根据您通过api或sdk回传给我们的时间为准，将次留数披露在回传时间。
	NextDayOpen string `orm:"next_day_open" json:"nextDayOpen" dc:"次留回传数"`
	// 用户在应用内首次完成付费的次数
	ActivePay string `orm:"active_pay" json:"activePay" dc:"首次付费数"`
	// 用户在应用内首次完成付费的成本，计算方式：消耗/首次付费数。
	ActivePayCost string `orm:"active_pay_cost" json:"activePayCost" dc:"首次付费成本"`
	// 计算方式：首次付费数/激活数。
	ActivePayRate string `orm:"active_pay_rate" json:"activePayRate" dc:"首次付费率"`
	// 当天用户在应用内完成付费的总次数，多次付费会重复计数。
	GamePayCount string `orm:"game_pay_count" json:"gamePayCount" dc:"付费次数"`
	// 当天用户在应用内完成付费的平均成本，计算方式：消耗/付费次数。
	GamePayCost string `orm:"game_pay_cost" json:"gamePayCost" dc:"付费成本"`
	// 用户激活应用后的7天内，在应用内完成付费的总次数，并将付费次数披露在每个用户的激活时间上。
	AttributionGamePay7dCount string `orm:"attribution_game_pay_7d_count" json:"attributionGamePay7dCount" dc:"7日付费次数(激活时间)"`
	// 用户激活应用后的7天内，在应用内完成付费的平均成本，计算方式：消耗/7日付费次数(激活时间)。
	AttributionGamePay7dCost string `orm:"attribution_game_pay_7d_cost" json:"attributionGamePay7dCost" dc:"7日付费成本(激活时间)"`
	// 7天内用户的平均付费次数，计算方式：7日付费次数（激活时间）/7日首次付费数(激活时间)，对首次付费数的统计仅在计划内去重且披露在每个用户的激活时间上。
	AttributionActivePay7dPerCount string `orm:"attribution_active_pay_7d_per_count" json:"attributionActivePay7dPerCount" dc:"7日人均付费次数(激活时间)"`
	// 用户调起APP后到达的次数，一般在DPA广告中使用
	InAppUv string `orm:"in_app_uv" json:"inAppUv" dc:"APP内访问"`
	// 用户调起APP后到达指定详情页的次数，一般在DPA广告中使用
	InAppDetailUv string `orm:"in_app_detail_uv" json:"inAppDetailUv" dc:"APP内访问详情页"`
	// 用户调起APP后加入购物车的次数，一般在DPA广告中使用
	InAppCart string `orm:"in_app_cart" json:"inAppCart" dc:"APP内加入购物车"`
	// 用户调起APP后完成付费的次数，一般在DPA广告中使用
	InAppPay string `orm:"in_app_pay" json:"inAppPay" dc:"APP内付费"`
	// 用户调起APP后提交订单的次数，一般在DPA广告中使用
	InAppOrder string `orm:"in_app_order" json:"inAppOrder" dc:"APP内下单"`

	// ---------------游戏行业 ---------------------
	// 所选时间范围内的激活用户，激活当日在APP内的付费金额。
	AttributionGameInAppLtv1day string `orm:"attribution_game_in_app_ltv_1day" json:"attributionGameInAppLtv1day" dc:"当日付费金额"`
	// 所选时间范围内的激活用户，激活后一天内在APP内的付费金额总和。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppLtv2days string `orm:"attribution_game_in_app_ltv_2days" json:"attributionGameInAppLtv2days" dc:"激活后一日付费金额"`
	// 所选时间范围内的激活用户，激活后二天内在APP内的付费金额总和。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppLtv3days string `orm:"attribution_game_in_app_ltv_3days" json:"attributionGameInAppLtv3days" dc:"激活后二日付费金额"`
	// 所选时间范围内的激活用户，激活后三天内在APP内的付费金额总和。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppLtv4days string `orm:"attribution_game_in_app_ltv_4days" json:"attributionGameInAppLtv4days" dc:"激活后三日付费金额"`
	// 所选时间范围内的激活用户，激活后四天内在APP内的付费金额总和。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppLtv5days string `orm:"attribution_game_in_app_ltv_5days" json:"attributionGameInAppLtv5days" dc:"激活后四日付费金额"`
	// 所选时间范围内的激活用户，激活后五天内在APP内的付费金额总和。
	AttributionGameInAppLtv6days string `orm:"attribution_game_in_app_ltv_6days" json:"attributionGameInAppLtv6days" dc:"激活后五日付费金额"`
	// 所选时间范围内的激活用户，激活后六天内在APP内的付费金额总和。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppLtv7days string `orm:"attribution_game_in_app_ltv_7days" json:"attributionGameInAppLtv7days" dc:"激活后六日付费金额"`
	// 所选时间范围内的激活用户，激活后七天内在APP内的付费金额总和。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppLtv8days string `orm:"attribution_game_in_app_ltv_8days" json:"attributionGameInAppLtv8days" dc:"激活后七日付费金额"`
	// 所选时间范围内的激活用户在激活当日的付费ROI，计算公式是：当日付费金额/所选时间的消耗。
	AttributionGameInAppRoi1day string `orm:"attribution_game_in_app_roi_1day" json:"attributionGameInAppRoi1day" dc:"当日付费ROI"`
	// 所选时间范围内的激活用户在激活后一日内的所有付费ROI，计算公式是：激活后一日付费金额/所选时间的消耗。该指标隔日产出，并做了模糊化处理。
	AttributionGameInAppRoi2days string `orm:"attribution_game_in_app_roi_2days" json:"attributionGameInAppRoi2days" dc:"激活后一日付费ROI"`
	// 所选时间范围内的激活用户在激活后二日内的所有付费ROI，计算公式是：激活后二日付费金额/所选时间的消耗。
	AttributionGameInAppRoi3days string `orm:"attribution_game_in_app_roi_3days" json:"attributionGameInAppRoi3days" dc:"激活后二日付费ROI"`
	// 所选时间范围内的激活用户在激活后三日内的所有付费ROI，计算公式是：激活后三日付费金额/所选时间的消耗。
	AttributionGameInAppRoi4days string `orm:"attribution_game_in_app_roi_4days" json:"attributionGameInAppRoi4days" dc:"激活后三日付费ROI"`
	// 所选时间范围内的激活用户在激活后四日内的所有付费ROI，计算公式是：激活后四日付费金额/所选时间的消耗。
	AttributionGameInAppRoi5days string `orm:"attribution_game_in_app_roi_5days" json:"attributionGameInAppRoi5days" dc:"激活后四日付费ROI"`
	// 所选时间范围内的激活用户在激活后五日内的所有付费ROI，计算公式是：激活后五日付费金额/所选时间的消耗。
	AttributionGameInAppRoi6days string `orm:"attribution_game_in_app_roi_6days" json:"attributionGameInAppRoi6days" dc:"激活后五日付费ROI"`
	// 所选时间范围内的激活用户在激活后六日内的所有付费ROI，计算公式是：激活后六日付费金额/所选时间的消耗。
	AttributionGameInAppRoi7days string `orm:"attribution_game_in_app_roi_7days" json:"attributionGameInAppRoi7days" dc:"激活后六日付费ROI"`
	// 所选时间范围内的激活用户在激活后七日内的整体付费ROI，计算公式是：激活后七日付费金额/所选时间的消耗。
	AttributionGameInAppRoi8days string `orm:"attribution_game_in_app_roi_8days" json:"attributionGameInAppRoi8days" dc:"激活后七日付费ROI"`
	// 广告计费当日激活且首次付费的次数
	AttributionDayActivePayCount string `orm:"attribution_day_active_pay_count" json:"attributionDayActivePayCount" dc:"计费当日激活且首次付费数"`
	// 消耗/计费当日激活且首次付费数
	//AttributionDayActivePayCost string `orm:"attribution_day_active_pay_cost" json:"attributionDayActivePayCost" dc:"计费当日激活且首次付费成本"`
	// 计费当日激活且首次付费数/激活数
	//AttributionDayActivePayRate string `orm:"attribution_day_active_pay_rate" json:"attributionDayActivePayRate" dc:"计费当日激活且首次付费率"`
	// 当日发生激活且首次付费的次数
	//ActivePayIntraDayCount string `orm:"active_pay_intra_day_count" json:"activePayIntraDayCount" dc:"激活当日首次付费数"`
	// 消耗/激活当日首次付费数
	//ActivePayIntraDayCost string `orm:"active_pay_intra_day_cost" json:"activePayIntraDayCost" dc:"激活当日首次付费成本"`
	// 激活当日首次付费数/激活数
	//ActivePayIntraDayRate string `orm:"active_pay_intra_day_rate" json:"activePayIntraDayRate" dc:"激活当日首次付费率"`
	// 激活事件发生后24小时首次付费数，归因到激活时间
	AttributionActivePayIntraOneDayCount string `orm:"attribution_active_pay_intra_one_day_count" json:"attributionActivePayIntraOneDayCount" dc:"激活后24h首次付费数"`
	// 消耗/激活后24小时首次付费数
	AttributionActivePayIntraOneDayCost string `orm:"attribution_active_pay_intra_one_day_cost" json:"attributionActivePayIntraOneDayCost" dc:"激活后24h首次付费成本"`
	// 激活后24小时首次付费数/激活数
	AttributionActivePayIntraOneDayRate string `orm:"attribution_active_pay_intra_one_day_rate" json:"attributionActivePayIntraOneDayRate" dc:"激活后24h首次付费率"`
	// 激活24小时内app付费金额
	AttributionActivePayIntraOneDayAmount string `orm:"attribution_active_pay_intra_one_day_amount" json:"attributionActivePayIntraOneDayAmount" dc:"激活后24h付费金额"`
	// 激活24小时内的付费金额/消耗
	AttributionActivePayIntraOneDayRoi string `orm:"attribution_active_pay_intra_one_day_roi" json:"attributionActivePayIntraOneDayRoi" dc:"激活后24h付费ROI"`
	// 所选时间范围内的激活用户，付费24小时内在APP内的付费金额总和。该指标隔日产出。
	//FirstPayIntra24hourAmount string `orm:"first_pay_intra_24hour_amount" json:"firstPayIntra24hourAmount" dc:"首次付费后24h内付费金额"`
	// 所选时间范围内的激活用户，在付费24小时内的所有付费ROI。计算公式是：付费24小时内的付费金额/所选时间的消耗。该指标隔日产出。
	//FirstPayIntra24hourRoi string `orm:"first_pay_intra_24hour_roi" json:"firstPayIntra24hourRoi" dc:"首次付费后24h付费ROI"`
	// ------------------------------------ APP下载数据 --------------------------
	// 所选时间范围内激活用户在激活后第2天留存的数量
	AttributionRetention2dCnt string `orm:"attribution_retention_2d_cnt" json:"attributionRetention2dCnt" dc:"2日留存数"`
	// 所选时间范围内激活后第2天仍留存用户的获取成本
	AttributionRetention2dCost string `orm:"attribution_retention_2d_cost" json:"attributionRetention2dCost" dc:"2日留存成本"`
	// 当日激活用户在2日内继续登录，则计为一个2日留存。2日留存率=2日留存数/激活数
	AttributionRetention2dRate string `orm:"attribution_retention_2d_rate" json:"attributionRetention2dRate" dc:"2日留存率"`
	// 所选时间范围内激活用户在激活后第3天留存的数量
	AttributionRetention3dCnt string `orm:"attribution_retention_3d_cnt" json:"attributionRetention3dCnt" dc:"3日留存数"`
	// 所选时间范围内激活后第3天仍留存用户的获取成本
	AttributionRetention3dCost string `orm:"attribution_retention_3d_cost" json:"attributionRetention3dCost" dc:"3日留存成本"`
	// 当日激活用户在3日内继续登录，则计为一个3日留存。3日留存率=3日留存数/激活数
	AttributionRetention3dRate string `orm:"attribution_retention_3d_rate" json:"attributionRetention3dRate" dc:"3日留存率"`
	// 所选时间范围内激活用户在激活后第4天留存的数量
	AttributionRetention4dCnt string `orm:"attribution_retention_4d_cnt" json:"attributionRetention4dCnt" dc:"4日留存数"`
	// 所选时间范围内激活后第4天仍留存用户的获取成本
	AttributionRetention4dCost string `orm:"attribution_retention_4d_cost" json:"attributionRetention4dCost" dc:"4日留存成本"`
	// 当日激活用户在4日内继续登录，则计为一个4日留存。4日留存率=4日留存数/激活数
	AttributionRetention4dRate string `orm:"attribution_retention_4d_rate" json:"attributionRetention4dRate" dc:"4日留存率"`
	// 所选时间范围内激活用户在激活后第5天留存的数量
	AttributionRetention5dCnt string `orm:"attribution_retention_5d_cnt" json:"attributionRetention5dCnt" dc:"5日留存数"`
	// 所选时间范围内激活后第5天仍留存用户的获取成本
	AttributionRetention5dCost string `orm:"attribution_retention_5d_cost" json:"attributionRetention5dCost" dc:"5日留存成本"`
	// 当日激活用户在5日内继续登录，则计为一个5日留存。5日留存率=5日留存数/激活数
	AttributionRetention5dRate string `orm:"attribution_retention_5d_rate" json:"attributionRetention5dRate" dc:"5日留存率"`
	// 所选时间范围内激活用户在激活后第6天留存的数量
	AttributionRetention6dCnt string `orm:"attribution_retention_6d_cnt" json:"attributionRetention6dCnt" dc:"6日留存数"`
	// 所选时间范围内激活后第6天仍留存用户的获取成本
	AttributionRetention6dCost string `orm:"attribution_retention_6d_cost" json:"attributionRetention6dCost" dc:"6日留存成本"`
	// 当日激活用户在6日内继续登录，则计为一个6日留存。6日留存率=6日留存数/激活数
	AttributionRetention6dRate string `orm:"attribution_retention_6d_rate" json:"attributionRetention6dRate" dc:"6日留存率"`
	// 当日激活用户在7日内继续登录，则计为一个7日留存。
	AttributionRetention7dCnt string `orm:"attribution_retention_7d_cnt" json:"attributionRetention7dCnt" dc:"7日留存数"`
	// 7日留存成本=消耗/7日留存数
	AttributionRetention7dCost string `orm:"attribution_retention_7d_cost" json:"attributionRetention7dCost" dc:"7日留存成本"`
	// 当日激活用户在7日内继续登录，则计为一个7日留存。7日留存率=7日留存数/激活数
	AttributionRetention7dRate string `orm:"attribution_retention_7d_rate" json:"attributionRetention7dRate" dc:"7日留存率"`
	// 所选时间范围内激活用户在激活后第1天-第7天留存的总量
	AttributionRetention7dSumCnt string `orm:"attribution_retention_7d_sum_cnt" json:"attributionRetention7dSumCnt" dc:"7日留存总数"`
	// 所选时间范围内激活后7日内留存用户的获取成本。 建议仅查看已完整回传7日内留存数据的指标，否则会导致数值偏高影响投放判断。 留存成本 = 消耗 / 7日内留存天数总量 =  消耗 / （次留量+2留量+3留量+4留量+5留量+6留量+7留量）。
	AttributionRetention7dTotalCost string `orm:"attribution_retention_7d_total_cost" json:"attributionRetention7dTotalCost" dc:"7日留存总成本"`
	// 计费时间后的7天内(含计费当日)，用户在应用内完成付费的总次数，并将付费次数披露在扣费(消耗产生)时间上。
	//AttributionBillingGamePay7dCount string `orm:"attribution_billing_game_pay_7d_count" json:"attributionBillingGamePay7dCount" dc:"7日付费次数(计费时间)"`
	// 计费时间后的7天内(含计费当日)，用户在应用内完成付费的平均成本。计算方式：消耗/7日付费次数(计费时间)。
	//AttributionBillingGamePay7dCost string `orm:"attribution_billing_game_pay_7d_cost" json:"attributionBillingGamePay7dCost" dc:"7日付费成本(计费时间)"`

	// ------------------------视频数据---------------------------
	// 播放时间大于0S的数量，在某些蜂窝网络环境下，需要您手动点击开始才会开始播放，因此有时播放数小于展示数。
	TotalPlay string `orm:"total_play" json:"totalPlay" dc:"播放量"`
	// 广告播放时间大于等于3秒的数量，如果视频总时长不足3秒，则记录播放完成的次数。
	//PlayDuration3s string `orm:"play_duration_3s" json:"playDuration3s" dc:"3秒播放数"`
	// 竞价广告播放时间大于等于10秒的数量，如果视频总时长不足10秒，则记录播放完成的次数。品牌广告在部分APP（头条、头条lite、抖音、西瓜、抖音火山版、皮皮虾）播放时间大于等于5秒的数量，在其他APP大于等于3秒的数量，如果视频总时长不足5秒/3秒时，则记录播放完成的次数。
	ValidPlay string `orm:"valid_play" json:"validPlay" dc:"有效播放数"`
	// 计算公式：总花费/有效播放数，当天数据可能会有波动，次日早8点后稳定。
	ValidPlayCost string `orm:"valid_play_cost" json:"validPlayCost" dc:"有效播放成本"`
	// 计算公式：有效播放数/展示数。
	ValidPlayRate string `orm:"valid_play_rate" json:"validPlayRate" dc:"有效播放率"`
	// 有效播放数/1000，其中有效播放数是指，竞价广告播放时间大于等于 10 秒的数量，如果视频总时长不足 10 秒，则记录播放完成的次数；品牌广告在部分 APP（头条、头条 lite、抖音、西瓜、抖音火山版、皮皮虾）播放时间大于等于 5s 的数量，在其他 APP 大于等于 3s 的数量，如果视频总时长不足 5s/3s 时，则记录播放完成的次数。
	//ValidPlayOfMille string `orm:"valid_play_of_mille" json:"validPlayOfMille" dc:"千次有效播放数"`
	// 总花费/千次有效播放数，当天数据可能会有波动，次日早 8 点后稳定。
	//ValidPlayCostOfMille string `orm:"valid_play_cost_of_mille" json:"validPlayCostOfMille" dc:"千次有效播放成本"`
	// 用户播放至视频长度25%及以上的次数，包括跳跃播放至此长度的播放次数
	Play25FeedBreak string `orm:"play_25_feed_break" json:"play25FeedBreak" dc:"25%进度播放数"`
	// 用户播放至视频长度50%及以上的次数，包括跳跃播放至此长度的播放次数
	Play50FeedBreak string `orm:"play_50_feed_break" json:"play50FeedBreak" dc:"50%进度播放数"`
	// 用户播放至视频长度75%及以上的次数，包括跳跃播放至此长度的播放次数
	Play75FeedBreak string `orm:"play_75_feed_break" json:"play75FeedBreak" dc:"75%进度播放数"`
	// 用户播放至视频长度99%及以上的次数，包括跳跃播放至此长度的播放次数
	Play99FeedBreak string `orm:"play_99_feed_break" json:"play99FeedBreak" dc:"99%进度播放数"`
	// 计算方法：视频播放总实际时长／播放总次数（不包含跳跃的时长）
	AveragePlayTimePerPlay string `orm:"average_play_time_per_play" json:"averagePlayTimePerPlay" dc:"平均单次播放时长"`
	// 计算公式：播放完成数/播放数。
	PlayOverRate string `orm:"play_over_rate" json:"playOverRate" dc:"完播率"`
	// 在wifi环境下视频的播放数/视频播放总数
	WifiPlayRate string `orm:"wifi_play_rate" json:"wifiPlayRate" dc:"WiFi播放占比"`
	// 对于视频卡片类广告，在视频播放到3秒时进行卡片展现的数量。
	CardShow string `orm:"card_show" json:"cardShow" dc:"3秒卡片展现数"`
	// 广告被用户点赞的数量
	DyLike string `orm:"dy_like" json:"dyLike" dc:"点赞数"`
	// 用户在评论区对广告创意输入评论并点击提交的次数，仅限有评论区的APP（西瓜目前没有评论区）
	DyComment string `orm:"dy_comment" json:"dyComment" dc:"评论量"`
	// 用户把广告分享到其他社交媒体的次数，成功完成一次分享行为记一次分享数
	DyShare string `orm:"dy_share" json:"dyShare" dc:"分享量"`
	// 用户点击对广告不感兴趣的反馈的行为数量。
	//AdDislikeCnt string `orm:"ad_dislike_cnt" json:"adDislikeCnt" dc:"不感兴趣数"`
	// 用户认为广告质量较差而点击举报的行为数量。
	//AdReportCnt string `orm:"ad_report_cnt" json:"adReportCnt" dc:"举报数"`
	// 抖音广告中，用户点击挑战赛查看的数量
	IesChallengeClick string `orm:"ies_challenge_click" json:"iesChallengeClick" dc:"挑战赛查看数"`
	// 抖音广告中，用户点击音乐查看的数量
	IesMusicClick string `orm:"ies_music_click" json:"iesMusicClick" dc:"音乐查看数"`
	// 抖音广告中，用户点击POI组件的次数，一般出现在信息流或评论页面。POI为Point of Interest缩写
	LocationClick string `orm:"location_click" json:"locationClick" dc:"POI点击数"`
	// -----------------------线索收集-----------------
	// 有效获客是指用户完成了有价值动作的次数，其中动作支持您自定义。例如：您可将表单提交后用户的进一步行为，自定义为有效获客。
	CustomerEffective string `orm:"customer_effective" json:"customerEffective" dc:"有效获客"`
	// 有效获客是指用户完成了有价值动作的次数，其中动作支持您自定义。例如：您可将表单提交后用户的进一步行为，自定义为有效获客。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionCustomerEffective string `orm:"attribution_customer_effective" json:"attributionCustomerEffective" dc:"有效获客(计费时间)"`
	// 消耗/有效获客(计费时间) 计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionCustomerEffectiveCost string `orm:"attribution_customer_effective_cost" json:"attributionCustomerEffectiveCost" dc:"有效获客成本(计费时间)"`
	// 存在意向是指用户支付了小额订单的次数。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionCluePaySucceed string `orm:"attribution_clue_pay_succeed" json:"attributionCluePaySucceed" dc:"支付-存在意向(计费时间)"`
	// 消耗/支付-存在意向  计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionCluePaySucceedCost string `orm:"attribution_clue_pay_succeed_cost" json:"attributionCluePaySucceedCost" dc:"存在意向成本(计费时间)"`
	// 加为好友是指在回访之后，添加用户好友的次数。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueInterflow string `orm:"attribution_clue_interflow" json:"attributionClueInterflow" dc:"回访-加为好友(计费时间)"`
	// 消耗/回访-加为好友(计费时间) 计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueInterflowCost string `orm:"attribution_clue_interflow_cost" json:"attributionClueInterflowCost" dc:"加为好友成本(计费时间)"`
	// 高潜成交是指在回访之后，被判定为有较高成交潜力的客户。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueHighIntention string `orm:"attribution_clue_high_intention" json:"attributionClueHighIntention" dc:"回访-高潜成交(计费时间)"`
	// 消耗/回访-高潜成交成本(计费时间) 计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueHighIntentionCost string `orm:"attribution_clue_high_intention_cost" json:"attributionClueHighIntentionCost" dc:"高潜成交成本(计费时间)"`
	// 信息确认是指在回访之后，用户进行了信息确认的次数。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueConfirm string `orm:"attribution_clue_confirm" json:"attributionClueConfirm" dc:"回访-信息确认(计费时间)"`
	// 消耗/回访-信息确认(计费时间) 计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueConfirmCost string `orm:"attribution_clue_confirm_cost" json:"attributionClueConfirmCost" dc:"信息确认成本(计费时间)"`
	// 用户在门店落地页多线沟通的在线咨询中留资咨询的次数
	//ConsultClue string `orm:"consult_clue" json:"consultClue" dc:"留咨咨询"`
	// 用户扫描二维码，成功添加商家的企业微信的用户人数。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionWorkWechatAddedCount string `orm:"attribution_work_wechat_added_count" json:"attributionWorkWechatAddedCount" dc:"企业微信添加好友数(计费时间)"`
	// 取消和广告客户员工企业微信的好友关系的用户人数。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionWorkWechatUnfriendCount string `orm:"attribution_work_wechat_unfriend_count" json:"attributionWorkWechatUnfriendCount" dc:"企业微信取消好友数(计费时间)"`
	// 表单提交是指用户在落地页多线沟通提交表单的次数。计费时间是指在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。例如：广告在10月1日发生消耗，用户提交表单时间为10月2日，则该表单提交会记为10月1日的表单提交数，而非10月2日。
	//AttributionForm string `orm:"attribution_form" json:"attributionForm" dc:"表单提交(计费时间)"`
	// 接通线索数是指拨打且接通的线索数。计费时间是指在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。例如：广告在10月1日发生消耗，实际客服给用户拨打并接通时间为10月2日，则该接通线索会记为10月1日的接通线索数，而非10月2日。
	//AttributionClueConnectedCount string `orm:"attribution_clue_connected_count" json:"attributionClueConnectedCount" dc:"接通线索数(计费时间)"`
	// 计算方式：接通线索数(计费时间)/表单提交数(计费时间)。计费时间是指在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueConnectedRate string `orm:"attribution_clue_connected_rate" json:"attributionClueConnectedRate" dc:"接通率(计费时间)"`
	// 计算方式：消耗/接通线索数(计费时间)。计费时间是指在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	//AttributionClueConnectedCost string `orm:"attribution_clue_connected_cost" json:"attributionClueConnectedCost" dc:"接通成本(计费时间)"`
	// 广告主产生拨打、并沉淀到巨量平台的线索数，该指标统计时间以线索产生时间为准。例如：广告在10月1日展现，用户在10月1日提交线索，实际客服给用户拨打时间为10月2日，则该拨打线索数会记为10月1日的拨打线索数，而非10月2日。该指标追溯周期为近7天，即10月1日产生的线索，10月7日前（含当日）拨打均会计入10月1日。  指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueDialedCount string `orm:"clue_dialed_count" json:"clueDialedCount" dc:"拨打线索数"`
	// 广告主拨打电话，接通线索与拨打线索的比率。计算方式：接通线索数/拨打线索数。 指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueConnectedRate string `orm:"clue_connected_rate" json:"clueConnectedRate" dc:"拨打接通率"`
	// 广告主拨打且接通，并沉淀到巨量平台的线索的平均成本，计算方式：拨打率*消耗/接通线索数，该指标统计时间以线索产生时间为准。 例如:广告在10月1日展现，用户在10月1日提交线索，实际客服给用户拨打且接通时间为10月2日，则该接通线索数会记为10月1日的接通线索数，而非10月2日。该指标追溯周期为近7天，即10月1日产生的线索，10月7日前(含当日)拨打并接通均会计入10月1日。指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueConnectedCost string `orm:"clue_connected_cost" json:"clueConnectedCost" dc:"拨打接通线索成本"`
	// 广告主接通后产生30秒沟通，并沉淀到巨量平台的线索数，该指标统计时间以线索产生时间为准。例如：广告在10月1日展现，用户在10月1日提交线索，实际客服给用户拨打并沟通30秒的时间为10月2日，则该30秒沟通线索数会记为10月1日的30秒沟通线索数，而非10月2日。该指标追溯周期为近7天，即10月1日产生的线索，10月7日前（含当日）沟通30秒均会计入10月1日。 指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueConnected30sCount string `orm:"clue_connected_30s_count" json:"clueConnected30sCount" dc:"30秒沟通线索数"`
	// 广告主客户拨打电话，30秒沟通线索与拨打线索的比率。计算方式：30秒沟通线索/拨打线索数。 指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueConnected30sRate string `orm:"clue_connected_30s_rate" json:"clueConnected30sRate" dc:"30秒沟通线索率"`
	// 广告主接通后产生30s沟通，并沉淀到巨量平台的线索的平均成本，计算方式：拨打率*消耗 / 30s接通线索数，该指标统计时间以线索产生时间为准。 例如：广告在10月1日展现，用户在10月1日提交线索，实际客服给用户拨打并沟通30秒的时间为10月2日，则该30秒沟通线索数会记为10月1日的30秒沟通线索数，而非10月2日。该指标追溯周期为近7天，即10月1日产生的线索，10月7日前（含当日）沟通30秒均会计入10月1日。 指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueConnected30sCost string `orm:"clue_connected_30s_cost" json:"clueConnected30sCost" dc:"30秒沟通线索成本"`
	// 客户拨打电话，平均到每条线索数的通话时长。计算方式：总通话时长/接通线索数。 指标更新时间：t+1天，即10月2日更新10月1日数据
	//ClueConnectedAverageDuration string `orm:"clue_connected_average_duration" json:"clueConnectedAverageDuration" dc:"平均通话时长(秒)"`
	// 用户在抖音号私信中留下线索（如号码、微信等）的次数
	//ClueMessageCount string `orm:"clue_message_count" json:"clueMessageCount" dc:"私信留资数"`
	// 用户通过长按复制微信号的次数
	Wechat string `orm:"wechat" json:"wechat" dc:"微信复制"`
	// 表单提交和附加创意表单提交之和
	//FormAndSubmitCount string `orm:"form_and_submit_count" json:"formAndSubmitCount" dc:"表单提交数(包含附加创意表单数)"`
	// 消耗/表单提交数(包含附加创意表单数)
	//FormAndSubmitCost string `orm:"form_and_submit_cost" json:"formAndSubmitCost" dc:"表单提交成本(包含附加创意表单数)"`
	// 意向表单数为用户完成意向表单填写且满足广告主意向定义的表单数
	//IntentionFormAndSubmitCount string `orm:"intention_form_and_submit_count" json:"intentionFormAndSubmitCount" dc:"意向表单数"`
	// 消耗/意向表单数
	//IntentionFormAndSubmitCost string `orm:"intention_form_and_submit_cost" json:"intentionFormAndSubmitCost" dc:"意向表单成本"`
	// -----------------小程序/小游戏----------------------

	// 所选时间范围内的激活用户在激活当日的变现金额
	AttributionMicroGame0dLtv string `orm:"attribution_micro_game_0d_ltv" json:"attributionMicroGame0dLtv" dc:"小程序/小游戏当日LTV"`
	// 所选时间范围内的激活用户在激活后三日内的变现金额
	AttributionMicroGame3dLtv string `orm:"attribution_micro_game_3d_ltv" json:"attributionMicroGame3dLtv" dc:"小程序/小游戏激活后三日LTV"`
	// 所选时间范围内的激活用户在激活后七日内的变现金额
	AttributionMicroGame7dLtv string `orm:"attribution_micro_game_7d_ltv" json:"attributionMicroGame7dLtv" dc:"小程序/小游戏激活后七日LTV"`
	// 所选时间范围内的激活用户在激活当日的广告变现ROI，计算公式是：当日LTV / 所选时间的消耗
	AttributionMicroGame0dRoi string `orm:"attribution_micro_game_0d_roi" json:"attributionMicroGame0dRoi" dc:"小程序/小游戏当日广告变现ROI"`
	// 所选时间范围内的激活用户在激活后三日内的广告变现ROI，计算公式是：三日LTV / 所选时间的消耗
	AttributionMicroGame3dRoi string `orm:"attribution_micro_game_3d_roi" json:"attributionMicroGame3dRoi" dc:"小程序/小游戏激活后三日广告变现ROI"`
	// 所选时间范围内的激活用户在激活后七日内的广告变现ROI，计算公式是：七日LTV / 所选时间的消耗
	AttributionMicroGame7dRoi string `orm:"attribution_micro_game_7d_roi" json:"attributionMicroGame7dRoi" dc:"小程序/小游戏激活后七日广告变现ROI"`
	// 所选时间范围内的激活用户在激活当日的变现金额 + 使用 “active_pay”事件回传付费金额时，平台所统计的内购金额总数
	//AttributionMicroGameIaapLtv1day string `orm:"attribution_micro_game_iaap_ltv_1day" json:"attributionMicroGameIaapLtv1day" dc:"小程序/小游戏当日内购&变现收入（激活时间）"`
	// 基于激活时间计算的，当日内购&变现金额/消耗
	//AttributionMicroGameIaapRoi1day string `orm:"attribution_micro_game_iaap_roi_1day" json:"attributionMicroGameIaapRoi1day" dc:"小程序/小游戏当日内购&变现ROI（激活时间）"`
	// 激活用户在24小时内产生的广告变现收入/消耗
	//AttributionMicroGame24hRoi string `orm:"attribution_micro_game_24h_roi" json:"attributionMicroGame24hRoi" dc:"激活后24小时变现ROI"`
	// 有效激活后30天内有产生广告变现或内购的激活数
	//AttributionEffectActive30dCount string `orm:"attribution_effect_active_30d_count" json:"attributionEffectActive30dCount" dc:"有效激活数"`
	// 广告消耗/有效激活数
	//AttributionEffectActive30dCost string `orm:"attribution_effect_active_30d_cost" json:"attributionEffectActive30dCost" dc:"有效激活成本"`
	// 激活后24小时内的付费金额+广告变现金额
	//AttributionMicroGameIaapLtv24h string `orm:"attribution_micro_game_iaap_ltv_24h" json:"attributionMicroGameIaapLtv24h" dc:"24小时综合流水（激活时间）"`
	//
	//StatAttributionMicroGame24hAmount string `orm:"stat_attribution_micro_game_24h_amount" json:"statAttributionMicroGame24hAmount" dc:"24小时变现金额（激活时间）"`
	// 24小时综合流水/广告消耗
	//AttributionMicroGameIaapLtv24hRoi string `orm:"attribution_micro_game_iaap_ltv_24h_roi" json:"attributionMicroGameIaapLtv24hRoi" dc:"24小时综合ROI（激活时间）"`
	// 激活后7天内的付费金额+广告变现金额
	//AttributionMicroGameIaapLtv7d string `orm:"attribution_micro_game_iaap_ltv_7d" json:"attributionMicroGameIaapLtv7d" dc:"7日综合流水（激活时间）"`
	// 7日综合流水/广告消耗
	//AttributionMicroGameIaapLtv7dRoi string `orm:"attribution_micro_game_iaap_ltv_7d_roi" json:"attributionMicroGameIaapLtv7dRoi" dc:"7日综合ROI（激活时间）"`
	//
	//StatAttributionMicroGame7dAmount string `orm:"stat_attribution_micro_game_7d_amount" json:"statAttributionMicroGame7dAmount" dc:"7日变现金额（激活时间）"`
}

type AutoGenerated struct {
	// ------------------电商行业 -------------
	// 当您使用“in_app_order”事件回传订单金额时，对应的GMV金额
	InAppOrderGmv string `orm:"in_app_order_gmv" json:"inAppOrderGmv" dc:"引流电商订单GMV"`
	// 引流电商订单GMV/消耗
	InAppOrderRoi string `orm:"in_app_order_roi" json:"inAppOrderRoi" dc:"引流电商订单ROI"`
	// 当您使用“in_app_pay”事件回传支付金额时，对应的GMV金额
	InAppPayGmv string `orm:"in_app_pay_gmv" json:"inAppPayGmv" dc:"引流电商支付GMV"`
	// 引流电商支付GMV/消耗
	InAppPayRoi string `orm:"in_app_pay_roi" json:"inAppPayRoi" dc:"引流电商支付ROI"`
	// 在转化行为发生（或回传）之后，将订单GMV回记到过去30天内的扣费（消耗产生）时间上。 例如：广告在8月20日展示给用户，用户点击广告后于8月23日产生1笔购买，则8月23日这笔购买将会展示在8月20日，8月23日没有订单GMV。
	AttributionInAppOrderGmv string `orm:"attribution_in_app_order_gmv" json:"attributionInAppOrderGmv" dc:"引流电商订单GMV（计费时间）"`
	// 在转化行为发生（或回传）之后，将订单ROI回记到过去30天内的扣费（消耗产生）时间上。 例如：广告在8月20日展示给用户，用户点击广告后于8月23日产生1笔购买，则8月23日这笔购买将会展示在8月20日，8月23日没有订单ROI。
	AttributionInAppOrderRoi string `orm:"attribution_in_app_order_roi" json:"attributionInAppOrderRoi" dc:"引流电商订单ROI（计费时间）"`
	// 当您使用 “active_pay”事件回传付费金额时，平台所统计的付费金额总数。数据记录在平台收到回传事件的时间。
	StatPayAmount string `orm:"stat_pay_amount" json:"statPayAmount" dc:"付费金额(回传时间)"`
	// 付费金额(回传时间)/消耗
	PayAmountRoi string `orm:"pay_amount_roi" json:"payAmountRoi" dc:"付费ROI(回传时间)"`
	// 用户点击POI页面的电话按钮的次数
	Phone string `orm:"phone" json:"phone" dc:"点击电话按钮"`
	// 用户在门店落地页多线沟通提交表单的次数
	Form string `orm:"form" json:"form" dc:"表单提交"`
	// 用户查看创意的附加创意后，提交表单的次数
	FormSubmit string `orm:"form_submit" json:"formSubmit" dc:"附加创意表单提交"`
	// 用户进行地图搜索的次数
	Map string `orm:"map" json:"map" dc:"地图搜索"`
	// 用户点击按钮button的次数
	Button string `orm:"button" json:"button" dc:"按钮button"`
	// 用户在关键页面的浏览次数
	View string `orm:"view" json:"view" dc:"关键页面浏览"`
	// 用户点击下载开始的次数
	DownloadStart string `orm:"download_start" json:"downloadStart" dc:"下载开始"`
	// 用户点击QQ咨询按钮的次数
	Qq string `orm:"qq" json:"qq" dc:"QQ咨询"`
	// 用户点击抽奖按钮的次数
	Lottery string `orm:"lottery" json:"lottery" dc:"抽奖"`
	// 用户点击投票按钮的次数
	Vote string `orm:"vote" json:"vote" dc:"投票"`
	// 用户点击短信咨询的次数
	Message string `orm:"message" json:"message" dc:"短信咨询"`
	// 用户跳转至其他页面的次数
	Redirect string `orm:"redirect" json:"redirect" dc:"页面跳转"`
	// 用户购买商品的次数
	Shopping string `orm:"shopping" json:"shopping" dc:"商品购买"`
	// 用户点击在线咨询按钮的次数
	Consult string `orm:"consult" json:"consult" dc:"在线咨询"`
	// 用户在门店落地页多线沟通的在线咨询中有效咨询的次数
	ConsultEffective string `orm:"consult_effective" json:"consultEffective" dc:"有效咨询"`
	// 用户点击确认拨打电话按钮的次数
	PhoneConfirm string `orm:"phone_confirm" json:"phoneConfirm" dc:"智能电话-确认拨打"`
	// 用户在门店落地页点击多线沟通的电话按钮并接通的次数
	PhoneConnect string `orm:"phone_connect" json:"phoneConnect" dc:"智能电话-确认接通"`
	// 用户在门店落地页点击多线沟通的电话按钮并有效接通的次数
	PhoneEffective string `orm:"phone_effective" json:"phoneEffective" dc:"智能电话-有效接通"`
	// 用户点击POI页面卡券领取按钮的次数
	Coupon string `orm:"coupon" json:"coupon" dc:"建站卡券领取"`
	// 用户点击卡券页领取按钮的次数
	CouponSinglePage string `orm:"coupon_single_page" json:"couponSinglePage" dc:"卡券页领取"`
	// 用户调起第三方店铺的次数
	RedirectToShop string `orm:"redirect_to_shop" json:"redirectToShop" dc:"调起店铺"`
	// 用户点击顶部地图图标区域展开详细地图/点击图片下方地址展开详细地图/点击图片下方地址展开详细地图的次数
	PoiAddressClick string `orm:"poi_address_click" json:"poiAddressClick" dc:"查看店铺地址"`
	// 用户点击POI页面的收藏按钮的次数
	PoiCollect string `orm:"poi_collect" json:"poiCollect" dc:"店铺收藏"`

	// 互联网金融-贷款行业中，用户成功提交贷款额度申请的行为
	LoanCompletion string `orm:"loan_completion" json:"loanCompletion" dc:"完件数"`
	// 计算方式：总花费／完件数。当天数据可能会有波动，次日早8点后稳定。
	LoanCompletionCost string `orm:"loan_completion_cost" json:"loanCompletionCost" dc:"完件成本"`
	// 计算方式：完件数／注册数
	LoanCompletionRate string `orm:"loan_completion_rate" json:"loanCompletionRate" dc:"完件率"`
	// 互联网金融-贷款行业中，用户提交一部分个人信息后，广告主初步审批通过，并引导用户进行更加详细的信息填写以完成最终授信
	PreLoanCredit string `orm:"pre_loan_credit" json:"preLoanCredit" dc:"预授信数"`
	// 计算方式：总花费／预授信数。当天数据可能会有波动，次日早8点后稳定。
	PreLoanCreditCost string `orm:"pre_loan_credit_cost" json:"preLoanCreditCost" dc:"预授信成本"`
	// 互联网金融-贷款行业中，用户提交贷款额度申请后，广告主审批通过，给予用户可贷款的额度
	LoanCredit string `orm:"loan_credit" json:"loanCredit" dc:"授信数"`
	// 计算方式：总花费／授信数。当天数据可能会有波动，次日早8点后稳定。
	LoanCreditCost string `orm:"loan_credit_cost" json:"loanCreditCost" dc:"授信成本"`
	// 计算方式：授信数／完件数
	LoanCreditRate string `orm:"loan_credit_rate" json:"loanCreditRate" dc:"授信率"`
	// 互联网金融-贷款行业中，用户的贷款申请通过审批并发放到用户
	Loan string `orm:"loan" json:"loan" dc:"放款数"`
	// 计算方式：总花费/放款数。当天数据可能会有波动，次日早8点后稳定
	LoanCost string `orm:"loan_cost" json:"loanCost" dc:"放款成本"`
	// 计算方式：放款数/授信数
	LoanRate string `orm:"loan_rate" json:"loanRate" dc:"放款率"`
	// 用户完成保险支付的总次数
	PremiumPaymentCount string `orm:"premium_payment_count" json:"premiumPaymentCount" dc:"保险支付数"`
	// 总花费/保险支付总次数
	PremiumPaymentCost string `orm:"premium_payment_cost" json:"premiumPaymentCost" dc:"保险支付成本"`
	// 所选时间范围内的保险支付ROI，计算公式：保费金额*经验系数/消耗。该指标暂时只支持深度转化目标是保险ROI出价的计划。
	InsuranceLtRoi string `orm:"insurance_lt_roi" json:"insuranceLtRoi" dc:"保险支付ROI"`
	// 留下银行卡信息的用户人数
	BankcardInformationCount string `orm:"bankcard_information_count" json:"bankcardInformationCount" dc:"银行卡信息填写完成数"`
	// 完成个人信息补充的用户人数
	PersonalInformationCount string `orm:"personal_information_count" json:"personalInformationCount" dc:"补充个人信息填写完成数"`
	// 完成活体认证的用户人数
	CertificationInformationCount string `orm:"certification_information_count" json:"certificationInformationCount" dc:"用户活体认证信息上传完成数"`
	// 成功开户的用户人数
	OpenAccountCount string `orm:"open_account_count" json:"openAccountCount" dc:"开户数"`
	// 财商课体验课出席第一次课的用户人数
	FirstClassCount string `orm:"first_class_count" json:"firstClassCount" dc:"到课数"`
	// 财商课体验课出席第二次课的用户人数
	SecondClassCount string `orm:"second_class_count" json:"secondClassCount" dc:"二次课到课数"`
	// 取消客户企业号和个人号关注的用户数（金融行业）
	UnfollowInWechatCount string `orm:"unfollow_in_wechat_count" json:"unfollowInWechatCount" dc:"微信取关数"`
	// 用户添加微信成功（金融行业）
	InWechatPayCount string `orm:"in_wechat_pay_count" json:"inWechatPayCount" dc:"微信内付费数"`
	// 所选时间范围内的微信用户首次消息数之和。计费时间是指：在行为发生（或回传）后，将对应的行为回记到过去30天内的扣费（消耗产生）时间上。
	AttributionWorkWechatDialogCount string `orm:"attribution_work_wechat_dialog_count" json:"attributionWorkWechatDialogCount" dc:"企业微信用户首次消息数(计费时间)"`
	// 低质量用户的授信
	LowLoanCreditCount string `orm:"low_loan_credit_count" json:"lowLoanCreditCount" dc:"次级授信数"`
	// 高质量用户的授信
	HighLoanCreditCount string `orm:"high_loan_credit_count" json:"highLoanCreditCount" dc:"高价值授信数"`
	// 在保险M2期内退保的用户数
	WithdrawM2Count string `orm:"withdraw_m2_count" json:"withdrawM2Count" dc:"M2内退保数"`
	// 购买低价课后完成正价课购买的用户人数
	AttributionConversionClassCount string `orm:"attribution_conversion_class_count" json:"attributionConversionClassCount" dc:"正价课购买数"`
	// 金融财富行业中，用户完成小额订单成交。
	SmallAmountPayCount string `orm:"small_amount_pay_count" json:"smallAmountPayCount" dc:"财富小单成交"`
	// 以计费时间开始的3天内（3X24小时），累计转化的授信个数
	AttributionLoanCredit3dCount string `orm:"attribution_loan_credit_3d_count" json:"attributionLoanCredit3dCount" dc:"三日授信个数（计费时间）"`
	// 广告曝光后3日内用户首笔授信金额之和。
	AttributionLoanCredit3dAmount string `orm:"attribution_loan_credit_3d_amount" json:"attributionLoanCredit3dAmount" dc:"三日授信金额（计费时间）"`
	// 实际ROI=3日内授信金额/累计消耗，为投放期内实际ROI数据。
	AttributionLoanCredit3dRoi string `orm:"attribution_loan_credit_3d_roi" json:"attributionLoanCredit3dRoi" dc:"三日授信ROI （计费时间）"`
	// 广告曝光后3日内用户首笔授信金额的平均值。
	AttributionLoanCredit3dAmountAvg string `orm:"attribution_loan_credit_3d_amount_avg" json:"attributionLoanCredit3dAmountAvg" dc:"三日授信件均金额（计费时间）"`
	// 以计费时间开始的7天内（7X24小时），累计转化的支用个数
	AttributionLoan7dCount string `orm:"attribution_loan_7d_count" json:"attributionLoan7dCount" dc:"七日支用个数（计费时间）"`
	// 广告曝光后7日内用户累计支用金额之和。
	AttributionLoan7dAmount string `orm:"attribution_loan_7d_amount" json:"attributionLoan7dAmount" dc:"七日支用金额 （计费时间）"`
	// 实际ROI=7日内支用金额/累计消耗，为投放期内实际ROI数据。
	AttributionLoan7dRoi string `orm:"attribution_loan_7d_roi" json:"attributionLoan7dRoi" dc:"七日支用ROI （计费时间）"`

	// 出行行业司机在应用内提交身份认证的次数
	SubmitCertificationCount string `orm:"submit_certification_count" json:"submitCertificationCount" dc:"提交身份认证数"`
	// 出行行业司机在应用内成功通过身份认证的次数
	ApprovalCount string `orm:"approval_count" json:"approvalCount" dc:"通过身份认证数"`
	// 出行行业司机在应用内首次成功完成订单的数量
	FirstOrderCount string `orm:"first_order_count" json:"firstOrderCount" dc:"司机首次完单数"`
	// 出行行业乘客在应用内首次成功下单的数量
	FirstRentalOrderCount string `orm:"first_rental_order_count" json:"firstRentalOrderCount" dc:"乘客首次下单数"`
	// 出行行业乘客在应用内首次成功支付订单的数量
	CommuteFirstPayCount string `orm:"commute_first_pay_count" json:"commuteFirstPayCount" dc:"乘客首次支付数"`

	// 点击广告头像进入主页进行访问的次数，目前抖音、头条号外、抖音火山版可进行主页访问
	DyHomeVisited string `orm:"dy_home_visited" json:"dyHomeVisited" dc:"主页访问量"`
	// 广告被受众新增关注的数量，目前抖音、头条号外、微头条、抖音火山版可新增关注
	DyFollow string `orm:"dy_follow" json:"dyFollow" dc:"粉丝量"`
	// 用户在广告主抖音号主页中发起私信的次数，按人次记数
	MessageAction string `orm:"message_action" json:"messageAction" dc:"私信数"`
	// 用户在广告主抖音号主页内访问落地页的次数
	ClickLandingPage string `orm:"click_landing_page" json:"clickLandingPage" dc:"推广页访问量"`
	// 用户在广告主抖音号主页中访问商品橱窗的次数
	ClickShopwindow string `orm:"click_shopwindow" json:"clickShopwindow" dc:"主页商品橱窗访问量"`
	// 用户在广告主抖音号主页内访问落地页的次数
	ClickWebsite string `orm:"click_website" json:"clickWebsite" dc:"主页内落地页访问量"`
	// 用户在广告主抖音号主页内点击电话拨打的次数
	ClickCallDy string `orm:"click_call_dy" json:"clickCallDy" dc:"主页内电话拨打点击量"`
	// 用户在广告主抖音号主页内点击下载的次数
	ClickDownload string `orm:"click_download" json:"clickDownload" dc:"主页下载链接点击量"`
	// 用户进入您直播间的次数
	LubanLiveEnterCnt string `orm:"luban_live_enter_cnt" json:"lubanLiveEnterCnt" dc:"直播间观看数"`
	// 用户在您的直播间单次观看时长超过1分钟的次数，目前仅支持鲁班直播、抖音号推广直播、品牌Feeds Live直播和品牌TopLive直播
	LiveWatchOneMinuteCount string `orm:"live_watch_one_minute_count" json:"liveWatchOneMinuteCount" dc:"直播间超过1分钟观看数"`
	// 您的直播间新增的关注数，目前仅支持鲁班直播、抖音号推广直播、品牌Feeds Live直播和品牌TopLive直播
	LubanLiveFollowCnt string `orm:"luban_live_follow_cnt" json:"lubanLiveFollowCnt" dc:"直播间关注数"`
	// 用户在您的直播间点击分享到其他社交媒体的次数，目前仅支持鲁班直播、抖音号推广直播、品牌Feeds Live直播和品牌TopLive直播
	LubanLiveShareCnt string `orm:"luban_live_share_cnt" json:"lubanLiveShareCnt" dc:"直播间分享数"`
	// 用户在您的直播间输入评论并点击发送消息的次数，目前仅支持鲁班直播、抖音号推广直播、品牌Feeds Live直播和品牌TopLive直播
	LubanLiveCommentCnt string `orm:"luban_live_comment_cnt" json:"lubanLiveCommentCnt" dc:"直播间评论数"`
	// 用户点击小风车+卡片组件的总次数
	LiveComponentClickCount string `orm:"live_component_click_count" json:"liveComponentClickCount" dc:"组件点击数"`
	// 总花费/组件点击数
	LiveComponentClickCost string `orm:"live_component_click_cost" json:"liveComponentClickCost" dc:"组件点击成本"`
	// 组件点击数/组件曝光数
	LiveComponentClickRate string `orm:"live_component_click_rate" json:"liveComponentClickRate" dc:"组件点击率"`
	// 用户在您的直播间成功打赏的次数，目前仅支持鲁班直播、抖音号推广直播、品牌Feeds Live直播和品牌TopLive直播
	LubanLiveGiftCnt string `orm:"luban_live_gift_cnt" json:"lubanLiveGiftCnt" dc:"直播间打赏次数"`
	// 用户在您的直播间打赏的总金额，单位：音浪。目前仅支持鲁班直播、抖音号推广直播、品牌Feeds Live直播和品牌TopLive直播
	LubanLiveGiftAmount string `orm:"luban_live_gift_amount" json:"lubanLiveGiftAmount" dc:"直播间礼物总金额"`
	// A3为巨量云图0-5A关系资产中的「问询」人群，为品牌人群资产中最有潜力发生后续转化的种草人群。本指标采用「实时触点归因」，将新增A3人群归因到最后一个触达的广告，按照广告粒度聚合统计。
	A3AskCount string `orm:"a3_ask_count" json:"a3AskCount" dc:"新增A3(问询)数"`
	// 计算方式：消耗 / 新增A3(问询)数，即获取单个A3用户所花费的成本
	A3AskCost string `orm:"a3_ask_cost" json:"a3AskCost" dc:"新增A3(问询)成本"`
	// 在选定周期内，该广告曝光用户点击素材底部搜索词、评论区小蓝词等产生的搜索次数
	SearchAfterReadPv string `orm:"search_after_read_pv" json:"searchAfterReadPv" dc:"看后搜次数"`
	// 看后搜次数/广告曝光次数*100%
	SearchAfterPvRate string `orm:"search_after_pv_rate" json:"searchAfterPvRate" dc:"看后搜率"`
}
