/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameCreateV30Request struct for ToolsWechatGameCreateV30Request
type ToolsWechatGameCreateV30Request struct {
	//
	AccountId   int64                               `json:"account_id"`
	AccountType ToolsWechatGameCreateV30AccountType `json:"account_type"`
	AgeLimit    *ToolsWechatGameCreateV30AgeLimit   `json:"age_limit,omitempty"`
	//
	AgeRemindUrl string                                    `json:"age_remind_url"`
	AnchorList   ToolsWechatGameCreateV30RequestAnchorList `json:"anchor_list"`
	//
	AntiAddictionUrl string `json:"anti_addiction_url"`
	//
	ArtStyle *string `json:"art_style,omitempty"`
	//
	CompanyId *int64 `json:"company_id,omitempty"`
	//
	DiscountRate *int64 `json:"discount_rate,omitempty"`
	//
	FeatureTags []string `json:"feature_tags,omitempty"`
	//
	HasDiscount *bool `json:"has_discount,omitempty"`
	//
	HasOnlineEarning    *bool                                        `json:"has_online_earning,omitempty"`
	MaxPaymentTierRange *ToolsWechatGameCreateV30MaxPaymentTierRange `json:"max_payment_tier_range,omitempty"`
	MidPaymentTierRange *ToolsWechatGameCreateV30MidPaymentTierRange `json:"mid_payment_tier_range,omitempty"`
	MinPaymentTierRange *ToolsWechatGameCreateV30MinPaymentTierRange `json:"min_payment_tier_range,omitempty"`
	//
	Name string `json:"name"`
	//
	NetworkEnvironment []string `json:"network_environment,omitempty"`
	//
	Path string `json:"path"`
	//
	RealNameUrl  string                                `json:"real_name_url"`
	RevenueModel *ToolsWechatGameCreateV30RevenueModel `json:"revenue_model,omitempty"`
	//
	ScheduleCards []*ToolsWechatGameCreateV30ScheduleCards `json:"schedule_cards,omitempty"`
	//
	ScreenRecordUrl string `json:"screen_record_url"`
	//
	TagInfo *string `json:"tag_info,omitempty"`
	//
	ThemeTag *string `json:"theme_tag,omitempty"`
	//
	UserName string `json:"user_name"`
}
