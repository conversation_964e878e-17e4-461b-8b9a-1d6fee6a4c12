// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: internal/app/ad/dao/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdAnalyzeDataDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdAnalyzeDataDao struct {
	*internal.FqAdAnalyzeDataDao
}

var (
	// FqAdAnalyzeData is globally public accessible object for table tools_gen_table operations.
	FqAdAnalyzeData = fqAdAnalyzeDataDao{
		internal.NewFqAdAnalyzeDataDao(),
	}
)

// Fill with you ideas below.
