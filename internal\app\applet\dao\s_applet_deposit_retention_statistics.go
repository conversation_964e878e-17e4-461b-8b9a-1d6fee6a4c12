// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-08-28 15:12:15
// 生成路径: internal/app/applet/dao/s_applet_deposit_retention_statistics.go
// 生成人：cyao
// desc:小程序维度用户/充值留存数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sAppletDepositRetentionStatisticsDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sAppletDepositRetentionStatisticsDao struct {
	*internal.SAppletDepositRetentionStatisticsDao
}

var (
	// SAppletDepositRetentionStatistics is globally public accessible object for table tools_gen_table operations.
	SAppletDepositRetentionStatistics = sAppletDepositRetentionStatisticsDao{
		internal.NewSAppletDepositRetentionStatisticsDao(),
	}
	SAppletDepositRetentionStatisticsAnalytic = sAppletDepositRetentionStatisticsDao{
		internal.NewSAppletDepositRetentionStatisticsAnalyticDao(),
	}
)

// Fill with you ideas below.
