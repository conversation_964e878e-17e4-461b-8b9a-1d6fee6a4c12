// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: internal/app/ad/controller/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdInfoController struct {
	systemController.BaseController
}

var KsAdInfo = new(ksAdInfoController)

// List 列表
func (c *ksAdInfoController) List(ctx context.Context, req *ad.KsAdInfoSearchReq) (res *ad.KsAdInfoSearchRes, err error) {
	res = new(ad.KsAdInfoSearchRes)
	res.KsAdInfoSearchRes, err = service.KsAdInfo().List(ctx, &req.KsAdInfoSearchReq)
	return
}

// Get 获取快手账号管理
func (c *ksAdInfoController) Get(ctx context.Context, req *ad.KsAdInfoGetReq) (res *ad.KsAdInfoGetRes, err error) {
	res = new(ad.KsAdInfoGetRes)
	res.KsAdInfoInfoRes, err = service.KsAdInfo().GetById(ctx, req.Id)
	return
}

// Add 添加快手账号管理
func (c *ksAdInfoController) Add(ctx context.Context, req *ad.KsAdInfoAddReq) (res *ad.KsAdInfoAddRes, err error) {
	err = service.KsAdInfo().Add(ctx, req.KsAdInfoAddReq)
	return
}

// Edit 修改快手账号管理
func (c *ksAdInfoController) Edit(ctx context.Context, req *ad.KsAdInfoEditReq) (res *ad.KsAdInfoEditRes, err error) {
	err = service.KsAdInfo().Edit(ctx, req.KsAdInfoEditReq)
	return
}

// Delete 删除快手账号管理
func (c *ksAdInfoController) Delete(ctx context.Context, req *ad.KsAdInfoDeleteReq) (res *ad.KsAdInfoDeleteRes, err error) {
	err = service.KsAdInfo().Delete(ctx, req.Ids)
	return
}
