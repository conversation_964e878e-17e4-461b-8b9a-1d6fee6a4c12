// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-07 11:44:22
// 生成路径: internal/app/ad/model/entity/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdAccountInfo is the golang structure for table ks_ad_account_info.
type KsAdAccountInfo struct {
	gmeta.Meta          `orm:"table:ks_ad_account_info, do:true"`
	AdvertiserId        interface{} `orm:"advertiser_id,primary" json:"advertiserId"`        // 快手账户ID
	Id                  interface{} `orm:"id,primary" json:"id"`                             // id
	PrimaryIndustryName interface{} `orm:"primary_industry_name" json:"primaryIndustryName"` // 一级行业名称
	AppId               interface{} `orm:"app_id" json:"appId"`                              // 授权的app_id
	IndustryId          interface{} `orm:"industry_id" json:"industryId"`                    // 二级行业ID
	AccountId           interface{} `orm:"account_id" json:"accountId"`                      // 账户id
	IndustryName        interface{} `orm:"industry_name" json:"industryName"`                // 二级行业名称
	AccountName         interface{} `orm:"account_name" json:"accountName"`                  // 快手账户名称
	DeliveryType        interface{} `orm:"delivery_type" json:"deliveryType"`                // 投放方式: 0:默认；1:优先效果
	PrimaryIndustryId   interface{} `orm:"primary_industry_id" json:"primaryIndustryId"`     // 一级行业ID
	EffectFirst         interface{} `orm:"effect_first" json:"effectFirst"`                  // 优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定
	CorporationName     interface{} `orm:"corporation_name" json:"corporationName"`          // 公司名称
	ProductName         interface{} `orm:"product_name" json:"productName"`                  // 账户产品名称
	DirectRebate        interface{} `orm:"direct_rebate" json:"directRebate"`                // 激励余额，单位：元
	ContractRebate      interface{} `orm:"contract_rebate" json:"contractRebate"`            // 框返余额，单位：元
	RechargeBalance     interface{} `orm:"recharge_balance" json:"rechargeBalance"`          // 充值余额，单位：元
	Balance             interface{} `orm:"balance" json:"balance"`                           // 账户总余额，单位：元
	CreatedAt           *gtime.Time `orm:"created_at" json:"createdAt"`                      // 创建时间
	DeletedAt           *gtime.Time `orm:"deleted_at" json:"deletedAt"`                      // 删除时间
}
