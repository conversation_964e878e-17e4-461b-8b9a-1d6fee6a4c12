// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-30 11:50:07
// 生成路径: api/v1/channel/s_channel_detail_statistics.go
// 生成人：gfast
// desc:渠道Roi相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelDetailStatisticsSearchReq 分页请求参数
type SChannelDetailStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"渠道Roi" method:"get" summary:"渠道Roi列表"`
	commonApi.Author
	model.SChannelDetailStatisticsSearchReq
}

// SChannelDetailStatisticsSearchRes 列表返回结果
type SChannelDetailStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelDetailStatisticsSearchRes
}

// SChannelDetailStatisticsExportReq 导出请求
type SChannelDetailStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"渠道Roi" method:"get" summary:"渠道Roi导出"`
	commonApi.Author
	model.SChannelDetailStatisticsSearchReq
}

// SChannelDetailStatisticsExportRes 导出响应
type SChannelDetailStatisticsExportRes struct {
	commonApi.EmptyRes
}

// SChannelDetailStatisticsAddReq 添加操作请求参数
type SChannelDetailStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"渠道Roi" method:"post" summary:"渠道Roi添加"`
	commonApi.Author
	*model.SChannelDetailStatisticsAddReq
}

// SChannelDetailStatisticsAddRes 添加操作返回结果
type SChannelDetailStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SChannelDetailStatisticsEditReq 修改操作请求参数
type SChannelDetailStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"渠道Roi" method:"put" summary:"渠道Roi修改"`
	commonApi.Author
	*model.SChannelDetailStatisticsEditReq
}

// SChannelDetailStatisticsEditRes 修改操作返回结果
type SChannelDetailStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SChannelDetailStatisticsGetReq 获取一条数据请求
type SChannelDetailStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"渠道Roi" method:"get" summary:"获取渠道Roi信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelDetailStatisticsGetRes 获取一条数据结果
type SChannelDetailStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelDetailStatisticsInfoRes
}

// SChannelDetailStatisticsDeleteReq 删除数据请求
type SChannelDetailStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道Roi" method:"post" summary:"删除渠道Roi"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelDetailStatisticsDeleteRes 删除数据返回
type SChannelDetailStatisticsDeleteRes struct {
	commonApi.EmptyRes
}

// SChannelDetailStatisticsReq 渠道ROI
type SChannelDetailStatisticsReq struct {
	g.Meta `path:"/detail/stat/all" tags:"分销统计" method:"post" summary:"渠道ROI"`
	commonApi.Author
	model.SChannelDetailStatisticsReq
}

// SChannelDetailStatisticsRes 渠道ROI
type SChannelDetailStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelDetailStatisticsRes
}
