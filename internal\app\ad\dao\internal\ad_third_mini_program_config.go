// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: internal/app/ad/dao/internal/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdThirdMiniProgramConfigD<PERSON> is the manager for logic model data accessing and custom defined data operations functions management.
type AdThirdMiniProgramConfigDao struct {
	table   string                          // Table is the underlying table name of the DAO.
	group   string                          // Group is the database configuration group name of current DAO.
	columns AdThirdMiniProgramConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdThirdMiniProgramConfigColumns defines and stores column names for table ad_third_mini_program_config.
type AdThirdMiniProgramConfigColumns struct {
	Id                string //
	AppId             string // 小程序ID
	AppName           string // 小程序名称
	OriginalId        string // 小程序原始ID
	AppType           string // 小程序类型：微信 抖音
	MonetizationModel string // 变现模式：IAP IAA
	Platform          string // 平台： 2: 番茄 3: 点众
	AccountId         string // 点众账户ID
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
}

var adThirdMiniProgramConfigColumns = AdThirdMiniProgramConfigColumns{
	Id:                "id",
	AppId:             "app_id",
	AppName:           "app_name",
	OriginalId:        "original_id",
	AppType:           "app_type",
	MonetizationModel: "monetization_model",
	Platform:          "platform",
	AccountId:         "account_id",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
}

// NewAdThirdMiniProgramConfigDao creates and returns a new DAO object for table data access.
func NewAdThirdMiniProgramConfigDao() *AdThirdMiniProgramConfigDao {
	return &AdThirdMiniProgramConfigDao{
		group:   "default",
		table:   "ad_third_mini_program_config",
		columns: adThirdMiniProgramConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdThirdMiniProgramConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdThirdMiniProgramConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdThirdMiniProgramConfigDao) Columns() AdThirdMiniProgramConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdThirdMiniProgramConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdThirdMiniProgramConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdThirdMiniProgramConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
