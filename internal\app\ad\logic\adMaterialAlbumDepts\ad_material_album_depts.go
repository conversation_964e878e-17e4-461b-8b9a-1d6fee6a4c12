// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-13 15:31:08
// 生成路径: internal/app/ad/logic/ad_material_album_depts.go
// 生成人：cyao
// desc:广告素材专辑和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdMaterialAlbumDepts(New())
}

func New() service.IAdMaterialAlbumDepts {
	return &sAdMaterialAlbumDepts{}
}

type sAdMaterialAlbumDepts struct{}

func (s *sAdMaterialAlbumDepts) List(ctx context.Context, req *model.AdMaterialAlbumDeptsSearchReq) (listRes *model.AdMaterialAlbumDeptsSearchRes, err error) {
	listRes = new(model.AdMaterialAlbumDeptsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdMaterialAlbumDepts.Ctx(ctx).WithAll()
		if req.AlbumId != "" {
			m = m.Where(dao.AdMaterialAlbumDepts.Columns().AlbumId+" = ?", req.AlbumId)
		}
		if req.DetpId != "" {
			m = m.Where(dao.AdMaterialAlbumDepts.Columns().DetpId+" = ?", req.DetpId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "album_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdMaterialAlbumDeptsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdMaterialAlbumDeptsListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdMaterialAlbumDeptsListRes{
				AlbumId: v.AlbumId,
				DetpId:  v.DetpId,
			}
		}
	})
	return
}

func (s *sAdMaterialAlbumDepts) GetByAlbumId(ctx context.Context, albumId int) (res *model.AdMaterialAlbumDeptsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialAlbumDepts.Ctx(ctx).WithAll().Where(dao.AdMaterialAlbumDepts.Columns().AlbumId, albumId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialAlbumDepts) Add(ctx context.Context, req *model.AdMaterialAlbumDeptsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbumDepts.Ctx(ctx).Insert(do.AdMaterialAlbumDepts{
			AlbumId: req.AlbumId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdMaterialAlbumDepts) Edit(ctx context.Context, req *model.AdMaterialAlbumDeptsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbumDepts.Ctx(ctx).WherePri(req.AlbumId).Update(do.AdMaterialAlbumDepts{})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdMaterialAlbumDepts) Delete(ctx context.Context, albumIds []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbumDepts.Ctx(ctx).Delete(dao.AdMaterialAlbumDepts.Columns().DetpId+" in (?)", albumIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdMaterialAlbumDepts) GetScopeAuthority(ctx context.Context, id int) []int {
	list := make([]*model.AdMaterialAlbumDeptsListRes, 0)
	dao.AdMaterialAlbumDepts.Ctx(ctx).Where(dao.AdMaterialAlbumDepts.Columns().AlbumId, id).Scan(&list)
	returnList := make([]int, 0)
	if len(list) > 0 {
		for _, item := range list {
			returnList = append(returnList, item.DetpId)
		}
	}
	return returnList
}
