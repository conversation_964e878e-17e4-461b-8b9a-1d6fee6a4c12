// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-07-23 14:36:19
// 生成路径: internal/app/applet/model/entity/s_coupon_code.go
// 生成人：lx
// desc:兑换码表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// SCouponCode is the golang structure for table s_coupon_code.
type SCouponCode struct {
	gmeta.Meta `orm:"table:s_coupon_code, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`          //
	Code       interface{} `orm:"code" json:"code"`              // 兑换码
	ExpireTime *gtime.Time `orm:"expire_time" json:"expireTime"` // 过期时间
	CreateTime *gtime.Time `orm:"create_time" json:"createTime"` // 创建时间
	UpdateTime *gtime.Time `orm:"update_time" json:"updateTime"` // 更新时间
	UseStatus  interface{} `orm:"use_status" json:"useStatus"`   // 使用状态，0未使用，1已使用
}
