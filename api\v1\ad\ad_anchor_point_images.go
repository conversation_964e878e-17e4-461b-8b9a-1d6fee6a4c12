// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: api/v1/ad/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdAnchorPointImagesSearchReq 分页请求参数
type AdAnchorPointImagesSearchReq struct {
	g.Meta `path:"/list" tags:"锚点图片表" method:"get" summary:"锚点图片表列表"`
	commonApi.Author
	model.AdAnchorPointImagesSearchReq
}

// AdAnchorPointImagesSearchRes 列表返回结果
type AdAnchorPointImagesSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAnchorPointImagesSearchRes
}

// AdAnchorPointImagesAddReq 添加操作请求参数
type AdAnchorPointImagesAddReq struct {
	g.Meta `path:"/add" tags:"锚点图片表" method:"post" summary:"锚点图片表添加"`
	commonApi.Author
	*model.AdAnchorPointImagesAddReq
}

// AdAnchorPointImagesAddRes 添加操作返回结果
type AdAnchorPointImagesAddRes struct {
	commonApi.EmptyRes
}

// AdAnchorPointImagesEditReq 修改操作请求参数
type AdAnchorPointImagesEditReq struct {
	g.Meta `path:"/edit" tags:"锚点图片表" method:"put" summary:"锚点图片表修改"`
	commonApi.Author
	*model.AdAnchorPointImagesEditReq
}

// AdAnchorPointImagesEditRes 修改操作返回结果
type AdAnchorPointImagesEditRes struct {
	commonApi.EmptyRes
}

// AdAnchorPointImagesGetReq 获取一条数据请求
type AdAnchorPointImagesGetReq struct {
	g.Meta `path:"/get" tags:"锚点图片表" method:"get" summary:"获取锚点图片表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAnchorPointImagesGetRes 获取一条数据结果
type AdAnchorPointImagesGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAnchorPointImagesInfoRes
}

// AdAnchorPointImagesDeleteReq 删除数据请求
type AdAnchorPointImagesDeleteReq struct {
	g.Meta `path:"/delete" tags:"锚点图片表" method:"delete" summary:"删除锚点图片表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAnchorPointImagesDeleteRes 删除数据返回
type AdAnchorPointImagesDeleteRes struct {
	commonApi.EmptyRes
}
