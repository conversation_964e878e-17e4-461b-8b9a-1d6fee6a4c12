// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-08-28 15:47:10
// 生成路径: internal/app/applet/logic/product_detail.go
// 生成人：cq
// desc:商品详情表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonEntity "github.com/tiger1103/gfast/v3/internal/app/common/model/entity"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterProductDetail(New())
}

func New() service.IProductDetail {
	return &sProductDetail{}
}

type sProductDetail struct{}

func (s *sProductDetail) List(ctx context.Context, req *model.ProductDetailSearchReq) (listRes *model.ProductDetailSearchRes, err error) {
	listRes = new(model.ProductDetailSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.ProductDetail.Ctx(ctx).WithAll().Where(dao.ProductDetail.Columns().Deleted+" = ?", 0)
		if req.AppIds != nil && len(req.AppIds) > 0 {
			var condition string
			for index, appId := range req.AppIds {
				if index == 0 {
					condition += fmt.Sprintf("FIND_IN_SET('%s', app_id) > 0", appId)
				} else {
					condition += " OR " + fmt.Sprintf("FIND_IN_SET('%s', app_id) > 0", appId)
				}
			}
			condition += " OR app_id = ''"
			m = m.Where(condition)
		}
		if req.Type != "" {
			m = m.Where(dao.ProductDetail.Columns().Type+" = ?", gconv.Int(req.Type))
		}
		if req.Status != "" {
			m = m.Where(dao.ProductDetail.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "sort desc, id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.ProductDetailListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.ProductDetailListRes, len(res))
		appIds := make([]string, 0)
		linq.From(res).SelectT(func(item *model.ProductDetailListRes) string {
			return item.AppId
		}).ToSlice(&appIds)
		appIdList := make([]string, 0)
		for _, v := range appIds {
			splits := strings.Split(v, ",")
			appIdList = append(appIdList, splits...)
		}
		miniInfos, err1 := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIdList)
		liberr.ErrIsNil(ctx, err1, "获取小程序名称失败")
		productTypeList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.ProductType)
		liberr.ErrIsNil(ctx, err1, "获取商品类型字典数据失败")
		productSpecList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.ProductSpecification)
		liberr.ErrIsNil(ctx, err1, "获取商品规格类型字典数据失败")
		giftTypeList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.GiftType)
		liberr.ErrIsNil(ctx, err1, "获取赠品类型字典数据失败")
		vipTypeList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.VipPayType)
		liberr.ErrIsNil(ctx, err1, "获取vip类型字典数据失败")
		for k, v := range res {
			productDetail := &model.ProductDetailListRes{
				Id:            v.Id,
				AppId:         v.AppId,
				ImgUrl:        v.ImgUrl,
				Title:         v.Title,
				Detail:        v.Detail,
				Price:         v.Price,
				OriginalPrice: v.OriginalPrice,
				Type:          v.Type,
				Specification: v.Specification,
				GiftType:      v.GiftType,
				GiftNums:      v.GiftNums,
				VipType:       v.VipType,
				Sort:          v.Sort,
				Status:        v.Status,
				Remark:        v.Remark,
				CreateTime:    v.CreateTime,
				UpdateTime:    v.UpdateTime,
				Deleted:       v.Deleted,
			}
			miniInfoList := make([]*sysModel.GetMiniInfoListRes, 0)
			linq.From(miniInfos).WhereT(func(item *sysModel.GetMiniInfoListRes) bool {
				return strings.Contains(v.AppId, item.AppId)
			}).ToSlice(&miniInfoList)
			appNameList := make([]string, 0)
			linq.From(miniInfoList).SelectT(func(item *sysModel.GetMiniInfoListRes) string {
				return item.AppName
			}).ToSlice(&appNameList)
			productDetail.AppName = strings.Join(appNameList, ",")
			productDetail.AppIds = strings.Split(v.AppId, ",")
			if productType, ok := linq.From(productTypeList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.Type)
			}).First().(*commonEntity.SysDictData); ok {
				productDetail.TypeName = productType.DictLabel
			}
			if productSpec, ok := linq.From(productSpecList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.Specification)
			}).First().(*commonEntity.SysDictData); ok {
				productDetail.SpecificationName = productSpec.DictLabel
			}
			if giftType, ok := linq.From(giftTypeList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.GiftType)
			}).First().(*commonEntity.SysDictData); ok {
				productDetail.GiftTypeName = giftType.DictLabel
			}
			if vipType, ok := linq.From(vipTypeList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.VipType)
			}).First().(*commonEntity.SysDictData); ok {
				productDetail.VipTypeName = vipType.DictLabel
			}
			listRes.List[k] = productDetail
		}
	})
	return
}

func (s *sProductDetail) GetById(ctx context.Context, id int) (res *model.ProductDetailInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.ProductDetail.Ctx(ctx).WithAll().Where(dao.ProductDetail.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sProductDetail) GetByIds(ctx context.Context, Ids []int) (res []*model.ProductDetailInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.ProductDetail.Ctx(ctx).WithAll().WhereIn(dao.ProductDetail.Columns().Id, Ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sProductDetail) Add(ctx context.Context, req *model.ProductDetailAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var appId string
		if req.AppIds != nil || len(req.AppIds) > 0 {
			appId = strings.Join(req.AppIds, ",")
		}
		if appId == "all" {
			appId = ""
		}
		_, err = dao.ProductDetail.Ctx(ctx).Insert(do.ProductDetail{
			AppId:         appId,
			ImgUrl:        req.ImgUrl,
			Title:         req.Title,
			Detail:        req.Detail,
			Price:         req.Price,
			OriginalPrice: req.OriginalPrice,
			Type:          req.Type,
			Specification: req.Specification,
			GiftType:      req.GiftType,
			GiftNums:      req.GiftNums,
			VipType:       req.VipType,
			Sort:          req.Sort,
			Status:        req.Status,
			Remark:        req.Remark,
			CreateTime:    gtime.Now(),
			UpdateTime:    gtime.Now(),
			Deleted:       0,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sProductDetail) Edit(ctx context.Context, req *model.ProductDetailEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var appId string
		if req.AppIds != nil || len(req.AppIds) > 0 {
			appId = strings.Join(req.AppIds, ",")
		}
		if appId == "all" {
			appId = ""
		}
		_, err = dao.ProductDetail.Ctx(ctx).WherePri(req.Id).Update(do.ProductDetail{
			AppId:         appId,
			ImgUrl:        req.ImgUrl,
			Title:         req.Title,
			Detail:        req.Detail,
			Price:         req.Price,
			OriginalPrice: req.OriginalPrice,
			Type:          req.Type,
			Specification: req.Specification,
			GiftType:      req.GiftType,
			GiftNums:      req.GiftNums,
			VipType:       req.VipType,
			Sort:          req.Sort,
			Status:        req.Status,
			Remark:        req.Remark,
			UpdateTime:    gtime.Now(),
			Deleted:       0,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sProductDetail) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, id := range ids {
			_, err = dao.ProductDetail.Ctx(ctx).WherePri(id).Update(do.ProductDetail{
				UpdateTime: gtime.Now(),
				Deleted:    1,
			})
			liberr.ErrIsNil(ctx, err, "删除失败")
		}
	})
	return
}
