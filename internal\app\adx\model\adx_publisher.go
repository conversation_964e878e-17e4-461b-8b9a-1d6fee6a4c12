// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:34:01
// 生成路径: internal/app/adx/model/adx_publisher.go
// 生成人：cq
// desc:ADX公司信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxPublisherInfoRes is the golang structure for table adx_publisher.
type AdxPublisherInfoRes struct {
	gmeta.Meta    `orm:"table:adx_publisher"`
	Id            uint64 `orm:"id,primary" json:"id" dc:"公司ID"`                // 公司ID
	PublisherName string `orm:"publisher_name" json:"publisherName" dc:"公司名称"` // 公司名称
}

type AdxPublisherListRes struct {
	Id            uint64 `json:"id" dc:"公司ID"`
	PublisherName string `json:"publisherName" dc:"公司名称"`
}

// AdxPublisherSearchReq 分页请求参数
type AdxPublisherSearchReq struct {
	comModel.PageReq
	Id            string `p:"id" dc:"公司ID"`            //公司ID
	PublisherName string `p:"publisherName" dc:"公司名称"` //公司名称
	StartTime     string `p:"startTime" dc:"开始时间"`
	EndTime       string `p:"endTime" dc:"结束时间"`
}

// AdxPublisherSearchRes 列表返回结果
type AdxPublisherSearchRes struct {
	comModel.ListRes
	List []*AdxPublisherListRes `json:"list"`
}

// AdxPublisherAddReq 添加操作请求参数
type AdxPublisherAddReq struct {
	Id            uint64 `p:"id" v:"required#主键ID不能为空" dc:"公司ID"`
	PublisherName string `p:"publisherName" v:"required#公司名称不能为空" dc:"公司名称"`
}

// AdxPublisherEditReq 修改操作请求参数
type AdxPublisherEditReq struct {
	Id            uint64 `p:"id" v:"required#主键ID不能为空" dc:"公司ID"`
	PublisherName string `p:"publisherName" v:"required#公司名称不能为空" dc:"公司名称"`
}
