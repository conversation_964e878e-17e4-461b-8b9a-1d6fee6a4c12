// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/model/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdAnchorPointImagesInfoRes is the golang structure for table ad_anchor_point_images.
type AdAnchorPointImagesInfoRes struct {
	gmeta.Meta     `orm:"table:ad_anchor_point_images"`
	Id             int    `orm:"id,primary" json:"id" dc:""`                                                                  //
	AnchorPointId  int    `orm:"anchor_point_id" json:"anchorPointId" dc:"锚点id"`                                              // 锚点id
	Uri            string `orm:"uri" json:"uri" dc:"url 地址"`                                                                  // url 地址
	Width          int    `orm:"width" json:"width" dc:"宽"`                                                                   // 宽
	Height         int    `orm:"height" json:"height" dc:"高"`                                                                 // 高
	Loading        int    `orm:"loading" json:"loading" dc:"0 ， 1 暂时不知道干嘛的"`                                                  // 0 ， 1 暂时不知道干嘛的
	ClMaterialId   int64  `orm:"cl_material_id" json:"clMaterialId" dc:"关联的素材id"`                                             // 关联的素材id
	ClThumbnailUri string `orm:"cl_thumbnail_uri" json:"clThumbnailUri" dc:"关联饿素材url"`                                        // 关联饿素材url
	ImageType      string `orm:"image_type" json:"imageType" dc:"图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'"` // 图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'
}

type AdAnchorPointImagesListRes struct {
	Id             int    `json:"id" dc:""`
	AnchorPointId  int    `json:"anchorPointId" dc:"锚点id"`
	Uri            string `json:"uri" dc:"url 地址"`
	Width          int    `json:"width" dc:"宽"`
	Height         int    `json:"height" dc:"高"`
	Loading        int    `json:"loading" dc:"0 ， 1 暂时不知道干嘛的"`
	ClMaterialId   int64  `json:"clMaterialId" dc:"关联的素材id"`
	ClThumbnailUri string `json:"clThumbnailUri" dc:"关联饿素材url"`
	ImageType      string `json:"imageType" dc:"图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'"`
}

// AdAnchorPointImagesSearchReq 分页请求参数
type AdAnchorPointImagesSearchReq struct {
	comModel.PageReq
	Id             string `p:"id" dc:""`                                                                  //
	AnchorPointId  string `p:"anchorPointId" v:"anchorPointId@integer#锚点id需为整数" dc:"锚点id"`                //锚点id
	Uri            string `p:"uri" dc:"url 地址"`                                                           //url 地址
	Width          string `p:"width" v:"width@integer#宽需为整数" dc:"宽"`                                      //宽
	Height         string `p:"height" v:"height@integer#高需为整数" dc:"高"`                                    //高
	Loading        string `p:"loading" v:"loading@integer#0 ， 1 暂时不知道干嘛的需为整数" dc:"0 ， 1 暂时不知道干嘛的"`        //0 ， 1 暂时不知道干嘛的
	ClMaterialId   string `p:"clMaterialId" v:"clMaterialId@integer#关联的素材id需为整数" dc:"关联的素材id"`            //关联的素材id
	ClThumbnailUri string `p:"clThumbnailUri" dc:"关联饿素材url"`                                              //关联饿素材url
	ImageType      string `p:"imageType" dc:"图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'"` //图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'
}

// AdAnchorPointImagesSearchRes 列表返回结果
type AdAnchorPointImagesSearchRes struct {
	comModel.ListRes
	List []*AdAnchorPointImagesListRes `json:"list"`
}

// AdAnchorPointImagesAddReq 添加操作请求参数
type AdAnchorPointImagesAddReq struct {
	AnchorPointId  int    `p:"anchorPointId" dc:"锚点id"`
	Uri            string `p:"uri"  dc:"url 地址"`
	Width          int    `p:"width" v:"required#宽不能为空" dc:"宽"`
	Height         int    `p:"height" v:"required#高不能为空" dc:"高"`
	Loading        int    `p:"loading"  dc:"0 ， 1 暂时不知道干嘛的"`
	ClMaterialId   int64  `p:"clMaterialId" v:"required#关联的素材id不能为空" dc:"关联的素材id"`
	ClThumbnailUri string `p:"clThumbnailUri" v:"required#关联饿素材url不能为空" dc:"关联饿素材url"`
	ImageType      string `p:"imageType" v:"required#图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'不能为空" dc:"图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'"`
}

// AdAnchorPointImagesEditReq 修改操作请求参数
type AdAnchorPointImagesEditReq struct {
	Id             int    `p:"id" json:"id"  dc:""`
	AnchorPointId  int    `p:"anchorPointId" json:"anchorPointId" v:"required#锚点id不能为空" dc:"锚点id"`
	Uri            string `p:"uri" json:"uri" dc:"url 地址"`
	Width          int    `p:"width" json:"width" v:"required#宽不能为空" dc:"宽"`
	Height         int    `p:"height" json:"height" v:"required#高不能为空" dc:"高"`
	Loading        int    `p:"loading" json:"loading"  dc:"0 ， 1 暂时不知道干嘛的"`
	ClMaterialId   int64  `p:"clMaterialId" json:"clMaterialId" v:"required#关联的素材id不能为空" dc:"关联的素材id"`
	ClThumbnailUri string `p:"clThumbnailUri" json:"clThumbnailUri" v:"required#关联饿素材url不能为空" dc:"关联饿素材url"`
	ImageType      string `p:"imageType" json:"imageType" v:"required#图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'不能为空" dc:"图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'"`
}

const (
	HeadImage = "head_image"
	IconImage = "icon_image"
	AppImage  = "app_image"
)
