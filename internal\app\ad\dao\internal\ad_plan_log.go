// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: internal/app/ad/dao/internal/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdPlanLogDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdPlanLogDao struct {
	table   string           // Table is the underlying table name of the DAO.
	group   string           // Group is the database configuration group name of current DAO.
	columns AdPlanLogColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdPlanLogColumns defines and stores column names for table ad_plan_log.
type AdPlanLogColumns struct {
	Id             string // ID
	PlanId         string // 计划ID
	RuleName       string // 计划规则名称
	MediaType      string // 媒体类型，1 巨量，2
	ObjectType     string // 对象类型，1 账户，2 项目，3 广告
	ScopeType      string // 范围，1 所有，2 指定范围
	ScopeEntityId  string // （和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空
	Conditions     string // 满足条件
	Content        string // 执行操作的内容
	ExecutionState string // 执行状态，1 成功，2 失败
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
}

var adPlanLogColumns = AdPlanLogColumns{
	Id:             "id",
	PlanId:         "plan_id",
	RuleName:       "rule_name",
	MediaType:      "media_type",
	ObjectType:     "object_type",
	ScopeType:      "scope_type",
	ScopeEntityId:  "scope_entity_id",
	Conditions:     "conditions",
	Content:        "content",
	ExecutionState: "execution_state",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
}

// NewAdPlanLogDao creates and returns a new DAO object for table data access.
func NewAdPlanLogDao() *AdPlanLogDao {
	return &AdPlanLogDao{
		group:   "default",
		table:   "ad_plan_log",
		columns: adPlanLogColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdPlanLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdPlanLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdPlanLogDao) Columns() AdPlanLogColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdPlanLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdPlanLogDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdPlanLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
