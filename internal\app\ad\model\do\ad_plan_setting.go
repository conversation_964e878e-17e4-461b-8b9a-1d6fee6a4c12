// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: internal/app/ad/model/entity/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdPlanSetting is the golang structure for table ad_plan_setting.
type AdPlanSetting struct {
	gmeta.Meta         `orm:"table:ad_plan_setting, do:true"`
	Id                 interface{} `orm:"id,primary" json:"id"`                           // ID
	RuleName           interface{} `orm:"rule_name" json:"ruleName"`                      // 计划规则名称
	UserId             interface{} `orm:"user_id" json:"userId"`                          // 用户id创建计划人id
	MediaType          interface{} `orm:"media_type" json:"mediaType"`                    // 媒体类型 1 巨量 2
	ObjectType         interface{} `orm:"object_type" json:"objectType"`                  // 对象类型 1 账户 2 项目 3 广告
	ScopeType          interface{} `orm:"scope_type" json:"scopeType"`                    // 范围 1.所有 2 指定范围
	ScopeObjectType    interface{} `orm:"scope_object_type" json:"scopeObjectType"`       // 指定范围类型，仅当 scope_type 为 2 时才不为空，1.账户 2 项目 3 广告
	ScopeEntityIds     interface{} `orm:"scope_entity_ids" json:"scopeEntityIds"`         // (和 object_type 匹配，不同对象对应不同的 ID) 指定范围类型，仅当 scope_type 为 2 时才不为空
	AdjustChannel      interface{} `orm:"adjust_channel" json:"adjustChannel"`            // 调整渠道开关，0 关，1 开
	OnlyMessage        interface{} `orm:"only_message" json:"onlyMessage"`                // 1 仅发送通知 2 为需要执行计划
	RunFrequency       interface{} `orm:"run_frequency" json:"runFrequency"`              // 运行频率，单位分钟
	EffectiveTimeType  interface{} `orm:"effective_time_type" json:"effectiveTimeType"`   // 生效时间类型，1 长期，2 时间范围
	EffectiveStartTime *gtime.Time `orm:"effective_start_time" json:"effectiveStartTime"` // 生效开始时间，仅当 effective_time_type 为 2 时有值
	EffectiveEndTime   *gtime.Time `orm:"effective_end_time" json:"effectiveEndTime"`     // 生效结束时间，仅当 effective_time_type 为 2 时有值
	ModeOfNotification interface{} `orm:"mode_of_notification" json:"modeOfNotification"` // 通知方式，1 站内信，2 短信，3 邮箱，以 | 分隔
	PhoneNo            interface{} `orm:"phone_no" json:"phoneNo"`                        // 手机号，多个以 | 分隔
	Email              interface{} `orm:"email" json:"email"`                             // 邮箱，以 | 分隔
	Status             interface{} `orm:"status" json:"status"`                           // 1启用 2 未启用
	CreatedAt          *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt          *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
}
