package service

import (
	"context"
	"github.com/smartwalle/alipay/v3"
	"github.com/tiger1103/gfast/v3/internal/app/aliPay/model"
	_ "github.com/tiger1103/gfast/v3/internal/app/system/logic/sPlatRules"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"log"
)

type (
	IAliPayProgram interface {
		//TradeRefund ali支付 退款
		TradeRefund(ctx context.Context, req alipay.TradeRefund) (refundResult *alipay.TradeRefundRsp, err error)
	}
)

func init() {
	RegisterAliPayProgram()
}

var (
	aliPayPrograms map[string]*alipay.Client
)

func AliPayProgram(appId string) *alipay.Client {
	if aliPayPrograms == nil {
		panic("implement not found for IAliPayProgram, forgot register?")
	}
	if aliPayPrograms[appId] == nil {
		RegisterAliPayProgram()
		if aliPayPrograms[appId] == nil {
			return nil
		} else {
			return aliPayPrograms[appId]
		}
	}
	return aliPayPrograms[appId]
}

func RegisterAliPayProgram() {
	mapClient := make(map[string]*alipay.Client)
	zfbConfigs, _ := sysService.SPlatRules().GetZFBConfigures(context.TODO())
	for key, value := range zfbConfigs {
		// PrivateKey 是使用工具生成的应用私钥
		client, err := alipay.New(value.AppID, value.PrivateKey, true)
		if err != nil {
			log.Fatalf("failed alipay.New: %v", err)
			panic(err)
		}
		//需要注意此处用到的公钥是支付宝公钥，不是我们用工具生成的应用公钥。
		client.LoadAliPayPublicKey(value.PublicKey)
		if len(value.EncodingAesKey) > 0 {
			client.SetEncryptKey(value.EncodingAesKey)
		}

		mapClient[key] = client
	}
	aliPayPrograms = mapClient
}

// AliPayFileUpload 支付宝文件上传接口  alipay.open.file.upload
func AliPayFileUpload(ctx context.Context, appId string, req model.AliUploadReq) (res *model.AliUploadRes, err error) {
	var p = alipay.NewPayload("alipay.open.file.upload")
	p.Encrypt = false // 文件上传不支持接口内容加密
	// 设置参数
	p.AddParam("biz_code", "content_creation")

	p.AddFileObject("content_creation", req.FileName, req.Reader)

	res = new(model.AliUploadRes)
	err = AliPayProgram(appId).Request(ctx, p, &res)
	if err != nil {

		return nil, err
	}
	return
}

// AliPayContentPublish 内容发布接口 alipay.social.base.contentlib.standardcontent.publish
func AliPayContentPublish(ctx context.Context, appId string, req model.AliContentPublishReq) (res *model.AliContentPublishRes, err error) {
	var p = alipay.NewPayload("alipay.social.base.contentlib.standardcontent.publish")
	p.Encrypt = false // 文件上传不支持接口内容加密
	// 设置参数
	//标题
	p.AddBizField("source_title", req.SourceTitle)
	//类型
	p.AddBizField("source_type", req.SourceType)
	//摘要
	p.AddBizField("source_summary", req.SourceSummary)
	//内容
	p.AddBizField("source_content", req.SourceContent)
	// 素材id
	p.AddBizField("source_media_infos", req.SourceMediaInfos)

	p.AddBizField("source_author", req.SourceAuthor)

	p.AddBizField("source_publish_date", req.SourcePublishDate)

	res = new(model.AliContentPublishRes)
	err = AliPayProgram(appId).Request(ctx, p, &res)
	if err != nil {

		return nil, err
	}
	return
}

// AliPayContentStatusQuery 内容状态查询
func AliPayContentStatusQuery(ctx context.Context, appId string, req model.AliContentStatusQueryReq) (res *model.AliContentStatusQueryRes, err error) {
	var p = alipay.NewPayload("alipay.social.base.contentlib.standardcontent.query")
	p.Encrypt = false // 文件上传不支持接口内容加密
	// 设置参数
	p.AddBizField("content_id", req.ContentId)
	//p.AddBizField("public_id", req.PublicID)

	res = new(model.AliContentStatusQueryRes)
	err = AliPayProgram(appId).Request(ctx, p, &res)
	if err != nil {
		return nil, err
	}
	return
}

// 内容删除 AliPayContentDelete https://opendocs.alipay.com/pre-open/02n4g5?pathHash=2180c9bb
func AliPayContentDelete(ctx context.Context, appId string, req model.AliContentDeleteReq) (res *model.AliContentDeleteRes, err error) {
	var p = alipay.NewPayload("alipay.social.base.contentlib.standardcontent.delete")
	p.Encrypt = false // 文件上传不支持接口内容加密
	// 设置参数
	p.AddBizField("content_id", req.ContentId)
	p.AddBizField("public_id", req.PublicID)

	res = new(model.AliContentDeleteRes)
	err = AliPayProgram(appId).Request(ctx, p, &res)
	if err != nil {
		return nil, err
	}
	return
}

// 内容批量查询接口 alipay.social.base.contentlib.standardcontent.batchquery https://opendocs.alipay.com/pre-open/02n4g6?pathHash=9983cdec
func AliPayContentBatchQuery(ctx context.Context, appId string, req model.AliContentBatchQueryReq) (res *model.AliContentBatchQueryRes, err error) {
	var p = alipay.NewPayload("alipay.social.base.contentlib.standardcontent.batchquery")
	p.Encrypt = false // 文件上传不支持接口内容加密
	// 设置参数
	p.AddBizField("public_id", req.PublicID)
	p.AddBizField("need_detail", req.NeedDetail)
	p.AddBizField("status", req.Status)
	p.AddBizField("page_num", req.PageNum)
	p.AddBizField("page_size", req.PageSize)

	res = new(model.AliContentBatchQueryRes)
	err = AliPayProgram(appId).Request(ctx, p, &res)
	if err != nil {
		return nil, err
	}
	return
}
