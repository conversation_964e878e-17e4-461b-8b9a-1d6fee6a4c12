// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: internal/app/adx/dao/internal/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdxMediaDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdxMediaDao struct {
	table   string          // Table is the underlying table name of the DAO.
	group   string          // Group is the database configuration group name of current DAO.
	columns AdxMediaColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdxMediaColumns defines and stores column names for table adx_media.
type AdxMediaColumns struct {
	Id        string // 媒体ID
	LogoUrl   string // 媒体logo
	MediaName string // 媒体名称
}

var adxMediaColumns = AdxMediaColumns{
	Id:        "id",
	LogoUrl:   "logo_url",
	MediaName: "media_name",
}

// NewAdxMediaDao creates and returns a new DAO object for table data access.
func NewAdxMediaDao() *AdxMediaDao {
	return &AdxMediaDao{
		group:   "default",
		table:   "adx_media",
		columns: adxMediaColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdxMediaDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdxMediaDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdxMediaDao) Columns() AdxMediaColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdxMediaDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdxMediaDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdxMediaDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
