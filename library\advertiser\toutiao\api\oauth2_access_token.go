/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
)

type Oauth2AccessTokenApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *model.Oauth2AccessTokenRequest
}

func (r *Oauth2AccessTokenApiService) SetCfg(cfg *conf.Configuration) *Oauth2AccessTokenApiService {
	r.cfg = cfg
	return r
}

func (r *Oauth2AccessTokenApiService) Oauth2AccessTokenRequest(oauth2AccessTokenRequest model.Oauth2AccessTokenRequest) *Oauth2AccessTokenApiService {
	r.Request = &oauth2AccessTokenRequest
	return r
}

func (r *Oauth2AccessTokenApiService) AccessToken(accessToken string) *Oauth2AccessTokenApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *Oauth2AccessTokenApiService) Do() (data *model.Oauth2AccessTokenResponse, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/oauth2/access_token/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(r.Request).
		SetResult(&model.Oauth2AccessTokenResponse{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(model.Oauth2AccessTokenResponse)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/oauth2/access_token/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
