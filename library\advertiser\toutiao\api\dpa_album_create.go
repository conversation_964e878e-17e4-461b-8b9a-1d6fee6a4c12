package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"net/http"
	"time"
)

// DpaAlbumCreateApiService DpaAlbumCreateApi service
type DpaAlbumCreateApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DpaAlbumCreateV2Request
}

type DpaAlbumCreateV2Request struct {
	AlbumTitle    string           `json:"album_title"`     // 短剧标题，请注意命名符合
	SeqNum        int              `json:"seq_num"`         // 总集数
	AppId         string           `json:"app_id"`          //app_id
	VideoMetaMico []*VideoMetaMico `json:"video_meta_mico"` // 短剧信息
}

type VideoMetaMico struct {
	Url   string `json:"url"`   // 视频url，需要带有文件扩展名， 示例：https://xxxx.com/xxxxx.mp4，目前视频只支持mp4 avi mov m3u8格式
	Title string `json:"title"` // 集名称
	Seq   int    `json:"seq"`   // 集序号
}

type DpaAlbumCreateV2Response struct {
	//
	Code *int64 `json:"code,omitempty"`
	//
	Data *DpaAlbumCreateResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

type DpaAlbumCreateResponseData struct {
	AlbumId string `json:"album_id,omitempty"`
}

func (r *DpaAlbumCreateApiService) SetCfg(cfg *conf.Configuration) *DpaAlbumCreateApiService {
	r.cfg = cfg
	return r
}

func (r *DpaAlbumCreateApiService) DpaAlbumCreateV2Request(DpaAlbumCreateV2Request DpaAlbumCreateV2Request) *DpaAlbumCreateApiService {
	r.Request = &DpaAlbumCreateV2Request
	return r
}

func (r *DpaAlbumCreateApiService) AccessToken(accessToken string) *DpaAlbumCreateApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DpaAlbumCreateApiService) Do() (data *DpaAlbumCreateV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/dpa/album/create/"

	configuration := conf.NewConfiguration()
	client := resty.New()
	client.SetTimeout(10 * time.Second) //设置全局超时时间
	client.SetTransport(&http.Transport{
		MaxIdleConnsPerHost:   10,               // 对于每个主机，保持最大空闲连接数为 10
		IdleConnTimeout:       30 * time.Second, // 空闲连接超时时间为 30 秒
		TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时时间为 10 秒
		ResponseHeaderTimeout: 20 * time.Second, // 等待响应头的超时时间为 20 秒
	})
	configuration.HTTPClient = client

	response, err := configuration.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("App-Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&DpaAlbumCreateV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(DpaAlbumCreateV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/dpa/album/create/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
