// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-27 17:38:56
// 生成路径: api/v1/channel/s_channel_hour_recharge_statistics.go
// 生成人：lx
// desc:渠道小时充值统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelHourRechargeStatisticsSearchReq 分页请求参数
type SChannelHourRechargeStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"渠道小时充值统计" method:"get" summary:"渠道小时充值统计列表"`
	commonApi.Author
	model.SChannelHourRechargeStatisticsSearchReq
}

// SChannelHourRechargeStatisticsSearchRes 列表返回结果
type SChannelHourRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelHourRechargeStatisticsSearchRes
}

// SChannelHourRechargeStatisticsAddReq 添加操作请求参数
type SChannelHourRechargeStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"渠道小时充值统计" method:"post" summary:"渠道小时充值统计添加"`
	commonApi.Author
	*model.SChannelHourRechargeStatisticsAddReq
}

// SChannelHourRechargeStatisticsAddRes 添加操作返回结果
type SChannelHourRechargeStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SChannelHourRechargeStatisticsEditReq 修改操作请求参数
type SChannelHourRechargeStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"渠道小时充值统计" method:"put" summary:"渠道小时充值统计修改"`
	commonApi.Author
	*model.SChannelHourRechargeStatisticsEditReq
}

// SChannelHourRechargeStatisticsEditRes 修改操作返回结果
type SChannelHourRechargeStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SChannelHourRechargeStatisticsGetReq 获取一条数据请求
type SChannelHourRechargeStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"渠道小时充值统计" method:"get" summary:"获取渠道小时充值统计信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelHourRechargeStatisticsGetRes 获取一条数据结果
type SChannelHourRechargeStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelHourRechargeStatisticsInfoRes
}

// SChannelHourRechargeStatisticsDeleteReq 删除数据请求
type SChannelHourRechargeStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道小时充值统计" method:"delete" summary:"删除渠道小时充值统计"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelHourRechargeStatisticsDeleteRes 删除数据返回
type SChannelHourRechargeStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
