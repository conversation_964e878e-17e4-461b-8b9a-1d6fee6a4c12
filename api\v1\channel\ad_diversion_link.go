// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-06-18 16:18:52
// 生成路径: api/v1/channel/ad_diversion_link.go
// 生成人：cq
// desc:广告导流链接配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// AdDiversionLinkSearchReq 分页请求参数
type AdDiversionLinkSearchReq struct {
	g.Meta `path:"/list" tags:"广告导流链接配置" method:"post" summary:"广告导流链接配置列表"`
	commonApi.Author
	model.AdDiversionLinkSearchReq
}

// AdDiversionLinkSearchRes 列表返回结果
type AdDiversionLinkSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdDiversionLinkSearchRes
}

// AdDiversionLinkAddReq 添加操作请求参数
type AdDiversionLinkAddReq struct {
	g.Meta `path:"/add" tags:"广告导流链接配置" method:"post" summary:"广告导流链接配置添加"`
	commonApi.Author
	*model.AdDiversionLinkAddReq
}

// AdDiversionLinkAddRes 添加操作返回结果
type AdDiversionLinkAddRes struct {
	commonApi.EmptyRes
}

// AdDiversionLinkEditReq 修改操作请求参数
type AdDiversionLinkEditReq struct {
	g.Meta `path:"/edit" tags:"广告导流链接配置" method:"put" summary:"广告导流链接配置修改"`
	commonApi.Author
	*model.AdDiversionLinkEditReq
}

// AdDiversionLinkEditRes 修改操作返回结果
type AdDiversionLinkEditRes struct {
	commonApi.EmptyRes
}

// AdDiversionLinkGetReq 获取一条数据请求
type AdDiversionLinkGetReq struct {
	g.Meta `path:"/get" tags:"广告导流链接配置" method:"get" summary:"获取广告导流链接配置信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdDiversionLinkGetRes 获取一条数据结果
type AdDiversionLinkGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdDiversionLinkInfoRes
}

// AdDiversionLinkRemarkReq 分页请求参数
type AdDiversionLinkRemarkReq struct {
	g.Meta `path:"/remark/list" tags:"广告导流链接配置" method:"post" summary:"广告导流链接备注列表"`
	commonApi.Author
	model.AdDiversionLinkRemarkReq
}

// AdDiversionLinkRemarkRes 列表返回结果
type AdDiversionLinkRemarkRes struct {
	g.Meta `mime:"application/json"`
	*model.AdDiversionLinkRemarkRes
}

// AdDiversionLinkGetByAccountsReq 分页请求参数
type AdDiversionLinkGetByAccountsReq struct {
	g.Meta `path:"/getByAccountList" tags:"广告导流链接配置" method:"post" summary:"主库查询广告导流链接配置列表"`
	commonApi.Author
	Accounts []string `p:"accounts" v:"required#渠道号列表不能为空"`
}

// AdDiversionLinkGetByAccountsRes 列表返回结果
type AdDiversionLinkGetByAccountsRes struct {
	g.Meta `mime:"application/json"`
	*model.AdDiversionLinkSearchRes
}
