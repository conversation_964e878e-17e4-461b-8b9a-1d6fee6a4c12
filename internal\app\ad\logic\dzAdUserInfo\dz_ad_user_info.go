// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/logic/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterDzAdUserInfo(New())
}

func New() service.IDzAdUserInfo {
	return &sDzAdUserInfo{}
}

type sDzAdUserInfo struct{}

func (s *sDzAdUserInfo) List(ctx context.Context, req *model.DzAdUserInfoSearchReq) (listRes *model.DzAdUserInfoSearchRes, err error) {
	listRes = new(model.DzAdUserInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdUserInfo.Ctx(ctx).WithAll()
		if req.UserId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().UserId+" = ?", req.UserId)
		}
		if req.Time != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().Time+" = ?", gconv.Int64(req.Time))
		}
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.AppId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().AppId+" = ?", req.AppId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OpenId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.AdId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().AdId+" = ?", req.AdId)
		}
		if req.BookId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().BookId+" = ?", req.BookId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.ClickId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().ClickId+" = ?", req.ClickId)
		}
		if req.UnionId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().UnionId+" = ?", req.UnionId)
		}
		if req.RegisterTime != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().RegisterTime+" = ?", gconv.Int64(req.RegisterTime))
		}
		if req.DyeTime != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().DyeTime+" = ?", gconv.Int64(req.DyeTime))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.DzAdUserInfo.Columns().CreatedAt+" >=? AND "+dao.DzAdUserInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "user_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.DzAdUserInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.DzAdUserInfoListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.DzAdUserInfoListRes{
				UserId:       v.UserId,
				Time:         v.Time,
				ChannelId:    v.ChannelId,
				AppId:        v.AppId,
				PromotionId:  v.PromotionId,
				OpenId:       v.OpenId,
				AdId:         v.AdId,
				BookId:       v.BookId,
				ProjectId:    v.ProjectId,
				ClickId:      v.ClickId,
				UnionId:      v.UnionId,
				RegisterTime: v.RegisterTime,
				DyeTime:      v.DyeTime,
				CreatedAt:    v.CreatedAt,
			}
		}
	})
	return
}

func (s *sDzAdUserInfo) GetExportData(ctx context.Context, req *model.DzAdUserInfoSearchReq) (listRes []*model.DzAdUserInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.DzAdUserInfo.Ctx(ctx).WithAll()
		if req.UserId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().UserId+" = ?", req.UserId)
		}
		if req.Time != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().Time+" = ?", gconv.Int64(req.Time))
		}
		if req.ChannelId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().ChannelId+" = ?", req.ChannelId)
		}
		if req.AppId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().AppId+" = ?", req.AppId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OpenId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.AdId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().AdId+" = ?", req.AdId)
		}
		if req.BookId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().BookId+" = ?", req.BookId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.ClickId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().ClickId+" = ?", req.ClickId)
		}
		if req.UnionId != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().UnionId+" = ?", req.UnionId)
		}
		if req.RegisterTime != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().RegisterTime+" = ?", gconv.Int64(req.RegisterTime))
		}
		if req.DyeTime != "" {
			m = m.Where(dao.DzAdUserInfo.Columns().DyeTime+" = ?", gconv.Int64(req.DyeTime))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.DzAdUserInfo.Columns().CreatedAt+" >=? AND "+dao.DzAdUserInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "user_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sDzAdUserInfo) GetByUserId(ctx context.Context, userId string) (res *model.DzAdUserInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.DzAdUserInfo.Ctx(ctx).WithAll().Where(dao.DzAdUserInfo.Columns().UserId, userId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sDzAdUserInfo) Add(ctx context.Context, req *model.DzAdUserInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdUserInfo.Ctx(ctx).Insert(do.DzAdUserInfo{
			UserId:       req.UserId,
			Time:         req.Time,
			ChannelId:    req.ChannelId,
			AppId:        req.AppId,
			PromotionId:  req.PromotionId,
			OpenId:       req.OpenId,
			AdId:         req.AdId,
			BookId:       req.BookId,
			ProjectId:    req.ProjectId,
			ClickId:      req.ClickId,
			UnionId:      req.UnionId,
			RegisterTime: req.RegisterTime,
			DyeTime:      req.DyeTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sDzAdUserInfo) Edit(ctx context.Context, req *model.DzAdUserInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdUserInfo.Ctx(ctx).WherePri(req.UserId).Update(do.DzAdUserInfo{
			Time:         req.Time,
			ChannelId:    req.ChannelId,
			AppId:        req.AppId,
			PromotionId:  req.PromotionId,
			OpenId:       req.OpenId,
			AdId:         req.AdId,
			BookId:       req.BookId,
			ProjectId:    req.ProjectId,
			ClickId:      req.ClickId,
			UnionId:      req.UnionId,
			RegisterTime: req.RegisterTime,
			DyeTime:      req.DyeTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sDzAdUserInfo) Delete(ctx context.Context, userIds []string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.DzAdUserInfo.Ctx(ctx).Delete(dao.DzAdUserInfo.Columns().UserId+" in (?)", userIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
