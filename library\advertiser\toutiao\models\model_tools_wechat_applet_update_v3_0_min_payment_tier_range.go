/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletUpdateV30MinPaymentTierRange
type ToolsWechatAppletUpdateV30MinPaymentTierRange string

// List of tools_wechat_applet_update_v3.0_min_payment_tier_range
const (
	SIX_TEN_ToolsWechatAppletUpdateV30MinPaymentTierRange      ToolsWechatAppletUpdateV30MinPaymentTierRange = "SIX_TEN"
	TEN_TWENTY_ToolsWechatAppletUpdateV30MinPaymentTierRange   ToolsWechatAppletUpdateV30MinPaymentTierRange = "TEN_TWENTY"
	TWENTY_FIFTY_ToolsWechatAppletUpdateV30MinPaymentTierRange ToolsWechatAppletUpdateV30MinPaymentTierRange = "TWENTY_FIFTY"
	ZERO_FIVE_ToolsWechatAppletUpdateV30MinPaymentTierRange    ToolsWechatAppletUpdateV30MinPaymentTierRange = "ZERO_FIVE"
)

// Ptr returns reference to tools_wechat_applet_update_v3.0_min_payment_tier_range value
func (v ToolsWechatAppletUpdateV30MinPaymentTierRange) Ptr() *ToolsWechatAppletUpdateV30MinPaymentTierRange {
	return &v
}
