// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-16 11:16:21
// 生成路径: internal/app/ad/model/entity/fq_ad_account_users.go
// 生成人：gfast
// desc:番茄账号权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdAccountUsers is the golang structure for table fq_ad_account_users.
type FqAdAccountUsers struct {
	gmeta.Meta    `orm:"table:fq_ad_account_users"`
	DistributorId int64 `orm:"distributor_id,primary" json:"distributorId"`  // 渠道ID
	SpecifyUserId int   `orm:"specify_user_id,primary" json:"specifyUserId"` // 用户ID
}
