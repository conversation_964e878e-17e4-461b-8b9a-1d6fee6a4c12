// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-10 14:40:38
// 生成路径: internal/app/ad/logic/ks_series_report_core_data.go
// 生成人：cyao
// desc:短剧核心总览数据报表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsSeriesReportCoreData(New())
}

func New() service.IKsSeriesReportCoreData {
	return &sKsSeriesReportCoreData{}
}

type sKsSeriesReportCoreData struct{}

func (s *sKsSeriesReportCoreData) GetKsReportCoreData(ctx context.Context, req api.QueryCoreDataReq) (res *api.SeriesCorePageDataSnake, err error) {
	res = new(api.SeriesCorePageDataSnake)
	err = g.Try(ctx, func(ctx context.Context) {
		// 根据aid 获取token
		token := api.GetAccessTokenByAppIdCache(req.AdvertiserID)
		// 首次请求第一页数据
		resp, innerError := api.GetKSApiClient().QueryCoreDataService.AccessToken(token).SetReq(req).Do()
		liberr.ErrIsNil(ctx, innerError, fmt.Sprintf("获取统计数据失败: %v", innerError))
		if resp.Code != 0 {
			err = fmt.Errorf("获取统计数据失败: %s", resp.Message)
			return
		}
		res = resp.Data
	})
	return
}

func (s *sKsSeriesReportCoreData) GetKsReportCoreDataStats(ctx context.Context, aId, appId int64, stateTime string) (res int, err error) {
	res = 0
	err = g.Try(ctx, func(ctx context.Context) {
		// 定义分页参数
		pageNo := 1
		pageSize := 100
		startTime, endTime := int64(0), int64(0)
		startTime, endTime, err = libUtils.GetDayTimestamps(stateTime)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("传入的时间转换失败: %v", err))
		// 根据aid 获取token
		token := api.GetAccessTokenByAppIdCache(aId)
		// 首次请求第一页数据
		resp, innerError := api.GetKSApiClient().QueryCoreDataService.AccessToken(token).SetReq(api.QueryCoreDataReq{
			AdvertiserID: aId,
			StartTime:    startTime,
			EndTime:      endTime,
			PageNo:       pageNo,
			PageSize:     pageSize,
			//SeriesIDs:    []int{}, todo 以后需要再说
		}).Do()
		liberr.ErrIsNil(ctx, innerError, fmt.Sprintf("获取统计数据失败: %v", innerError))
		if resp.Code != 0 {
			err = fmt.Errorf("获取统计数据失败: %s", resp.Message)
			return
		}

		// 保存第一页数据
		if len(resp.Data.DataList) > 0 {
			addList := make([]*do.KsSeriesReportCoreData, 0)
			for _, item := range resp.Data.DataList {
				addList = append(addList, &do.KsSeriesReportCoreData{
					AdvertiserId:                     aId,
					SeriesId:                         nil,
					TotalCharge:                      item.TotalCharge,
					AccuFansUserNum:                  item.AccuFansUserNum,
					FansUserNum:                      item.FansUserNum,
					EventPayRoi:                      item.EventPayRoi,
					EventPayRoiAll:                   item.EventPayRoiAll,
					PayUserCount:                     item.PayUserCount,
					PayCount:                         item.PayCount,
					PayAmt:                           item.PayAmt,
					IsFansUser:                       item.IsFansUser,
					DisplayPlayCnt:                   item.DisplayPlayCnt,
					DisplayLikeCnt:                   item.DisplayLikeCnt,
					DisplayCommentCnt:                item.DisplayCommentCnt,
					DisplayCollectCnt:                item.DisplayCollectCnt,
					MiniGameIaaRoi:                   item.MiniGameIaaRoi,
					MiniGameIaaPurchaseAmount:        item.MiniGameIaaPurchaseAmount,
					MiniGameIaaPurchaseAmountDivided: item.MiniGameIaaPurchaseAmountDivided,
					MiniGameIaaDividedRoi:            item.MiniGameIaaDividedRoi,
					Date:                             gtime.NewFromStrFormat(item.Date, "2006-01-02 15:04"),
				})
			}
			_, err = dao.KsSeriesReportCoreData.Ctx(ctx).Save(addList)
			res += len(addList)
			if err != nil {
				return
			}
		}

		// 判断是否存在分页数据：如果总记录数大于当前返回的数量，则需要进行分页查询
		totalCount := resp.Data.TotalCount
		// 如果总记录数大于第一页返回的数量，则需要处理剩余的分页数据
		if totalCount > len(resp.Data.DataList) {
			// 计算总页数（注意最后一页可能不足 pageSize 条数据）
			totalPages := (totalCount + pageSize - 1) / pageSize
			// 从第二页开始循环获取剩余数据
			for page := 2; page <= totalPages; page++ {
				moreResp, moreErr := api.GetKSApiClient().QueryCoreDataService.SetReq(api.QueryCoreDataReq{
					AdvertiserID: aId,
					StartTime:    startTime,
					EndTime:      endTime,
					PageNo:       page,
					PageSize:     pageSize,
					//SeriesIDs:    []int{}, todo 以后需要再说
				}).Do()
				liberr.ErrIsNil(ctx, moreErr, fmt.Sprintf("获取统计数据失败, 页码 %d: %v", page, moreErr))
				if moreResp.Code != 0 {
					err = fmt.Errorf("获取统计数据失败, 页码 %d: %s", page, moreResp.Message)
					return
				}
				// 保存当前页数据
				if len(moreResp.Data.DataList) > 0 {
					addList := make([]*do.KsSeriesReportCoreData, 0)
					for _, item := range resp.Data.DataList {
						addList = append(addList, &do.KsSeriesReportCoreData{
							AdvertiserId:                     aId,
							SeriesId:                         nil,
							TotalCharge:                      item.TotalCharge,
							AccuFansUserNum:                  item.AccuFansUserNum,
							FansUserNum:                      item.FansUserNum,
							EventPayRoi:                      item.EventPayRoi,
							EventPayRoiAll:                   item.EventPayRoiAll,
							PayUserCount:                     item.PayUserCount,
							PayCount:                         item.PayCount,
							PayAmt:                           item.PayAmt,
							IsFansUser:                       item.IsFansUser,
							DisplayPlayCnt:                   item.DisplayPlayCnt,
							DisplayLikeCnt:                   item.DisplayLikeCnt,
							DisplayCommentCnt:                item.DisplayCommentCnt,
							DisplayCollectCnt:                item.DisplayCollectCnt,
							MiniGameIaaRoi:                   item.MiniGameIaaRoi,
							MiniGameIaaPurchaseAmount:        item.MiniGameIaaPurchaseAmount,
							MiniGameIaaPurchaseAmountDivided: item.MiniGameIaaPurchaseAmountDivided,
							MiniGameIaaDividedRoi:            item.MiniGameIaaDividedRoi,
							Date:                             gtime.NewFromStrFormat(item.Date, time.DateTime),
						})
					}
					_, err = dao.KsSeriesReportCoreData.Ctx(ctx).Save(addList)
					res = len(addList)
					if err != nil {
						return
					}
				}
			}
		}
	})
	return
}

// DayAdDataTask 短剧核心广告报表 每日统计任务
func (s *sKsSeriesReportCoreData) DayAdDataTask(ctx context.Context, statDate string) (err error) {
	if len(statDate) == 0 {
		statDate = libUtils.GetYesterdayDate()
	}
	channelRechargeStatKey := model.KSAdCoreDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :ChannelStatTask ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = g.Try(ctx, func(ctx context.Context) {
		list, _ := service.KsAdInfo().List(ctx, &model.KsAdInfoSearchReq{
			Status: "authorized",
		})
		if len(list.List) > 0 {
			for _, item := range list.List {
				_, err = s.GetKsReportCoreDataStats(innerCtx, item.AdvertiserId, int64(item.AppId), statDate)
				if err != nil {
					g.Log().Error(innerCtx, err)
				}
			}
		}
	})
	return
}

func (s *sKsSeriesReportCoreData) List(ctx context.Context, req *model.KsSeriesReportCoreDataSearchReq) (listRes *model.KsSeriesReportCoreDataSearchRes, err error) {
	listRes = new(model.KsSeriesReportCoreDataSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsSeriesReportCoreData.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if req.SeriesId != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().SeriesId+" = ?", gconv.Int64(req.SeriesId))
		}
		if len(req.SeriesIds) > 0 {
			m = m.WhereIn(dao.KsSeriesReportCoreData.Columns().SeriesId, req.SeriesIds)
		}
		if req.AccuFansUserNum != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().AccuFansUserNum+" = ?", gconv.Int(req.AccuFansUserNum))
		}
		if req.FansUserNum != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().FansUserNum+" = ?", gconv.Int(req.FansUserNum))
		}
		if req.PayUserCount != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().PayUserCount+" = ?", gconv.Int(req.PayUserCount))
		}
		if req.PayCount != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().PayCount+" = ?", req.PayCount)
		}
		if req.IsFansUser != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().IsFansUser+" = ?", gconv.Int(req.IsFansUser))
		}
		if req.DisplayPlayCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayPlayCnt+" = ?", gconv.Int(req.DisplayPlayCnt))
		}
		if req.DisplayLikeCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayLikeCnt+" = ?", gconv.Int(req.DisplayLikeCnt))
		}
		if req.DisplayCommentCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayCommentCnt+" = ?", gconv.Int(req.DisplayCommentCnt))
		}
		if req.DisplayCollectCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayCollectCnt+" = ?", gconv.Int(req.DisplayCollectCnt))
		}
		if req.Date != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Date+" = ?", gconv.Time(req.Date))
		}
		if len(req.StartDate) > 0 && len(req.EndDate) > 0 {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Date+" >= ?", req.StartDate)
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Date+" <= ?", req.EndDate)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.KsSeriesReportCoreDataListRes
		var summary = new(model.StatSummaryDataRes)
		m.FieldSum(dao.KsSeriesReportCoreData.Columns().TotalCharge, "totalCharge").
			FieldSum(dao.KsSeriesReportCoreData.Columns().AccuFansUserNum, "accuFansUserNum").
			FieldSum(dao.KsSeriesReportCoreData.Columns().FansUserNum, "fansUserNum").
			FieldSum(dao.KsSeriesReportCoreData.Columns().PayUserCount, "payUserCount").
			FieldSum(dao.KsSeriesReportCoreData.Columns().PayAmt, "payAmt").
			FieldSum(dao.KsSeriesReportCoreData.Columns().DisplayPlayCnt, "displayPlayCnt").
			FieldSum(dao.KsSeriesReportCoreData.Columns().DisplayLikeCnt, "displayLikeCnt").
			FieldSum(dao.KsSeriesReportCoreData.Columns().DisplayCommentCnt, "displayCommentCnt").
			FieldSum(dao.KsSeriesReportCoreData.Columns().DisplayCollectCnt, "displayCollectCnt").
			FieldSum(dao.KsSeriesReportCoreData.Columns().MiniGameIaaPurchaseAmount, "miniGameIaaPurchaseAmount").
			Scan(&summary)
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsSeriesReportCoreDataListRes, len(res))
		listRes.SummaryData = summary
		aidList := make([]int64, 0)
		for k, v := range res {
			aidList = append(aidList, v.AdvertiserId)
			listRes.List[k] = &model.KsSeriesReportCoreDataListRes{
				Id:                               v.Id,
				AdvertiserId:                     v.AdvertiserId,
				SeriesId:                         v.SeriesId,
				TotalCharge:                      v.TotalCharge,
				AccuFansUserNum:                  v.AccuFansUserNum,
				FansUserNum:                      v.FansUserNum,
				EventPayRoi:                      v.EventPayRoi,
				EventPayRoiAll:                   v.EventPayRoiAll,
				PayUserCount:                     v.PayUserCount,
				PayCount:                         v.PayCount,
				PayAmt:                           v.PayAmt,
				IsFansUser:                       v.IsFansUser,
				DisplayPlayCnt:                   v.DisplayPlayCnt,
				DisplayLikeCnt:                   v.DisplayLikeCnt,
				DisplayCommentCnt:                v.DisplayCommentCnt,
				DisplayCollectCnt:                v.DisplayCollectCnt,
				MiniGameIaaRoi:                   v.MiniGameIaaRoi,
				MiniGameIaaPurchaseAmount:        v.MiniGameIaaPurchaseAmount,
				MiniGameIaaPurchaseAmountDivided: v.MiniGameIaaPurchaseAmountDivided,
				MiniGameIaaDividedRoi:            v.MiniGameIaaDividedRoi,
				Date:                             v.Date,
			}
		}
		aList, _ := service.KsAdInfo().GetByAIds(ctx, aidList)
		for _, item := range listRes.List {
			for _, adInfo := range aList {
				if item.AdvertiserId == adInfo.AdvertiserId {
					item.AdAccountName = adInfo.AdAccountName
					item.AccountMainName = adInfo.AccountMainName
				}
			}
		}
	})
	return
}

func (s *sKsSeriesReportCoreData) GetExportData(ctx context.Context, req *model.KsSeriesReportCoreDataSearchReq) (listRes []*model.KsSeriesReportCoreDataInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsSeriesReportCoreData.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if req.SeriesId != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().SeriesId+" = ?", gconv.Int64(req.SeriesId))
		}
		if len(req.SeriesIds) > 0 {
			m = m.WhereIn(dao.KsSeriesReportCoreData.Columns().SeriesId, req.SeriesIds)
		}
		if req.AccuFansUserNum != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().AccuFansUserNum+" = ?", gconv.Int(req.AccuFansUserNum))
		}
		if req.FansUserNum != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().FansUserNum+" = ?", gconv.Int(req.FansUserNum))
		}
		if req.PayUserCount != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().PayUserCount+" = ?", gconv.Int(req.PayUserCount))
		}
		if req.PayCount != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().PayCount+" = ?", req.PayCount)
		}
		if req.IsFansUser != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().IsFansUser+" = ?", gconv.Int(req.IsFansUser))
		}
		if req.DisplayPlayCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayPlayCnt+" = ?", gconv.Int(req.DisplayPlayCnt))
		}
		if req.DisplayLikeCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayLikeCnt+" = ?", gconv.Int(req.DisplayLikeCnt))
		}
		if req.DisplayCommentCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayCommentCnt+" = ?", gconv.Int(req.DisplayCommentCnt))
		}
		if req.DisplayCollectCnt != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().DisplayCollectCnt+" = ?", gconv.Int(req.DisplayCollectCnt))
		}
		if req.Date != "" {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Date+" = ?", gconv.Time(req.Date))
		}
		if len(req.StartDate) > 0 && len(req.EndDate) > 0 {
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Date+" >= ?", req.StartDate)
			m = m.Where(dao.KsSeriesReportCoreData.Columns().Date+" <= ?", req.EndDate)
		}

		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sKsSeriesReportCoreData) GetById(ctx context.Context, id uint64) (res *model.KsSeriesReportCoreDataInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsSeriesReportCoreData.Ctx(ctx).WithAll().Where(dao.KsSeriesReportCoreData.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsSeriesReportCoreData) Add(ctx context.Context, req *model.KsSeriesReportCoreDataAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsSeriesReportCoreData.Ctx(ctx).Insert(do.KsSeriesReportCoreData{
			AdvertiserId:                     req.AdvertiserId,
			SeriesId:                         req.SeriesId,
			TotalCharge:                      req.TotalCharge,
			AccuFansUserNum:                  req.AccuFansUserNum,
			FansUserNum:                      req.FansUserNum,
			EventPayRoi:                      req.EventPayRoi,
			EventPayRoiAll:                   req.EventPayRoiAll,
			PayUserCount:                     req.PayUserCount,
			PayCount:                         req.PayCount,
			PayAmt:                           req.PayAmt,
			IsFansUser:                       req.IsFansUser,
			DisplayPlayCnt:                   req.DisplayPlayCnt,
			DisplayLikeCnt:                   req.DisplayLikeCnt,
			DisplayCommentCnt:                req.DisplayCommentCnt,
			DisplayCollectCnt:                req.DisplayCollectCnt,
			MiniGameIaaRoi:                   req.MiniGameIaaRoi,
			MiniGameIaaPurchaseAmount:        req.MiniGameIaaPurchaseAmount,
			MiniGameIaaPurchaseAmountDivided: req.MiniGameIaaPurchaseAmountDivided,
			MiniGameIaaDividedRoi:            req.MiniGameIaaDividedRoi,
			Date:                             req.Date,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsSeriesReportCoreData) Edit(ctx context.Context, req *model.KsSeriesReportCoreDataEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsSeriesReportCoreData.Ctx(ctx).WherePri(req.Id).Update(do.KsSeriesReportCoreData{
			AdvertiserId:                     req.AdvertiserId,
			SeriesId:                         req.SeriesId,
			TotalCharge:                      req.TotalCharge,
			AccuFansUserNum:                  req.AccuFansUserNum,
			FansUserNum:                      req.FansUserNum,
			EventPayRoi:                      req.EventPayRoi,
			EventPayRoiAll:                   req.EventPayRoiAll,
			PayUserCount:                     req.PayUserCount,
			PayCount:                         req.PayCount,
			PayAmt:                           req.PayAmt,
			IsFansUser:                       req.IsFansUser,
			DisplayPlayCnt:                   req.DisplayPlayCnt,
			DisplayLikeCnt:                   req.DisplayLikeCnt,
			DisplayCommentCnt:                req.DisplayCommentCnt,
			DisplayCollectCnt:                req.DisplayCollectCnt,
			MiniGameIaaRoi:                   req.MiniGameIaaRoi,
			MiniGameIaaPurchaseAmount:        req.MiniGameIaaPurchaseAmount,
			MiniGameIaaPurchaseAmountDivided: req.MiniGameIaaPurchaseAmountDivided,
			MiniGameIaaDividedRoi:            req.MiniGameIaaDividedRoi,
			Date:                             req.Date,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsSeriesReportCoreData) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsSeriesReportCoreData.Ctx(ctx).Delete(dao.KsSeriesReportCoreData.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
