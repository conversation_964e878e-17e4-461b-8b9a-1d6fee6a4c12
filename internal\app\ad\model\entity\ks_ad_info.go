// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: internal/app/ad/model/entity/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdInfo is the golang structure for table ks_ad_info.
type KsAdInfo struct {
	gmeta.Meta      `orm:"table:ks_ad_info"`
	Id              int         `orm:"id,primary" json:"id"`                     // ID
	AdvertiserId    int64       `orm:"advertiser_id" json:"advertiserId"`        // 用户快手号id/广告主id
	AppId           int         `orm:"app_id" json:"appId"`                      // 授权的app_id
	AdAccountName   string      `orm:"ad_account_name" json:"adAccountName"`     // 快手经营者账号名称
	AccountMainName string      `orm:"account_main_name" json:"accountMainName"` // 账号主体名称
	Status          string      `orm:"status" json:"status"`                     //
	AuthTime        *gtime.Time `orm:"auth_time" json:"authTime"`                // 授权时间
	AuthUrl         string      `orm:"auth_url" json:"authUrl"`                  // 授权链接地址
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt"`              // 创建时间
}
