// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: internal/app/ad/dao/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdOrderDetailDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdOrderDetailDao struct {
	*internal.KsAdOrderDetailDao
}

var (
	// KsAdOrderDetail is globally public accessible object for table tools_gen_table operations.
	KsAdOrderDetail = ksAdOrderDetailDao{
		internal.NewKsAdOrderDetailDao(),
	}
)

// Fill with you ideas below.
