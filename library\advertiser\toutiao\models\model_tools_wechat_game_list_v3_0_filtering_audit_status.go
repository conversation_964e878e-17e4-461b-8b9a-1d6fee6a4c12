/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameListV30FilteringAuditStatus
type ToolsWechatGameListV30FilteringAuditStatus string

// List of tools_wechat_game_list_v3.0_filtering_audit_status
const (
	AUDIT_ACCEPTED_ToolsWechatGameListV30FilteringAuditStatus ToolsWechatGameListV30FilteringAuditStatus = "AUDIT_ACCEPTED"
	AUDITING_ToolsWechatGameListV30FilteringAuditStatus       ToolsWechatGameListV30FilteringAuditStatus = "AUDITING"
	AUDIT_REJECTED_ToolsWechatGameListV30FilteringAuditStatus ToolsWechatGameListV30FilteringAuditStatus = "AUDIT_REJECTED"
	ALL_ToolsWechatGameListV30FilteringAuditStatus            ToolsWechatGameListV30FilteringAuditStatus = "ALL"
)

// Ptr returns reference to tools_wechat_game_list_v3.0_filtering_audit_status value
func (v ToolsWechatGameListV30FilteringAuditStatus) Ptr() *ToolsWechatGameListV30FilteringAuditStatus {
	return &v
}
