// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-05 10:39:25
// 生成路径: api/v1/channel/s_channel_advertiser_record.go
// 生成人：cq
// desc:广告主记录表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelAdvertiserRecordSearchReq 分页请求参数
type SChannelAdvertiserRecordSearchReq struct {
	g.Meta `path:"/list" tags:"广告主记录表" method:"post" summary:"广告主记录表列表"`
	commonApi.Author
	model.SChannelAdvertiserRecordSearchReq
}

// SChannelAdvertiserRecordSearchRes 列表返回结果
type SChannelAdvertiserRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelAdvertiserRecordSearchRes
}
