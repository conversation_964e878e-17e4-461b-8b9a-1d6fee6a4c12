// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-06 10:52:30
// 生成路径: api/v1/member/m_member_points.go
// 生成人：cq
// desc:用户积分表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberPointsSearchReq 分页请求参数
type MMemberPointsSearchReq struct {
	g.Meta `path:"/list" tags:"用户积分表" method:"get" summary:"用户积分表列表"`
	commonApi.Author
	model.MMemberPointsSearchReq
}

// MMemberPointsSearchRes 列表返回结果
type MMemberPointsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberPointsSearchRes
}

// MMemberPointsEditReq 修改操作请求参数
type MMemberPointsEditReq struct {
	g.Meta `path:"/edit" tags:"用户积分表" method:"put" summary:"用户积分表修改"`
	commonApi.Author
	*model.MMemberPointsEditReq
}

// MMemberPointsEditRes 修改操作返回结果
type MMemberPointsEditRes struct {
	commonApi.EmptyRes
}
