// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-19 10:47:08
// 生成路径: api/v1/oceanengine/ad_promotion_metrics_data.go
// 生成人：cyao
// desc:广告账户下的广告的指标数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdPromotionMetricsDataSearchReq 分页请求参数
type AdPromotionMetricsDataSearchReq struct {
	g.Meta `path:"/list" tags:"广告账户下的广告的指标数据" method:"get" summary:"广告账户下的广告的指标数据列表"`
	commonApi.Author
	model.AdPromotionMetricsDataSearchReq
}

// AdPromotionReportReportSubTaskReq 订阅账户的报表数据
type AdPromotionReportReportSubTaskReq struct {
	g.Meta `path:"/getReport/sub/task" tags:"广告账户下的广告的指标数据" method:"post" summary:"AdPromotionReportMetricsData"`
	commonApi.Author
	StartTime string   `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string   `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	PIds      []string `p:"pIds" dc:"广告计划id"`
}

// AdPromotionReportReportSubTaskRes 返回
type AdPromotionReportReportSubTaskRes struct {
	g.Meta `mime:"application/json"`
}

// AdPromotionReportReportStatTaskReq 刷账户的报表数据
type AdPromotionReportReportStatTaskReq struct {
	g.Meta `path:"/getReport/stat/task" tags:"广告账户下的广告的指标数据" method:"post" summary:"AdPromotionReportMetricsData"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

// AdPromotionReportReportStatTaskRes 返回
type AdPromotionReportReportStatTaskRes struct {
	g.Meta `mime:"application/json"`
}

// AdPromotionMetricsDataSearchRes 列表返回结果
type AdPromotionMetricsDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionMetricsDataSearchRes
}

// AdPromotionMetricsDataAddReq 添加操作请求参数
type AdPromotionMetricsDataAddReq struct {
	g.Meta `path:"/add" tags:"广告账户下的广告的指标数据" method:"post" summary:"广告账户下的广告的指标数据添加"`
	commonApi.Author
	*model.AdPromotionMetricsDataAddReq
}

// AdPromotionMetricsDataAddRes 添加操作返回结果
type AdPromotionMetricsDataAddRes struct {
	commonApi.EmptyRes
}

// AdPromotionMetricsDataEditReq 修改操作请求参数
type AdPromotionMetricsDataEditReq struct {
	g.Meta `path:"/edit" tags:"广告账户下的广告的指标数据" method:"put" summary:"广告账户下的广告的指标数据修改"`
	commonApi.Author
	*model.AdPromotionMetricsDataEditReq
}

// AdPromotionMetricsDataEditRes 修改操作返回结果
type AdPromotionMetricsDataEditRes struct {
	commonApi.EmptyRes
}

// AdPromotionMetricsDataGetReq 获取一条数据请求
type AdPromotionMetricsDataGetReq struct {
	g.Meta `path:"/get" tags:"广告账户下的广告的指标数据" method:"get" summary:"获取广告账户下的广告的指标数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPromotionMetricsDataGetRes 获取一条数据结果
type AdPromotionMetricsDataGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionMetricsDataInfoRes
}

// AdPromotionMetricsDataDeleteReq 删除数据请求
type AdPromotionMetricsDataDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告账户下的广告的指标数据" method:"delete" summary:"删除广告账户下的广告的指标数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPromotionMetricsDataDeleteRes 删除数据返回
type AdPromotionMetricsDataDeleteRes struct {
	commonApi.EmptyRes
}

// AdPromotionReportDataSearchReq 获取报表数据
type AdPromotionReportDataSearchReq struct {
	g.Meta `path:"/getReport" tags:"广告账户下的广告的指标数据" method:"post" summary:"广告计划指标数据列表"`
	commonApi.Author
	model.AdPromotionReportDataSearch
}

type AdPromotionReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionReportDataSearchRes
}

// AdPromotionReportDataSearchReq 获取报表数据
type AdPromotionReportDataSearch2Req struct {
	g.Meta `path:"/getReport2" tags:"广告账户下的广告的指标数据" method:"post" summary:"广告计划指标数据列表"`
	commonApi.Author
	model.AdPromotionReportDataSearch2
}

type AdPromotionReportDataSearch2Res struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionReportDataSearchRes2
}

type AdPromotionAccountReportDataSearchReq struct {
	g.Meta `path:"/getAccountReport" tags:"广告账户下的广告的指标数据" method:"post" summary:"广告计划账号指标数据列表"`
	commonApi.Author
	model.AdPromotionAccountReportDataSearch
}

type AdPromotionAccountReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionAccountReportDataSearchRes
}

// 获取素材统计
type AdPromotionMaterialStatisticsReq struct {
	g.Meta `path:"/materialStatistics" tags:"广告账户下的广告的指标数据" method:"post" summary:"素材数据统计"`
	commonApi.Author
	model.AdPromotionMaterialStatisticsReq
}

type AdPromotionMaterialStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionMaterialReportDataSearchRes
}

// OptimizerDataStatisticsReq 优化师数据统计
type OptimizerDataStatisticsReq struct {
	g.Meta `path:"/optimizerDataStat" tags:"广告账户的指标数据" method:"post" summary:"优化师数据统计"`
	commonApi.Author
	model.OptimizerDataStatisticsReq
}

type OptimizerDataStatisticsRes struct {
	g.Meta `mime:"application/json"`
	*model.OptimizerDataStatisticsRes
}
