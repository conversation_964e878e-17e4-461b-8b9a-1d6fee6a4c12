// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: internal/app/adx/model/entity/adx_task.go
// 生成人：cyao
// desc:ADX任务主表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxTask is the golang structure for table adx_task.
type AdxTask struct {
	gmeta.Meta     `orm:"table:adx_task"`
	Id             int64       `orm:"id,primary" json:"id"`                  // 主键ID
	TaskName       string      `orm:"task_name" json:"taskName"`             // 任务名称
	OperationType  string      `orm:"operation_type" json:"operationType"`   // 操作类型（批量下载、单条下载）
	OperationCount int         `orm:"operation_count" json:"operationCount"` // 操作数量（下载视频数量）
	OperationTime  *gtime.Time `orm:"operation_time" json:"operationTime"`   // 操作时间
	Status         string      `orm:"status" json:"status"`                  // 执行状态（执行中、执行完成、执行失败）
	Operator       string      `orm:"operator" json:"operator"`              // 提交任务的用户名
	DownloadUrl    string      `orm:"download_url" json:"downloadUrl"`       // 打包下载地址（OSS压缩包地址）
	FailLog        string      `orm:"fail_log" json:"failLog"`               // 任务执行失败日志
	TotalFileSize  int64       `orm:"total_file_size" json:"totalFileSize"`  // 总文件大小，单位：字节
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`           // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`           // 更新时间
}
