// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: internal/app/ad/controller/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdUserPaymentRecordController struct {
	systemController.BaseController
}

var FqAdUserPaymentRecord = new(fqAdUserPaymentRecordController)

// List 列表
func (c *fqAdUserPaymentRecordController) List(ctx context.Context, req *ad.FqAdUserPaymentRecordSearchReq) (res *ad.FqAdUserPaymentRecordSearchRes, err error) {
	res = new(ad.FqAdUserPaymentRecordSearchRes)
	res.FqAdUserPaymentRecordSearchRes, err = service.FqAdUserPaymentRecord().List(ctx, &req.FqAdUserPaymentRecordSearchReq)
	return
}

func (c *fqAdAnalyzeDataController) Pull(ctx context.Context, req *ad.FqAdUserPaymentPullReq) (res *ad.FqAdUserPaymentPullRes, err error) {
	res = new(ad.FqAdUserPaymentPullRes)
	var count = 0
	// 修改ctx 不超时
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	for {
		if req.StartTime > req.EndTime {
			break
		}
		count, err = service.FqAdUserPaymentRecord().Pull(innerCtx, req.StartTime)
		if err != nil {
			g.Log().Error(ctx, err)
		}
		res.Count += count
		req.StartTime = libUtils.PlusDays(req.StartTime, 1)
	}
	return
}

// Export 导出excel
func (c *fqAdUserPaymentRecordController) Export(ctx context.Context, req *ad.FqAdUserPaymentRecordExportReq) (res *ad.FqAdUserPaymentRecordExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdUserPaymentRecordListRes
		listRes  *model.FqAdUserPaymentRecordSearchRes
		//表头
		tableHead = []interface{}{"分销商", "番茄账号", "投手", "渠道名", "常读订单id", "番茄分销id", "公众号/快应用id", "快应用/公众号名称", "付费推广链id", "第三方订单ID", "设备ID", "用户openid（H5书城、微信小程序、抖音小程序）", "微信公众号 open_id（仅适用网文微小复访的公众号用户）", "公众号名称（微小的小程序维度返回）", "付费金额", "用户最近一次点击推广链时的IP", "用户最近一次点击推广链时的UA", "付费时用户OAID（仅支持快应用）", "付费时用户android_id（仅支持快应用）", "用户染色时间", "微信开发者id(仅支持微信H5)", "染色推广链的书籍ID", "染色推广链的书籍名称", "染色推广链书籍性别", "染色推广链的书籍类型", "是否是充值活动", "H5书城用户订单最近阅读书籍（小程序不返回）", "企微用户企微id（公众号返回）", "订单类型：1 拟支付，2 非虚拟支付", "腾讯广告主id", "腾讯广告id", "腾讯广告创意id", "用户在微信/抖音开放平台下的唯一id", "视频ID（仅视频号场景）", "视频号订单类型（仅视频号场景）1.自然流量 2.加热流量", "视频号加热订单ID（仅视频号场景）", "场景参数，用于区分分销自挂载和CPS达人模式", "视频号ID（仅视频号场景）", "支付状态", "支付方式（1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成）", "付费时间戳", "订单创建时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.FqAdUserPaymentRecord().List(ctx, &req.FqAdUserPaymentRecordSearchReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				gconv.String(v.DistributorName),
				gconv.String(v.FqAccount),
				gconv.String(v.UserName),
				gconv.String(v.ChannelCode),
				gconv.String(v.TradeNo),
				gconv.String(v.DistributorId),
				v.AppId,
				v.AppName,
				gconv.String(v.PromotionId),
				v.OutTradeNo,
				v.DeviceId,
				v.OpenId,
				v.WxOaOpenId,
				v.WxOaName,
				v.PayAmount,
				v.Ip,
				v.UserAgent,
				v.Oaid,
				v.AndroidId,
				v.RegisterTime,
				v.WxPlatformAppKey,
				v.BookId,
				v.BookName,
				v.BookGender,
				v.BookCategory,
				v.Activity,
				v.RecentReadBookId,
				v.ExternalId,
				v.OrderType,
				v.AdvertiserId,
				v.AdgroupId,
				v.AdId,
				v.UnionId,
				v.WxVideoId,
				v.WxVcSourceType,
				v.WxPromotionId,
				v.WxSourceType,
				v.WxVideoChannelId,
				v.Status,
				v.PayWay,
				v.PayTimestamp,
				v.CreateTime,
			}

			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("用户买入行为- 对应番茄用户买入接口")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取用户买入行为- 对应番茄用户买入接口
func (c *fqAdUserPaymentRecordController) Get(ctx context.Context, req *ad.FqAdUserPaymentRecordGetReq) (res *ad.FqAdUserPaymentRecordGetRes, err error) {
	res = new(ad.FqAdUserPaymentRecordGetRes)
	res.FqAdUserPaymentRecordInfoRes, err = service.FqAdUserPaymentRecord().GetByTradeNo(ctx, req.TradeNo)
	return
}

// Add 添加用户买入行为- 对应番茄用户买入接口
func (c *fqAdUserPaymentRecordController) Add(ctx context.Context, req *ad.FqAdUserPaymentRecordAddReq) (res *ad.FqAdUserPaymentRecordAddRes, err error) {
	err = service.FqAdUserPaymentRecord().Add(ctx, req.FqAdUserPaymentRecordAddReq)
	return
}

func (c *fqAdUserPaymentRecordController) Save(ctx context.Context, req *ad.FqAdUserPaymentRecordSaveReq) (res *ad.FqAdUserPaymentRecordAddRes, err error) {
	err = service.FqAdUserPaymentRecord().Save(ctx, req.FqAdUserPaymentRecordSaveReq)
	return
}

// Edit 修改用户买入行为- 对应番茄用户买入接口
func (c *fqAdUserPaymentRecordController) Edit(ctx context.Context, req *ad.FqAdUserPaymentRecordEditReq) (res *ad.FqAdUserPaymentRecordEditRes, err error) {
	err = service.FqAdUserPaymentRecord().Edit(ctx, req.FqAdUserPaymentRecordEditReq)
	return
}

// Delete 删除用户买入行为- 对应番茄用户买入接口
func (c *fqAdUserPaymentRecordController) Delete(ctx context.Context, req *ad.FqAdUserPaymentRecordDeleteReq) (res *ad.FqAdUserPaymentRecordDeleteRes, err error) {
	err = service.FqAdUserPaymentRecord().Delete(ctx, req.TradeNos)
	return
}
