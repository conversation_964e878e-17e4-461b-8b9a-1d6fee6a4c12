// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-12-11 13:50:11
// 生成路径: internal/app/ad/dao/internal/ad_common_asset_title.go
// 生成人：cq
// desc:通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdCommonAssetTitleDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdCommonAssetTitleDao struct {
	table   string                    // Table is the underlying table name of the DAO.
	group   string                    // Group is the database configuration group name of current DAO.
	columns AdCommonAssetTitleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdCommonAssetTitleColumns defines and stores column names for table ad_common_asset_title.
type AdCommonAssetTitleColumns struct {
	Id                string //
	Title             string // 标题
	CategoryIds       string // 标题分类ID列表，以|分隔
	UserId            string // 创建者
	Last3DayClickRate string // 近3日点击率
	Last3DayCost      string // 近3日消耗
	HistoryClickRate  string // 历史点击率
	HistoryCost       string // 历史消耗
	AdCount           string // 关联广告数
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 删除时间
}

var adCommonAssetTitleColumns = AdCommonAssetTitleColumns{
	Id:                "id",
	Title:             "title",
	CategoryIds:       "category_ids",
	UserId:            "user_id",
	Last3DayClickRate: "last_3_day_click_rate",
	Last3DayCost:      "last_3_day_cost",
	HistoryClickRate:  "history_click_rate",
	HistoryCost:       "history_cost",
	AdCount:           "ad_count",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
}

// NewAdCommonAssetTitleDao creates and returns a new DAO object for table data access.
func NewAdCommonAssetTitleDao() *AdCommonAssetTitleDao {
	return &AdCommonAssetTitleDao{
		group:   "default",
		table:   "ad_common_asset_title",
		columns: adCommonAssetTitleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdCommonAssetTitleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdCommonAssetTitleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdCommonAssetTitleDao) Columns() AdCommonAssetTitleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdCommonAssetTitleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdCommonAssetTitleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdCommonAssetTitleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
