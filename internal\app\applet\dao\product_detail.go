// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-08-28 15:47:10
// 生成路径: internal/app/applet/dao/product_detail.go
// 生成人：cq
// desc:商品详情表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// productDetailDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type productDetailDao struct {
	*internal.ProductDetailDao
}

var (
	// ProductDetail is globally public accessible object for table tools_gen_table operations.
	ProductDetail = productDetailDao{
		internal.NewProductDetailDao(),
	}
)

// Fill with you ideas below.
