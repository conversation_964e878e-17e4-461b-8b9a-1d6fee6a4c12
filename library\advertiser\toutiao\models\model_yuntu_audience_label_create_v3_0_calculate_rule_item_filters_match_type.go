/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType
type YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType int64

// List of yuntu_audience_label_create_v3.0_calculate_rule_item_filters_match_type
const (
	Enum_0_YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType = 0
)

// Ptr returns reference to yuntu_audience_label_create_v3.0_calculate_rule_item_filters_match_type value
func (v YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType) Ptr() *YuntuAudienceLabelCreateV30CalculateRuleItemFiltersMatchType {
	return &v
}
