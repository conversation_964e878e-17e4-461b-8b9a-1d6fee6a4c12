// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-16 10:33:40
// 生成路径: api/v1/oceanengine/ad_promotion.go
// 生成人：cq
// desc:巨量广告表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdPromotionSearchReq 分页请求参数
type AdPromotionSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告表" method:"post" summary:"巨量广告表列表"`
	commonApi.Author
	model.AdPromotionSearchReq
}

// AdPromotionSearchRes 列表返回结果
type AdPromotionSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionSearchRes
}

// SelectDouyinAccountReq 选择抖音号
type SelectDouyinAccountReq struct {
	g.Meta `path:"/selectDouyinAccount" tags:"巨量广告表" method:"get" summary:"选择抖音号"`
	commonApi.Author
	model.SelectDouyinAccountReq
}

type SelectDouyinAccountRes struct {
	g.Meta `mime:"application/json"`
	*model.SelectDouyinAccountRes
}

// getNativeAnchor
type GetNativeAnchorReq struct {
	g.Meta `path:"/getNativeAnchor" tags:"巨量广告表" method:"get" summary:"获取锚点"`
	commonApi.Author
	model.GetNativeAnchorReq
}

type GetNativeAnchorRes struct {
	g.Meta `mime:"application/json"`
	*model.GetNativeAnchorRes
}

type GetToutiaoCreativeComponentReq struct {
	g.Meta `path:"/getToutiaoCreativeComponentList" tags:"巨量广告表" method:"post" summary:"选择附加创意组件"`
	commonApi.Author
	model.GetToutiaoCreativeComponentReq
}

type GetToutiaoCreativeComponentRes struct {
	g.Meta `mime:"application/json"`
	*model.GetToutiaoCreativeComponentRes
}

// AdPromotionExportReq 导出请求
type AdPromotionExportReq struct {
	g.Meta `path:"/export" tags:"巨量广告表" method:"get" summary:"巨量广告表导出"`
	commonApi.Author
	model.AdPromotionSearchReq
}

// AdPromotionExportRes 导出响应
type AdPromotionExportRes struct {
	commonApi.EmptyRes
}

// AdPromotionAddReq 添加操作请求参数
type AdPromotionAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告表" method:"post" summary:"巨量广告表添加"`
	commonApi.Author
	*model.AdPromotionAddReq
}

// AdPromotionAddRes 添加操作返回结果
type AdPromotionAddRes struct {
	commonApi.EmptyRes
}

// AdPromotionEditReq 修改操作请求参数
type AdPromotionEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告表" method:"put" summary:"巨量广告表修改"`
	commonApi.Author
	*model.AdPromotionEditReq
}

// AdPromotionEditRes 修改操作返回结果
type AdPromotionEditRes struct {
	commonApi.EmptyRes
}

// AdPromotionGetReq 获取一条数据请求
type AdPromotionGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告表" method:"get" summary:"获取巨量广告表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPromotionGetRes 获取一条数据结果
type AdPromotionGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPromotionInfoRes
}

// AdPromotionDeleteReq 删除数据请求
type AdPromotionDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告表" method:"post" summary:"删除巨量广告表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPromotionDeleteRes 删除数据返回
type AdPromotionDeleteRes struct {
	commonApi.EmptyRes
}

// RunSyncAdPromotionReq 同步巨量广告计划请求
type RunSyncAdPromotionReq struct {
	g.Meta `path:"/task" tags:"巨量广告表" method:"post" summary:"同步巨量广告计划"`
	commonApi.Author
	*model.SyncAdPromotionReq
}

// RunSyncAdPromotionRes 同步巨量广告计划返回
type RunSyncAdPromotionRes struct {
	commonApi.EmptyRes
}

// AdPromotionStatusUpdateReq 修改广告计划状态请求
type AdPromotionStatusUpdateReq struct {
	g.Meta `path:"/status/update" tags:"巨量广告表" method:"post" summary:"修改广告计划状态"`
	commonApi.Author
	*model.AdPromotionStatusUpdateReq
}

// AdPromotionStatusUpdateRes 修改广告计划状态返回
type AdPromotionStatusUpdateRes struct {
	commonApi.EmptyRes
}
