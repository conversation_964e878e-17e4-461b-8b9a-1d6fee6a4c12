// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: internal/app/ad/controller/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type dzAdAccountChannelController struct {
	systemController.BaseController
}

var DzAdAccountChannel = new(dzAdAccountChannelController)

// List 列表
func (c *dzAdAccountChannelController) List(ctx context.Context, req *ad.DzAdAccountChannelSearchReq) (res *ad.DzAdAccountChannelSearchRes, err error) {
	res = new(ad.DzAdAccountChannelSearchRes)
	res.DzAdAccountChannelSearchRes, err = service.DzAdAccountChannel().List(ctx, &req.DzAdAccountChannelSearchReq)
	return
}

// Export 导出excel
func (c *dzAdAccountChannelController) Export(ctx context.Context, req *ad.DzAdAccountChannelExportReq) (res *ad.DzAdAccountChannelExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.DzAdAccountChannelInfoRes
		//表头
		tableHead = []interface{}{"渠道ID", "主账号ID", "渠道昵称", "渠道用户名称", "1单端，2双端", "创建时间", "更新时间", "删除时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.DzAdAccountChannel().GetExportData(ctx, &req.DzAdAccountChannelSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.ChannelId,
				v.AccountId,
				v.NickName,
				v.UserName,
				v.JumpType,
				v.CreatedAt.Format("Y-m-d H:i:s"),
				v.UpdatedAt.Format("Y-m-d H:i:s"),
				v.DeletedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("点众渠道")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取点众渠道
func (c *dzAdAccountChannelController) Get(ctx context.Context, req *ad.DzAdAccountChannelGetReq) (res *ad.DzAdAccountChannelGetRes, err error) {
	res = new(ad.DzAdAccountChannelGetRes)
	res.DzAdAccountChannelInfoRes, err = service.DzAdAccountChannel().GetByChannelId(ctx, req.ChannelId)
	return
}

// Add 添加点众渠道
func (c *dzAdAccountChannelController) Add(ctx context.Context, req *ad.DzAdAccountChannelAddReq) (res *ad.DzAdAccountChannelAddRes, err error) {
	err = service.DzAdAccountChannel().Add(ctx, req.DzAdAccountChannelAddReq)
	return
}

// SetAppId
func (c *dzAdAccountChannelController) SetAppId(ctx context.Context, req *ad.DzAdAccountChannelSetAppIdReq) (res *ad.DzAdAccountChannelSetAppIdRes, err error) {
	err = service.DzAdAccountChannel().SetAppId(ctx, req.DzAdAccountChannelSetAppIdReq)
	return
}

// Edit 修改点众渠道
func (c *dzAdAccountChannelController) Edit(ctx context.Context, req *ad.DzAdAccountChannelEditReq) (res *ad.DzAdAccountChannelEditRes, err error) {
	err = service.DzAdAccountChannel().Edit(ctx, req.DzAdAccountChannelEditReq)
	return
}

// Delete 删除点众渠道
func (c *dzAdAccountChannelController) Delete(ctx context.Context, req *ad.DzAdAccountChannelDeleteReq) (res *ad.DzAdAccountChannelDeleteRes, err error) {
	err = service.DzAdAccountChannel().Delete(ctx, req.ChannelIds)
	return
}

// SyncDZAdAccountChannel 同步渠道
func (c *dzAdAccountChannelController) SyncDZAdAccountChannel(ctx context.Context, req *ad.SyncDZAdAccountChannelReq) (res *ad.SyncFqAdAccountChannelRes, err error) {
	err = service.DzAdAccountChannel().SyncDzAdAccountChannel(ctx)
	return
}

// AddDZAuth
func (c *dzAdAccountChannelController) AddDZAuth(ctx context.Context, req *ad.AddDZAuthReq) (res *ad.AddDZAuthRes, err error) {
	err = service.DzAdAccountChannel().AddDZAuth(ctx, req.DZAdAddAuthReq)
	return
}
