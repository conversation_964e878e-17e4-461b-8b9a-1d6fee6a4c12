// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: internal/app/ad/model/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdSalerCopyRightInfoRes is the golang structure for table ks_ad_saler_copy_right.
type KsAdSalerCopyRightInfoRes struct {
	gmeta.Meta                                `orm:"table:ks_ad_saler_copy_right"`
	Id                                        int64       `orm:"id,primary" json:"id" dc:"id"`                                                                                                        // id
	AdvertiserId                              int64       `orm:"advertiser_id" json:"advertiserId" dc:"用户快手号id"`                                                                                      // 用户快手号id
	ReportDate                                string      `orm:"report_date" json:"reportDate" dc:"日期"`                                                                                               // 日期
	CopyrightSeriesName                       string      `orm:"copyright_series_name" json:"copyrightSeriesName" dc:"短剧名称"`                                                                          // 短剧名称
	SalerEntityName                           string      `orm:"saler_entity_name" json:"salerEntityName" dc:"分销商机构主体名称"`                                                                             // 分销商机构主体名称
	SalerRateStr                              string      `orm:"saler_rate_str" json:"salerRateStr" dc:"版权方: 10.0%, 分销商: 90.0%"`                                                                      // 版权方: 10.0%, 分销商: 90.0%
	PayAmt                                    float64     `orm:"pay_amt" json:"payAmt" dc:"付费金额 (seriesType为2时特有)"`                                                                                   // 付费金额 (seriesType为2时特有)
	CopyrightEventPayPurchaseAmount           float64     `orm:"copyright_event_pay_purchase_amount" json:"copyrightEventPayPurchaseAmount" dc:"版权方分成金额 (seriesType为2时特有)"`                           // 版权方分成金额 (seriesType为2时特有)
	MiniGameIaaPurchaseAmount                 float64     `orm:"mini_game_iaa_purchase_amount" json:"miniGameIaaPurchaseAmount" dc:"IAA广告含返货LTV(元) (seriesType为1时特有)"`                                // IAA广告含返货LTV(元) (seriesType为1时特有)
	MiniGameIaaPurchaseAmountDivided          float64     `orm:"mini_game_iaa_purchase_amount_divided" json:"miniGameIaaPurchaseAmountDivided" dc:"IAA广告不含返货LTV(元) (seriesType为1时特有)"`                // IAA广告不含返货LTV(元) (seriesType为1时特有)
	CopyrightMiniGameIaaPurchaseAmountDivided float64     `orm:"copyright_mini_game_iaa_purchase_amount_divided" json:"copyrightMiniGameIaaPurchaseAmountDivided" dc:"版权方分成LTV(元) (seriesType为1时特有)"` // 版权方分成LTV(元) (seriesType为1时特有)
	SeriesType                                int64       `orm:"series_type" json:"seriesType" dc:"短剧类型: 1->IAA短剧，2->IAP短剧"`
	CreatedAt                                 *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt                                 *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
}

type KsAdSalerCopyRightListRes struct {
	Id                                        int64       `json:"id" dc:"id"`
	AdvertiserId                              int64       `json:"advertiserId" dc:"用户快手号id"`
	AdvertiserName                            string      `json:"advertiserName" dc:"用户快手号名称"`
	ReportDate                                string      `json:"reportDate" dc:"日期"`
	CopyrightSeriesName                       string      `json:"copyrightSeriesName" dc:"短剧名称"`
	SalerEntityName                           string      `json:"salerEntityName" dc:"分销商机构主体名称"`
	SalerRateStr                              string      `json:"salerRateStr" dc:"版权方: 10.0%, 分销商: 90.0%"`
	PayAmt                                    float64     `json:"payAmt" dc:"付费金额 (seriesType为2时特有)"`
	CopyrightEventPayPurchaseAmount           float64     `json:"copyrightEventPayPurchaseAmount" dc:"版权方分成金额 (seriesType为2时特有)"`
	DistributorPurchaseAmount                 float64     `json:"distributorPurchaseAmount" dc:"分销商分成金额 = 付费金额 - 版权方分成金额"`
	MiniGameIaaPurchaseAmount                 float64     `json:"miniGameIaaPurchaseAmount" dc:"IAA广告含返货LTV(元) (seriesType为1时特有)"`
	MiniGameIaaPurchaseAmountDivided          float64     `json:"miniGameIaaPurchaseAmountDivided" dc:"IAA广告不含返货LTV(元) (seriesType为1时特有)"`
	CopyrightMiniGameIaaPurchaseAmountDivided float64     `json:"copyrightMiniGameIaaPurchaseAmountDivided" dc:"版权方分成LTV(元) (seriesType为1时特有)"`
	DistributorPurchaseAmountDivided          float64     `json:"distributorPurchaseAmountDivided" dc:"分销方分成LTV = IAA广告变现LTV(不含返货) - 版权方分成"`
	SeriesType                                int64       `json:"seriesType" dc:"短剧类型: 1->IAA短剧，2->IAP短剧"`
	CreatedAt                                 *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdSalerCopyRightSearchReq 分页请求参数
type KsAdSalerCopyRightSearchReq struct {
	comModel.PageReq
	Id                     string              `p:"id" dc:"id"`
	AdvertiserId           string              `p:"advertiserId" v:"advertiserId@integer#用户快手号id需为整数" dc:"用户快手号id"`
	StartReportDate        string              `p:"startReportDate" dc:"开始日期"`
	EndReportDate          string              `p:"endReportDate" dc:"结束日期"`
	CopyrightSeriesNameMap map[string][]string `p:"copyrightSeriesNameMap" dc:"短剧名称列表"`
	SalerEntityName        string              `p:"salerEntityName" dc:"分销商机构主体名称"`
	SeriesType             int64               `p:"seriesType" dc:"短剧类型: 1->IAA短剧，2->IAP短剧"`
}

// KsAdSalerCopyRightSearchRes 列表返回结果
type KsAdSalerCopyRightSearchRes struct {
	comModel.ListRes
	List    []*KsAdSalerCopyRightListRes `json:"list"`
	Summary *KsAdSalerCopyRightListRes   `json:"summary"`
}

// KsAdSalerCopyRightAddReq 添加操作请求参数
type KsAdSalerCopyRightAddReq struct {
	AdvertiserId                              int64   `p:"advertiserId" v:"required#用户快手号id不能为空" dc:"用户快手号id"`
	ReportDate                                string  `p:"reportDate" v:"required#日期不能为空" dc:"日期"`
	CopyrightSeriesName                       string  `p:"copyrightSeriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	SalerEntityName                           string  `p:"salerEntityName" v:"required#分销商机构主体名称不能为空" dc:"分销商机构主体名称"`
	SalerRateStr                              string  `p:"salerRateStr"  dc:"版权方: 10.0%, 分销商: 90.0%"`
	PayAmt                                    float64 `p:"payAmt"  dc:"付费金额 (seriesType为2时特有)"`
	CopyrightEventPayPurchaseAmount           float64 `p:"copyrightEventPayPurchaseAmount"  dc:"版权方分成金额 (seriesType为2时特有)"`
	MiniGameIaaPurchaseAmount                 float64 `p:"miniGameIaaPurchaseAmount"  dc:"IAA广告含返货LTV(元) (seriesType为1时特有)"`
	MiniGameIaaPurchaseAmountDivided          float64 `p:"miniGameIaaPurchaseAmountDivided"  dc:"IAA广告不含返货LTV(元) (seriesType为1时特有)"`
	CopyrightMiniGameIaaPurchaseAmountDivided float64 `p:"copyrightMiniGameIaaPurchaseAmountDivided"  dc:"版权方分成LTV(元) (seriesType为1时特有)"`
	SeriesType                                int64   `p:"seriesType" dc:"短剧类型: 1->IAA短剧，2->IAP短剧"`
}

// KsAdSalerCopyRightEditReq 修改操作请求参数
type KsAdSalerCopyRightEditReq struct {
	Id                                        int64   `p:"id" v:"required#主键ID不能为空" dc:"id"`
	AdvertiserId                              int64   `p:"advertiserId" v:"required#用户快手号id不能为空" dc:"用户快手号id"`
	ReportDate                                string  `p:"reportDate" v:"required#日期不能为空" dc:"日期"`
	CopyrightSeriesName                       string  `p:"copyrightSeriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	SalerEntityName                           string  `p:"salerEntityName" v:"required#分销商机构主体名称不能为空" dc:"分销商机构主体名称"`
	SalerRateStr                              string  `p:"salerRateStr"  dc:"版权方: 10.0%, 分销商: 90.0%"`
	PayAmt                                    float64 `p:"payAmt"  dc:"付费金额 (seriesType为2时特有)"`
	CopyrightEventPayPurchaseAmount           float64 `p:"copyrightEventPayPurchaseAmount"  dc:"版权方分成金额 (seriesType为2时特有)"`
	MiniGameIaaPurchaseAmount                 float64 `p:"miniGameIaaPurchaseAmount"  dc:"IAA广告含返货LTV(元) (seriesType为1时特有)"`
	MiniGameIaaPurchaseAmountDivided          float64 `p:"miniGameIaaPurchaseAmountDivided"  dc:"IAA广告不含返货LTV(元) (seriesType为1时特有)"`
	CopyrightMiniGameIaaPurchaseAmountDivided float64 `p:"copyrightMiniGameIaaPurchaseAmountDivided"  dc:"版权方分成LTV(元) (seriesType为1时特有)"`
	SeriesType                                int64   `p:"seriesType" dc:"短剧类型: 1->IAA短剧，2->IAP短剧"`
}
