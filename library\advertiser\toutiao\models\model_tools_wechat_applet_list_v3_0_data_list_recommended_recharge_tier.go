/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListRecommendedRechargeTier
type ToolsWechatAppletListV30DataListRecommendedRechargeTier string

// List of tools_wechat_applet_list_v3.0_data_list_recommended_recharge_tier
const (
	ABOVE_200_ToolsWechatAppletListV30DataListRecommendedRechargeTier             ToolsWechatAppletListV30DataListRecommendedRechargeTier = "ABOVE_200"
	FROM_100_TO_200_ToolsWechatAppletListV30DataListRecommendedRechargeTier       ToolsWechatAppletListV30DataListRecommendedRechargeTier = "FROM_100_TO_200"
	FROM_FIFTY_TO_HUNDRED_ToolsWechatAppletListV30DataListRecommendedRechargeTier ToolsWechatAppletListV30DataListRecommendedRechargeTier = "FROM_FIFTY_TO_HUNDRED"
	FROM_ONE_TO_FIFTY_ToolsWechatAppletListV30DataListRecommendedRechargeTier     ToolsWechatAppletListV30DataListRecommendedRechargeTier = "FROM_ONE_TO_FIFTY"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_recommended_recharge_tier value
func (v ToolsWechatAppletListV30DataListRecommendedRechargeTier) Ptr() *ToolsWechatAppletListV30DataListRecommendedRechargeTier {
	return &v
}
