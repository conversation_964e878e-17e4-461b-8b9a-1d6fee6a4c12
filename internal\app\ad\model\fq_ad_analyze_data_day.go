// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: internal/app/ad/model/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdAnalyzeDataDayInfoRes is the golang structure for table fq_ad_analyze_data_day.
type FqAdAnalyzeDataDayInfoRes struct {
	gmeta.Meta     `orm:"table:fq_ad_analyze_data_day"`
	Id             int         `orm:"id,primary" json:"id" dc:"id"`                      // id
	DistributorId  int64       `orm:"distributor_id" json:"distributorId" dc:"渠道ID"`     // 渠道ID
	PromotionId    string      `orm:"promotion_id" json:"promotionId" dc:"推广链id"`        // 推广链id
	StatDate       string      `orm:"stat_date" json:"statDate" dc:"统计日期"`               // 统计日期
	AddDesktopNum  int         `orm:"add_desktop_num" json:"addDesktopNum" dc:"新增桌面用户数"` // 新增桌面用户数
	RechargeNum    int         `orm:"recharge_num" json:"rechargeNum" dc:"充值用户数"`        // 充值用户数
	UserNum        int         `orm:"user_num" json:"userNum" dc:"用户总数"`                 // 用户总数
	RoiDetail      string      `orm:"roi_detail" json:"roiDetail" dc:"ROI 明细，JSON 格式存储"` // ROI 明细，JSON 格式存储
	RechargeAmount int64       `orm:"recharge_amount" json:"rechargeAmount"   dc:"充值金额"`
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"` // 删除时间
}

type FqAdAnalyzeDataDayListRes struct {
	Id                     int         `json:"id" dc:"id"`
	StatDate               string      `json:"statDate" dc:"统计日期"`
	FqAccount              string      `json:"fqAccount" dc:"番茄账号"`
	FqChannel              string      `json:"fqChannel" dc:"渠道"`
	AppId                  string      `json:"appId" dc:"公众号/快应用id（分销平台id）"`
	AppName                string      `json:"appName" dc:"快应用/公众号名称"`
	FqChannelDistributorId string      `json:"fqChannelDistributorId" dc:"番茄渠道ID"`
	ChannelCode            string      `orm:"channel_code" json:"channelCode" dc:"番茄渠道"`
	DistributorName        string      `json:"distributorName" dc:"投手上级分销"`
	UserName               string      `json:"userName" dc:"投手名"`
	UserNum                int         `json:"userNum" dc:"激活数"`
	RechargeNum            int         `json:"rechargeNum" dc:"充值用户数"`
	AddDesktopNum          int         `json:"addDesktopNum" dc:"新增桌面用户数"`
	RechargeAmount         float64     `json:"rechargeAmount"   dc:"充值金额"`
	DistributorId          int64       `json:"distributorId" dc:"渠道ID"`
	PromotionId            int64       `json:"promotionId" dc:"推广链id"`
	RoiDetail              string      `json:"roiDetail" dc:"ROI 明细，JSON 格式存储"`
	UserId                 uint64      `json:"userId" dc:"投手id"`
	CreatedAt              *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// FqAdAnalyzeDataDaySearchReq 分页请求参数
type FqAdAnalyzeDataDaySearchReq struct {
	comModel.PageReq
	Id                      string   `p:"id" dc:"id"`                                                       //id
	FqDistributorId         string   `p:"fqDistributorId" dc:"fq渠道ID"`                                      //渠道ID
	FqDistributorIds        []string `p:"fqDistributorIds"  dc:"fq渠道ID"`                                    //渠道ID
	FqChannelDistributorId  string   `p:"fqChannelDistributorId"   dc:"番茄渠道ID"`                             //渠道Id
	FqChannelDistributorIds []string `p:"fqChannelDistributorIds"   dc:"番茄渠道ID"`                            //渠道Id
	PromotionId             string   `p:"promotionId" v:"promotionId@integer#推广链id需为整数" dc:"推广链id"`         //推广链id
	StatDate                string   `p:"statDate" dc:"统计日期"`                                               //统计日期
	AddDesktopNum           string   `p:"addDesktopNum" v:"addDesktopNum@integer#新增桌面用户数需为整数" dc:"新增桌面用户数"` //新增桌面用户数
	RechargeNum             string   `p:"rechargeNum" v:"rechargeNum@integer#充值用户数需为整数" dc:"充值用户数"`         //充值用户数
	UserNum                 string   `p:"userNum" v:"userNum@integer#用户总数需为整数" dc:"用户总数"`                   //用户总数
	RoiDetail               string   `p:"roiDetail" dc:"ROI 明细，JSON 格式存储"`                                  //ROI 明细，JSON 格式存储
	StartTime               string   `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime                 string   `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	DistributorId           int      `p:"distributorId" dc:"分销id"`
	ChannelCode             string   `orm:"channel_code" p:"channelCode" dc:"渠道号"`
	ChannelCodes            []string `orm:"channel_codes" p:"channelCodes" dc:"渠道号"`
	PitcherId               int      `p:"pitcherId" dc:"投手id"`
	CreatedAt               string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// FqAdAnalyzeDataDaySearchRes 列表返回结果
type FqAdAnalyzeDataDaySearchRes struct {
	comModel.ListRes
	List    []*FqAdAnalyzeDataDayListRes `json:"list"`
	Summary FqAdAnalyzeDataSummary2
}

// FqAdAnalyzeDataDayAddReq 添加操作请求参数
type FqAdAnalyzeDataDayAddReq struct {
	DistributorId  int64  `p:"distributorId" v:"required#渠道ID不能为空" dc:"渠道ID"`
	PromotionId    string `p:"promotionId" v:"required#推广链id不能为空" dc:"推广链id"`
	StatDate       string `p:"statDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AddDesktopNum  int    `p:"addDesktopNum"  dc:"新增桌面用户数"`
	RechargeNum    int    `p:"rechargeNum"  dc:"充值用户数"`
	UserNum        int    `p:"userNum"  dc:"用户总数"`
	RechargeAmount int64  `p:"rechargeAmount"   dc:"充值金额"`
	RoiDetail      string `p:"roiDetail"  dc:"ROI 明细，JSON 格式存储"`
}

// FqAdAnalyzeDataDayEditReq 修改操作请求参数
type FqAdAnalyzeDataDayEditReq struct {
	Id            int    `p:"id" v:"required#主键ID不能为空" dc:"id"`
	DistributorId int64  `p:"distributorId" v:"required#渠道ID不能为空" dc:"渠道ID"`
	PromotionId   int64  `p:"promotionId" v:"required#推广链id不能为空" dc:"推广链id"`
	StatDate      string `p:"statDate" v:"required#统计日期不能为空" dc:"统计日期"`
	AddDesktopNum int    `p:"addDesktopNum"  dc:"新增桌面用户数"`
	RechargeNum   int    `p:"rechargeNum"  dc:"充值用户数"`
	UserNum       int    `p:"userNum"  dc:"用户总数"`
	RoiDetail     string `p:"roiDetail"  dc:"ROI 明细，JSON 格式存储"`
	//
	RechargeAmount int64 `p:"rechargeAmount"   dc:"充值金额"`
}
