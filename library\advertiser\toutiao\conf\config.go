package conf

import (
	"github.com/go-resty/resty/v2"
)

type contextKey string

func (c contextKey) String() string {
	return "ck " + string(c)
}

var (
	// ContextAccessToken takes a string access token as authentication for the request.
	ContextAccessToken = contextKey("accesstoken")
	// ContextEnableLog  takes a bool as a flag to enable request response log
	ContextEnableLog = contextKey("enable_log")
)

// Configuration stores the configuration of the API client
type Configuration struct {
	Host          string            `json:"host,omitempty"`
	Scheme        string            `json:"scheme,omitempty"`
	DefaultHeader map[string]string `json:"defaultHeader,omitempty"`
	UserAgent     string            `json:"userAgent,omitempty"`
	Debug         bool              `json:"debug,omitempty"`
	LogEnable     bool              `json:"log_enable,omitempty"`
	UseLogMw      bool              `json:"use_log_mw,omitempty"`
	HTTPClient    *resty.Client
}

// NewConfiguration returns a new Configuration object
func NewConfiguration() *Configuration {
	cfg := &Configuration{
		DefaultHeader: make(map[string]string),
		UserAgent:     "Bytedance Ads Openapi SDK",
		Host:          "api.oceanengine.com",
		Scheme:        "https",
		LogEnable:     false,
		UseLogMw:      true,
	}
	return cfg
}

// AddDefaultHeader adds a new HTTP header to the default header in the request
func (c *Configuration) AddDefaultHeader(key string, value string) {
	c.DefaultHeader[key] = value
}

func (c *Configuration) GetBasePath() string {
	return c.Scheme + "://" + c.Host
}
