/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30FilteringAuditStatus
type ToolsWechatAppletListV30FilteringAuditStatus string

// List of tools_wechat_applet_list_v3.0_filtering_audit_status
const (
	AUDIT_ACCEPTED_ToolsWechatAppletListV30FilteringAuditStatus ToolsWechatAppletListV30FilteringAuditStatus = "AUDIT_ACCEPTED"
	AUDITING_ToolsWechatAppletListV30FilteringAuditStatus       ToolsWechatAppletListV30FilteringAuditStatus = "AUDITING"
	AUDIT_REJECTED_ToolsWechatAppletListV30FilteringAuditStatus ToolsWechatAppletListV30FilteringAuditStatus = "AUDIT_REJECTED"
	ALL_ToolsWechatAppletListV30FilteringAuditStatus            ToolsWechatAppletListV30FilteringAuditStatus = "ALL"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_filtering_audit_status value
func (v ToolsWechatAppletListV30FilteringAuditStatus) Ptr() *ToolsWechatAppletListV30FilteringAuditStatus {
	return &v
}
