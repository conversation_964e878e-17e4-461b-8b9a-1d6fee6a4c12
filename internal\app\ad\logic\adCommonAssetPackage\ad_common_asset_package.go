// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-11 13:50:44
// 生成路径: internal/app/ad/logic/ad_common_asset_package.go
// 生成人：cq
// desc:通用资产-标题包
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdCommonAssetPackage(New())
}

func New() service.IAdCommonAssetPackage {
	return &sAdCommonAssetPackage{}
}

type sAdCommonAssetPackage struct{}

func (s *sAdCommonAssetPackage) List(ctx context.Context, req *model.AdCommonAssetPackageSearchReq) (listRes *model.AdCommonAssetPackageSearchRes, err error) {
	listRes = new(model.AdCommonAssetPackageSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdCommonAssetPackage.Ctx(ctx).As("ad").LeftJoin("sys_user u", "ad.user_id = u.id")
		if req.UserIds != nil && len(req.UserIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetPackage.Columns().UserId, req.UserIds)
		} else if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdCommonAssetPackage.Columns().UserId, userIds)
		}
		if req.PackageName != "" {
			m = m.Where("ad."+dao.AdCommonAssetPackage.Columns().PackageName+" like ?", "%"+req.PackageName+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdCommonAssetPackageListRes
		err = m.Fields("ad.id as id").
			Fields("ad.package_name as packageName").
			Fields("ad.title_ids as titleIds").
			Fields("ad.user_id as userId").
			Fields("u.user_name as userName").
			Fields("ad.last_3_day_click_rate as last3DayClickRate").
			Fields("ad.last_3_day_cost as last3DayCost").
			Fields("ad.history_click_rate as historyClickRate").
			Fields("ad.history_cost as historyCost").
			Fields("ad.ad_count as adCount").
			Fields("ad.created_at as createdAt").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdCommonAssetPackageListRes, len(res))
		for k, v := range res {
			assetPackage := &model.AdCommonAssetPackageListRes{
				Id:                v.Id,
				PackageName:       v.PackageName,
				TitleIds:          v.TitleIds,
				UserId:            v.UserId,
				UserName:          v.UserName,
				Last3DayClickRate: v.Last3DayClickRate,
				Last3DayCost:      v.Last3DayCost,
				HistoryClickRate:  v.HistoryClickRate,
				HistoryCost:       v.HistoryCost,
				AdCount:           v.AdCount,
				CreatedAt:         v.CreatedAt,
			}
			titleRes, _ := service.AdCommonAssetTitle().List(ctx, &model.AdCommonAssetTitleSearchReq{
				TitleIds: gconv.Ints(strings.Split(v.TitleIds, commonConsts.Delimiter)),
			})
			assetPackage.TitleList = titleRes.List
			assetPackage.TitleCount = len(titleRes.List)
			listRes.List[k] = assetPackage
		}
	})
	return
}

func (s *sAdCommonAssetPackage) GetExportData(ctx context.Context, req *model.AdCommonAssetPackageSearchReq) (listRes []*model.AdCommonAssetPackageListRes, err error) {
	res, _ := s.List(ctx, req)
	listRes = res.List
	return
}

func (s *sAdCommonAssetPackage) GetById(ctx context.Context, id int) (res *model.AdCommonAssetPackageInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdCommonAssetPackage.Ctx(ctx).WithAll().Where(dao.AdCommonAssetPackage.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdCommonAssetPackage) Add(ctx context.Context, req *model.AdCommonAssetPackageAddReq) (err error) {
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		err = g.Try(ctx, func(ctx context.Context) {
			user := sysService.Context().GetLoginUser(ctx)
			var titleIds = req.TitleIds
			if req.Titles != nil && len(req.Titles) > 0 {
				for _, title := range req.Titles {
					titleId, err1 := service.AdCommonAssetTitle().AddAndGetId(ctx, &model.AdCommonAssetTitleAddReq{
						Titles: []string{title},
						UserId: int(user.Id),
					})
					liberr.ErrIsNil(ctx, err1, "添加标题失败")
					titleIds = append(titleIds, gconv.Int(titleId))
				}
			}
			_, err = dao.AdCommonAssetPackage.Ctx(ctx).Insert(do.AdCommonAssetPackage{
				PackageName: req.PackageName,
				TitleIds:    strings.Join(gconv.Strings(titleIds), commonConsts.Delimiter),
				UserId:      user.Id,
			})
			liberr.ErrIsNil(ctx, err, "添加失败")
		})
		return err
	})
	return
}

func (s *sAdCommonAssetPackage) Edit(ctx context.Context, req *model.AdCommonAssetPackageEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		var titleIds = make([]int, 0)
		if req.Titles != nil && len(req.Titles) > 0 {
			for _, title := range req.Titles {
				titleId, err1 := service.AdCommonAssetTitle().AddAndGetId(ctx, &model.AdCommonAssetTitleAddReq{
					Titles: []string{title},
					UserId: int(user.Id),
				})
				liberr.ErrIsNil(ctx, err1, "添加标题失败")
				titleIds = append(titleIds, gconv.Int(titleId))
			}
		}
		titleIds = append(titleIds, req.TitleIds...)
		_, err = dao.AdCommonAssetPackage.Ctx(ctx).WherePri(req.Id).Update(do.AdCommonAssetPackage{
			PackageName: req.PackageName,
			TitleIds:    strings.Join(gconv.Strings(titleIds), commonConsts.Delimiter),
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdCommonAssetPackage) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdCommonAssetPackage.Ctx(ctx).Delete(dao.AdCommonAssetPackage.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
