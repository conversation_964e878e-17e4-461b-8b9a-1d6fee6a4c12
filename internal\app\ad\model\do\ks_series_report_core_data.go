// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-10 14:40:38
// 生成路径: internal/app/ad/model/entity/ks_series_report_core_data.go
// 生成人：cyao
// desc:短剧核心总览数据报表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsSeriesReportCoreData is the golang structure for table ks_series_report_core_data.
type KsSeriesReportCoreData struct {
	gmeta.Meta                       `orm:"table:ks_series_report_core_data, do:true"`
	Id                               interface{} `orm:"id,primary" json:"id"`                                                          // id自增
	AdvertiserId                     interface{} `orm:"advertiser_id" json:"advertiserId"`                                             // 账户ID
	SeriesId                         interface{} `orm:"series_id" json:"seriesId"`                                                     // 短剧id
	TotalCharge                      interface{} `orm:"total_charge" json:"totalCharge"`                                               // 消耗
	AccuFansUserNum                  interface{} `orm:"accu_fans_user_num" json:"accuFansUserNum"`                                     // 粉丝峰值量
	FansUserNum                      interface{} `orm:"fans_user_num" json:"fansUserNum"`                                              // 粉丝净增量
	EventPayRoi                      interface{} `orm:"event_pay_roi" json:"eventPayRoi"`                                              // 商业化ROI
	EventPayRoiAll                   interface{} `orm:"event_pay_roi_all" json:"eventPayRoiAll"`                                       // 全域ROI
	PayUserCount                     interface{} `orm:"pay_user_count" json:"payUserCount"`                                            // 付费人数
	PayCount                         interface{} `orm:"pay_count" json:"payCount"`                                                     // 付费订单数
	PayAmt                           interface{} `orm:"pay_amt" json:"payAmt"`                                                         // 付费金额
	IsFansUser                       interface{} `orm:"is_fans_user" json:"isFansUser"`                                                // 是否粉丝
	DisplayPlayCnt                   interface{} `orm:"display_play_cnt" json:"displayPlayCnt"`                                        // 播放数
	DisplayLikeCnt                   interface{} `orm:"display_like_cnt" json:"displayLikeCnt"`                                        // 点赞数
	DisplayCommentCnt                interface{} `orm:"display_comment_cnt" json:"displayCommentCnt"`                                  // 评论数
	DisplayCollectCnt                interface{} `orm:"display_collect_cnt" json:"displayCollectCnt"`                                  // 收藏数
	MiniGameIaaRoi                   interface{} `orm:"mini_game_iaa_roi" json:"miniGameIaaRoi"`                                       // IAA广告变现ROI（含返货）
	MiniGameIaaPurchaseAmount        interface{} `orm:"mini_game_iaa_purchase_amount" json:"miniGameIaaPurchaseAmount"`                // IAA广告变现LTV（含返货，元）
	MiniGameIaaPurchaseAmountDivided interface{} `orm:"mini_game_iaa_purchase_amount_divided" json:"miniGameIaaPurchaseAmountDivided"` // IAA广告变现LTV（不含返货，元）
	MiniGameIaaDividedRoi            interface{} `orm:"mini_game_iaa_divided_roi" json:"miniGameIaaDividedRoi"`                        // IAA广告变现ROI（不含返货）
	Date                             *gtime.Time `orm:"date" json:"date"`                                                              // 日期（yyyy-MM-dd hh:mm:ss）
}
