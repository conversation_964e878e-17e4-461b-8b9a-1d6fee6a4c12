// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-09 16:24:54
// 生成路径: api/v1/member/m_member_lock_info.go
// 生成人：lx
// desc:用户锁定信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberLockInfoSearchReq 分页请求参数
type MMemberLockInfoSearchReq struct {
	g.Meta `path:"/list" tags:"用户锁定信息" method:"get" summary:"用户锁定信息列表"`
	commonApi.Author
	model.MMemberLockInfoSearchReq
}

// MMemberLockInfoSearchRes 列表返回结果
type MMemberLockInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberLockInfoSearchRes
}

// MMemberLockInfoAddReq 添加操作请求参数
type MMemberLockInfoAddReq struct {
	g.Meta `path:"/add" tags:"用户锁定信息" method:"post" summary:"用户锁定信息添加"`
	commonApi.Author
	*model.MMemberLockInfoAddReq
}

// MMemberLockInfoAddRes 添加操作返回结果
type MMemberLockInfoAddRes struct {
	commonApi.EmptyRes
}

// MMemberLockInfoEditReq 修改操作请求参数
type MMemberLockInfoEditReq struct {
	g.Meta `path:"/edit" tags:"用户锁定信息" method:"put" summary:"用户锁定信息修改"`
	commonApi.Author
	*model.MMemberLockInfoEditReq
}

// MMemberLockInfoEditRes 修改操作返回结果
type MMemberLockInfoEditRes struct {
	commonApi.EmptyRes
}

// MMemberLockInfoGetReq 获取一条数据请求
type MMemberLockInfoGetReq struct {
	g.Meta `path:"/get" tags:"用户锁定信息" method:"get" summary:"获取用户锁定信息信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberLockInfoGetRes 获取一条数据结果
type MMemberLockInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberLockInfoInfoRes
}

// MMemberLockInfoDeleteReq 删除数据请求
type MMemberLockInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户锁定信息" method:"delete" summary:"删除用户锁定信息"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MMemberLockInfoDeleteRes 删除数据返回
type MMemberLockInfoDeleteRes struct {
	commonApi.EmptyRes
}

// MMemberLockReq 锁定用户
type MMemberLockReq struct {
	g.Meta `path:"/lock" tags:"用户锁定信息" method:"post" summary:"用户锁定"`
	commonApi.Author
	*model.MMemberLockReq
}
type MMemberUnLockReq struct {
	g.Meta `path:"/unLock" tags:"用户锁定信息" method:"post" summary:"用户解锁"`
	commonApi.Author
	*model.MMemberLockReq
}
