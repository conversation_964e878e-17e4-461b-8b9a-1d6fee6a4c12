// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: internal/app/ad/dao/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adPlanSettingDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adPlanSettingDao struct {
	*internal.AdPlanSettingDao
}

var (
	// AdPlanSetting is globally public accessible object for table tools_gen_table operations.
	AdPlanSetting = adPlanSettingDao{
		internal.NewAdPlanSettingDao(),
	}
)

// Fill with you ideas below.
