// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: internal/app/ad/dao/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdInfoDao struct {
	*internal.KsAdInfoDao
}

var (
	// KsAdInfo is globally public accessible object for table tools_gen_table operations.
	KsAdInfo = ksAdInfoDao{
		internal.NewKsAdInfoDao(),
	}
)

// Fill with you ideas below.
