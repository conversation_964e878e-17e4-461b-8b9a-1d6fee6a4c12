// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: internal/app/ad/logic/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAccountSeries(New())
}

func New() service.IKsAccountSeries {
	return &sKsAccountSeries{}
}

type sKsAccountSeries struct{}

func (s *sKsAccountSeries) List(ctx context.Context, req *model.KsAccountSeriesSearchReq) (listRes *model.KsAccountSeriesSearchRes, err error) {
	listRes = new(model.KsAccountSeriesSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAccountSeries.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAccountSeries.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.SeriesId > 0 {
			m = m.Where(dao.KsAccountSeries.Columns().SeriesId+" = ?", req.SeriesId)
		}
		if req.SeriesName != "" {
			m = m.Where(dao.KsAccountSeries.Columns().SeriesName+" like ?", "%"+req.SeriesName+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "advertiser_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.KsAccountSeriesListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAccountSeriesListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAccountSeriesListRes{
				AdvertiserId: v.AdvertiserId,
				SeriesId:     v.SeriesId,
				SeriesName:   v.SeriesName,
			}
		}
	})
	return
}

func (s *sKsAccountSeries) GetExportData(ctx context.Context, req *model.KsAccountSeriesSearchReq) (listRes []*model.KsAccountSeriesInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAccountSeries.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAccountSeries.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.SeriesId > 0 {
			m = m.Where(dao.KsAccountSeries.Columns().SeriesId+" = ?", req.SeriesId)
		}
		if req.SeriesName != "" {
			m = m.Where(dao.KsAccountSeries.Columns().SeriesName+" like ?", "%"+req.SeriesName+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "advertiser_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sKsAccountSeries) BatchAdd(ctx context.Context, req []*model.KsAccountSeriesAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req) == 0 {
			return
		}
		list := make([]do.KsAccountSeries, len(req))
		for i, item := range req {
			list[i] = do.KsAccountSeries{
				AdvertiserId: item.AdvertiserId,
				SeriesId:     item.SeriesId,
				SeriesName:   item.SeriesName,
			}
		}
		_, err = dao.KsAccountSeries.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAccountSeries) Add(ctx context.Context, req *model.KsAccountSeriesAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAccountSeries.Ctx(ctx).Insert(do.KsAccountSeries{
			SeriesId:     req.SeriesId,
			AdvertiserId: req.AdvertiserId,
			SeriesName:   req.SeriesName,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAccountSeries) Edit(ctx context.Context, req *model.KsAccountSeriesEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAccountSeries.Ctx(ctx).WherePri(req.AdvertiserId).Update(do.KsAccountSeries{
			SeriesId:     req.SeriesId,
			AdvertiserId: req.AdvertiserId,
			SeriesName:   req.SeriesName,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAccountSeries) Delete(ctx context.Context, advertiserIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAccountSeries.Ctx(ctx).Delete(dao.KsAccountSeries.Columns().AdvertiserId+" in (?)", advertiserIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetKsAdAccountSeriesList 拉取快手账户短剧列表
func (s *sKsAccountSeries) GetKsAdAccountSeriesList(ctx context.Context, accessToken string, aid int64, inDb bool) (res []api.SeriesInfoSnake, err error) {
	res = make([]api.SeriesInfoSnake, 0)
	err = g.Try(ctx, func(ctx context.Context) {
		if aid == 0 {
			err = errors.New("aid   不能为空")
			return
		}
		if len(accessToken) == 0 {
			accessToken = api.GetAccessTokenByAppIdCache(aid)
		}
		accountRes, err := api.GetKSApiClient().QuerySeriesInfoService.SetReq(api.QuerySeriesInfoReq{
			AdvertiserID: aid,
		}).AccessToken(accessToken).Do()

		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("------------- QuerySeriesInfoService err：%v --------------------", err))
		} else {
			res = accountRes.Data
			if !inDb {
				return
			}
			// 每次插入500条记录
			batchSize := 500
			var batch []*model.KsAccountSeriesAddReq
			for _, item := range accountRes.Data {
				req := &model.KsAccountSeriesAddReq{
					AdvertiserId: aid,
					SeriesId:     gconv.Int64(item.SeriesId),
					SeriesName:   item.SeriesName,
				}
				batch = append(batch, req)
				// 当达到500条时，执行一次批量插入，并重置批次
				if len(batch) >= batchSize {
					if err = s.BatchAdd(ctx, batch); err != nil {
						return
					}
					// 清空当前批次，准备下一批
					batch = batch[:0]
				}
			}
			// 如果还有剩余不足500条的记录，也执行一次插入
			if len(batch) > 0 {
				if err = s.BatchAdd(ctx, batch); err != nil {
					return
				}
			}
		}
	})
	return
}
