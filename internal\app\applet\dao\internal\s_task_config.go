// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-07-16 14:26:31
// 生成路径: internal/app/applet/dao/internal/s_task_config.go
// 生成人：cq
// desc:任务配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// STaskConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type STaskConfigDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns STaskConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// STaskConfigColumns defines and stores column names for table s_task_config.
type STaskConfigColumns struct {
	Id         string //
	AppId      string // 小程序ID
	Type       string // 任务类型 1：签到 2：每日解锁剧集 3：每日看视频 4：每日看剧 5：开启签到提醒 6：加止桌面
	TypeName   string // 任务类型名称
	RewardType string // 奖励类型 1：积分 2：金币
	RewardNum  string // 奖励数量
	TargetNum  string // 完成次数
	Sort       string // 排序
	Status     string // 状态 1：启用 2：禁用
	Remark     string // 备注
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

var sTaskConfigColumns = STaskConfigColumns{
	Id:         "id",
	AppId:      "app_id",
	Type:       "type",
	TypeName:   "type_name",
	RewardType: "reward_type",
	RewardNum:  "reward_num",
	TargetNum:  "target_num",
	Sort:       "sort",
	Status:     "status",
	Remark:     "remark",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewSTaskConfigDao creates and returns a new DAO object for table data access.
func NewSTaskConfigDao() *STaskConfigDao {
	return &STaskConfigDao{
		group:   "default",
		table:   "s_task_config",
		columns: sTaskConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *STaskConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *STaskConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *STaskConfigDao) Columns() STaskConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *STaskConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *STaskConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *STaskConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
