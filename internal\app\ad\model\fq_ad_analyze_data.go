// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: internal/app/ad/model/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdAnalyzeDataInfoRes is the golang structure for table fq_ad_analyze_data.
type FqAdAnalyzeDataInfoRes struct {
	gmeta.Meta        `orm:"table:fq_ad_analyze_data"`
	Id                int         `orm:"id,primary" json:"id" dc:"id"`                            // id
	DistributorId     int64       `orm:"distributor_id" json:"distributorId" dc:"fa渠道ID"`         // 渠道ID
	PromotionId       string      `orm:"promotion_id" json:"promotionId" dc:"推广链id"`              // 推广链id
	AddDesktopUserNum int64       `orm:"add_desktop_user_num" json:"addDesktopUserNum" dc:"加桌人数"` // 加桌人数
	PaidRate          float64     `orm:"paid_rate" json:"paidRate" dc:"付费率"`                      // 付费率
	PaidUserNum       int64       `orm:"paid_user_num" json:"paidUserNum" dc:"充值人数，时间-投放当日"`      // 充值人数，时间-投放当日
	RechargeAmount    int64       `orm:"recharge_amount" json:"rechargeAmount" dc:"累积充值，单位 分"`    // 累积充值，单位 分
	Uv                int64       `orm:"uv" json:"uv" dc:"激活人数"`                                  // 激活人数
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                   // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                   // 更新时间
	DeletedAt         *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                   // 删除时间
}

type FqAdAnalyzeDataListRes struct {
	Id                     int         `json:"id" dc:"id"`
	FqAccount              string      `json:"fqAccount" dc:"番茄账号"`
	FqChannel              string      `json:"fqChannel" dc:"渠道"`
	AppId                  string      `json:"appId" dc:"公众号/快应用id（分销平台id）"`
	AppName                string      `json:"appName" dc:"快应用/公众号名称"`
	FqChannelDistributorId string      `json:"fqChannelDistributorId" dc:"番茄渠道ID"`
	ChannelCode            string      `orm:"channel_code" json:"channelCode" dc:"渠道号"`
	DistributorName        string      `json:"distributorName" dc:"投手上级分销"`
	UserName               string      `json:"userName" dc:"投手名"`
	DistributorId          int64       `json:"distributorId" dc:"番茄渠道ID"`
	Uv                     int64       `json:"uv" dc:"激活人数"`
	PaidUserNum            int64       `json:"paidUserNum" dc:"充值人数，时间-投放当日"`
	AddDesktopUserNum      int64       `json:"addDesktopUserNum" dc:"加桌人数"`
	RechargeAmount         float64     `json:"rechargeAmount" dc:"累积充值，单位 分"`
	PaidRate               float64     `json:"paidRate" dc:"付费率"`
	PromotionId            string      `json:"promotionId" dc:"推广链id"`
	UserId                 uint64      `json:"userId" dc:"投手id"`
	CreatedAt              *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// FqAdAnalyzeDataSearchReq 分页请求参数
type FqAdAnalyzeDataSearchReq struct {
	comModel.PageReq
	Id                      string   `p:"id" dc:"id"`                                                             //id
	FqDistributorId         string   `p:"fqDistributorId"   dc:"番茄账号id"`                                          //渠道ID
	FqDistributorIds        []string `p:"fqDistributorIds"   dc:"番茄账号id"`                                         //渠道ID
	FqChannelDistributorId  string   `p:"fqChannelDistributorId"   dc:"番茄渠道ID"`                                   //渠道Id
	FqChannelDistributorIds []string `p:"fqChannelDistributorIds"   dc:"番茄渠道ID"`                                  //渠道Id
	PromotionId             string   `p:"promotionId"  dc:"推广链id"`                                                //推广链id
	AddDesktopUserNum       string   `p:"addDesktopUserNum" v:"addDesktopUserNum@integer#加桌人数需为整数" dc:"加桌人数"`     //加桌人数
	PaidRate                string   `p:"paidRate" v:"paidRate@float#付费率需为浮点数" dc:"付费率"`                          //付费率
	PaidUserNum             string   `p:"paidUserNum" v:"paidUserNum@integer#充值人数，时间-投放当日需为整数" dc:"充值人数，时间-投放当日"` //充值人数，时间-投放当日
	RechargeAmount          string   `p:"rechargeAmount" v:"rechargeAmount@integer#累积充值，单位 分需为整数" dc:"累积充值，单位 分"` //累积充值，单位 分
	Uv                      string   `p:"uv" v:"uv@integer#激活人数需为整数" dc:"激活人数"`                                   //激活人数
	CreatedAt               string   `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	DistributorId           int      `p:"distributorId" dc:"分销id"`
	ChannelCode             string   `orm:"channel_code" p:"channelCode" dc:"渠道号"`
	ChannelCodes            []string `orm:"channel_codes" p:"channelCodes" dc:"渠道号"`
	PitcherId               int      `p:"pitcherId" dc:"投手id"`
}

// FqAdAnalyzeDataSearchRes 列表返回结果
type FqAdAnalyzeDataSearchRes struct {
	comModel.ListRes
	List    []*FqAdAnalyzeDataListRes `json:"list"`
	Summary FqAdAnalyzeDataSummary    `json:"summary"`
}

type FqAdAnalyzeDataSummary struct {
	Uv                int64   `json:"uv" dc:"激活人数"`
	AddDesktopUserNum int64   `json:"addDesktopUserNum" dc:"加桌人数"`
	PaidUserNum       int64   `json:"paidUserNum" dc:"充值人数，时间-投放当日"`
	RechargeAmount    float64 `json:"rechargeAmount" dc:"累积充值，单位 分"`
}

type FqAdAnalyzeDataSummary2 struct {
	UserNum        int64   `json:"userNum" dc:"激活人数"`
	AddDesktopNum  int64   `json:"addDesktopNum" dc:"加桌人数"`
	RechargeNum    int64   `json:"rechargeNum" dc:"充值人数，时间-投放当日"`
	RechargeAmount float64 `json:"rechargeAmount" dc:"累积充值，单位 分"`
}

// FqAdAnalyzeDataAddReq 添加操作请求参数
type FqAdAnalyzeDataAddReq struct {
	DistributorId     int64   `p:"distributorId" v:"required#渠道ID不能为空" dc:"渠道ID"`
	PromotionId       string  `p:"promotionId" v:"required#推广链id不能为空" dc:"推广链id"`
	AddDesktopUserNum int64   `p:"addDesktopUserNum"  dc:"加桌人数"`
	PaidRate          float64 `p:"paidRate"  dc:"付费率"`
	PaidUserNum       int64   `p:"paidUserNum"  dc:"充值人数，时间-投放当日"`
	RechargeAmount    int64   `p:"rechargeAmount"  dc:"累积充值，单位 分"`
	Uv                int64   `p:"uv"  dc:"激活人数"`
}

// FqAdAnalyzeDataEditReq 修改操作请求参数
type FqAdAnalyzeDataEditReq struct {
	Id                int     `p:"id" v:"required#主键ID不能为空" dc:"id"`
	DistributorId     int64   `p:"distributorId" v:"required#渠道ID不能为空" dc:"渠道ID"`
	PromotionId       string  `p:"promotionId" v:"required#推广链id不能为空" dc:"推广链id"`
	AddDesktopUserNum int64   `p:"addDesktopUserNum"  dc:"加桌人数"`
	PaidRate          float64 `p:"paidRate"  dc:"付费率"`
	PaidUserNum       int64   `p:"paidUserNum"  dc:"充值人数，时间-投放当日"`
	RechargeAmount    int64   `p:"rechargeAmount"  dc:"累积充值，单位 分"`
	Uv                int64   `p:"uv"  dc:"激活人数"`
}
