/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// EventManagerEventsCreateV2ApiService 资产下创建事件
type EventManagerEventsCreateV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.EventManagerEventsCreateV2Request
}

func (r *EventManagerEventsCreateV2ApiService) SetCfg(cfg *conf.Configuration) *EventManagerEventsCreateV2ApiService {
	r.cfg = cfg
	return r
}

func (r *EventManagerEventsCreateV2ApiService) SetReq(req models.EventManagerEventsCreateV2Request) *EventManagerEventsCreateV2ApiService {
	r.Request = &req
	return r
}

func (r *EventManagerEventsCreateV2ApiService) AccessToken(accessToken string) *EventManagerEventsCreateV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *EventManagerEventsCreateV2ApiService) Do() (data *models.EventManagerEventsCreateV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/event_manager/events/create/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.EventManagerEventsCreateV2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.EventManagerEventsCreateV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/event_manager/events/create/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
