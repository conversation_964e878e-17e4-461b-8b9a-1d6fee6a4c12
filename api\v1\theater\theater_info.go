// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-25 11:55:58
// 生成路径: api/v1/theater/theater_info.go
// 生成人：gfast
// desc:剧集主表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterInfoSearchReq 分页请求参数
type TheaterInfoSearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广" method:"get" summary:"短剧详情列表（自动生产的作废）"`
	commonApi.Author
	model.TheaterInfoSearchReq
}

// TheaterInfoSearchRes 列表返回结果
type TheaterInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterInfoSearchRes
}

// TheaterInfoUploadToDouYinReq 上传到抖音商品库
type TheaterInfoUploadToDouYinReq struct {
	g.Meta `path:"/uploadToDouYin" tags:"短剧推广" method:"post" summary:"上传到抖音商品库"`
	commonApi.Author
	*model.TheaterInfoUploadToDouYinReq
}

type TheaterInfoUploadToDouYinRes struct {
	commonApi.EmptyRes
}

// TheaterInfoAddReq 添加操作请求参数
type TheaterInfoAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广" method:"post" summary:"短剧添加"`
	commonApi.Author
	*model.TheaterInfoAddReq
}

// TheaterInfoAddRes 添加操作返回结果
type TheaterInfoAddRes struct {
	commonApi.EmptyRes
}

// GetTheaterListReq 获取看剧列表 短剧推广 / 短剧中心 获取短剧列表
type GetTheaterListReq struct {
	g.Meta `path:"/getTheaterList" tags:"短剧推广" method:"get" summary:"短剧中心"`
	commonApi.Author
	*model.GetTheaterListReq
}

// GetTheaterListRes 列表返回结果
type GetTheaterListRes struct {
	g.Meta `mime:"application/json"`
	*model.GetTheaterListRes
}

// TheaterInfoEditReq 修改操作请求参数
type TheaterInfoEditReq struct {
	g.Meta `path:"/edit" tags:"短剧推广" method:"put" summary:"短剧列表（编辑操作）"`
	commonApi.Author
	*model.TheaterInfoEditReq
}

// TheaterInfoEditRes 修改操作返回结果
type TheaterInfoEditRes struct {
	commonApi.EmptyRes
}

// TheaterInfoGetReq 获取一条数据请求
type TheaterInfoGetReq struct {
	g.Meta `path:"/get" tags:"短剧推广" method:"get" summary:"编辑操作查询数据"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// TheaterInfoGetRes 获取一条数据结果
type TheaterInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterInfoInfoRes
}

// WarmUpReq 短剧推广 发送短剧
type WarmUpReq struct {
	g.Meta `path:"/sysDramaWarmUp" tags:"短剧推广" method:"post" summary:"发送短剧"`
	commonApi.Author
	ParentId uint `p:"parentId" v:"required#主键必须"` //通过主键获取
}

type WarmUpRes struct {
	commonApi.EmptyRes
}

// TheaterInfoExportXmlReq 导出xml请求参数
type TheaterInfoExportXmlReq struct {
	g.Meta `path:"/export/xml" tags:"短剧推广" method:"post" summary:"导出xml"`
	commonApi.Author
	*model.TheaterInfoExportXmlReq
}

// TheaterInfoExportXmlRes 导出xml数据结果
type TheaterInfoExportXmlRes struct {
	commonApi.EmptyRes
}

// TheaterInfoDeleteReq 删除数据请求
//type TheaterInfoDeleteReq struct {
//	g.Meta `path:"/delete" tags:"短剧推广" method:"delete" summary:"删除剧集"`
//	commonApi.Author
//	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
//}

// TheaterInfoDeleteRes 删除数据返回
//
//	type TheaterInfoDeleteRes struct {
//		commonApi.EmptyRes
//	}
//
// RetentionStatByParentIdReq  剧集相关的留存统计单个剧集
type RetentionStatByParentIdReq struct {
	g.Meta `path:"/retention/stat" tags:"用户看剧统计" method:"post" summary:"短剧剧集数据统计"`
	commonApi.Author
	*model.RetentionStatByParentIdReq
}

type RetentionStatRes struct {
	g.Meta `mime:"application/json"`
	*model.RetentionStatRes
}

type HourRetentionStatByParentIdReq struct {
	g.Meta `path:"/hour/retention/stat" tags:"用户看剧统计" method:"post" summary:"短剧剧集数据统计(Hour)按小时统计"`
	commonApi.Author
	*model.RetentionStatByParentIdReq
}

type HourRetentionStatRes struct {
	g.Meta `mime:"application/json"`
	*model.HourRetentionStatRes
}

type RetentionStatByPitcherReq struct {
	g.Meta `path:"/retention/pitcher/stat" tags:"分销统计" method:"post" summary:"投手播放数据统计"`
	commonApi.Author
	*model.RetentionStatByPitcherReq
}

type RetentionStatByPitcherRes struct {
	g.Meta `mime:"application/json"`
	*model.RetentionStatByPitcherRes
}

// RetentionStatByPitcherExportReq 导出请求
type RetentionStatByPitcherExportReq struct {
	g.Meta `path:"/retention/pitcher/export" tags:"分销统计" method:"post" summary:"投手播放数据统计导出"`
	commonApi.Author
	model.RetentionStatByPitcherReq
}

// PromotionalListExportRes 导出响应
type PromotionalListExportRes struct {
	commonApi.EmptyRes
}
type SyncTheaterRetentionStatReq struct {
	g.Meta `path:"/retention/stat/task" tags:"Task" method:"post" summary:"SyncTheaterRetentionStat"`
	commonApi.Author
	StatDate  string
	StartTime string
	BeforeDay bool
}
type GetTheaterRankReq struct {
	g.Meta `path:"/getTheaterRank" tags:"首页" method:"post" summary:"剧排行"`
	commonApi.Author
	*model.TheaterRankReq
}

type SendNewTheaterDataReportReq struct {
	g.Meta `path:"/sendNewTheaterDataReport" tags:"短剧推广" method:"post" summary:"新剧数据播报"`
	commonApi.Author
}

type SendOutsourcedTheaterDataReportReq struct {
	g.Meta `path:"/sendOutsourcedTheaterDataReport" tags:"短剧推广" method:"post" summary:"外采剧数据推送"`
	commonApi.Author
}
