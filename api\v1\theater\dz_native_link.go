// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-28 10:26:22
// 生成路径: api/v1/theater/dz_native_link.go
// 生成人：cq
// desc:点众原生链接相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// DzNativeLinkSearchReq 分页请求参数
type DzNativeLinkSearchReq struct {
	g.Meta `path:"/list" tags:"点众原生链接" method:"get" summary:"点众原生链接列表"`
	commonApi.Author
	model.DzNativeLinkSearchReq
}

// DzNativeLinkSearchRes 列表返回结果
type DzNativeLinkSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzNativeLinkSearchRes
}

// DzNativeLinkAddReq 添加操作请求参数
type DzNativeLinkAddReq struct {
	g.Meta `path:"/add" tags:"点众原生链接" method:"post" summary:"点众原生链接添加"`
	commonApi.Author
	*model.DzNativeLinkAddReq
}

// DzNativeLinkAddRes 添加操作返回结果
type DzNativeLinkAddRes struct {
	commonApi.EmptyRes
}

// DzNativeLinkEditReq 修改操作请求参数
type DzNativeLinkEditReq struct {
	g.Meta `path:"/edit" tags:"点众原生链接" method:"put" summary:"点众原生链接修改"`
	commonApi.Author
	*model.DzNativeLinkEditReq
}

// DzNativeLinkEditRes 修改操作返回结果
type DzNativeLinkEditRes struct {
	commonApi.EmptyRes
}

// DzNativeLinkGetReq 获取一条数据请求
type DzNativeLinkGetReq struct {
	g.Meta `path:"/get" tags:"点众原生链接" method:"get" summary:"获取点众原生链接信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// DzNativeLinkGetRes 获取一条数据结果
type DzNativeLinkGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzNativeLinkInfoRes
}

// DzNativeLinkDeleteReq 删除数据请求
type DzNativeLinkDeleteReq struct {
	g.Meta `path:"/delete" tags:"点众原生链接" method:"delete" summary:"删除点众原生链接"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// DzNativeLinkDeleteRes 删除数据返回
type DzNativeLinkDeleteRes struct {
	commonApi.EmptyRes
}

// DzNativeLinkCrawlReq 点众原生链接爬取请求
type DzNativeLinkCrawlReq struct {
	g.Meta `path:"/crawl" tags:"点众原生链接" method:"post" summary:"点众原生链接爬取"`
	commonApi.Author
}

// DzNativeLinkCrawlRes 点众原生链接爬取返回
type DzNativeLinkCrawlRes struct {
	commonApi.EmptyRes
}
