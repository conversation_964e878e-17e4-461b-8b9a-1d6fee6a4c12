// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/model/entity/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAppConfig is the golang structure for table ad_app_config.
type AdAppConfig struct {
	gmeta.Meta `orm:"table:ad_app_config"`
	Id         int         `orm:"id,primary" json:"id"`        // ID
	AppId      string      `orm:"app_id" json:"appId"`         //
	Secret     string      `orm:"secret" json:"secret"`        //
	Type       int         `orm:"type" json:"type"`            // 应用类型：1：巨量 2：广点通
	AuthNums   int         `orm:"auth_nums" json:"authNums"`   // 已授权账号数
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt"` // 删除时间
}
