// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-07 15:30:59
// 生成路径: api/v1/applet/s_customer_qr_code_config.go
// 生成人：cyao
// desc:微信二维码配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SCustomerQrCodeConfigSearchReq 分页请求参数
type SCustomerQrCodeConfigSearchReq struct {
	g.Meta `path:"/list" tags:"小程序设置" method:"get" summary:"企微二维码配置列表"`
	commonApi.Author
	model.SCustomerQrCodeConfigSearchReq
}

// SCustomerQrCodeConfigSearchRes 列表返回结果
type SCustomerQrCodeConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SCustomerQrCodeConfigSearchRes
}

// SCustomerQrCodeConfigAddReq 添加操作请求参数
type SCustomerQrCodeConfigAddReq struct {
	g.Meta `path:"/add" tags:"小程序设置" method:"post" summary:"企微二维码配置添加"`
	commonApi.Author
	*model.SCustomerQrCodeConfigAddReq
}

// SCustomerQrCodeConfigAddRes 添加操作返回结果
type SCustomerQrCodeConfigAddRes struct {
	commonApi.EmptyRes
}

// SCustomerQrCodeConfigEditReq 修改操作请求参数
type SCustomerQrCodeConfigEditReq struct {
	g.Meta `path:"/edit" tags:"小程序设置" method:"put" summary:"企微二维码修改"`
	commonApi.Author
	*model.SCustomerQrCodeConfigEditReq
}

// SCustomerQrCodeConfigEditRes 修改操作返回结果
type SCustomerQrCodeConfigEditRes struct {
	commonApi.EmptyRes
}

// SCustomerQrCodeConfigGetReq 获取一条数据请求
type SCustomerQrCodeConfigGetReq struct {
	g.Meta `path:"/get" tags:"小程序设置" method:"get" summary:"企微二维码配置 列表"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SCustomerQrCodeConfigGetRes 获取一条数据结果
type SCustomerQrCodeConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SCustomerQrCodeConfigInfoRes
}

// SCustomerQrCodeConfigDeleteReq 删除数据请求
type SCustomerQrCodeConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"小程序设置" method:"post" summary:"删除 企微二维码配置"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SCustomerQrCodeConfigDeleteRes 删除数据返回
type SCustomerQrCodeConfigDeleteRes struct {
	commonApi.EmptyRes
}
