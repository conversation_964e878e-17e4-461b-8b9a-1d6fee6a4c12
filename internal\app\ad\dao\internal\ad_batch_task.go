// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/dao/internal/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdBatchTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdBatchTaskDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns AdBatchTaskColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdBatchTaskColumns defines and stores column names for table ad_batch_task.
type AdBatchTaskColumns struct {
	Id         string //
	TaskId     string // 任务ID
	TaskName   string // 任务名称
	MediaType  string // 媒体类型 1：巨量
	OptObject  string // 操作对象类型 1：账户 2：项目 3：广告
	OptType    string // 操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像
	OptNum     string // 操作数量
	OptStatus  string // 执行状态：EXECUTING：执行中  COMPLETED：执行完成
	SuccessNum string // 成功数量
	FailNum    string // 失败数量
	UserId     string // 归属人员
	CreatedAt  string // 创建时间
	UpdatedAt  string // 更新时间
	DeletedAt  string // 删除时间
}

var adBatchTaskColumns = AdBatchTaskColumns{
	Id:         "id",
	TaskId:     "task_id",
	TaskName:   "task_name",
	MediaType:  "media_type",
	OptObject:  "opt_object",
	OptType:    "opt_type",
	OptNum:     "opt_num",
	OptStatus:  "opt_status",
	SuccessNum: "success_num",
	FailNum:    "fail_num",
	UserId:     "user_id",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	DeletedAt:  "deleted_at",
}

// NewAdBatchTaskDao creates and returns a new DAO object for table data access.
func NewAdBatchTaskDao() *AdBatchTaskDao {
	return &AdBatchTaskDao{
		group:   "default",
		table:   "ad_batch_task",
		columns: adBatchTaskColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdBatchTaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdBatchTaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdBatchTaskDao) Columns() AdBatchTaskColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdBatchTaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdBatchTaskDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdBatchTaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
