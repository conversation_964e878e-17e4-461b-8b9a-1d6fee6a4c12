/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
)

// 获取巨量app AccessToken
type GetOauth2AppAccessTokenApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *GetOauth2AppAccessTokenRequest
}

type GetOauth2AppAccessTokenRequest struct {
	//
	AppId int64 `json:"app_id,omitempty"`
	//
	Secret string `json:"secret"`
}

type GetOauth2AppAccessTokenResponse struct {
	//
	Code *int64                    `json:"code,omitempty"`
	Data *GetOauth2AppAccessTokenData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

type GetOauth2AppAccessTokenData struct {
	//access_token
	AccessToken string `json:"access_token,omitempty"`
	ExpiresIn   int64  `json:"expires_in,omitempty"`
}

func (r *GetOauth2AppAccessTokenApiService) SetCfg(cfg *conf.Configuration) *GetOauth2AppAccessTokenApiService {
	r.cfg = cfg
	return r
}

func (r *GetOauth2AppAccessTokenApiService) Oauth2AppAccessTokenRequest(Oauth2AppAccessTokenRequest GetOauth2AppAccessTokenRequest) *GetOauth2AppAccessTokenApiService {
	r.Request = &Oauth2AppAccessTokenRequest
	return r
}

func (r *GetOauth2AppAccessTokenApiService) AccessToken(accessToken string) *GetOauth2AppAccessTokenApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *GetOauth2AppAccessTokenApiService) Do() (data *GetOauth2AppAccessTokenResponse, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/oauth2/app_access_token/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(r.Request).
		SetResult(&GetOauth2AppAccessTokenResponse{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(GetOauth2AppAccessTokenResponse)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/oauth2/app_access_token/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
