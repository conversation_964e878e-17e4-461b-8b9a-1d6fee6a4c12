// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-06-28 16:12:55
// 生成路径: api/v1/theater/theater_play_recharge_stat.go
// 生成人：lx
// desc:剧播放充值统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterPlayRechargeStatSearchReq 分页请求参数
type TheaterPlayRechargeStatSearchReq struct {
	g.Meta `path:"/list" tags:"剧播放充值统计" method:"get" summary:"剧播放充值统计列表"`
	commonApi.Author
	model.TheaterPlayRechargeStatSearchReq
}

// TheaterPlayRechargeStatSearchRes 列表返回结果
type TheaterPlayRechargeStatSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterPlayRechargeStatSearchRes
}

// TheaterPlayRechargeStatAddReq 添加操作请求参数
type TheaterPlayRechargeStatAddReq struct {
	g.Meta `path:"/add" tags:"剧播放充值统计" method:"post" summary:"剧播放充值统计添加"`
	commonApi.Author
	*model.TheaterPlayRechargeStatAddReq
}

// TheaterPlayRechargeStatAddRes 添加操作返回结果
type TheaterPlayRechargeStatAddRes struct {
	commonApi.EmptyRes
}

// TheaterPlayRechargeStatEditReq 修改操作请求参数
type TheaterPlayRechargeStatEditReq struct {
	g.Meta `path:"/edit" tags:"剧播放充值统计" method:"put" summary:"剧播放充值统计修改"`
	commonApi.Author
	*model.TheaterPlayRechargeStatEditReq
}

// TheaterPlayRechargeStatEditRes 修改操作返回结果
type TheaterPlayRechargeStatEditRes struct {
	commonApi.EmptyRes
}

// TheaterPlayRechargeStatGetReq 获取一条数据请求
type TheaterPlayRechargeStatGetReq struct {
	g.Meta `path:"/get" tags:"剧播放充值统计" method:"get" summary:"获取剧播放充值统计信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// TheaterPlayRechargeStatGetRes 获取一条数据结果
type TheaterPlayRechargeStatGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterPlayRechargeStatInfoRes
}

// TheaterPlayRechargeStatDeleteReq 删除数据请求
type TheaterPlayRechargeStatDeleteReq struct {
	g.Meta `path:"/delete" tags:"剧播放充值统计" method:"delete" summary:"删除剧播放充值统计"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// TheaterPlayRechargeStatDeleteRes 删除数据返回
type TheaterPlayRechargeStatDeleteRes struct {
	commonApi.EmptyRes
}
