// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: internal/app/adx/model/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxMediaInfoRes is the golang structure for table adx_media.
type AdxMediaInfoRes struct {
	gmeta.Meta `orm:"table:adx_media"`
	Id         uint   `orm:"id,primary" json:"id" dc:"媒体ID"`        // 媒体ID
	LogoUrl    string `orm:"logo_url" json:"logoUrl" dc:"媒体logo"`   // 媒体logo
	MediaName  string `orm:"media_name" json:"mediaName" dc:"媒体名称"` // 媒体名称
}

type AdxMediaListRes struct {
	Id        uint   `json:"id" dc:"媒体ID"`
	LogoUrl   string `json:"logoUrl" dc:"媒体logo"`
	MediaName string `json:"mediaName" dc:"媒体名称"`
}

// AdxMediaSearchReq 分页请求参数
type AdxMediaSearchReq struct {
	comModel.PageReq
	Id        string `p:"id" dc:"媒体ID"`        //媒体ID
	LogoUrl   string `p:"logoUrl" dc:"媒体logo"` //媒体logo
	MediaName string `p:"mediaName" dc:"媒体名称"` //媒体名称
}

// AdxMediaSearchRes 列表返回结果
type AdxMediaSearchRes struct {
	comModel.ListRes
	List []*AdxMediaListRes `json:"list"`
}

// AdxMediaAddReq 添加操作请求参数
type AdxMediaAddReq struct {
	Id        uint   `p:"id" v:"required#主键ID不能为空" dc:"媒体ID"`
	LogoUrl   string `p:"logoUrl"  dc:"媒体logo"`
	MediaName string `p:"mediaName" v:"required#媒体名称不能为空" dc:"媒体名称"`
}

// AdxMediaEditReq 修改操作请求参数
type AdxMediaEditReq struct {
	Id        uint   `p:"id" v:"required#主键ID不能为空" dc:"媒体ID"`
	LogoUrl   string `p:"logoUrl"  dc:"媒体logo"`
	MediaName string `p:"mediaName" v:"required#媒体名称不能为空" dc:"媒体名称"`
}
