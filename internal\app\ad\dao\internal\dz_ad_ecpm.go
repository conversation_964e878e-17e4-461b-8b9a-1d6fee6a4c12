// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/dao/internal/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdEcpmDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdEcpmDao struct {
	table   string          // Table is the underlying table name of the DAO.
	group   string          // Group is the database configuration group name of current DAO.
	columns DzAdEcpmColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdEcpmColumns defines and stores column names for table dz_ad_ecpm.
type DzAdEcpmColumns struct {
	Id          string // 主键ID
	Time        string // 请求时间戳，单位毫秒
	UserId      string // 用户ID
	ChannelId   string // 渠道ID
	AppId       string // 小程序ID
	PromotionId string // 推广链路ID
	OpenId      string // openID
	EcpmId      string // ecpm接口ID
	EcpmCost    string // ecpm接口成本，单位十万分之元
	AdType      string // 广告类型，如激励视频广告/插屏广告等
	EventTime   string // ecpm接口事件时间，单位秒
	DyeTime     string // 用户染色时间戳，单位秒
	CreatedAt   string // 创建时间
}

var dzAdEcpmColumns = DzAdEcpmColumns{
	Id:          "id",
	Time:        "time",
	UserId:      "user_id",
	ChannelId:   "channel_id",
	AppId:       "app_id",
	PromotionId: "promotion_id",
	OpenId:      "open_id",
	EcpmId:      "ecpm_id",
	EcpmCost:    "ecpm_cost",
	AdType:      "ad_type",
	EventTime:   "event_time",
	DyeTime:     "dye_time",
	CreatedAt:   "created_at",
}

// NewDzAdEcpmDao creates and returns a new DAO object for table data access.
func NewDzAdEcpmDao() *DzAdEcpmDao {
	return &DzAdEcpmDao{
		group:   "default",
		table:   "dz_ad_ecpm",
		columns: dzAdEcpmColumns,
	}
}

func NewDzAdEcpmAnalyticDao() *DzAdEcpmDao {
	return &DzAdEcpmDao{
		group:   "analyticDB",
		table:   "dz_ad_ecpm",
		columns: dzAdEcpmColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdEcpmDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdEcpmDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdEcpmDao) Columns() DzAdEcpmColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdEcpmDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdEcpmDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdEcpmDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
