/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service
type CustomerCenterAdvertiserListV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *model.CustomerCenterAdvertiserListV2Request
}

func (r *CustomerCenterAdvertiserListV2ApiService) SetCfg(cfg *conf.Configuration) *CustomerCenterAdvertiserListV2ApiService {
	r.cfg = cfg
	return r
}

func (r *CustomerCenterAdvertiserListV2ApiService) CustomerCenterAdvertiserListV2Request(customerCenterAdvertiserListV2Request model.CustomerCenterAdvertiserListV2Request) *CustomerCenterAdvertiserListV2ApiService {
	r.Request = &customerCenterAdvertiserListV2Request
	return r
}

func (r *CustomerCenterAdvertiserListV2ApiService) AccessToken(accessToken string) *CustomerCenterAdvertiserListV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *CustomerCenterAdvertiserListV2ApiService) Do() (data *model.CustomerCenterAdvertiserListV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/customer_center/advertiser/list/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "cc_account_id", r.Request.CcAccountId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&model.CustomerCenterAdvertiserListV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(model.CustomerCenterAdvertiserListV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/customer_center/advertiser/list/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
