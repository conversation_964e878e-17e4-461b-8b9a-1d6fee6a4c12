// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: internal/app/ad/model/entity/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdAnalyzeData is the golang structure for table fq_ad_analyze_data.
type FqAdAnalyzeData struct {
	gmeta.Meta        `orm:"table:fq_ad_analyze_data"`
	Id                int         `orm:"id,primary" json:"id"`                          // id
	DistributorId     int64       `orm:"distributor_id" json:"distributorId"`           // 渠道ID
	PromotionId       string      `orm:"promotion_id" json:"promotionId"`               // 推广链id
	AddDesktopUserNum int64       `orm:"add_desktop_user_num" json:"addDesktopUserNum"` // 加桌人数
	PaidRate          float64     `orm:"paid_rate" json:"paidRate"`                     // 付费率
	PaidUserNum       int64       `orm:"paid_user_num" json:"paidUserNum"`              // 充值人数，时间-投放当日
	RechargeAmount    int64       `orm:"recharge_amount" json:"rechargeAmount"`         // 累积充值，单位 分
	Uv                int64       `orm:"uv" json:"uv"`                                  // 激活人数
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt"`                   // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt"`                   // 更新时间
	DeletedAt         *gtime.Time `orm:"deleted_at" json:"deletedAt"`                   // 删除时间
}
