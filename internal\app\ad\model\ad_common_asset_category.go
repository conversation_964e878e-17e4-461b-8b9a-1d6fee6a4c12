// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: internal/app/ad/model/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdCommonAssetCategoryInfoRes is the golang structure for table ad_common_asset_category.
type AdCommonAssetCategoryInfoRes struct {
	gmeta.Meta `orm:"table:ad_common_asset_category"`
	Id         int         `orm:"id,primary" json:"id" dc:""`            //
	Category   string      `orm:"category" json:"category" dc:"分类"`      // 分类
	UserId     int         `orm:"user_id" json:"userId" dc:"创建者"`        // 创建者
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"` // 删除时间
}

type AdCommonAssetCategoryListRes struct {
	Id        int         `json:"id" dc:""`
	Category  string      `json:"category" dc:"分类"`
	UserId    int         `json:"userId" dc:"创建者"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdCommonAssetCategorySearchReq 分页请求参数
type AdCommonAssetCategorySearchReq struct {
	comModel.PageReq
	Category    string `p:"category" dc:"分类"` //分类
	CategoryIds []int  `p:"categoryIds" dc:"分类ID列表"`
}

// AdCommonAssetCategorySearchRes 列表返回结果
type AdCommonAssetCategorySearchRes struct {
	comModel.ListRes
	List []*AdCommonAssetCategoryListRes `json:"list"`
}

// AdCommonAssetCategoryAddReq 添加操作请求参数
type AdCommonAssetCategoryAddReq struct {
	Category string `p:"category"  dc:"分类"`
}

// AdCommonAssetCategoryEditReq 修改操作请求参数
type AdCommonAssetCategoryEditReq struct {
	Id       int    `p:"id" v:"required#主键ID不能为空" dc:""`
	Category string `p:"category"  dc:"分类"`
}
