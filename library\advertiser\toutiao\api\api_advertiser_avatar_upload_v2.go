/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AdvertiserBudgetUpdateV2ApiService AdvertiserBudgetUpdateV2Api service
type AdvertiserAvatarUploadV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.AdvertiserAvatarUploadV2Request
}

func (r *AdvertiserAvatarUploadV2ApiService) SetCfg(cfg *conf.Configuration) *AdvertiserAvatarUploadV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AdvertiserAvatarUploadV2ApiService) AdvertiserAvatarUploadV2Request(advertiserAvatarUploadV2Request models.AdvertiserAvatarUploadV2Request) *AdvertiserAvatarUploadV2ApiService {
	r.Request = &advertiserAvatarUploadV2Request
	return r
}

func (r *AdvertiserAvatarUploadV2ApiService) AccessToken(accessToken string) *AdvertiserAvatarUploadV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AdvertiserAvatarUploadV2ApiService) Do() (data *models.AdvertiserAvatarUploadV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/advertiser/avatar/upload/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	request := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "multipart/form-data").
		SetHeader("Access-Token", r.token).
		SetMultipartFormData(localVarQueryParams).
		SetResult(&models.AdvertiserAvatarUploadV2Response{})
	var formFiles = make(map[string]*models.FormFileInfo)
	formFiles["image_file"] = r.Request.ImageFile
	request.SetFileReader("image_file", r.Request.ImageFile.FileName, bytes.NewReader(r.Request.ImageFile.FileBytes))
	response, err := request.Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AdvertiserAvatarUploadV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/avatar/upload/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
