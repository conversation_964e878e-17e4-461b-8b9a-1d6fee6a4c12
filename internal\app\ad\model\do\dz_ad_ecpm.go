// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/model/entity/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdEcpm is the golang structure for table dz_ad_ecpm.
type DzAdEcpm struct {
	gmeta.Meta  `orm:"table:dz_ad_ecpm, do:true"`
	Id          interface{} `orm:"id,primary" json:"id"`            // 主键ID
	Time        interface{} `orm:"time" json:"time"`                // 请求时间戳，单位毫秒
	UserId      interface{} `orm:"user_id" json:"userId"`           // 用户ID
	ChannelId   interface{} `orm:"channel_id" json:"channelId"`     // 渠道ID
	AppId       interface{} `orm:"app_id" json:"appId"`             // 小程序ID
	PromotionId interface{} `orm:"promotion_id" json:"promotionId"` // 推广链路ID
	OpenId      interface{} `orm:"open_id" json:"openId"`           // openID
	EcpmId      interface{} `orm:"ecpm_id" json:"ecpmId"`           // ecpm接口ID
	EcpmCost    interface{} `orm:"ecpm_cost" json:"ecpmCost"`       // ecpm接口成本，单位十万分之元
	AdType      interface{} `orm:"ad_type" json:"adType"`           // 广告类型，如激励视频广告/插屏广告等
	EventTime   interface{} `orm:"event_time" json:"eventTime"`     // ecpm接口事件时间，单位秒
	DyeTime     interface{} `orm:"dye_time" json:"dyeTime"`         // 用户染色时间戳，单位秒
	CreateDate  interface{} `orm:"create_date" json:"createDate"`   // 日期
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt"`     // 创建时间
}
