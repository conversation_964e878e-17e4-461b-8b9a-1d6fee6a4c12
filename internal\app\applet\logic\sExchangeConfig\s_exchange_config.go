// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-07-16 17:18:05
// 生成路径: internal/app/applet/logic/s_exchange_config.go
// 生成人：cq
// desc:兑换配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonEntity "github.com/tiger1103/gfast/v3/internal/app/common/model/entity"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterSExchangeConfig(New())
}

func New() service.ISExchangeConfig {
	return &sSExchangeConfig{}
}

type sSExchangeConfig struct{}

/*
List 查询兑换配置表列表
需求：
1、在不改变表结构的情况下，不同小程序的同一兑换且配置一样合并显示为一条数据，appId列显示多个小程序名称，id列取最小的一列
2、新增时每个小程序都新增一条记录
3、编辑时减少了小程序，删除对应的小程序配置，新增了小程序，新增一条记录，旧的配置直接更新
*/
func (s *sSExchangeConfig) List(ctx context.Context, req *model.SExchangeConfigSearchReq) (listRes *model.SExchangeConfigSearchRes, err error) {
	listRes = new(model.SExchangeConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SExchangeConfig.Ctx(ctx).WithAll()
		var res []*model.SExchangeConfigListRes
		err = m.Fields("type as type").
			Fields("vip_type as vipType").
			Fields("num as num").
			Fields("consume as consume").
			Fields("MIN(id) as id").
			Fields("GROUP_CONCAT(id) as idList").
			Fields("GROUP_CONCAT(app_id) as appId").
			Fields("ANY_VALUE(img_url) as imgUrl").
			Fields("ANY_VALUE(title) as title").
			Fields("ANY_VALUE(sort) as sort").
			Fields("ANY_VALUE(remark) as remark").
			Group("type", "vip_type", "num", "consume").
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取兑换配置失败")
		// 根据appIds条件过滤
		if req.AppIds != nil && len(req.AppIds) > 0 {
			filterRes := make([]*model.SExchangeConfigListRes, 0)
			for _, appId := range req.AppIds {
				for _, v := range res {
					if strings.Contains(v.AppId, appId) {
						filterRes = append(filterRes, v)
					}
				}
			}
			res = filterRes
		}
		// 根据type条件过滤
		if req.Type != "" {
			filterRes := make([]*model.SExchangeConfigListRes, 0)
			for _, v := range res {
				if v.Type == gconv.Int(req.Type) {
					filterRes = append(filterRes, v)
				}
			}
			res = filterRes
		}
		// 根据types条件过滤
		if req.Types != nil && len(req.Types) > 0 {
			filterRes := make([]*model.SExchangeConfigListRes, 0)
			for _, typeId := range req.Types {
				for _, v := range res {
					if v.Type == gconv.Int(typeId) {
						filterRes = append(filterRes, v)
					}
				}
			}
			res = filterRes
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		linq.From(res).Sort(func(i, j interface{}) bool {
			return i.(*model.SExchangeConfigListRes).Sort > j.(*model.SExchangeConfigListRes).Sort
		}).ToSlice(&res)
		pageRes := make([]*model.SExchangeConfigListRes, 0)
		linq.From(res).Skip(req.PageSize * (req.PageNum - 1)).Take(req.PageSize).ToSlice(&pageRes)
		listRes.List = make([]*model.SExchangeConfigListRes, len(pageRes))
		listRes.Total = len(res)

		appIds := make([]string, 0)
		linq.From(pageRes).SelectT(func(item *model.SExchangeConfigListRes) string {
			return item.AppId
		}).ToSlice(&appIds)
		appIdSet := gset.NewStrSet()
		for _, v := range appIds {
			splits := strings.Split(v, ",")
			appIdSet.Add(splits...)
		}
		// 获取小程序名称
		miniInfos := make([]*systemModel.GetMiniInfoListRes, 0)
		if appIdSet.Size() > 0 {
			miniInfos, err = sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIdSet.Slice())
			liberr.ErrIsNil(ctx, err, "获取小程序名称失败")
		}
		rewardTypeList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.ExchangeRewardType)
		liberr.ErrIsNil(ctx, err1, "获取兑换兑换字典数据失败")
		vipTypeList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.VipPayType)
		liberr.ErrIsNil(ctx, err1, "获取vip类型字典数据失败")
		for k, v := range res {
			config := &model.SExchangeConfigListRes{
				Id:      v.Id,
				IdList:  v.IdList,
				AppId:   v.AppId,
				ImgUrl:  v.ImgUrl,
				Title:   v.Title,
				Type:    v.Type,
				VipType: v.VipType,
				Num:     v.Num,
				Consume: v.Consume,
				Sort:    v.Sort,
				Remark:  v.Remark,
			}
			miniInfoList := make([]*systemModel.GetMiniInfoListRes, 0)
			linq.From(miniInfos).WhereT(func(item *systemModel.GetMiniInfoListRes) bool {
				return strings.Contains(v.AppId, item.AppId)
			}).ToSlice(&miniInfoList)
			appNames := make([]string, 0)
			linq.From(miniInfoList).SelectT(func(item *systemModel.GetMiniInfoListRes) string {
				return item.AppName
			}).ToSlice(&appNames)
			config.AppName = strings.Join(appNames, ",")
			config.AppIds = strings.Split(v.AppId, ",")
			ids := make([]int, 0)
			for _, id := range strings.Split(v.IdList, ",") {
				ids = append(ids, gconv.Int(id))
			}
			config.Ids = ids
			if rewardType, ok := linq.From(rewardTypeList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.Type)
			}).First().(*commonEntity.SysDictData); ok {
				config.TypeName = rewardType.DictLabel
			}
			if vipType, ok := linq.From(vipTypeList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.VipType)
			}).First().(*commonEntity.SysDictData); ok {
				config.VipTypeName = vipType.DictLabel
			}
			listRes.List[k] = config
		}
	})
	return
}

func (s *sSExchangeConfig) GetById(ctx context.Context, id int) (res *model.SExchangeConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SExchangeConfig.Ctx(ctx).WithAll().Where(dao.SExchangeConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSExchangeConfig) Add(ctx context.Context, req *model.SExchangeConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.AppIds == nil || len(req.AppIds) == 0 {
			req.AppIds = []string{""}
		}
		// 查询小程序是否存在配置
		existConfigs, err1 := s.GetByAppIds(ctx, req.AppIds, req.Type)
		liberr.ErrIsNil(ctx, err1, "查询小程序是否存在配置失败")
		if existConfigs != nil && len(existConfigs) > 0 {
			appIds := make([]string, 0)
			linq.From(existConfigs).SelectT(func(item *model.SExchangeConfigInfoRes) string {
				return item.AppId
			}).ToSlice(&appIds)
			err1 = errors.New(fmt.Sprintf("小程序ID: %v 已存在配置", strings.Join(appIds, ",")))
			liberr.ErrIsNil(ctx, err1)
		}
		ExchangeConfigs := make([]do.SExchangeConfig, 0)
		for _, appId := range req.AppIds {
			if appId == "all" {
				appId = ""
			}
			ExchangeConfig := do.SExchangeConfig{
				AppId:      appId,
				ImgUrl:     req.ImgUrl,
				Title:      req.Title,
				Type:       req.Type,
				VipType:    req.VipType,
				Num:        req.Num,
				Consume:    req.Consume,
				Sort:       req.Sort,
				Remark:     req.Remark,
				CreateTime: gtime.Now(),
				UpdateTime: gtime.Now(),
			}
			ExchangeConfigs = append(ExchangeConfigs, ExchangeConfig)
		}
		_, err = dao.SExchangeConfig.Ctx(ctx).Batch(len(ExchangeConfigs)).Insert(ExchangeConfigs)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSExchangeConfig) Edit(ctx context.Context, req *model.SExchangeConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 请求的配置列表
		existConfigs, err1 := s.GetByAppIds(ctx, req.AppIds, req.Type)
		// 旧的配置列表
		configs, err1 := s.GetByIds(ctx, req.Ids, req.Type)
		liberr.ErrIsNil(ctx, err1, "查询小程序是否存在配置失败")
		// 校验小程序是否存在配置
		existAppIds := make([]string, 0)
		for _, existConfig := range existConfigs {
			if !libUtils.FindTargetInt(req.Ids, existConfig.Id) {
				existAppIds = append(existAppIds, existConfig.AppId)
			}
		}
		if len(existAppIds) > 0 {
			miniInfos, _ := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, existAppIds)
			appNames := make([]string, 0)
			linq.From(miniInfos).SelectT(func(item *systemModel.GetMiniInfoListRes) string {
				return item.AppName
			}).ToSlice(&appNames)
			err2 := errors.New(fmt.Sprintf("小程序: %s 已存在配置", strings.Join(appNames, ",")))
			liberr.ErrIsNil(ctx, err2)
		}
		// 旧的配置更新
		oldConfigs := make([]*model.SExchangeConfigInfoRes, 0)
		// 新的配置插入
		newConfigs := make([]*model.SExchangeConfigInfoRes, 0)
		for _, appId := range req.AppIds {
			if config, ok := linq.From(configs).WhereT(func(item *model.SExchangeConfigInfoRes) bool {
				return item.AppId == appId
			}).First().(*model.SExchangeConfigInfoRes); ok {
				oldConfigs = append(oldConfigs, config)
			} else {
				if appId == "all" {
					appId = ""
				}
				newConfig := &model.SExchangeConfigInfoRes{
					AppId:      appId,
					ImgUrl:     req.ImgUrl,
					Title:      req.Title,
					Type:       req.Type,
					VipType:    req.VipType,
					Num:        req.Num,
					Consume:    req.Consume,
					Sort:       req.Sort,
					Remark:     req.Remark,
					CreateTime: gtime.Now(),
					UpdateTime: gtime.Now(),
				}
				newConfigs = append(newConfigs, newConfig)
			}
		}
		// 需要删除的ID列表 configs里面不在oldConfigs里面的
		deleteIds := make([]int, 0)
		oldIds := make([]int, 0)
		linq.From(oldConfigs).SelectT(func(item *model.SExchangeConfigInfoRes) int {
			return item.Id
		}).ToSlice(&oldIds)
		linq.From(configs).WhereT(func(item *model.SExchangeConfigInfoRes) bool {
			return !linq.From(oldIds).Contains(item.Id)
		}).SelectT(func(item *model.SExchangeConfigInfoRes) int {
			return item.Id
		}).ToSlice(&deleteIds)
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			err = g.Try(ctx, func(ctx context.Context) {
				for _, oldConfig := range oldConfigs {
					_, err = dao.SExchangeConfig.Ctx(ctx).WherePri(oldConfig.Id).Update(do.SExchangeConfig{
						AppId:      oldConfig.AppId,
						ImgUrl:     req.ImgUrl,
						Title:      req.Title,
						Type:       req.Type,
						VipType:    req.VipType,
						Num:        req.Num,
						Consume:    req.Consume,
						Sort:       req.Sort,
						Remark:     req.Remark,
						UpdateTime: gtime.Now(),
					})
				}
				if len(newConfigs) > 0 {
					_, err = dao.SExchangeConfig.Ctx(ctx).Save(newConfigs)
				}
				if len(deleteIds) > 0 {
					_, err = dao.SExchangeConfig.Ctx(ctx).Delete(dao.SExchangeConfig.Columns().Id+" in (?)", deleteIds)
				}
			})
			return err
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSExchangeConfig) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SExchangeConfig.Ctx(ctx).Delete(dao.SExchangeConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sSExchangeConfig) GetByAppIds(ctx context.Context, appIds []string, configType int) (res []*model.SExchangeConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if appIds[0] == "all" {
			appIds = []string{""}
		}
		err = dao.SExchangeConfig.Ctx(ctx).
			WhereIn(dao.SExchangeConfig.Columns().AppId, appIds).
			Where(dao.SExchangeConfig.Columns().Type, configType).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取兑换配置失败")
	})
	return
}

func (s *sSExchangeConfig) GetByIds(ctx context.Context, ids []int, configType int) (res []*model.SExchangeConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SExchangeConfig.Ctx(ctx).
			WhereIn(dao.SExchangeConfig.Columns().Id, ids).
			Where(dao.SExchangeConfig.Columns().Type, configType).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取兑换配置失败")
	})
	return
}
