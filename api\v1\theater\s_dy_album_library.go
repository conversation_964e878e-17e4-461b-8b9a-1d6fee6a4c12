// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-18 17:43:20
// 生成路径: api/v1/theater/s_dy_album_library.go
// 生成人：cq
// desc:抖音短剧库表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyAlbumLibrarySearchReq 分页请求参数
type SDyAlbumLibrarySearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"get" summary:"短剧列表"`
	commonApi.Author
	model.SDyAlbumLibrarySearchReq
}

// SDyAlbumLibrarySearchRes 列表返回结果
type SDyAlbumLibrarySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumLibrarySearchRes
}

// SDyAlbumLibraryAddReq 添加操作请求参数
type SDyAlbumLibraryAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"添加短剧"`
	commonApi.Author
	*model.SDyAlbumLibraryAddReq
}

// SDyAlbumLibraryAddRes 添加操作返回结果
type SDyAlbumLibraryAddRes struct {
	commonApi.EmptyRes
}

// SDyAlbumLibraryEditReq 修改操作请求参数
type SDyAlbumLibraryEditReq struct {
	g.Meta `path:"/edit" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"put" summary:"编辑短剧"`
	commonApi.Author
	*model.SDyAlbumLibraryEditReq
}

// SDyAlbumLibraryEditRes 修改操作返回结果
type SDyAlbumLibraryEditRes struct {
	commonApi.EmptyRes
}

// SDyAlbumLibraryReviewReq 短剧送审请求参数
type SDyAlbumLibraryReviewReq struct {
	g.Meta `path:"/review" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"put" summary:"短剧送审"`
	commonApi.Author
	AlbumId string `p:"albumId" dc:"短剧ID"`
}

// SDyAlbumLibraryReviewRes 短剧送审返回结果
type SDyAlbumLibraryReviewRes struct {
	commonApi.EmptyRes
}

// SDyAlbumLibraryOnlineReq 短剧上线请求参数
type SDyAlbumLibraryOnlineReq struct {
	g.Meta `path:"/online" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"put" summary:"短剧上线"`
	commonApi.Author
	AlbumId string `p:"albumId" dc:"短剧ID"`
}

// SDyAlbumLibraryOnlineRes 短剧上线返回结果
type SDyAlbumLibraryOnlineRes struct {
	commonApi.EmptyRes
}

// SDyAlbumLibraryGetReq 获取一条数据请求
type SDyAlbumLibraryGetReq struct {
	g.Meta `path:"/get" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"get" summary:"获取短剧信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SDyAlbumLibraryGetRes 获取一条数据结果
type SDyAlbumLibraryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumLibraryInfoRes
}

// SDyAlbumLibraryDeleteReq 删除数据请求
type SDyAlbumLibraryDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"删除短剧"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SDyAlbumLibraryDeleteRes 删除数据返回
type SDyAlbumLibraryDeleteRes struct {
	commonApi.EmptyRes
}

// SDyNotifyReq 抖音通知请求
type SDyNotifyReq struct {
	commonApi.Author
	g.Meta `path:"/dyNotify" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"抖音通知"`
	*model.SDyNotifyReq
}

// 获取抖音审核结果
type SDyAlbumGetReviewResultReq struct {
	g.Meta `path:"/getAlbumReviewResult" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"获取内容库审核结果"`
	commonApi.Author
	*model.SDyAlbumGetReviewResultReq
}

// UpdateAlbumDistributeReq 更新短剧可见性
type UpdateAlbumDistributeReq struct {
	g.Meta `path:"/update/distribute" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"更新短剧可见性"`
	commonApi.Author
	AlbumId string `p:"albumId" dc:"短剧ID"`
}

// UpdateTheaterDyInfoReq 更新短剧中心抖音剧集信息
type UpdateTheaterDyInfoReq struct {
	g.Meta `path:"/update/theater/dyInfo" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"更新短剧中心抖音剧集信息"`
	commonApi.Author
	AlbumId  string `p:"albumId" dc:"短剧ID"`
	ParentId int    `p:"parentId" dc:"剧集父id"`
}
