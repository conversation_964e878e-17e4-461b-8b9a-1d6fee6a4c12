// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:33:12
// 生成路径: api/v1/adx/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxCreativeSearchReq 分页请求参数
type AdxCreativeSearchReq struct {
	g.Meta `path:"/list" tags:"ADX短剧广告计划表" method:"post" summary:"ADX短剧广告计划表列表"`
	commonApi.Author
	model.AdxCreativeSearchReq
}

// AdxCreativeSearchRes 列表返回结果
type AdxCreativeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxCreativeSearchRes
}

// AdxCreativeExportReq 导出请求
type AdxCreativeExportReq struct {
	g.Meta `path:"/export" tags:"ADX短剧广告计划表" method:"post" summary:"ADX短剧广告计划表导出"`
	commonApi.Author
	model.AdxCreativeSearchReq
}

// AdxCreativeExportRes 导出响应
type AdxCreativeExportRes struct {
	commonApi.EmptyRes
}

// AdxCreativeAddReq 添加操作请求参数
type AdxCreativeAddReq struct {
	g.Meta `path:"/add" tags:"ADX短剧广告计划表" method:"post" summary:"ADX短剧广告计划表添加"`
	commonApi.Author
	*model.AdxCreativeAddReq
}

// AdxCreativeAddRes 添加操作返回结果
type AdxCreativeAddRes struct {
	commonApi.EmptyRes
}

// AdxCreativeEditReq 修改操作请求参数
type AdxCreativeEditReq struct {
	g.Meta `path:"/edit" tags:"ADX短剧广告计划表" method:"put" summary:"ADX短剧广告计划表修改"`
	commonApi.Author
	*model.AdxCreativeEditReq
}

// AdxCreativeEditRes 修改操作返回结果
type AdxCreativeEditRes struct {
	commonApi.EmptyRes
}

// AdxCreativeGetReq 获取一条数据请求
type AdxCreativeGetReq struct {
	g.Meta `path:"/get" tags:"ADX短剧广告计划表" method:"get" summary:"获取ADX短剧广告计划表信息"`
	commonApi.Author
	CreativeId uint64 `p:"creativeId" v:"required#主键必须"` //通过主键获取
}

// AdxCreativeGetRes 获取一条数据结果
type AdxCreativeGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxCreativeInfoRes
}

// AdxCreativeDeleteReq 删除数据请求
type AdxCreativeDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX短剧广告计划表" method:"post" summary:"删除ADX短剧广告计划表"`
	commonApi.Author
	CreativeIds []uint64 `p:"creativeIds" v:"required#主键必须"` //通过主键删除
}

// AdxCreativeDeleteRes 删除数据返回
type AdxCreativeDeleteRes struct {
	commonApi.EmptyRes
}

// AdxCreativeSyncReq 同步ADX广告计划请求参数
type AdxCreativeSyncReq struct {
	g.Meta `path:"/sync" tags:"ADX短剧广告计划表" method:"post" summary:"同步ADX广告计划"`
	commonApi.Author
	model.AdxCreativeSearchReq
}

// AdxCreativeSyncRes 同步ADX广告计划返回结果
type AdxCreativeSyncRes struct {
	g.Meta `mime:"application/json"`
}
