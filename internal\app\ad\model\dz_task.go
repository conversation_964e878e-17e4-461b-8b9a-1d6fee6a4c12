// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: internal/app/ad/model/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzTaskInfoRes is the golang structure for table dz_task.
type DzTaskInfoRes struct {
	gmeta.Meta `orm:"table:dz_task"`
	TaskId     string `orm:"task_id,primary" json:"taskId" dc:"任务id"` // 任务id
	QueryUrl   string `orm:"query_url" json:"queryUrl" dc:"查询url"`    // 查询url
	QueryJson  string `orm:"query_json" json:"queryJson" dc:"查询参数"`   // 查询参数
	// status
	Status    int         `orm:"status" json:"status" dc:"状态"`
	Xurl      string      `orm:"xurl" json:"xurl" dc:"xurl"`
	CreatedAt *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
}

type DzTaskListRes struct {
	TaskId    string      `json:"taskId" dc:"任务id"`
	QueryUrl  string      `json:"queryUrl" dc:"查询url"`
	QueryJson string      `json:"queryJson" dc:"查询参数"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	Status    int         `json:"status" dc:"状态"`
	Xurl      string      `json:"xurl" dc:"xurl"`
}

// DzTaskSearchReq 分页请求参数
type DzTaskSearchReq struct {
	comModel.PageReq
	TaskId    string `p:"taskId" dc:"任务id"`                                                       //任务id
	QueryUrl  string `p:"queryUrl" dc:"查询url"`                                                    //查询url
	QueryJson string `p:"queryJson" dc:"查询参数"`                                                    //查询参数
	CreatedAt string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// DzTaskSearchRes 列表返回结果
type DzTaskSearchRes struct {
	comModel.ListRes
	List []*DzTaskListRes `json:"list"`
}

// DzTaskAddReq 添加操作请求参数
type DzTaskAddReq struct {
	TaskId    string `p:"taskId" v:"required#主键ID不能为空" dc:"任务id"`
	QueryUrl  string `p:"queryUrl" v:"required#查询url不能为空" dc:"查询url"`
	QueryJson string `p:"queryJson" v:"required#查询参数不能为空" dc:"查询参数"`
	Xurl      string `p:"xurl"   dc:"Xurl"`
	Status    int    `p:"status"    dc:"状态"`
}

// DzTaskEditReq 修改操作请求参数
type DzTaskEditReq struct {
	TaskId    string `p:"taskId" v:"required#主键ID不能为空" dc:"任务id"`
	QueryUrl  string `p:"queryUrl" v:"required#查询url不能为空" dc:"查询url"`
	QueryJson string `p:"queryJson" v:"required#查询参数不能为空" dc:"查询参数"`
	Xurl      string `p:"xurl"   dc:"Xurl"`
	Status    int    `p:"status"    dc:"状态"`
}
