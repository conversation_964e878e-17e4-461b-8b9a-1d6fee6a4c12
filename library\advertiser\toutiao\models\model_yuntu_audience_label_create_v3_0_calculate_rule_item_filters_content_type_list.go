/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList
type YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList int64

// List of yuntu_audience_label_create_v3.0_calculate_rule_item_filters_content_type_list
const (
	Enum_3_YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList = 3
	Enum_4_YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList = 4
	Enum_5_YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList <PERSON><PERSON><PERSON>udienceLabelCreateV30CalculateRuleItemFiltersContentTypeList = 5
)

// Ptr returns reference to yuntu_audience_label_create_v3.0_calculate_rule_item_filters_content_type_list value
func (v YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList) Ptr() *YuntuAudienceLabelCreateV30CalculateRuleItemFiltersContentTypeList {
	return &v
}
