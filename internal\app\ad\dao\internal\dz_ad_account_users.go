// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:33
// 生成路径: internal/app/ad/dao/internal/dz_ad_account_users.go
// 生成人：cyao
// desc:权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdAccountUsersDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdAccountUsersDao struct {
	table   string                  // Table is the underlying table name of the DAO.
	group   string                  // Group is the database configuration group name of current DAO.
	columns DzAdAccountUsersColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdAccountUsersColumns defines and stores column names for table dz_ad_account_users.
type DzAdAccountUsersColumns struct {
	ChannelId     string // 渠道id
	SpecifyUserId string // 用户ID
}

var dzAdAccountUsersColumns = DzAdAccountUsersColumns{
	ChannelId:     "channel_id",
	SpecifyUserId: "specify_user_id",
}

// NewDzAdAccountUsersDao creates and returns a new DAO object for table data access.
func NewDzAdAccountUsersDao() *DzAdAccountUsersDao {
	return &DzAdAccountUsersDao{
		group:   "default",
		table:   "dz_ad_account_users",
		columns: dzAdAccountUsersColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdAccountUsersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdAccountUsersDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdAccountUsersDao) Columns() DzAdAccountUsersColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdAccountUsersDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdAccountUsersDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdAccountUsersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
