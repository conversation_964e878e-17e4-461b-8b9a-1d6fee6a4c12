// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:33:37
// 生成路径: internal/app/adx/service/adx_playlet.go
// 生成人：cq
// desc:ADX短剧基本信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxPlaylet interface {
	List(ctx context.Context, req *model.AdxPlayletSearchReq) (res *model.AdxPlayletSearchRes, err error)
	PullAllData(ctx context.Context) (err error)
	PullIncrementalData(ctx context.Context, date string) (err error)
	GetExportData(ctx context.Context, req *model.AdxPlayletSearchReq) (listRes []*model.AdxPlayletInfoRes, err error)
	GetByPlayletId(ctx context.Context, PlayletId uint64) (res *model.AdxPlayletInfoRes, err error)
	GetByName(ctx context.Context, name string, likeQuery bool) (res []*model.AdxPlayletInfoRes, err error)
	GetByPlayletIds(ctx context.Context, playletIds []int64) (res []*model.AdxPlayletDetialRes, err error)
	GetDetail(ctx context.Context, playletId uint64) (res *model.AdxPlayletDetialRes, err error)
	GetDetail2(ctx context.Context, playletId uint64) (res *model.AdxPlayletDetialRes, err error)
	GetDetail3(ctx context.Context, playletId uint64) (res *model.AdxPlayletDetialRes, err error)
	GetDetail3Timer(ctx context.Context) (err error)
	Add(ctx context.Context, req *model.AdxPlayletAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxPlayletEditReq) (err error)
	Delete(ctx context.Context, PlayletId []uint64) (err error)
}

var localAdxPlaylet IAdxPlaylet

func AdxPlaylet() IAdxPlaylet {
	if localAdxPlaylet == nil {
		panic("implement not found for interface IAdxPlaylet, forgot register?")
	}
	return localAdxPlaylet
}

func RegisterAdxPlaylet(i IAdxPlaylet) {
	localAdxPlaylet = i
}
