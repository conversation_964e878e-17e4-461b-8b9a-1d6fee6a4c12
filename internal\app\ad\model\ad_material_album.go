// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: internal/app/ad/model/ad_material_album.go
// 生成人：cyao
// desc:广告素材专辑
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdMaterialAlbumInfoRes is the golang structure for table ad_material_album.
type AdMaterialAlbumInfoRes struct {
	gmeta.Meta     `orm:"table:ad_material_album"`
	AlbumId        int         `orm:"album_id,primary" json:"albumId" dc:"id"`                                               // id
	AlbumName      string      `orm:"album_name" json:"albumName" dc:"专辑名称"`                                                 // 专辑名称
	UserId         int         `orm:"user_id" json:"userId" dc:"创建人"`                                                        // 创建人
	ScopeAuthority int         `orm:"scope_authority" json:"scopeAuthority" dc:"1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限"` // 1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限
	DeptIds        []int       `orm:"dept_ids" json:"deptIds" dc:"指定部门id "`
	SpecifyUserIds []int       `orm:"specify_user_ids" json:"specifyUserIds" dc:"指定用户id"`
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
}

type AdMaterialAlbumListRes struct {
	AlbumId        int         `json:"albumId" dc:"id"`
	AlbumName      string      `json:"albumName" dc:"专辑名称"`
	UserId         int         `json:"userId" dc:"创建人"`
	ScopeAuthority int         `json:"scopeAuthority" dc:"1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限"`
	Remark         string      `orm:"remark" json:"remark"` // 备注
	CreatedAt      *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdMaterialAlbumSearchReq 分页请求参数
type AdMaterialAlbumSearchReq struct {
	comModel.PageReq
	AlbumId   int    `p:"albumId" dc:"id"`     //id
	AlbumName string `p:"albumName" dc:"专辑名称"` //专辑名称
}

// AdMaterialAlbumSearchRes 列表返回结果
type AdMaterialAlbumSearchRes struct {
	comModel.ListRes
	List []*AdMaterialAlbumListRes `json:"list"`
}

// AdMaterialAlbumAddReq 添加操作请求参数
type AdMaterialAlbumAddReq struct {
	AlbumName      string `p:"albumName" v:"required#专辑名称不能为空" dc:"专辑名称"`
	UserId         int    `p:"userId" v:"required#创建人不能为空" dc:"创建人"`
	ScopeAuthority int    `p:"scopeAuthority" v:"required#1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限不能为空" dc:"1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限"`
	DeptIds        []int  `p:"deptIds"  dc:"指定部门id "`
	SpecifyUserIds []int  `p:"specifyUserIds"  dc:"指定用户id"`
	Remark         string `orm:"remark" json:"remark"` // 备注
}

// AdMaterialAlbumEditReq 修改操作请求参数
type AdMaterialAlbumEditReq struct {
	AlbumId        int    `p:"albumId" v:"required#主键ID不能为空" dc:"id"`
	AlbumName      string `p:"albumName" v:"required#专辑名称不能为空" dc:"专辑名称"`
	UserId         int    `p:"userId" v:"required#创建人不能为空" dc:"创建人"`
	ScopeAuthority int    `p:"scopeAuthority" v:"required#1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限不能为空" dc:"1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限"`
	DeptIds        []int  `p:"deptIds"  dc:"指定部门id "`
	SpecifyUserIds []int  `p:"specifyUserIds"  dc:"指定用户id"`
	Remark         string `orm:"remark" json:"remark"` // 备注
}
