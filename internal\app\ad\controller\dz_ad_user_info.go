// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/controller/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type dzAdUserInfoController struct {
	systemController.BaseController
}

var DzAdUserInfo = new(dzAdUserInfoController)

// List 列表
func (c *dzAdUserInfoController) List(ctx context.Context, req *ad.DzAdUserInfoSearchReq) (res *ad.DzAdUserInfoSearchRes, err error) {
	res = new(ad.DzAdUserInfoSearchRes)
	res.DzAdUserInfoSearchRes, err = service.DzAdUserInfo().List(ctx, &req.DzAdUserInfoSearchReq)
	return
}

// Export 导出excel
func (c *dzAdUserInfoController) Export(ctx context.Context, req *ad.DzAdUserInfoExportReq) (res *ad.DzAdUserInfoExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.DzAdUserInfoInfoRes
		//表头
		tableHead = []interface{}{"用户ID", "请求时间戳，单位毫秒", "渠道ID", "小程序ID", "推广班组ID", "openID", "广告ID", "书籍ID", "广告计划ID", "广告点击ID", "unionID", "用户注册时间戳，单位毫秒", "用户染色时间戳，单位毫秒", "创建时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.DzAdUserInfo().GetExportData(ctx, &req.DzAdUserInfoSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.UserId,
				v.Time,
				v.ChannelId,
				v.AppId,
				v.PromotionId,
				v.OpenId,
				v.AdId,
				v.BookId,
				v.ProjectId,
				v.ClickId,
				v.UnionId,
				v.RegisterTime,
				v.DyeTime,
				v.CreatedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("广告注册用户信息表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取广告注册用户信息表
func (c *dzAdUserInfoController) Get(ctx context.Context, req *ad.DzAdUserInfoGetReq) (res *ad.DzAdUserInfoGetRes, err error) {
	res = new(ad.DzAdUserInfoGetRes)
	res.DzAdUserInfoInfoRes, err = service.DzAdUserInfo().GetByUserId(ctx, req.UserId)
	return
}

// Add 添加广告注册用户信息表
func (c *dzAdUserInfoController) Add(ctx context.Context, req *ad.DzAdUserInfoAddReq) (res *ad.DzAdUserInfoAddRes, err error) {
	err = service.DzAdUserInfo().Add(ctx, req.DzAdUserInfoAddReq)
	return
}

// Edit 修改广告注册用户信息表
func (c *dzAdUserInfoController) Edit(ctx context.Context, req *ad.DzAdUserInfoEditReq) (res *ad.DzAdUserInfoEditRes, err error) {
	err = service.DzAdUserInfo().Edit(ctx, req.DzAdUserInfoEditReq)
	return
}

// Delete 删除广告注册用户信息表
func (c *dzAdUserInfoController) Delete(ctx context.Context, req *ad.DzAdUserInfoDeleteReq) (res *ad.DzAdUserInfoDeleteRes, err error) {
	err = service.DzAdUserInfo().Delete(ctx, req.UserIds)
	return
}
