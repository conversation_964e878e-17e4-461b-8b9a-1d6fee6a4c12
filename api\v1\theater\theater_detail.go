// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-02-19 17:35:12
// 生成路径: api/v1/theater/theater_detail.go
// 生成人：cyao
// desc:剧集子表信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterDetailSearchReq 分页请求参数
type TheaterDetailSearchReq struct {
	g.Meta `path:"/getTheaterDetail" tags:"短剧推广" method:"get" summary:"短剧中心/获取短剧详情（观看操作）"`
	commonApi.Author
	model.TheaterDetailSearchReq
}

// TheaterDetailSearchRes 列表返回结果
type TheaterDetailSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterDetailSearchRes
}

// TheaterDetailAddReq 添加操作请求参数
type TheaterDetailAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广" method:"post" summary:"剧集子表信息添加"`
	commonApi.Author
	*model.TheaterDetailAddReq
}

// TheaterDetailAddRes 添加操作返回结果
type TheaterDetailAddRes struct {
	commonApi.EmptyRes
}

// TheaterDetailEditReq 修改操作请求参数
type TheaterDetailEditReq struct {
	g.Meta `path:"/edit" tags:"短剧推广" method:"put" summary:"剧集子表信息修改"`
	commonApi.Author
	*model.TheaterDetailEditReq
}

// TheaterDetailEditRes 修改操作返回结果
type TheaterDetailEditRes struct {
	commonApi.EmptyRes
}

// TheaterDetailGetReq 获取一条数据请求
type TheaterDetailGetReq struct {
	g.Meta `path:"/get" tags:"短剧推广" method:"get" summary:"获取剧集子表信息信息"`
	commonApi.Author
	SubId int64 `p:"subId" v:"required#主键必须"` //通过主键获取
}

// TheaterDetailGetRes 获取一条数据结果
type TheaterDetailGetRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterDetailInfoRes
}

// TheaterDetailDeleteReq 删除数据请求
//type TheaterDetailDeleteReq struct {
//	g.Meta `path:"/delete" tags:"短剧推广" method:"post" summary:"删除剧集子表信息"`
//	commonApi.Author
//	SubIds []int64 `p:"subIds" v:"required#主键必须"` //通过主键删除
//}

// TheaterDetailDeleteRes 删除数据返回
type TheaterDetailDeleteRes struct {
	commonApi.EmptyRes
}

type TheaterRemoveDetailsReq struct {
	g.Meta `path:"/removeDetails" tags:"短剧推广" method:"post" summary:"删除剧集子表信息"`
	commonApi.Author
	SubIds   []int64 `p:"subIds"`   //通过主键删除
	ParentId int64   `p:"parentId"` //通过父级Id进行删除 （只保留第一条集数为1的数据）
}

type BatchAddDetailsReq struct {
	g.Meta `path:"/batch/add/details" tags:"短剧推广" method:"post" summary:"短剧批量上传"`
	commonApi.Author
	*model.BatchAddDetailsRes
}

type BatchAddDetailsRes struct {
	commonApi.EmptyRes
}
