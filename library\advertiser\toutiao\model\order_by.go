package model

import "fmt"

// ReportCustomGetV30OrderByType
type OrderByType string

// List of order_by_type
const (
	ASC_OrderByType  OrderByType = "ASC"
	DESC_OrderByType OrderByType = "DESC"
)

// All allowed values of ReportCustomGetV30OrderByType enum
var AllowedOrderByTypeEnumValues = []OrderByType{
	"ASC",
	"DESC",
}

// NewOrderByTypeFromValue returns a pointer to a valid ReportCustomGetV30OrderByType
// for the value passed as argument, or an error if the value passed is not allowed by the enum
func NewOrderByTypeFromValue(v string) (*OrderByType, error) {
	ev := OrderByType(v)
	if ev.IsValid() {
		return &ev, nil
	} else {
		return nil, fmt.Errorf("invalid value '%v' for ReportCustomGetV30OrderByType: valid values are %v", v, AllowedOrderByTypeEnumValues)
	}
}

// IsValid return true if the value is valid for the enum, false otherwise
func (v OrderByType) IsValid() bool {
	for _, existing := range AllowedOrderByTypeEnumValues {
		if existing == v {
			return true
		}
	}
	return false
}

// Ptr returns reference to report_custom_get_v3.0_order_by_type value
func (v OrderByType) Ptr() *OrderByType {
	return &v
}
