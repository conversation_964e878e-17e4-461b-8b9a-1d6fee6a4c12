// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-16 11:16:20
// 生成路径: internal/app/ad/logic/fq_ad_account_depts.go
// 生成人：gfast
// desc:番茄账号权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdAccountDepts(New())
}

func New() service.IFqAdAccountDepts {
	return &sFqAdAccountDepts{}
}

type sFqAdAccountDepts struct{}

func (s *sFqAdAccountDepts) List(ctx context.Context, req *model.FqAdAccountDeptsSearchReq) (listRes *model.FqAdAccountDeptsSearchRes, err error) {
	listRes = new(model.FqAdAccountDeptsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAccountDepts.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdAccountDepts.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if len(req.DistributorIds) > 0 {
			m = m.WhereIn(dao.FqAdAccountDepts.Columns().DistributorId, req.DistributorIds)
		}
		if req.DetpId != "" {
			m = m.Where(dao.FqAdAccountDepts.Columns().DetpId+" = ?", req.DetpId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.FqAdAccountDeptsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdAccountDeptsListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.FqAdAccountDeptsListRes{
				DistributorId: v.DistributorId,
				DetpId:        v.DetpId,
			}
		}
	})
	return
}

func (s *sFqAdAccountDepts) GetExportData(ctx context.Context, req *model.FqAdAccountDeptsSearchReq) (listRes []*model.FqAdAccountDeptsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAccountDepts.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdAccountDepts.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.DetpId != "" {
			m = m.Where(dao.FqAdAccountDepts.Columns().DetpId+" = ?", req.DetpId)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdAccountDepts) GetByDetpId(ctx context.Context, distributorId int64) (res *model.FqAdAccountDeptsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccountDepts.Ctx(ctx).WithAll().Where(dao.FqAdAccountDepts.Columns().DetpId, distributorId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccountDepts) Add(ctx context.Context, req *model.FqAdAccountDeptsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountDepts.Ctx(ctx).Insert(do.FqAdAccountDepts{
			DistributorId: req.DistributorId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdAccountDepts) Edit(ctx context.Context, req *model.FqAdAccountDeptsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountDepts.Ctx(ctx).WherePri(req.DistributorId).Update(do.FqAdAccountDepts{})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdAccountDepts) Delete(ctx context.Context, distributorIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountDepts.Ctx(ctx).Delete(dao.FqAdAccountDepts.Columns().DetpId+" in (?)", distributorIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
