// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-13 15:31:11
// 生成路径: internal/app/ad/logic/ad_material_album_users.go
// 生成人：cyao
// desc:广告素材专辑和用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdMaterialAlbumUsers(New())
}

func New() service.IAdMaterialAlbumUsers {
	return &sAdMaterialAlbumUsers{}
}

type sAdMaterialAlbumUsers struct{}

func (s *sAdMaterialAlbumUsers) List(ctx context.Context, req *model.AdMaterialAlbumUsersSearchReq) (listRes *model.AdMaterialAlbumUsersSearchRes, err error) {
	listRes = new(model.AdMaterialAlbumUsersSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdMaterialAlbumUsers.Ctx(ctx).WithAll()
		if req.AlbumId != "" {
			m = m.Where(dao.AdMaterialAlbumUsers.Columns().AlbumId+" = ?", req.AlbumId)
		}
		if req.SpecifyUserId != "" {
			m = m.Where(dao.AdMaterialAlbumUsers.Columns().SpecifyUserId+" = ?", req.SpecifyUserId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "album_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdMaterialAlbumUsersListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdMaterialAlbumUsersListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdMaterialAlbumUsersListRes{
				AlbumId:       v.AlbumId,
				SpecifyUserId: v.SpecifyUserId,
			}
		}
	})
	return
}

func (s *sAdMaterialAlbumUsers) GetByAlbumId(ctx context.Context, albumId int) (res *model.AdMaterialAlbumUsersInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialAlbumUsers.Ctx(ctx).WithAll().Where(dao.AdMaterialAlbumUsers.Columns().AlbumId, albumId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialAlbumUsers) Add(ctx context.Context, req *model.AdMaterialAlbumUsersAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbumUsers.Ctx(ctx).Insert(do.AdMaterialAlbumUsers{
			AlbumId: req.AlbumId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdMaterialAlbumUsers) Edit(ctx context.Context, req *model.AdMaterialAlbumUsersEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbumUsers.Ctx(ctx).WherePri(req.AlbumId).Update(do.AdMaterialAlbumUsers{})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdMaterialAlbumUsers) Delete(ctx context.Context, albumIds []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbumUsers.Ctx(ctx).Delete(dao.AdMaterialAlbumUsers.Columns().SpecifyUserId+" in (?)", albumIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// 获取所属用户
func (s *sAdMaterialAlbumUsers) GetSpecifyUserIds(ctx context.Context, id int) []int {
	list := make([]int, 0)
	scanList := make([]*model.AdMaterialAlbumUsersInfoRes, 0)
	dao.AdMaterialAlbumUsers.Ctx(ctx).Where(dao.AdMaterialAlbumUsers.Columns().AlbumId, id).Scan(&scanList)
	if len(scanList) > 0 {
		for _, item := range scanList {
			list = append(list, item.SpecifyUserId)
		}
	}
	return list
}
