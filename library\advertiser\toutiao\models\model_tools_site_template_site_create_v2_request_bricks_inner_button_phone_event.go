/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2RequestBricksInnerButtonPhoneEvent phoneEvent事件行为描述
type ToolsSiteTemplateSiteCreateV2RequestBricksInnerButtonPhoneEvent struct {
	// 智能电话组件ID，用户可以通过[【创建智能电话】](https://open.oceanengine.com/doc/index.html?key=ad&type=api&id=1696710643975180#item-link-%E8%AF%B7%E6%B1%82%E5%9C%B0%E5%9D%80)接口或[【青鸟线索通平台】]（https://clue.oceanengine.com/）创建智能电话组件
	InstanceId int64 `json:"instance_id"`
}
