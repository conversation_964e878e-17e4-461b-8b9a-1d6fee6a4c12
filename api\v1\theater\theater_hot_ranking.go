// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-10-14 15:39:04
// 生成路径: api/v1/theater/theater_hot_ranking.go
// 生成人：cq
// desc:短剧热度排行榜相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// TheaterHotRankingSearchReq 分页请求参数
type TheaterHotRankingSearchReq struct {
	g.Meta `path:"/list" tags:"短剧热度排行榜" method:"get" summary:"短剧热度排行榜列表"`
	commonApi.Author
	model.TheaterHotRankingSearchReq
}

// TheaterHotRankingSearchRes 列表返回结果
type TheaterHotRankingSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.TheaterHotRankingSearchRes
}

// TheaterHotRankingStatTaskReq 短剧小时多维度次数统计任务
type TheaterHotRankingStatTaskReq struct {
	g.Meta `path:"/task" tags:"短剧热度排行榜" method:"get" summary:"短剧热度排行榜统计任务"`
	commonApi.Author
	model.TheaterHotRankingStatTaskReq
}

// TheaterHotRankingStatTaskRes 列表返回结果
type TheaterHotRankingStatTaskRes struct {
	g.Meta `mime:"application/json"`
}
