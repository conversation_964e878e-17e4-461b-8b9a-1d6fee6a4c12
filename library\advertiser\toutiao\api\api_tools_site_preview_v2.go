/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/oceanengine/ad_open_sdk_go/config"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

type ToolsSitePreviewV2ApiService struct {
	ctx          context.Context
	cfg          *conf.Configuration
	token        string
	advertiserId int64
	siteId       string
}

// 广告主id，广告主id， 传的这个advertiser_id的数字的范围：1 &lt;&#x3D; advertiser_id &lt;&#x3D; MAX_INT64
func (r *ToolsSitePreviewV2ApiService) AdvertiserId(advertiserId int64) *ToolsSitePreviewV2ApiService {
	r.advertiserId = advertiserId
	return r
}

// 橙子建站站点id
func (r *ToolsSitePreviewV2ApiService) SiteId(siteId string) *ToolsSitePreviewV2ApiService {
	r.siteId = siteId
	return r
}

func (r *ToolsSitePreviewV2ApiService) AccessToken(accessToken string) *ToolsSitePreviewV2ApiService {
	r.ctx = context.WithValue(r.ctx, config.ContextAccessToken, accessToken)
	return r
}

func (r *ToolsSitePreviewV2ApiService) SetCfg(cfg *conf.Configuration) *ToolsSitePreviewV2ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsSitePreviewV2ApiService) WithLog(enable bool) *ToolsSitePreviewV2ApiService {
	if enable {
		r.ctx = context.WithValue(r.ctx, config.ContextEnableLog, true)
	}
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsSitePreviewV2ApiService) Do() (data *models.ToolsSitePreviewV2Response, err error) {
	if r.advertiserId == 0 || len(r.siteId) == 0 {
		return nil, errors.New("广告主id，橙子建站站点id不能为空")
	}
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/tools/site/get/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_ids", r.advertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "site_id", r.siteId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.ToolsSitePreviewV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsSitePreviewV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/tools/site/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
