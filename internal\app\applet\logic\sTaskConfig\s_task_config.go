// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-07-16 14:26:31
// 生成路径: internal/app/applet/logic/s_task_config.go
// 生成人：cq
// desc:任务配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	jsoniter "github.com/json-iterator/go"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonEntity "github.com/tiger1103/gfast/v3/internal/app/common/model/entity"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
)

func init() {
	service.RegisterSTaskConfig(New())
}

func New() service.ISTaskConfig {
	return &sSTaskConfig{}
}

type sSTaskConfig struct{}

/*
List 查询任务配置表列表
需求：
1、在不改变表结构的情况下，不同小程序的同一任务且配置一样合并显示为一条数据，appId列显示多个小程序名称，id列取最小的一列
2、任务类型为每日看广告，每个小程序单独显示一条数据，不需要合并
3、新增时每个小程序都新增一条记录
4、编辑时减少了小程序，删除对应的小程序配置，新增了小程序，新增一条记录，旧的配置直接更新
*/
func (s *sSTaskConfig) List(ctx context.Context, req *model.STaskConfigSearchReq) (listRes *model.STaskConfigSearchRes, err error) {
	listRes = new(model.STaskConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.STaskConfig.Ctx(ctx).WithAll()
		var configs []*model.STaskConfigListRes
		// 先查询不是每日看广告的任务配置
		err = m.WhereNot(dao.STaskConfig.Columns().Type, commonConsts.WatchVideosDaily).
			Fields("type as type").
			Fields("reward_type as rewardType").
			Fields("reward_num as rewardNum").
			Fields("target_num as targetNum").
			Fields("MIN(id) as id").
			Fields("GROUP_CONCAT(id) as idList").
			Fields("GROUP_CONCAT(app_id) as appId").
			Fields("ANY_VALUE(sort) as sort").
			Fields("ANY_VALUE(ad_position_id) as adPositionId").
			Fields("ANY_VALUE(double_switch) as doubleSwitch").
			Fields("ANY_VALUE(status) as status").
			Fields("ANY_VALUE(remark) as remark").
			Group("type", "reward_type", "reward_num", "target_num").
			Scan(&configs)
		liberr.ErrIsNil(ctx, err, "获取非每日看广告配置失败")
		// 再查询每日看广告任务配置
		var watchVideoConfigs []*model.STaskConfigListRes
		err = m.Where(dao.STaskConfig.Columns().Type, commonConsts.WatchVideosDaily).Scan(&watchVideoConfigs)

		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		// 合并数据，分页排序
		res := make([]*model.STaskConfigListRes, 0)
		res = append(res, configs...)
		res = append(res, watchVideoConfigs...)
		// 根据appIds条件过滤
		if req.AppIds != nil && len(req.AppIds) > 0 {
			filterRes := make([]*model.STaskConfigListRes, 0)
			for _, appId := range req.AppIds {
				for _, v := range res {
					if strings.Contains(v.AppId, appId) {
						filterRes = append(filterRes, v)
					}
				}
			}
			res = filterRes
		}
		linq.From(res).Sort(func(i, j interface{}) bool {
			return i.(*model.STaskConfigListRes).Sort > j.(*model.STaskConfigListRes).Sort
		}).ToSlice(&res)
		pageRes := make([]*model.STaskConfigListRes, 0)
		linq.From(res).Skip(req.PageSize * (req.PageNum - 1)).Take(req.PageSize).ToSlice(&pageRes)
		listRes.List = make([]*model.STaskConfigListRes, len(pageRes))
		listRes.Total = len(res)

		dictDataList, err1 := commonService.SysDictData().GetByType(ctx, commonConsts.TaskType)
		liberr.ErrIsNil(ctx, err1, "获取字典数据失败")

		appIds := make([]string, 0)
		linq.From(pageRes).SelectT(func(item *model.STaskConfigListRes) string {
			return item.AppId
		}).ToSlice(&appIds)
		appIdSet := gset.NewStrSet()
		for _, v := range appIds {
			splits := strings.Split(v, ",")
			appIdSet.Add(splits...)
		}
		// 获取小程序名称
		miniInfos := make([]*systemModel.GetMiniInfoListRes, 0)
		if appIdSet.Size() > 0 {
			miniInfos, err1 = sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIdSet.Slice())
			liberr.ErrIsNil(ctx, err1, "获取小程序名称失败")
		}
		for k, v := range pageRes {
			config := &model.STaskConfigListRes{
				Id:           v.Id,
				IdList:       v.IdList,
				AppId:        v.AppId,
				Type:         v.Type,
				RewardType:   v.RewardType,
				RewardNum:    v.RewardNum,
				TargetNum:    v.TargetNum,
				Sort:         v.Sort,
				Status:       v.Status,
				Remark:       v.Remark,
				AdPositionId: v.AdPositionId,
				DoubleSwitch: v.DoubleSwitch,
			}
			dictDataInterface := linq.From(dictDataList).WhereT(func(item *commonEntity.SysDictData) bool {
				return item.DictValue == gconv.String(v.Type)
			}).First()
			if dictData, ok := dictDataInterface.(*commonEntity.SysDictData); ok {
				config.Name = dictData.DictLabel
			}
			miniInfoList := make([]*systemModel.GetMiniInfoListRes, 0)
			linq.From(miniInfos).WhereT(func(item *systemModel.GetMiniInfoListRes) bool {
				return strings.Contains(v.AppId, item.AppId)
			}).ToSlice(&miniInfoList)
			appNames := make([]string, 0)
			linq.From(miniInfoList).SelectT(func(item *systemModel.GetMiniInfoListRes) string {
				return item.AppName
			}).ToSlice(&appNames)
			config.AppName = strings.Join(appNames, ",")
			config.AppIds = strings.Split(v.AppId, ",")
			ids := make([]int, 0)
			if v.Type == commonConsts.WatchVideosDaily {
				ids = append(ids, v.Id)
			} else {
				for _, id := range strings.Split(v.IdList, ",") {
					ids = append(ids, gconv.Int(id))
				}
			}
			config.Ids = ids
			if v.Type == commonConsts.SignIn {
				_ = json.Unmarshal([]byte(v.RewardNum), &config.SignInList)
			} else if v.Type == commonConsts.WatchDramaDaily {
				_ = json.Unmarshal([]byte(v.RewardNum), &config.WatchVideoList)
			}
			listRes.List[k] = config
		}
	})
	return
}

func (s *sSTaskConfig) GetById(ctx context.Context, id int) (res *model.STaskConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.STaskConfig.Ctx(ctx).WithAll().Where(dao.STaskConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSTaskConfig) Add(ctx context.Context, req *model.STaskConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.AppIds == nil || len(req.AppIds) == 0 {
			req.AppIds = []string{""}
		}
		// 查询小程序是否存在配置
		existConfigs, err1 := s.GetByAppIds(ctx, req.AppIds, req.Type)
		liberr.ErrIsNil(ctx, err1, "查询小程序是否存在配置失败")
		if existConfigs != nil && len(existConfigs) > 0 {
			appIds := make([]string, 0)
			linq.From(existConfigs).SelectT(func(item *model.STaskConfigInfoRes) string {
				return item.AppId
			}).ToSlice(&appIds)
			err1 = errors.New(fmt.Sprintf("小程序ID: %v 已存在配置", strings.Join(appIds, ",")))
			liberr.ErrIsNil(ctx, err1)
		}
		rewardNum := s.BuildRewardNum(ctx, req.RewardNum, req.Type, req.SignInList, req.WatchVideoList)
		typeName := s.GetTypeName(ctx, req.Type)
		taskConfigs := make([]do.STaskConfig, 0)
		for _, appId := range req.AppIds {
			if appId == "all" {
				appId = ""
			}
			taskConfig := do.STaskConfig{
				AppId:        appId,
				Type:         req.Type,
				TypeName:     typeName,
				RewardType:   req.RewardType,
				RewardNum:    rewardNum,
				TargetNum:    req.TargetNum,
				Sort:         req.Sort,
				Status:       req.Status,
				DoubleSwitch: req.DoubleSwitch,
				Remark:       req.Remark,
				CreateTime:   gtime.Now(),
				UpdateTime:   gtime.Now(),
				AdPositionId: req.AdPositionId,
			}
			taskConfigs = append(taskConfigs, taskConfig)
		}
		_, err = dao.STaskConfig.Ctx(ctx).Batch(len(taskConfigs)).Insert(taskConfigs)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSTaskConfig) Edit(ctx context.Context, req *model.STaskConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 请求的配置列表
		existConfigs, err1 := s.GetByAppIds(ctx, req.AppIds, req.Type)
		// 旧的配置列表
		configs, err1 := s.GetByIds(ctx, req.Ids, req.Type)
		liberr.ErrIsNil(ctx, err1, "查询任务配置失败")
		existAppIds := make([]string, 0)
		// 校验小程序是否存在配置
		for _, existConfig := range existConfigs {
			if !libUtils.FindTargetInt(req.Ids, existConfig.Id) {
				existAppIds = append(existAppIds, existConfig.AppId)
			}
		}
		if len(existAppIds) > 0 {
			miniInfos, _ := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, existAppIds)
			appNames := make([]string, 0)
			linq.From(miniInfos).SelectT(func(item *systemModel.GetMiniInfoListRes) string {
				return item.AppName
			}).ToSlice(&appNames)
			err2 := errors.New(fmt.Sprintf("小程序: %s 已存在配置", strings.Join(appNames, ",")))
			liberr.ErrIsNil(ctx, err2)
		}
		rewardNum := s.BuildRewardNum(ctx, req.RewardNum, req.Type, req.SignInList, req.WatchVideoList)
		typeName := s.GetTypeName(ctx, req.Type)
		// 旧的配置更新
		oldConfigs := make([]*model.STaskConfigInfoRes, 0)
		// 新的配置插入
		newConfigs := make([]*model.STaskConfigInfoRes, 0)
		for _, appId := range req.AppIds {
			if config, ok := linq.From(configs).WhereT(func(item *model.STaskConfigInfoRes) bool {
				return item.AppId == appId
			}).First().(*model.STaskConfigInfoRes); ok {
				oldConfigs = append(oldConfigs, config)
			} else {
				if appId == "all" {
					appId = ""
				}
				newConfig := &model.STaskConfigInfoRes{
					AppId:        appId,
					Type:         req.Type,
					TypeName:     typeName,
					RewardType:   req.RewardType,
					RewardNum:    rewardNum,
					TargetNum:    req.TargetNum,
					Sort:         req.Sort,
					Status:       req.Status,
					DoubleSwitch: req.DoubleSwitch,
					Remark:       req.Remark,
					CreateTime:   gtime.Now(),
					UpdateTime:   gtime.Now(),
					AdPositionId: req.AdPositionId,
				}
				newConfigs = append(newConfigs, newConfig)
			}
		}
		// 需要删除的ID列表 configs里面不在oldConfigs里面的
		deleteIds := make([]int, 0)
		oldIds := make([]int, 0)
		linq.From(oldConfigs).SelectT(func(item *model.STaskConfigInfoRes) int {
			return item.Id
		}).ToSlice(&oldIds)
		linq.From(configs).WhereT(func(item *model.STaskConfigInfoRes) bool {
			return !linq.From(oldIds).Contains(item.Id)
		}).SelectT(func(item *model.STaskConfigInfoRes) int {
			return item.Id
		}).ToSlice(&deleteIds)

		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			err = g.Try(ctx, func(ctx context.Context) {
				for _, oldConfig := range oldConfigs {
					_, err = dao.STaskConfig.Ctx(ctx).WherePri(oldConfig.Id).Update(do.STaskConfig{
						AppId:        oldConfig.AppId,
						Type:         req.Type,
						TypeName:     typeName,
						RewardType:   req.RewardType,
						RewardNum:    rewardNum,
						TargetNum:    req.TargetNum,
						Sort:         req.Sort,
						Status:       req.Status,
						Remark:       req.Remark,
						UpdateTime:   gtime.Now(),
						AdPositionId: req.AdPositionId,
						DoubleSwitch: req.DoubleSwitch,
					})
				}
				if len(newConfigs) > 0 {
					_, err = dao.STaskConfig.Ctx(ctx).Save(newConfigs)
				}
				if len(deleteIds) > 0 {
					_, err = dao.STaskConfig.Ctx(ctx).Delete(dao.STaskConfig.Columns().Id+" in (?)", deleteIds)
				}
			})
			return err
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSTaskConfig) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.STaskConfig.Ctx(ctx).Delete(dao.STaskConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sSTaskConfig) GetByAppId(ctx context.Context, taskType int, appId string) (res *model.STaskConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.STaskConfig.Ctx(ctx).WithAll().
			Where(dao.STaskConfig.Columns().Type, taskType).
			Where(dao.STaskConfig.Columns().AppId, appId).
			WhereOr(dao.STaskConfig.Columns().AppId, "").
			OrderDesc(dao.STaskConfig.Columns().AppId).
			Limit(1).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSTaskConfig) GetByAppIds(ctx context.Context, appIds []string, taskType int) (res []*model.STaskConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if appIds[0] == "all" {
			appIds = []string{""}
		}
		err = dao.STaskConfig.Ctx(ctx).
			WhereIn(dao.STaskConfig.Columns().AppId, appIds).
			Where(dao.STaskConfig.Columns().Type, taskType).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取任务配置失败")
	})
	return
}

func (s *sSTaskConfig) GetByIds(ctx context.Context, ids []int, taskType int) (res []*model.STaskConfigInfoRes, err error) {
	if taskType > 0 {
		err = g.Try(ctx, func(ctx context.Context) {
			err = dao.STaskConfig.Ctx(ctx).
				WhereIn(dao.STaskConfig.Columns().Id, ids).
				Where(dao.STaskConfig.Columns().Type, taskType).
				Scan(&res)
			liberr.ErrIsNil(ctx, err, "获取任务配置失败")
		})
	} else {
		err = g.Try(ctx, func(ctx context.Context) {
			err = dao.STaskConfig.Ctx(ctx).
				WhereIn(dao.STaskConfig.Columns().Id, ids).
				Scan(&res)
			liberr.ErrIsNil(ctx, err, "获取任务配置失败")
		})
	}
	return
}

func (s *sSTaskConfig) BuildRewardNum(ctx context.Context, rewardNum string, taskType int, signInList []*model.SignIn, watchVideoList []*model.WatchVideo) string {
	if taskType == commonConsts.SignIn {
		signInStr, err2 := jsoniter.MarshalToString(signInList)
		liberr.ErrIsNil(ctx, err2, "SignInList MarshalToString err")
		rewardNum = signInStr
	} else if taskType == commonConsts.WatchDramaDaily {
		watchVideoStr, err2 := jsoniter.MarshalToString(watchVideoList)
		liberr.ErrIsNil(ctx, err2, "WatchVideoList MarshalToString err")
		rewardNum = watchVideoStr
	}
	return rewardNum
}

func (s *sSTaskConfig) GetTypeName(ctx context.Context, taskType int) string {
	dictDataList, err := commonService.SysDictData().GetByType(ctx, commonConsts.TaskType)
	liberr.ErrIsNil(ctx, err, "获取任务类型字典数据失败")
	var typeName string
	if dictData, ok := linq.From(dictDataList).WhereT(func(item *commonEntity.SysDictData) bool {
		return item.DictValue == gconv.String(taskType)
	}).First().(*commonEntity.SysDictData); ok {
		typeName = dictData.DictLabel
	}
	return typeName
}
