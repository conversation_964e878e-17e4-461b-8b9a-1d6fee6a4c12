// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: internal/app/ad/dao/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// mpAdEventDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type mpAdEventDao struct {
	*internal.MpAdEventDao
}

var (
	// MpAdEvent is globally public accessible object for table tools_gen_table operations.
	MpAdEvent = mpAdEventDao{
		internal.NewMpAdEventDao(),
	}
)

// Fill with you ideas below.
