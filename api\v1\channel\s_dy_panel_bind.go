// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-20 21:09:35
// 生成路径: api/v1/channel/s_dy_panel_bind.go
// 生成人：cq
// desc:抖音面板绑定记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SDyPanelBindSearchReq 分页请求参数
type SDyPanelBindSearchReq struct {
	g.Meta `path:"/list" tags:"抖音面板绑定记录" method:"get" summary:"抖音面板绑定记录列表"`
	commonApi.Author
	model.SDyPanelBindSearchReq
}

// SDyPanelBindSearchRes 列表返回结果
type SDyPanelBindSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyPanelBindSearchRes
}

// SDyPanelBindAddReq 添加操作请求参数
type SDyPanelBindAddReq struct {
	g.Meta `path:"/add" tags:"抖音面板绑定记录" method:"post" summary:"抖音面板绑定记录添加"`
	commonApi.Author
	*model.SDyPanelBindAddReq
}

// SDyPanelBindAddRes 添加操作返回结果
type SDyPanelBindAddRes struct {
	commonApi.EmptyRes
}

// SDyPanelBindEditReq 修改操作请求参数
type SDyPanelBindEditReq struct {
	g.Meta `path:"/edit" tags:"抖音面板绑定记录" method:"put" summary:"抖音面板绑定记录修改"`
	commonApi.Author
	*model.SDyPanelBindEditReq
}

// SDyPanelBindEditRes 修改操作返回结果
type SDyPanelBindEditRes struct {
	commonApi.EmptyRes
}

// SDyPanelBindGetReq 获取一条数据请求
type SDyPanelBindGetReq struct {
	g.Meta `path:"/get" tags:"抖音面板绑定记录" method:"get" summary:"获取抖音面板绑定记录信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SDyPanelBindGetRes 获取一条数据结果
type SDyPanelBindGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyPanelBindInfoRes
}

// SDyPanelBindDeleteReq 删除数据请求
type SDyPanelBindDeleteReq struct {
	g.Meta `path:"/delete" tags:"抖音面板绑定记录" method:"delete" summary:"删除抖音面板绑定记录"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SDyPanelBindDeleteRes 删除数据返回
type SDyPanelBindDeleteRes struct {
	commonApi.EmptyRes
}

// RunCheckPanelBindReq 检测面板绑定任务请求参数
type RunCheckPanelBindReq struct {
	g.Meta `path:"/task" tags:"抖音面板绑定记录" method:"get" summary:"检测面板绑定任务"`
	commonApi.Author
	*model.SDyPanelBindSearchReq
}

// RunCheckPanelBindRes 检测面板绑定任务结果
type RunCheckPanelBindRes struct {
	g.Meta `mime:"application/json"`
}

// BindFailedPanelByTimeReq 绑定面板到抖音请求
type BindFailedPanelByTimeReq struct {
	g.Meta `path:"/bindPanel" tags:"商品信息主表" method:"post" summary:"绑定面板到抖音"`
	commonApi.Author
	Account string `p:"account"`
}

// BindFailedPanelByTimeRes 绑定面板到抖音返回
type BindFailedPanelByTimeRes struct {
	commonApi.EmptyRes
}
