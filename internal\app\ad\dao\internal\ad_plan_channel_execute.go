// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-11-27 11:19:17
// 生成路径: internal/app/ad/dao/internal/ad_plan_channel_execute.go
// 生成人：cq
// desc:广告渠道执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdPlanChannelExecuteDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdPlanChannelExecuteDao struct {
	table   string                      // Table is the underlying table name of the DAO.
	group   string                      // Group is the database configuration group name of current DAO.
	columns AdPlanChannelExecuteColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdPlanChannelExecuteColumns defines and stores column names for table ad_plan_channel_execute.
type AdPlanChannelExecuteColumns struct {
	Id          string //
	PlanId      string // 计划ID
	ChannelList string // 渠道列表，用|分隔
	TemplateId  string // 充值模板ID
	SinglePrice string // 单集价格
	LockNum     string // 付费集数
	RewardId    string // 激励广告配置ID
	AdSettingId string // 广告回传配置ID
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

var adPlanChannelExecuteColumns = AdPlanChannelExecuteColumns{
	Id:          "id",
	PlanId:      "plan_id",
	ChannelList: "channel_list",
	TemplateId:  "template_id",
	SinglePrice: "single_price",
	LockNum:     "lock_num",
	RewardId:    "reward_id",
	AdSettingId: "ad_setting_id",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewAdPlanChannelExecuteDao creates and returns a new DAO object for table data access.
func NewAdPlanChannelExecuteDao() *AdPlanChannelExecuteDao {
	return &AdPlanChannelExecuteDao{
		group:   "default",
		table:   "ad_plan_channel_execute",
		columns: adPlanChannelExecuteColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdPlanChannelExecuteDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdPlanChannelExecuteDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdPlanChannelExecuteDao) Columns() AdPlanChannelExecuteColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdPlanChannelExecuteDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdPlanChannelExecuteDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdPlanChannelExecuteDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
