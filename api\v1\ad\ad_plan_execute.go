// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: api/v1/ad/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdPlanExecuteSearchReq 分页请求参数
type AdPlanExecuteSearchReq struct {
	g.Meta `path:"/list" tags:"广告计划执行配置" method:"get" summary:"广告计划执行配置列表"`
	commonApi.Author
	model.AdPlanExecuteSearchReq
}

// AdPlanExecuteSearchRes 列表返回结果
type AdPlanExecuteSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanExecuteSearchRes
}

// AdPlanExecuteAddReq 添加操作请求参数
type AdPlanExecuteAddReq struct {
	g.Meta `path:"/add" tags:"广告计划执行配置" method:"post" summary:"广告计划执行配置添加"`
	commonApi.Author
	*model.AdPlanExecuteAddReq
}

// AdPlanExecuteAddRes 添加操作返回结果
type AdPlanExecuteAddRes struct {
	commonApi.EmptyRes
}

// AdPlanExecuteEditReq 修改操作请求参数
type AdPlanExecuteEditReq struct {
	g.Meta `path:"/edit" tags:"广告计划执行配置" method:"put" summary:"广告计划执行配置修改"`
	commonApi.Author
	*model.AdPlanExecuteEditReq
}

// AdPlanExecuteEditRes 修改操作返回结果
type AdPlanExecuteEditRes struct {
	commonApi.EmptyRes
}

// AdPlanExecuteGetReq 获取一条数据请求
type AdPlanExecuteGetReq struct {
	g.Meta `path:"/get" tags:"广告计划执行配置" method:"get" summary:"获取广告计划执行配置信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPlanExecuteGetRes 获取一条数据结果
type AdPlanExecuteGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanExecuteInfoRes
}

// AdPlanExecuteDeleteReq 删除数据请求
type AdPlanExecuteDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告计划执行配置" method:"post" summary:"删除广告计划执行配置"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPlanExecuteDeleteRes 删除数据返回
type AdPlanExecuteDeleteRes struct {
	commonApi.EmptyRes
}
