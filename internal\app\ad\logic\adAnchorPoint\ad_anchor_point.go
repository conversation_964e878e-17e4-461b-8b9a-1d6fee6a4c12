// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/logic/ad_anchor_point.go
// 生成人：cyao
// desc:锚点表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"
	commonModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	oceanengineService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModels "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAnchorPoint(New())
}

func New() service.IAdAnchorPoint {
	return &sAdAnchorPoint{}
}

type sAdAnchorPoint struct{}

func (s *sAdAnchorPoint) List(ctx context.Context, req *model.AdAnchorPointSearchReq) (listRes *model.AdAnchorPointSearchRes, err error) {
	listRes = new(model.AdAnchorPointSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {

		userInfo := sysService.Context().GetLoginUser(ctx)
		uIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})

		m := dao.AdAnchorPoint.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().Id+" = ?", req.Id)
		}
		if req.AnchorPointName != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().AnchorPointName+" like ?", "%"+req.AnchorPointName+"%")
		}
		if req.ToolTitle != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().ToolTitle+" = ?", req.ToolTitle)
		}
		if req.AnchorType != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().AnchorType+" = ?", req.AnchorType)
		}
		if req.AndroidAnchorTitle != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().AndroidAnchorTitle+" = ?", req.AndroidAnchorTitle)
		}
		if req.IosAnchorTitle != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().IosAnchorTitle+" = ?", req.IosAnchorTitle)
		}
		if req.GuideText != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().GuideText+" = ?", req.GuideText)
		}
		if req.AnchorImageMode != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().AnchorImageMode+" = ?", gconv.Int(req.AnchorImageMode))
		}
		if req.GameDescription != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().GameDescription+" = ?", req.GameDescription)
		}
		if req.AppDescription != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().AppDescription+" = ?", req.AppDescription)
		}
		if req.GameCharatoristic != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().GameCharatoristic+" = ?", req.GameCharatoristic)
		}
		if req.OtherDescription != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().OtherDescription+" = ?", req.OtherDescription)
		}
		if req.MainUserId != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().MainUserId+" = ?", gconv.Int64(req.MainUserId))
		}

		if !admin && len(uIds) > 0 {
			m = m.WhereIn(dao.AdAnchorPoint.Columns().MainUserId, uIds)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.AdAnchorPoint.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAnchorPointListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAnchorPointListRes, len(res))
		ids := make([]uint64, 0)
		for k, v := range res {
			ids = append(ids, uint64(v.MainUserId))
			listRes.List[k] = &model.AdAnchorPointListRes{
				Id:                 v.Id,
				AnchorPointName:    v.AnchorPointName,
				ToolTitle:          v.ToolTitle,
				AnchorType:         v.AnchorType,
				AndroidAnchorTitle: v.AndroidAnchorTitle,
				IosAnchorTitle:     v.IosAnchorTitle,
				AppTags:            v.AppTags,
				GuideText:          v.GuideText,
				AnchorImageMode:    v.AnchorImageMode,
				GameDescription:    v.GameDescription,
				AppDescription:     v.AppDescription,
				GameCharatoristic:  v.GameCharatoristic,
				OtherDescription:   v.OtherDescription,
				MainUserId:         v.MainUserId,
				CreateTime:         v.CreateTime,
			}
		}
		userIds, err2 := sysService.SysUser().GetUserByIds(ctx, ids)
		if err2 != nil {
			return
		} else {
			for _, v := range listRes.List {
				for _, k := range userIds {
					if v.MainUserId == int64(k.Id) {
						v.CreateUserName = k.UserName
					}
				}
			}
		}
	})
	return
}
func toAdAnchorPointImagesEditReq(req *model.AdAnchorPointImagesListRes) *model.AdAnchorPointImagesEditReq {
	if req != nil {
		return &model.AdAnchorPointImagesEditReq{
			Id:             req.Id,
			AnchorPointId:  req.AnchorPointId,
			Uri:            req.Uri,
			Width:          req.Width,
			Height:         req.Height,
			Loading:        req.Loading,
			ClMaterialId:   req.ClMaterialId,
			ClThumbnailUri: req.ClThumbnailUri,
			ImageType:      req.ImageType,
		}
	}
	return nil
}

func (s *sAdAnchorPoint) GetById(ctx context.Context, id int) (res *model.AdAnchorPointInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAnchorPoint.Ctx(ctx).WithAll().Where(dao.AdAnchorPoint.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		// 查询图片列表
		list, _ := service.AdAnchorPointImages().List(ctx, &model.AdAnchorPointImagesSearchReq{AnchorPointId: gconv.String(id),
			PageReq: commonModel.PageReq{PageNum: 1, PageSize: 1000}})
		res.HeadImageList = make([]*model.AdAnchorPointImagesEditReq, 0)
		res.IconImages = make([]*model.AdAnchorPointImagesEditReq, 0)
		res.AppImages = make([]*model.AdAnchorPointImagesEditReq, 0)
		for _, v := range list.List {
			switch v.ImageType {
			case model.HeadImage:
				res.HeadImageList = append(res.HeadImageList, toAdAnchorPointImagesEditReq(v))
			case model.IconImage:
				res.IconImages = append(res.IconImages, toAdAnchorPointImagesEditReq(v))
			case model.AppImage:
				res.AppImages = append(res.AppImages, toAdAnchorPointImagesEditReq(v))
			}
		}

	})
	return
}

// 推送 锚点
func (s *sAdAnchorPoint) PostAnchorPoint(ctx context.Context, req *model.PostAnchorPointReq) (res *toutiaoModels.NativeAnchorCreateV30ResponseData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		adAnchorPointInfo, innerErr := s.GetById(ctx, req.Id)
		liberr.ErrIsNil(ctx, innerErr, "推送失败")
		tokenRes, err1 := oceanengineService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, req.AdvertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		accessToken := tokenRes.AccessToken
		aid := gconv.Int64(req.AdvertiserId)
		createRes, err3 := advertiser.GetToutiaoApiClient().NativeAnchorCreateV30ApiService.
			AccessToken(accessToken).
			SetRequest(toutiaoModels.NativeAnchorCreateV30Request{
				AdvertiserId: &aid,
				AnchorInfo:   EntityToNativeAnchorCreateV30Request(adAnchorPointInfo, req.InstanceId),
			}).Do()
		liberr.ErrIsNil(ctx, err3)
		res = createRes.Data
		// 创建关联表格数据
		service.AdAnchorPointUpload().Add(ctx, &model.AdAnchorPointUploadAddReq{
			AnchorType:    string(res.AnchorType),
			AnchorPointId: req.Id,
			AnchorId:      res.AnchorId,
			CreateTime:    gtime.Now(),
		})
	})
	return
}

func EntityToNativeAnchorCreateV30Request(req *model.AdAnchorPointInfoRes, InstanceId *int64) *toutiaoModels.NativeAnchorCreateV30RequestAnchorInfo {

	if req.AnchorType == "APP_INTERNET_SERVICE_MICRO_APP" {
		req.AnchorType = "APP_INTERNET_SERVICE"
	}
	switch req.AnchorType {
	case "APP_INTERNET_SERVICE_MICRO_APP", "APP_INTERNET_SERVICE":
		appImages := make([]*toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchorAppImagesInner, 0)
		for _, image := range req.AppImages {
			appImages = append(appImages, &toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchorAppImagesInner{
				Uri:    image.Uri,
				Width:  image.Width,
				Height: image.Height,
			})
		}
		headImages := make([]*toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchorHeadImageListInner, 0)
		for _, image := range req.HeadImageList {
			headImages = append(headImages, &toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchorHeadImageListInner{
				Uri:    image.Uri,
				Width:  image.Width,
				Height: image.Height,
			})
		}
		iconImages := make([]*toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchorIconImagesInner, 0)
		for _, image := range req.IconImages {
			iconImages = append(iconImages, &toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchorIconImagesInner{
				Uri:    image.Uri,
				Width:  image.Width,
				Height: image.Height,
			})
		}

		netServiceAnchor := &toutiaoModels.NativeAnchorCreateV30RequestAnchorInfoNetServiceAnchor{
			AnchorImageMode:    req.AnchorImageMode * 100,
			AndroidAnchorTitle: &req.AndroidAnchorTitle,
			//AndroidDownloadUrl: &req.AnchorType,
			AppDescription: &req.AppDescription,
			AppImages:      appImages,
			//AppOpenUrl:     nil,
			AppTags:        strings.Split(req.AppTags, ","),
			GuideText:      &req.GuideText,
			HeadImageList:  headImages,
			IconImages:     iconImages,
			InstanceId:     InstanceId, // 微信小程序资产id 必填 创建的时候带过来
			IosAnchorTitle: &req.IosAnchorTitle,
			//IosDownloadUrl:     nil,
			NetServiceType: toutiaoModels.MICRO_APP_NativeAnchorCreateV30AnchorInfoNetServiceAnchorNetServiceType,
			//NovelChapter:      nil,
			//PathParam:         nil,
			//PlatformType:      nil,
			//QuickAppId:        nil,
			//WechatExternalUrl: nil,
			//WechatPackageId:   nil,
		}
		return &toutiaoModels.NativeAnchorCreateV30RequestAnchorInfo{
			AnchorType:       toutiaoModels.APP_INTERNET_SERVICE_NativeAnchorCreateV30AnchorInfoAnchorType,
			NetServiceAnchor: netServiceAnchor,
			ToolTitle:        &req.ToolTitle,
		}
	default:
		return nil
	}

}

func (s *sAdAnchorPoint) Add(ctx context.Context, req *model.AdAnchorPointAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		result, err := dao.AdAnchorPoint.Ctx(ctx).Insert(do.AdAnchorPoint{
			AnchorPointName:    req.AnchorPointName,
			ToolTitle:          req.ToolTitle,
			AnchorType:         req.AnchorType,
			AndroidAnchorTitle: req.AndroidAnchorTitle,
			IosAnchorTitle:     req.IosAnchorTitle,
			AppTags:            req.AppTags,
			GuideText:          req.GuideText,
			AnchorImageMode:    req.AnchorImageMode,
			GameDescription:    req.GameDescription,
			AppDescription:     req.AppDescription,
			GameCharatoristic:  req.GameCharatoristic,
			OtherDescription:   req.OtherDescription,
			MainUserId:         req.MainUserId,
			CreateTime:         gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
		lastId, _ := result.LastInsertId()
		addList := make([]*model.AdAnchorPointImagesAddReq, 0)
		if lastId > 0 {
			for _, image := range req.HeadImageList {
				image.AnchorPointId = int(lastId)
				addList = append(addList, image)
			}
			for _, image := range req.IconImages {
				image.AnchorPointId = int(lastId)
				addList = append(addList, image)
			}
			for _, image := range req.AppImages {
				image.AnchorPointId = int(lastId)
				addList = append(addList, image)
			}
		}
		err = service.AdAnchorPointImages().Adds(ctx, addList)
		liberr.ErrIsNil(ctx, err, "添加图片失败")
	})
	return
}

func (s *sAdAnchorPoint) Edit(ctx context.Context, req *model.AdAnchorPointEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPoint.Ctx(ctx).WherePri(req.Id).Update(do.AdAnchorPoint{
			AnchorPointName:    req.AnchorPointName,
			ToolTitle:          req.ToolTitle,
			AnchorType:         req.AnchorType,
			AndroidAnchorTitle: req.AndroidAnchorTitle,
			IosAnchorTitle:     req.IosAnchorTitle,
			AppTags:            req.AppTags,
			GuideText:          req.GuideText,
			AnchorImageMode:    req.AnchorImageMode,
			GameDescription:    req.GameDescription,
			AppDescription:     req.AppDescription,
			GameCharatoristic:  req.GameCharatoristic,
			OtherDescription:   req.OtherDescription,
			MainUserId:         req.MainUserId,
			CreateTime:         req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
		err = service.AdAnchorPointImages().DeleteByAPId(ctx, []int{req.Id})
		liberr.ErrIsNil(ctx, err, "删除图片失败")
		addList := make([]*model.AdAnchorPointImagesEditReq, 0)
		for _, image := range req.HeadImageList {
			addList = append(addList, image)
		}
		for _, image := range req.IconImages {
			addList = append(addList, image)
		}
		for _, image := range req.AppImages {
			addList = append(addList, image)
		}
		err = service.AdAnchorPointImages().Edits(ctx, addList)
		liberr.ErrIsNil(ctx, err, "图片修改失败")
	})
	return
}

func (s *sAdAnchorPoint) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPoint.Ctx(ctx).Delete(dao.AdAnchorPoint.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
		err = service.AdAnchorPointImages().DeleteByAPId(ctx, ids)
		liberr.ErrIsNil(ctx, err, "删除图片失败")
	})
	return
}
