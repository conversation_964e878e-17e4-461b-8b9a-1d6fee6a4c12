// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:30
// 生成路径: internal/app/ad/dao/internal/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdAccountDeptsDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdAccountDeptsDao struct {
	table   string                  // Table is the underlying table name of the DAO.
	group   string                  // Group is the database configuration group name of current DAO.
	columns DzAdAccountDeptsColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdAccountDeptsColumns defines and stores column names for table dz_ad_account_depts.
type DzAdAccountDeptsColumns struct {
	ChannelId string // 渠道id
	DetpId    string // 部门ID
}

var dzAdAccountDeptsColumns = DzAdAccountDeptsColumns{
	ChannelId: "channel_id",
	DetpId:    "detp_id",
}

// NewDzAdAccountDeptsDao creates and returns a new DAO object for table data access.
func NewDzAdAccountDeptsDao() *DzAdAccountDeptsDao {
	return &DzAdAccountDeptsDao{
		group:   "default",
		table:   "dz_ad_account_depts",
		columns: dzAdAccountDeptsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdAccountDeptsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdAccountDeptsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdAccountDeptsDao) Columns() DzAdAccountDeptsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdAccountDeptsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdAccountDeptsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdAccountDeptsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
