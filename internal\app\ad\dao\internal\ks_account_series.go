// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: internal/app/ad/dao/internal/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAccountSeriesDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAccountSeriesDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns KsAccountSeriesColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAccountSeriesColumns defines and stores column names for table ks_account_series.
type KsAccountSeriesColumns struct {
	AdvertiserId string // 用户快手号id/广告主id
	SeriesId     string // 短剧id
	SeriesName   string // 短剧名称
}

var ksAccountSeriesColumns = KsAccountSeriesColumns{
	AdvertiserId: "advertiser_id",
	SeriesId:     "series_id",
	SeriesName:   "series_name",
}

// NewKsAccountSeriesDao creates and returns a new DAO object for table data access.
func NewKsAccountSeriesDao() *KsAccountSeriesDao {
	return &KsAccountSeriesDao{
		group:   "default",
		table:   "ks_account_series",
		columns: ksAccountSeriesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAccountSeriesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAccountSeriesDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAccountSeriesDao) Columns() KsAccountSeriesColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAccountSeriesDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAccountSeriesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAccountSeriesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
