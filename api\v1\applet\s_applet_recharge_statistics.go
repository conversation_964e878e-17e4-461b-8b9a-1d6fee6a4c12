// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-02 18:25:26
// 生成路径: api/v1/applet/s_applet_rechare_statistics.go
// 生成人：len
// desc:小程序充值统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package applet

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
)

// SAppletRechargeStatisticsSearchReq 分页请求参数
type SAppletRechargeStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"小程序充值统计" method:"post" summary:"小程序充值统计列表"`
	commonApi.Author
	model.SAppletRechargeStatisticsSearchReq
}

// SAppletRechareStatisticsSearchRes 列表返回结果
type SAppletRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SAppletRechargeStatisticsSearchRes
}

// SAppletRechargeStatisticsAddReq 添加操作请求参数
type SAppletRechargeStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"小程序充值统计" method:"post" summary:"小程序充值统计添加"`
	commonApi.Author
	*model.SAppletRechargeStatisticsAddReq
}

// SAppletRechargeStatisticsAddRes 添加操作返回结果
type SAppletRechargeStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SAppletRechargeStatisticsEditReq 修改操作请求参数
type SAppletRechargeStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"小程序充值统计" method:"put" summary:"小程序充值统计修改"`
	commonApi.Author
	*model.SAppletRechargeStatisticsEditReq
}

// SAppletRechargeStatisticsEditRes 修改操作返回结果
type SAppletRechargeStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SAppletRechargeStatisticsGetReq 获取一条数据请求
type SAppletRechargeStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"小程序充值统计" method:"get" summary:"获取小程序充值统计信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SAppletRechargeStatisticsGetRes 获取一条数据结果
type SAppletRechargeStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SAppletRechargeStatisticsInfoRes
}

// SAppletRechargeStatisticsDeleteReq 删除数据请求
type SAppletRechargeStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"小程序充值统计" method:"delete" summary:"删除小程序充值统计"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SAppletRechargeStatisticsDeleteRes 删除数据返回
type SAppletRechargeStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
type SAppletRechargeStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"小程序充值统计导出" method:"post" summary:"小程序充值统计导出"`
	commonApi.Author
	model.SAppletRechargeStatisticsSearchReq
}
