// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-14 17:13:10
// 生成路径: api/v1/oceanengine/ad_asset_custom_audience.go
// 生成人：cq
// desc:资产-人群包相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetCustomAudienceSearchReq 分页请求参数
type AdAssetCustomAudienceSearchReq struct {
	g.Meta `path:"/list" tags:"资产-人群包" method:"post" summary:"资产-人群包列表"`
	commonApi.Author
	model.AdAssetCustomAudienceSearchReq
}

// AdAssetCustomAudienceSearchRes 列表返回结果
type AdAssetCustomAudienceSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetCustomAudienceSearchRes
}

// AdAssetCustomAudienceAddReq 添加操作请求参数
type AdAssetCustomAudienceAddReq struct {
	g.Meta `path:"/add" tags:"资产-人群包" method:"post" summary:"资产-人群包添加"`
	commonApi.Author
	*model.AdAssetCustomAudienceAddReq
}

// AdAssetCustomAudienceAddRes 添加操作返回结果
type AdAssetCustomAudienceAddRes struct {
	commonApi.EmptyRes
}

// AdAssetCustomAudienceSyncReq 同步人群包请求参数
type AdAssetCustomAudienceSyncReq struct {
	g.Meta `path:"/sync" tags:"资产-人群包" method:"post" summary:"资产-同步人群包"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds" dc:"广告主ID列表"`
}

// AdAssetCustomAudienceSyncRes 同步人群包返回结果
type AdAssetCustomAudienceSyncRes struct {
	commonApi.EmptyRes
}

// AdAssetCustomAudiencePushReq 推送人群包请求参数
type AdAssetCustomAudiencePushReq struct {
	g.Meta `path:"/push" tags:"资产-人群包" method:"post" summary:"资产-推送人群包"`
	commonApi.Author
	CustomAudienceIds   []string `p:"customAudienceIds" dc:"人群包ID列表"`
	TargetAdvertiserIds []string `p:"targetAdvertiserIds" dc:"目标广告主ID列表"`
}

// AdAssetCustomAudiencePushRes 推送人群包返回结果
type AdAssetCustomAudiencePushRes struct {
	commonApi.EmptyRes
}

// AdAssetCustomAudienceEditReq 修改操作请求参数
type AdAssetCustomAudienceEditReq struct {
	g.Meta `path:"/edit" tags:"资产-人群包" method:"put" summary:"资产-人群包修改"`
	commonApi.Author
	*model.AdAssetCustomAudienceEditReq
}

// AdAssetCustomAudienceEditRes 修改操作返回结果
type AdAssetCustomAudienceEditRes struct {
	commonApi.EmptyRes
}

// AdAssetCustomAudienceGetReq 获取一条数据请求
type AdAssetCustomAudienceGetReq struct {
	g.Meta `path:"/get" tags:"资产-人群包" method:"get" summary:"获取资产-人群包信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetCustomAudienceGetRes 获取一条数据结果
type AdAssetCustomAudienceGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetCustomAudienceInfoRes
}

// AdAssetCustomAudienceDeleteReq 删除数据请求
type AdAssetCustomAudienceDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-人群包" method:"post" summary:"删除资产-人群包"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetCustomAudienceDeleteRes 删除数据返回
type AdAssetCustomAudienceDeleteRes struct {
	commonApi.EmptyRes
}
