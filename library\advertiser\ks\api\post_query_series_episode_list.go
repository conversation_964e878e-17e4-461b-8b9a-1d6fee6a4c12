package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// QuerySeriesEpisodeListService 查询短剧的剧集列表
type QuerySeriesEpisodeListService struct {
	ctx     context.Context
	cfg     *Configuration
	token   string
	Request *QuerySeriesEpisodeListReq
}

// QuerySeriesEpisodeListReq 请求结构体
type QuerySeriesEpisodeListReq struct {
	AdvertiserId int64  `json:"advertiser_id"` // 广告主ID，必填
	Cursor       string `json:"cursor"`        // 游标
	EpisodeName  string `json:"episode_name"`  // 剧集名称
	PageSize     *int   `json:"page_size"`     // 每次请求的数量，当cursor和page_size未传入时，则最多返回5000条
	SeriesId     int64  `json:"series_id"`     // 短剧id 必填
	UserId       int64  `json:"user_id"`       // 快手号id 必填
}

type MapiSeriesEpisodeQueryResV2Snake struct {
	Episodes []MapiEpisodeInfoSnake `json:"episodes,omitempty"` // 短剧剧集信息列表
	Cursor   string                 `json:"cursor,omitempty"`   // 游标
}

// MapiEpisodeInfoSnake 短剧剧集信息
type MapiEpisodeInfoSnake struct {
	Description string `json:"description"` // 剧集描述
	Id          int64  `json:"id"`          // 剧集id
	Name        string `json:"name"`        // 剧集名称
	OrderNo     int    `json:"order_no"`    // 剧集顺序
	SerialId    int64  `json:"serial_id"`   // 短剧id(旧)
	SeriesId    int64  `json:"series_id"`   // 短剧id 接口没有这个字段 给前端用的
	EpisodeId   int64  `json:"episode_id"`  // 剧集id 接口没有这个字段 给前端用的
	KsUserId    int64  `json:"ks_user_id"`  // 快手号id 接口没有这个字段 给前端用的
}

func (r *QuerySeriesEpisodeListService) SetCfg(cfg *Configuration) *QuerySeriesEpisodeListService {
	r.cfg = cfg
	return r
}

func (r *QuerySeriesEpisodeListService) SetReq(req QuerySeriesEpisodeListReq) *QuerySeriesEpisodeListService {
	r.Request = &req
	return r
}

func (r *QuerySeriesEpisodeListService) AccessToken(accessToken string) *QuerySeriesEpisodeListService {
	r.token = accessToken
	return r
}

func (r *QuerySeriesEpisodeListService) Do() (data *KsBaseResp[MapiSeriesEpisodeQueryResV2Snake], err error) {
	localBasePath := r.cfg.BasePath
	localVarPath := localBasePath + "/rest/openapi/gw/dsp/series/episode/list"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&KsBaseResp[MapiSeriesEpisodeQueryResV2Snake]{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(KsBaseResp[MapiSeriesEpisodeQueryResV2Snake])
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/rest/openapi/gw/dsp/series/episode/list解析响应出错: %v\n", err))
	}
	if resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(resp.Message)
	}
}
