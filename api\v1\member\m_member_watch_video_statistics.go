// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-22 18:02:57
// 生成路径: api/v1/member/m_member_watch_video_statistics.go
// 生成人：cq
// desc:看剧任务统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberWatchVideoStatisticsSearchReq 分页请求参数
type MMemberWatchVideoStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"看剧任务统计" method:"post" summary:"看剧任务统计列表"`
	commonApi.Author
	model.MMemberWatchVideoStatisticsSearchReq
}

// MMemberWatchVideoStatisticsSearchRes 列表返回结果
type MMemberWatchVideoStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberWatchVideoStatisticsSearchRes
}

// MMemberWatchVideoStatisticsExportReq 导出请求
type MMemberWatchVideoStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"看剧任务统计" method:"post" summary:"看剧任务统计导出"`
	commonApi.Author
	model.MMemberWatchVideoStatisticsSearchReq
}

// MMemberWatchVideoStatisticsExportRes 导出响应
type MMemberWatchVideoStatisticsExportRes struct {
	commonApi.EmptyRes
}
