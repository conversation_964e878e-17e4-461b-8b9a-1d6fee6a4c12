// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/dao/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adBatchTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adBatchTaskDao struct {
	*internal.AdBatchTaskDao
}

var (
	// AdBatchTask is globally public accessible object for table tools_gen_table operations.
	AdBatchTask = adBatchTaskDao{
		internal.NewAdBatchTaskDao(),
	}
)

// Fill with you ideas below.
