// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-06-05 11:33:12
// 生成路径: internal/app/adx/dao/internal/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdxCreativeDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdxCreativeDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns AdxCreativeColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdxCreativeColumns defines and stores column names for table adx_creative.
type AdxCreativeColumns struct {
	CreativeId    string // 计划ID
	FirstSeen     string // 首次出现日期
	LastSeen      string // 最后出现日期
	MaterialId    string // 素材ID
	MediaId       string // 媒体ID
	MobileType    string // 投放平台1.安卓2.苹果
	NumDate       string // 投放日期及次数
	PositionId    string // 广告位ID
	ProductId     string // 投放产品ID
	PublisherId   string // 公司ID
	TalentAccount string // 达人账号
	TalentName    string // 达人名称
	TargetUrl     string // 落地页
	TargetUrlId   string // 落地页ID
	Title1        string // 使用的标题
	Title2        string // 使用的文案
}

var adxCreativeColumns = AdxCreativeColumns{
	CreativeId:    "creative_id",
	FirstSeen:     "first_seen",
	LastSeen:      "last_seen",
	MaterialId:    "material_id",
	MediaId:       "media_id",
	MobileType:    "mobile_type",
	NumDate:       "num_date",
	PositionId:    "position_id",
	ProductId:     "product_id",
	PublisherId:   "publisher_id",
	TalentAccount: "talent_account",
	TalentName:    "talent_name",
	TargetUrl:     "target_url",
	TargetUrlId:   "target_url_id",
	Title1:        "title1",
	Title2:        "title2",
}

// NewAdxCreativeDao creates and returns a new DAO object for table data access.
func NewAdxCreativeDao() *AdxCreativeDao {
	return &AdxCreativeDao{
		group:   "default",
		table:   "adx_creative",
		columns: adxCreativeColumns,
	}
}
func NewAdxCreativeAnalyticDao() *AdxCreativeDao {
	return &AdxCreativeDao{
		group:   "analyticDB",
		table:   "adx_creative",
		columns: adxCreativeColumns,
	}
} // DB retrieves and returns the underlying raw database management object of current DAO.

func NewAdxCreativeBakAnalyticDao() *AdxCreativeDao {
	return &AdxCreativeDao{
		group:   "analyticDB",
		table:   "adx_creative_bak",
		columns: adxCreativeColumns,
	}
}

func (dao *AdxCreativeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdxCreativeDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdxCreativeDao) Columns() AdxCreativeColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdxCreativeDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdxCreativeDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdxCreativeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
