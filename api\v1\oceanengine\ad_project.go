// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-16 10:32:42
// 生成路径: api/v1/oceanengine/ad_project.go
// 生成人：cq
// desc:巨量项目表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdProjectSearchReq 分页请求参数
type AdProjectSearchReq struct {
	g.Meta `path:"/list" tags:"巨量项目表" method:"post" summary:"巨量项目表列表"`
	commonApi.Author
	model.AdProjectSearchReq
}

// AdProjectSearchRes 列表返回结果
type AdProjectSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectSearchRes
}

// AdProjectExportReq 导出请求
type AdProjectExportReq struct {
	g.Meta `path:"/export" tags:"巨量项目表" method:"get" summary:"巨量项目表导出"`
	commonApi.Author
	model.AdProjectSearchReq
}

// AdProjectExportRes 导出响应
type AdProjectExportRes struct {
	commonApi.EmptyRes
}

// AdProjectAddReq 添加操作请求参数
type AdProjectAddReq struct {
	g.Meta `path:"/add" tags:"巨量项目表" method:"post" summary:"巨量项目表添加"`
	commonApi.Author
	*model.AdProjectAddReq
}

// AdProjectAddRes 添加操作返回结果
type AdProjectAddRes struct {
	commonApi.EmptyRes
}

// AdProjectEditReq 修改操作请求参数
type AdProjectEditReq struct {
	g.Meta `path:"/edit" tags:"巨量项目表" method:"put" summary:"巨量项目表修改"`
	commonApi.Author
	*model.AdProjectEditReq
}

// AdProjectEditRes 修改操作返回结果
type AdProjectEditRes struct {
	commonApi.EmptyRes
}

// AdProjectGetReq 获取一条数据请求
type AdProjectGetReq struct {
	g.Meta `path:"/get" tags:"巨量项目表" method:"get" summary:"获取巨量项目表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdProjectGetRes 获取一条数据结果
type AdProjectGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectInfoRes
}

// AdProjectDeleteReq 删除数据请求
type AdProjectDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量项目表" method:"post" summary:"删除巨量项目表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdProjectDeleteRes 删除数据返回
type AdProjectDeleteRes struct {
	commonApi.EmptyRes
}

// RunSyncAdProjectReq 同步巨量项目请求
type RunSyncAdProjectReq struct {
	g.Meta `path:"/task" tags:"巨量项目表" method:"post" summary:"同步巨量项目"`
	commonApi.Author
	*model.SyncAdProjectReq
}

// RunSyncAdProjectRes 同步巨量项目返回
type RunSyncAdProjectRes struct {
	commonApi.EmptyRes
}

// AdProjectStatusUpdateReq 修改项目状态请求
type AdProjectStatusUpdateReq struct {
	g.Meta `path:"/status/update" tags:"巨量项目表" method:"post" summary:"修改项目状态"`
	commonApi.Author
	*model.AdProjectStatusUpdateReq
}

// AdProjectStatusUpdateRes 修改项目状态返回
type AdProjectStatusUpdateRes struct {
	commonApi.EmptyRes
}
