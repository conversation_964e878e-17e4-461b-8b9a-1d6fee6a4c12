// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-09-02 14:44:06
// 生成路径: internal/app/applet/controller/sys_active_pop_up_window.go
// 生成人：cyao
// desc:活动弹出窗口
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sysActivePopUpWindowController struct {
	systemController.BaseController
}

var SysActivePopUpWindow = new(sysActivePopUpWindowController)

// List 列表
func (c *sysActivePopUpWindowController) List(ctx context.Context, req *applet.SysActivePopUpWindowSearchReq) (res *applet.SysActivePopUpWindowSearchRes, err error) {
	res = new(applet.SysActivePopUpWindowSearchRes)
	res.SysActivePopUpWindowSearchRes, err = service.SysActivePopUpWindow().List(ctx, &req.SysActivePopUpWindowSearchReq)
	return
}

// Get 获取活动弹出窗口
func (c *sysActivePopUpWindowController) Get(ctx context.Context, req *applet.SysActivePopUpWindowGetReq) (res *applet.SysActivePopUpWindowGetRes, err error) {
	res = new(applet.SysActivePopUpWindowGetRes)
	res.SysActivePopUpWindowInfoRes, err = service.SysActivePopUpWindow().GetById(ctx, req.Id)
	return
}

// Add 添加活动弹出窗口
func (c *sysActivePopUpWindowController) Add(ctx context.Context, req *applet.SysActivePopUpWindowAddReq) (res *applet.SysActivePopUpWindowAddRes, err error) {
	err = service.SysActivePopUpWindow().Add(ctx, req.SysActivePopUpWindowAddReq)
	return
}

// Edit 修改活动弹出窗口
func (c *sysActivePopUpWindowController) Edit(ctx context.Context, req *applet.SysActivePopUpWindowEditReq) (res *applet.SysActivePopUpWindowEditRes, err error) {
	err = service.SysActivePopUpWindow().Edit(ctx, req.SysActivePopUpWindowEditReq)
	return
}

// Delete 删除活动弹出窗口
func (c *sysActivePopUpWindowController) Delete(ctx context.Context, req *applet.SysActivePopUpWindowDeleteReq) (res *applet.SysActivePopUpWindowDeleteRes, err error) {
	err = service.SysActivePopUpWindow().Delete(ctx, req.Ids)
	return
}
