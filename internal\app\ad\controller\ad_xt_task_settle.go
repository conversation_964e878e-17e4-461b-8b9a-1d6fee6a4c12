// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: internal/app/ad/controller/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"time"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type adXtTaskSettleController struct {
	systemController.BaseController
}

var AdXtTaskSettle = new(adXtTaskSettleController)

// List 列表
func (c *adXtTaskSettleController) List(ctx context.Context, req *ad.AdXtTaskSettleSearchReq) (res *ad.AdXtTaskSettleSearchRes, err error) {
	res = new(ad.AdXtTaskSettleSearchRes)
	res.AdXtTaskSettleSearchRes, err = service.AdXtTaskSettle().List(ctx, &req.AdXtTaskSettleSearchReq)
	return
}

// Pull 获取星图任务列表和任务详情
func (c *adXtTaskSettleController) Pull(ctx context.Context, req *ad.AdXtTaskSettlePullReq) (res *ad.AdXtTaskSettlePullRes, err error) {
	res = new(ad.AdXtTaskSettlePullRes)
	innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()
	res.Count, err = service.AdXtTaskSettle().Pull(innerContext, req.AdvertiserId)
	return
}

func (c *adXtTaskSettleController) PullDetail(ctx context.Context, req *ad.AdXtTaskSettlePullDetailReq) (res *ad.AdXtTaskSettlePullDetailRes, err error) {
	res = new(ad.AdXtTaskSettlePullDetailRes)
	err = service.AdXtTaskSettle().PullDetail(ctx, req.AdvertiserId)
	return
}

// Export 导出excel
func (c *adXtTaskSettleController) Export(ctx context.Context, req *ad.AdXtTaskSettleExportReq) (res *ad.AdXtTaskSettleExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdXtTaskSettleListRes
		//表头
		tableHead = []interface{}{"ID", "账户ID", "账户名", "任务ID", "任务名称", "视频唯一ID", "作者ID", "用户平台ID", "内容提供方ID", "视频标题", "视频分享链接", "作者昵称", "发布时间(unix时间戳)", "安卓端激活数", "iOS端激活数", "评论数", "点赞数", "有效点赞数", "播放次数", "有效播放次数", "分享次数", "推广次数", "组件点击次数", "预估广告收入(JSON格式)", "预估销售额(JSON格式)", "播放数据(JSON格式)", "广告分成结算(JSON格式)", "CPS结算(JSON格式)", "小时级IAA消耗", "小时级IAP消耗", "打赏金额", "相关性审核结果", "打赏等级"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRs, _ := service.AdXtTaskSettle().List(ctx, &req.AdXtTaskSettleSearchReq)

		if listRs == nil || len(listRs.List) == 0 {
			break
		} else {
			listData = listRs.List
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.Id,
				v.AdvertiserId,
				v.AdvertiserNick,
				v.TaskId,
				v.TaskName,
				gconv.String(v.ItemId),
				gconv.String(v.AuthorId),
				gconv.String(v.Uid),
				gconv.String(v.ProviderId),
				v.Title,
				v.Link,
				v.AuthorNickname,
				gtime.NewFromTimeStamp(int64(v.ReleaseTime)).Format("Y-m-d H:i:s"),
				v.AndroidActivateCnt,
				v.IosActivateCnt,
				v.CommentCnt,
				v.LikeCnt,
				v.ValidLikeCnt,
				v.PlayVv,
				v.ValidPlayVv,
				v.ShareCnt,
				v.PromoteCnt,
				v.ComponentClickCnt,
				libUtils.ToRound(gconv.Float64(v.EstAdCost)/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(gconv.Float64(v.EstSales)/100, 2, libUtils.RoundHalfEven),
				v.Play,
				libUtils.ToRound(gconv.Float64(v.SettleAdShare)/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(gconv.Float64(v.SettleCps)/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(v.IaaCostHour/100, 2, libUtils.RoundHalfEven),
				libUtils.ToRound(v.IapCostHour/100, 2, libUtils.RoundHalfEven),
				v.RewardAmount,
				v.RelevanceAuditResult,
				v.RewardLevel,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("星图任务结算数据")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取星图任务结算数据
func (c *adXtTaskSettleController) Get(ctx context.Context, req *ad.AdXtTaskSettleGetReq) (res *ad.AdXtTaskSettleGetRes, err error) {
	res = new(ad.AdXtTaskSettleGetRes)
	res.AdXtTaskSettleInfoRes, err = service.AdXtTaskSettle().GetById(ctx, req.Id)
	return
}

// Add 添加星图任务结算数据
func (c *adXtTaskSettleController) Add(ctx context.Context, req *ad.AdXtTaskSettleAddReq) (res *ad.AdXtTaskSettleAddRes, err error) {
	err = service.AdXtTaskSettle().Add(ctx, req.AdXtTaskSettleAddReq)
	return
}

// Edit 修改星图任务结算数据
func (c *adXtTaskSettleController) Edit(ctx context.Context, req *ad.AdXtTaskSettleEditReq) (res *ad.AdXtTaskSettleEditRes, err error) {
	err = service.AdXtTaskSettle().Edit(ctx, req.AdXtTaskSettleEditReq)
	return
}

// Delete 删除星图任务结算数据
func (c *adXtTaskSettleController) Delete(ctx context.Context, req *ad.AdXtTaskSettleDeleteReq) (res *ad.AdXtTaskSettleDeleteRes, err error) {
	err = service.AdXtTaskSettle().Delete(ctx, req.Ids)
	return
}
