// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/service/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IDzAdUserInfo interface {
	List(ctx context.Context, req *model.DzAdUserInfoSearchReq) (res *model.DzAdUserInfoSearchRes, err error)
	GetExportData(ctx context.Context, req *model.DzAdUserInfoSearchReq) (listRes []*model.DzAdUserInfoInfoRes, err error)
	GetByUserId(ctx context.Context, UserId string) (res *model.DzAdUserInfoInfoRes, err error)
	Add(ctx context.Context, req *model.DzAdUserInfoAddReq) (err error)
	Edit(ctx context.Context, req *model.DzAdUserInfoEditReq) (err error)
	Delete(ctx context.Context, UserId []string) (err error)
}

var localDzAdUserInfo IDzAdUserInfo

func DzAdUserInfo() IDzAdUserInfo {
	if localDzAdUserInfo == nil {
		panic("implement not found for interface IDzAdUserInfo, forgot register?")
	}
	return localDzAdUserInfo
}

func RegisterDzAdUserInfo(i IDzAdUserInfo) {
	localDzAdUserInfo = i
}
