// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-21 14:30:52
// 生成路径: internal/app/ad/model/entity/ad_xt_task_settle_daily.go
// 生成人：cyao
// desc:星图结算数据分天
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdXtTaskSettleDaily is the golang structure for table ad_xt_task_settle_daily.
type AdXtTaskSettleDaily struct {
	gmeta.Meta    `orm:"table:ad_xt_task_settle_daily"`
	Id            int64       `orm:"id,primary" json:"id"`                 // ID
	AdvertiserId  string      `orm:"advertiser_id" json:"advertiserId"`    // 账户ID对应star_id
	TaskId        string      `orm:"task_id" json:"taskId"`                // 任务ID
	ItemId        int64       `orm:"item_id" json:"itemId"`                // 视频唯一ID
	AuthorId      int64       `orm:"author_id" json:"authorId"`            // 作者ID
	Uid           int64       `orm:"uid" json:"uid"`                       // 用户平台ID
	ProviderId    int64       `orm:"provider_id" json:"providerId"`        // 内容提供方ID
	PDate         *gtime.Time `orm:"p_date" json:"pDate"`                  // 日期
	EstSales      int64       `orm:"est_sales" json:"estSales"`            // 当天产生的预估付费流水金额
	SettleCps     int64       `orm:"settle_cps" json:"settleCps"`          // 当天发放的达人付费分佣金额
	EstAdCost     int64       `orm:"est_ad_cost" json:"estAdCost"`         // 当天产生的预估广告消耗金额
	SettleAdShare int64       `orm:"settle_ad_share" json:"settleAdShare"` // 当天发放的达人广告分成金额
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`          // 创建时间
}
