// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-15 17:09:24
// 生成路径: api/v1/channel/m_member_ad_callback_summary.go
// 生成人：cq
// desc:广告回传汇总相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// MMemberAdCallbackSummarySearchReq 分页请求参数
type MMemberAdCallbackSummarySearchReq struct {
	g.Meta `path:"/list" tags:"广告回传汇总" method:"get" summary:"广告回传汇总列表"`
	commonApi.Author
	model.MMemberAdCallbackSummarySearchReq
}

// MMemberAdCallbackSummarySearchRes 列表返回结果
type MMemberAdCallbackSummarySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberAdCallbackSummarySearchRes
}

// MMemberAdCallbackSummaryExportReq 导出请求
type MMemberAdCallbackSummaryExportReq struct {
	g.Meta `path:"/export" tags:"广告回传汇总" method:"get" summary:"广告回传汇总导出"`
	commonApi.Author
	model.MMemberAdCallbackSummarySearchReq
}

// MMemberAdCallbackSummaryExportRes 导出响应
type MMemberAdCallbackSummaryExportRes struct {
	commonApi.EmptyRes
}

// MMemberAdCallbackSummaryEditReq 修改操作请求参数
type MMemberAdCallbackSummaryEditReq struct {
	g.Meta `path:"/edit" tags:"广告回传汇总" method:"put" summary:"广告回传汇总修改"`
	commonApi.Author
	*model.MMemberAdCallbackSummaryEditReq
}

// MMemberAdCallbackSummaryEditRes 修改操作返回结果
type MMemberAdCallbackSummaryEditRes struct {
	commonApi.EmptyRes
}
