// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-16 11:16:19
// 生成路径: internal/app/ad/model/entity/fq_ad_account_depts.go
// 生成人：gfast
// desc:番茄账号权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdAccountDepts is the golang structure for table fq_ad_account_depts.
type FqAdAccountDepts struct {
	gmeta.Meta    `orm:"table:fq_ad_account_depts"`
	DistributorId int64 `orm:"distributor_id,primary" json:"distributorId"` // 渠道ID
	DetpId        int   `orm:"detp_id,primary" json:"detpId"`               // 部门ID
}
