// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: internal/app/ad/model/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdXtTaskInfoRes is the golang structure for table ad_xt_task.
type AdXtTaskInfoRes struct {
	gmeta.Meta              `orm:"table:ad_xt_task"`
	Id                      int64       `orm:"id,primary" json:"id" dc:"ID"`
	AdvertiserId            string      `orm:"advertiser_id" json:"advertiserId" dc:"账户ID 对应 star_id "`
	TaskId                  string      `orm:"task_id" json:"taskId" dc:"任务ID"`
	TaskName                string      `orm:"task_name" json:"taskName" dc:"任务名称"`
	UniversalSettlementType int         `orm:"universal_settlement_type" json:"universalSettlementType" dc:"结算方式枚举 16 19 33 混合分成"` //结算方式枚举 16 19 33 混合分成
	Attachments             string      `orm:"attachments" json:"attachments" dc:"参考素材"`                                           // 参考素材
	AuthorTaskName          string      `orm:"author_task_name" json:"authorTaskName" dc:"达人侧任务名称"`
	TaskIcon                string      `orm:"task_icon" json:"taskIcon" dc:"任务图标"`
	TaskHeadImage           string      `orm:"task_head_image" json:"taskHeadImage" dc:"任务头图"`
	StartTime               *gtime.Time `orm:"start_time" json:"startTime" dc:"投稿开始时间"`
	EndTime                 *gtime.Time `orm:"end_time" json:"endTime" dc:"任务截止时间"`
	SampleVideo             string      `orm:"sample_video" json:"sampleVideo" dc:"示例视频"`
	MicroAppId              string      `orm:"micro_app_id" json:"microAppId" dc:"小程序ID"`
	StartPage               string      `orm:"start_page" json:"startPage" dc:"小程序落地页地址"`
	AnchorTitle             string      `orm:"anchor_title" json:"anchorTitle" dc:"组件标题"`
	CommissionType          string      `orm:"commission_type" json:"commissionType" dc:"结算方式0unkonw 1广告分成 2cps支付分佣 3cps绑定分佣 7混合分成"`
	AuthorScope             string      `orm:"author_scope" json:"authorScope" dc:"达人定向范围，任务定向类型判断规则参考"`
	ProviderScope           string      `orm:"provider_scope" json:"providerScope" dc:"服务商定向范围，任务定向类型判断规则参考"`
	CommissionRate          string      `orm:"commission_rate" json:"commissionRate" dc:"分佣比例，适用于付费分佣结算和广告分成结算"`
	AdCommissionRate        float64     `orm:"ad_commission_rate" json:"adCommissionRate" dc:"广告分成比例"`
	PayCommissionRate       float64     `orm:"pay_commission_rate" json:"payCommissionRate" dc:"付费分佣比例"`
	AccountDivideDay        string      `orm:"account_divide_day" json:"accountDivideDay" dc:"最长分账周期"`
	DemandDesc              string      `orm:"demand_desc" json:"demandDesc" dc:"任务介绍"`
	OmTaskStatus            int         `orm:"om_task_status" json:"omTaskStatus" dc:"任务状态：1审核中 2审核失败 3进行中 4下架 4计费中 6已取消 7已结束 8已关闭"`
	OmTaskTag               string      `orm:"om_task_tag" json:"omTaskTag" dc:"任务标签"`
	AuthorList              string      `orm:"author_list" json:"authorList" dc:""`
	CreatedAt               *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`
	UpdatedAt               *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`
	DeletedAt               *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`
}

type XTAdKSService struct {
	AppId  int64  `json:"appId"`
	Secret string `json:"secret"`
}

type AdAndTaskId struct {
	TaskId       string `orm:"task_id" json:"taskId" dc:"任务ID"`
	AdvertiserId string `orm:"advertiser_id" json:"advertiserId" dc:"账户ID 对应 star_id "`
}

type AdXtTaskListRes struct {
	Id                      int64       `json:"id" dc:"ID"`
	AdvertiserId            string      `json:"advertiserId" dc:"账户ID 对应 star_id "`
	AdvertiserName          string      `json:"advertiserName" dc:"账户名称"`
	TaskId                  string      `json:"taskId" dc:"任务ID"`
	TaskName                string      `json:"taskName" dc:"任务名称"`
	UniversalSettlementType int         `json:"universalSettlementType" dc:"结算方式枚举 16 19 33 混合分成"` //结算方式枚举 16 19 33 混合分成
	Attachments             string      `json:"attachments" dc:"参考素材"`
	AuthorTaskName          string      `json:"authorTaskName" dc:"达人侧任务名称"`
	TaskIcon                string      `json:"taskIcon" dc:"任务图标"`
	TaskHeadImage           string      `json:"taskHeadImage" dc:"任务头图"`
	StartTime               *gtime.Time `json:"startTime" dc:"投稿开始时间"`
	EndTime                 *gtime.Time `json:"endTime" dc:"任务截止时间"`
	SampleVideo             string      `json:"sampleVideo" dc:"示例视频"`
	MicroAppId              string      `json:"microAppId" dc:"小程序ID"`
	AppName                 string      `json:"appName" dc:"小程序名称"` // 小程序名称
	StartPage               string      `json:"startPage" dc:"小程序落地页地址"`
	AnchorTitle             string      `json:"anchorTitle" dc:"组件标题"`
	CommissionType          string      `json:"commissionType" dc:"结算方式0unkonw"`
	AuthorScope             string      `json:"authorScope" dc:"达人定向范围，任务定向类型判断规则参考"`
	ProviderScope           string      `json:"providerScope" dc:"服务商定向范围，任务定向类型判断规则参考"`
	CommissionRate          string      `json:"commissionRate" dc:"分佣比例，适用于付费分佣结算和广告分成结算"`
	AdCommissionRate        float64     `json:"adCommissionRate" dc:"广告分成比例"`
	PayCommissionRate       float64     `json:"payCommissionRate" dc:"付费分佣比例"`
	AccountDivideDay        string      `json:"accountDivideDay" dc:"最长分账周期"`
	DemandDesc              string      `json:"demandDesc" dc:"任务介绍"`
	OmTaskStatus            int         `json:"omTaskStatus" dc:"任务状态：1审核中 2审核失败 3进行中 4下架 4计费中 6已取消 7已结束 8已关闭"`
	OmTaskTag               string      `json:"omTaskTag" dc:"任务标签"`
	AuthorList              string      `json:"authorList" dc:""`
	CreatedAt               *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdXtTaskSearchReq 分页请求参数
type AdXtTaskSearchReq struct {
	comModel.PageReq
	Id                      string `p:"id" dc:"ID"`                                                                 //ID
	AdvertiserId            string `p:"advertiserId" dc:"账户ID 对应 star_id"`                                          //账户ID 对应 star_id
	AdvertiserNick          string `p:"advertiserNick" dc:"账户名称"`                                                   //账户名称
	TaskId                  string `p:"taskId" dc:"任务ID"`                                                           //任务ID
	UniversalSettlementType int    `p:"universalSettlementType" dc:"结算方式枚举 16 19 33 混合分成"`                          //结算方式枚举 16 19 33 混合分成
	TaskName                string `p:"taskName" dc:"任务名称"`                                                         //任务名称
	Attachments             string `p:"attachments" dc:"参考素材"`                                                      //参考素材
	AuthorTaskName          string `p:"authorTaskName" dc:"达人侧任务名称"`                                                //达人侧任务名称
	TaskIcon                string `p:"taskIcon" dc:"任务图标"`                                                         //任务图标
	TaskHeadImage           string `p:"taskHeadImage" dc:"任务头图"`                                                    //任务头图
	StartTime               string `p:"startTime" v:"startTime@datetime#投稿开始时间需为YYYY-MM-DD hh:mm:ss格式" dc:"投稿开始时间"` //投稿开始时间
	EndTime                 string `p:"endTime" v:"endTime@datetime#任务截止时间需为YYYY-MM-DD hh:mm:ss格式" dc:"任务截止时间"`     //任务截止时间
	SampleVideo             string `p:"sampleVideo" dc:"示例视频"`                                                      //示例视频
	MicroAppId              string `p:"microAppId" v:"microAppId@integer#小程序ID需为整数" dc:"小程序ID"`                     //小程序ID
	StartPage               string `p:"startPage" dc:"小程序落地页地址"`
	AnchorTitle             string `p:"anchorTitle" dc:"组件标题"`
	CommissionType          string `p:"commissionType" dc:"结算方式0unkonw 1广告分成 2cps支付分佣 3cps绑定分佣 7混合分成"`
	AuthorScope             string `p:"authorScope" dc:"达人定向范围，任务定向类型判断规则参考"`
	ProviderScope           string `p:"providerScope" dc:"服务商定向范围，任务定向类型判断规则参考"`
	CommissionRate          string `p:"commissionRate" dc:"分佣比例，适用于付费分佣结算和广告分成结算"`
	AdCommissionRate        string `p:"adCommissionRate" v:"adCommissionRate@float#广告分成比例需为浮点数" dc:"广告分成比例"`
	PayCommissionRate       string `p:"payCommissionRate" v:"payCommissionRate@float#付费分佣比例需为浮点数" dc:"付费分佣比例"`
	AccountDivideDay        string `p:"accountDivideDay" dc:"最长分账周期"`
	DemandDesc              string `p:"demandDesc" dc:"任务介绍"`
	OmTaskStatus            string `p:"omTaskStatus" dc:""`
	OmTaskTag               string `p:"omTaskTag" dc:"任务标签"`
	AuthorList              string `p:"authorList" dc:""`
	CreatedAt               string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`
}

// AdXtTaskSearchRes 列表返回结果
type AdXtTaskSearchRes struct {
	comModel.ListRes
	List []*AdXtTaskListRes `json:"list"`
}

// AdXtTaskAddReq 添加操作请求参数
type AdXtTaskAddReq struct {
	AdvertiserId            string      `p:"advertiserId"  dc:"账户ID 对应 star_id "`
	TaskId                  string      `p:"taskId"  dc:"任务ID"`
	TaskName                string      `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	UniversalSettlementType int         `p:"universalSettlementType" dc:"结算方式枚举 16 19 33 混合分成"` //结算方式枚举 16 19 33 混合分成
	Attachments             string      `p:"attachments"  dc:"参考素材"`
	AuthorTaskName          string      `p:"authorTaskName" v:"required#达人侧任务名称不能为空" dc:"达人侧任务名称"`
	TaskIcon                string      `p:"taskIcon"  dc:"任务图标"`
	TaskHeadImage           string      `p:"taskHeadImage"  dc:"任务头图"`
	StartTime               *gtime.Time `p:"startTime"  dc:"投稿开始时间"`
	EndTime                 *gtime.Time `p:"endTime"  dc:"任务截止时间"`
	SampleVideo             string      `p:"sampleVideo"  dc:"示例视频"`
	MicroAppId              string      `p:"microAppId"  dc:"小程序ID"`
	StartPage               string      `p:"startPage"  dc:"小程序落地页地址"`
	AnchorTitle             string      `p:"anchorTitle"  dc:"组件标题"`
	CommissionType          string      `p:"commissionType"  dc:"结算方式0unkonw 1广告分成 2cps支付分佣 3cps绑定分佣 7混合分成"`
	AuthorScope             string      `p:"authorScope"  dc:"达人定向范围，任务定向类型判断规则参考"`
	ProviderScope           string      `p:"providerScope"  dc:"服务商定向范围，任务定向类型判断规则参考"`
	CommissionRate          string      `p:"commissionRate"  dc:"分佣比例，适用于付费分佣结算和广告分成结算"`
	AdCommissionRate        float64     `p:"adCommissionRate"  dc:"广告分成比例"`
	PayCommissionRate       float64     `p:"payCommissionRate"  dc:"付费分佣比例"`
	AccountDivideDay        string      `p:"accountDivideDay"  dc:"最长分账周期"`
	DemandDesc              string      `p:"demandDesc"  dc:"任务介绍"`
	OmTaskStatus            int         `p:"omTaskStatus"   dc:""`
	OmTaskTag               string      `p:"omTaskTag"  dc:"任务标签"`
	AuthorList              string      `p:"authorList"  dc:""`
}

// AdXtTaskEditReq 修改操作请求参数
type AdXtTaskEditReq struct {
	Id                      int64       `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AdvertiserId            string      `p:"advertiserId"  dc:"账户ID 对应 star_id "`
	TaskId                  string      `p:"taskId"  dc:"任务ID"`
	TaskName                string      `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	UniversalSettlementType int         `p:"universalSettlementType" dc:"结算方式枚举 16 19 33 混合分成"` //结算方式枚举 16 19 33 混合分成
	Attachments             string      `p:"attachments"  dc:"参考素材"`
	AuthorTaskName          string      `p:"authorTaskName" v:"required#达人侧任务名称不能为空" dc:"达人侧任务名称"`
	TaskIcon                string      `p:"taskIcon"  dc:"任务图标"`
	TaskHeadImage           string      `p:"taskHeadImage"  dc:"任务头图"`
	StartTime               *gtime.Time `p:"startTime"  dc:"投稿开始时间"`
	EndTime                 *gtime.Time `p:"endTime"  dc:"任务截止时间"`
	SampleVideo             string      `p:"sampleVideo"  dc:"示例视频"`
	MicroAppId              string      `p:"microAppId"  dc:"小程序ID"`
	StartPage               string      `p:"startPage"  dc:"小程序落地页地址"`
	AnchorTitle             string      `p:"anchorTitle"  dc:"组件标题"`
	CommissionType          string      `p:"commissionType"  dc:"结算方式0unkonw 1广告分成 2cps支付分佣 3cps绑定分佣 7混合分成"`
	AuthorScope             string      `p:"authorScope"  dc:"达人定向范围，任务定向类型判断规则参考"`
	ProviderScope           string      `p:"providerScope"  dc:"服务商定向范围，任务定向类型判断规则参考"`
	CommissionRate          string      `p:"commissionRate"  dc:"分佣比例，适用于付费分佣结算和广告分成结算"`
	AdCommissionRate        float64     `p:"adCommissionRate"  dc:"广告分成比例"`
	PayCommissionRate       float64     `p:"payCommissionRate"  dc:"付费分佣比例"`
	AccountDivideDay        string      `p:"accountDivideDay"  dc:"最长分账周期"`
	DemandDesc              string      `p:"demandDesc"  dc:"任务介绍"`
	OmTaskStatus            int         `p:"omTaskStatus"   dc:""`
	OmTaskTag               string      `p:"omTaskTag"  dc:"任务标签"`
	AuthorList              string      `p:"authorList"  dc:""`
}
