// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:34:01
// 生成路径: api/v1/adx/adx_publisher.go
// 生成人：cq
// desc:ADX公司信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxPublisherSearchReq 分页请求参数
type AdxPublisherSearchReq struct {
	g.Meta `path:"/list" tags:"ADX公司信息表" method:"post" summary:"ADX公司信息表列表"`
	commonApi.Author
	model.AdxPublisherSearchReq
}

// AdxPublisherSearchRes 列表返回结果
type AdxPublisherSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxPublisherSearchRes
}

// AdxPublisherExportReq 导出请求
type AdxPublisherExportReq struct {
	g.Meta `path:"/export" tags:"ADX公司信息表" method:"post" summary:"ADX公司信息表导出"`
	commonApi.Author
	model.AdxPublisherSearchReq
}

// AdxPublisherExportRes 导出响应
type AdxPublisherExportRes struct {
	commonApi.EmptyRes
}

// AdxPublisherAddReq 添加操作请求参数
type AdxPublisherAddReq struct {
	g.Meta `path:"/add" tags:"ADX公司信息表" method:"post" summary:"ADX公司信息表添加"`
	commonApi.Author
	*model.AdxPublisherAddReq
}

// AdxPublisherAddRes 添加操作返回结果
type AdxPublisherAddRes struct {
	commonApi.EmptyRes
}

// AdxPublisherEditReq 修改操作请求参数
type AdxPublisherEditReq struct {
	g.Meta `path:"/edit" tags:"ADX公司信息表" method:"put" summary:"ADX公司信息表修改"`
	commonApi.Author
	*model.AdxPublisherEditReq
}

// AdxPublisherEditRes 修改操作返回结果
type AdxPublisherEditRes struct {
	commonApi.EmptyRes
}

// AdxPublisherGetReq 获取一条数据请求
type AdxPublisherGetReq struct {
	g.Meta `path:"/get" tags:"ADX公司信息表" method:"get" summary:"获取ADX公司信息表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdxPublisherGetRes 获取一条数据结果
type AdxPublisherGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxPublisherInfoRes
}

// AdxPublisherDeleteReq 删除数据请求
type AdxPublisherDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX公司信息表" method:"post" summary:"删除ADX公司信息表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdxPublisherDeleteRes 删除数据返回
type AdxPublisherDeleteRes struct {
	commonApi.EmptyRes
}

// AdxMaterialSyncReq 同步ADX公司请求参数
type AdxPublisherSyncReq struct {
	g.Meta `path:"/sync" tags:"ADX公司信息表" method:"post" summary:"同步ADX公司"`
	commonApi.Author
	model.AdxPublisherSearchReq
}

// AdxPublisherSyncRes 同步ADX产品返回结果
type AdxPublisherSyncRes struct {
	g.Meta `mime:"application/json"`
}
