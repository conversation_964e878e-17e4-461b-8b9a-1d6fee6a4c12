// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-21 14:30:52
// 生成路径: internal/app/ad/logic/ad_xt_task_settle_daily.go
// 生成人：cyao
// desc:星图结算数据分天
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdXtTaskSettleDaily(New())
}

func New() service.IAdXtTaskSettleDaily {
	return &sAdXtTaskSettleDaily{}
}

type sAdXtTaskSettleDaily struct{}

func (s *sAdXtTaskSettleDaily) List(ctx context.Context, req *model.AdXtTaskSettleDailySearchReq) (listRes *model.AdXtTaskSettleDailySearchRes, err error) {
	listRes = new(model.AdXtTaskSettleDailySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtTaskSettleDaily.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().TaskId+" = ?", req.TaskId)
		}
		if len(req.TaskIds) > 0 {
			m = m.WhereIn(dao.AdXtTaskSettleDaily.Columns().TaskId, req.TaskIds)
			// 刷一下详情的数据 todo
			libUtils.SafeGo(func() {
				innerCtx, cancel := context.WithCancel(context.Background())
				defer cancel()
				taskList, err := service.AdXtTask().GetByTaskIds(innerCtx, req.TaskIds)
				if err != nil {
					return
				}
				if taskList != nil && len(taskList) > 0 {
					for _, taskInfo := range taskList {
						accessToken, _ := service.AdXtAccount().GetAccessTokenByAdvertiserId(innerCtx, taskInfo.AdvertiserId)
						err = service.AdXtTaskSettle().SaveAdXtTaskSettleByTaskId(innerCtx, accessToken, taskInfo.AdvertiserId, []string{taskInfo.TaskId})
						if err != nil {
							g.Log().Error(ctx, err, "保存AdXtTaskSettleByTaskId失败")
						}
					}
				}

			})

		}
		if req.ItemId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().ItemId+" = ?", gconv.Int64(req.ItemId))
		}
		if req.AuthorId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().AuthorId+" = ?", gconv.Int64(req.AuthorId))
		}
		if req.Uid != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().Uid+" = ?", gconv.Int64(req.Uid))
		}
		if req.ProviderId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().ProviderId+" = ?", gconv.Int64(req.ProviderId))
		}
		if req.PDate != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().PDate+" = ?", gconv.Time(req.PDate))
		}
		if req.EstSales > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().EstSales+" = ?", req.EstSales)
		}
		if req.SettleCps > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().SettleCps+" = ?", req.SettleCps)
		}
		if req.SettleAdShare > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().SettleAdShare+" = ?", req.SettleAdShare)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().CreatedAt+" >=? AND "+dao.AdXtTaskSettleDaily.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if len(req.StartDate) > 0 && len(req.EndDate) > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().PDate+" >= ?", req.StartDate)
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().PDate+" <= ?", req.EndDate)
		}

		var summary = new(model.AdXtTaskSettleSummary)
		m.Fields(" SUM(est_sales) as estSales  ,SUM(settle_ad_share) as settleAdShare ,SUM(est_ad_cost) as estAdCost , SUM(settle_cps) as settleCps").
			Scan(&summary)
		listRes.Summary = summary

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.AdXtTaskSettleDailyListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")

		tIds := make([]string, 0)
		itemIds := make([]int64, 0)
		aidList := make([]string, 0)
		listRes.List = make([]*model.AdXtTaskSettleDailyListRes, len(res))

		for k, v := range res {
			tIds = append(tIds, v.TaskId)
			itemIds = append(itemIds, gconv.Int64(v.ItemId))
			aidList = append(aidList, v.AdvertiserId)
			listRes.List[k] = &model.AdXtTaskSettleDailyListRes{
				Id:            v.Id,
				AdvertiserId:  v.AdvertiserId,
				TaskId:        v.TaskId,
				ItemId:        v.ItemId,
				AuthorId:      v.AuthorId,
				Uid:           v.Uid,
				ProviderId:    v.ProviderId,
				PDate:         v.PDate,
				EstSales:      v.EstSales,
				SettleCps:     v.SettleCps,
				EstAdCost:     v.EstAdCost,
				SettleAdShare: v.SettleAdShare,
				CreatedAt:     v.CreatedAt,
			}
		}
		aList, _ := service.AdXtAccount().GetByAIds(ctx, aidList)
		tList, _ := service.AdXtTask().GetByTaskIds(ctx, tIds)
		itemList, _ := service.AdXtTaskSettle().GetByItemIds(ctx, itemIds)
		for _, item := range listRes.List {
			for _, adInfo := range tList {
				if item.TaskId == adInfo.TaskId {
					item.TaskName = adInfo.TaskName
				}
			}
			for _, taskInfo := range itemList {
				if item.ItemId == gconv.String(taskInfo.ItemId) {
					item.AuthorNickname = taskInfo.AuthorNickname
					item.Title = taskInfo.Title

				}
			}
			for _, adInfo := range aList {
				if item.AdvertiserId == adInfo.AdvertiserId {
					item.AdvertiserNick = adInfo.AdvertiserNick
				}
			}
		}

	})
	return
}

func (s *sAdXtTaskSettleDaily) GetExportData(ctx context.Context, req *model.AdXtTaskSettleDailySearchReq) (listRes []*model.AdXtTaskSettleDailyInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdXtTaskSettleDaily.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().TaskId+" = ?", req.TaskId)
		}
		if len(req.TaskIds) > 0 {
			m = m.WhereIn(dao.AdXtTaskSettleDaily.Columns().TaskId, req.TaskIds)
		}
		if req.ItemId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().ItemId+" = ?", gconv.Int64(req.ItemId))
		}
		if req.AuthorId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().AuthorId+" = ?", gconv.Int64(req.AuthorId))
		}
		if req.Uid != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().Uid+" = ?", gconv.Int64(req.Uid))
		}
		if req.ProviderId != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().ProviderId+" = ?", gconv.Int64(req.ProviderId))
		}
		if req.PDate != "" {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().PDate+" = ?", gconv.Time(req.PDate))
		}
		if req.EstSales > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().EstSales+" = ?", req.EstSales)
		}
		if req.SettleCps > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().SettleCps+" = ?", req.SettleCps)
		}
		if req.SettleAdShare > 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().SettleAdShare+" = ?", req.SettleAdShare)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdXtTaskSettleDaily.Columns().CreatedAt+" >=? AND "+dao.AdXtTaskSettleDaily.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdXtTaskSettleDaily) GetById(ctx context.Context, id int64) (res *model.AdXtTaskSettleDailyInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdXtTaskSettleDaily.Ctx(ctx).WithAll().Where(dao.AdXtTaskSettleDaily.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdXtTaskSettleDaily) Add(ctx context.Context, req *model.AdXtTaskSettleDailyAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTaskSettleDaily.Ctx(ctx).Insert(do.AdXtTaskSettleDaily{
			AdvertiserId:  req.AdvertiserId,
			TaskId:        req.TaskId,
			ItemId:        req.ItemId,
			AuthorId:      req.AuthorId,
			Uid:           req.Uid,
			ProviderId:    req.ProviderId,
			PDate:         req.PDate,
			EstSales:      req.EstSales,
			SettleCps:     req.SettleCps,
			EstAdCost:     req.EstAdCost,
			SettleAdShare: req.SettleAdShare,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdXtTaskSettleDaily) Edit(ctx context.Context, req *model.AdXtTaskSettleDailyEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTaskSettleDaily.Ctx(ctx).WherePri(req.Id).Update(do.AdXtTaskSettleDaily{
			AdvertiserId:  req.AdvertiserId,
			TaskId:        req.TaskId,
			ItemId:        req.ItemId,
			AuthorId:      req.AuthorId,
			Uid:           req.Uid,
			ProviderId:    req.ProviderId,
			PDate:         req.PDate,
			EstSales:      req.EstSales,
			SettleCps:     req.SettleCps,
			EstAdCost:     req.EstAdCost,
			SettleAdShare: req.SettleAdShare,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdXtTaskSettleDaily) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdXtTaskSettleDaily.Ctx(ctx).Delete(dao.AdXtTaskSettleDaily.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
