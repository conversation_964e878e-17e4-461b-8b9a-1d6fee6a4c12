/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// FileImageAdV2ApiService FileImageAdV2Api service

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service  异步上传视频文件
type FileUploadTaskCreateV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.FileUploadTaskCreateV2Request
}

func (r *FileUploadTaskCreateV2ApiService) SetCfg(cfg *conf.Configuration) *FileUploadTaskCreateV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileUploadTaskCreateV2ApiService) SetRequest(req models.FileUploadTaskCreateV2Request) *FileUploadTaskCreateV2ApiService {
	r.Request = &req
	return r
}

func (r *FileUploadTaskCreateV2ApiService) AccessToken(accessToken string) *FileUploadTaskCreateV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *FileUploadTaskCreateV2ApiService) Do() (data *models.FileUploadTaskCreateV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/upload_task/create/"
	//localVarQueryParams := map[string]string{}
	if r.Request == nil {
		return nil, errors.New("请求参数不能为空")
	}
	if r.Request.AccountId == 0 {
		return nil, errors.New("请求参数AccountId不能为空")
	}
	if r.Request.AccountType == "" {
		r.Request.AccountType = models.ADVERTISER_FileUploadTaskCreateV2AccountType
	}
	if libUtils.IsNullOrEmpty(r.Request.Filename) {
		return nil, errors.New("请求参数Filename不能为空")
	}
	if libUtils.IsNullOrEmpty(r.Request.VideoUrl) {
		return nil, errors.New("请求参数VideoUrl不能为空")
	}

	request := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.FileUploadTaskCreateV2Response{})
	response, err := request.Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.FileUploadTaskCreateV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/upload_task/create/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
