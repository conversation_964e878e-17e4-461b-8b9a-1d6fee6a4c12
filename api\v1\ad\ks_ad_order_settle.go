// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: api/v1/ad/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdOrderSettleSearchReq 分页请求参数
type KsAdOrderSettleSearchReq struct {
	g.Meta `path:"/list" tags:"快手订单日结算汇总" method:"post" summary:"快手订单日结算汇总列表"`
	commonApi.Author
	model.KsAdOrderSettleSearchReq
}

// KsAdOrderSettleSearchRes 列表返回结果
type KsAdOrderSettleSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdOrderSettleSearchRes
}

// KsAdOrderSettleExportReq 导出请求
type KsAdOrderSettleExportReq struct {
	g.Meta `path:"/export" tags:"快手订单日结算汇总" method:"post" summary:"快手订单日结算汇总导出"`
	commonApi.Author
	model.KsAdOrderSettleSearchReq
}

// KsAdOrderSettleExportRes 导出响应
type KsAdOrderSettleExportRes struct {
	commonApi.EmptyRes
}

// KsAdOrderSettleAddReq 添加操作请求参数
type KsAdOrderSettleAddReq struct {
	g.Meta `path:"/add" tags:"快手订单日结算汇总" method:"post" summary:"快手订单日结算汇总添加"`
	commonApi.Author
	*model.KsAdOrderSettleAddReq
}

// KsAdOrderSettleAddRes 添加操作返回结果
type KsAdOrderSettleAddRes struct {
	commonApi.EmptyRes
}

// KsAdOrderSettleEditReq 修改操作请求参数
type KsAdOrderSettleEditReq struct {
	g.Meta `path:"/edit" tags:"快手订单日结算汇总" method:"put" summary:"快手订单日结算汇总修改"`
	commonApi.Author
	*model.KsAdOrderSettleEditReq
}

// KsAdOrderSettleEditRes 修改操作返回结果
type KsAdOrderSettleEditRes struct {
	commonApi.EmptyRes
}

// KsAdOrderSettleGetReq 获取一条数据请求
type KsAdOrderSettleGetReq struct {
	g.Meta `path:"/get" tags:"快手订单日结算汇总" method:"get" summary:"获取快手订单日结算汇总信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdOrderSettleGetRes 获取一条数据结果
type KsAdOrderSettleGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdOrderSettleInfoRes
}

// KsAdOrderSettleDeleteReq 删除数据请求
type KsAdOrderSettleDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手订单日结算汇总" method:"post" summary:"删除快手订单日结算汇总"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdOrderSettleDeleteRes 删除数据返回
type KsAdOrderSettleDeleteRes struct {
	commonApi.EmptyRes
}

// KsAdOrderSettleTaskReq 分页请求参数
type KsAdOrderSettleTaskReq struct {
	g.Meta `path:"/task" tags:"快手订单日结算汇总" method:"post" summary:"快手订单日结算汇总任务"`
	commonApi.Author
	model.KsAdOrderSettleSearchReq
}

// KsAdOrderSettleTaskRes 列表返回结果
type KsAdOrderSettleTaskRes struct {
	g.Meta `mime:"application/json"`
}
