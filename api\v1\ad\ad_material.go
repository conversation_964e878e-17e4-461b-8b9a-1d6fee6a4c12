// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-11 11:34:17
// 生成路径: api/v1/ad/ad_material.go
// 生成人：cyao
// desc:素材主表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdMaterialSearchReq 分页请求参数
type AdMaterialSearchReq struct {
	g.Meta `path:"/list" tags:"素材主表" method:"get" summary:"素材主表列表"`
	commonApi.Author
	model.AdMaterialSearchReq
}

// AdMaterialSearchRes 列表返回结果
type AdMaterialSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialSearchRes
}

// AdMaterialAddReq 添加操作请求参数
type AdMaterialAddReq struct {
	g.Meta `path:"/add" tags:"素材主表" method:"post" summary:"素材主表添加"`
	commonApi.Author
	*model.AdMaterialAddReq
}

// AdMaterialAddRes 添加操作返回结果
type AdMaterialAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialEditReq 修改操作请求参数
type AdMaterialEditReq struct {
	g.Meta `path:"/edit" tags:"素材主表" method:"put" summary:"素材主表修改"`
	commonApi.Author
	*model.AdMaterialEditReq
}

// AdMaterialEditRes 修改操作返回结果
type AdMaterialEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialGetReq 获取一条数据请求
type AdMaterialGetReq struct {
	g.Meta `path:"/get" tags:"素材主表" method:"get" summary:"获取素材主表信息"`
	commonApi.Author
	MaterialId int `p:"materialId" v:"required#主键必须"` //通过主键获取
}

// AdMaterialGetRes 获取一条数据结果
type AdMaterialGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialInfoRes
}

// AdMaterialDeleteReq 删除数据请求
type AdMaterialDeleteReq struct {
	g.Meta `path:"/delete" tags:"素材主表" method:"delete" summary:"删除素材主表"`
	commonApi.Author
	MaterialIds []int `p:"materialIds" v:"required#主键必须"` //通过主键删除
}

// AdMaterialDeleteRes 删除数据返回
type AdMaterialDeleteRes struct {
	commonApi.EmptyRes
}
