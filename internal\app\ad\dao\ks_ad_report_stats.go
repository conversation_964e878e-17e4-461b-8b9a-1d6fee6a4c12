// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-07 14:26:18
// 生成路径: internal/app/ad/dao/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdReportStatsDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdReportStatsDao struct {
	*internal.KsAdReportStatsDao
}

var (
	// KsAdReportStats is globally public accessible object for table tools_gen_table operations.
	KsAdReportStats = ksAdReportStatsDao{
		internal.NewKsAdReportStatsDao(),
	}
)

// Fill with you ideas below.
