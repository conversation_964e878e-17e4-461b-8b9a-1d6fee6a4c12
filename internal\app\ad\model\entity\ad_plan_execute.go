// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: internal/app/ad/model/entity/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdPlanExecute is the golang structure for table ad_plan_execute.
type AdPlanExecute struct {
	gmeta.Meta     `orm:"table:ad_plan_execute"`
	Id             int         `orm:"id,primary" json:"id"`                  // ID
	PlanId         int         `orm:"plan_id" json:"planId"`                 // 计划ID
	ExecuteType    int         `orm:"execute_type" json:"executeType"`       // 执行类型，1 更新预算，2 更新出价（广告），3 暂停计划
	AdjustmentType int         `orm:"adjustment_type" json:"adjustmentType"` // 调整方式，1 调整至目标值，2 增加，3 减少
	ValueType      int         `orm:"value_type" json:"valueType"`           // 1 元 2 %
	ExecuteValue   int         `orm:"execute_value" json:"executeValue"`     // 调整的目标值，增加或者减少的值
	LimitValue     int         `orm:"limit_value" json:"limitValue"`         // 最低/最高调整至
	TimeDimension  int         `orm:"time_dimension" json:"timeDimension"`   // 时间维度，1 天
	ExecuteTimes   int         `orm:"execute_times" json:"executeTimes"`     // 执行次数，1、2、3、4 等
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`           // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`           // 更新时间
}
