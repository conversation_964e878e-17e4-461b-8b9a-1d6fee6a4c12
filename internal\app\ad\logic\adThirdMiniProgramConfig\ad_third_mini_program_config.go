// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: internal/app/ad/logic/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdThirdMiniProgramConfig(New())
}

func New() service.IAdThirdMiniProgramConfig {
	return &sAdThirdMiniProgramConfig{}
}

type sAdThirdMiniProgramConfig struct{}

func (s *sAdThirdMiniProgramConfig) List(ctx context.Context, req *model.AdThirdMiniProgramConfigSearchReq) (listRes *model.AdThirdMiniProgramConfigSearchRes, err error) {
	listRes = new(model.AdThirdMiniProgramConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdThirdMiniProgramConfig.Ctx(ctx).WithAll()
		if req.AppId != "" {
			m = m.Where(dao.AdThirdMiniProgramConfig.Columns().AppId+" = ?", req.AppId)
		}
		if req.AppName != "" {
			m = m.Where(dao.AdThirdMiniProgramConfig.Columns().AppName+" like ?", "%"+req.AppName+"%")
		}
		if req.OriginalId != "" {
			m = m.Where(dao.AdThirdMiniProgramConfig.Columns().OriginalId+" = ?", req.OriginalId)
		}
		if req.AppType != "" {
			m = m.Where(dao.AdThirdMiniProgramConfig.Columns().AppType+" = ?", req.AppType)
		}
		if req.MonetizationModel != "" {
			m = m.Where(dao.AdThirdMiniProgramConfig.Columns().MonetizationModel+" = ?", req.MonetizationModel)
		}
		if req.Platform != "" {
			m = m.Where(dao.AdThirdMiniProgramConfig.Columns().Platform+" = ?", gconv.Int(req.Platform))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdThirdMiniProgramConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdThirdMiniProgramConfigListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdThirdMiniProgramConfigListRes{
				Id:                v.Id,
				AppId:             v.AppId,
				AppName:           v.AppName,
				OriginalId:        v.OriginalId,
				AppType:           v.AppType,
				MonetizationModel: v.MonetizationModel,
				Platform:          v.Platform,
				CreatedAt:         v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdThirdMiniProgramConfig) GetById(ctx context.Context, id int64) (res *model.AdThirdMiniProgramConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdThirdMiniProgramConfig.Ctx(ctx).WithAll().Where(dao.AdThirdMiniProgramConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdThirdMiniProgramConfig) GetByAppId(ctx context.Context, appId string) (res *model.AdThirdMiniProgramConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdThirdMiniProgramConfig.Ctx(ctx).WithAll().Where(dao.AdThirdMiniProgramConfig.Columns().AppId, appId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdThirdMiniProgramConfig) GetByAppIds(ctx context.Context, ids []string) (res []*model.AdThirdMiniProgramConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdThirdMiniProgramConfig.Ctx(ctx).WithAll().WhereIn(dao.AdThirdMiniProgramConfig.Columns().AppId, ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdThirdMiniProgramConfig) Add(ctx context.Context, req *model.AdThirdMiniProgramConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdThirdMiniProgramConfig.Ctx(ctx).Insert(do.AdThirdMiniProgramConfig{
			AppId:             req.AppId,
			AppName:           req.AppName,
			OriginalId:        req.OriginalId,
			AppType:           req.AppType,
			MonetizationModel: req.MonetizationModel,
			Platform:          req.Platform,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdThirdMiniProgramConfig) Edit(ctx context.Context, req *model.AdThirdMiniProgramConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdThirdMiniProgramConfig.Ctx(ctx).WherePri(req.Id).Update(do.AdThirdMiniProgramConfig{
			AppId:             req.AppId,
			AppName:           req.AppName,
			OriginalId:        req.OriginalId,
			AppType:           req.AppType,
			MonetizationModel: req.MonetizationModel,
			Platform:          req.Platform,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdThirdMiniProgramConfig) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdThirdMiniProgramConfig.Ctx(ctx).Delete(dao.AdThirdMiniProgramConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
