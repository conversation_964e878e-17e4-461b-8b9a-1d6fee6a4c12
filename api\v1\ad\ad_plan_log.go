// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: api/v1/ad/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdPlanLogSearchReq 分页请求参数
type AdPlanLogSearchReq struct {
	g.Meta `path:"/list" tags:"广告计划执行日志" method:"get" summary:"广告计划执行日志列表"`
	commonApi.Author
	model.AdPlanLogSearchReq
}

// AdPlanLogSearchRes 列表返回结果
type AdPlanLogSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanLogSearchRes
}

// AdPlanLogAddReq 添加操作请求参数
type AdPlanLogAddReq struct {
	g.Meta `path:"/add" tags:"广告计划执行日志" method:"post" summary:"广告计划执行日志添加"`
	commonApi.Author
	*model.AdPlanLogAddReq
}

// AdPlanLogAddRes 添加操作返回结果
type AdPlanLogAddRes struct {
	commonApi.EmptyRes
}

// AdPlanLogEditReq 修改操作请求参数
type AdPlanLogEditReq struct {
	g.Meta `path:"/edit" tags:"广告计划执行日志" method:"put" summary:"广告计划执行日志修改"`
	commonApi.Author
	*model.AdPlanLogEditReq
}

// AdPlanLogEditRes 修改操作返回结果
type AdPlanLogEditRes struct {
	commonApi.EmptyRes
}

// AdPlanLogGetReq 获取一条数据请求
type AdPlanLogGetReq struct {
	g.Meta `path:"/get" tags:"广告计划执行日志" method:"get" summary:"获取广告计划执行日志信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdPlanLogGetRes 获取一条数据结果
type AdPlanLogGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdPlanLogInfoRes
}

// AdPlanLogDeleteReq 删除数据请求
type AdPlanLogDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告计划执行日志" method:"delete" summary:"删除广告计划执行日志"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdPlanLogDeleteRes 删除数据返回
type AdPlanLogDeleteRes struct {
	commonApi.EmptyRes
}
