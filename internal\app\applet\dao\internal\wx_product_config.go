// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-03-08 18:20:26
// 生成路径: internal/app/applet/dao/internal/wx_product_config.go
// 生成人：lx
// desc:产品配置（充值）
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WxProductConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type WxProductConfigDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns WxProductConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// WxProductConfigColumns defines and stores column names for table wx_product_config.
type WxProductConfigColumns struct {
	Id          string //
	ProductId   string // 道具id
	ProductName string // 道具名称
	Price       string // 价格(元)
	AppId       string // 小程序ID
	DelFlag     string // 是否删除 0：否 1：是
	Publish     string // 是否发布 0：否 1：是
	Remark      string // 道具备注
	ImgUrl      string // 道具图片url地址
}

var wxProductConfigColumns = WxProductConfigColumns{
	Id:          "id",
	ProductId:   "product_id",
	ProductName: "product_name",
	Price:       "price",
	AppId:       "app_id",
	DelFlag:     "del_flag",
	Publish:     "publish",
	Remark:      "remark",
	ImgUrl:      "img_url",
}

// NewWxProductConfigDao creates and returns a new DAO object for table data access.
func NewWxProductConfigDao() *WxProductConfigDao {
	return &WxProductConfigDao{
		group:   "default",
		table:   "wx_product_config",
		columns: wxProductConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *WxProductConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *WxProductConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *WxProductConfigDao) Columns() WxProductConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *WxProductConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *WxProductConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *WxProductConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
