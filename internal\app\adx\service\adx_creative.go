// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:33:13
// 生成路径: internal/app/adx/service/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxCreative interface {
	List(ctx context.Context, req *model.AdxCreativeSearchReq) (res *model.AdxCreativeSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxCreativeSearchReq) (listRes []*model.AdxCreativeInfoRes, err error)
	GetByCreativeId(ctx context.Context, CreativeId uint64) (res *model.AdxCreativeInfoRes, err error)
	Add(ctx context.Context, req *model.AdxCreativeAddReq) (err error)
	BatchAdd(ctx context.Context, batchReq []*model.AdxCreativeAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxCreativeEditReq) (err error)
	Delete(ctx context.Context, CreativeId []uint64) (err error)
	RunSyncAdxCreativeTask(ctx context.Context, req *model.AdxCreativeSearchReq) (err error)
	SyncAdxCreativeTask(ctx context.Context)
	SyncTodayAdxCreativeTask(ctx context.Context)
	GetMinByProductIds(ctx context.Context, productIds []int64) (res *model.AdxCreativeInfoRes, err error)
	GetMinByPublisherIds(ctx context.Context, publisherIds []int64) (res *model.AdxCreativeInfoRes, err error)
}

var localAdxCreative IAdxCreative

func AdxCreative() IAdxCreative {
	if localAdxCreative == nil {
		panic("implement not found for interface IAdxCreative, forgot register?")
	}
	return localAdxCreative
}

func RegisterAdxCreative(i IAdxCreative) {
	localAdxCreative = i
}
