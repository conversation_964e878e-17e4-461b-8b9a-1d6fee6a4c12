// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-21 14:30:52
// 生成路径: api/v1/ad/ad_xt_task_settle_daily.go
// 生成人：cyao
// desc:星图结算数据分天相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdXtTaskSettleDailySearchReq 分页请求参数
type AdXtTaskSettleDailySearchReq struct {
	g.Meta `path:"/list" tags:"星图结算数据分天" method:"post" summary:"星图结算数据分天列表"`
	commonApi.Author
	model.AdXtTaskSettleDailySearchReq
}

// AdXtTaskSettleDailySearchRes 列表返回结果
type AdXtTaskSettleDailySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtTaskSettleDailySearchRes
}

// AdXtTaskSettleDailyExportReq 导出请求
type AdXtTaskSettleDailyExportReq struct {
	g.Meta `path:"/export" tags:"星图结算数据分天" method:"get" summary:"星图结算数据分天导出"`
	commonApi.Author
	model.AdXtTaskSettleDailySearchReq
}

// AdXtTaskSettleDailyExportRes 导出响应
type AdXtTaskSettleDailyExportRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettleDailyAddReq 添加操作请求参数
type AdXtTaskSettleDailyAddReq struct {
	g.Meta `path:"/add" tags:"星图结算数据分天" method:"post" summary:"星图结算数据分天添加"`
	commonApi.Author
	*model.AdXtTaskSettleDailyAddReq
}

// AdXtTaskSettleDailyAddRes 添加操作返回结果
type AdXtTaskSettleDailyAddRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettleDailyEditReq 修改操作请求参数
type AdXtTaskSettleDailyEditReq struct {
	g.Meta `path:"/edit" tags:"星图结算数据分天" method:"put" summary:"星图结算数据分天修改"`
	commonApi.Author
	*model.AdXtTaskSettleDailyEditReq
}

// AdXtTaskSettleDailyEditRes 修改操作返回结果
type AdXtTaskSettleDailyEditRes struct {
	commonApi.EmptyRes
}

// AdXtTaskSettleDailyGetReq 获取一条数据请求
type AdXtTaskSettleDailyGetReq struct {
	g.Meta `path:"/get" tags:"星图结算数据分天" method:"get" summary:"获取星图结算数据分天信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdXtTaskSettleDailyGetRes 获取一条数据结果
type AdXtTaskSettleDailyGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtTaskSettleDailyInfoRes
}

// AdXtTaskSettleDailyDeleteReq 删除数据请求
type AdXtTaskSettleDailyDeleteReq struct {
	g.Meta `path:"/delete" tags:"星图结算数据分天" method:"delete" summary:"删除星图结算数据分天"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdXtTaskSettleDailyDeleteRes 删除数据返回
type AdXtTaskSettleDailyDeleteRes struct {
	commonApi.EmptyRes
}
