// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/router/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/adx/controller"
)

func (router *Router) BindAdxHotRankingController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adxHotRanking", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdxHotRanking,
		)
	})
}
