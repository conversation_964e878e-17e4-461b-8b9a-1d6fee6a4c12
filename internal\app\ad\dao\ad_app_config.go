// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-11-13 10:42:38
// 生成路径: internal/app/ad/dao/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adAppConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adAppConfigDao struct {
	*internal.AdAppConfigDao
}

var (
	// AdAppConfig is globally public accessible object for table tools_gen_table operations.
	AdAppConfig = adAppConfigDao{
		internal.NewAdAppConfigDao(),
	}
)

// Fill with you ideas below.
