// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-08-06 18:34:55
// 生成路径: api/v1/theater/s_pitcher_video_recharge_statistics.go
// 生成人：lx
// desc:投手短剧充值统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SPitcherVideoRechargeStatisticsSearchReq 分页请求参数
type SPitcherVideoRechargeStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"投手短剧充值统计" method:"get" summary:"投手短剧充值统计列表"`
	commonApi.Author
	model.SPitcherVideoRechargeStatisticsSearchReq
}

// SPitcherVideoRechargeStatisticsSearchRes 列表返回结果
type SPitcherVideoRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SPitcherVideoRechargeStatisticsSearchRes
}

// SPitcherVideoRechargeStatisticsAddReq 添加操作请求参数
type SPitcherVideoRechargeStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"投手短剧充值统计" method:"post" summary:"投手短剧充值统计添加"`
	commonApi.Author
	*model.SPitcherVideoRechargeStatisticsAddReq
}

// SPitcherVideoRechargeStatisticsAddRes 添加操作返回结果
type SPitcherVideoRechargeStatisticsAddRes struct {
	commonApi.EmptyRes
}

// SPitcherVideoRechargeStatisticsEditReq 修改操作请求参数
type SPitcherVideoRechargeStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"投手短剧充值统计" method:"put" summary:"投手短剧充值统计修改"`
	commonApi.Author
	*model.SPitcherVideoRechargeStatisticsEditReq
}

// SPitcherVideoRechargeStatisticsEditRes 修改操作返回结果
type SPitcherVideoRechargeStatisticsEditRes struct {
	commonApi.EmptyRes
}

// SPitcherVideoRechargeStatisticsGetReq 获取一条数据请求
type SPitcherVideoRechargeStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"投手短剧充值统计" method:"get" summary:"获取投手短剧充值统计信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SPitcherVideoRechargeStatisticsGetRes 获取一条数据结果
type SPitcherVideoRechargeStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SPitcherVideoRechargeStatisticsInfoRes
}

// SPitcherVideoRechargeStatisticsDeleteReq 删除数据请求
type SPitcherVideoRechargeStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"投手短剧充值统计" method:"delete" summary:"删除投手短剧充值统计"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SPitcherVideoRechargeStatisticsDeleteRes 删除数据返回
type SPitcherVideoRechargeStatisticsDeleteRes struct {
	commonApi.EmptyRes
}
