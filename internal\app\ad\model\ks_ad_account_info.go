// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-07 11:44:22
// 生成路径: internal/app/ad/model/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdAccountInfoInfoRes is the golang structure for table ks_ad_account_info.
type KsAdAccountInfoInfoRes struct {
	gmeta.Meta          `orm:"table:ks_ad_account_info"`
	AdvertiserId        int64       `orm:"advertiser_id,primary" json:"advertiserId" dc:"快手账户ID"`                // 快手账户ID
	Id                  uint64      `orm:"id,primary" json:"id" dc:"id"`                                         // id
	PrimaryIndustryName string      `orm:"primary_industry_name" json:"primaryIndustryName" dc:"一级行业名称"`         // 一级行业名称
	AppId               int         `orm:"app_id" json:"appId" dc:"授权的app_id"`                                   // 授权的app_id
	IndustryId          int64       `orm:"industry_id" json:"industryId" dc:"二级行业ID"`                            // 二级行业ID
	AccountId           int64       `orm:"account_id" json:"accountId" dc:"账户id"`                                // 账户id
	IndustryName        string      `orm:"industry_name" json:"industryName" dc:"二级行业名称"`                        // 二级行业名称
	AccountName         string      `orm:"account_name" json:"accountName" dc:"快手账户名称"`                          // 快手账户名称
	DeliveryType        int         `orm:"delivery_type" json:"deliveryType" dc:"投放方式: 0:默认；1:优先效果"`             // 投放方式: 0:默认；1:优先效果
	PrimaryIndustryId   int64       `orm:"primary_industry_id" json:"primaryIndustryId" dc:"一级行业ID"`             // 一级行业ID
	EffectFirst         int         `orm:"effect_first" json:"effectFirst" dc:"优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定"` // 优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定
	CorporationName     string      `orm:"corporation_name" json:"corporationName" dc:"公司名称"`                    // 公司名称
	ProductName         string      `orm:"product_name" json:"productName" dc:"账户产品名称"`                          // 账户产品名称
	DirectRebate        float64     `orm:"direct_rebate" json:"directRebate" dc:"激励余额，单位：元"`                     // 激励余额，单位：元
	ContractRebate      float64     `orm:"contract_rebate" json:"contractRebate" dc:"框返余额，单位：元"`                 // 框返余额，单位：元
	RechargeBalance     float64     `orm:"recharge_balance" json:"rechargeBalance" dc:"充值余额，单位：元"`               // 充值余额，单位：元
	Balance             float64     `orm:"balance" json:"balance" dc:"账户总余额，单位：元"`                               // 账户总余额，单位：元
	CreatedAt           *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                // 创建时间
	DeletedAt           *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                // 删除时间
}

type KsAdAccountInfoListRes struct {
	AdvertiserId        int64       `json:"advertiserId" dc:"快手账户ID"`
	Id                  uint64      `json:"id" dc:"id"`
	PrimaryIndustryName string      `json:"primaryIndustryName" dc:"一级行业名称"`
	AppId               int         `json:"appId" dc:"授权的app_id"`
	IndustryId          int64       `json:"industryId" dc:"二级行业ID"`
	AccountId           int64       `json:"accountId" dc:"账户id"`
	IndustryName        string      `json:"industryName" dc:"二级行业名称"`
	AccountName         string      `json:"accountName" dc:"快手账户名称"`
	DeliveryType        int         `json:"deliveryType" dc:"投放方式: 0:默认；1:优先效果"`
	PrimaryIndustryId   int64       `json:"primaryIndustryId" dc:"一级行业ID"`
	EffectFirst         int         `json:"effectFirst" dc:"优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定"`
	CorporationName     string      `json:"corporationName" dc:"公司名称"`
	ProductName         string      `json:"productName" dc:"账户产品名称"`
	DirectRebate        float64     `json:"directRebate" dc:"激励余额，单位：元"`
	ContractRebate      float64     `json:"contractRebate" dc:"框返余额，单位：元"`
	RechargeBalance     float64     `json:"rechargeBalance" dc:"充值余额，单位：元"`
	Balance             float64     `json:"balance" dc:"账户总余额，单位：元"`
	CreatedAt           *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdAccountInfoSearchReq 分页请求参数
type KsAdAccountInfoSearchReq struct {
	comModel.PageReq
	AdvertiserId        string `p:"advertiserId" dc:"快手账户ID"`                                                                                     //快手账户ID
	Id                  string `p:"id" dc:"id"`                                                                                                   //id
	PrimaryIndustryName string `p:"primaryIndustryName" dc:"一级行业名称"`                                                                              //一级行业名称
	AppId               string `p:"appId" v:"appId@integer#授权的app_id需为整数" dc:"授权的app_id"`                                                         //授权的app_id
	IndustryId          string `p:"industryId" v:"industryId@integer#二级行业ID需为整数" dc:"二级行业ID"`                                                     //二级行业ID
	AccountId           string `p:"accountId" v:"accountId@integer#账户id需为整数" dc:"账户id"`                                                           //账户id
	IndustryName        string `p:"industryName" dc:"二级行业名称"`                                                                                     //二级行业名称
	AccountName         string `p:"accountName" dc:"快手账户名称"`                                                                                      //快手账户名称
	DeliveryType        string `p:"deliveryType" v:"deliveryType@integer#投放方式: 0:默认；1:优先效果需为整数" dc:"投放方式: 0:默认；1:优先效果"`                           //投放方式: 0:默认；1:优先效果
	PrimaryIndustryId   string `p:"primaryIndustryId" v:"primaryIndustryId@integer#一级行业ID需为整数" dc:"一级行业ID"`                                       //一级行业ID
	EffectFirst         string `p:"effectFirst" v:"effectFirst@integer#优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定需为整数" dc:"优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定"` //优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定
	CorporationName     string `p:"corporationName" dc:"公司名称"`                                                                                    //公司名称
	ProductName         string `p:"productName" dc:"账户产品名称"`                                                                                      //账户产品名称
	DirectRebate        string `p:"directRebate" v:"directRebate@float#激励余额，单位：元需为浮点数" dc:"激励余额，单位：元"`                                            //激励余额，单位：元
	ContractRebate      string `p:"contractRebate" v:"contractRebate@float#框返余额，单位：元需为浮点数" dc:"框返余额，单位：元"`                                        //框返余额，单位：元
	RechargeBalance     string `p:"rechargeBalance" v:"rechargeBalance@float#充值余额，单位：元需为浮点数" dc:"充值余额，单位：元"`                                      //充值余额，单位：元
	Balance             string `p:"balance" v:"balance@float#账户总余额，单位：元需为浮点数" dc:"账户总余额，单位：元"`                                                    //账户总余额，单位：元
	CreatedAt           string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                       //创建时间
}

// KsAdAccountInfoSearchRes 列表返回结果
type KsAdAccountInfoSearchRes struct {
	comModel.ListRes
	List []*KsAdAccountInfoListRes `json:"list"`
}

type GetKsAdAccountListReq struct {
	AccessToken string `p:"accessToken"  dc:"accessToken"`
	Aid         int64  `p:"aid" v:"required#主键ID不能为空" dc:"主键ID"`
	AppId       int64  `p:"appId" v:"required#主键ID不能为空" dc:"主键ID"`
}

type GetKsAdAccountListRes struct {
	List []*KsAdAccountInfoAddReq `json:"list"`
}

// KsAdAccountInfoAddReq 添加操作请求参数
type KsAdAccountInfoAddReq struct {
	AdvertiserId        int64   `p:"advertiserId" v:"required#主键ID不能为空" dc:"快手账户ID"`
	PrimaryIndustryName string  `p:"primaryIndustryName" v:"required#一级行业名称不能为空" dc:"一级行业名称"`
	AppId               int     `p:"appId"  dc:"授权的app_id"`
	IndustryId          int64   `p:"industryId"  dc:"二级行业ID"`
	AccountId           int64   `p:"accountId"  dc:"账户id"`
	IndustryName        string  `p:"industryName" v:"required#二级行业名称不能为空" dc:"二级行业名称"`
	AccountName         string  `p:"accountName" v:"required#快手账户名称不能为空" dc:"快手账户名称"`
	DeliveryType        int     `p:"deliveryType"  dc:"投放方式: 0:默认；1:优先效果"`
	PrimaryIndustryId   int64   `p:"primaryIndustryId"  dc:"一级行业ID"`
	EffectFirst         int     `p:"effectFirst"  dc:"优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定"`
	CorporationName     string  `p:"corporationName" v:"required#公司名称不能为空" dc:"公司名称"`
	ProductName         string  `p:"productName" v:"required#账户产品名称不能为空" dc:"账户产品名称"`
	DirectRebate        float64 `p:"directRebate"  dc:"激励余额，单位：元"`
	ContractRebate      float64 `p:"contractRebate"  dc:"框返余额，单位：元"`
	RechargeBalance     float64 `p:"rechargeBalance"  dc:"充值余额，单位：元"`
	Balance             float64 `p:"balance"  dc:"账户总余额，单位：元"`
}

// KsAdAccountInfoEditReq 修改操作请求参数
type KsAdAccountInfoEditReq struct {
	Id                  int64   `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	AdvertiserId        int64   `p:"advertiserId" v:"required#主键ID不能为空" dc:"快手账户ID"`
	PrimaryIndustryName string  `p:"primaryIndustryName" v:"required#一级行业名称不能为空" dc:"一级行业名称"`
	AppId               int     `p:"appId"  dc:"授权的app_id"`
	IndustryId          int64   `p:"industryId"  dc:"二级行业ID"`
	AccountId           int64   `p:"accountId"  dc:"账户id"`
	IndustryName        string  `p:"industryName" v:"required#二级行业名称不能为空" dc:"二级行业名称"`
	AccountName         string  `p:"accountName" v:"required#快手账户名称不能为空" dc:"快手账户名称"`
	DeliveryType        int     `p:"deliveryType"  dc:"投放方式: 0:默认；1:优先效果"`
	PrimaryIndustryId   int64   `p:"primaryIndustryId"  dc:"一级行业ID"`
	EffectFirst         int     `p:"effectFirst"  dc:"优先效果策略生效状态: 1:开启；其他:未开启，由系统自动设定"`
	CorporationName     string  `p:"corporationName" v:"required#公司名称不能为空" dc:"公司名称"`
	ProductName         string  `p:"productName" v:"required#账户产品名称不能为空" dc:"账户产品名称"`
	DirectRebate        float64 `p:"directRebate"  dc:"激励余额，单位：元"`
	ContractRebate      float64 `p:"contractRebate"  dc:"框返余额，单位：元"`
	RechargeBalance     float64 `p:"rechargeBalance"  dc:"充值余额，单位：元"`
	Balance             float64 `p:"balance"  dc:"账户总余额，单位：元"`
}

type KsAdAccountAIdsRes struct {
	AdvertiserId int64 `json:"advertiserId" dc:"快手账户ID"`
}
