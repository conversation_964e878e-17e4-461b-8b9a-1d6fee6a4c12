/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// FileImageAdV2ApiService FileImageAdV2Api service

// AdvertiserInfoV2ApiService AdvertiserInfoV2Api service  上传广告图片
type ToolsQuotaGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2ToolsQuotaGetGetRequest
}

type ApiOpenApi2ToolsQuotaGetGetRequest struct {
	AdvertiserId  *int64
	CampaignType  *models.ToolsQuotaGetV2CampaignType
	DeliveryRange *models.ToolsQuotaGetV2DeliveryRange
}

func (r *ToolsQuotaGetV2ApiService) SetCfg(cfg *conf.Configuration) *ToolsQuotaGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsQuotaGetV2ApiService) SetRequest(req ApiOpenApi2ToolsQuotaGetGetRequest) *ToolsQuotaGetV2ApiService {
	r.Request = &req
	return r
}

func (r *ToolsQuotaGetV2ApiService) AccessToken(accessToken string) *ToolsQuotaGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsQuotaGetV2ApiService) Do() (data *models.ToolsQuotaGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/tools/quota/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.CampaignType == nil {
		return nil, errors.New("CampaignType is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "campaign_type", r.Request.CampaignType)
	if r.Request.DeliveryRange != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "delivery_range", r.Request.DeliveryRange)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.ToolsQuotaGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsQuotaGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/tools/quota/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
