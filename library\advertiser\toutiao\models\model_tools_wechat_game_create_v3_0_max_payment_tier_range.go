/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameCreateV30MaxPaymentTierRange
type ToolsWechatGameCreateV30MaxPaymentTierRange string

// List of tools_wechat_game_create_v3.0_max_payment_tier_range
const (
	ABOVE_1000_ToolsWechatGameCreateV30MaxPaymentTierRange       ToolsWechatGameCreateV30MaxPaymentTierRange = "ABOVE_1000"
	BELOW_500_ToolsWechatGameCreateV30MaxPaymentTierRange        ToolsWechatGameCreateV30MaxPaymentTierRange = "BELOW_500"
	FROM_500_TO_1000_ToolsWechatGameCreateV30MaxPaymentTierRange ToolsWechatGameCreateV30MaxPaymentTierRange = "FROM_500_TO_1000"
)

// Ptr returns reference to tools_wechat_game_create_v3.0_max_payment_tier_range value
func (v ToolsWechatGameCreateV30MaxPaymentTierRange) Ptr() *ToolsWechatGameCreateV30MaxPaymentTierRange {
	return &v
}
