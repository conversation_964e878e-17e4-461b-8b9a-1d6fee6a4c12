// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/model/entity/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxHotRanking is the golang structure for table adx_hot_ranking.
type AdxHotRanking struct {
	gmeta.Meta          `orm:"table:adx_hot_ranking"`
	PlayletId           uint64               `orm:"playlet_id,primary" json:"playletId"` // 短剧ID
	BusinessDate        string               `orm:"business_date,primary" json:"businessDate"`
	PlayletName         string               `orm:"playlet_name" json:"playletName"`            // 短剧名称
	Ranking             uint                 `orm:"ranking" json:"ranking"`                     // 排名
	ConsumeNum          int64                `orm:"consume_num" json:"consumeNum"`              // 当日热力值
	TotalConsumeNum     int64                `orm:"total_consume_num" json:"totalConsumeNum"`   // 累计热力值
	NewFlag             int                  `orm:"new_flag" json:"newFlag"`                    // 是否有new标识
	PlayletTags         []string             `orm:"playlet_tags" json:"playletTags"`            // 短剧标签
	ContractorList      []model.Contractor   `orm:"contractor_list" json:"contractorList"`      // 承制方列表
	RelatedPartyList    []model.RelatedParty `orm:"related_party_list" json:"relatedPartyList"` // 关联方列表
	CopyrightHolderList []string             `orm:"copyright_holder_list" json:"copyrightHolderList" dc:"版权方列表"`
}
