// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:30
// 生成路径: internal/app/ad/model/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdAccountDeptsInfoRes is the golang structure for table dz_ad_account_depts.
type DzAdAccountDeptsInfoRes struct {
	gmeta.Meta `orm:"table:dz_ad_account_depts"`
	ChannelId  int64 `orm:"channel_id,primary" json:"channelId" dc:"渠道id"` // 渠道id
	DetpId     int   `orm:"detp_id,primary" json:"detpId" dc:"部门ID"`       // 部门ID
}

type DzAdAccountDeptsListRes struct {
	ChannelId int64 `json:"channelId" dc:"渠道id"`
	DetpId    int   `json:"detpId" dc:"部门ID"`
}

// DzAdAccountDeptsSearchReq 分页请求参数
type DzAdAccountDeptsSearchReq struct {
	comModel.PageReq
	ChannelId  string  `p:"channelId" dc:"渠道id"` //渠道id
	ChannelIds []int64 `p:"channelIds" dc:"渠道id"`
	DetpId     string  `p:"detpId" dc:"部门ID"` //部门ID
}

// DzAdAccountDeptsSearchRes 列表返回结果
type DzAdAccountDeptsSearchRes struct {
	comModel.ListRes
	List []*DzAdAccountDeptsListRes `json:"list"`
}

// DzAdAccountDeptsAddReq 添加操作请求参数
type DzAdAccountDeptsAddReq struct {
	ChannelId int64 `p:"channelId" v:"required#主键ID不能为空" dc:"渠道id"`
}

// DzAdAccountDeptsEditReq 修改操作请求参数
type DzAdAccountDeptsEditReq struct {
	ChannelId int64 `p:"channelId" v:"required#主键ID不能为空" dc:"渠道id"`
}
