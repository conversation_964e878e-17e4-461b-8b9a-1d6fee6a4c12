// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-17 15:27:48
// 生成路径: api/v1/member/m_member_points_exchange.go
// 生成人：cq
// desc:积分兑换表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberPointsExchangeSearchReq 分页请求参数
type MMemberPointsExchangeSearchReq struct {
	g.Meta `path:"/list" tags:"积分兑换表" method:"post" summary:"积分兑换表列表"`
	commonApi.Author
	model.MMemberPointsExchangeSearchReq
}

// MMemberPointsExchangeSearchRes 列表返回结果
type MMemberPointsExchangeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberPointsExchangeSearchRes
}

// MMemberPointsExchangeExportReq 导出请求
type MMemberPointsExchangeExportReq struct {
	g.Meta `path:"/export" tags:"积分兑换表" method:"post" summary:"积分兑换表导出"`
	commonApi.Author
	model.MMemberPointsExchangeSearchReq
}

// MMemberPointsExchangeExportRes 导出响应
type MMemberPointsExchangeExportRes struct {
	commonApi.EmptyRes
}

// MMemberPointsExchangeGetReq 获取一条数据请求
type MMemberPointsExchangeGetReq struct {
	g.Meta `path:"/get" tags:"积分兑换表" method:"get" summary:"获取积分兑换表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberPointsExchangeGetRes 获取一条数据结果
type MMemberPointsExchangeGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberPointsExchangeInfoRes
}
