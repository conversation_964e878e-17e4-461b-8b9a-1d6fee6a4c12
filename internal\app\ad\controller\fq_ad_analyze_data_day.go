// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: internal/app/ad/controller/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdAnalyzeDataDayController struct {
	systemController.BaseController
}

var FqAdAnalyzeDataDay = new(fqAdAnalyzeDataDayController)

// List 列表
func (c *fqAdAnalyzeDataDayController) List(ctx context.Context, req *ad.FqAdAnalyzeDataDaySearchReq) (res *ad.FqAdAnalyzeDataDaySearchRes, err error) {
	res = new(ad.FqAdAnalyzeDataDaySearchRes)
	res.FqAdAnalyzeDataDaySearchRes, err = service.FqAdAnalyzeDataDay().List(ctx, &req.FqAdAnalyzeDataDaySearchReq)
	return
}

// Export 导出excel
func (c *fqAdAnalyzeDataDayController) Export(ctx context.Context, req *ad.FqAdAnalyzeDataDayExportReq) (res *ad.FqAdAnalyzeDataDayExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdAnalyzeDataDayListRes
		listRes  *model.FqAdAnalyzeDataDaySearchRes
		//表头
		tableHead = []interface{}{"番茄账号", "渠道号", "投手上级分销", "投手名", "番茄渠道ID", "推广链id", "统计日期", "新增桌面用户数", "充值用户数", "用户总数", "充值金额"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.FqAdAnalyzeDataDay().List(ctx, &req.FqAdAnalyzeDataDaySearchReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.FqAccount,
				v.ChannelCode,
				v.DistributorName,
				v.UserName,
				gconv.String(v.DistributorId),
				gconv.String(v.PromotionId),
				gconv.String(v.StatDate),
				v.AddDesktopNum,
				v.RechargeNum,
				v.UserNum,
				v.RechargeAmount,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode(" 获取回本统计-分天数据")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取 获取回本统计-分天数据
func (c *fqAdAnalyzeDataDayController) Get(ctx context.Context, req *ad.FqAdAnalyzeDataDayGetReq) (res *ad.FqAdAnalyzeDataDayGetRes, err error) {
	res = new(ad.FqAdAnalyzeDataDayGetRes)
	res.FqAdAnalyzeDataDayInfoRes, err = service.FqAdAnalyzeDataDay().GetById(ctx, req.Id)
	return
}

// Add 添加 获取回本统计-分天数据
func (c *fqAdAnalyzeDataDayController) Add(ctx context.Context, req *ad.FqAdAnalyzeDataDayAddReq) (res *ad.FqAdAnalyzeDataDayAddRes, err error) {
	err = service.FqAdAnalyzeDataDay().Add(ctx, req.FqAdAnalyzeDataDayAddReq)
	return
}

// Edit 修改 获取回本统计-分天数据
func (c *fqAdAnalyzeDataDayController) Edit(ctx context.Context, req *ad.FqAdAnalyzeDataDayEditReq) (res *ad.FqAdAnalyzeDataDayEditRes, err error) {
	err = service.FqAdAnalyzeDataDay().Edit(ctx, req.FqAdAnalyzeDataDayEditReq)
	return
}

// Delete 删除 获取回本统计-分天数据
func (c *fqAdAnalyzeDataDayController) Delete(ctx context.Context, req *ad.FqAdAnalyzeDataDayDeleteReq) (res *ad.FqAdAnalyzeDataDayDeleteRes, err error) {
	err = service.FqAdAnalyzeDataDay().Delete(ctx, req.Ids)
	return
}
