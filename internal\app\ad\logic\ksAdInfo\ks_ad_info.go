// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: internal/app/ad/logic/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdInfo(New())
}

func New() service.IKsAdInfo {
	return &sKsAdInfo{}
}

type sKsAdInfo struct{}

func (s *sKsAdInfo) List(ctx context.Context, req *model.KsAdInfoSearchReq) (listRes *model.KsAdInfoSearchRes, err error) {
	listRes = new(model.KsAdInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdInfo.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdInfo.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdInfo.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if req.AppId != "" {
			m = m.Where(dao.KsAdInfo.Columns().AppId+" = ?", gconv.Int(req.AppId))
		}
		if req.AdAccountName != "" {
			m = m.Where(dao.KsAdInfo.Columns().AdAccountName+" like ?", "%"+req.AdAccountName+"%")
		}
		if req.AccountMainName != "" {
			m = m.Where(dao.KsAdInfo.Columns().AccountMainName+" like ?", "%"+req.AccountMainName+"%")
		}
		if req.Status != "" {
			m = m.Where(dao.KsAdInfo.Columns().Status+" = ?", req.Status)
		}
		if req.AuthTime != "" {
			m = m.Where(dao.KsAdInfo.Columns().AuthTime+" = ?", gconv.Time(req.AuthTime))
		}
		if req.AuthUrl != "" {
			m = m.Where(dao.KsAdInfo.Columns().AuthUrl+" = ?", req.AuthUrl)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdInfo.Columns().CreatedAt+" >=? AND "+dao.KsAdInfo.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdInfoListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdInfoListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdInfoListRes{
				Id:              v.Id,
				AdvertiserId:    v.AdvertiserId,
				AppId:           v.AppId,
				AdAccountName:   v.AdAccountName,
				AccountMainName: v.AccountMainName,
				Status:          v.Status,
				AuthTime:        v.AuthTime,
				AuthUrl:         v.AuthUrl,
				CreatedAt:       v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdInfo) GetById(ctx context.Context, id int) (res *model.KsAdInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdInfo.Ctx(ctx).WithAll().Where(dao.KsAdInfo.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdInfo) GetByAdvertiserIds(ctx context.Context, advertiserIds []int64) (res []*model.KsAdInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdInfo.Ctx(ctx).WithAll().WhereIn(dao.KsAdInfo.Columns().AdvertiserId, advertiserIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetByAIds
func (s *sKsAdInfo) GetByAIds(ctx context.Context, ids []int64) (res []*model.KsAdInfoListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdInfo.Ctx(ctx).WithAll().Where(dao.KsAdInfo.Columns().AdvertiserId+" in (?)", ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdInfo) Add(ctx context.Context, req *model.KsAdInfoAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdInfo.Ctx(ctx).Insert(do.KsAdInfo{
			AdvertiserId:    req.AdvertiserId,
			AppId:           req.AppId,
			AdAccountName:   req.AdAccountName,
			AccountMainName: req.AccountMainName,
			Status:          req.Status,
			AuthTime:        req.AuthTime,
			AuthUrl:         req.AuthUrl,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdInfo) Edit(ctx context.Context, req *model.KsAdInfoEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdInfo.Ctx(ctx).WherePri(req.Id).Update(do.KsAdInfo{
			AdvertiserId:    req.AdvertiserId,
			AppId:           req.AppId,
			AdAccountName:   req.AdAccountName,
			AccountMainName: req.AccountMainName,
			Status:          req.Status,
			AuthTime:        req.AuthTime,
			AuthUrl:         req.AuthUrl,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdInfo) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdInfo.Ctx(ctx).Delete(dao.KsAdInfo.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
