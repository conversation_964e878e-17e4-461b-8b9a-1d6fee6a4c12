// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-07 14:26:18
// 生成路径: internal/app/ad/model/entity/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdReportStats is the golang structure for table ks_ad_report_stats.
type KsAdReportStats struct {
	gmeta.Meta                                    `orm:"table:ks_ad_report_stats"`
	AdvertiserId                                  int64       `orm:"advertiser_id,primary" json:"advertiserId"`                                                                  // 账户ID
	Id                                            uint64      `orm:"id,primary" json:"id"`                                                                                       //
	Likes                                         int         `orm:"likes" json:"likes"`                                                                                         // 点赞数
	Share                                         int         `orm:"share" json:"share"`                                                                                         // 分享数
	PhotoClick                                    int         `orm:"photo_click" json:"photoClick"`                                                                              // 封面点击数
	Impression                                    int         `orm:"impression" json:"impression"`                                                                               // 封面曝光数
	EventPay                                      int         `orm:"event_pay" json:"eventPay"`                                                                                  // 付费次数
	T0DirectPaiedCnt                              int         `orm:"t0_direct_paied_cnt" json:"t0DirectPaiedCnt"`                                                                // 付费次数(计费时间)
	EventPayPurchaseAmount                        float64     `orm:"event_pay_purchase_amount" json:"eventPayPurchaseAmount"`                                                    // 付费金额
	T0DirectPaiedAmt                              float64     `orm:"t0_direct_paied_amt" json:"t0DirectPaiedAmt"`                                                                // 付费金额(计费时间)
	AdShow                                        int         `orm:"ad_show" json:"adShow"`                                                                                      // 广告曝光
	TotalCharge                                   float64     `orm:"total_charge" json:"totalCharge"`                                                                            // 消耗
	EventAppInvoked                               int         `orm:"event_app_invoked" json:"eventAppInvoked"`                                                                   // 唤起应用数
	EventPayPurchaseAmountFirstDay                float64     `orm:"event_pay_purchase_amount_first_day" json:"eventPayPurchaseAmountFirstDay"`                                  // 激活当日付费金额
	EventPayPurchaseAmountOneDayByConversion      float64     `orm:"event_pay_purchase_amount_one_day_by_conversion" json:"eventPayPurchaseAmountOneDayByConversion"`            // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountWeekByConversion        float64     `orm:"event_pay_purchase_amount_week_by_conversion" json:"eventPayPurchaseAmountWeekByConversion"`                 // 激活后七日付费金额
	EventPayPurchaseAmountThreeDayByConversion    float64     `orm:"event_pay_purchase_amount_three_day_by_conversion" json:"eventPayPurchaseAmountThreeDayByConversion"`        // 激活后三日付费金额
	Conversion                                    int         `orm:"conversion" json:"conversion"`                                                                               // 激活数
	T0DirectConversionCnt                         int         `orm:"t0_direct_conversion_cnt" json:"t0DirectConversionCnt"`                                                      // 激活数(计费时间)
	Negative                                      int         `orm:"negative" json:"negative"`                                                                                   // 减少此类作品数
	Report                                        int         `orm:"report" json:"report"`                                                                                       // 举报数
	Block                                         int         `orm:"block" json:"block"`                                                                                         // 拉黑数
	Comment                                       int         `orm:"comment" json:"comment"`                                                                                     // 评论数
	EventPayFirstDay                              int         `orm:"event_pay_first_day" json:"eventPayFirstDay"`                                                                // 首日付费次数
	PlayedNum                                     int         `orm:"played_num" json:"playedNum"`                                                                                // 素材曝光数
	PlayedThreeSeconds                            int         `orm:"played_three_seconds" json:"playedThreeSeconds"`                                                             // 3s播放数
	AdPhotoPlayed75Percent                        int         `orm:"ad_photo_played75percent" json:"adPhotoPlayed75Percent"`                                                     // 75%播放进度数
	PlayedEnd                                     int         `orm:"played_end" json:"playedEnd"`                                                                                // 完播数
	Follow                                        int         `orm:"follow" json:"follow"`                                                                                       // 新增粉丝数
	EventNewUserPay                               int         `orm:"event_new_user_pay" json:"eventNewUserPay"`                                                                  // 新增付费人数
	AdItemClick                                   int         `orm:"ad_item_click" json:"adItemClick"`                                                                           // 行为数
	T7PaiedCnt                                    int         `orm:"t7_paied_cnt" json:"t7PaiedCnt"`                                                                             // 7日累计付费次数
	T7PaiedAmt                                    float64     `orm:"t7_paied_amt" json:"t7PaiedAmt"`                                                                             // 7日累计付费金额
	ConversionNumByImpression7D                   int         `orm:"conversion_num_by_impression7d" json:"conversionNumByImpression7D"`                                          // 转化数(计费时间)
	DeepConversionNumByImpression7D               int         `orm:"deep_conversion_num_by_impression7d" json:"deepConversionNumByImpression7D"`                                 // 深度转化数(计费时间)
	ConversionNum                                 int         `orm:"conversion_num" json:"conversionNum"`                                                                        // 转化数(回传时间)
	DeepConversionNum                             int         `orm:"deep_conversion_num" json:"deepConversionNum"`                                                               // 深度转化数
	T0PaiedCnt                                    int         `orm:"t0_paied_cnt" json:"t0PaiedCnt"`                                                                             // 当日累计付费次数
	T0PaiedAmt                                    float64     `orm:"t0_paied_amt" json:"t0PaiedAmt"`                                                                             // 当日累计付费金额
	Play3SRatio                                   float64     `orm:"play3s_ratio" json:"play3SRatio"`                                                                            // 3s播放率
	AdPhotoPlayed75PercentRatio2                  float64     `orm:"ad_photo_played_75percent_ratio" json:"adPhotoPlayed75PercentRatio"`                                         // 75%进度播放率
	T7PaiedRoi                                    float64     `orm:"t7_paied_roi" json:"t7PaiedRoi"`                                                                             // 7日累计ROI
	T0PaiedRoi                                    float64     `orm:"t0_paied_roi" json:"t0PaiedRoi"`                                                                             // 当日累计ROI
	PhotoClickRatio                               float64     `orm:"photo_click_ratio" json:"photoClickRatio"`                                                                   // 封面点击率
	EventPayCost                                  float64     `orm:"event_pay_cost" json:"eventPayCost"`                                                                         // 付费次数成本
	EventPayRoi                                   float64     `orm:"event_pay_roi" json:"eventPayRoi"`                                                                           // 付费ROI
	EventAppInvokedCost                           float64     `orm:"event_app_invoked_cost" json:"eventAppInvokedCost"`                                                          // 唤起应用成本
	EventAppInvokedRatio                          float64     `orm:"event_app_invoked_ratio" json:"eventAppInvokedRatio"`                                                        // 唤起应用率
	ConversionCost                                float64     `orm:"conversion_cost" json:"conversionCost"`                                                                      // 激活单价
	EventPayFirstDayRoi                           float64     `orm:"event_pay_first_day_roi" json:"eventPayFirstDayRoi"`                                                         // 激活当日ROI
	EventPayPurchaseAmountOneDayByConversionRoi   float64     `orm:"event_pay_purchase_amount_one_day_by_conversion_roi" json:"eventPayPurchaseAmountOneDayByConversionRoi"`     // 激活后24h-ROI(激活时间)
	EventPayPurchaseAmountThreeDayByConversionRoi float64     `orm:"event_pay_purchase_amount_three_day_by_conversion_roi" json:"eventPayPurchaseAmountThreeDayByConversionRoi"` // 激活后3日ROI
	EventPayPurchaseAmountWeekByConversionRoi     float64     `orm:"event_pay_purchase_amount_week_by_conversion_roi" json:"eventPayPurchaseAmountWeekByConversionRoi"`          // 激活后7日ROI
	PhotoClickCost                                float64     `orm:"photo_click_cost" json:"photoClickCost"`                                                                     // 平均封面点击单价（元）
	Impression1KCost                              float64     `orm:"impression1k_cost" json:"impression1KCost"`                                                                  // 平均千次封面曝光花费（元）
	Click1KCost                                   float64     `orm:"click1k_cost" json:"click1KCost"`                                                                            // 平均千次素材曝光花费（元）
	ActionCost                                    float64     `orm:"action_cost" json:"actionCost"`                                                                              // 平均行为单价（元）
	DeepConversionCostByImpression7D              float64     `orm:"deep_conversion_cost_by_impression7d" json:"deepConversionCostByImpression7D"`                               // 深度转化成本(计费时间)，单位元
	DeepConversionRatioByImpression7D             float64     `orm:"deep_conversion_ratio_by_impression7d" json:"deepConversionRatioByImpression7D"`                             // 深度转化率(计费时间)
	EventPayFirstDayCost                          float64     `orm:"event_pay_first_day_cost" json:"eventPayFirstDayCost"`                                                       // 首日付费次数成本，单位元
	ActionRatio                                   float64     `orm:"action_ratio" json:"actionRatio"`                                                                            // 素材点击率
	PlayEndRatio                                  float64     `orm:"play_end_ratio" json:"playEndRatio"`                                                                         // 完播率
	EventNewUserPayCost                           float64     `orm:"event_new_user_pay_cost" json:"eventNewUserPayCost"`                                                         // 新增付费人数成本，单位元
	EventNewUserPayRatio                          float64     `orm:"event_new_user_pay_ratio" json:"eventNewUserPayRatio"`                                                       // 新增付费人数率
	ActionNewRatio                                float64     `orm:"action_new_ratio" json:"actionNewRatio"`                                                                     // 行为率
	ConversionCostByImpression7D                  float64     `orm:"conversion_cost_by_impression7d" json:"conversionCostByImpression7D"`                                        // 转化成本(计费时间)，单位元
	ConversionRatioByImpression7D                 float64     `orm:"conversion_ratio_by_impression7d" json:"conversionRatioByImpression7D"`                                      // 转化率(计费时间)
	Date                                          *gtime.Time `orm:"date" json:"date"`                                                                                           // 日期，格式：yyyy-MM-dd HH:mm:ss
	KeyAction                                     int64       `orm:"key_action" json:"keyAction"`                                                                                // 关键行为数
	AdPhotoPlayed75PercentRatio                   float64     `orm:"ad_photo_played75percent_ratio" json:"adPhotoPlayed75PercentRatio"`                                          // 75%进度播放数
	AccountId                                     int64       `orm:"account_id" json:"accountId"`                                                                                // 账号ID
	SeriesId                                      int64       `orm:"series_id" json:"seriesId"`                                                                                  // 短剧ID
	RechargeRate                                  float64     `orm:"recharge_rate" json:"rechargeRate"`                                                                          // 充值几率
	MiniGameIaaRoi                                float64     `orm:"mini_game_iaa_roi" json:"miniGameIaaRoi"`                                                                    // IAA广告变现ROI
	MiniGameIaaPurchaseAmount                     float64     `orm:"mini_game_iaa_purchase_amount" json:"miniGameIaaPurchaseAmount"`                                             // IAA广告变现LTV（元）
	CreateTime                                    string      `orm:"create_time" json:"createTime"`                                                                              // 统计日期
}
