// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-24 17:06:23
// 生成路径: api/v1/system/s_video_statistics.go
// 生成人：gfast
// desc:短剧统计表格相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SVideoStatisticsSearchReq 分页请求参数
type SVideoStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"短剧统计表格" method:"get" summary:"短剧统计表格列表"`
	commonApi.Author
	model.SVideoStatisticsSearchReq
}

// SVideoStatisticsSearchRes 列表返回结果
type SVideoStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SVideoStatisticsSearchRes
}

// SVideoStatisticsExportReq 导出请求
type SVideoStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"短剧统计表格" method:"get" summary:"短剧统计表格导出"`
	commonApi.Author
	model.SVideoStatisticsSearchReq
}

// SVideoStatisticsExportRes 导出响应
type SVideoStatisticsExportRes struct {
	commonApi.EmptyRes
}

// SVideoRechargeStatisticsSearchReq 分页请求参数
type SVideoRechargeStatisticsSearchReq struct {
	g.Meta `path:"/recharge/list" tags:"分销统计" method:"post" summary:"短剧充值统计"`
	commonApi.Author
	model.SVideoRechargeStatisticsSearchReq
}

// SVideoRechargeStatisticsSearchRes 列表返回结果
type SVideoRechargeStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SVideoRechargeStatisticsSearchRes
}

// SVideoRechargeStatisticsExportReq 导出请求
type SVideoRechargeStatisticsExportReq struct {
	g.Meta `path:"/recharge/export" tags:"分销统计" method:"post" summary:"短剧充值统计表格导出"`
	commonApi.Author
	model.SVideoRechargeStatisticsSearchReq
}

// SVideoRechargeStatisticsExportRes 导出响应
type SVideoRechargeStatisticsExportRes struct {
	commonApi.EmptyRes
}
