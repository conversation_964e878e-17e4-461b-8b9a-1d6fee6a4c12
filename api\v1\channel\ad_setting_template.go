// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-14 11:15:55
// 生成路径: api/v1/channel/ad_setting_template.go
// 生成人：cq
// desc:广告回传模板相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// AdSettingTemplateSearchReq 分页请求参数
type AdSettingTemplateSearchReq struct {
	g.Meta `path:"/list" tags:"广告回传模板" method:"post" summary:"广告回传模板列表"`
	commonApi.Author
	model.AdSettingTemplateSearchReq
}

// AdSettingTemplateSearchRes 列表返回结果
type AdSettingTemplateSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingTemplateSearchRes
}

// AdSettingTemplateAddReq 添加操作请求参数
type AdSettingTemplateAddReq struct {
	g.Meta `path:"/add" tags:"广告回传模板" method:"post" summary:"广告回传模板添加"`
	commonApi.Author
	*model.AdSettingTemplateAddReq
}

// AdSettingTemplateAddRes 添加操作返回结果
type AdSettingTemplateAddRes struct {
	commonApi.EmptyRes
	AdSettingTemplateId   int64  `json:"adSettingTemplateId"`
	AdSettingTemplateName string `json:"adSettingTemplateName"`
}

// AdSettingTemplateEditReq 修改操作请求参数
type AdSettingTemplateEditReq struct {
	g.Meta `path:"/edit" tags:"广告回传模板" method:"put" summary:"广告回传模板修改"`
	commonApi.Author
	*model.AdSettingTemplateEditReq
}

// AdSettingTemplateEditRes 修改操作返回结果
type AdSettingTemplateEditRes struct {
	commonApi.EmptyRes
}

// AdSettingTemplateGetReq 获取一条数据请求
type AdSettingTemplateGetReq struct {
	g.Meta `path:"/get" tags:"广告回传模板" method:"get" summary:"获取广告回传模板信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdSettingTemplateGetRes 获取一条数据结果
type AdSettingTemplateGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdSettingTemplateInfoRes
}

// AdSettingTemplateDeleteReq 删除数据请求
type AdSettingTemplateDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告回传模板" method:"post" summary:"删除广告回传模板"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdSettingTemplateDeleteRes 删除数据返回
type AdSettingTemplateDeleteRes struct {
	commonApi.EmptyRes
}

// AdSettingTemplateCopyReq 复制回传模板请求
type AdSettingTemplateCopyReq struct {
	g.Meta `path:"/copy" tags:"广告回传模板" method:"post" summary:"复制回传模板"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"`
}

// AdSettingTemplateCopyRes 复制回传模板返回
type AdSettingTemplateCopyRes struct {
	commonApi.EmptyRes
}

// AdSettingTemplateShareReq 共享回传模板请求
type AdSettingTemplateShareReq struct {
	g.Meta `path:"/share" tags:"广告回传模板" method:"post" summary:"共享回传模板"`
	commonApi.Author
	Id      int64    `p:"id" v:"required#主键必须"`
	UserIds []uint64 `p:"userIds" dc:"共享用户ID列表"`
}

// AdSettingTemplateShareRes 共享回传模板返回
type AdSettingTemplateShareRes struct {
	commonApi.EmptyRes
}

// AdSettingTemplateSetDefaultReq 设置回传模板是否默认请求
type AdSettingTemplateSetDefaultReq struct {
	g.Meta `path:"/setDefault" tags:"广告回传模板" method:"post" summary:"设置回传模板是否默认"`
	commonApi.Author
	Id        int64 `p:"id" v:"required#主键必须"`
	IsDefault int   `p:"isDefault" v:"required#是否默认必须" dc:"是否默认 0：否 1：是"`
}

// AdSettingTemplateSetDefaultRes 设置回传模板是否默认返回
type AdSettingTemplateSetDefaultRes struct {
	commonApi.EmptyRes
}

// AdSettingTemplateGetDefaultReq 获取默认模板请求
type AdSettingTemplateGetDefaultReq struct {
	g.Meta `path:"/getDefault" tags:"小程序设置" method:"post" summary:"获取默认模板"`
	commonApi.Author
}

// AdSettingTemplateGetDefaultRes 获取默认模板返回
type AdSettingTemplateGetDefaultRes struct {
	commonApi.EmptyRes
	*model.AdSettingTemplateInfoRes
}
