// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-22 16:43:37
// 生成路径: api/v1/theater/s_dy_auth_authorize.go
// 生成人：cq
// desc:抖音短剧授权表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyAuthAuthorizeSearchReq 分页请求参数
type SDyAuthAuthorizeSearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"get" summary:"短剧授权表列表"`
	commonApi.Author
	model.SDyAuthAuthorizeSearchReq
}

// SDyAuthAuthorizeSearchRes 列表返回结果
type SDyAuthAuthorizeSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAuthAuthorizeSearchRes
}

// SDyAuthAuthorizeAddReq 添加操作请求参数
type SDyAuthAuthorizeAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"添加短剧授权"`
	commonApi.Author
	*model.SDyAuthAuthorizeAddReq
}

// SDyAuthAuthorizeAddRes 添加操作返回结果
type SDyAuthAuthorizeAddRes struct {
	commonApi.EmptyRes
}
