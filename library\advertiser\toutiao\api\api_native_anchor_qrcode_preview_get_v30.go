/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// NativeAnchorQrcodePreviewGetV30ApiService NativeAnchorQrcodePreviewGetV30Api service

type NativeAnchorQrcodePreviewGetV30ApiService struct {
	ctx          context.Context
	cfg          *conf.Configuration
	token        string
	AdvertiserId *int64
	AnchorIds    *[]string
	AnchorType   *models.NativeAnchorQrcodePreviewGetV30AnchorType
}

func (r *NativeAnchorQrcodePreviewGetV30ApiService) SetCfg(cfg *conf.Configuration) *NativeAnchorQrcodePreviewGetV30ApiService {
	r.cfg = cfg
	return r
}

func (r *NativeAnchorQrcodePreviewGetV30ApiService) SetReq(anchorIds []string, advertiserId int64, anchorType *models.NativeAnchorQrcodePreviewGetV30AnchorType) *NativeAnchorQrcodePreviewGetV30ApiService {
	*r.AnchorIds = anchorIds
	*r.AdvertiserId = advertiserId
	r.AnchorType = anchorType
	return r
}

func (r *NativeAnchorQrcodePreviewGetV30ApiService) AccessToken(accessToken string) *NativeAnchorQrcodePreviewGetV30ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *NativeAnchorQrcodePreviewGetV30ApiService) Do() (data *models.NativeAnchorQrcodePreviewGetV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/native_anchor/qrcode_preview/get/"
	localVarQueryParams := map[string]string{}

	if r.AnchorIds == nil {
		return nil, errors.New("anchorIds is required and must be specified")
	}
	if len(*r.AnchorIds) < 1 {
		return nil, errors.New("anchorIds must have at least 1 elements")
	}
	if len(*r.AnchorIds) > 20 {
		return nil, errors.New("anchorIds must have less than 20 elements")
	}
	if r.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.AnchorType == nil {
		return nil, errors.New("anchorType is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "anchor_ids", r.AnchorIds)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "anchor_type", r.AnchorType)

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.NativeAnchorQrcodePreviewGetV30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.NativeAnchorQrcodePreviewGetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/native_anchor/qrcode_preview/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
