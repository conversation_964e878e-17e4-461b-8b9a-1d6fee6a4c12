// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-03-07 15:30:59
// 生成路径: internal/app/applet/dao/internal/s_customer_qr_code_config.go
// 生成人：cyao
// desc:微信二维码配置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SCustomerQrCodeConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type SCustomerQrCodeConfigDao struct {
	table   string                       // Table is the underlying table name of the DAO.
	group   string                       // Group is the database configuration group name of current DAO.
	columns SCustomerQrCodeConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// SCustomerQrCodeConfigColumns defines and stores column names for table s_customer_qr_code_config.
type SCustomerQrCodeConfigColumns struct {
	Id         string // id
	AppId      string // 小程序ID
	QrCodeUrl  string // 小程序名称
	Remark     string // 备注
	Createtime string // 创建时间
	Updatetime string // 更新时间
}

var sCustomerQrCodeConfigColumns = SCustomerQrCodeConfigColumns{
	Id:         "id",
	AppId:      "app_id",
	QrCodeUrl:  "qr_code_url",
	Remark:     "remark",
	Createtime: "createtime",
	Updatetime: "updatetime",
}

// NewSCustomerQrCodeConfigDao creates and returns a new DAO object for table data access.
func NewSCustomerQrCodeConfigDao() *SCustomerQrCodeConfigDao {
	return &SCustomerQrCodeConfigDao{
		group:   "default",
		table:   "s_customer_qr_code_config",
		columns: sCustomerQrCodeConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SCustomerQrCodeConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SCustomerQrCodeConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SCustomerQrCodeConfigDao) Columns() SCustomerQrCodeConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SCustomerQrCodeConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SCustomerQrCodeConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SCustomerQrCodeConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
