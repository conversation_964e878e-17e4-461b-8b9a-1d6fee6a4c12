// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-20 15:16:54
// 生成路径: internal/app/ad/dao/ad_xt_task_settle.go
// 生成人：cyao
// desc:星图任务结算数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adXtTaskSettleDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adXtTaskSettleDao struct {
	*internal.AdXtTaskSettleDao
}

var (
	// AdXtTaskSettle is globally public accessible object for table tools_gen_table operations.
	AdXtTaskSettle = adXtTaskSettleDao{
		internal.NewAdXtTaskSettleDao(),
	}
)

// Fill with you ideas below.
