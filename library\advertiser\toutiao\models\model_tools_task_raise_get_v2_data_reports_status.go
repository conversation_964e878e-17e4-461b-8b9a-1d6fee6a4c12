/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsTaskRaiseGetV2DataReportsStatus
type ToolsTaskRaiseGetV2DataReportsStatus string

// List of tools_task_raise_get_v2_data_reports_status
const (
	RAISING_ToolsTaskRaiseGetV2DataReportsStatus ToolsTaskRaiseGetV2DataReportsStatus = "RAISING"
	STOP_ToolsTaskRaiseGetV2DataReportsStatus    ToolsTaskRaiseGetV2DataReportsStatus = "STOP"
)

// Ptr returns reference to tools_task_raise_get_v2_data_reports_status value
func (v ToolsTaskRaiseGetV2DataReportsStatus) Ptr() *ToolsTaskRaiseGetV2DataReportsStatus {
	return &v
}
