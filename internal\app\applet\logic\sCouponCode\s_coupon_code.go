// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-07-23 14:36:19
// 生成路径: internal/app/applet/logic/s_coupon_code.go
// 生成人：lx
// desc:兑换码表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	jsonGo "github.com/json-iterator/go"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	comConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysConsts "github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"google.golang.org/appengine/log"
	"strconv"
	"strings"
	"time"
)

func init() {
	service.RegisterSCouponCode(New())
}

func New() service.ISCouponCode {
	return &sSCouponCode{}
}

type sSCouponCode struct{}

func (s *sSCouponCode) CouponStockNotifyTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		PlatCouponCockStockNotifyLocK := comConsts.PlatCouponCockStockNotifyLocK
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(PlatCouponCockStockNotifyLocK, redsync.WithTries(1), redsync.WithExpiry(time.Second*10), redsync.WithRetryDelay(50*time.Millisecond))
		if err := mutex.TryLockContext(ctx); err != nil {
			g.Log().Info(ctx, "Redisson没有获取到分布式锁："+PlatCouponCockStockNotifyLocK+", TaskName :SynCouponCode ")
			return
		}
		//
		current, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeCurrentIndex)
		num, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeListNum)
		notifyStatus, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCockStockNotifyStatus)
		if notifyStatus.Int() == 0 {
			//一个小时前未通知
			if current.Int() == num.Int() {
				//使用最后的队列了，看看是否要补货
				length, _ := commonService.RedisCache().LLen(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int()))
				stockNotifyNum := g.Cfg().MustGet(ctx, "transportCode.stockNotifyNum").Int64()
				if length <= stockNotifyNum {
					phones := g.Cfg().MustGet(ctx, "transportCode.stockNotifyPhones").String()
					signName := g.Cfg().MustGet(ctx, "sms.signName").String()
					stockNotifyTemplate := g.Cfg().MustGet(ctx, "transportCode.stockNotifyTemplate").String()
					libUtils.SendMessageWithTemplate(ctx, &phones, &signName, &stockNotifyTemplate, nil)
					commonService.RedisCache().Set(ctx, comConsts.PlatCouponCockStockNotifyStatus, 1)
					commonService.RedisCache().Expire(ctx, comConsts.PlatCouponCockStockNotifyStatus, 3600)
				}
			}
		}

	})
	if err != nil {
		g.Log().Error(ctx, err)
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CouponStockNotifyTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵库存通知，执行失败",
		})
	} else {
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CouponStockNotifyTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵库存通知，执行成功",
		})
	}

}
func (s *sSCouponCode) CouponStockNotifyTaskNew(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		PlatCouponCockStockNotifyLocK := comConsts.PlatCouponCockStockNotifyLocK
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(PlatCouponCockStockNotifyLocK, redsync.WithTries(1), redsync.WithExpiry(time.Second*10), redsync.WithRetryDelay(50*time.Millisecond))
		if err := mutex.TryLockContext(ctx); err != nil {
			g.Log().Info(ctx, "Redisson没有获取到分布式锁："+PlatCouponCockStockNotifyLocK+", TaskName :SynCouponCode ")
			return
		}
		//查询数据规格
		sysDictData, _ := commonService.SysDictData().GetByType(ctx, "product_specification") //查询产品规划
		for _, v := range sysDictData {
			if v.DictValue == "1" {
				current, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeCurrentIndex)
				num, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeListNum)
				notifyStatus, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCockStockNotifyStatus)
				if notifyStatus.Int() == 0 {
					//一个小时前未通知
					if current.Int() == num.Int() {
						//使用最后的队列了，看看是否要补货
						length, _ := commonService.RedisCache().LLen(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int()))
						stockNotifyNum := g.Cfg().MustGet(ctx, "transportCode.stockNotifyNum").Int64()
						if length <= stockNotifyNum {
							phones := g.Cfg().MustGet(ctx, "transportCode.stockNotifyPhones").String()
							signName := g.Cfg().MustGet(ctx, "sms.signName").String()
							stockNotifyTemplate := g.Cfg().MustGet(ctx, "transportCode.stockNotifyTemplate").String()
							m := make(map[string]string)
							m["productName"] = "乘车码"
							m["specs"] = v.DictValue
							m["num"] = strconv.Itoa(int(length))
							param, _ := json.Marshal(m)
							paramStr := string(param)
							libUtils.SendMessageWithTemplate(ctx, &phones, &signName, &stockNotifyTemplate, &paramStr)
							commonService.RedisCache().Set(ctx, comConsts.PlatCouponCockStockNotifyStatus, 1)
							commonService.RedisCache().Expire(ctx, comConsts.PlatCouponCockStockNotifyStatus, 3600)
						}
					}
				}
			} else {
				current, _ := commonService.RedisCache().Get(ctx, fmt.Sprintf("%s%s", comConsts.PlatCouponCodeCurrentIndexSpecs, v.DictValue))
				num, _ := commonService.RedisCache().Get(ctx, fmt.Sprintf("%s%s", comConsts.PlatCouponCodeListNumSpecs, v.DictValue))
				notifyStatus, _ := commonService.RedisCache().Get(ctx, fmt.Sprintf("%s%s", comConsts.PlatCouponCockStockNotifyStatusSpecs, v.DictValue))
				if notifyStatus.Int() == 0 {
					//一个小时前未通知
					if current.Int() == num.Int() {
						//使用最后的队列了，看看是否要补货
						length, _ := commonService.RedisCache().LLen(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int()))
						stockNotifyNum := g.Cfg().MustGet(ctx, "transportCode.stockNotifyNum").Int64()
						if length <= stockNotifyNum {
							phones := g.Cfg().MustGet(ctx, "transportCode.stockNotifyPhones").String()
							signName := g.Cfg().MustGet(ctx, "sms.signName").String()
							stockNotifyTemplate := g.Cfg().MustGet(ctx, "transportCode.stockNotifyTemplate").String()
							m := make(map[string]string)
							m["productName"] = "乘车码"
							m["specs"] = v.DictValue
							m["num"] = strconv.Itoa(int(length))
							param, _ := json.Marshal(m)
							paramStr := string(param)
							libUtils.SendMessageWithTemplate(ctx, &phones, &signName, &stockNotifyTemplate, &paramStr)
							commonService.RedisCache().Set(ctx, comConsts.PlatCouponCockStockNotifyStatusSpecs, 1)
							commonService.RedisCache().Expire(ctx, comConsts.PlatCouponCockStockNotifyStatusSpecs, 3600)
						}
					}
				}
			}
		}

	})
	if err != nil {
		g.Log().Error(ctx, err)
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CouponStockNotifyTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵库存通知，执行失败",
		})
	} else {
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CouponStockNotifyTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵库存通知，执行成功",
		})
	}

}
func (s *sSCouponCode) GetByCouponCode(ctx context.Context, code string) (res *model.SCouponCodeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SCouponCode.Ctx(ctx).WithAll().Where(dao.SCouponCode.Columns().Code, code).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSCouponCode) SynCouponCodeTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		synCouponLock := comConsts.PlatCouponCockStockLocK
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(synCouponLock, redsync.WithTries(1), redsync.WithExpiry(time.Second*20))
		if err := mutex.TryLockContext(ctx); err != nil {
			g.Log().Info(ctx, "Redisson没有获取到分布式锁："+synCouponLock+", TaskName :SynCouponCode ")
			return
		}
		exchangeCodeRes := new(model.ExchangeCodeRes)
		exchangeCodeReq := new(model.ExchangeCodeReq)
		exchangeCodeReq.Key = g.Cfg().MustGet(ctx, "transportCode.key").String()
		queueSize := g.Cfg().MustGet(ctx, "transportCode.queueSize").Int()
		exchangeCodeReq.Limit = queueSize
		exchangeCodeReq.ExchangeStatus = 1 //查询未使用的
		url := g.Cfg().MustGet(ctx, "transportCode.url").String()
		var response []byte
		var jsonByte []byte
		var statList []*model.SCouponCodeInfoRes
		hasNext := 1
		current, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeCurrentIndex) //取当前队列
		length, _ := commonService.RedisCache().LLen(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int()))
		//redis队列，还有数据，说明是补货，补货的话，把当前key改成previous数据库
		if length > 0 {
			//先消耗上一队列的数据
			commonService.RedisCache().Rename(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int()), comConsts.PlatCouponCodePreList)
		}
		for i := 1; hasNext > 0; i++ {
			exchangeCodeReq.Page = i
			jsonByte, _ = json.Marshal(exchangeCodeReq)
			fmt.Println(string(jsonByte))
			client := libUtils.NewHTTPClient(sysConsts.Timeout * time.Second)
			response, _ = client.PostWithHeader(url, jsonByte, map[string]string{
				"Content-Type": "application/json",
			})
			resStr := strings.Replace(string(response), "\n", "", -1)
			resStr = strings.Replace(resStr, "\r", "", -1)
			resStr = strings.Replace(resStr, "\\", "", -1)
			index := strings.Index(resStr, "{")
			resStr = resStr[index:]
			err := jsonGo.UnmarshalFromString(resStr, &exchangeCodeRes)
			if err != nil {
				log.Errorf(ctx, "请求获取乘车兑换码接口异常, err: %v", err)
				return
			}
			if exchangeCodeRes.Status == 1 {

				exchangeCodeDataList := exchangeCodeRes.Result
				if len(exchangeCodeDataList) > 0 {
					for _, v := range exchangeCodeDataList {
						num := v.Num //可用数目
						//判断下数据是否存在，不存在的才加入到list中去
						couponCode, _ := s.GetByCouponCode(ctx, v.Code)
						if couponCode != nil {
							//存在数据，继续循环
							continue
						}
						//判断当前规格的数据，需要入哪个队列
						//	key := s.GetPlatCouponCodeListName(ctx, num, queueSize)

						sCouponCodeInfoRes := new(model.SCouponCodeInfoRes)
						sCouponCodeInfoRes.Code = v.Code
						availableEndTime := time.Unix(v.AvailableEndTime, 0)
						t := new(gtime.Time)
						t.Time = availableEndTime
						sCouponCodeInfoRes.ExpireTime = t
						statList = append(statList, sCouponCodeInfoRes)
						sCouponCodeInfoRes.CreateTime = gtime.Now()
						sCouponCodeInfoRes.UpdateTime = gtime.Now()
						sCouponCodeInfoRes.Num = num
						_, err = commonService.RedisCache().LPush(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, i), v.Code)
						startSecond := time.Now().Unix() - 200 //提早一点过期
						endSecond := t.Unix()                  //到时时间
						expTime := endSecond - startSecond
						_, err = commonService.RedisCache().Expire(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, i), expTime)
						if err != nil {
							log.Errorf(ctx, " %s push到redis异常", v.Code)
						}

					}

					dao.SCouponCode.Ctx(ctx).Save(statList)
					commonService.RedisCache().Set(ctx, comConsts.PlatCouponCodeListNum, i)      //队列个数
					commonService.RedisCache().Set(ctx, comConsts.PlatCouponCodeCurrentIndex, 1) //设置当前队列为1
					statList = []*model.SCouponCodeInfoRes{}                                     //清除数据
				} else {
					hasNext = hasNext - 1 //减少一次，减少到0后，说明没有下一页数据了
				}
			} else {
				hasNext = hasNext - 1
			}
		}
	})
	if err != nil {
		g.Log().Error(ctx, err)
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SynCouponCodeTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵同步，执行失败",
		})
	} else {
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SynCouponCodeTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵同步，执行成功",
		})
	}
}
func (s *sSCouponCode) SynCouponCodeSpecsTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		synCouponLock := comConsts.PlatCouponCockStockLocK
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(synCouponLock, redsync.WithTries(1), redsync.WithExpiry(time.Second*20))
		if err := mutex.TryLockContext(ctx); err != nil {
			g.Log().Info(ctx, "Redisson没有获取到分布式锁："+synCouponLock+", TaskName :SynCouponCode ")
			return
		}
		exchangeCodeRes := new(model.ExchangeCodeRes)
		exchangeCodeReq := new(model.ExchangeCodeReq)
		exchangeCodeReq.Key = g.Cfg().MustGet(ctx, "transportCode.key").String()
		queueSize := g.Cfg().MustGet(ctx, "transportCode.queueSize").Int()
		exchangeCodeReq.Limit = queueSize
		exchangeCodeReq.ExchangeStatus = 1 //查询未使用的
		url := g.Cfg().MustGet(ctx, "transportCode.url").String()
		var response []byte
		var jsonByte []byte
		var statList []*model.SCouponCodeInfoRes
		hasNext := 1

		sysDictDatas, _ := commonService.SysDictData().GetByType(ctx, "product_specification") //查询产品规划
		for _, v := range sysDictDatas {
			speci := v.DictValue
			if speci == "1" {
				//将遗留的数据，放入到pre队列中
				current, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeCurrentIndex) //取当前队列
				currKey := fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int())
				length, _ := commonService.RedisCache().LLen(ctx, currKey)
				if length > 0 {
					commonService.RedisCache().Rename(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, current.Int()), comConsts.PlatCouponCodePreList)
				}
				commonService.RedisCache().Set(ctx, comConsts.PlatCouponCodeCurrentIndex, 1) //设置当前队列为1
			} else {
				//将遗留的数据，放入到pre队列中
				current, _ := commonService.RedisCache().Get(ctx, fmt.Sprintf("%s%s", comConsts.PlatCouponCodeCurrentIndexSpecs, speci)) //取当前规格队列
				currKey := fmt.Sprintf("%s%s%s%d", comConsts.PlatCouponCodeListSpecs, speci, ":", current.Int())
				length, _ := commonService.RedisCache().LLen(ctx, currKey)
				if length > 0 {
					commonService.RedisCache().Rename(ctx, currKey, fmt.Sprintf("%s%s", comConsts.PlatCouponCodePreListSpecs, speci))
				}
				commonService.RedisCache().Set(ctx, fmt.Sprintf("%s%s", comConsts.PlatCouponCodeCurrentIndexSpecs, speci), 1) //设置当前队列为1
			}
		}
		//读取数据
		for i := 1; hasNext > 0; i++ {
			exchangeCodeReq.Page = i
			jsonByte, _ = json.Marshal(exchangeCodeReq)
			client := libUtils.NewHTTPClient(sysConsts.Timeout * time.Second)
			response, _ = client.PostWithHeader(url, jsonByte, map[string]string{
				"Content-Type": "application/json",
			})
			resStr := strings.Replace(string(response), "\n", "", -1)
			resStr = strings.Replace(resStr, "\r", "", -1)
			resStr = strings.Replace(resStr, "\\", "", -1)
			index := strings.Index(resStr, "{")
			resStr = resStr[index:]
			err := jsonGo.UnmarshalFromString(resStr, &exchangeCodeRes)
			if err != nil {
				log.Errorf(ctx, "请求获取乘车兑换码接口异常, err: %v", err)
				return
			}
			if exchangeCodeRes.Status == 1 {

				exchangeCodeDataList := exchangeCodeRes.Result
				////rand.Seed(time.Now().UnixNano()) // 设置种子
				////num := rand.Intn(100)
				////var exchangeCodeDataList []*model.ExchangeCodeData
				////codeData1 := new(model.ExchangeCodeData)
				////codeData1.Code = strconv.Itoa(num)
				////codeData1.Num = 5
				////codeData1.AvailableEndTime = 1725897599
				////codeData2 := new(model.ExchangeCodeData)
				////num1 := rand.Intn(100)
				////codeData2.Code = strconv.Itoa(num1)
				////codeData2.Num = 10
				////codeData2.AvailableEndTime = 1725897599
				////codeData3 := new(model.ExchangeCodeData)
				////num2 := rand.Intn(100)
				////codeData3.Code = strconv.Itoa(num2)
				////codeData3.Num = 1
				////codeData3.AvailableEndTime = 1725897599
				////codeData4 := new(model.ExchangeCodeData)
				////num3 := rand.Intn(100)
				////codeData4.Code = strconv.Itoa(num3)
				////codeData4.Num = 10
				////codeData4.AvailableEndTime = 1725897599
				//
				//if i == 1 {
				//	exchangeCodeDataList = append(exchangeCodeDataList, codeData2)
				//	exchangeCodeDataList = append(exchangeCodeDataList, codeData1)
				//	exchangeCodeDataList = append(exchangeCodeDataList, codeData3)
				//	exchangeCodeDataList = append(exchangeCodeDataList, codeData4)
				//}

				if len(exchangeCodeDataList) > 0 {
					for _, v := range exchangeCodeDataList {
						num := v.Num //可用数目
						//判断下数据是否存在，不存在的才加入到list中去
						couponCode, _ := s.GetByCouponCode(ctx, v.Code)
						if couponCode != nil {
							//存在数据，继续循环
							continue
						}
						//判断当前规格的数据，需要入哪个队列
						key := s.GetPlatCouponCodeListName(ctx, num, queueSize)

						sCouponCodeInfoRes := new(model.SCouponCodeInfoRes)
						sCouponCodeInfoRes.Code = v.Code
						availableEndTime := time.Unix(v.AvailableEndTime, 0)
						t := new(gtime.Time)
						t.Time = availableEndTime
						sCouponCodeInfoRes.ExpireTime = t
						statList = append(statList, sCouponCodeInfoRes)
						sCouponCodeInfoRes.CreateTime = gtime.Now()
						sCouponCodeInfoRes.UpdateTime = gtime.Now()
						sCouponCodeInfoRes.Num = num
						_, err = commonService.RedisCache().LPush(ctx, key, v.Code)
						startSecond := time.Now().Unix() - 200 //提早一点过期
						endSecond := t.Unix()                  //到时时间
						expTime := endSecond - startSecond
						_, err = commonService.RedisCache().Expire(ctx, key, expTime)
						if err != nil {
							log.Errorf(ctx, " %s push到redis异常", v.Code)
						}

					}

					dao.SCouponCode.Ctx(ctx).Save(statList)

					statList = []*model.SCouponCodeInfoRes{} //清除数据
				} else {
					hasNext = hasNext - 1 //减少一次，减少到0后，说明没有下一页数据了
				}
			} else {
				hasNext = hasNext - 1
			}
		}
	})
	if err != nil {
		g.Log().Error(ctx, err)
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SynCouponCodeTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵同步，执行失败",
		})
	} else {
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SynCouponCodeTask",
			CreatedAt:  gtime.Now(),
			Result:     "劵同步，执行成功",
		})
	}
}

// 获取当前的code所需要存放的队列
func (s *sSCouponCode) GetPlatCouponCodeListName(ctx context.Context, speci int, queueSize int) (key string) {
	//判断当前num规格code要入哪个队列
	var number int
	if speci == 1 {
		num, _ := commonService.RedisCache().Get(ctx, comConsts.PlatCouponCodeListNum)
		if num.Int() == 0 {
			number = 1
		} else {
			number = num.Int()
		}
		//判断当前队列大小
		size, _ := commonService.RedisCache().LLen(ctx, fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, number))
		numKey := fmt.Sprintf("%s", comConsts.PlatCouponCodeListNum)
		if size >= int64(queueSize) {
			//增加队列号
			key = fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, number+1)

			commonService.RedisCache().Set(ctx, numKey, number+1)
		} else {
			key = fmt.Sprintf("%s%d", comConsts.PlatCouponCodeList, number)
			commonService.RedisCache().Set(ctx, numKey, number)
		}

	} else {
		numKey := fmt.Sprintf("%s%d", comConsts.PlatCouponCodeListNumSpecs, speci)
		num, _ := commonService.RedisCache().Get(ctx, numKey)
		if num.Int() == 0 {
			number = 1
		} else {
			number = num.Int()
		}

		//判断当前队列大小
		size, _ := commonService.RedisCache().LLen(ctx, fmt.Sprintf("%s%d%s%d", comConsts.PlatCouponCodeListSpecs, speci, ":", number))
		if size >= int64(queueSize) {
			//增加队列号
			key = fmt.Sprintf("%s%d%s%d", comConsts.PlatCouponCodeListSpecs, speci, ":", number+1)
			commonService.RedisCache().Set(ctx, numKey, number+1)
		} else {
			key = fmt.Sprintf("%s%d%s%d", comConsts.PlatCouponCodeListSpecs, speci, ":", number)
			commonService.RedisCache().Set(ctx, numKey, number) //设置队列数目
		}
	}

	return
}
func (s *sSCouponCode) List(ctx context.Context, req *model.SCouponCodeSearchReq) (listRes *model.SCouponCodeSearchRes, err error) {
	listRes = new(model.SCouponCodeSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SCouponCode.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.SCouponCode.Columns().Id+" = ?", req.Id)
		}
		if req.Code != "" {
			m = m.Where(dao.SCouponCode.Columns().Code+" = ?", req.Code)
		}
		if req.ExpireTime != "" {
			m = m.Where(dao.SCouponCode.Columns().ExpireTime+" = ?", gconv.Time(req.ExpireTime))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.SCouponCode.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.SCouponCode.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		if req.UseStatus != "" {
			m = m.Where(dao.SCouponCode.Columns().UseStatus+" = ?", gconv.Int(req.UseStatus))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SCouponCodeListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SCouponCodeListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.SCouponCodeListRes{
				Id:         v.Id,
				Code:       v.Code,
				ExpireTime: v.ExpireTime,
				CreateTime: v.CreateTime,
				UpdateTime: v.UpdateTime,
				UseStatus:  v.UseStatus,
			}
		}
	})
	return
}

func (s *sSCouponCode) GetById(ctx context.Context, id int) (res *model.SCouponCodeInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SCouponCode.Ctx(ctx).WithAll().Where(dao.SCouponCode.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSCouponCode) Add(ctx context.Context, req *model.SCouponCodeAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SCouponCode.Ctx(ctx).Insert(do.SCouponCode{
			Code:       req.Code,
			ExpireTime: req.ExpireTime,
			CreateTime: req.CreateTime,
			UpdateTime: req.UpdateTime,
			UseStatus:  req.UseStatus,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSCouponCode) Edit(ctx context.Context, req *model.SCouponCodeEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SCouponCode.Ctx(ctx).WherePri(req.Id).Update(do.SCouponCode{
			Code:       req.Code,
			ExpireTime: req.ExpireTime,
			CreateTime: req.CreateTime,
			UpdateTime: req.UpdateTime,
			UseStatus:  req.UseStatus,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSCouponCode) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SCouponCode.Ctx(ctx).Delete(dao.SCouponCode.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
