// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/dao/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adToolsSiteDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adToolsSiteDao struct {
	*internal.AdToolsSiteDao
}

var (
	// AdToolsSite is globally public accessible object for table tools_gen_table operations.
	AdToolsSite = adToolsSiteDao{
		internal.NewAdToolsSiteDao(),
	}
)

// Fill with you ideas below.
