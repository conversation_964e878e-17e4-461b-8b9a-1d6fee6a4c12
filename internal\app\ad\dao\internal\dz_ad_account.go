// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:25
// 生成路径: internal/app/ad/dao/internal/dz_ad_account.go
// 生成人：cyao
// desc:点众账号管理表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdAccountDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdAccountDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns DzAdAccountColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdAccountColumns defines and stores column names for table dz_ad_account.
type DzAdAccountColumns struct {
	Id          string // 主键ID
	AccountName string // 番茄账号名称
	AccountId   string // 账号ID
	Token       string // 接口token
	Remark      string // 备注
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

var dzAdAccountColumns = DzAdAccountColumns{
	Id:          "id",
	AccountName: "account_name",
	AccountId:   "account_id",
	Token:       "token",
	Remark:      "remark",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewDzAdAccountDao creates and returns a new DAO object for table data access.
func NewDzAdAccountDao() *DzAdAccountDao {
	return &DzAdAccountDao{
		group:   "default",
		table:   "dz_ad_account",
		columns: dzAdAccountColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdAccountDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdAccountDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdAccountDao) Columns() DzAdAccountColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdAccountDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdAccountDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdAccountDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
