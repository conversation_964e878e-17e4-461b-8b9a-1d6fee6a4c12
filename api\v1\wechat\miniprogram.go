package wechat

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/silenceper/wechat/v2/miniprogram/virtualpayment"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

type QueryOrderReq struct {
	g.Meta `path:"/mini/query/order" tags:"微信小程序" method:"get" summary:"查询虚拟支付订单"`
	commonApi.Author
	*virtualpayment.QueryOrderRequest
	AppId string `json:"app_id"`
}

type QueryOrderRes struct {
	commonApi.EmptyRes
	virtualpayment.QueryOrderResponse
}

type RefundOrderReq struct {
	g.Meta `path:"/mini/refund/order" tags:"微信小程序" method:"get" summary:"发起虚拟支付订单退款"`
	commonApi.Author
	*virtualpayment.RefundOrderRequest
	AppId string `json:"app_id"`
}

type RefundOrderRes struct {
	commonApi.EmptyRes
	virtualpayment.RefundOrderResponse
}
