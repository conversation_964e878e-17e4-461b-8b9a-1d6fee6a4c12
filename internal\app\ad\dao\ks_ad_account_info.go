// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-07 11:44:22
// 生成路径: internal/app/ad/dao/ks_ad_account_info.go
// 生成人：cyao
// desc:广告主资质信息余额信息
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdAccountInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdAccountInfoDao struct {
	*internal.KsAdAccountInfoDao
}

var (
	// KsAdAccountInfo is globally public accessible object for table tools_gen_table operations.
	KsAdAccountInfo = ksAdAccountInfoDao{
		internal.NewKsAdAccountInfoDao(),
	}
)

// Fill with you ideas below.
