// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:35
// 生成路径: internal/app/ad/model/entity/dz_ad_ecpm.go
// 生成人：cyao
// desc:广告ECPM信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdEcpm is the golang structure for table dz_ad_ecpm.
type DzAdEcpm struct {
	gmeta.Meta  `orm:"table:dz_ad_ecpm"`
	Id          uint64      `orm:"id,primary" json:"id"`            // 主键ID
	Time        int64       `orm:"time" json:"time"`                // 请求时间戳，单位毫秒
	UserId      string      `orm:"user_id" json:"userId"`           // 用户ID
	ChannelId   string      `orm:"channel_id" json:"channelId"`     // 渠道ID
	AppId       string      `orm:"app_id" json:"appId"`             // 小程序ID
	PromotionId string      `orm:"promotion_id" json:"promotionId"` // 推广链路ID
	OpenId      string      `orm:"open_id" json:"openId"`           // openID
	EcpmId      string      `orm:"ecpm_id" json:"ecpmId"`           // ecpm接口ID
	EcpmCost    string      `orm:"ecpm_cost" json:"ecpmCost"`       // ecpm接口成本，单位十万分之元
	AdType      string      `orm:"ad_type" json:"adType"`           // 广告类型，如激励视频广告/插屏广告等
	EventTime   int64       `orm:"event_time" json:"eventTime"`     // ecpm接口事件时间，单位秒
	DyeTime     int64       `orm:"dye_time" json:"dyeTime"`         // 用户染色时间戳，单位秒
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt"`     // 创建时间
}
