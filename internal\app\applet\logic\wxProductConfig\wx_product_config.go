// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-03-08 18:20:26
// 生成路径: internal/app/applet/logic/wx_product_config.go
// 生成人：lx
// desc:产品配置（充值）
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/silenceper/wechat/v2/miniprogram/virtualpayment"
	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/entity"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	wechatService "github.com/tiger1103/gfast/v3/internal/app/wechat/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
	"sync"
)

func init() {
	service.RegisterWxProductConfig(New())
}

func New() service.IWxProductConfig {
	return &sWxProductConfig{}
}

type sWxProductConfig struct{}

func (s *sWxProductConfig) GetPriceList(ctx context.Context, req *applet.WxProductConfigGetPriceReq) (res []*model.WxProductConfigListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.WxProductConfig.Ctx(ctx).WithAll().
			Fields("price").
			Where(dao.WxProductConfig.Columns().DelFlag+" = ?", 1).
			Group(dao.WxProductConfig.Columns().Price).
			OrderAsc(dao.WxProductConfig.Columns().Price).
			Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sWxProductConfig) List(ctx context.Context, req *model.WxProductConfigSearchReq) (listRes *model.WxProductConfigSearchRes, err error) {
	listRes = new(model.WxProductConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.WxProductConfig.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.WxProductConfig.Columns().Id+" = ?", req.Id)
		}
		if req.ProductId != "" {
			m = m.Where(dao.WxProductConfig.Columns().ProductId+" like ?", "%"+req.ProductId+"%")
		}
		if req.ProductName != "" {
			m = m.Where(dao.WxProductConfig.Columns().ProductName+" like ?", "%"+req.ProductName+"%")
		}
		if req.AppId != "" {
			m = m.Where(dao.WxProductConfig.Columns().AppId+" = ?", req.AppId)
		}
		if req.DelFlag != "" {
			m = m.Where(dao.WxProductConfig.Columns().DelFlag+" = ?", req.DelFlag)
		}
		if req.Publish != "" {
			m = m.Where(dao.WxProductConfig.Columns().Publish+" = ?", req.Publish)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.WxProductConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")

		appIds := make([]string, 0)
		linq.From(res).SelectT(func(item *model.WxProductConfigListRes) string {
			return item.AppId
		}).ToSlice(&appIds)
		// 获取小程序名称
		miniInfos, errs := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIds)
		liberr.ErrIsNil(ctx, errs, "获取小程序名称失败")

		listRes.List = make([]*model.WxProductConfigListRes, len(res))
		for k, v := range res {
			config := &model.WxProductConfigListRes{
				Id:          v.Id,
				ProductId:   v.ProductId,
				ProductName: v.ProductName,
				Price:       v.Price,
				AppId:       v.AppId,
				DelFlag:     v.DelFlag,
				Publish:     v.Publish,
				Remark:      v.Remark,
				ImgUrl:      v.ImgUrl,
			}
			appInterface := linq.From(miniInfos).WhereT(func(item *sysModel.GetMiniInfoListRes) bool {
				return item.AppId == v.AppId
			}).First()
			if miniInfo, ok := appInterface.(*sysModel.GetMiniInfoListRes); ok {
				config.AppName = miniInfo.AppName
			}
			listRes.List[k] = config
		}
	})
	return
}

func (s *sWxProductConfig) GetById(ctx context.Context, id int) (res *model.WxProductConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.WxProductConfig.Ctx(ctx).WithAll().Where(dao.WxProductConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// Add 新增道具
func (s *sWxProductConfig) Add(ctx context.Context, req *model.WxProductConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 校验道具ID
		if !libUtils.IsValidStr(req.ProductId) {
			err = errors.New("道具id只允许使用字母、数字、'_'、'-'")
			liberr.ErrIsNil(ctx, err)
		}
		// 校验道具是否已存在 productId和appId唯一
		configs := make([]*entity.WxProductConfig, 0)
		err = dao.WxProductConfig.Ctx(ctx).
			Where(dao.WxProductConfig.Columns().ProductId, req.ProductId).
			WhereIn(dao.WxProductConfig.Columns().AppId, req.AppIds).Scan(&configs)
		if len(configs) > 0 {
			appIds := make([]string, 0)
			linq.From(configs).SelectT(func(item *entity.WxProductConfig) string {
				return item.AppId
			}).ToSlice(&appIds)
			err = errors.New(fmt.Sprintf("小程序：%s，已存在该道具ID：%s", strings.Join(appIds, ","), req.ProductId))
			liberr.ErrIsNil(ctx, err)
		}
		env := g.Config("wechat").MustGet(ctx, "wechat.virtual.env").Int()
		uploadItem := make([]*virtualpayment.UploadItem, 0)
		uploadItem = append(uploadItem, &virtualpayment.UploadItem{
			ID:      req.ProductId,
			Name:    req.ProductName,
			Price:   gconv.Int(req.Price * 100),
			Remark:  req.Remark,
			ItemURL: req.ImgUrl,
		})
		pool := grpool.New(len(req.AppIds))
		var wg sync.WaitGroup
		var mu sync.Mutex
		for _, appId := range req.AppIds {
			wg.Add(1)
			appIdCopy := appId
			errs := pool.Add(ctx, func(ctx context.Context) {
				defer wg.Done()
				g.Log().Infof(ctx, "启动批量上传道具任务appId：%s", appIdCopy)
				res, errs := wechatService.MiniProgram(appIdCopy).StartUploadGoods(ctx, &virtualpayment.StartUploadGoodsRequest{
					UploadItem: uploadItem,
					Env:        virtualpayment.Env(env),
				})
				g.Log().Infof(ctx, "启动批量上传道具任务res：%v", res)
				if errs != nil {
					g.Log().Errorf(context.Background(), "批量上传道具任务异常：%v", errs)
					return
				}
				if res.CommonError.ErrCode != 0 {
					g.Log().Errorf(context.Background(), "批量上传道具任务失败：%s", res.CommonError.ErrMsg)
					return
				}
				mu.Lock()
				configs = append(configs, &entity.WxProductConfig{
					ProductId:   req.ProductId,
					ProductName: req.ProductName,
					Price:       req.Price,
					AppId:       appIdCopy,
					Remark:      req.Remark,
					ImgUrl:      req.ImgUrl,
					DelFlag:     1,
					Publish:     0,
				})
				mu.Unlock()
			})
			if errs != nil {
				g.Log().Errorf(context.Background(), "批量上传道具任务异常：%v", errs)
			}
		}
		wg.Wait()
		for _, config := range configs {
			g.Log().Infof(ctx, "启动批量上传道具任务config：%+v", config)
		}
		_, err = dao.WxProductConfig.Ctx(ctx).Save(configs)
		liberr.ErrIsNil(ctx, err, "添加道具失败")
	})
	return
}

// Publish 发布道具
func (s *sWxProductConfig) Publish(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		env := g.Config("wechat").MustGet(ctx, "wechat.virtual.env").Int()
		configs := make([]*entity.WxProductConfig, 0)
		err = dao.WxProductConfig.Ctx(ctx).WhereIn(dao.WxProductConfig.Columns().Id, ids).Scan(&configs)

		productIds := make([]string, 0)
		linq.From(configs).WhereT(func(item *entity.WxProductConfig) bool {
			return item.Publish == 1
		}).SelectT(func(item *entity.WxProductConfig) string {
			return item.ProductId
		}).ToSlice(&productIds)
		if len(productIds) > 0 {
			err = errors.New(fmt.Sprintf("道具ID：%s已发布", strings.Join(productIds, ",")))
			liberr.ErrIsNil(ctx, err)
		}

		// 根据appId分组，批量发布
		var groupRes []linq.Group
		linq.From(configs).GroupByT(
			func(key *entity.WxProductConfig) string { return key.AppId },
			func(value *entity.WxProductConfig) *entity.WxProductConfig { return value }).
			ToSlice(&groupRes)

		pool := grpool.New(len(groupRes))
		var wg sync.WaitGroup
		for _, group := range groupRes {
			publishConfigs := make([]*entity.WxProductConfig, 0)
			publishItem := make([]*virtualpayment.PublishItem, 0)
			for _, configInterface := range group.Group {
				if config, ok := configInterface.(*entity.WxProductConfig); ok {
					publishItem = append(publishItem, &virtualpayment.PublishItem{
						ID: config.ProductId,
					})
					publishConfigs = append(publishConfigs, config)
				}
			}
			if appId, ok := group.Key.(string); ok {
				appIdCopy := appId
				wg.Add(1)
				errs := pool.Add(ctx, func(ctx context.Context) {
					defer wg.Done()
					res, errs := wechatService.MiniProgram(gconv.String(appIdCopy)).StartPublishGoods(ctx, &virtualpayment.StartPublishGoodsRequest{
						Env:         virtualpayment.Env(env),
						PublishItem: publishItem,
					})
					g.Log().Infof(ctx, "启动批量发布道具任务res：%v", res)
					if errs != nil {
						g.Log().Errorf(context.Background(), "批量上传道具任务异常：%v", errs)
						return
					}
					if res.CommonError.ErrCode != 0 {
						g.Log().Errorf(context.Background(), "批量上传道具任务失败：%s", res.CommonError.ErrMsg)
						return
					}
					for _, v := range publishConfigs {
						v.Publish = 1
					}
					if len(publishConfigs) > 0 {
						_, err = dao.WxProductConfig.Ctx(ctx).Save(&publishConfigs)
						liberr.ErrIsNil(ctx, err, "发布道具失败")
					}
				})
				if errs != nil {
					g.Log().Errorf(context.Background(), "批量上传道具任务异常：%v", errs)
				}
			}
		}
		wg.Wait()
	})
	return
}

// SetStatus 启用禁用 设置delFag: 1：启用 0：禁用
func (s *sWxProductConfig) SetStatus(ctx context.Context, ids []int, status int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res := make([]*entity.WxProductConfig, 0)
		err = dao.WxProductConfig.Ctx(ctx).WhereIn(dao.WxProductConfig.Columns().Id, ids).Scan(&res)
		for _, v := range res {
			v.DelFlag = status
		}
		if len(res) > 0 {
			_, err = dao.WxProductConfig.Ctx(ctx).Save(&res)
		}
		liberr.ErrIsNil(ctx, err, "修改道具状态失败")
	})
	return
}

func (s *sWxProductConfig) Edit(ctx context.Context, req *model.WxProductConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.WxProductConfig.Ctx(ctx).WherePri(req.Id).Update(do.WxProductConfig{
			ProductId:   req.ProductId,
			ProductName: req.ProductName,
			Price:       req.Price,
			AppId:       req.AppId,
			DelFlag:     req.DelFlag,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sWxProductConfig) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.WxProductConfig.Ctx(ctx).Delete(dao.WxProductConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
