// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-23 15:51:09
// 生成路径: internal/app/ad/controller/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adMaterialUploadController struct {
	systemController.BaseController
}

var AdMaterialUpload = new(adMaterialUploadController)

// List 列表
func (c *adMaterialUploadController) List(ctx context.Context, req *ad.AdMaterialUploadSearchReq) (res *ad.AdMaterialUploadSearchRes, err error) {
	res = new(ad.AdMaterialUploadSearchRes)
	res.AdMaterialUploadSearchRes, err = service.AdMaterialUpload().List(ctx, &req.AdMaterialUploadSearchReq)
	return
}

// Get 获取素材上传之后的表格和广告挂钩
func (c *adMaterialUploadController) Get(ctx context.Context, req *ad.AdMaterialUploadGetReq) (res *ad.AdMaterialUploadGetRes, err error) {
	res = new(ad.AdMaterialUploadGetRes)
	res.AdMaterialUploadInfoRes, err = service.AdMaterialUpload().GetById(ctx, req.Id)
	return
}

func (c *adMaterialUploadController) AdMaterialUploadPull(ctx context.Context, req *ad.AdMaterialUploadPullReq) (res *ad.AdMaterialUploadPullRes, err error) {
	err = service.AdMaterialUpload().Pull(ctx, req.AdvertiserId, req.MaterialType)
	return nil, err
}

func (c *adMaterialUploadController) SyncMediaMaterialToLocal(ctx context.Context, req *ad.SyncMediaMaterialToLocalReq) (res *ad.SyncMediaMaterialToLocalRes, err error) {
	err = service.AdMaterialUpload().SyncMediaMaterialToLocal(ctx, req.SyncMediaMaterialToLocalReq)
	return
}

// Add 添加素材上传之后的表格和广告挂钩
func (c *adMaterialUploadController) Add(ctx context.Context, req *ad.AdMaterialUploadAddReq) (res *ad.AdMaterialUploadAddRes, err error) {
	err = service.AdMaterialUpload().Add(ctx, req.AdMaterialUploadAddReq)
	return
}

// Edit 修改素材上传之后的表格和广告挂钩
func (c *adMaterialUploadController) Edit(ctx context.Context, req *ad.AdMaterialUploadEditReq) (res *ad.AdMaterialUploadEditRes, err error) {
	err = service.AdMaterialUpload().Edit(ctx, req.AdMaterialUploadEditReq)
	return
}

// Delete 删除素材上传之后的表格和广告挂钩
func (c *adMaterialUploadController) Delete(ctx context.Context, req *ad.AdMaterialUploadDeleteReq) (res *ad.AdMaterialUploadDeleteRes, err error) {
	err = service.AdMaterialUpload().Delete(ctx, req.Ids)
	return
}
