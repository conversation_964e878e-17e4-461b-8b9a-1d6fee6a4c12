/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// EventManagerDeepBidTypeGetV30ApiService EventManagerDeepBidTypeGetV30Api service
type EventManagerDeepBidTypeGetV30ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApiV30EventManagerDeepBidTypeGetGetRequest
}
type ApiOpenApiV30EventManagerDeepBidTypeGetGetRequest struct {
	AdvertiserId       int64                                                   `json:"advertiserId"`
	ExternalAction     *models.EventManagerDeepBidTypeGetV30ExternalAction     `json:"externalAction"`
	AssetId            *int64                                                  `json:"assetId"`
	DeepExternalAction *models.EventManagerDeepBidTypeGetV30DeepExternalAction `json:"deepExternalAction"`
	ConvertId          *int64                                                  `json:"convertId"`
	DeliveryMode       *models.EventManagerDeepBidTypeGetV30DeliveryMode       `json:"deliveryMode"`
	LandingType        *models.EventManagerDeepBidTypeGetV30LandingType        `json:"landingType"`
	AdType             *models.EventManagerDeepBidTypeGetV30AdType             `json:"adType"`
}

func (r *EventManagerDeepBidTypeGetV30ApiService) SetCfg(cfg *conf.Configuration) *EventManagerDeepBidTypeGetV30ApiService {
	r.cfg = cfg
	return r
}

func (r *EventManagerDeepBidTypeGetV30ApiService) SetRequest(req ApiOpenApiV30EventManagerDeepBidTypeGetGetRequest) *EventManagerDeepBidTypeGetV30ApiService {
	r.Request = &req
	return r
}

func (r *EventManagerDeepBidTypeGetV30ApiService) AccessToken(accessToken string) *EventManagerDeepBidTypeGetV30ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *EventManagerDeepBidTypeGetV30ApiService) Do() (data *models.EventManagerDeepBidTypeGetV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/event_manager/deep_bid_type/get/"
	localVarQueryParams := map[string]string{}

	if r.Request.AdvertiserId < 1 {
		return nil, errors.New("advertiserId must be greater than 1")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	if r.Request.AssetId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "asset_id", r.Request.AssetId)
	}
	if r.Request.DeliveryMode != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "delivery_mode", r.Request.DeliveryMode)
	}
	if r.Request.ExternalAction == nil {
		return nil, errors.New("externalAction is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "external_action", r.Request.ExternalAction)
	if r.Request.DeepExternalAction != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "deep_external_action", r.Request.DeepExternalAction)
	}
	if r.Request.ConvertId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "convert_id", r.Request.ConvertId)
	}
	if r.Request.LandingType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "landing_type", r.Request.LandingType)
	}
	if r.Request.AdType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "ad_type", r.Request.AdType)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.EventManagerDeepBidTypeGetV30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.EventManagerDeepBidTypeGetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/event_manager/deep_bid_type/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}

// Execute executes the request
//
//	@return EventManagerDeepBidTypeGetV30Response
//func (a *EventManagerDeepBidTypeGetV30ApiService) getExecute(r *ApiOpenApiV30EventManagerDeepBidTypeGetGetRequest) (*EventManagerDeepBidTypeGetV30Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodGet
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *EventManagerDeepBidTypeGetV30Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/v3.0/event_manager/deep_bid_type/get/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//	if r.advertiserId == nil {
//		return localVarReturnValue, nil, ReportError("advertiserId is required and must be specified")
//	}
//	if *r.advertiserId < 1 {
//		return localVarReturnValue, nil, ReportError("advertiserId must be greater than 1")
//	}
//	if r.externalAction == nil {
//		return localVarReturnValue, nil, ReportError("externalAction is required and must be specified")
//	}
//
//	parameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
//	if r.assetId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "asset_id", r.assetId)
//	}
//	parameterAddToHeaderOrQuery(localVarQueryParams, "external_action", r.externalAction)
//	if r.deepExternalAction != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "deep_external_action", r.deepExternalAction)
//	}
//	if r.convertId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "convert_id", r.convertId)
//	}
//	if r.deliveryMode != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "delivery_mode", r.deliveryMode)
//	}
//	if r.landingType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "landing_type", r.landingType)
//	}
//	if r.adType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "ad_type", r.adType)
//	}
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
