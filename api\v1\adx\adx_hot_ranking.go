// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: api/v1/adx/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package adx

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

// AdxHotRankingSearchReq 分页请求参数
type AdxHotRankingSearchReq struct {
	g.Meta `path:"/list" tags:"ADX热力榜数据表" method:"post" summary:"ADX热力榜数据表列表"`
	commonApi.Author
	model.AdxHotRankingSearchReq
}

// AdxHotRankingSearchRes 列表返回结果
type AdxHotRankingSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxHotRankingSearchRes
}

// AdxHotRankingPullReq 拉取全量数据
type AdxHotRankingPullReq struct {
	g.Meta `path:"/pull" tags:"ADX热力榜数据表" method:"get" summary:"拉取全量数据"`
	commonApi.Author
	StartTime string
	EndTime   string
}
type AdxHotRankingPullRes struct {
	commonApi.EmptyRes
}

// AdxHotRankingPullIncrementReq 拉取增量数据需要传递日期
type AdxHotRankingPullIncrementReq struct {
	g.Meta `path:"/pullIncrement" tags:"ADX热力榜数据表" method:"get" summary:"拉取增量数据"`
	commonApi.Author
	Date string
}
type AdxHotRankingPullIncrementRes struct {
	commonApi.EmptyRes
}

// AdxHotRankingExportReq 导出请求
type AdxHotRankingExportReq struct {
	g.Meta `path:"/export" tags:"ADX热力榜数据表" method:"post" summary:"ADX热力榜数据表导出"`
	commonApi.Author
	model.AdxHotRankingSearchReq
}

// AdxHotRankingExportRes 导出响应
type AdxHotRankingExportRes struct {
	commonApi.EmptyRes
}

// AdxHotRankingAddReq 添加操作请求参数
type AdxHotRankingAddReq struct {
	g.Meta `path:"/add" tags:"ADX热力榜数据表" method:"post" summary:"ADX热力榜数据表添加"`
	commonApi.Author
	*model.AdxHotRankingAddReq
}

// AdxHotRankingAddRes 添加操作返回结果
type AdxHotRankingAddRes struct {
	commonApi.EmptyRes
}

// AdxHotRankingEditReq 修改操作请求参数
type AdxHotRankingEditReq struct {
	g.Meta `path:"/edit" tags:"ADX热力榜数据表" method:"put" summary:"ADX热力榜数据表修改"`
	commonApi.Author
	*model.AdxHotRankingEditReq
}

// AdxHotRankingEditRes 修改操作返回结果
type AdxHotRankingEditRes struct {
	commonApi.EmptyRes
}

// AdxHotRankingGetReq 获取一条数据请求
type AdxHotRankingGetReq struct {
	g.Meta `path:"/get" tags:"ADX热力榜数据表" method:"get" summary:"获取ADX热力榜数据表信息"`
	commonApi.Author
	PlayletId uint64 `p:"playletId" v:"required#主键必须"` //通过主键获取
}

// AdxHotRankingGetRes 获取一条数据结果
type AdxHotRankingGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdxHotRankingInfoRes
}

// AdxHotRankingDeleteReq 删除数据请求
type AdxHotRankingDeleteReq struct {
	g.Meta `path:"/delete" tags:"ADX热力榜数据表" method:"delete" summary:"删除ADX热力榜数据表"`
	commonApi.Author
	PlayletIds []uint64 `p:"playletIds" v:"required#主键必须"` //通过主键删除
}

// AdxHotRankingDeleteRes 删除数据返回
type AdxHotRankingDeleteRes struct {
	commonApi.EmptyRes
}
