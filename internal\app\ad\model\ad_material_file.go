// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-11 11:34:19
// 生成路径: internal/app/ad/model/ad_material_file.go
// 生成人：cyao
// desc:广告素材文件夹
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdMaterialFileInfoRes is the golang structure for table ad_material_file.
type AdMaterialFileInfoRes struct {
	gmeta.Meta  `orm:"table:ad_material_file"`
	FileId      int         `orm:"file_id,primary" json:"fileId" dc:"id"`    // id
	FileName    string      `orm:"file_name" json:"fileName" dc:"文件夹名称"`     // 文件夹名称
	UserId      int         `orm:"user_id" json:"userId" dc:"创建人"`           // 创建人
	AlbumId     int         `orm:"album_id" json:"albumId" dc:"专辑id"`        // 专辑id
	ParentId    int         `orm:"parent_id" json:"parentId" dc:"父级文件夹的id"`  // 父级文件夹的id
	Remark      string      `orm:"remark" json:"remark" dc:"备注"`             // 备注
	Preview     string      `orm:"preview" json:"preview" dc:"缩略图 使用| 进行分割"` // 缩略图 使用| 进行分割
	MaterialNum int         `orm:"material_num" json:"materialNum" dc:"素材数量"`
	AllPath     string      `orm:"all_path" json:"allPath" dc:"fileId 全路径 方便反查以下划线分隔"` // fileId 全路径 方便反查以下划线分隔
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`              // 创建时间
	AlbumName   string      `orm:"album_name" json:"albumName" dc:"专辑名称"`
}

type MaterialNumByAlbumId struct {
	AlbumId     int    `orm:"album_id" json:"albumId" dc:"专辑id"` // 专辑id
	MaterialNum int    `orm:"material_num" json:"materialNum" dc:"素材数量"`
	Preview     string `orm:"preview" json:"preview" dc:"缩略图 使用| 进行分割"` // 缩略图 使用| 进行分割
}

type AdMaterialFileListRes struct {
	FileId      int         `json:"fileId" dc:"id"`
	FileName    string      `json:"fileName" dc:"文件夹名称"`
	UserId      int         `json:"userId" dc:"创建人"`
	AlbumId     int         `json:"albumId" dc:"专辑id"`
	ParentId    int         `json:"parentId" dc:"父级文件夹的id"`
	Remark      string      `json:"remark" dc:"备注"`
	AllPath     string      `json:"allPath" dc:"fileId 全路径 方便反查以下划线分隔"`
	Preview     string      `orm:"preview" json:"preview" dc:"缩略图 使用| 进行分割"` // 缩略图 使用| 进行分割
	MaterialNum int         `orm:"material_num" json:"materialNum" dc:"素材数量"`
	CreatedAt   *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdMaterialFileSearchReq 分页请求参数
type AdMaterialFileSearchReq struct {
	comModel.PageReq
	FileId    int    `p:"fileId" dc:"id"`                                                         //id
	FileName  string `p:"fileName" dc:"文件夹名称"`                                                    //文件夹名称
	UserId    string `p:"userId" v:"userId@integer#创建人需为整数" dc:"创建人"`                             //创建人
	AlbumId   int    `p:"albumId" v:"albumId@integer#专辑id需为整数" dc:"专辑id"`                         //专辑id
	ParentId  int    `p:"parentId" v:"parentId@integer#父级文件夹的id需为整数" dc:"父级文件夹的id"`               //父级文件夹的id
	AllPath   string `p:"allPath" dc:"fileId 全路径 方便反查以下划线分隔"`                                     //fileId 全路径 方便反查以下划线分隔
	CreatedAt string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

type AdMaterialFileChildListReq struct {
	FileId  int `json:"fileId" dc:"id"`    // 传值二选一
	AlbumId int `json:"albumId" dc:"专辑id"` // 传值二选一
}

type GetAllPath struct {
	AlbumId int    `json:"albumId" dc:"专辑id"`
	AllPath string `p:"allPath" dc:"fileId 全路径 方便反查以下划线分隔"` //fileId 全路径 方便反查以下划线分隔
}

type AdMaterialFileChildListRes struct {
	List []*AdMaterialFileChildList `json:"list"`
}

type AdMaterialFileChildList struct {
	FileId    int64                      `json:"fileId"`
	AlbumId   int64                      `json:"albumId"`
	TitleName string                     `json:"titleName"`
	HaveChild int                        `json:"haveChild"` // 是否拥有下一个层级 1 有 0 没有
	ThisType  int                        `json:"thisType"`  // 1 为素材目录  2 为文件夹目录
	ParentId  int64                      `json:"parentId"  dc:"父级文件夹的id"`
	Children  []*AdMaterialFileChildList `json:"children"`
}

// AdMaterialFileSearchRes 列表返回结果
type AdMaterialFileSearchRes struct {
	comModel.ListRes
	List []*AdMaterialFileListRes `json:"list"`
}

// AdMaterialFileAddReq 添加操作请求参数
type AdMaterialFileAddReq struct {
	FileName    string `p:"fileName" v:"required#文件夹名称不能为空" dc:"文件夹名称"`
	UserId      int    `p:"userId" v:"required#创建人不能为空" dc:"创建人"`
	AlbumId     int    `p:"albumId"  dc:"专辑id"`
	ParentId    int    `p:"parentId"  dc:"父级文件夹的id"`
	Remark      string `p:"remark"  dc:"备注"`
	Preview     string `orm:"preview" json:"preview" dc:"缩略图 使用| 进行分割"` // 缩略图 使用| 进行分割
	MaterialNum int    `orm:"material_num" json:"materialNum" dc:"素材数量"`
	AllPath     string `p:"allPath"  dc:"fileId 全路径 方便反查以下划线分隔"`
}

// AdMaterialFileEditReq 修改操作请求参数
type AdMaterialFileEditReq struct {
	FileId      int    `p:"fileId" v:"required#主键ID不能为空" dc:"id"`
	FileName    string `p:"fileName" v:"required#文件夹名称不能为空" dc:"文件夹名称"`
	UserId      int    `p:"userId" v:"required#创建人不能为空" dc:"创建人"`
	AlbumId     int    `p:"albumId"  dc:"专辑id"`
	ParentId    int    `p:"parentId"  dc:"父级文件夹的id"`
	Remark      string `p:"remark"  dc:"备注"`
	Preview     string `orm:"preview" json:"preview" dc:"缩略图 使用| 进行分割"` // 缩略图 使用| 进行分割
	MaterialNum int    `orm:"material_num" json:"materialNum" dc:"素材数量"`
	AllPath     string `p:"allPath"  dc:"fileId 全路径 方便反查以下划线分隔"`
}
