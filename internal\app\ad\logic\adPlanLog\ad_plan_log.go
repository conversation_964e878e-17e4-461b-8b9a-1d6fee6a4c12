// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: internal/app/ad/logic/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/tiger1103/gfast/v3/library/libUtils"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdPlanLog(New())
}

func New() service.IAdPlanLog {
	return &sAdPlanLog{}
}

type sAdPlanLog struct{}

func (s *sAdPlanLog) List(ctx context.Context, req *model.AdPlanLogSearchReq) (listRes *model.AdPlanLogSearchRes, err error) {
	listRes = new(model.AdPlanLogSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPlanLog.Ctx(ctx).WithAll()

		if req.PlanId != "" {
			m = m.Where(dao.AdPlanLog.Columns().PlanId+" = ?", gconv.Int(req.PlanId))
		}
		if req.RuleName != "" {
			m = m.Where(dao.AdPlanLog.Columns().RuleName+" like ?", "%"+req.RuleName+"%")
		}
		if req.MediaType != "" {
			m = m.Where(dao.AdPlanLog.Columns().MediaType+" = ?", gconv.Int(req.MediaType))
		}
		if req.ObjectType != "" {
			m = m.Where(dao.AdPlanLog.Columns().ObjectType+" = ?", gconv.Int(req.ObjectType))
		}
		if req.ScopeType != "" {
			m = m.Where(dao.AdPlanLog.Columns().ScopeType+" = ?", gconv.Int(req.ScopeType))
		}
		if req.ScopeEntityId != "" {
			m = m.Where(dao.AdPlanLog.Columns().ScopeEntityId+" = ?", gconv.Int64(req.ScopeEntityId))
		}
		if req.ExecutionState != "" {
			m = m.Where(dao.AdPlanLog.Columns().ExecutionState+" = ?", gconv.Int(req.ExecutionState))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdPlanLog.Columns().CreatedAt+" >=? AND "+dao.AdPlanLog.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.StartTime != "" {
			m = m.Where(dao.AdPlanLog.Columns().CreatedAt+" >= ?", req.StartTime)
		}
		if req.EndTime != "" {
			m = m.Where(dao.AdPlanLog.Columns().CreatedAt+" <= ?", req.EndTime)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPlanLogListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPlanLogListRes, len(res))
		idsMap := make(map[int][]int64)
		for k, v := range res {
			if _, ok := idsMap[v.ScopeType]; ok {
				idsMap[v.ScopeType] = append(idsMap[v.ScopeType], v.ScopeEntityId)
			} else {
				idsMap[v.ScopeType] = []int64{v.ScopeEntityId}
			}
			listRes.List[k] = &model.AdPlanLogListRes{
				Id:             v.Id,
				PlanId:         v.PlanId,
				RuleName:       v.RuleName,
				MediaType:      v.MediaType,
				ObjectType:     v.ObjectType,
				ScopeType:      v.ScopeType,
				RuleExecuteId:  v.RuleExecuteId,
				ScopeEntityId:  v.ScopeEntityId,
				Conditions:     v.Conditions,
				Content:        v.Content,
				ExecutionState: v.ExecutionState,
				CreatedAt:      v.CreatedAt,
			}
		}

	})
	return
}

func (s *sAdPlanLog) GetById(ctx context.Context, id int) (res *model.AdPlanLogInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanLog.Ctx(ctx).WithAll().Where(dao.AdPlanLog.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanLog) Add(ctx context.Context, req *model.AdPlanLogAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanLog.Ctx(ctx).Insert(do.AdPlanLog{
			PlanId:         req.PlanId,
			RuleName:       req.RuleName,
			MediaType:      req.MediaType,
			ObjectType:     req.ObjectType,
			ScopeType:      req.ScopeType,
			RuleExecuteId:  req.RuleExecuteId,
			ScopeEntityId:  req.ScopeEntityId,
			Conditions:     req.Conditions,
			Content:        req.Content,
			ExecutionState: req.ExecutionState,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPlanLog) Edit(ctx context.Context, req *model.AdPlanLogEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanLog.Ctx(ctx).WherePri(req.Id).Update(do.AdPlanLog{
			PlanId:         req.PlanId,
			RuleName:       req.RuleName,
			MediaType:      req.MediaType,
			ObjectType:     req.ObjectType,
			ScopeType:      req.ScopeType,
			RuleExecuteId:  req.RuleExecuteId,
			ScopeEntityId:  req.ScopeEntityId,
			Conditions:     req.Conditions,
			Content:        req.Content,
			ExecutionState: req.ExecutionState,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdPlanLog) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanLog.Ctx(ctx).Delete(dao.AdPlanLog.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetDayExecuteCount day 天前 判断当天执行过多少次
func (s *sAdPlanLog) GetDayExecuteCount(ctx context.Context, planId, scopeEntityId, day int) (count int) {
	_ = g.Try(ctx, func(ctx context.Context) {
		day = day - 1
		count, _ = dao.AdPlanLog.Ctx(ctx).Where(dao.AdPlanLog.Columns().PlanId, planId).Where(dao.AdPlanLog.Columns().ScopeEntityId, scopeEntityId).Where(dao.AdPlanLog.Columns().CreatedAt+" >= ?", libUtils.StringTimeAddDay(libUtils.GetNowDate(), -day)).Where(dao.AdPlanLog.Columns().ExecutionState, 1).Count()
	})
	return 0
}
