// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-08-02 18:25:27
// 生成路径: internal/app/applet/logic/s_applet_rechare_statistics.go
// 生成人：len
// desc:小程序充值统计
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterSAppletRechargeStatistics(New())
}

func New() service.ISAppletRechargeStatistics {
	return &sSAppletRechargeStatistics{}
}

type sSAppletRechargeStatistics struct{}

func (s *sSAppletRechargeStatistics) List(ctx context.Context, req *model.SAppletRechargeStatisticsSearchReq) (listRes *model.SAppletRechargeStatisticsSearchRes, err error) {
	listRes = new(model.SAppletRechargeStatisticsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := buildListSql(ctx, req)
		totalMap := buildListSql(ctx, req)

		if req.Merge == 1 {
			m = m.Group("app_id")
		} else {
			m = m.Group("app_id,create_date")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		orderBy := "totalAmount desc"
		//if req.OrderBy != "" {
		//	order = req.OrderBy
		//}
		if req.OrderBy != "" && !libUtils.IsSQLInjection(req.OrderBy) && !libUtils.IsSQLInjection(req.OrderType) {
			orderBy = fmt.Sprintf("%s %s", req.OrderBy, req.OrderType)
		}

		m = m.Fields("any_value(app_id) appId ,any_value(app_name) appName, any_value(app_type) appType,any_value(create_date) createDate")
		m = m.Fields("sum(new_user_amount) newUserAmount,sum(total_amount) totalAmount,sum(wechat_android_recharge_amount) wechatAndroidRechargeAmount")
		m = m.Fields("sum(wechat_ios_recharge_amount) wechatIosRechargeAmount,sum(dy_ios_recharge_amount) dyIosRechargeAmount")
		m = m.Fields("sum(dy_android_recharge_amount) dyAndroidRechargeAmount,sum(total_ad_up) totalAdUp,sum(new_user_ad_up) newUserAdUp")

		// 汇总数据
		err = totalMap.FieldSum("new_user_amount", "newUserAmount").
			FieldSum("total_amount", "totalAmount").
			FieldSum("total_ad_up", "totalAdUp").
			FieldSum("new_user_ad_up", "newUserAdUp").
			FieldSum("dy_ios_recharge_amount", "dyIosRechargeAmount").
			FieldSum("dy_android_recharge_amount", "dyAndroidRechargeAmount").
			FieldSum("wechat_ios_recharge_amount", "wechatIosRechargeAmount").
			FieldSum("wechat_android_recharge_amount", "wechatAndroidRechargeAmount").
			Scan(&listRes.Summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")

		var res []*model.SAppletRechargeStatisticsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(orderBy).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SAppletRechargeStatisticsListRes, len(res))
		for k, v := range res {
			createDate := v.CreateDate
			if req.Merge == 1 {
				createDate = req.StartTime + " - " + req.EndTime
			}
			listRes.List[k] = &model.SAppletRechargeStatisticsListRes{
				Id:                          v.Id,
				AppId:                       v.AppId,
				AppName:                     v.AppName,
				AppType:                     v.AppType,
				NewUserAmount:               libUtils.ToRound(v.NewUserAmount, 2, libUtils.RoundDown),
				TotalAmount:                 libUtils.ToRound(v.TotalAmount, 2, libUtils.RoundDown),
				WechatAndroidRechargeAmount: libUtils.ToRound(v.WechatAndroidRechargeAmount, 2, libUtils.RoundDown),
				WechatIosRechargeAmount:     libUtils.ToRound(v.WechatIosRechargeAmount, 2, libUtils.RoundDown),
				DyIosRechargeAmount:         libUtils.ToRound(v.DyIosRechargeAmount, 2, libUtils.RoundDown),
				DyAndroidRechargeAmount:     libUtils.ToRound(v.DyAndroidRechargeAmount, 2, libUtils.RoundDown),
				CreateDate:                  createDate,
				CreateTime:                  v.CreateTime,
				TotalAdUp:                   libUtils.ToRound(v.TotalAdUp, 5, libUtils.RoundDown),
			}
		}
	})
	return
}
func buildListSql(ctx context.Context, req *model.SAppletRechargeStatisticsSearchReq) *gdb.Model {
	m := dao.SAppletRechargeStatistics.Ctx(ctx).WithAll().As("a").
		LeftJoin("sys_user", "u", "a.pitcher_id = u.id").
		LeftJoin("sys_dept", "de", "de.dept_id = u.dept_id")
	if req.Id != "" {
		m = m.Where("a."+dao.SAppletRechargeStatistics.Columns().Id+" = ?", req.Id)
	}
	if len(req.AppIds) > 0 {
		m = m.WhereIn("a."+dao.SAppletRechargeStatistics.Columns().AppId, req.AppIds)
	}
	if req.AppType != "" {
		m = m.Where("a."+dao.SAppletRechargeStatistics.Columns().AppType+" = ?", req.AppType)

	}
	if req.StartTime != "" {
		m = m.WhereGTE("a."+dao.SAppletRechargeStatistics.Columns().CreateDate, req.StartTime)

	}
	if req.EndTime != "" {
		m = m.WhereLTE("a."+dao.SAppletRechargeStatistics.Columns().CreateDate, req.EndTime)

	}
	loginUser := sysService.Context().GetLoginUser(ctx)
	ids, isAdmin, _ := sysService.SysUser().GetContainUser(ctx, loginUser)
	//	additional, _ := sysService.SysUser().GetUserAdditional(ctx, int(loginUser.Id))
	//查询该用户所拥有的小程序
	if !isAdmin {
		//非admin,直接查投手下面的数据
		//m = m.WhereIn(dao.SAppletRechargeStatistics.Columns().AppId, additional.AppId)
		m = m.WhereIn("a."+dao.SAppletRechargeStatistics.Columns().PitcherId, ids)
	}
	if req.DistributorIds != nil && len(req.DistributorIds) > 0 {
		_, userIds := sysService.SysUser().GetUserIdsAndAdmin(ctx, req.DistributorIds)
		m = m.WhereIn("a."+dao.SAppletRechargeStatistics.Columns().PitcherId, userIds)
	}
	if len(req.DeptIds) > 0 {
		m = m.WhereIn("u.dept_id", req.DeptIds)
	}
	return m
}
func (s *sSAppletRechargeStatistics) GetById(ctx context.Context, id int) (res *model.SAppletRechargeStatisticsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SAppletRechargeStatistics.Ctx(ctx).WithAll().Where(dao.SAppletRechargeStatistics.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSAppletRechargeStatistics) Add(ctx context.Context, req *model.SAppletRechargeStatisticsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SAppletRechargeStatistics.Ctx(ctx).Insert(do.SAppletRechareStatistics{
			AppId:                       req.AppId,
			AppName:                     req.AppName,
			AppType:                     req.AppType,
			NewUserAmount:               req.NewUserAmount,
			TotalAmount:                 req.TotalAmount,
			WechatAndroidRechargeAmount: req.WechatAndroidRechargeAmount,
			WechatIosRechargeAmount:     req.WechatIosRechargeAmount,
			DyIosRechargeAmount:         req.DyIosRechargeAmount,
			DyAndroidRechargeAmount:     req.DyAndroidRechargeAmount,
			CreateDate:                  req.CreateDate,
			CreateTime:                  req.CreateTime,
			TotalAdUp:                   req.TotalAdUp,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSAppletRechargeStatistics) Edit(ctx context.Context, req *model.SAppletRechargeStatisticsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SAppletRechargeStatistics.Ctx(ctx).WherePri(req.Id).Update(do.SAppletRechareStatistics{
			AppId:                       req.AppId,
			AppName:                     req.AppName,
			AppType:                     req.AppType,
			NewUserAmount:               req.NewUserAmount,
			TotalAmount:                 req.TotalAmount,
			WechatAndroidRechargeAmount: req.WechatAndroidRechargeAmount,
			WechatIosRechargeAmount:     req.WechatIosRechargeAmount,
			DyIosRechargeAmount:         req.DyIosRechargeAmount,
			DyAndroidRechargeAmount:     req.DyAndroidRechargeAmount,
			CreateDate:                  req.CreateDate,
			CreateTime:                  req.CreateTime,
			TotalAdUp:                   req.TotalAdUp,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSAppletRechargeStatistics) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SAppletRechargeStatistics.Ctx(ctx).Delete(dao.SAppletRechargeStatistics.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
