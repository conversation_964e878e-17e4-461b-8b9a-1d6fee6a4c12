// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-27 11:18:34
// 生成路径: internal/app/ad/logic/ad_plan_rule.go
// 生成人：cq
// desc:广告计划规则设置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdPlanRule(New())
}

func New() service.IAdPlanRule {
	return &sAdPlanRule{}
}

type sAdPlanRule struct{}

func (s *sAdPlanRule) List(ctx context.Context, req *model.AdPlanRuleSearchReq) (listRes *model.AdPlanRuleSearchRes, err error) {
	listRes = new(model.AdPlanRuleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPlanRule.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdPlanRule.Columns().Id+" = ?", req.Id)
		}
		if req.PlanId != "" {
			m = m.Where(dao.AdPlanRule.Columns().PlanId+" = ?", gconv.Int(req.PlanId))
		}
		if req.TimeScope != "" {
			m = m.Where(dao.AdPlanRule.Columns().TimeScope+" = ?", gconv.Int(req.TimeScope))
		}
		if req.MetricsName != "" {
			m = m.Where(dao.AdPlanRule.Columns().MetricsName+" like ?", "%"+req.MetricsName+"%")
		}
		if req.Operator != "" {
			m = m.Where(dao.AdPlanRule.Columns().Operator+" = ?", req.Operator)
		}
		if req.Unit != "" {
			m = m.Where(dao.AdPlanRule.Columns().Unit+" = ?", req.Unit)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdPlanRule.Columns().CreatedAt+" >=? AND "+dao.AdPlanRule.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPlanRuleListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPlanRuleListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdPlanRuleListRes{
				Id:          v.Id,
				PlanId:      v.PlanId,
				TimeScope:   v.TimeScope,
				MetricsName: v.MetricsName,
				Operator:    v.Operator,
				Unit:        v.Unit,
				TargetValue: v.TargetValue,
				CreatedAt:   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdPlanRule) GetById(ctx context.Context, id int) (res *model.AdPlanRuleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanRule.Ctx(ctx).WithAll().Where(dao.AdPlanRule.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanRule) GetByPlanId(ctx context.Context, planId int) (res []*model.AdPlanRuleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanRule.Ctx(ctx).WithAll().Where(dao.AdPlanRule.Columns().PlanId, planId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanRule) GetByPlanIds(ctx context.Context, planIds []int) (res []*model.AdPlanRuleInfoRes, err error) {
	res = make([]*model.AdPlanRuleInfoRes, 0)
	if planIds == nil || len(planIds) == 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanRule.Ctx(ctx).WithAll().WhereIn(dao.AdPlanRule.Columns().PlanId, planIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanRule) Add(ctx context.Context, req *model.AdPlanRuleAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanRule.Ctx(ctx).Insert(do.AdPlanRule{
			PlanId:      req.PlanId,
			TimeScope:   req.TimeScope,
			MetricsName: req.MetricsName,
			Operator:    req.Operator,
			Unit:        req.Unit,
			TargetValue: req.TargetValue,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPlanRule) BatchAdd(ctx context.Context, req []*model.AdPlanRuleAddReq) (err error) {
	if req == nil || len(req) == 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		batchAdd := make([]do.AdPlanRule, len(req))
		for k, v := range req {
			batchAdd[k] = do.AdPlanRule{
				PlanId:      v.PlanId,
				TimeScope:   v.TimeScope,
				MetricsName: v.MetricsName,
				Operator:    v.Operator,
				Unit:        v.Unit,
				TargetValue: v.TargetValue,
			}
		}
		_, err = dao.AdPlanRule.Ctx(ctx).Insert(batchAdd)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPlanRule) Edit(ctx context.Context, req *model.AdPlanRuleEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanRule.Ctx(ctx).WherePri(req.Id).Update(do.AdPlanRule{
			PlanId:      req.PlanId,
			TimeScope:   req.TimeScope,
			MetricsName: req.MetricsName,
			Operator:    req.Operator,
			Unit:        req.Unit,
			TargetValue: req.TargetValue,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdPlanRule) Delete(ctx context.Context, ids []int) (err error) {
	if ids == nil || len(ids) == 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanRule.Ctx(ctx).Delete(dao.AdPlanRule.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// JudgePlanRule todo 判断方法的抽象
func (s *sAdPlanRule) JudgePlanRule(ctx context.Context, list []*model.AdPlanRuleAddReq) bool {

	return true
}
