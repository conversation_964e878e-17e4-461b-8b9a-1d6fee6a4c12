// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-03-06 17:53:48
// 生成路径: internal/app/applet/dao/s_advertiser_config.go
// 生成人：cq
// desc:广告配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sAdvertiserConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sAdvertiserConfigDao struct {
	*internal.SAdvertiserConfigDao
}

var (
	// SAdvertiserConfig is globally public accessible object for table tools_gen_table operations.
	SAdvertiserConfig = sAdvertiserConfigDao{
		internal.NewSAdvertiserConfigDao(),
	}
)

// Fill with you ideas below.
