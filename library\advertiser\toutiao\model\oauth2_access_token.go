/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.
package model

// Oauth2AccessTokenRequest struct for Oauth2AccessTokenRequest
type Oauth2AccessTokenRequest struct {
	//
	AppId *int64 `json:"app_id,omitempty"`
	//
	AuthCode string `json:"auth_code"`
	//
	Secret string `json:"secret"`
}

// Oauth2AccessTokenResponse struct for Oauth2AccessTokenResponse
type Oauth2AccessTokenResponse struct {
	//
	Code *int64                         `json:"code,omitempty"`
	Data *Oauth2AccessTokenResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// Oauth2AccessTokenResponseData
type Oauth2AccessTokenResponseData struct {
	//
	AccessToken *string `json:"access_token,omitempty"`
	//
	AdvertiserIds []int64 `json:"advertiser_ids,omitempty"`
	//
	ExpiresIn *int64 `json:"expires_in,omitempty"`
	//
	RefreshToken *string `json:"refresh_token,omitempty"`
	//
	RefreshTokenExpiresIn *int64 `json:"refresh_token_expires_in,omitempty"`
}
