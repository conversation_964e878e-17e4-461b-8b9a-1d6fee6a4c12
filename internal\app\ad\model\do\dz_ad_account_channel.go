// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: internal/app/ad/model/entity/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdAccountChannel is the golang structure for table dz_ad_account_channel.
type DzAdAccountChannel struct {
	gmeta.Meta `orm:"table:dz_ad_account_channel, do:true"`
	ChannelId  interface{} `orm:"channel_id,primary" json:"channelId"` // 渠道ID
	AccountId  interface{} `orm:"account_id" json:"accountId"`         // 主账号ID
	NickName   interface{} `orm:"nick_name" json:"nickName"`           // 渠道昵称
	UserName   interface{} `orm:"user_name" json:"userName"`           // 渠道用户名称
	JumpType   interface{} `orm:"jump_type" json:"jumpType"`           // 1单端，2双端
	OfAppId    interface{} `orm:"of_app_id" json:"ofAppId"`            // 小程序ID
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"`         // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt"`         // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt"`         // 删除时间
}
