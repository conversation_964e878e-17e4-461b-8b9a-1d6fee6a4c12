// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-11 13:50:44
// 生成路径: internal/app/ad/model/ad_common_asset_package.go
// 生成人：cq
// desc:通用资产-标题包
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdCommonAssetPackageInfoRes is the golang structure for table ad_common_asset_package.
type AdCommonAssetPackageInfoRes struct {
	gmeta.Meta        `orm:"table:ad_common_asset_package"`
	Id                int         `orm:"id,primary" json:"id" dc:""`                                 //
	PackageName       string      `orm:"package_name" json:"packageName" dc:"标题包名称"`                 // 标题包名称
	TitleIds          string      `orm:"title_ids" json:"titleIds" dc:"标题ID列表，以|分隔"`                 // 标题ID列表，以|分隔
	UserId            int         `orm:"user_id" json:"userId" dc:"创建者"`                             // 创建者
	Last3DayClickRate float64     `orm:"last_3_day_click_rate" json:"last3DayClickRate" dc:"近3日点击率"` // 近3日点击率
	Last3DayCost      float64     `orm:"last_3_day_cost" json:"last3DayCost" dc:"近3日消耗"`             // 近3日消耗
	HistoryClickRate  float64     `orm:"history_click_rate" json:"historyClickRate" dc:"历史点击率"`      // 历史点击率
	HistoryCost       float64     `orm:"history_cost" json:"historyCost" dc:"历史消耗"`                  // 历史消耗
	AdCount           int         `orm:"ad_count" json:"adCount" dc:"关联广告数"`                         // 关联广告数
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                      // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                      // 更新时间
	DeletedAt         *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                      // 删除时间
}

type AdCommonAssetPackageListRes struct {
	Id                int                          `json:"id" dc:""`
	PackageName       string                       `json:"packageName" dc:"标题包名称"`
	TitleIds          string                       `json:"titleIds" dc:"标题ID列表，以|分隔"`
	TitleList         []*AdCommonAssetTitleListRes `json:"titleList" dc:"标题列表"`
	TitleCount        int                          `json:"titleCount" dc:"标题数量"`
	UserId            int                          `json:"userId" dc:"创建者ID"`
	UserName          string                       `json:"userName" dc:"创建者"`
	Last3DayClickRate float64                      `json:"last3DayClickRate" dc:"近3日点击率"`
	Last3DayCost      float64                      `json:"last3DayCost" dc:"近3日消耗"`
	HistoryClickRate  float64                      `json:"historyClickRate" dc:"历史点击率"`
	HistoryCost       float64                      `json:"historyCost" dc:"历史消耗"`
	AdCount           int                          `json:"adCount" dc:"关联广告数"`
	CreatedAt         *gtime.Time                  `json:"createdAt" dc:"创建时间"`
}

// AdCommonAssetPackageSearchReq 分页请求参数
type AdCommonAssetPackageSearchReq struct {
	comModel.PageReq
	PackageName string `p:"packageName" dc:"标题包名称"`                      //标题包名称
	UserIds     []int  `p:"userIds" v:"userId@integer#创建者需为整数" dc:"创建者"` //创建者
}

// AdCommonAssetPackageSearchRes 列表返回结果
type AdCommonAssetPackageSearchRes struct {
	comModel.ListRes
	List []*AdCommonAssetPackageListRes `json:"list"`
}

// AdCommonAssetPackageAddReq 添加操作请求参数
type AdCommonAssetPackageAddReq struct {
	PackageName string   `p:"packageName" v:"required#标题包名称不能为空" dc:"标题包名称"`
	TitleIds    []int    `p:"titleIds" dc:"标题ID列表"`
	Titles      []string `p:"titles" dc:"标题列表"`
}

// AdCommonAssetPackageEditReq 修改操作请求参数
type AdCommonAssetPackageEditReq struct {
	Id          int      `p:"id" v:"required#主键ID不能为空" dc:""`
	PackageName string   `p:"packageName" v:"required#标题包名称不能为空" dc:"标题包名称"`
	TitleIds    []int    `p:"titleIds" dc:"标题ID列表"`
	Titles      []string `p:"titles" dc:"标题列表"`
}
