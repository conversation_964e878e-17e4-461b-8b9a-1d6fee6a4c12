/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

type AccountFundGetV3Request struct {
	AccountIds     *[]int64
	AccountType    AccountFundGetV3AccountType
	GrantTypeSplit AccountFundGetV3GrantTypeSplit
}

// AccountFundGetV30AccountType
type AccountFundGetV3AccountType string

// List of account_fund_get_v3.0_account_type
const (
	AD_AccountFundGetV3AccountType        AccountFundGetV3AccountType = "AD"
	STAR_AccountFundGetV3AccountType      AccountFundGetV3AccountType = "STAR"
	LOCAL_AccountFundGetV3AccountType     AccountFundGetV3AccountType = "LOCAL"
	QIANCHUAN_AccountFundGetV3AccountType AccountFundGetV3AccountType = "QIANCHUAN"
)

// AccountFundGetV3GrantTypeSplit
type AccountFundGetV3GrantTypeSplit string

// List of account_fund_get_v3.0_grant_type_split
const (
	ON_AccountFundGetV3GrantTypeSplit  AccountFundGetV3GrantTypeSplit = "ON"
	OFF_AccountFundGetV3GrantTypeSplit AccountFundGetV3GrantTypeSplit = "OFF"
)

// AccountFundGetV3Response struct for AccountFundGetV3Response
type AccountFundGetV3Response struct {
	//
	Code *int64                        `json:"code,omitempty"`
	Data *AccountFundGetV3ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// AccountFundGetV30ResponseData
type AccountFundGetV3ResponseData struct {
	//
	List []*AccountFundGetV3ResponseDataListInner `json:"list,omitempty"`
}

// AccountFundGetV3ResponseDataListInner struct for AccountFundGetV3ResponseDataListInner
type AccountFundGetV3ResponseDataListInner struct {
	//
	AccountId *int64 `json:"account_id,omitempty"`
	//
	Balance *int64 `json:"balance,omitempty"`
	//
	BiddingCash *int64 `json:"bidding_cash,omitempty"`
	//
	BiddingCreditBalance *int64 `json:"bidding_credit_balance,omitempty"`
	//
	BiddingCreditValidBalance *int64 `json:"bidding_credit_valid_balance,omitempty"`
	//
	BiddingPrepayBalance *int64 `json:"bidding_prepay_balance,omitempty"`
	//
	BiddingPrepayValidBalance *int64 `json:"bidding_prepay_valid_balance,omitempty"`
	//
	BiddingValidGrant *int64 `json:"bidding_valid_grant,omitempty"`
	//
	BrandCash *int64 `json:"brand_cash,omitempty"`
	//
	BrandCreditBalance *int64 `json:"brand_credit_balance,omitempty"`
	//
	BrandCreditValidBalance *int64 `json:"brand_credit_valid_balance,omitempty"`
	//
	BrandPrepayBalance *int64 `json:"brand_prepay_balance,omitempty"`
	//
	BrandPrepayValidBalance *int64 `json:"brand_prepay_valid_balance,omitempty"`
	//
	BrandValidGrant *int64 `json:"brand_valid_grant,omitempty"`
	//
	Cash *int64 `json:"cash,omitempty"`
	//
	CommonGrant *int64 `json:"common_grant,omitempty"`
	//
	CompensationGrant *int64 `json:"compensation_grant,omitempty"`
	//
	CompensationValidGrant *int64 `json:"compensation_valid_grant,omitempty"`
	//
	CreditBalance *int64 `json:"credit_balance,omitempty"`
	//
	CreditTransferBalance *int64 `json:"credit_transfer_balance,omitempty"`
	//
	CreditValidBalance *int64 `json:"credit_valid_balance,omitempty"`
	//
	DefaultGrant *int64 `json:"default_grant,omitempty"`
	//
	GeneralCash *int64 `json:"general_cash,omitempty"`
	//
	GeneralCreditBalance *int64 `json:"general_credit_balance,omitempty"`
	//
	GeneralCreditValidBalance *int64 `json:"general_credit_valid_balance,omitempty"`
	//
	GeneralPrepayBalance *int64 `json:"general_prepay_balance,omitempty"`
	//
	GeneralPrepayValidBalance *int64 `json:"general_prepay_valid_balance,omitempty"`
	//
	GeneralValidGrant *int64 `json:"general_valid_grant,omitempty"`
	//
	Grant *int64 `json:"grant,omitempty"`
	//
	PrepayBalance *int64 `json:"prepay_balance,omitempty"`
	//
	PrepayTransferBalance *int64 `json:"prepay_transfer_balance,omitempty"`
	//
	PrepayValidBalance *int64 `json:"prepay_valid_balance,omitempty"`
	//
	ReturnGoodsAbs *int64 `json:"return_goods_abs,omitempty"`
	//
	SearchGrant *int64 `json:"search_grant,omitempty"`
	//
	TransferBalance *int64 `json:"transfer_balance,omitempty"`
	//
	TransferCash *int64 `json:"transfer_cash,omitempty"`
	//
	UnionGrant *int64 `json:"union_grant,omitempty"`
	//
	ValidBalance *int64 `json:"valid_balance,omitempty"`
	//
	ValidCash *int64 `json:"valid_cash,omitempty"`
	//
	ValidGrant *int64 `json:"valid_grant,omitempty"`
	//
	ValidReturnGoodsAbs *int64 `json:"valid_return_goods_abs,omitempty"`
	//
	WalletId *int64 `json:"wallet_id,omitempty"`
	//
	WalletTotalBalanceValid *int64 `json:"wallet_total_balance_valid,omitempty"`
}
