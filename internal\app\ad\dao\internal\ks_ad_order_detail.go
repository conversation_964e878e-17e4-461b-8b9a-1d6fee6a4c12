// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: internal/app/ad/dao/internal/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdOrderDetailDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdOrderDetailDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns KsAdOrderDetailColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdOrderDetailColumns defines and stores column names for table ks_ad_order_detail.
type KsAdOrderDetailColumns struct {
	Id              string // id
	AdvertiserId    string // 用户快手号id
	PayDate         string // 购买时间
	SettleDate      string // 结算时间
	OrderId         string // 订单ID
	OrderType       string // 订单类型 本账号售卖、子账户售卖、分销售卖
	CopyrightUid    string // 版权商UID
	CopyrightName   string // 版权商名称
	SeriesName      string // 短剧名称
	SubAccountUid   string // 子账号UID
	SubAccountName  string // 子账号名称
	PayProvider     string // 支付渠道
	PayAmt          string // 结算订单总金额（元）
	RedundPrice     string // 退款金额（元）
	CommissionPrice string // 佣金（元）
	SettlePrice     string // 可提现金额（元）
	SettleAmt       string // 分成金额（元）
	SalerRateStr    string // 分成比例
	Expenditure     string // 总支出（元）
	Income          string // 总收入（元）
	SettleRate      string // 分成比例 仅分销售卖该字段有值
	CreatedAt       string // 创建时间
	UpdatedAt       string // 更新时间
}

var ksAdOrderDetailColumns = KsAdOrderDetailColumns{
	Id:              "id",
	AdvertiserId:    "advertiser_id",
	PayDate:         "pay_date",
	SettleDate:      "settle_date",
	OrderId:         "order_id",
	OrderType:       "order_type",
	CopyrightUid:    "copyright_uid",
	CopyrightName:   "copyright_name",
	SeriesName:      "series_name",
	SubAccountUid:   "sub_account_uid",
	SubAccountName:  "sub_account_name",
	PayProvider:     "pay_provider",
	PayAmt:          "pay_amt",
	RedundPrice:     "redund_price",
	CommissionPrice: "commission_price",
	SettlePrice:     "settle_price",
	SettleAmt:       "settle_amt",
	SalerRateStr:    "saler_rate_str",
	Expenditure:     "expenditure",
	Income:          "income",
	SettleRate:      "settle_rate",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewKsAdOrderDetailDao creates and returns a new DAO object for table data access.
func NewKsAdOrderDetailDao() *KsAdOrderDetailDao {
	return &KsAdOrderDetailDao{
		group:   "default",
		table:   "ks_ad_order_detail",
		columns: ksAdOrderDetailColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdOrderDetailDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdOrderDetailDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdOrderDetailDao) Columns() KsAdOrderDetailColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdOrderDetailDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdOrderDetailDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdOrderDetailDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
