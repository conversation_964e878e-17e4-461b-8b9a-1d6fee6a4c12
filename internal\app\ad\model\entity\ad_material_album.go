// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: internal/app/ad/model/entity/ad_material_album.go
// 生成人：cyao
// desc:广告素材专辑
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdMaterialAlbum is the golang structure for table ad_material_album.
type AdMaterialAlbum struct {
	gmeta.Meta     `orm:"table:ad_material_album"`
	AlbumId        int         `orm:"album_id,primary" json:"albumId"`       // id
	AlbumName      string      `orm:"album_name" json:"albumName"`           // 专辑名称
	UserId         int         `orm:"user_id" json:"userId"`                 // 创建人
	ScopeAuthority int         `orm:"scope_authority" json:"scopeAuthority"` // 1 默认权限（角色权限） 2部分人权限部门 3 部分人有权限指定用户  4所有人权限
	Remark         string      `orm:"remark" json:"remark"`                  // 备注
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`           // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`           // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"`           // 删除时间
}
