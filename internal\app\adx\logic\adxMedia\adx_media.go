// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: internal/app/adx/logic/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/os/gtime"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdxMedia(New())
}

func New() service.IAdxMedia {
	return &sAdxMedia{}
}

type sAdxMedia struct{}

func (s *sAdxMedia) List(ctx context.Context, req *model.AdxMediaSearchReq) (listRes *model.AdxMediaSearchRes, err error) {
	listRes = new(model.AdxMediaSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxMedia.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxMedia.Columns().Id+" = ?", req.Id)
		}
		if req.LogoUrl != "" {
			m = m.Where(dao.AdxMedia.Columns().LogoUrl+" = ?", req.LogoUrl)
		}
		if req.MediaName != "" {
			m = m.Where(dao.AdxMedia.Columns().MediaName+" like ?", "%"+req.MediaName+"%")
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxMediaListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxMediaListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxMediaListRes{
				Id:        v.Id,
				LogoUrl:   v.LogoUrl,
				MediaName: v.MediaName,
			}
		}
	})
	return
}

func (s *sAdxMedia) GetExportData(ctx context.Context, req *model.AdxMediaSearchReq) (listRes []*model.AdxMediaInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxMedia.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdxMedia.Columns().Id+" = ?", req.Id)
		}
		if req.LogoUrl != "" {
			m = m.Where(dao.AdxMedia.Columns().LogoUrl+" = ?", req.LogoUrl)
		}
		if req.MediaName != "" {
			m = m.Where(dao.AdxMedia.Columns().MediaName+" like ?", "%"+req.MediaName+"%")
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxMedia) GetById(ctx context.Context, id uint) (res *model.AdxMediaInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxMedia.Ctx(ctx).WithAll().Where(dao.AdxMedia.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxMedia) GetByIds(ctx context.Context, ids []int) (res []*model.AdxMediaInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxMedia.Ctx(ctx).WithAll().WhereIn(dao.AdxMedia.Columns().Id, ids).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxMedia) Add(ctx context.Context, req *model.AdxMediaAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxMedia.Ctx(ctx).Insert(do.AdxMedia{
			Id:        req.Id,
			LogoUrl:   req.LogoUrl,
			MediaName: req.MediaName,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxMedia) BatchAdd(ctx context.Context, batchReq []*model.AdxMediaAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdxMedia, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdxMedia{
				Id:        v.Id,
				LogoUrl:   v.LogoUrl,
				MediaName: v.MediaName,
			}
		}
		_, err = dao.AdxMedia.Ctx(ctx).Save(data)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdxMedia) Edit(ctx context.Context, req *model.AdxMediaEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxMedia.Ctx(ctx).WherePri(req.Id).Update(do.AdxMedia{
			LogoUrl:   req.LogoUrl,
			MediaName: req.MediaName,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxMedia) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxMedia.Ctx(ctx).Delete(dao.AdxMedia.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdxMedia) RunSyncAdxMediaTask(ctx context.Context, req *model.AdxMediaSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithCancel(context.Background())
		defer cancel()
		errors := s.SyncAdxMedia(innerContext)
		if errors != nil {
			g.Log().Error(innerContext, errors)
		}
	})
	return
}

func (s *sAdxMedia) SyncAdxMediaTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatAdxMediaLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatAdxMediaLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		err = s.SyncAdxMedia(ctx)
		liberr.ErrIsNil(ctx, err, "同步ADX媒体失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "SyncAdxMediaTask",
			CreatedAt:  gtime.Now(),
			Result:     "同步ADX媒体，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sAdxMedia) SyncAdxMedia(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var id int64 = 0
		var pageSize = 500
		for {
			mediaRes, err1 := api.GetAdxApiClient().MediaService.SetReq(api.MediaRequest{
				Id:       id,
				PageSize: pageSize,
			}).Do()
			if err1 != nil {
				g.Log().Error(ctx, err1)
				break
			}
			if len(mediaRes.Content) == 0 {
				break
			}
			var batchReq []*model.AdxMediaAddReq
			for _, v := range mediaRes.Content {
				batchReq = append(batchReq, &model.AdxMediaAddReq{
					Id:        uint(v.Id),
					LogoUrl:   v.LogoUrl,
					MediaName: v.MediaName,
				})
			}
			err = s.BatchAdd(ctx, batchReq)
			if err != nil {
				g.Log().Error(ctx, err)
				break
			}
			if mediaRes.Page.PageId == id {
				break
			}
			id = mediaRes.Page.PageId
		}
	})
	return
}
