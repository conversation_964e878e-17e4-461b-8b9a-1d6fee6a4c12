/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsUnionFlowPackageGetV2DataListFlowPackageType
type ToolsUnionFlowPackageGetV2DataListFlowPackageType string

// List of tools_union_flow_package_get_v2_data_list_flow_package_type
const (
	CUSTOMIZE_ToolsUnionFlowPackageGetV2DataListFlowPackageType ToolsUnionFlowPackageGetV2DataListFlowPackageType = "CUSTOMIZE"
	FEATURED_ToolsUnionFlowPackageGetV2DataListFlowPackageType  ToolsUnionFlowPackageGetV2DataListFlowPackageType = "FEATURED"
	SYSTEM_ToolsUnionFlowPackageGetV2DataListFlowPackageType    ToolsUnionFlowPackageGetV2DataListFlowPackageType = "SYSTEM"
)

// Ptr returns reference to tools_union_flow_package_get_v2_data_list_flow_package_type value
func (v ToolsUnionFlowPackageGetV2DataListFlowPackageType) Ptr() *ToolsUnionFlowPackageGetV2DataListFlowPackageType {
	return &v
}
