// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-22 11:52:42
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_title.go
// 生成人：cq
// desc:快手策略组-文案
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyTitle(New())
}

func New() service.IKsAdvertiserStrategyTitle {
	return &sKsAdvertiserStrategyTitle{}
}

type sKsAdvertiserStrategyTitle struct{}

func (s *sKsAdvertiserStrategyTitle) List(ctx context.Context, req *model.KsAdvertiserStrategyTitleSearchReq) (listRes *model.KsAdvertiserStrategyTitleSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyTitleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyTitle.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().Id+" = ?", req.Id)
		}
		if req.StrategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().StrategyId+" = ?", req.StrategyId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.TitleAllocationMethod != "" {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().TitleAllocationMethod+" = ?", req.TitleAllocationMethod)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyTitle.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyTitleListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyTitleListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyTitleListRes{
				Id:                    v.Id,
				StrategyId:            v.StrategyId,
				TaskId:                v.TaskId,
				TitleAllocationMethod: v.TitleAllocationMethod,
				TitleData:             v.TitleData,
				CreatedAt:             v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyTitle) GetById(ctx context.Context, id uint64) (res *model.KsAdvertiserStrategyTitleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyTitle.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyTitle.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTitle) GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyTitleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyTitle.Ctx(ctx).WithAll()
		if strategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().StrategyId+" = ?", strategyId)
		}
		if taskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyTitle.Columns().TaskId+" = ?", taskId)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTitle) Add(ctx context.Context, req *model.KsAdvertiserStrategyTitleAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTitle.Ctx(ctx).Insert(do.KsAdvertiserStrategyTitle{
			StrategyId:            req.StrategyId,
			TaskId:                req.TaskId,
			TitleAllocationMethod: req.TitleAllocationMethod,
			TitleData:             req.TitleData,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTitle) Edit(ctx context.Context, req *model.KsAdvertiserStrategyTitleEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTitle.Ctx(ctx).
			Where(dao.KsAdvertiserStrategyTitle.Columns().StrategyId, req.StrategyId).
			Update(do.KsAdvertiserStrategyTitle{
				TaskId:                req.TaskId,
				TitleAllocationMethod: req.TitleAllocationMethod,
				TitleData:             req.TitleData,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyTitle) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyTitle.Ctx(ctx).Delete(dao.KsAdvertiserStrategyTitle.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
