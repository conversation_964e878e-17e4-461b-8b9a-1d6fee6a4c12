// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: internal/app/adx/dao/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao/internal"
)

// adxTaskItemDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adxTaskItemDao struct {
	*internal.AdxTaskItemDao
}

var (
	// AdxTaskItem is globally public accessible object for table tools_gen_table operations.
	AdxTaskItem = adxTaskItemDao{
		internal.NewAdxTaskItemDao(),
	}
)

// Fill with you ideas below.
