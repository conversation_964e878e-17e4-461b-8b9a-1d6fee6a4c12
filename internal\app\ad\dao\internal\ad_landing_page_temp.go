// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-27 16:23:10
// 生成路径: internal/app/ad/dao/internal/ad_landing_page_temp.go
// 生成人：cyao
// desc:落地页模板
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdLandingPageTempDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdLandingPageTempDao struct {
	table   string                   // Table is the underlying table name of the DAO.
	group   string                   // Group is the database configuration group name of current DAO.
	columns AdLandingPageTempColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdLandingPageTempColumns defines and stores column names for table ad_landing_page_temp.
type AdLandingPageTempColumns struct {
	Id         string // ID
	MainUserId string // 创建用户id
	TempleName string // 模板名称
	TempleType string // 模板类型 0 应用下载  1 微信小程序  2微信小游戏
	Bricks     string // json的结构体详情结构见文档
	AdNum      string // 广告数量
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
	DeletedAt  string // 删除时间
}

var adLandingPageTempColumns = AdLandingPageTempColumns{
	Id:         "id",
	MainUserId: "main_user_id",
	TempleName: "temple_name",
	TempleType: "temple_type",
	Bricks:     "bricks",
	AdNum:      "ad_num",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeletedAt:  "deleted_at",
}

// NewAdLandingPageTempDao creates and returns a new DAO object for table data access.
func NewAdLandingPageTempDao() *AdLandingPageTempDao {
	return &AdLandingPageTempDao{
		group:   "default",
		table:   "ad_landing_page_temp",
		columns: adLandingPageTempColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdLandingPageTempDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdLandingPageTempDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdLandingPageTempDao) Columns() AdLandingPageTempColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdLandingPageTempDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdLandingPageTempDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdLandingPageTempDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
