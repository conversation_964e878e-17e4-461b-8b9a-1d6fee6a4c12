// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/model/entity/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdToolsSite is the golang structure for table ad_tools_site.
type AdToolsSite struct {
	gmeta.Meta   `orm:"table:ad_tools_site"`
	Id           int64       `orm:"id,primary" json:"id"`              // ID
	AdvertiserId int64       `orm:"advertiser_id" json:"advertiserId"` // 广告主ID
	SiteId       string      `orm:"site_id" json:"siteId"`             // 站点ID
	SiteName     string      `orm:"site_name" json:"siteName"`         // 站点名称
	Status       string      `orm:"status" json:"status"`              // 状态
	SiteType     string      `orm:"site_type" json:"siteType"`         // 站点类型
	FunctionType string      `orm:"function_type" json:"functionType"` // 功能类型
	Thumbnail    string      `orm:"thumbnail" json:"thumbnail"`        // 缩略图
	MainUserId   int         `orm:"main_user_id" json:"mainUserId"`    // 主用户ID
	CreateTime   *gtime.Time `orm:"create_time" json:"createTime"`     // 创建时间
	UpdateTime   *gtime.Time `orm:"update_time" json:"updateTime"`     // 更新时间
}
