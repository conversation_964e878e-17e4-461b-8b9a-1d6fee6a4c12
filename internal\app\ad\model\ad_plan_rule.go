// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-11-27 11:18:34
// 生成路径: internal/app/ad/model/ad_plan_rule.go
// 生成人：cq
// desc:广告计划规则设置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdPlanRuleInfoRes is the golang structure for table ad_plan_rule.
type AdPlanRuleInfoRes struct {
	gmeta.Meta  `orm:"table:ad_plan_rule"`
	Id          int         `orm:"id,primary" json:"id" dc:"ID"`                               // ID
	PlanId      int         `orm:"plan_id" json:"planId" dc:"计划ID"`                            // 计划ID
	TimeScope   int         `orm:"time_scope" json:"timeScope" dc:"时间范围，0 表示当天，3 表示过去三天，依次类推"` // 时间范围，0 表示当天，3 表示过去三天，依次类推
	MetricsName string      `orm:"metrics_name" json:"metricsName" dc:"指标名，根据指标筛选数据是否达标"`      // 指标名，根据指标筛选数据是否达标
	Operator    string      `orm:"operator" json:"operator" dc:"运算符，例如 >、<、>= 等"`              // 运算符，例如 >、<、>= 等
	Unit        string      `orm:"unit" json:"unit" dc:"单位，例如元、%"`                             // 单位，例如元、%
	TargetValue float64     `orm:"target_value" json:"targetValue" dc:"运算的最终目标值"`              // 运算的最终目标值
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                      // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                      // 更新时间
	DeletedAt   *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                      // 删除时间
}

type AdPlanRuleListRes struct {
	Id          int         `json:"id" dc:"ID"`
	PlanId      int         `json:"planId" dc:"计划ID"`
	TimeScope   int         `json:"timeScope" dc:"时间范围，1 表示当天，3 表示过去三天，依次类推"`
	MetricsName string      `json:"metricsName" dc:"指标名，根据指标筛选数据是否达标"`
	Operator    string      `json:"operator" dc:"运算符，例如 >、<、>= 等"`
	Unit        string      `json:"unit" dc:"单位，例如元、%"`
	TargetValue float64     `json:"targetValue" dc:"运算的最终目标值"`
	CreatedAt   *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdPlanRuleSearchReq 分页请求参数
type AdPlanRuleSearchReq struct {
	comModel.PageReq
	Id          string `p:"id" dc:"ID"`                                                                                   //ID
	PlanId      string `p:"planId" v:"planId@integer#计划ID需为整数" dc:"计划ID"`                                                 //计划ID
	TimeScope   string `p:"timeScope" v:"timeScope@integer#时间范围，0 表示当天，3 表示过去三天，依次类推需为整数" dc:"时间范围，0 表示当天，3 表示过去三天，依次类推"` //时间范围，0 表示当天，3 表示过去三天，依次类推
	MetricsName string `p:"metricsName" dc:"指标名，根据指标筛选数据是否达标"`                                                            //指标名，根据指标筛选数据是否达标
	Operator    string `p:"operator" dc:"运算符，例如 >、<、>= 等"`                                                                //运算符，例如 >、<、>= 等
	Unit        string `p:"unit" dc:"单位，例如元、%"`                                                                           //单位，例如元、%
	TargetValue string `p:"targetValue" v:"targetValue@float#运算的最终目标值需为浮点数" dc:"运算的最终目标值"`                                //运算的最终目标值
	CreatedAt   string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                       //创建时间
}

// AdPlanRuleSearchRes 列表返回结果
type AdPlanRuleSearchRes struct {
	comModel.ListRes
	List []*AdPlanRuleListRes `json:"list"`
}

// AdPlanRuleAddReq 添加操作请求参数
type AdPlanRuleAddReq struct {
	PlanId      int     `p:"planId" dc:"计划ID"`
	TimeScope   int     `p:"timeScope" v:"required#时间范围，0 表示当天，3 表示过去三天，依次类推不能为空" dc:"时间范围，0 表示当天，3 表示过去三天，依次类推"`
	MetricsName string  `p:"metricsName" v:"required#指标名，根据指标筛选数据是否达标不能为空" dc:"指标名，根据指标筛选数据是否达标"`
	Operator    string  `p:"operator"  dc:"运算符，例如 >、<、>= 等"`
	Unit        string  `p:"unit"  dc:"单位，例如元、%"`
	TargetValue float64 `p:"targetValue"  dc:"运算的最终目标值"`
}

// AdPlanRuleEditReq 修改操作请求参数
type AdPlanRuleEditReq struct {
	Id          int     `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	PlanId      int     `p:"planId" v:"required#计划ID不能为空" dc:"计划ID"`
	TimeScope   int     `p:"timeScope" v:"required#时间范围，0 表示当天，3 表示过去三天，依次类推不能为空" dc:"时间范围，0 表示当天，3 表示过去三天，依次类推"`
	MetricsName string  `p:"metricsName" v:"required#指标名，根据指标筛选数据是否达标不能为空" dc:"指标名，根据指标筛选数据是否达标"`
	Operator    string  `p:"operator"  dc:"运算符，例如 >、<、>= 等"`
	Unit        string  `p:"unit"  dc:"单位，例如元、%"`
	TargetValue float64 `p:"targetValue"  dc:"运算的最终目标值"`
}
