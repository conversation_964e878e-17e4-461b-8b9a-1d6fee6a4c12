/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
)

// Oauth2AdvertiserGetApiService Oauth2AdvertiserGetApi service
type Oauth2AdvertiserGetApiService struct {
	ctx   context.Context
	cfg   *conf.Configuration
	token string
}

func (r *Oauth2AdvertiserGetApiService) SetCfg(cfg *conf.Configuration) *Oauth2AdvertiserGetApiService {
	r.cfg = cfg
	return r
}

func (r *Oauth2AdvertiserGetApiService) AccessToken(accessToken string) *Oauth2AdvertiserGetApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *Oauth2AdvertiserGetApiService) Do() (data *model.Oauth2AdvertiserGetResponse, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/oauth2/advertiser/get/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(map[string]string{
			"access_token": r.token,
		}).
		SetResult(&model.Oauth2AdvertiserGetResponse{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(model.Oauth2AdvertiserGetResponse)
	// 将 JSON 响应解码到结构体中
	resBody := response.Body()
	g.Log().Infof(context.Background(), "/open_api/oauth2/advertiser/get/resBody: %v", string(resBody))
	err = json.Unmarshal(resBody, &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/oauth2/advertiser/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
