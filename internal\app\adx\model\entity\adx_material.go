// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/model/entity/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxMaterial is the golang structure for table adx_material.
type AdxMaterial struct {
	gmeta.Meta     `orm:"table:adx_material"`
	MaterialId     uint64   `orm:"material_id,primary" json:"materialId"` // 素材ID
	AdType         []string `orm:"ad_type" json:"adType"`                 // 广告类型列表
	CommentNum     int64    `orm:"comment_num" json:"commentNum"`         // 评论数
	CreateMonth    string   `orm:"create_month" json:"createMonth"`       // 创建年月(格式:YYYY-MM)
	DownloadNum    int64    `orm:"download_num" json:"downloadNum"`       // 预估转化量
	DurationMillis int64    `orm:"duration_millis" json:"durationMillis"` // 时长(毫秒)
	ExposureNum    int64    `orm:"exposure_num" json:"exposureNum"`       // 预估曝光量
	ForwardNum     int64    `orm:"forward_num" json:"forwardNum"`         // 分享数
	LikeNum        int64    `orm:"like_num" json:"likeNum"`               // 点赞数
	MaterialHeight uint     `orm:"material_height" json:"materialHeight"` // 素材高度(像素)
	MaterialType   uint     `orm:"material_type" json:"materialType"`     // 素材类型1图片2视频
	MaterialWidth  uint     `orm:"material_width" json:"materialWidth"`   // 素材宽度(像素)
	PicList        []string `orm:"pic_list" json:"picList"`               // 素材封面链接列表
	PlayNum        int64    `orm:"play_num" json:"playNum"`               // 播放数
	VideoList      []string `orm:"video_list" json:"videoList"`           // 素材视频链接列表
}
