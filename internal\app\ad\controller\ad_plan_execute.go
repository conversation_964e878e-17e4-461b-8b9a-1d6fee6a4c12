// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: internal/app/ad/controller/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adPlanExecuteController struct {
	systemController.BaseController
}

var AdPlanExecute = new(adPlanExecuteController)

// List 列表
func (c *adPlanExecuteController) List(ctx context.Context, req *ad.AdPlanExecuteSearchReq) (res *ad.AdPlanExecuteSearchRes, err error) {
	res = new(ad.AdPlanExecuteSearchRes)
	res.AdPlanExecuteSearchRes, err = service.AdPlanExecute().List(ctx, &req.AdPlanExecuteSearchReq)
	return
}

// Get 获取广告计划执行配置
func (c *adPlanExecuteController) Get(ctx context.Context, req *ad.AdPlanExecuteGetReq) (res *ad.AdPlanExecuteGetRes, err error) {
	res = new(ad.AdPlanExecuteGetRes)
	res.AdPlanExecuteInfoRes, err = service.AdPlanExecute().GetById(ctx, req.Id)
	return
}

// Add 添加广告计划执行配置
func (c *adPlanExecuteController) Add(ctx context.Context, req *ad.AdPlanExecuteAddReq) (res *ad.AdPlanExecuteAddRes, err error) {
	err = service.AdPlanExecute().Add(ctx, req.AdPlanExecuteAddReq)
	return
}

// Edit 修改广告计划执行配置
func (c *adPlanExecuteController) Edit(ctx context.Context, req *ad.AdPlanExecuteEditReq) (res *ad.AdPlanExecuteEditRes, err error) {
	err = service.AdPlanExecute().Edit(ctx, req.AdPlanExecuteEditReq)
	return
}

// Delete 删除广告计划执行配置
func (c *adPlanExecuteController) Delete(ctx context.Context, req *ad.AdPlanExecuteDeleteReq) (res *ad.AdPlanExecuteDeleteRes, err error) {
	err = service.AdPlanExecute().Delete(ctx, req.Ids)
	return
}
