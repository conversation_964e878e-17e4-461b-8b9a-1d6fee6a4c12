// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: internal/app/ad/dao/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdAnalyzeDataDayDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdAnalyzeDataDayDao struct {
	*internal.FqAdAnalyzeDataDayDao
}

var (
	// FqAdAnalyzeDataDay is globally public accessible object for table tools_gen_table operations.
	FqAdAnalyzeDataDay = fqAdAnalyzeDataDayDao{
		internal.NewFqAdAnalyzeDataDayDao(),
	}
)

// Fill with you ideas below.
