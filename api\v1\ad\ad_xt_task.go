// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: api/v1/ad/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdXtTaskSearchReq 分页请求参数
type AdXtTaskSearchReq struct {
	g.Meta `path:"/list" tags:"星图任务列表和任务详情" method:"get" summary:"星图任务列表和任务详情列表"`
	commonApi.Author
	model.AdXtTaskSearchReq
}

// AdXtTaskSearchRes 列表返回结果
type AdXtTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtTaskSearchRes
}

// AdXtTaskPullReq 分页请求参数
type AdXtTaskPullReq struct {
	g.Meta `path:"/pull" tags:"星图任务列表和任务详情" method:"get" summary:"pull"`
	commonApi.Author
	//AdvertiserId string `p:"advertiserId" v:"required#主键必须"` //通过主键获取
	//AppId        int64  `p:"appId" v:"required#主键必须"`
	//StatTime string `p:"statTime" v:"required#日期必须"`
}

// AdXtTaskPullRes
type AdXtTaskPullRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

type AdXtTaskAsyncReq struct {
	g.Meta `path:"/async" tags:"星图任务列表和任务详情" method:"get" summary:"async"`
	commonApi.Author
	StartTime string `p:"startTime" v:"required#日期必须"`
	EndTime   string `p:"endTime" v:"required#日期必须"`
}
type AdXtTaskAsyncRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// AdXtTaskExportReq 导出请求
type AdXtTaskExportReq struct {
	g.Meta `path:"/export" tags:"星图任务列表和任务详情" method:"get" summary:"星图任务列表和任务详情导出"`
	commonApi.Author
	model.AdXtTaskSearchReq
}

// AdXtTaskExportRes 导出响应
type AdXtTaskExportRes struct {
	commonApi.EmptyRes
}

// AdXtTaskAddReq 添加操作请求参数
type AdXtTaskAddReq struct {
	g.Meta `path:"/add" tags:"星图任务列表和任务详情" method:"post" summary:"星图任务列表和任务详情添加"`
	commonApi.Author
	*model.AdXtTaskAddReq
}

// AdXtTaskAddRes 添加操作返回结果
type AdXtTaskAddRes struct {
	commonApi.EmptyRes
}

// AdXtTaskEditReq 修改操作请求参数
type AdXtTaskEditReq struct {
	g.Meta `path:"/edit" tags:"星图任务列表和任务详情" method:"put" summary:"星图任务列表和任务详情修改"`
	commonApi.Author
	*model.AdXtTaskEditReq
}

// AdXtTaskEditRes 修改操作返回结果
type AdXtTaskEditRes struct {
	commonApi.EmptyRes
}

// AdXtTaskGetReq 获取一条数据请求
type AdXtTaskGetReq struct {
	g.Meta `path:"/get" tags:"星图任务列表和任务详情" method:"get" summary:"获取星图任务列表和任务详情信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdXtTaskGetRes 获取一条数据结果
type AdXtTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdXtTaskInfoRes
}

// AdXtTaskDeleteReq 删除数据请求
type AdXtTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"星图任务列表和任务详情" method:"delete" summary:"删除星图任务列表和任务详情"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdXtTaskDeleteRes 删除数据返回
type AdXtTaskDeleteRes struct {
	commonApi.EmptyRes
}
