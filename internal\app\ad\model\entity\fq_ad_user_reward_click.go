// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: internal/app/ad/model/entity/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdUserRewardClick is the golang structure for table fq_ad_user_reward_click.
type FqAdUserRewardClick struct {
	gmeta.Meta    `orm:"table:fq_ad_user_reward_click"`
	EcpmNo        string      `orm:"ecpm_no,primary" json:"ecpmNo"`       // 激励收益的唯一键，对齐抖开接口的req_id
	DistributorId string      `orm:"distributor_id" json:"distributorId"` // 快应用/公众号对应distributor_id
	AppId         string      `orm:"app_id" json:"appId"`                 // 公众号/快应用/小程序id（分销平台id）【v1.2】
	AppName       string      `orm:"app_name" json:"appName"`             // 快应用/公众号/小程序名称【v1.2】
	DeviceId      string      `orm:"device_id" json:"deviceId"`           // 脱敏后的用户设备ID
	PromotionId   string      `orm:"promotion_id" json:"promotionId"`     // 推广链id
	EcpmCost      int64       `orm:"ecpm_cost" json:"ecpmCost"`           // 激励点击金额，单位十万分之一元
	EventTime     int64       `orm:"event_time" json:"eventTime"`         // 激励点击的时间戳
	RegisterTime  int64       `orm:"register_time" json:"registerTime"`   // 用户染色时间戳
	BookId        string      `orm:"book_id" json:"bookId"`               // 染色推广链的短剧ID
	BookName      string      `orm:"book_name" json:"bookName"`           // 染色推广链的短剧名称
	BookGender    int         `orm:"book_gender" json:"bookGender"`       // 染色推广链短剧性别(0女生、1男生、2无性别)
	BookCategory  string      `orm:"book_category" json:"bookCategory"`   // 染色推广链的短剧类型
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`         // 创建时间
}
