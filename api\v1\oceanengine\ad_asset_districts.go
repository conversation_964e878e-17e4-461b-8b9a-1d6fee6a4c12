// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-18 15:05:11
// 生成路径: api/v1/oceanengine/ad_asset_districts.go
// 生成人：cq
// desc:资产-定向包-行政信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetDistrictsSearchReq 分页请求参数
type AdAssetDistrictsSearchReq struct {
	g.Meta `path:"/list" tags:"资产-定向包-行政信息" method:"post" summary:"资产-定向包-行政信息列表"`
	commonApi.Author
	model.AdAssetDistrictsSearchReq
}

// AdAssetDistrictsSearchRes 列表返回结果
type AdAssetDistrictsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetDistrictsSearchRes
}

// AdAssetDistrictsAddReq 添加操作请求参数
type AdAssetDistrictsAddReq struct {
	g.Meta `path:"/add" tags:"资产-定向包-行政信息" method:"post" summary:"资产-定向包-行政信息添加"`
	commonApi.Author
	*model.AdAssetDistrictsAddReq
}

// AdAssetDistrictsAddRes 添加操作返回结果
type AdAssetDistrictsAddRes struct {
	commonApi.EmptyRes
}

// AdAssetDistrictsEditReq 修改操作请求参数
type AdAssetDistrictsEditReq struct {
	g.Meta `path:"/edit" tags:"资产-定向包-行政信息" method:"put" summary:"资产-定向包-行政信息修改"`
	commonApi.Author
	*model.AdAssetDistrictsEditReq
}

// AdAssetDistrictsEditRes 修改操作返回结果
type AdAssetDistrictsEditRes struct {
	commonApi.EmptyRes
}

// AdAssetDistrictsGetReq 获取一条数据请求
type AdAssetDistrictsGetReq struct {
	g.Meta `path:"/get" tags:"资产-定向包-行政信息" method:"get" summary:"获取资产-定向包-行政信息信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetDistrictsGetRes 获取一条数据结果
type AdAssetDistrictsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetDistrictsInfoRes
}

// AdAssetDistrictsDeleteReq 删除数据请求
type AdAssetDistrictsDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-定向包-行政信息" method:"post" summary:"删除资产-定向包-行政信息"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetDistrictsDeleteRes 删除数据返回
type AdAssetDistrictsDeleteRes struct {
	commonApi.EmptyRes
}

// AdAssetDistrictsSyncReq 同步行政信息请求
type AdAssetDistrictsSyncReq struct {
	g.Meta `path:"/sync" tags:"资产-定向包-行政信息" method:"post" summary:"资产-定向包-同步行政信息"`
	commonApi.Author
}

// AdAssetDistrictsSyncRes 同步行政信息返回
type AdAssetDistrictsSyncRes struct {
	commonApi.EmptyRes
}
