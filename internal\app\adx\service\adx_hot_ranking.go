// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/service/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
)

type IAdxHotRanking interface {
	List(ctx context.Context, req *model.AdxHotRankingSearchReq) (res *model.AdxHotRankingSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdxHotRankingSearchReq) (listRes []*model.AdxHotRankingInfoRes, err error)
	GetByPlayletId(ctx context.Context, PlayletId uint64) (res *model.AdxHotRankingInfoRes, err error)
	PullAllData(ctx context.Context, startTime, endTime string) (err error)
	PullIncrementalData(ctx context.Context, date string) (err error)
	Add(ctx context.Context, req *model.AdxHotRankingAddReq) (err error)
	Edit(ctx context.Context, req *model.AdxHotRankingEditReq) (err error)
	Delete(ctx context.Context, PlayletId []uint64) (err error)
}

var localAdxHotRanking IAdxHotRanking

func AdxHotRanking() IAdxHotRanking {
	if localAdxHotRanking == nil {
		panic("implement not found for interface IAdxHotRanking, forgot register?")
	}
	return localAdxHotRanking
}

func RegisterAdxHotRanking(i IAdxHotRanking) {
	localAdxHotRanking = i
}
