// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-06-09 17:30:44
// 生成路径: api/v1/channel/s_channel_push_task.go
// 生成人：cq
// desc:渠道推送任务相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// SChannelPushTaskSearchReq 分页请求参数
type SChannelPushTaskSearchReq struct {
	g.Meta `path:"/list" tags:"渠道推送任务" method:"post" summary:"渠道推送任务列表"`
	commonApi.Author
	model.SChannelPushTaskSearchReq
}

// SChannelPushTaskSearchRes 列表返回结果
type SChannelPushTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelPushTaskSearchRes
}

// SChannelPushTaskAddReq 添加操作请求参数
type SChannelPushTaskAddReq struct {
	g.Meta `path:"/add" tags:"渠道推送任务" method:"post" summary:"渠道推送任务添加"`
	commonApi.Author
	*model.SChannelPushTaskAddReq
}

// SChannelPushTaskAddRes 添加操作返回结果
type SChannelPushTaskAddRes struct {
	commonApi.EmptyRes
}

// SChannelPushTaskEditReq 修改操作请求参数
type SChannelPushTaskEditReq struct {
	g.Meta `path:"/edit" tags:"渠道推送任务" method:"put" summary:"渠道推送任务修改"`
	commonApi.Author
	*model.SChannelPushTaskEditReq
}

// SChannelPushTaskEditRes 修改操作返回结果
type SChannelPushTaskEditRes struct {
	commonApi.EmptyRes
}

// SChannelPushTaskGetReq 获取一条数据请求
type SChannelPushTaskGetReq struct {
	g.Meta `path:"/get" tags:"渠道推送任务" method:"get" summary:"获取渠道推送任务信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SChannelPushTaskGetRes 获取一条数据结果
type SChannelPushTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SChannelPushTaskInfoRes
}

// SChannelPushTaskDeleteReq 删除数据请求
type SChannelPushTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"渠道推送任务" method:"post" summary:"删除渠道推送任务"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SChannelPushTaskDeleteRes 删除数据返回
type SChannelPushTaskDeleteRes struct {
	commonApi.EmptyRes
}
