// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-06-05 11:33:37
// 生成路径: internal/app/adx/logic/adx_playlet.go
// 生成人：cq
// desc:ADX短剧基本信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model"
	"github.com/tiger1103/gfast/v3/internal/app/adx/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/adx/service"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/advertiser/adx/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"golang.org/x/sync/semaphore"
	"sync"
	"time"
)

func init() {
	service.RegisterAdxPlaylet(New())
}

func New() service.IAdxPlaylet {
	return &sAdxPlaylet{}
}

type sAdxPlaylet struct{}

func (s *sAdxPlaylet) List(ctx context.Context, req *model.AdxPlayletSearchReq) (listRes *model.AdxPlayletSearchRes, err error) {
	listRes = new(model.AdxPlayletSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxPlaylet.Ctx(ctx).WithAll()
		if req.PlayletId != "" {
			m = m.Where(dao.AdxPlaylet.Columns().PlayletId+" = ?", req.PlayletId)
		}
		if req.PlayletName != "" {
			m = m.Where(dao.AdxPlaylet.Columns().PlayletName+" like ?", "%"+req.PlayletName+"%")
		}
		if req.CoverOss != "" {
			m = m.Where(dao.AdxPlaylet.Columns().CoverOss+" = ?", req.CoverOss)
		}
		if req.Description != "" {
			m = m.Where(dao.AdxPlaylet.Columns().Description+" = ?", req.Description)
		}
		if req.ReleaseStartDate != "" {
			m = m.Where(dao.AdxPlaylet.Columns().ReleaseStartDate+" = ?", req.ReleaseStartDate)
		}
		if req.TotalEpisode != "" {
			m = m.Where(dao.AdxPlaylet.Columns().TotalEpisode+" = ?", gconv.Uint(req.TotalEpisode))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "playlet_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdxPlayletListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdxPlayletListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdxPlayletListRes{
				PlayletId:           v.PlayletId,
				PlayletName:         v.PlayletName,
				CoverOss:            v.CoverOss,
				Description:         v.Description,
				ReleaseStartDate:    v.ReleaseStartDate,
				TotalEpisode:        v.TotalEpisode,
				ContractorList:      v.ContractorList,
				CopyrightHolderList: v.CopyrightHolderList,
				RelatedPartyList:    v.RelatedPartyList,
				Tags:                v.Tags,
			}
		}
	})
	return
}

// 拉取全量数据
func (s *sAdxPlaylet) PullAllData(ctx context.Context) (err error) {
	channelRechargeStatKey := consts.ADXALLPlayletData
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :PullAllData ")
		return
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	tagMap := make(map[string]bool)
	err = g.Try(innerCtx, func(innerCtx context.Context) {
		id := 0
		for {
			response, err := api.GetAdxApiClient().PlayletService.SetReq(api.PlayletRequest{
				Id:       id,
				PageSize: 500,
				PullType: 1,
			}).Do()
			if err != nil {
				g.Log().Errorf(innerCtx, "playlet PullAllData失败: %v", err)
			}
			if response.Msg == "Success" && len(response.Content) > 0 {

				listRes := make([]*model.AdxPlayletEditReq, 0, len(response.Content))
				for _, item := range response.Content {
					for _, tag := range item.Tags {
						tagMap[tag] = true
					}
					listRes = append(listRes, &model.AdxPlayletEditReq{
						PlayletId:           item.PlayletId,
						PlayletName:         item.PlayletName,
						CoverOss:            item.CoverOss,
						Description:         item.Description,
						ReleaseStartDate:    item.ReleaseStartDate,
						TotalEpisode:        item.TotalEpisode,
						ContractorList:      item.ContractorList,
						CopyrightHolderList: item.CopyrightHolderList,
						RelatedPartyList:    item.RelatedPartyList,
						Tags:                item.Tags,
					})
				}
				err = s.BatchUpdate(innerCtx, listRes)
				if err != nil {
					g.Log().Errorf(innerCtx, "playlet PullAllData失败: %v", err)
				}
			} else {
				break
			}
			if response.Page.PageId == id {
				break
			}
			id = response.Page.PageId
		}
	})
	// 将tag 加载到redis中
	tags := make([]string, 0, len(tagMap))
	// 将tag 加载到redis中
	for key, _ := range tagMap {
		tags = append(tags, key)
	}
	_ = commonService.GetGoRedis().SAdd(ctx, consts.ADXPlayletTag, tags)
	return
}

// 拉取增量数据
func (s *sAdxPlaylet) PullIncrementalData(ctx context.Context, date string) (err error) {
	channelRechargeStatKey := consts.ADXPlayletData + date
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+",IAdxPlaylet TaskName :PullIncrementalData ")
		return
	}
	defer mutex.UnlockContext(ctx)

	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	tagMap := make(map[string]bool)
	err = g.Try(innerCtx, func(innerCtx context.Context) {
		id := 0
		for {
			response, err := api.GetAdxApiClient().PlayletService.SetReq(api.PlayletRequest{
				Id:         id,
				PageSize:   500,
				PullType:   2,
				UpdateDate: date,
			}).Do()
			if err != nil {
				g.Log().Errorf(innerCtx, "playlet PullAllData失败: %v", err)
			}
			if response.Msg == "Success" && len(response.Content) > 0 {

				listRes := make([]*model.AdxPlayletEditReq, 0, len(response.Content))
				for _, item := range response.Content {
					for _, tag := range item.Tags {
						tagMap[tag] = true
					}
					listRes = append(listRes, &model.AdxPlayletEditReq{
						PlayletId:           item.PlayletId,
						PlayletName:         item.PlayletName,
						CoverOss:            item.CoverOss,
						Description:         item.Description,
						ReleaseStartDate:    item.ReleaseStartDate,
						TotalEpisode:        item.TotalEpisode,
						ContractorList:      item.ContractorList,
						CopyrightHolderList: item.CopyrightHolderList,
						RelatedPartyList:    item.RelatedPartyList,
						Tags:                item.Tags,
					})
				}
				err = s.BatchUpdate(innerCtx, listRes)
				if err != nil {
					g.Log().Errorf(innerCtx, "playlet PullAllData失败: %v", err)
				}
			} else {
				break
			}
			if response.Page.PageId == id {
				break
			}
			id = response.Page.PageId
		}
		tags := make([]string, 0, len(tagMap))
		// 将tag 加载到redis中
		for key, _ := range tagMap {
			tags = append(tags, key)
		}
		_ = commonService.GetGoRedis().SAdd(ctx, consts.ADXPlayletTag, tags)

	})
	return
}

func (s *sAdxPlaylet) GetExportData(ctx context.Context, req *model.AdxPlayletSearchReq) (listRes []*model.AdxPlayletInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxPlaylet.Ctx(ctx).WithAll()
		if req.PlayletId != "" {
			m = m.Where(dao.AdxPlaylet.Columns().PlayletId+" = ?", req.PlayletId)
		}
		if req.PlayletName != "" {
			m = m.Where(dao.AdxPlaylet.Columns().PlayletName+" like ?", "%"+req.PlayletName+"%")
		}
		if req.CoverOss != "" {
			m = m.Where(dao.AdxPlaylet.Columns().CoverOss+" = ?", req.CoverOss)
		}
		if req.Description != "" {
			m = m.Where(dao.AdxPlaylet.Columns().Description+" = ?", req.Description)
		}
		if req.ReleaseStartDate != "" {
			m = m.Where(dao.AdxPlaylet.Columns().ReleaseStartDate+" = ?", req.ReleaseStartDate)
		}
		if req.TotalEpisode != "" {
			m = m.Where(dao.AdxPlaylet.Columns().TotalEpisode+" = ?", gconv.Uint(req.TotalEpisode))
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "playlet_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdxPlaylet) GetByPlayletId(ctx context.Context, playletId uint64) (res *model.AdxPlayletInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxPlaylet.Ctx(ctx).WithAll().Where(dao.AdxPlaylet.Columns().PlayletId, playletId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxPlaylet) GetByName(ctx context.Context, name string, likeQuery bool) (res []*model.AdxPlayletInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdxPlaylet.Ctx(ctx).WithAll()
		if likeQuery {
			m = m.WhereLike(dao.AdxPlaylet.Columns().PlayletName, "%"+name+"%")
		} else {
			m = m.Where(dao.AdxPlaylet.Columns().PlayletName, name)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdxPlaylet) GetByPlayletIds(ctx context.Context, playletIds []int64) (res []*model.AdxPlayletDetialRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdxPlaylet.Ctx(ctx).WithAll().WhereIn(dao.AdxPlaylet.Columns().PlayletId, playletIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// GetDetail
func (s *sAdxPlaylet) GetDetail(ctx context.Context, playletId uint64) (res *model.AdxPlayletDetialRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.AdxPlayletDetialRes)
		// 首先判断redis 缓存中是否存在
		cmdStr := commonService.GetGoRedis().Get(ctx, fmt.Sprintf(consts.ADXPlayletDetailData+"%d", playletId))
		if cmdStr.Err() == nil && cmdStr != nil {
			data := cmdStr.Val()
			// json 序列化
			err = json.Unmarshal([]byte(data), &res)
			return
		}
		var wg sync.WaitGroup
		var firstErr error
		var once sync.Once
		var mu sync.Mutex
		sem := semaphore.NewWeighted(8) // 控制最多4个goroutine并发执行
		var startTime = time.Now().UnixMilli()
		setErr := func(e error) {
			once.Do(func() { firstErr = e })
		}
		err = dao.AdxPlayletAnaltic.Ctx(ctx).WithAll().Where(dao.AdxPlaylet.Columns().PlayletId, playletId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		var materialList = make([]int64, 0)
		arrayList, err := dao.AdxMaterialAnalytic.Ctx(ctx).Fields(dao.AdxMaterial.Columns().MaterialId, dao.AdxMaterial.Columns().PlayletId).Where(dao.AdxMaterial.Columns().PlayletId, playletId).Group(dao.AdxMaterial.Columns().MaterialId).Array()
		for _, value := range arrayList {
			materialList = append(materialList, value.Int64())
		}
		// 根据materialList 进行分页 2000一页
		// 判断有多少页
		pageSize := 2000
		total := len(materialList) / pageSize
		var totalPage = total
		// 余数
		var remain = len(materialList) % pageSize
		if remain > 0 {
			totalPage++
		}
		var endTime = time.Now().UnixMilli()
		g.Log().Infof(ctx, "GetDetail1111获取数据耗时: %dms", endTime-startTime)
		wg.Add(2 * totalPage)
		for i := 0; i < len(materialList); i += pageSize {
			end := i + pageSize
			if end > len(materialList) {
				end = len(materialList)
			}
			materialPage := materialList[i:end]

			// 启动两个 goroutine 示例（你可以根据实际只需要一个）
			go func(pageData []int64) {
				defer wg.Done()
				if err := sem.Acquire(ctx, 1); err != nil {
					setErr(err)
					return
				}
				sem.Release(1)
				var inner = time.Now().UnixMilli()
				planNum, innerErr := dao.AdxCreativeAnalytic.Ctx(ctx).WhereIn(dao.AdxCreativeAnalytic.Columns().MaterialId, pageData).Group(dao.AdxCreativeAnalytic.Columns().CreativeId).Count()
				mu.Lock()
				res.PlanNum += uint(planNum)
				mu.Unlock()

				if innerErr != nil {
					setErr(innerErr)
					return
				}
				var innerEndTime = time.Now().UnixMilli()
				g.Log().Infof(ctx, "GetDetail1Innerr获取数据耗时: %dms", innerEndTime-inner)

			}(materialPage)

			go func(pageData []int64) {
				defer wg.Done()
				if err := sem.Acquire(ctx, 1); err != nil {
					setErr(err)
					return
				}
				defer sem.Release(1)
				var inner = time.Now().UnixMilli()
				var results []struct {
					MaterialId   int64 `json:"materialId"`
					DeliveryDays int64 `json:"deliveryDays"`
				}

				innerErr := dao.AdxCreativeAnalytic.Ctx(ctx).Fields("material_id As materialId, DATEDIFF(MAX(last_seen), MIN(first_seen)) + 1 AS deliveryDays").WhereIn(dao.AdxCreative.Columns().MaterialId, pageData).Group(dao.AdxCreative.Columns().MaterialId).Scan(&results)
				if innerErr != nil {
					setErr(innerErr)
					return
				}
				var totalDays int64 = 0
				for _, r := range results {
					totalDays += r.DeliveryDays
				}
				mu.Lock()
				res.Days += uint(totalDays)
				mu.Unlock()
				var innerEndTime = time.Now().UnixMilli()
				g.Log().Infof(ctx, "GetDetail1Innerr2获取数据耗时: %dms", innerEndTime-inner)
			}(materialPage)
		}
		res.MaterialNum = uint(len(materialList))

		if firstErr != nil {
			err = firstErr
			return
		}
		wg.Wait()
		var endTime2 = time.Now().UnixMilli()
		g.Log().Infof(ctx, "GetDetail2222获取数据耗时: %dms", endTime2-startTime)
		// 计算热力值
		_ = dao.AdxHotRanking.Ctx(ctx).Where(dao.AdxHotRanking.Columns().PlayletId, playletId).FieldMax(dao.AdxHotRanking.Columns().TotalConsumeNum, "heatValue").Scan(&res)
		// 序列化res
		data, err := json.Marshal(res)
		liberr.ErrIsNil(ctx, err, "序列化失败")
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf(consts.ADXPlayletDetailData+"%d", playletId), data, time.Hour*2)
	})
	return
}

func (s *sAdxPlaylet) GetDetail2(ctx context.Context, playletId uint64) (res *model.AdxPlayletDetialRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.AdxPlayletDetialRes)
		err = dao.AdxPlayletAnaltic.Ctx(ctx).WithAll().Where(dao.AdxPlaylet.Columns().PlayletId, playletId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		arrayList, err1 := dao.AdxMaterialAnalytic.Ctx(ctx).WithAll().
			Fields(dao.AdxMaterial.Columns().MaterialId).
			Where(dao.AdxMaterial.Columns().PlayletId, playletId).
			Group(dao.AdxMaterial.Columns().MaterialId).
			Array()
		liberr.ErrIsNil(ctx, err1, "获取信息失败")
		materialIds := make([]int64, 0)
		for _, value := range arrayList {
			materialIds = append(materialIds, value.Int64())
		}
		var wg sync.WaitGroup
		var mu sync.Mutex
		var pageSize = 2000
		pool := grpool.New(20)
		numWorkers := decimal.NewFromInt(gconv.Int64(len(materialIds))).
			Div(decimal.NewFromInt(gconv.Int64(pageSize))).
			RoundCeil(0).IntPart()
		var results = make([]model.AdxPlayLetData, 0)
		for i := 0; i < gconv.Int(numWorkers); i++ {
			wg.Add(1)
			start := i * pageSize
			end := start + pageSize
			pageData := materialIds[start:end]
			copyI := i
			_ = pool.Add(ctx, func(ctx context.Context) {
				var startTime = time.Now().UnixMilli()
				defer wg.Done()
				var pageResults []model.AdxPlayLetData
				innerCtx := context.Background()
				err = dao.AdxCreativeBakAnalytic.Ctx(innerCtx).WithAll().
					Fields("MIN(first_seen) as minFirstSeen").
					Fields("MAX(last_seen) as maxLastSeen").
					Fields("COUNT(creative_id) AS planNum").
					WhereIn(dao.AdxCreative.Columns().MaterialId, pageData).
					Group(dao.AdxCreative.Columns().CreativeId).
					Scan(&pageResults)
				if err != nil {
					g.Log().Errorf(ctx, "GetDetail2获取数据失败：%v", err)
					return
				}
				var endTime = time.Now().UnixMilli()
				g.Log().Infof(ctx, "goroutine: %v, GetDetail2获取数据耗时: %dms", copyI, endTime-startTime)
				mu.Lock()
				results = append(results, pageResults...)
				mu.Unlock()
			})
		}
		wg.Wait()
		var planNum int64
		var minFirstSeen string
		var maxLastSeen string
		for index, v := range results {
			if index == 0 {
				minFirstSeen = v.MinFirstSeen
				maxLastSeen = v.MaxLastSeen
			} else {
				if v.MinFirstSeen < minFirstSeen {
					minFirstSeen = v.MinFirstSeen
				}
				if v.MaxLastSeen > maxLastSeen {
					maxLastSeen = v.MaxLastSeen
				}
			}
			planNum += v.PlanNum
		}
		res.Days = uint(libUtils.GetBetweenDays(minFirstSeen, maxLastSeen))
		res.PlanNum = uint(planNum)
		res.MaterialNum = uint(len(materialIds))
	})
	return
}

// 定时任务调用 GetDetail3
func (s *sAdxPlaylet) GetDetail3Timer(ctx context.Context) (err error) {
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	// 获取list 前三页
	listRes, err := service.AdxHotRanking().List(innerCtx, &model.AdxHotRankingSearchReq{
		PageReq: comModel.PageReq{
			PageNum:  1,
			PageSize: 30,
		},
		StartTime: libUtils.GetYesterdayDateString(),
		EndTime:   libUtils.GetYesterdayDateString(),
	})
	for _, item := range listRes.List {
		s.GetDetail3(innerCtx, item.PlayletId)
	}
	return
}

// GetDetail
func (s *sAdxPlaylet) GetDetail3(ctx context.Context, playletId uint64) (res *model.AdxPlayletDetialRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = new(model.AdxPlayletDetialRes)
		// 首先判断redis 缓存中是否存在
		cmdStr := commonService.GetGoRedis().Get(ctx, fmt.Sprintf(consts.ADXPlayletDetailData+"%d", playletId))
		if cmdStr.Err() == nil && cmdStr != nil {
			data := cmdStr.Val()
			// json 序列化
			err = json.Unmarshal([]byte(data), &res)
			return
		}
		var wg sync.WaitGroup
		var firstErr error
		var once sync.Once

		setErr := func(e error) {
			once.Do(func() { firstErr = e })
		}
		wg.Add(2)
		go func() {
			defer wg.Done()
			err = dao.AdxPlayletAnaltic.Ctx(ctx).WithAll().Where(dao.AdxPlaylet.Columns().PlayletId, playletId).Scan(&res)
			liberr.ErrIsNil(ctx, err, "获取信息失败")
			//  DATEDIFF(MAX(c.last_seen), MIN(c.first_seen)) + 1 AS days,
			innerErr := dao.AdxPlayletAnaltic.Ctx(ctx).Raw(`	SELECT
  (SELECT COUNT(*) FROM (
      SELECT DISTINCT m.material_id
      FROM adx_material m
      WHERE m.playlet_id = ?
  ) t1) AS materialNum,

  (SELECT COUNT(*) FROM (
      SELECT DISTINCT c.creative_id
      FROM adx_material m
      JOIN adx_creative c ON m.material_id = c.material_id
      WHERE m.playlet_id = ?
  ) t2) AS planNum`, playletId, playletId).Scan(&res)
			if innerErr != nil {
				setErr(innerErr)
				return
			}
		}()

		go func() {
			defer wg.Done()
			innerErr := dao.AdxPlayletAnaltic.Ctx(ctx).Raw(`	SELECT
		SUM(delivery_days) AS days
		FROM (
			SELECT
		m.material_id,
			m.playlet_id,
			DATEDIFF(MAX(c.last_seen), MIN(c.first_seen)) + 1 AS delivery_days
		FROM adx_creative c
		JOIN adx_material m ON c.material_id = m.material_id
		where m.playlet_id = ?
		GROUP BY m.material_id, m.playlet_id
		) AS material_delivery
		GROUP BY playlet_id
`, playletId).Scan(&res)
			if innerErr != nil {
				setErr(innerErr)
				return
			}
		}()
		if firstErr != nil {
			err = firstErr
			return
		}
		// 计算热力值
		_ = dao.AdxHotRanking.Ctx(ctx).Where(dao.AdxHotRanking.Columns().PlayletId, playletId).FieldMax(dao.AdxHotRanking.Columns().TotalConsumeNum, "heatValue").Scan(&res)
		wg.Wait()
		// 序列化res
		data, err := json.Marshal(res)
		liberr.ErrIsNil(ctx, err, "序列化失败")
		commonService.GetGoRedis().Set(ctx, fmt.Sprintf(consts.ADXPlayletDetailData+"%d", playletId), data, time.Hour*3)
	})
	return
}

func (s *sAdxPlaylet) Add(ctx context.Context, req *model.AdxPlayletAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPlaylet.Ctx(ctx).Insert(do.AdxPlaylet{
			PlayletId:           req.PlayletId,
			PlayletName:         req.PlayletName,
			CoverOss:            req.CoverOss,
			Description:         req.Description,
			ReleaseStartDate:    req.ReleaseStartDate,
			TotalEpisode:        req.TotalEpisode,
			ContractorList:      req.ContractorList,
			CopyrightHolderList: req.CopyrightHolderList,
			RelatedPartyList:    req.RelatedPartyList,
			Tags:                req.Tags,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

// BatchUpdate 批量更新
func (s *sAdxPlaylet) BatchUpdate(ctx context.Context, req []*model.AdxPlayletEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPlaylet.Ctx(ctx).Save(req)
	})
	return
}

func (s *sAdxPlaylet) Edit(ctx context.Context, req *model.AdxPlayletEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPlaylet.Ctx(ctx).WherePri(req.PlayletId).Update(do.AdxPlaylet{
			PlayletName:         req.PlayletName,
			CoverOss:            req.CoverOss,
			Description:         req.Description,
			ReleaseStartDate:    req.ReleaseStartDate,
			TotalEpisode:        req.TotalEpisode,
			ContractorList:      req.ContractorList,
			CopyrightHolderList: req.CopyrightHolderList,
			RelatedPartyList:    req.RelatedPartyList,
			Tags:                req.Tags,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdxPlaylet) Delete(ctx context.Context, playletIds []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdxPlaylet.Ctx(ctx).Delete(dao.AdxPlaylet.Columns().PlayletId+" in (?)", playletIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
