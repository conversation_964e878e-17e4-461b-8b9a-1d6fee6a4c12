/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameCreateV30MinPaymentTierRange
type ToolsWechatGameCreateV30MinPaymentTierRange string

// List of tools_wechat_game_create_v3.0_min_payment_tier_range
const (
	SIX_TEN_ToolsWechatGameCreateV30MinPaymentTierRange      ToolsWechatGameCreateV30MinPaymentTierRange = "SIX_TEN"
	TEN_TWENTY_ToolsWechatGameCreateV30MinPaymentTierRange   ToolsWechatGameCreateV30MinPaymentTierRange = "TEN_TWENTY"
	TWENTY_FIFTY_ToolsWechatGameCreateV30MinPaymentTierRange ToolsWechatGameCreateV30MinPaymentTierRange = "TWENTY_FIFTY"
	ZERO_FIVE_ToolsWechatGameCreateV30MinPaymentTierRange    ToolsWechatGameCreateV30MinPaymentTierRange = "ZERO_FIVE"
)

// Ptr returns reference to tools_wechat_game_create_v3.0_min_payment_tier_range value
func (v ToolsWechatGameCreateV30MinPaymentTierRange) Ptr() *ToolsWechatGameCreateV30MinPaymentTierRange {
	return &v
}
