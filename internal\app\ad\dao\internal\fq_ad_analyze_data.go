// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-16 15:29:37
// 生成路径: internal/app/ad/dao/internal/fq_ad_analyze_data.go
// 生成人：gfast
// desc: 获取回本统计-汇总数据
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FqAdAnalyzeDataDao is the manager for logic model data accessing and custom defined data operations functions management.
type FqAdAnalyzeDataDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns FqAdAnalyzeDataColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// FqAdAnalyzeDataColumns defines and stores column names for table fq_ad_analyze_data.
type FqAdAnalyzeDataColumns struct {
	Id                string // id
	DistributorId     string // 渠道ID
	PromotionId       string // 推广链id
	AddDesktopUserNum string // 加桌人数
	PaidRate          string // 付费率
	PaidUserNum       string // 充值人数，时间-投放当日
	RechargeAmount    string // 累积充值，单位 分
	Uv                string // 激活人数
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 删除时间
}

var fqAdAnalyzeDataColumns = FqAdAnalyzeDataColumns{
	Id:                "id",
	DistributorId:     "distributor_id",
	PromotionId:       "promotion_id",
	AddDesktopUserNum: "add_desktop_user_num",
	PaidRate:          "paid_rate",
	PaidUserNum:       "paid_user_num",
	RechargeAmount:    "recharge_amount",
	Uv:                "uv",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
}

// NewFqAdAnalyzeDataDao creates and returns a new DAO object for table data access.
func NewFqAdAnalyzeDataDao() *FqAdAnalyzeDataDao {
	return &FqAdAnalyzeDataDao{
		group:   "default",
		table:   "fq_ad_analyze_data",
		columns: fqAdAnalyzeDataColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *FqAdAnalyzeDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *FqAdAnalyzeDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *FqAdAnalyzeDataDao) Columns() FqAdAnalyzeDataColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *FqAdAnalyzeDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *FqAdAnalyzeDataDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *FqAdAnalyzeDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
