// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-02-21 11:03:05
// 生成路径: api/v1/order/pay_logs.go
// 生成人：cq
// desc:会员支付记录表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/order/model"
)

// PayLogsSearchReq 分页请求参数
type PayLogsSearchReq struct {
	g.Meta `path:"/list" tags:"会员支付记录表" method:"get" summary:"会员支付记录表列表"`
	commonApi.Author
	model.PayLogsSearchReq
}

// PayLogsSearchRes 列表返回结果
type PayLogsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.PayLogsSearchRes
}

// PayLogsExportReq 导出请求
type PayLogsExportReq struct {
	g.Meta `path:"/export" tags:"会员支付记录表" method:"get" summary:"会员支付记录表导出"`
	commonApi.Author
	model.PayLogsSearchReq
}

// PayLogsExportRes 导出响应
type PayLogsExportRes struct {
	commonApi.EmptyRes
}

// PayLogsAddReq 添加操作请求参数
type PayLogsAddReq struct {
	g.Meta `path:"/add" tags:"会员支付记录表" method:"post" summary:"会员支付记录表添加"`
	commonApi.Author
	*model.PayLogsAddReq
}

// PayLogsAddRes 添加操作返回结果
type PayLogsAddRes struct {
	commonApi.EmptyRes
}

// PayLogsEditReq 修改操作请求参数
type PayLogsEditReq struct {
	g.Meta `path:"/edit" tags:"会员支付记录表" method:"put" summary:"会员支付记录表修改"`
	commonApi.Author
	*model.PayLogsEditReq
}

// PayLogsEditRes 修改操作返回结果
type PayLogsEditRes struct {
	commonApi.EmptyRes
}

// PayLogsGetReq 获取一条数据请求
type PayLogsGetReq struct {
	g.Meta `path:"/get" tags:"会员支付记录表" method:"get" summary:"获取会员支付记录表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// PayLogsGetRes 获取一条数据结果
type PayLogsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.PayLogsInfoRes
}

// PayLogsDeleteReq 删除数据请求
type PayLogsDeleteReq struct {
	g.Meta `path:"/delete" tags:"会员支付记录表" method:"post" summary:"删除会员支付记录表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// PayLogsDeleteRes 删除数据返回
type PayLogsDeleteRes struct {
	commonApi.EmptyRes
}
