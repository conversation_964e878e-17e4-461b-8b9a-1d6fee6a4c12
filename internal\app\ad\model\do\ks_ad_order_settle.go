// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: internal/app/ad/model/entity/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdOrderSettle is the golang structure for table ks_ad_order_settle.
type KsAdOrderSettle struct {
	gmeta.Meta                       `orm:"table:ks_ad_order_settle, do:true"`
	Id                               interface{} `orm:"id,primary" json:"id"`                                                         // id
	AdvertiserId                     interface{} `orm:"advertiser_id" json:"advertiserId"`                                            // 用户快手号id
	SettleDate                       interface{} `orm:"settle_date" json:"settleDate"`                                                // 结算时间
	SeriesId                         interface{} `orm:"series_id" json:"seriesId"`                                                    // 短剧ID
	SeriesName                       interface{} `orm:"series_name" json:"seriesName"`                                                // 短剧名称
	CopyrightUid                     interface{} `orm:"copyright_uid" json:"copyrightUid"`                                            // 版权商UID
	CopyrightName                    interface{} `orm:"copyright_name" json:"copyrightName"`                                          // 版权商名称
	SalerUid                         interface{} `orm:"saler_uid" json:"salerUid"`                                                    // 分销商UID
	SalerName                        interface{} `orm:"saler_name" json:"salerName"`                                                  // 分销商名称
	SubAccountUid                    interface{} `orm:"sub_account_uid" json:"subAccountUid"`                                         // 子账号UID
	SubAccountName                   interface{} `orm:"sub_account_name" json:"subAccountName"`                                       // 子账号名称
	PayProvider                      interface{} `orm:"pay_provider" json:"payProvider"`                                              // 支付渠道
	PayAmt                           interface{} `orm:"pay_amt" json:"payAmt"`                                                        // 结算订单总金额（元）
	RedundPrice                      interface{} `orm:"redund_price" json:"redundPrice"`                                              // 退款金额（元）
	CommissionPrice                  interface{} `orm:"commission_price" json:"commissionPrice"`                                      // 佣金（元）
	SettlePrice                      interface{} `orm:"settle_price" json:"settlePrice"`                                              // 可提现金额（元）
	SubAccountOrderPayAmt            interface{} `orm:"sub_account_order_pay_amt" json:"subAccountOrderPayAmt"`                       // 子账号结算（元）
	CurAccountOrderPayAmt            interface{} `orm:"cur_account_order_pay_amt" json:"curAccountOrderPayAmt"`                       // 本账号结算（元）
	CopyrightDistributionOrderPayAmt interface{} `orm:"copyright_distribution_order_pay_amt" json:"copyrightDistributionOrderPayAmt"` // 分销分成前结算（元）（版权方视角）
	SalerDistributionOrderPayAmt     interface{} `orm:"saler_distribution_order_pay_amt" json:"salerDistributionOrderPayAmt"`         // 分销分成前结算（元）（分销视角）
	Expenditure                      interface{} `orm:"expenditure" json:"expenditure"`                                               // 总支出（元）
	PayDate                          interface{} `orm:"pay_date" json:"payDate"`                                                      // 支付时间
	OrderType                        interface{} `orm:"order_type" json:"orderType"`                                                  // 订单类型 本账号售卖、子账户售卖、分销售卖
	CreatedAt                        *gtime.Time `orm:"created_at" json:"createdAt"`                                                  // 创建时间
	UpdatedAt                        *gtime.Time `orm:"updated_at" json:"updatedAt"`                                                  // 更新时间
}
