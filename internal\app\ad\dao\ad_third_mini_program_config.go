// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: internal/app/ad/dao/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adThirdMiniProgramConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adThirdMiniProgramConfigDao struct {
	*internal.AdThirdMiniProgramConfigDao
}

var (
	// AdThirdMiniProgramConfig is globally public accessible object for table tools_gen_table operations.
	AdThirdMiniProgramConfig = adThirdMiniProgramConfigDao{
		internal.NewAdThirdMiniProgramConfigDao(),
	}
)

// Fill with you ideas below.
