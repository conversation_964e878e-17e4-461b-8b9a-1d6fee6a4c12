/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2BricksType
type ToolsSiteTemplateSiteCreateV2BricksType string

// List of tools_site_template_site_create_v2_bricks_type
const (
	BUTTON_ToolsSiteTemplateSiteCreateV2BricksType        ToolsSiteTemplateSiteCreateV2BricksType = "BUTTON"
	COUPON_ToolsSiteTemplateSiteCreateV2BricksType        ToolsSiteTemplateSiteCreateV2BricksType = "COUPON"
	FORM_ToolsSiteTemplateSiteCreateV2BricksType          ToolsSiteTemplateSiteCreateV2BricksType = "FORM"
	PICTURE_ToolsSiteTemplateSiteCreateV2BricksType       ToolsSiteTemplateSiteCreateV2BricksType = "PICTURE"
	PICTURE_GROUP_ToolsSiteTemplateSiteCreateV2BricksType ToolsSiteTemplateSiteCreateV2BricksType = "PICTURE_GROUP"
	RICH_TEXT_ToolsSiteTemplateSiteCreateV2BricksType     ToolsSiteTemplateSiteCreateV2BricksType = "RICH_TEXT"
	SIMPLE_TEXT_ToolsSiteTemplateSiteCreateV2BricksType   ToolsSiteTemplateSiteCreateV2BricksType = "SIMPLE_TEXT"
	VIDEO_ToolsSiteTemplateSiteCreateV2BricksType         ToolsSiteTemplateSiteCreateV2BricksType = "VIDEO"
	WECHAT_APPLET_ToolsSiteTemplateSiteCreateV2BricksType ToolsSiteTemplateSiteCreateV2BricksType = "WECHAT_APPLET"
	WECHAT_GAME_ToolsSiteTemplateSiteCreateV2BricksType   ToolsSiteTemplateSiteCreateV2BricksType = "WECHAT_GAME"
)

// Ptr returns reference to tools_site_template_site_create_v2_bricks_type value
func (v ToolsSiteTemplateSiteCreateV2BricksType) Ptr() *ToolsSiteTemplateSiteCreateV2BricksType {
	return &v
}
