// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: internal/app/ad/model/entity/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// FqAdAccountChannel is the golang structure for table fq_ad_account_channel.
type FqAdAccountChannel struct {
	gmeta.Meta           `orm:"table:fq_ad_account_channel"`
	ChannelDistributorId int64       `orm:"channel_distributor_id,primary" json:"channelDistributorId"` // 渠道ID
	DistributorId        int64       `orm:"distributor_id" json:"distributorId"`                        // 父级渠道ID
	NickName             string      `orm:"nick_name" json:"nickName"`                                  // 渠道名称
	AppId                string      `orm:"app_id" json:"appId"`                                        // 小程序ID
	AppName              string      `orm:"app_name" json:"appName"`                                    // 小程序名称
	AppType              int         `orm:"app_type" json:"appType"`                                    // 业务类型
	CreatedAt            *gtime.Time `orm:"created_at" json:"createdAt"`                                // 创建时间
	UpdatedAt            *gtime.Time `orm:"updated_at" json:"updatedAt"`                                // 更新时间
	DeletedAt            *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                // 删除时间
}
