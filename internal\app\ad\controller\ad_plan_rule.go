// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-27 11:18:34
// 生成路径: internal/app/ad/controller/ad_plan_rule.go
// 生成人：cq
// desc:广告计划规则设置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adPlanRuleController struct {
	systemController.BaseController
}

var AdPlanRule = new(adPlanRuleController)

// List 列表
func (c *adPlanRuleController) List(ctx context.Context, req *ad.AdPlanRuleSearchReq) (res *ad.AdPlanRuleSearchRes, err error) {
	res = new(ad.AdPlanRuleSearchRes)
	res.AdPlanRuleSearchRes, err = service.AdPlanRule().List(ctx, &req.AdPlanRuleSearchReq)
	return
}

// Get 获取广告计划规则设置
func (c *adPlanRuleController) Get(ctx context.Context, req *ad.AdPlanRuleGetReq) (res *ad.AdPlanRuleGetRes, err error) {
	res = new(ad.AdPlanRuleGetRes)
	res.AdPlanRuleInfoRes, err = service.AdPlanRule().GetById(ctx, req.Id)
	return
}

// Add 添加广告计划规则设置
func (c *adPlanRuleController) Add(ctx context.Context, req *ad.AdPlanRuleAddReq) (res *ad.AdPlanRuleAddRes, err error) {
	err = service.AdPlanRule().Add(ctx, req.AdPlanRuleAddReq)
	return
}

// Edit 修改广告计划规则设置
func (c *adPlanRuleController) Edit(ctx context.Context, req *ad.AdPlanRuleEditReq) (res *ad.AdPlanRuleEditRes, err error) {
	err = service.AdPlanRule().Edit(ctx, req.AdPlanRuleEditReq)
	return
}

// Delete 删除广告计划规则设置
func (c *adPlanRuleController) Delete(ctx context.Context, req *ad.AdPlanRuleDeleteReq) (res *ad.AdPlanRuleDeleteRes, err error) {
	err = service.AdPlanRule().Delete(ctx, req.Ids)
	return
}
