// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-18 10:28:45
// 生成路径: internal/app/ad/model/ad_third_mini_program_config.go
// 生成人：cq
// desc:第三方小程序配置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdThirdMiniProgramConfigInfoRes is the golang structure for table ad_third_mini_program_config.
type AdThirdMiniProgramConfigInfoRes struct {
	gmeta.Meta        `orm:"table:ad_third_mini_program_config"`
	Id                int64       `orm:"id,primary" json:"id" dc:""`                                    //
	AppId             string      `orm:"app_id" json:"appId" dc:"小程序ID"`                                // 小程序ID
	AppName           string      `orm:"app_name" json:"appName" dc:"小程序名称"`                            // 小程序名称
	OriginalId        string      `orm:"original_id" json:"originalId" dc:"小程序原始ID"`                    // 小程序原始ID
	AppType           string      `orm:"app_type" json:"appType" dc:"小程序类型：微信 抖音"`                      // 小程序类型：微信 抖音
	MonetizationModel string      `orm:"monetization_model" json:"monetizationModel" dc:"变现模式：IAP IAA"` // 变现模式：IAP IAA
	Platform          int         `orm:"platform" json:"platform" dc:"平台： 2: 番茄 3: 点众"`                 // 平台： 2: 番茄 3: 点众
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                         // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                         // 更新时间
}

type AdThirdMiniProgramConfigListRes struct {
	Id                int64       `json:"id" dc:""`
	AppId             string      `json:"appId" dc:"小程序ID"`
	AppName           string      `json:"appName" dc:"小程序名称"`
	OriginalId        string      `json:"originalId" dc:"小程序原始ID"`
	AppType           string      `json:"appType" dc:"小程序类型：微信 抖音"`
	MonetizationModel string      `json:"monetizationModel" dc:"变现模式：IAP IAA"`
	Platform          int         `json:"platform" dc:"平台： 2: 番茄 3: 点众"`
	CreatedAt         *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdThirdMiniProgramConfigSearchReq 分页请求参数
type AdThirdMiniProgramConfigSearchReq struct {
	comModel.PageReq
	AppId             string `p:"appId" dc:"小程序ID"`                                                       //小程序ID
	AppName           string `p:"appName" dc:"小程序名称"`                                                     //小程序名称
	OriginalId        string `p:"originalId" dc:"小程序原始ID"`                                                //小程序原始ID
	AppType           string `p:"appType" dc:"小程序类型：微信 抖音"`                                               //小程序类型：微信 抖音
	MonetizationModel string `p:"monetizationModel" dc:"变现模式：IAP IAA"`                                    //变现模式：IAP IAA
	Platform          string `p:"platform" v:"platform@integer#平台： 2: 番茄 3: 点众需为整数" dc:"平台： 2: 番茄 3: 点众"` //平台： 2: 番茄 3: 点众
}

// AdThirdMiniProgramConfigSearchRes 列表返回结果
type AdThirdMiniProgramConfigSearchRes struct {
	comModel.ListRes
	List []*AdThirdMiniProgramConfigListRes `json:"list"`
}

// AdThirdMiniProgramConfigAddReq 添加操作请求参数
type AdThirdMiniProgramConfigAddReq struct {
	AppId             string `p:"appId"  dc:"小程序ID"`
	AppName           string `p:"appName" v:"required#小程序名称不能为空" dc:"小程序名称"`
	OriginalId        string `p:"originalId"  dc:"小程序原始ID"`
	AppType           string `p:"appType"  dc:"小程序类型：微信 抖音"`
	MonetizationModel string `p:"monetizationModel"  dc:"变现模式：IAP IAA"`
	Platform          int    `p:"platform"  dc:"平台： 2: 番茄 3: 点众"`
}

// AdThirdMiniProgramConfigEditReq 修改操作请求参数
type AdThirdMiniProgramConfigEditReq struct {
	Id                int64  `p:"id" v:"required#主键ID不能为空" dc:""`
	AppId             string `p:"appId"  dc:"小程序ID"`
	AppName           string `p:"appName" v:"required#小程序名称不能为空" dc:"小程序名称"`
	OriginalId        string `p:"originalId"  dc:"小程序原始ID"`
	AppType           string `p:"appType"  dc:"小程序类型：微信 抖音"`
	MonetizationModel string `p:"monetizationModel"  dc:"变现模式：IAP IAA"`
	Platform          int    `p:"platform"  dc:"平台： 2: 番茄 3: 点众"`
}
