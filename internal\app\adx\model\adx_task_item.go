// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-09 11:04:07
// 生成路径: internal/app/adx/model/adx_task_item.go
// 生成人：cyao
// desc:ADX任务素材明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxTaskItemInfoRes is the golang structure for table adx_task_item.
type AdxTaskItemInfoRes struct {
	gmeta.Meta    `orm:"table:adx_task_item"`
	Id            int64       `orm:"id,primary" json:"id" dc:"主键ID"`              // 主键ID
	TaskId        int64       `orm:"task_id" json:"taskId" dc:"关联任务ID（外键）"`       // 关联任务ID（外键）
	MaterialUrl   string      `orm:"material_url" json:"materialUrl" dc:"素材文件地址"` // 素材文件地址
	ImgUrl        string      `orm:"img_url" json:"imgUrl" dc:"图片地址"`
	MaterialType  string      `orm:"material_type" json:"materialType" dc:"素材类型（图片、视频）"`          // 素材类型（图片、视频）
	MaterialSize  string      `orm:"material_size" json:"materialSize" dc:"素材尺寸，如1080*1940"`      // 素材尺寸，如1080*1940
	RelatedScript string      `orm:"related_script" json:"relatedScript" dc:"关联短剧名称"`             // 关联短剧名称
	OperationTime *gtime.Time `orm:"operation_time" json:"operationTime" dc:"操作时间"`               // 操作时间
	Result        string      `orm:"result" json:"result" dc:"执行结果（成功、失败）"`                       // 执行结果（成功、失败）
	FailReason    string      `orm:"fail_reason" json:"failReason" dc:"失败原因（仅在 result = 失败 时有值）"` // 失败原因（仅在 result = 失败 时有值）
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                       // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                       // 更新时间
}

type AdxTaskItemListRes struct {
	Id            int64       `json:"id" dc:"主键ID"`
	TaskId        int64       `json:"taskId" dc:"关联任务ID（外键）"`
	ImgUrl        string      `json:"imgUrl" dc:"图片地址"`
	MaterialUrl   string      `json:"materialUrl" dc:"素材文件地址"`
	MaterialType  string      `json:"materialType" dc:"素材类型（图片、视频）"`
	MaterialSize  string      `json:"materialSize" dc:"素材尺寸，如1080*1940"`
	RelatedScript string      `json:"relatedScript" dc:"关联短剧名称"`
	OperationTime *gtime.Time `json:"operationTime" dc:"操作时间"`
	Result        string      `json:"result" dc:"执行结果（成功、失败）"`
	FailReason    string      `json:"failReason" dc:"失败原因（仅在 result = 失败 时有值）"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdxTaskItemSearchReq 分页请求参数
type AdxTaskItemSearchReq struct {
	comModel.PageReq
	Id            string `p:"id" dc:"主键ID"`                                             //主键ID
	TaskId        string `p:"taskId" v:"taskId@integer#关联任务ID（外键）需为整数" dc:"关联任务ID（外键）"` //关联任务ID（外键）
	ImgUrl        string `p:"imgUrl" dc:"图片地址"`
	MaterialUrl   string `p:"materialUrl" dc:"素材文件地址"`                                                        //素材文件地址
	MaterialType  string `p:"materialType" dc:"素材类型（图片、视频）"`                                                  //素材类型（图片、视频）
	MaterialSize  string `p:"materialSize" dc:"素材尺寸，如1080*1940"`                                              //素材尺寸，如1080*1940
	RelatedScript string `p:"relatedScript" dc:"关联短剧名称"`                                                      //关联短剧名称
	OperationTime string `p:"operationTime" v:"operationTime@datetime#操作时间需为YYYY-MM-DD hh:mm:ss格式" dc:"操作时间"` //操作时间
	Result        string `p:"result" dc:"执行结果（成功、失败）"`                                                        //执行结果（成功、失败）
	FailReason    string `p:"failReason" dc:"失败原因（仅在 result = 失败 时有值）"`                                       //失败原因（仅在 result = 失败 时有值）
	CreatedAt     string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`         //创建时间
}

// AdxTaskItemSearchRes 列表返回结果
type AdxTaskItemSearchRes struct {
	comModel.ListRes
	List []*AdxTaskItemListRes `json:"list"`
}
type AdxTaskItemDownloadStatusReq struct {
	TaskItemId int64  `p:"taskItemId" v:"required#任务ID不能为空" dc:"任务itemID"`
	Status     string `p:"status" v:"required#状态不能为空" dc:"状态"`
	FailReason string `p:"failReason" dc:"失败原因"`
}

// AdxTaskItemAddReq 添加操作请求参数
type AdxTaskItemAddReq struct {
	TaskId      int64  `p:"taskId" v:"required#关联任务ID（外键）不能为空" dc:"关联任务ID（外键）"`
	MaterialUrl string `p:"materialUrl"  dc:"素材文件地址"`
	//ImgUrl
	ImgUrl        string      `p:"imgUrl"  dc:"图片地址"`
	MaterialType  string      `p:"materialType"  dc:"素材类型（图片、视频）"`
	MaterialSize  string      `p:"materialSize"  dc:"素材尺寸，如1080*1940"`
	RelatedScript string      `p:"relatedScript"  dc:"关联短剧名称"`
	OperationTime *gtime.Time `p:"operationTime"  dc:"操作时间"`
	Result        string      `p:"result"  dc:"执行结果（成功、失败）"`
	FailReason    string      `p:"failReason"  dc:"失败原因（仅在 result = 失败 时有值）"`
}

// AdxTaskItemEditReq 修改操作请求参数
type AdxTaskItemEditReq struct {
	Id            int64       `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	TaskId        int64       `p:"taskId" v:"required#关联任务ID（外键）不能为空" dc:"关联任务ID（外键）"`
	MaterialUrl   string      `p:"materialUrl"  dc:"素材文件地址"`
	MaterialType  string      `p:"materialType"  dc:"素材类型（图片、视频）"`
	MaterialSize  string      `p:"materialSize"  dc:"素材尺寸，如1080*1940"`
	RelatedScript string      `p:"relatedScript"  dc:"关联短剧名称"`
	OperationTime *gtime.Time `p:"operationTime"  dc:"操作时间"`
	Result        string      `p:"result"  dc:"执行结果（成功、失败）"`
	FailReason    string      `p:"failReason"  dc:"失败原因（仅在 result = 失败 时有值）"`
}
