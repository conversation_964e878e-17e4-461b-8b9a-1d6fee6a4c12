// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-07 14:26:18
// 生成路径: internal/app/ad/model/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdReportStatsInfoRes is the golang structure for table ks_ad_report_stats.
type KsAdReportStatsInfoRes struct {
	gmeta.Meta                                    `orm:"table:ks_ad_report_stats"`
	AdvertiserId                                  int64       `orm:"advertiser_id,primary" json:"advertiserId" dc:"账户ID"`                                                                          // 账户ID
	Id                                            uint64      `orm:"id,primary" json:"id" dc:""`                                                                                                   //
	Likes                                         int         `orm:"likes" json:"likes" dc:"点赞数"`                                                                                                  // 点赞数
	Share                                         int         `orm:"share" json:"share" dc:"分享数"`                                                                                                  // 分享数
	PhotoClick                                    int         `orm:"photo_click" json:"photoClick" dc:"封面点击数"`                                                                                     // 封面点击数
	Impression                                    int         `orm:"impression" json:"impression" dc:"封面曝光数"`                                                                                      // 封面曝光数
	EventPay                                      int         `orm:"event_pay" json:"eventPay" dc:"付费次数"`                                                                                          // 付费次数
	T0DirectPaiedCnt                              int         `orm:"t0_direct_paied_cnt" json:"t0DirectPaiedCnt" dc:"付费次数(计费时间)"`                                                                  // 付费次数(计费时间)
	EventPayPurchaseAmount                        float64     `orm:"event_pay_purchase_amount" json:"eventPayPurchaseAmount" dc:"付费金额"`                                                            // 付费金额
	T0DirectPaiedAmt                              float64     `orm:"t0_direct_paied_amt" json:"t0DirectPaiedAmt" dc:"付费金额(计费时间)"`                                                                  // 付费金额(计费时间)
	AdShow                                        int         `orm:"ad_show" json:"adShow" dc:"广告曝光"`                                                                                              // 广告曝光
	TotalCharge                                   float64     `orm:"total_charge" json:"totalCharge" dc:"消耗"`                                                                                      // 消耗
	EventAppInvoked                               int         `orm:"event_app_invoked" json:"eventAppInvoked" dc:"唤起应用数"`                                                                          // 唤起应用数
	EventPayPurchaseAmountFirstDay                float64     `orm:"event_pay_purchase_amount_first_day" json:"eventPayPurchaseAmountFirstDay" dc:"激活当日付费金额"`                                      // 激活当日付费金额
	EventPayPurchaseAmountOneDayByConversion      float64     `orm:"event_pay_purchase_amount_one_day_by_conversion" json:"eventPayPurchaseAmountOneDayByConversion" dc:"激活后24h付费金额(激活时间)"`        // 激活后24h付费金额(激活时间)
	EventPayPurchaseAmountWeekByConversion        float64     `orm:"event_pay_purchase_amount_week_by_conversion" json:"eventPayPurchaseAmountWeekByConversion" dc:"激活后七日付费金额"`                    // 激活后七日付费金额
	EventPayPurchaseAmountThreeDayByConversion    float64     `orm:"event_pay_purchase_amount_three_day_by_conversion" json:"eventPayPurchaseAmountThreeDayByConversion" dc:"激活后三日付费金额"`           // 激活后三日付费金额
	Conversion                                    int         `orm:"conversion" json:"conversion" dc:"激活数"`                                                                                        // 激活数
	T0DirectConversionCnt                         int         `orm:"t0_direct_conversion_cnt" json:"t0DirectConversionCnt" dc:"激活数(计费时间)"`                                                         // 激活数(计费时间)
	Negative                                      int         `orm:"negative" json:"negative" dc:"减少此类作品数"`                                                                                        // 减少此类作品数
	Report                                        int         `orm:"report" json:"report" dc:"举报数"`                                                                                                // 举报数
	Block                                         int         `orm:"block" json:"block" dc:"拉黑数"`                                                                                                  // 拉黑数
	Comment                                       int         `orm:"comment" json:"comment" dc:"评论数"`                                                                                              // 评论数
	EventPayFirstDay                              int         `orm:"event_pay_first_day" json:"eventPayFirstDay" dc:"首日付费次数"`                                                                      // 首日付费次数
	PlayedNum                                     int         `orm:"played_num" json:"playedNum" dc:"素材曝光数"`                                                                                       // 素材曝光数
	PlayedThreeSeconds                            int         `orm:"played_three_seconds" json:"playedThreeSeconds" dc:"3s播放数"`                                                                    // 3s播放数
	AdPhotoPlayed75Percent                        int         `orm:"ad_photo_played75percent" json:"adPhotoPlayed75Percent" dc:"75%播放进度数"`                                                         // 75%播放进度数
	PlayedEnd                                     int         `orm:"played_end" json:"playedEnd" dc:"完播数"`                                                                                         // 完播数
	Follow                                        int         `orm:"follow" json:"follow" dc:"新增粉丝数"`                                                                                              // 新增粉丝数
	EventNewUserPay                               int         `orm:"event_new_user_pay" json:"eventNewUserPay" dc:"新增付费人数"`                                                                        // 新增付费人数
	AdItemClick                                   int         `orm:"ad_item_click" json:"adItemClick" dc:"行为数"`                                                                                    // 行为数
	T7PaiedCnt                                    int         `orm:"t7_paied_cnt" json:"t7PaiedCnt" dc:"7日累计付费次数"`                                                                                 // 7日累计付费次数
	T7PaiedAmt                                    float64     `orm:"t7_paied_amt" json:"t7PaiedAmt" dc:"7日累计付费金额"`                                                                                 // 7日累计付费金额
	ConversionNumByImpression7D                   int         `orm:"conversion_num_by_impression7d" json:"conversionNumByImpression7D" dc:"转化数(计费时间)"`                                             // 转化数(计费时间)
	DeepConversionNumByImpression7D               int         `orm:"deep_conversion_num_by_impression7d" json:"deepConversionNumByImpression7D" dc:"深度转化数(计费时间)"`                                  // 深度转化数(计费时间)
	ConversionNum                                 int         `orm:"conversion_num" json:"conversionNum" dc:"转化数(回传时间)"`                                                                           // 转化数(回传时间)
	DeepConversionNum                             int         `orm:"deep_conversion_num" json:"deepConversionNum" dc:"深度转化数"`                                                                      // 深度转化数
	T0PaiedCnt                                    int         `orm:"t0_paied_cnt" json:"t0PaiedCnt" dc:"当日累计付费次数"`                                                                                 // 当日累计付费次数
	T0PaiedAmt                                    float64     `orm:"t0_paied_amt" json:"t0PaiedAmt" dc:"当日累计付费金额"`                                                                                 // 当日累计付费金额
	Play3SRatio                                   float64     `orm:"play3s_ratio" json:"play3SRatio" dc:"3s播放率"`                                                                                   // 3s播放率
	AdPhotoPlayed75PercentRatio2                  float64     `orm:"ad_photo_played_75percent_ratio" json:"adPhotoPlayed75PercentRatio2" dc:"75%进度播放率"`                                            // 75%进度播放率
	T7PaiedRoi                                    float64     `orm:"t7_paied_roi" json:"t7PaiedRoi" dc:"7日累计ROI"`                                                                                  // 7日累计ROI
	T0PaiedRoi                                    float64     `orm:"t0_paied_roi" json:"t0PaiedRoi" dc:"当日累计ROI"`                                                                                  // 当日累计ROI
	PhotoClickRatio                               float64     `orm:"photo_click_ratio" json:"photoClickRatio" dc:"封面点击率"`                                                                          // 封面点击率
	EventPayCost                                  float64     `orm:"event_pay_cost" json:"eventPayCost" dc:"付费次数成本"`                                                                               // 付费次数成本
	EventPayRoi                                   float64     `orm:"event_pay_roi" json:"eventPayRoi" dc:"付费ROI"`                                                                                  // 付费ROI
	EventAppInvokedCost                           float64     `orm:"event_app_invoked_cost" json:"eventAppInvokedCost" dc:"唤起应用成本"`                                                                // 唤起应用成本
	EventAppInvokedRatio                          float64     `orm:"event_app_invoked_ratio" json:"eventAppInvokedRatio" dc:"唤起应用率"`                                                               // 唤起应用率
	ConversionCost                                float64     `orm:"conversion_cost" json:"conversionCost" dc:"激活单价"`                                                                              // 激活单价
	EventPayFirstDayRoi                           float64     `orm:"event_pay_first_day_roi" json:"eventPayFirstDayRoi" dc:"激活当日ROI"`                                                              // 激活当日ROI
	EventPayPurchaseAmountOneDayByConversionRoi   float64     `orm:"event_pay_purchase_amount_one_day_by_conversion_roi" json:"eventPayPurchaseAmountOneDayByConversionRoi" dc:"激活后24h-ROI(激活时间)"` // 激活后24h-ROI(激活时间)
	EventPayPurchaseAmountThreeDayByConversionRoi float64     `orm:"event_pay_purchase_amount_three_day_by_conversion_roi" json:"eventPayPurchaseAmountThreeDayByConversionRoi" dc:"激活后3日ROI"`     // 激活后3日ROI
	EventPayPurchaseAmountWeekByConversionRoi     float64     `orm:"event_pay_purchase_amount_week_by_conversion_roi" json:"eventPayPurchaseAmountWeekByConversionRoi" dc:"激活后7日ROI"`              // 激活后7日ROI
	PhotoClickCost                                float64     `orm:"photo_click_cost" json:"photoClickCost" dc:"平均封面点击单价（元）"`                                                                      // 平均封面点击单价（元）
	Impression1KCost                              float64     `orm:"impression1k_cost" json:"impression1KCost" dc:"平均千次封面曝光花费（元）"`                                                                 // 平均千次封面曝光花费（元）
	Click1KCost                                   float64     `orm:"click1k_cost" json:"click1KCost" dc:"平均千次素材曝光花费（元）"`                                                                           // 平均千次素材曝光花费（元）
	ActionCost                                    float64     `orm:"action_cost" json:"actionCost" dc:"平均行为单价（元）"`                                                                                 // 平均行为单价（元）
	DeepConversionCostByImpression7D              float64     `orm:"deep_conversion_cost_by_impression7d" json:"deepConversionCostByImpression7D" dc:"深度转化成本(计费时间)，单位元"`                           // 深度转化成本(计费时间)，单位元
	DeepConversionRatioByImpression7D             float64     `orm:"deep_conversion_ratio_by_impression7d" json:"deepConversionRatioByImpression7D" dc:"深度转化率(计费时间)"`                              // 深度转化率(计费时间)
	EventPayFirstDayCost                          float64     `orm:"event_pay_first_day_cost" json:"eventPayFirstDayCost" dc:"首日付费次数成本，单位元"`                                                       // 首日付费次数成本，单位元
	ActionRatio                                   float64     `orm:"action_ratio" json:"actionRatio" dc:"素材点击率"`                                                                                   // 素材点击率
	PlayEndRatio                                  float64     `orm:"play_end_ratio" json:"playEndRatio" dc:"完播率"`                                                                                  // 完播率
	EventNewUserPayCost                           float64     `orm:"event_new_user_pay_cost" json:"eventNewUserPayCost" dc:"新增付费人数成本，单位元"`                                                         // 新增付费人数成本，单位元
	EventNewUserPayRatio                          float64     `orm:"event_new_user_pay_ratio" json:"eventNewUserPayRatio" dc:"新增付费人数率"`                                                            // 新增付费人数率
	ActionNewRatio                                float64     `orm:"action_new_ratio" json:"actionNewRatio" dc:"行为率"`                                                                              // 行为率
	ConversionCostByImpression7D                  float64     `orm:"conversion_cost_by_impression7d" json:"conversionCostByImpression7D" dc:"转化成本(计费时间)，单位元"`                                      // 转化成本(计费时间)，单位元
	ConversionRatioByImpression7D                 float64     `orm:"conversion_ratio_by_impression7d" json:"conversionRatioByImpression7D" dc:"转化率(计费时间)"`                                         // 转化率(计费时间)
	Date                                          *gtime.Time `orm:"date" json:"date" dc:"日期，格式：yyyy-MM-dd HH:mm:ss"`                                                                              // 日期，格式：yyyy-MM-dd HH:mm:ss
	KeyAction                                     int64       `orm:"key_action" json:"keyAction" dc:"关键行为数"`                                                                                       // 关键行为数
	AdPhotoPlayed75PercentRatio                   float64     `orm:"ad_photo_played75percent_ratio" json:"adPhotoPlayed75PercentRatio" dc:"75%进度播放数"`                                              // 75%进度播放数
	AccountId                                     int64       `orm:"account_id" json:"accountId" dc:"账号ID"`                                                                                        // 账号ID
	SeriesId                                      int64       `orm:"series_id" json:"seriesId" dc:"短剧ID"`                                                                                          // 短剧ID
	RechargeRate                                  float64     `orm:"recharge_rate" json:"rechargeRate" dc:"充值几率"`                                                                                  // 充值几率
	MiniGameIaaRoi                                float64     `orm:"mini_game_iaa_roi" json:"miniGameIaaRoi" dc:"IAA广告变现ROI"`                                                                      // IAA广告变现ROI
	MiniGameIaaPurchaseAmount                     float64     `orm:"mini_game_iaa_purchase_amount" json:"miniGameIaaPurchaseAmount" dc:"IAA广告变现LTV（元）"`                                            // IAA广告变现LTV（元）
	CreateTime                                    string      `orm:"create_time" json:"createTime" dc:"统计日期"`                                                                                      // 统计日期
}

type KsAdReportStatsListRes struct {
	AdvertiserId                                  int64       `json:"advertiserId" dc:"账户ID"`
	AdAccountName                                 string      `json:"adAccountName"  dc:"快手经营者账号名称"`
	AccountMainName                               string      `json:"accountMainName"   dc:"账号主体名称"`
	Id                                            uint64      `json:"id" dc:""`
	Likes                                         int         `json:"likes" dc:"点赞数"`
	Share                                         int         `json:"share" dc:"分享数"`
	PhotoClick                                    int         `json:"photoClick" dc:"封面点击数"`
	Impression                                    int         `json:"impression" dc:"封面曝光数"`
	EventPay                                      int         `json:"eventPay" dc:"付费次数"`
	T0DirectPaiedCnt                              int         `json:"t0DirectPaiedCnt" dc:"付费次数(计费时间)"`
	EventPayPurchaseAmount                        float64     `json:"eventPayPurchaseAmount" dc:"付费金额"`
	T0DirectPaiedAmt                              float64     `json:"t0DirectPaiedAmt" dc:"付费金额(计费时间)"`
	AdShow                                        int         `json:"adShow" dc:"广告曝光"`
	TotalCharge                                   float64     `json:"totalCharge" dc:"消耗"`
	EventAppInvoked                               int         `json:"eventAppInvoked" dc:"唤起应用数"`
	EventPayPurchaseAmountFirstDay                float64     `json:"eventPayPurchaseAmountFirstDay" dc:"激活当日付费金额"`
	EventPayPurchaseAmountOneDayByConversion      float64     `json:"eventPayPurchaseAmountOneDayByConversion" dc:"激活后24h付费金额(激活时间)"`
	EventPayPurchaseAmountWeekByConversion        float64     `json:"eventPayPurchaseAmountWeekByConversion" dc:"激活后七日付费金额"`
	EventPayPurchaseAmountThreeDayByConversion    float64     `json:"eventPayPurchaseAmountThreeDayByConversion" dc:"激活后三日付费金额"`
	Conversion                                    int         `json:"conversion" dc:"激活数"`
	T0DirectConversionCnt                         int         `json:"t0DirectConversionCnt" dc:"激活数(计费时间)"`
	Negative                                      int         `json:"negative" dc:"减少此类作品数"`
	Report                                        int         `json:"report" dc:"举报数"`
	Block                                         int         `json:"block" dc:"拉黑数"`
	Comment                                       int         `json:"comment" dc:"评论数"`
	EventPayFirstDay                              int         `json:"eventPayFirstDay" dc:"首日付费次数"`
	PlayedNum                                     int         `json:"playedNum" dc:"素材曝光数"`
	PlayedThreeSeconds                            int         `json:"playedThreeSeconds" dc:"3s播放数"`
	AdPhotoPlayed75Percent                        int         `json:"adPhotoPlayed75Percent" dc:"75%播放进度数"`
	PlayedEnd                                     int         `json:"playedEnd" dc:"完播数"`
	Follow                                        int         `json:"follow" dc:"新增粉丝数"`
	EventNewUserPay                               int         `json:"eventNewUserPay" dc:"新增付费人数"`
	AdItemClick                                   int         `json:"adItemClick" dc:"行为数"`
	T7PaiedCnt                                    int         `json:"t7PaiedCnt" dc:"7日累计付费次数"`
	T7PaiedAmt                                    float64     `json:"t7PaiedAmt" dc:"7日累计付费金额"`
	ConversionNumByImpression7D                   int         `json:"conversionNumByImpression7D" dc:"转化数(计费时间)"`
	DeepConversionNumByImpression7D               int         `json:"deepConversionNumByImpression7D" dc:"深度转化数(计费时间)"`
	ConversionNum                                 int         `json:"conversionNum" dc:"转化数(回传时间)"`
	DeepConversionNum                             int         `json:"deepConversionNum" dc:"深度转化数"`
	T0PaiedCnt                                    int         `json:"t0PaiedCnt" dc:"当日累计付费次数"`
	T0PaiedAmt                                    float64     `json:"t0PaiedAmt" dc:"当日累计付费金额"`
	Play3SRatio                                   float64     `json:"play3SRatio" dc:"3s播放率"`
	AdPhotoPlayed75PercentRatio                   float64     `json:"adPhotoPlayed75PercentRatio" dc:"75%进度播放率"`
	T7PaiedRoi                                    float64     `json:"t7PaiedRoi" dc:"7日累计ROI"`
	T0PaiedRoi                                    float64     `json:"t0PaiedRoi" dc:"当日累计ROI"`
	PhotoClickRatio                               float64     `json:"photoClickRatio" dc:"封面点击率"`
	EventPayCost                                  float64     `json:"eventPayCost" dc:"付费次数成本"`
	EventPayRoi                                   float64     `json:"eventPayRoi" dc:"付费ROI"`
	EventAppInvokedCost                           float64     `json:"eventAppInvokedCost" dc:"唤起应用成本"`
	EventAppInvokedRatio                          float64     `json:"eventAppInvokedRatio" dc:"唤起应用率"`
	ConversionCost                                float64     `json:"conversionCost" dc:"激活单价"`
	EventPayFirstDayRoi                           float64     `json:"eventPayFirstDayRoi" dc:"激活当日ROI"`
	EventPayPurchaseAmountOneDayByConversionRoi   float64     `json:"eventPayPurchaseAmountOneDayByConversionRoi" dc:"激活后24h-ROI(激活时间)"`
	EventPayPurchaseAmountThreeDayByConversionRoi float64     `json:"eventPayPurchaseAmountThreeDayByConversionRoi" dc:"激活后3日ROI"`
	EventPayPurchaseAmountWeekByConversionRoi     float64     `json:"eventPayPurchaseAmountWeekByConversionRoi" dc:"激活后7日ROI"`
	PhotoClickCost                                float64     `json:"photoClickCost" dc:"平均封面点击单价（元）"`
	Impression1KCost                              float64     `json:"impression1KCost" dc:"平均千次封面曝光花费（元）"`
	Click1KCost                                   float64     `json:"click1KCost" dc:"平均千次素材曝光花费（元）"`
	ActionCost                                    float64     `json:"actionCost" dc:"平均行为单价（元）"`
	DeepConversionCostByImpression7D              float64     `json:"deepConversionCostByImpression7D" dc:"深度转化成本(计费时间)，单位元"`
	DeepConversionRatioByImpression7D             float64     `json:"deepConversionRatioByImpression7D" dc:"深度转化率(计费时间)"`
	EventPayFirstDayCost                          float64     `json:"eventPayFirstDayCost" dc:"首日付费次数成本，单位元"`
	ActionRatio                                   float64     `json:"actionRatio" dc:"素材点击率"`
	PlayEndRatio                                  float64     `json:"playEndRatio" dc:"完播率"`
	EventNewUserPayCost                           float64     `json:"eventNewUserPayCost" dc:"新增付费人数成本，单位元"`
	EventNewUserPayRatio                          float64     `json:"eventNewUserPayRatio" dc:"新增付费人数率"`
	ActionNewRatio                                float64     `json:"actionNewRatio" dc:"行为率"`
	ConversionCostByImpression7D                  float64     `json:"conversionCostByImpression7D" dc:"转化成本(计费时间)，单位元"`
	ConversionRatioByImpression7D                 float64     `json:"conversionRatioByImpression7D" dc:"转化率(计费时间)"`
	Date                                          *gtime.Time `json:"date" dc:"日期，格式：yyyy-MM-dd HH:mm:ss"`
	KeyAction                                     int64       `json:"keyAction" dc:"关键行为数"`
	AdPhotoPlayed75PercentRatio2                  float64     `json:"adPhotoPlayed75PercentRatio2" dc:"75%进度播放数"`
	AccountId                                     int64       `json:"accountId" dc:"账号ID"`
	SeriesId                                      int64       `json:"seriesId" dc:"短剧ID"`
	RechargeRate                                  float64     `json:"rechargeRate" dc:"充值几率"`
	MiniGameIaaRoi                                float64     `json:"miniGameIaaRoi" dc:"IAA广告变现ROI"`
	MiniGameIaaPurchaseAmount                     float64     `json:"miniGameIaaPurchaseAmount" dc:"IAA广告变现LTV（元）"`
	CreateTime                                    string      `json:"createTime" dc:"统计日期"`
}

// KsAdReportStatsSearchReq 分页请求参数
type KsAdReportStatsSearchReq struct {
	comModel.PageReq
	AdvertiserId                                  string   `p:"advertiserId" dc:"账户ID"`                                                                                                                        //账户ID
	AdvertiserIds                                 []string `p:"advertiserIds" dc:"账户ID"`                                                                                                                       //账户ID
	Id                                            string   `p:"id" dc:""`                                                                                                                                      //
	Likes                                         string   `p:"likes" v:"likes@integer#点赞数需为整数" dc:"点赞数"`                                                                                                      //点赞数
	Share                                         string   `p:"share" v:"share@integer#分享数需为整数" dc:"分享数"`                                                                                                      //分享数
	PhotoClick                                    string   `p:"photoClick" v:"photoClick@integer#封面点击数需为整数" dc:"封面点击数"`                                                                                        //封面点击数
	Impression                                    string   `p:"impression" v:"impression@integer#封面曝光数需为整数" dc:"封面曝光数"`                                                                                        //封面曝光数
	EventPay                                      string   `p:"eventPay" v:"eventPay@integer#付费次数需为整数" dc:"付费次数"`                                                                                              //付费次数
	T0DirectPaiedCnt                              string   `p:"t0DirectPaiedCnt" v:"t0DirectPaiedCnt@integer#付费次数(计费时间)需为整数" dc:"付费次数(计费时间)"`                                                                  //付费次数(计费时间)
	EventPayPurchaseAmount                        string   `p:"eventPayPurchaseAmount" v:"eventPayPurchaseAmount@float#付费金额需为浮点数" dc:"付费金额"`                                                                   //付费金额
	T0DirectPaiedAmt                              string   `p:"t0DirectPaiedAmt" v:"t0DirectPaiedAmt@float#付费金额(计费时间)需为浮点数" dc:"付费金额(计费时间)"`                                                                   //付费金额(计费时间)
	AdShow                                        string   `p:"adShow" v:"adShow@integer#广告曝光需为整数" dc:"广告曝光"`                                                                                                  //广告曝光
	TotalCharge                                   string   `p:"totalCharge" v:"totalCharge@float#消耗需为浮点数" dc:"消耗"`                                                                                             //消耗
	EventAppInvoked                               string   `p:"eventAppInvoked" v:"eventAppInvoked@integer#唤起应用数需为整数" dc:"唤起应用数"`                                                                              //唤起应用数
	EventPayPurchaseAmountFirstDay                string   `p:"eventPayPurchaseAmountFirstDay" v:"eventPayPurchaseAmountFirstDay@float#激活当日付费金额需为浮点数" dc:"激活当日付费金额"`                                           //激活当日付费金额
	EventPayPurchaseAmountOneDayByConversion      string   `p:"eventPayPurchaseAmountOneDayByConversion" v:"eventPayPurchaseAmountOneDayByConversion@float#激活后24h付费金额(激活时间)需为浮点数" dc:"激活后24h付费金额(激活时间)"`       //激活后24h付费金额(激活时间)
	EventPayPurchaseAmountWeekByConversion        string   `p:"eventPayPurchaseAmountWeekByConversion" v:"eventPayPurchaseAmountWeekByConversion@float#激活后七日付费金额需为浮点数" dc:"激活后七日付费金额"`                         //激活后七日付费金额
	EventPayPurchaseAmountThreeDayByConversion    string   `p:"eventPayPurchaseAmountThreeDayByConversion" v:"eventPayPurchaseAmountThreeDayByConversion@float#激活后三日付费金额需为浮点数" dc:"激活后三日付费金额"`                 //激活后三日付费金额
	Conversion                                    string   `p:"conversion" v:"conversion@integer#激活数需为整数" dc:"激活数"`                                                                                            //激活数
	T0DirectConversionCnt                         string   `p:"t0DirectConversionCnt" v:"t0DirectConversionCnt@integer#激活数(计费时间)需为整数" dc:"激活数(计费时间)"`                                                          //激活数(计费时间)
	Negative                                      string   `p:"negative" v:"negative@integer#减少此类作品数需为整数" dc:"减少此类作品数"`                                                                                        //减少此类作品数
	Report                                        string   `p:"report" v:"report@integer#举报数需为整数" dc:"举报数"`                                                                                                    //举报数
	Block                                         string   `p:"block" v:"block@integer#拉黑数需为整数" dc:"拉黑数"`                                                                                                      //拉黑数
	Comment                                       string   `p:"comment" v:"comment@integer#评论数需为整数" dc:"评论数"`                                                                                                  //评论数
	EventPayFirstDay                              string   `p:"eventPayFirstDay" v:"eventPayFirstDay@integer#首日付费次数需为整数" dc:"首日付费次数"`                                                                          //首日付费次数
	PlayedNum                                     string   `p:"playedNum" v:"playedNum@integer#素材曝光数需为整数" dc:"素材曝光数"`                                                                                          //素材曝光数
	PlayedThreeSeconds                            string   `p:"playedThreeSeconds" v:"playedThreeSeconds@integer#3s播放数需为整数" dc:"3s播放数"`                                                                        //3s播放数
	AdPhotoPlayed75Percent                        string   `p:"adPhotoPlayed75Percent" v:"adPhotoPlayed75Percent@integer#75%播放进度数需为整数" dc:"75%播放进度数"`                                                          //75%播放进度数
	PlayedEnd                                     string   `p:"playedEnd" v:"playedEnd@integer#完播数需为整数" dc:"完播数"`                                                                                              //完播数
	Follow                                        string   `p:"follow" v:"follow@integer#新增粉丝数需为整数" dc:"新增粉丝数"`                                                                                                //新增粉丝数
	EventNewUserPay                               string   `p:"eventNewUserPay" v:"eventNewUserPay@integer#新增付费人数需为整数" dc:"新增付费人数"`                                                                            //新增付费人数
	AdItemClick                                   string   `p:"adItemClick" v:"adItemClick@integer#行为数需为整数" dc:"行为数"`                                                                                          //行为数
	T7PaiedCnt                                    string   `p:"t7PaiedCnt" v:"t7PaiedCnt@integer#7日累计付费次数需为整数" dc:"7日累计付费次数"`                                                                                  //7日累计付费次数
	T7PaiedAmt                                    string   `p:"t7PaiedAmt" v:"t7PaiedAmt@float#7日累计付费金额需为浮点数" dc:"7日累计付费金额"`                                                                                   //7日累计付费金额
	ConversionNumByImpression7D                   string   `p:"conversionNumByImpression7D" v:"conversionNumByImpression7D@integer#转化数(计费时间)需为整数" dc:"转化数(计费时间)"`                                              //转化数(计费时间)
	DeepConversionNumByImpression7D               string   `p:"deepConversionNumByImpression7D" v:"deepConversionNumByImpression7D@integer#深度转化数(计费时间)需为整数" dc:"深度转化数(计费时间)"`                                  //深度转化数(计费时间)
	ConversionNum                                 string   `p:"conversionNum" v:"conversionNum@integer#转化数(回传时间)需为整数" dc:"转化数(回传时间)"`                                                                          //转化数(回传时间)
	DeepConversionNum                             string   `p:"deepConversionNum" v:"deepConversionNum@integer#深度转化数需为整数" dc:"深度转化数"`                                                                          //深度转化数
	T0PaiedCnt                                    string   `p:"t0PaiedCnt" v:"t0PaiedCnt@integer#当日累计付费次数需为整数" dc:"当日累计付费次数"`                                                                                  //当日累计付费次数
	T0PaiedAmt                                    string   `p:"t0PaiedAmt" v:"t0PaiedAmt@float#当日累计付费金额需为浮点数" dc:"当日累计付费金额"`                                                                                   //当日累计付费金额
	Play3SRatio                                   string   `p:"play3SRatio" v:"play3SRatio@float#3s播放率需为浮点数" dc:"3s播放率"`                                                                                       //3s播放率
	AdPhotoPlayed75PercentRatio                   string   `p:"adPhotoPlayed75PercentRatio" v:"adPhotoPlayed75PercentRatio@float#75%进度播放率需为浮点数" dc:"75%进度播放率"`                                                 //75%进度播放率
	T7PaiedRoi                                    string   `p:"t7PaiedRoi" v:"t7PaiedRoi@float#7日累计ROI需为浮点数" dc:"7日累计ROI"`                                                                                     //7日累计ROI
	T0PaiedRoi                                    string   `p:"t0PaiedRoi" v:"t0PaiedRoi@float#当日累计ROI需为浮点数" dc:"当日累计ROI"`                                                                                     //当日累计ROI
	PhotoClickRatio                               string   `p:"photoClickRatio" v:"photoClickRatio@float#封面点击率需为浮点数" dc:"封面点击率"`                                                                               //封面点击率
	EventPayCost                                  string   `p:"eventPayCost" v:"eventPayCost@float#付费次数成本需为浮点数" dc:"付费次数成本"`                                                                                   //付费次数成本
	EventPayRoi                                   string   `p:"eventPayRoi" v:"eventPayRoi@float#付费ROI需为浮点数" dc:"付费ROI"`                                                                                       //付费ROI
	EventAppInvokedCost                           string   `p:"eventAppInvokedCost" v:"eventAppInvokedCost@float#唤起应用成本需为浮点数" dc:"唤起应用成本"`                                                                     //唤起应用成本
	EventAppInvokedRatio                          string   `p:"eventAppInvokedRatio" v:"eventAppInvokedRatio@float#唤起应用率需为浮点数" dc:"唤起应用率"`                                                                     //唤起应用率
	ConversionCost                                string   `p:"conversionCost" v:"conversionCost@float#激活单价需为浮点数" dc:"激活单价"`                                                                                   //激活单价
	EventPayFirstDayRoi                           string   `p:"eventPayFirstDayRoi" v:"eventPayFirstDayRoi@float#激活当日ROI需为浮点数" dc:"激活当日ROI"`                                                                   //激活当日ROI
	EventPayPurchaseAmountOneDayByConversionRoi   string   `p:"eventPayPurchaseAmountOneDayByConversionRoi" v:"eventPayPurchaseAmountOneDayByConversionRoi@float#激活后24h-ROI(激活时间)需为浮点数" dc:"激活后24h-ROI(激活时间)"` //激活后24h-ROI(激活时间)
	EventPayPurchaseAmountThreeDayByConversionRoi string   `p:"eventPayPurchaseAmountThreeDayByConversionRoi" v:"eventPayPurchaseAmountThreeDayByConversionRoi@float#激活后3日ROI需为浮点数" dc:"激活后3日ROI"`             //激活后3日ROI
	EventPayPurchaseAmountWeekByConversionRoi     string   `p:"eventPayPurchaseAmountWeekByConversionRoi" v:"eventPayPurchaseAmountWeekByConversionRoi@float#激活后7日ROI需为浮点数" dc:"激活后7日ROI"`                     //激活后7日ROI
	PhotoClickCost                                string   `p:"photoClickCost" v:"photoClickCost@float#平均封面点击单价（元）需为浮点数" dc:"平均封面点击单价（元）"`                                                                     //平均封面点击单价（元）
	Impression1KCost                              string   `p:"impression1KCost" v:"impression1KCost@float#平均千次封面曝光花费（元）需为浮点数" dc:"平均千次封面曝光花费（元）"`                                                             //平均千次封面曝光花费（元）
	Click1KCost                                   string   `p:"click1KCost" v:"click1KCost@float#平均千次素材曝光花费（元）需为浮点数" dc:"平均千次素材曝光花费（元）"`                                                                       //平均千次素材曝光花费（元）
	ActionCost                                    string   `p:"actionCost" v:"actionCost@float#平均行为单价（元）需为浮点数" dc:"平均行为单价（元）"`                                                                                 //平均行为单价（元）
	DeepConversionCostByImpression7D              string   `p:"deepConversionCostByImpression7D" v:"deepConversionCostByImpression7D@float#深度转化成本(计费时间)，单位元需为浮点数" dc:"深度转化成本(计费时间)，单位元"`                       //深度转化成本(计费时间)，单位元
	DeepConversionRatioByImpression7D             string   `p:"deepConversionRatioByImpression7D" v:"deepConversionRatioByImpression7D@float#深度转化率(计费时间)需为浮点数" dc:"深度转化率(计费时间)"`                               //深度转化率(计费时间)
	EventPayFirstDayCost                          string   `p:"eventPayFirstDayCost" v:"eventPayFirstDayCost@float#首日付费次数成本，单位元需为浮点数" dc:"首日付费次数成本，单位元"`                                                       //首日付费次数成本，单位元
	ActionRatio                                   string   `p:"actionRatio" v:"actionRatio@float#素材点击率需为浮点数" dc:"素材点击率"`                                                                                       //素材点击率
	PlayEndRatio                                  string   `p:"playEndRatio" v:"playEndRatio@float#完播率需为浮点数" dc:"完播率"`                                                                                         //完播率
	EventNewUserPayCost                           string   `p:"eventNewUserPayCost" v:"eventNewUserPayCost@float#新增付费人数成本，单位元需为浮点数" dc:"新增付费人数成本，单位元"`                                                         //新增付费人数成本，单位元
	EventNewUserPayRatio                          string   `p:"eventNewUserPayRatio" v:"eventNewUserPayRatio@float#新增付费人数率需为浮点数" dc:"新增付费人数率"`                                                                 //新增付费人数率
	ActionNewRatio                                string   `p:"actionNewRatio" v:"actionNewRatio@float#行为率需为浮点数" dc:"行为率"`                                                                                     //行为率
	ConversionCostByImpression7D                  string   `p:"conversionCostByImpression7D" v:"conversionCostByImpression7D@float#转化成本(计费时间)，单位元需为浮点数" dc:"转化成本(计费时间)，单位元"`                                   //转化成本(计费时间)，单位元
	ConversionRatioByImpression7D                 string   `p:"conversionRatioByImpression7D" v:"conversionRatioByImpression7D@float#转化率(计费时间)需为浮点数" dc:"转化率(计费时间)"`                                           //转化率(计费时间)
	Date                                          string   `p:"date" v:"date@datetime#日期，格式：yyyy-MM-dd HH:mm:ss需为YYYY-MM-DD hh:mm:ss格式" dc:"日期，格式：yyyy-MM-dd HH:mm:ss"`                                        //日期，格式：yyyy-MM-dd HH:mm:ss
	KeyAction                                     string   `p:"keyAction" v:"keyAction@integer#关键行为数需为整数" dc:"关键行为数"`                                                                                          //关键行为数
	AdPhotoPlayed75PercentRatio2                  string   `p:"adPhotoPlayed75PercentRatio" v:"adPhotoPlayed75PercentRatio@float#75%进度播放数需为浮点数" dc:"75%进度播放数"`                                                 //75%进度播放数
	AccountId                                     string   `p:"accountId" v:"accountId@integer#账号ID需为整数" dc:"账号ID"`                                                                                            //账号ID
	//SeriesId                                      string `p:"seriesId" v:"seriesId@integer#短剧ID需为整数" dc:"短剧ID"`                                                                                              //短剧ID
	SeriesIds                 []int64 `p:"seriesIds"  dc:"短剧IDs"`                                                                               //短剧IDs
	RechargeRate              string  `p:"rechargeRate" v:"rechargeRate@float#充值几率需为浮点数" dc:"充值几率"`                                             //充值几率
	MiniGameIaaRoi            string  `p:"miniGameIaaRoi" v:"miniGameIaaRoi@float#IAA广告变现ROI需为浮点数" dc:"IAA广告变现ROI"`                             //IAA广告变现ROI
	MiniGameIaaPurchaseAmount string  `p:"miniGameIaaPurchaseAmount" v:"miniGameIaaPurchaseAmount@float#IAA广告变现LTV（元）需为浮点数" dc:"IAA广告变现LTV（元）"` //IAA广告变现LTV（元）
	CreateTime                string  `p:"createTime" dc:"统计日期"`                                                                                //统计日期
	StartDate                 string  `p:"startDate" dc:"开始时间"`
	EndDate                   string  `p:"endDate" dc:"结束时间"`
}

// KsAdReportStatsSearchRes 列表返回结果
type KsAdReportStatsSearchRes struct {
	comModel.ListRes
	List    []*KsAdReportStatsListRes `json:"list"`
	Summary *KsAdReportSummaryDataRes `json:"summary"`
}

type KsAdReportSummaryDataRes struct {
	Likes                                      int     `json:"likes"  dc:"点赞数"`
	Share                                      int     `json:"share"  dc:"分享数"`
	PhotoClick                                 int     `json:"photoClick"  dc:"封面点击数"`
	Impression                                 int     `json:"impression"  dc:"封面曝光数"`
	EventPay                                   int     `json:"eventPay"  dc:"付费次数"`
	T0DirectPaiedCnt                           int     `json:"t0DirectPaiedCnt"  dc:"付费次数(计费时间)"`
	EventPayPurchaseAmount                     float64 `json:"eventPayPurchaseAmount"  dc:"付费金额"`
	T0DirectPaiedAmt                           float64 `json:"t0DirectPaiedAmt"  dc:"付费金额(计费时间)"`
	AdShow                                     int     `json:"adShow"  dc:"广告曝光"`
	TotalCharge                                float64 `json:"totalCharge"  dc:"消耗"`
	EventAppInvoked                            int     `json:"eventAppInvoked"  dc:"唤起应用数"`
	EventPayPurchaseAmountFirstDay             float64 `json:"eventPayPurchaseAmountFirstDay"  dc:"激活当日付费金额"`
	EventPayPurchaseAmountOneDayByConversion   float64 `json:"eventPayPurchaseAmountOneDayByConversion"  dc:"激活后24h付费金额(激活时间)"`
	EventPayPurchaseAmountWeekByConversion     float64 `json:"eventPayPurchaseAmountWeekByConversion"  dc:"激活后七日付费金额"`
	EventPayPurchaseAmountThreeDayByConversion float64 `json:"eventPayPurchaseAmountThreeDayByConversion"  dc:"激活后三日付费金额"`
	Conversion                                 int     `json:"conversion"  dc:"激活数"`
	T0DirectConversionCnt                      int     `json:"t0DirectConversionCnt"  dc:"激活数(计费时间)"`
	Negative                                   int     `json:"negative"  dc:"减少此类作品数"`
	Report                                     int     `json:"report"  dc:"举报数"`
	Block                                      int     `json:"block"  dc:"拉黑数"`
	Comment                                    int     `json:"comment"  dc:"评论数"`
	EventPayFirstDay                           int     `json:"eventPayFirstDay"  dc:"首日付费次数"`
	PlayedNum                                  int     `json:"playedNum"  dc:"素材曝光数"`
	PlayedThreeSeconds                         int     `json:"playedThreeSeconds"  dc:"3s播放数"`
	AdPhotoPlayed75Percent                     int     `json:"adPhotoPlayed75Percent"  dc:"75%播放进度数"`
	PlayedEnd                                  int     `json:"playedEnd"  dc:"完播数"`
	Follow                                     int     `json:"follow"  dc:"新增粉丝数"`
	EventNewUserPay                            int     `json:"eventNewUserPay"  dc:"新增付费人数"`
	AdItemClick                                int     `json:"adItemClick"  dc:"行为数"`
	T7PaiedCnt                                 int     `json:"t7PaiedCnt"  dc:"7日累计付费次数"`
	T7PaiedAmt                                 float64 `json:"t7PaiedAmt"  dc:"7日累计付费金额"`
	ConversionNumByImpression7D                int     `json:"conversionNumByImpression7D"  dc:"转化数(计费时间)"`
	DeepConversionNumByImpression7D            int     `json:"deepConversionNumByImpression7D"  dc:"深度转化数(计费时间)"`
	ConversionNum                              int     `json:"conversionNum"  dc:"转化数(回传时间)"`
	DeepConversionNum                          int     `json:"deepConversionNum"  dc:"深度转化数"`
	T0PaiedCnt                                 int     `json:"t0PaiedCnt"  dc:"当日累计付费次数"`
	T0PaiedAmt                                 float64 `json:"t0PaiedAmt"  dc:"当日累计付费金额"`
	//EventPayCost                               float64 `json:"eventPayCost"  dc:"付费次数成本"`
	//EventAppInvokedCost float64 `json:"eventAppInvokedCost"  dc:"唤起应用成本"`
	//ConversionCost                   float64 `json:"conversionCost"  dc:"激活单价"`
	DeepConversionCostByImpression7D float64 `json:"deepConversionCostByImpression7D"  dc:"深度转化成本(计费时间)，单位元"`
	//EventPayFirstDayCost             float64 `json:"eventPayFirstDayCost"  dc:"首日付费次数成本，单位元"`
	//EventNewUserPayCost              float64 `json:"eventNewUserPayCost"  dc:"新增付费人数成本，单位元"`
	ConversionCostByImpression7D float64 `json:"conversionCostByImpression7D"  dc:"转化成本(计费时间)，单位元"`
	KeyAction                    int64   `json:"keyAction"  dc:"关键行为数"`
	AdPhotoPlayed75PercentRatio2 float64 `json:"adPhotoPlayed75PercentRatio2"  dc:"75%进度播放数"`
	RechargeRate                 float64 `json:"rechargeRate"  dc:"充值几率"`
	MiniGameIaaPurchaseAmount    float64 `json:"miniGameIaaPurchaseAmount"  dc:"IAA广告变现LTV（元）"`
}

// KsAdReportStatsAddReq 添加操作请求参数
type KsAdReportStatsAddReq struct {
	AdvertiserId                                  int64       `p:"advertiserId" v:"required#主键ID不能为空" dc:"账户ID"`
	Likes                                         int         `p:"likes"  dc:"点赞数"`
	Share                                         int         `p:"share"  dc:"分享数"`
	PhotoClick                                    int         `p:"photoClick"  dc:"封面点击数"`
	Impression                                    int         `p:"impression"  dc:"封面曝光数"`
	EventPay                                      int         `p:"eventPay"  dc:"付费次数"`
	T0DirectPaiedCnt                              int         `p:"t0DirectPaiedCnt"  dc:"付费次数(计费时间)"`
	EventPayPurchaseAmount                        float64     `p:"eventPayPurchaseAmount"  dc:"付费金额"`
	T0DirectPaiedAmt                              float64     `p:"t0DirectPaiedAmt"  dc:"付费金额(计费时间)"`
	AdShow                                        int         `p:"adShow"  dc:"广告曝光"`
	TotalCharge                                   float64     `p:"totalCharge"  dc:"消耗"`
	EventAppInvoked                               int         `p:"eventAppInvoked"  dc:"唤起应用数"`
	EventPayPurchaseAmountFirstDay                float64     `p:"eventPayPurchaseAmountFirstDay"  dc:"激活当日付费金额"`
	EventPayPurchaseAmountOneDayByConversion      float64     `p:"eventPayPurchaseAmountOneDayByConversion"  dc:"激活后24h付费金额(激活时间)"`
	EventPayPurchaseAmountWeekByConversion        float64     `p:"eventPayPurchaseAmountWeekByConversion"  dc:"激活后七日付费金额"`
	EventPayPurchaseAmountThreeDayByConversion    float64     `p:"eventPayPurchaseAmountThreeDayByConversion"  dc:"激活后三日付费金额"`
	Conversion                                    int         `p:"conversion"  dc:"激活数"`
	T0DirectConversionCnt                         int         `p:"t0DirectConversionCnt"  dc:"激活数(计费时间)"`
	Negative                                      int         `p:"negative"  dc:"减少此类作品数"`
	Report                                        int         `p:"report"  dc:"举报数"`
	Block                                         int         `p:"block"  dc:"拉黑数"`
	Comment                                       int         `p:"comment"  dc:"评论数"`
	EventPayFirstDay                              int         `p:"eventPayFirstDay"  dc:"首日付费次数"`
	PlayedNum                                     int         `p:"playedNum"  dc:"素材曝光数"`
	PlayedThreeSeconds                            int         `p:"playedThreeSeconds"  dc:"3s播放数"`
	AdPhotoPlayed75Percent                        int         `p:"adPhotoPlayed75Percent"  dc:"75%播放进度数"`
	PlayedEnd                                     int         `p:"playedEnd"  dc:"完播数"`
	Follow                                        int         `p:"follow"  dc:"新增粉丝数"`
	EventNewUserPay                               int         `p:"eventNewUserPay"  dc:"新增付费人数"`
	AdItemClick                                   int         `p:"adItemClick"  dc:"行为数"`
	T7PaiedCnt                                    int         `p:"t7PaiedCnt"  dc:"7日累计付费次数"`
	T7PaiedAmt                                    float64     `p:"t7PaiedAmt"  dc:"7日累计付费金额"`
	ConversionNumByImpression7D                   int         `p:"conversionNumByImpression7D"  dc:"转化数(计费时间)"`
	DeepConversionNumByImpression7D               int         `p:"deepConversionNumByImpression7D"  dc:"深度转化数(计费时间)"`
	ConversionNum                                 int         `p:"conversionNum"  dc:"转化数(回传时间)"`
	DeepConversionNum                             int         `p:"deepConversionNum"  dc:"深度转化数"`
	T0PaiedCnt                                    int         `p:"t0PaiedCnt"  dc:"当日累计付费次数"`
	T0PaiedAmt                                    float64     `p:"t0PaiedAmt"  dc:"当日累计付费金额"`
	Play3SRatio                                   float64     `p:"play3SRatio"  dc:"3s播放率"`
	AdPhotoPlayed75PercentRatio                   float64     `p:"adPhotoPlayed75PercentRatio"  dc:"75%进度播放率"`
	T7PaiedRoi                                    float64     `p:"t7PaiedRoi"  dc:"7日累计ROI"`
	T0PaiedRoi                                    float64     `p:"t0PaiedRoi"  dc:"当日累计ROI"`
	PhotoClickRatio                               float64     `p:"photoClickRatio"  dc:"封面点击率"`
	EventPayCost                                  float64     `p:"eventPayCost"  dc:"付费次数成本"`
	EventPayRoi                                   float64     `p:"eventPayRoi"  dc:"付费ROI"`
	EventAppInvokedCost                           float64     `p:"eventAppInvokedCost"  dc:"唤起应用成本"`
	EventAppInvokedRatio                          float64     `p:"eventAppInvokedRatio"  dc:"唤起应用率"`
	ConversionCost                                float64     `p:"conversionCost"  dc:"激活单价"`
	EventPayFirstDayRoi                           float64     `p:"eventPayFirstDayRoi"  dc:"激活当日ROI"`
	EventPayPurchaseAmountOneDayByConversionRoi   float64     `p:"eventPayPurchaseAmountOneDayByConversionRoi"  dc:"激活后24h-ROI(激活时间)"`
	EventPayPurchaseAmountThreeDayByConversionRoi float64     `p:"eventPayPurchaseAmountThreeDayByConversionRoi"  dc:"激活后3日ROI"`
	EventPayPurchaseAmountWeekByConversionRoi     float64     `p:"eventPayPurchaseAmountWeekByConversionRoi"  dc:"激活后7日ROI"`
	PhotoClickCost                                float64     `p:"photoClickCost"  dc:"平均封面点击单价（元）"`
	Impression1KCost                              float64     `p:"impression1KCost"  dc:"平均千次封面曝光花费（元）"`
	Click1KCost                                   float64     `p:"click1KCost"  dc:"平均千次素材曝光花费（元）"`
	ActionCost                                    float64     `p:"actionCost"  dc:"平均行为单价（元）"`
	DeepConversionCostByImpression7D              float64     `p:"deepConversionCostByImpression7D"  dc:"深度转化成本(计费时间)，单位元"`
	DeepConversionRatioByImpression7D             float64     `p:"deepConversionRatioByImpression7D"  dc:"深度转化率(计费时间)"`
	EventPayFirstDayCost                          float64     `p:"eventPayFirstDayCost"  dc:"首日付费次数成本，单位元"`
	ActionRatio                                   float64     `p:"actionRatio"  dc:"素材点击率"`
	PlayEndRatio                                  float64     `p:"playEndRatio"  dc:"完播率"`
	EventNewUserPayCost                           float64     `p:"eventNewUserPayCost"  dc:"新增付费人数成本，单位元"`
	EventNewUserPayRatio                          float64     `p:"eventNewUserPayRatio"  dc:"新增付费人数率"`
	ActionNewRatio                                float64     `p:"actionNewRatio"  dc:"行为率"`
	ConversionCostByImpression7D                  float64     `p:"conversionCostByImpression7D"  dc:"转化成本(计费时间)，单位元"`
	ConversionRatioByImpression7D                 float64     `p:"conversionRatioByImpression7D"  dc:"转化率(计费时间)"`
	Date                                          *gtime.Time `p:"date"  dc:"日期，格式：yyyy-MM-dd HH:mm:ss"`
	KeyAction                                     int64       `p:"keyAction"  dc:"关键行为数"`
	AdPhotoPlayed75PercentRatio2                  float64     `p:"adPhotoPlayed75PercentRatio2"  dc:"75%进度播放数"`
	AccountId                                     int64       `p:"accountId"  dc:"账号ID"`
	SeriesId                                      int64       `p:"seriesId"  dc:"短剧ID"`
	RechargeRate                                  float64     `p:"rechargeRate"  dc:"充值几率"`
	MiniGameIaaRoi                                float64     `p:"miniGameIaaRoi"  dc:"IAA广告变现ROI"`
	MiniGameIaaPurchaseAmount                     float64     `p:"miniGameIaaPurchaseAmount"  dc:"IAA广告变现LTV（元）"`
	CreateTime                                    string      `p:"createTime" v:"required#统计日期不能为空" dc:"统计日期"`
}

// KsAdReportStatsEditReq 修改操作请求参数
type KsAdReportStatsEditReq struct {
	AdvertiserId                                  int64       `p:"advertiserId" v:"required#主键ID不能为空" dc:"账户ID"`
	Likes                                         int         `p:"likes"  dc:"点赞数"`
	Share                                         int         `p:"share"  dc:"分享数"`
	PhotoClick                                    int         `p:"photoClick"  dc:"封面点击数"`
	Impression                                    int         `p:"impression"  dc:"封面曝光数"`
	EventPay                                      int         `p:"eventPay"  dc:"付费次数"`
	T0DirectPaiedCnt                              int         `p:"t0DirectPaiedCnt"  dc:"付费次数(计费时间)"`
	EventPayPurchaseAmount                        float64     `p:"eventPayPurchaseAmount"  dc:"付费金额"`
	T0DirectPaiedAmt                              float64     `p:"t0DirectPaiedAmt"  dc:"付费金额(计费时间)"`
	AdShow                                        int         `p:"adShow"  dc:"广告曝光"`
	TotalCharge                                   float64     `p:"totalCharge"  dc:"消耗"`
	EventAppInvoked                               int         `p:"eventAppInvoked"  dc:"唤起应用数"`
	EventPayPurchaseAmountFirstDay                float64     `p:"eventPayPurchaseAmountFirstDay"  dc:"激活当日付费金额"`
	EventPayPurchaseAmountOneDayByConversion      float64     `p:"eventPayPurchaseAmountOneDayByConversion"  dc:"激活后24h付费金额(激活时间)"`
	EventPayPurchaseAmountWeekByConversion        float64     `p:"eventPayPurchaseAmountWeekByConversion"  dc:"激活后七日付费金额"`
	EventPayPurchaseAmountThreeDayByConversion    float64     `p:"eventPayPurchaseAmountThreeDayByConversion"  dc:"激活后三日付费金额"`
	Conversion                                    int         `p:"conversion"  dc:"激活数"`
	T0DirectConversionCnt                         int         `p:"t0DirectConversionCnt"  dc:"激活数(计费时间)"`
	Negative                                      int         `p:"negative"  dc:"减少此类作品数"`
	Report                                        int         `p:"report"  dc:"举报数"`
	Block                                         int         `p:"block"  dc:"拉黑数"`
	Comment                                       int         `p:"comment"  dc:"评论数"`
	EventPayFirstDay                              int         `p:"eventPayFirstDay"  dc:"首日付费次数"`
	PlayedNum                                     int         `p:"playedNum"  dc:"素材曝光数"`
	PlayedThreeSeconds                            int         `p:"playedThreeSeconds"  dc:"3s播放数"`
	AdPhotoPlayed75Percent                        int         `p:"adPhotoPlayed75Percent"  dc:"75%播放进度数"`
	PlayedEnd                                     int         `p:"playedEnd"  dc:"完播数"`
	Follow                                        int         `p:"follow"  dc:"新增粉丝数"`
	EventNewUserPay                               int         `p:"eventNewUserPay"  dc:"新增付费人数"`
	AdItemClick                                   int         `p:"adItemClick"  dc:"行为数"`
	T7PaiedCnt                                    int         `p:"t7PaiedCnt"  dc:"7日累计付费次数"`
	T7PaiedAmt                                    float64     `p:"t7PaiedAmt"  dc:"7日累计付费金额"`
	ConversionNumByImpression7D                   int         `p:"conversionNumByImpression7D"  dc:"转化数(计费时间)"`
	DeepConversionNumByImpression7D               int         `p:"deepConversionNumByImpression7D"  dc:"深度转化数(计费时间)"`
	ConversionNum                                 int         `p:"conversionNum"  dc:"转化数(回传时间)"`
	DeepConversionNum                             int         `p:"deepConversionNum"  dc:"深度转化数"`
	T0PaiedCnt                                    int         `p:"t0PaiedCnt"  dc:"当日累计付费次数"`
	T0PaiedAmt                                    float64     `p:"t0PaiedAmt"  dc:"当日累计付费金额"`
	Play3SRatio                                   float64     `p:"play3SRatio"  dc:"3s播放率"`
	AdPhotoPlayed75PercentRatio                   float64     `p:"adPhotoPlayed75PercentRatio"  dc:"75%进度播放率"`
	T7PaiedRoi                                    float64     `p:"t7PaiedRoi"  dc:"7日累计ROI"`
	T0PaiedRoi                                    float64     `p:"t0PaiedRoi"  dc:"当日累计ROI"`
	PhotoClickRatio                               float64     `p:"photoClickRatio"  dc:"封面点击率"`
	EventPayCost                                  float64     `p:"eventPayCost"  dc:"付费次数成本"`
	EventPayRoi                                   float64     `p:"eventPayRoi"  dc:"付费ROI"`
	EventAppInvokedCost                           float64     `p:"eventAppInvokedCost"  dc:"唤起应用成本"`
	EventAppInvokedRatio                          float64     `p:"eventAppInvokedRatio"  dc:"唤起应用率"`
	ConversionCost                                float64     `p:"conversionCost"  dc:"激活单价"`
	EventPayFirstDayRoi                           float64     `p:"eventPayFirstDayRoi"  dc:"激活当日ROI"`
	EventPayPurchaseAmountOneDayByConversionRoi   float64     `p:"eventPayPurchaseAmountOneDayByConversionRoi"  dc:"激活后24h-ROI(激活时间)"`
	EventPayPurchaseAmountThreeDayByConversionRoi float64     `p:"eventPayPurchaseAmountThreeDayByConversionRoi"  dc:"激活后3日ROI"`
	EventPayPurchaseAmountWeekByConversionRoi     float64     `p:"eventPayPurchaseAmountWeekByConversionRoi"  dc:"激活后7日ROI"`
	PhotoClickCost                                float64     `p:"photoClickCost"  dc:"平均封面点击单价（元）"`
	Impression1KCost                              float64     `p:"impression1KCost"  dc:"平均千次封面曝光花费（元）"`
	Click1KCost                                   float64     `p:"click1KCost"  dc:"平均千次素材曝光花费（元）"`
	ActionCost                                    float64     `p:"actionCost"  dc:"平均行为单价（元）"`
	DeepConversionCostByImpression7D              float64     `p:"deepConversionCostByImpression7D"  dc:"深度转化成本(计费时间)，单位元"`
	DeepConversionRatioByImpression7D             float64     `p:"deepConversionRatioByImpression7D"  dc:"深度转化率(计费时间)"`
	EventPayFirstDayCost                          float64     `p:"eventPayFirstDayCost"  dc:"首日付费次数成本，单位元"`
	ActionRatio                                   float64     `p:"actionRatio"  dc:"素材点击率"`
	PlayEndRatio                                  float64     `p:"playEndRatio"  dc:"完播率"`
	EventNewUserPayCost                           float64     `p:"eventNewUserPayCost"  dc:"新增付费人数成本，单位元"`
	EventNewUserPayRatio                          float64     `p:"eventNewUserPayRatio"  dc:"新增付费人数率"`
	ActionNewRatio                                float64     `p:"actionNewRatio"  dc:"行为率"`
	ConversionCostByImpression7D                  float64     `p:"conversionCostByImpression7D"  dc:"转化成本(计费时间)，单位元"`
	ConversionRatioByImpression7D                 float64     `p:"conversionRatioByImpression7D"  dc:"转化率(计费时间)"`
	Date                                          *gtime.Time `p:"date"  dc:"日期，格式：yyyy-MM-dd HH:mm:ss"`
	KeyAction                                     int64       `p:"keyAction"  dc:"关键行为数"`
	AdPhotoPlayed75PercentRatio2                  float64     `p:"adPhotoPlayed75PercentRatio2"  dc:"75%进度播放数"`
	AccountId                                     int64       `p:"accountId"  dc:"账号ID"`
	SeriesId                                      int64       `p:"seriesId"  dc:"短剧ID"`
	RechargeRate                                  float64     `p:"rechargeRate"  dc:"充值几率"`
	MiniGameIaaRoi                                float64     `p:"miniGameIaaRoi"  dc:"IAA广告变现ROI"`
	MiniGameIaaPurchaseAmount                     float64     `p:"miniGameIaaPurchaseAmount"  dc:"IAA广告变现LTV（元）"`
	CreateTime                                    string      `p:"createTime" v:"required#统计日期不能为空" dc:"统计日期"`
}
