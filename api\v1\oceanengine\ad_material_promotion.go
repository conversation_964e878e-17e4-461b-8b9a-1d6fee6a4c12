// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-02-21 16:22:39
// 生成路径: api/v1/oceanengine/ad_material_promotion.go
// 生成人：cyao
// desc:素材广告关联表格相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdMaterialPromotionSearchReq 分页请求参数
type AdMaterialPromotionSearchReq struct {
	g.Meta `path:"/list" tags:"素材广告关联表格" method:"get" summary:"素材广告关联表格列表"`
	commonApi.Author
	model.AdMaterialPromotionSearchReq
}

// AdMaterialPromotionSearchRes 列表返回结果
type AdMaterialPromotionSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialPromotionSearchRes
}

// AdMaterialPromotionAddReq 添加操作请求参数
type AdMaterialPromotionAddReq struct {
	g.Meta `path:"/add" tags:"素材广告关联表格" method:"post" summary:"素材广告关联表格添加"`
	commonApi.Author
	*model.AdMaterialPromotionAddReq
}

// AdMaterialPromotionAddRes 添加操作返回结果
type AdMaterialPromotionAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialPromotionEditReq 修改操作请求参数
type AdMaterialPromotionEditReq struct {
	g.Meta `path:"/edit" tags:"素材广告关联表格" method:"put" summary:"素材广告关联表格修改"`
	commonApi.Author
	*model.AdMaterialPromotionEditReq
}

// AdMaterialPromotionEditRes 修改操作返回结果
type AdMaterialPromotionEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialPromotionGetReq 获取一条数据请求
type AdMaterialPromotionGetReq struct {
	g.Meta `path:"/get" tags:"素材广告关联表格" method:"get" summary:"获取素材广告关联表格信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdMaterialPromotionGetRes 获取一条数据结果
type AdMaterialPromotionGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialPromotionInfoRes
}

// AdMaterialPromotionDeleteReq 删除数据请求
type AdMaterialPromotionDeleteReq struct {
	g.Meta `path:"/delete" tags:"素材广告关联表格" method:"delete" summary:"删除素材广告关联表格"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdMaterialPromotionDeleteRes 删除数据返回
type AdMaterialPromotionDeleteRes struct {
	commonApi.EmptyRes
}
