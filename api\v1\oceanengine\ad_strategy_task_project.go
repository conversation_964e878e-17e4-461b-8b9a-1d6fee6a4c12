// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:12
// 生成路径: api/v1/oceanengine/ad_strategy_task_project.go
// 生成人：gfast
// desc:巨量广告搭建-任务-项目相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyTaskProjectSearchReq 分页请求参数
type AdStrategyTaskProjectSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-任务-项目" method:"get" summary:"巨量广告搭建-任务-项目列表"`
	commonApi.Author
	model.AdStrategyTaskProjectSearchReq
}

// AdStrategyTaskProjectSearchRes 列表返回结果
type AdStrategyTaskProjectSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTaskProjectSearchRes
}

// AdStrategyTaskProjectAddReq 添加操作请求参数
type AdStrategyTaskProjectAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-任务-项目" method:"post" summary:"巨量广告搭建-任务-项目添加"`
	commonApi.Author
	*model.AdStrategyTaskProjectAddReq
}

// AdStrategyTaskProjectAddRes 添加操作返回结果
type AdStrategyTaskProjectAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyTaskProjectEditReq 修改操作请求参数
type AdStrategyTaskProjectEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-任务-项目" method:"put" summary:"巨量广告搭建-任务-项目修改"`
	commonApi.Author
	*model.AdStrategyTaskProjectEditReq
}

// AdStrategyTaskProjectEditRes 修改操作返回结果
type AdStrategyTaskProjectEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyTaskProjectGetReq 获取一条数据请求
type AdStrategyTaskProjectGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-任务-项目" method:"get" summary:"获取巨量广告搭建-任务-项目信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdStrategyTaskProjectGetRes 获取一条数据结果
type AdStrategyTaskProjectGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyTaskProjectInfoRes
}

// AdStrategyTaskProjectDeleteReq 删除数据请求
type AdStrategyTaskProjectDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-任务-项目" method:"delete" summary:"删除巨量广告搭建-任务-项目"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdStrategyTaskProjectDeleteRes 删除数据返回
type AdStrategyTaskProjectDeleteRes struct {
	commonApi.EmptyRes
}
