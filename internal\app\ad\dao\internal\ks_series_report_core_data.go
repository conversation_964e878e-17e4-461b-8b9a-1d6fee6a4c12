// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-10 14:40:38
// 生成路径: internal/app/ad/dao/internal/ks_series_report_core_data.go
// 生成人：cyao
// desc:短剧核心总览数据报表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsSeriesReportCoreDataDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsSeriesReportCoreDataDao struct {
	table   string                        // Table is the underlying table name of the DAO.
	group   string                        // Group is the database configuration group name of current DAO.
	columns KsSeriesReportCoreDataColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsSeriesReportCoreDataColumns defines and stores column names for table ks_series_report_core_data.
type KsSeriesReportCoreDataColumns struct {
	Id                               string // id自增
	AdvertiserId                     string // 账户ID
	SeriesId                         string // 短剧id
	TotalCharge                      string // 消耗
	AccuFansUserNum                  string // 粉丝峰值量
	FansUserNum                      string // 粉丝净增量
	EventPayRoi                      string // 商业化ROI
	EventPayRoiAll                   string // 全域ROI
	PayUserCount                     string // 付费人数
	PayCount                         string // 付费订单数
	PayAmt                           string // 付费金额
	IsFansUser                       string // 是否粉丝
	DisplayPlayCnt                   string // 播放数
	DisplayLikeCnt                   string // 点赞数
	DisplayCommentCnt                string // 评论数
	DisplayCollectCnt                string // 收藏数
	MiniGameIaaRoi                   string // IAA广告变现ROI（含返货）
	MiniGameIaaPurchaseAmount        string // IAA广告变现LTV（含返货，元）
	MiniGameIaaPurchaseAmountDivided string // IAA广告变现LTV（不含返货，元）
	MiniGameIaaDividedRoi            string // IAA广告变现ROI（不含返货）
	Date                             string // 日期（yyyy-MM-dd hh:mm:ss）
}

var ksSeriesReportCoreDataColumns = KsSeriesReportCoreDataColumns{
	Id:                               "id",
	AdvertiserId:                     "advertiser_id",
	SeriesId:                         "series_id",
	TotalCharge:                      "total_charge",
	AccuFansUserNum:                  "accu_fans_user_num",
	FansUserNum:                      "fans_user_num",
	EventPayRoi:                      "event_pay_roi",
	EventPayRoiAll:                   "event_pay_roi_all",
	PayUserCount:                     "pay_user_count",
	PayCount:                         "pay_count",
	PayAmt:                           "pay_amt",
	IsFansUser:                       "is_fans_user",
	DisplayPlayCnt:                   "display_play_cnt",
	DisplayLikeCnt:                   "display_like_cnt",
	DisplayCommentCnt:                "display_comment_cnt",
	DisplayCollectCnt:                "display_collect_cnt",
	MiniGameIaaRoi:                   "mini_game_iaa_roi",
	MiniGameIaaPurchaseAmount:        "mini_game_iaa_purchase_amount",
	MiniGameIaaPurchaseAmountDivided: "mini_game_iaa_purchase_amount_divided",
	MiniGameIaaDividedRoi:            "mini_game_iaa_divided_roi",
	Date:                             "date",
}

// NewKsSeriesReportCoreDataDao creates and returns a new DAO object for table data access.
func NewKsSeriesReportCoreDataDao() *KsSeriesReportCoreDataDao {
	return &KsSeriesReportCoreDataDao{
		group:   "default",
		table:   "ks_series_report_core_data",
		columns: ksSeriesReportCoreDataColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsSeriesReportCoreDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsSeriesReportCoreDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsSeriesReportCoreDataDao) Columns() KsSeriesReportCoreDataColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsSeriesReportCoreDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsSeriesReportCoreDataDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsSeriesReportCoreDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
