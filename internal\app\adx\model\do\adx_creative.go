// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:13
// 生成路径: internal/app/adx/model/entity/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxCreative is the golang structure for table adx_creative.
type AdxCreative struct {
	gmeta.Meta    `orm:"table:adx_creative, do:true"`
	CreativeId    interface{} `orm:"creative_id,primary" json:"creativeId"` // 计划ID
	FirstSeen     interface{} `orm:"first_seen" json:"firstSeen"`           // 首次出现日期
	LastSeen      interface{} `orm:"last_seen" json:"lastSeen"`             // 最后出现日期
	MaterialId    interface{} `orm:"material_id" json:"materialId"`         // 素材ID
	MediaId       interface{} `orm:"media_id" json:"mediaId"`               // 媒体ID
	MobileType    interface{} `orm:"mobile_type" json:"mobileType"`         // 投放平台1.安卓2.苹果
	NumDate       interface{} `orm:"num_date" json:"numDate"`               // 投放日期及次数
	PositionId    interface{} `orm:"position_id" json:"positionId"`         // 广告位ID
	ProductId     interface{} `orm:"product_id" json:"productId"`           // 投放产品ID
	PublisherId   interface{} `orm:"publisher_id" json:"publisherId"`       // 公司ID
	TalentAccount interface{} `orm:"talent_account" json:"talentAccount"`   // 达人账号
	TalentName    interface{} `orm:"talent_name" json:"talentName"`         // 达人名称
	TargetUrl     interface{} `orm:"target_url" json:"targetUrl"`           // 落地页
	TargetUrlId   interface{} `orm:"target_url_id" json:"targetUrlId"`      // 落地页ID
	Title1        interface{} `orm:"title1" json:"title1"`                  // 使用的标题
	Title2        interface{} `orm:"title2" json:"title2"`                  // 使用的文案
}
