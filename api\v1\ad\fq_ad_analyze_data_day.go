// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-16 15:29:39
// 生成路径: api/v1/ad/fq_ad_analyze_data_day.go
// 生成人：gfast
// desc: 获取回本统计-分天数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdAnalyzeDataDaySearchReq 分页请求参数
type FqAdAnalyzeDataDaySearchReq struct {
	g.Meta `path:"/list" tags:" 获取回本统计-分天数据" method:"post" summary:" 获取回本统计-分天数据列表"`
	commonApi.Author
	model.FqAdAnalyzeDataDaySearchReq
}

// FqAdAnalyzeDataDaySearchRes 列表返回结果
type FqAdAnalyzeDataDaySearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAnalyzeDataDaySearchRes
}

// FqAdAnalyzeDataDayExportReq 导出请求
type FqAdAnalyzeDataDayExportReq struct {
	g.Meta `path:"/export" tags:" 获取回本统计-分天数据" method:"get" summary:" 获取回本统计-分天数据导出"`
	commonApi.Author
	model.FqAdAnalyzeDataDaySearchReq
}

// FqAdAnalyzeDataDayExportRes 导出响应
type FqAdAnalyzeDataDayExportRes struct {
	commonApi.EmptyRes
}

// FqAdAnalyzeDataDayAddReq 添加操作请求参数
type FqAdAnalyzeDataDayAddReq struct {
	g.Meta `path:"/add" tags:" 获取回本统计-分天数据" method:"post" summary:" 获取回本统计-分天数据添加"`
	commonApi.Author
	*model.FqAdAnalyzeDataDayAddReq
}

// FqAdAnalyzeDataDayAddRes 添加操作返回结果
type FqAdAnalyzeDataDayAddRes struct {
	commonApi.EmptyRes
}

// FqAdAnalyzeDataDayEditReq 修改操作请求参数
type FqAdAnalyzeDataDayEditReq struct {
	g.Meta `path:"/edit" tags:" 获取回本统计-分天数据" method:"put" summary:" 获取回本统计-分天数据修改"`
	commonApi.Author
	*model.FqAdAnalyzeDataDayEditReq
}

// FqAdAnalyzeDataDayEditRes 修改操作返回结果
type FqAdAnalyzeDataDayEditRes struct {
	commonApi.EmptyRes
}

// FqAdAnalyzeDataDayGetReq 获取一条数据请求
type FqAdAnalyzeDataDayGetReq struct {
	g.Meta `path:"/get" tags:" 获取回本统计-分天数据" method:"get" summary:"获取 获取回本统计-分天数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// FqAdAnalyzeDataDayGetRes 获取一条数据结果
type FqAdAnalyzeDataDayGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAnalyzeDataDayInfoRes
}

// FqAdAnalyzeDataDayDeleteReq 删除数据请求
type FqAdAnalyzeDataDayDeleteReq struct {
	g.Meta `path:"/delete" tags:" 获取回本统计-分天数据" method:"delete" summary:"删除 获取回本统计-分天数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// FqAdAnalyzeDataDayDeleteRes 删除数据返回
type FqAdAnalyzeDataDayDeleteRes struct {
	commonApi.EmptyRes
}
