/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsUnionFlowPackageReportV2ResponseDataListInner struct for ToolsUnionFlowPackageReportV2ResponseDataListInner
type ToolsUnionFlowPackageReportV2ResponseDataListInner struct {
	//
	Active *float64 `json:"active,omitempty"`
	//
	ActiveCost *float64 `json:"active_cost,omitempty"`
	//
	ActivePay *float64 `json:"active_pay,omitempty"`
	//
	ActivePayCost *float64 `json:"active_pay_cost,omitempty"`
	//
	ActivePayRate *float64 `json:"active_pay_rate,omitempty"`
	//
	ActiveRegister *float64 `json:"active_register,omitempty"`
	//
	ActiveRegisterCost *float64 `json:"active_register_cost,omitempty"`
	//
	ActiveRegisterRate *float64 `json:"active_register_rate,omitempty"`
	//
	ClickCnt *float64 `json:"click_cnt,omitempty"`
	//
	ConversionCost *float64 `json:"conversion_cost,omitempty"`
	//
	ConvertCnt *float64 `json:"convert_cnt,omitempty"`
	//
	Cost *float64 `json:"cost,omitempty"`
	//
	Form *float64 `json:"form,omitempty"`
	//
	FormCost *float64 `json:"form_cost,omitempty"`
	//
	GameAddiction *float64 `json:"game_addiction,omitempty"`
	//
	GameAddictionCost *float64 `json:"game_addiction_cost,omitempty"`
	//
	GameAddictionRate *float64 `json:"game_addiction_rate,omitempty"`
	//
	LoanCompletion *float64 `json:"loan_completion,omitempty"`
	//
	LoanCompletionCost *float64 `json:"loan_completion_cost,omitempty"`
	//
	LoanCredit *float64 `json:"loan_credit,omitempty"`
	//
	LoanCreditCost *float64 `json:"loan_credit_cost,omitempty"`
	//
	NextDayOpen *float64 `json:"next_day_open,omitempty"`
	//
	NextDayOpenCost *float64 `json:"next_day_open_cost,omitempty"`
	//
	NextDayOpenRate *float64 `json:"next_day_open_rate,omitempty"`
	//
	PreLoanCredit *float64 `json:"pre_loan_credit,omitempty"`
	//
	PreLoanCreditCost *float64 `json:"pre_loan_credit_cost,omitempty"`
	//
	Rit *int64 `json:"rit,omitempty"`
	//
	ShowCnt *float64 `json:"show_cnt,omitempty"`
	//
	StatCost *float64 `json:"stat_cost,omitempty"`
}
