// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: api/v1/ad/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdBatchTaskDetailSearchReq 分页请求参数
type AdBatchTaskDetailSearchReq struct {
	g.Meta `path:"/list" tags:"广告批量操作任务详情" method:"post" summary:"广告批量操作任务详情列表"`
	commonApi.Author
	model.AdBatchTaskDetailSearchReq
}

// AdBatchTaskDetailSearchRes 列表返回结果
type AdBatchTaskDetailSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdBatchTaskDetailSearchRes
}

// AdBatchTaskDetailAddReq 添加操作请求参数
type AdBatchTaskDetailAddReq struct {
	g.Meta `path:"/add" tags:"广告批量操作任务详情" method:"post" summary:"广告批量操作任务详情添加"`
	commonApi.Author
	*model.AdBatchTaskDetailAddReq
}

// AdBatchTaskDetailAddRes 添加操作返回结果
type AdBatchTaskDetailAddRes struct {
	commonApi.EmptyRes
}

// AdBatchTaskDetailEditReq 修改操作请求参数
type AdBatchTaskDetailEditReq struct {
	g.Meta `path:"/edit" tags:"广告批量操作任务详情" method:"put" summary:"广告批量操作任务详情修改"`
	commonApi.Author
	*model.AdBatchTaskDetailEditReq
}

// AdBatchTaskDetailEditRes 修改操作返回结果
type AdBatchTaskDetailEditRes struct {
	commonApi.EmptyRes
}

// AdBatchTaskDetailGetReq 获取一条数据请求
type AdBatchTaskDetailGetReq struct {
	g.Meta `path:"/get" tags:"广告批量操作任务详情" method:"get" summary:"获取广告批量操作任务详情信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdBatchTaskDetailGetRes 获取一条数据结果
type AdBatchTaskDetailGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdBatchTaskDetailInfoRes
}

// AdBatchTaskDetailDeleteReq 删除数据请求
type AdBatchTaskDetailDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告批量操作任务详情" method:"post" summary:"删除广告批量操作任务详情"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdBatchTaskDetailDeleteRes 删除数据返回
type AdBatchTaskDetailDeleteRes struct {
	commonApi.EmptyRes
}
