/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// FileVideoUploadTaskListV2ApiService   获取异步上传视频文件结果
type FileVideoUploadTaskListV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2FileVideoUploadTaskListGetRequest
}

type ApiOpenApi2FileVideoUploadTaskListGetRequest struct {
	AccountId   int64
	AccountType models.FileVideoUploadTaskListV2AccountType
	TaskIds     *[]int64
}

func (r *FileVideoUploadTaskListV2ApiService) SetCfg(cfg *conf.Configuration) *FileVideoUploadTaskListV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileVideoUploadTaskListV2ApiService) SetRequest(advertiserInfoV2Request ApiOpenApi2FileVideoUploadTaskListGetRequest) *FileVideoUploadTaskListV2ApiService {
	r.Request = &advertiserInfoV2Request
	return r
}

func (r *FileVideoUploadTaskListV2ApiService) AccessToken(accessToken string) *FileVideoUploadTaskListV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *FileVideoUploadTaskListV2ApiService) Do() (data *models.FileVideoUploadTaskListV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/video/upload_task/list/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "account_id", r.Request.AccountId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "account_type", r.Request.AccountType)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "task_ids", r.Request.TaskIds)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.FileVideoUploadTaskListV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.FileVideoUploadTaskListV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/video/upload_task/list/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
