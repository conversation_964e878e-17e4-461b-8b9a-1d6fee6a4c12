// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-05 16:56:44
// 生成路径: api/v1/system/sys_notice_template.go
// 生成人：cq
// desc:通知公告模板相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// SysNoticeTemplateSearchReq 分页请求参数
type SysNoticeTemplateSearchReq struct {
	g.Meta `path:"/list" tags:"通知公告模板" method:"get" summary:"通知公告模板列表"`
	commonApi.Author
	model.SysNoticeTemplateSearchReq
}

// SysNoticeTemplateSearchRes 列表返回结果
type SysNoticeTemplateSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SysNoticeTemplateSearchRes
}

// SysNoticeTemplateAddReq 添加操作请求参数
type SysNoticeTemplateAddReq struct {
	g.Meta `path:"/add" tags:"通知公告模板" method:"post" summary:"通知公告模板添加"`
	commonApi.Author
	*model.SysNoticeTemplateAddReq
}

// SysNoticeTemplateAddRes 添加操作返回结果
type SysNoticeTemplateAddRes struct {
	commonApi.EmptyRes
}

// SysNoticeTemplateEditReq 修改操作请求参数
type SysNoticeTemplateEditReq struct {
	g.Meta `path:"/edit" tags:"通知公告模板" method:"put" summary:"通知公告模板修改"`
	commonApi.Author
	*model.SysNoticeTemplateEditReq
}

// SysNoticeTemplateEditRes 修改操作返回结果
type SysNoticeTemplateEditRes struct {
	commonApi.EmptyRes
}

// SysNoticeTemplateGetReq 获取一条数据请求
type SysNoticeTemplateGetReq struct {
	g.Meta `path:"/get" tags:"通知公告模板" method:"get" summary:"获取通知公告模板信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SysNoticeTemplateGetRes 获取一条数据结果
type SysNoticeTemplateGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SysNoticeTemplateInfoRes
}

// SysNoticeTemplateDeleteReq 删除数据请求
type SysNoticeTemplateDeleteReq struct {
	g.Meta `path:"/post" tags:"通知公告模板" method:"post" summary:"删除通知公告模板"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SysNoticeTemplateDeleteRes 删除数据返回
type SysNoticeTemplateDeleteRes struct {
	commonApi.EmptyRes
}
