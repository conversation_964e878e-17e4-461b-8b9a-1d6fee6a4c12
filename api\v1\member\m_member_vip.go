// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-08 18:21:14
// 生成路径: api/v1/member/m_member_vip.go
// 生成人：lx
// desc:vip信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberVipSearchReq 分页请求参数
type MMemberVipSearchReq struct {
	g.Meta `path:"/list" tags:"vip信息表" method:"get" summary:"vip信息表列表"`
	commonApi.Author
	model.MMemberVipSearchReq
}

// MMemberVipSearchRes 列表返回结果
type MMemberVipSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberVipSearchRes
}

// MMemberVipAddReq 添加操作请求参数
type MMemberVipAddReq struct {
	g.Meta `path:"/add" tags:"vip信息表" method:"post" summary:"vip信息表添加"`
	commonApi.Author
	*model.MMemberVipAddReq
}

// MMemberVipAddRes 添加操作返回结果
type MMemberVipAddRes struct {
	commonApi.EmptyRes
}

// MMemberVipEditReq 修改操作请求参数
type MMemberVipEditReq struct {
	g.Meta `path:"/edit" tags:"vip信息表" method:"post" summary:"vip信息表修改"`
	commonApi.Author
	*model.MMemberVipEditReq
}

// MMemberVipEditRes 修改操作返回结果
type MMemberVipEditRes struct {
	commonApi.EmptyRes
}

// MMemberVipGetReq 获取一条数据请求
type MMemberVipGetReq struct {
	g.Meta `path:"/get" tags:"vip信息表" method:"post" summary:"获取vip信息表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberVipGetRes 获取一条数据结果
type MMemberVipGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberVipInfoRes
}

// MMemberVipDeleteReq 删除数据请求
type MMemberVipDeleteReq struct {
	g.Meta `path:"/delete" tags:"vip信息表" method:"post" summary:"删除vip信息表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MMemberVipDeleteRes 删除数据返回
type MMemberVipDeleteRes struct {
	commonApi.EmptyRes
}

// MMemberTheaterVipAddReq 添加剧VIP请求
type MMemberTheaterVipAddReq struct {
	g.Meta `path:"/addTheaterVip" tags:"vip信息表" method:"post" summary:"添加剧VIP"`
	commonApi.Author
	SaasId    string `p:"saasId" v:"required#saasId必须"`
	TheaterId int    `p:"theaterId" v:"required#剧ID必须"`
}

// MMemberTheaterVipAddRes 添加剧VIP数据返回
type MMemberTheaterVipAddRes struct {
	commonApi.EmptyRes
}
