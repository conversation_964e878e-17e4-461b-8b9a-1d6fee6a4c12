// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-01-24 17:39:00
// 生成路径: api/v1/member/m_member_watch_statistics.go
// 生成人：cyao
// desc:用户看剧统计表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberWatchStatisticsSearchReq 分页请求参数
type MMemberWatchStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"用户看剧统计表" method:"get" summary:"用户看剧统计表列表"`
	commonApi.Author
	model.MMemberWatchStatisticsSearchReq
}

// MMemberWatchStatisticsSearchRes 列表返回结果
type MMemberWatchStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberWatchStatisticsSearchRes
}

// StatListReq 分页请求参数
type StatListReq struct {
	g.Meta `path:"/stat/list" tags:"用户看剧统计表" method:"get" summary:"用户看剧统计"`
	commonApi.Author
	model.StatListReq
}

type StatListRes struct {
	g.Meta `mime:"application/json"`
	*model.StatListRes
}

// MMemberWatchStatisticsExportReq 导出请求
type MMemberWatchStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"用户看剧统计表" method:"get" summary:"用户看剧统计导出"`
	commonApi.Author
	model.StatListReq
}

// MMemberWatchStatisticsExportRes 导出响应
type MMemberWatchStatisticsExportRes struct {
	commonApi.EmptyRes
}

// MMemberWatchStatisticsAddReq 添加操作请求参数
type MMemberWatchStatisticsAddReq struct {
	g.Meta `path:"/add" tags:"用户看剧统计表" method:"post" summary:"用户看剧统计表添加"`
	commonApi.Author
	*model.MMemberWatchStatisticsAddReq
}

// MMemberWatchStatisticsAddRes 添加操作返回结果
type MMemberWatchStatisticsAddRes struct {
	commonApi.EmptyRes
}

// MMemberWatchStatisticsEditReq 修改操作请求参数
type MMemberWatchStatisticsEditReq struct {
	g.Meta `path:"/edit" tags:"用户看剧统计表" method:"put" summary:"用户看剧统计表修改"`
	commonApi.Author
	*model.MMemberWatchStatisticsEditReq
}

// MMemberWatchStatisticsEditRes 修改操作返回结果
type MMemberWatchStatisticsEditRes struct {
	commonApi.EmptyRes
}

// MMemberWatchStatisticsGetReq 获取一条数据请求
type MMemberWatchStatisticsGetReq struct {
	g.Meta `path:"/get" tags:"用户看剧统计表" method:"get" summary:"获取用户看剧统计表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberWatchStatisticsGetRes 获取一条数据结果
type MMemberWatchStatisticsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberWatchStatisticsInfoRes
}

// MMemberWatchStatisticsDeleteReq 删除数据请求
type MMemberWatchStatisticsDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户看剧统计表" method:"post" summary:"删除用户看剧统计表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// MMemberWatchStatisticsDeleteRes 删除数据返回
type MMemberWatchStatisticsDeleteRes struct {
	commonApi.EmptyRes
}

// MMemberWatchStatTaskReq 用户看剧统计任务请求参数
type MMemberWatchStatTaskReq struct {
	g.Meta `path:"/task" tags:"用户看剧统计表" method:"get" summary:"用户看剧统计任务"`
	commonApi.Author
	model.StatListReq
}

type MMemberWatchStatTaskRes struct {
	commonApi.EmptyRes
}
