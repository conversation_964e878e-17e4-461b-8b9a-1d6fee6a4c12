// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:58
// 生成路径: internal/app/adx/model/entity/adx_product.go
// 生成人：cq
// desc:ADX产品信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxProduct is the golang structure for table adx_product.
type AdxProduct struct {
	gmeta.Meta  `orm:"table:adx_product, do:true"`
	Id          interface{} `orm:"id,primary" json:"id"`            // 产品ID
	ProductName interface{} `orm:"product_name" json:"productName"` // 产品名称
	Industry    interface{} `orm:"industry" json:"industry"`        // 所属行业
	Icon        interface{} `orm:"icon" json:"icon"`                // 产品图标URL
	ChargesType interface{} `orm:"charges_type" json:"chargesType"` // 资费类型(枚举值)
	Type        interface{} `orm:"type" json:"type"`                // 产品类型(枚举值)
}
