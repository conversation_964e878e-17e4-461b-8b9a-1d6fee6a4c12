// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-18 15:23:34
// 生成路径: api/v1/ad/fq_ad_user_info.go
// 生成人：gfast
// desc:用户注册信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdUserInfoSearchReq 分页请求参数
type FqAdUserInfoSearchReq struct {
	g.Meta `path:"/list" tags:"用户注册信息表" method:"get" summary:"用户注册信息表列表"`
	commonApi.Author
	model.FqAdUserInfoSearchReq
}

// FqAdUserInfoSearchRes 列表返回结果
type FqAdUserInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdUserInfoSearchRes
}

// FqAdUserInfoPullReq
type FqAdUserInfoPullReq struct {
	g.Meta `path:"/userInfo/pull" tags:"用户注册信息表" method:"get" summary:"pull"`
	commonApi.Author
}

// FqAdUserInfoPullRes
type FqAdUserInfoPullRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// FqAdUserInfoExportReq 导出请求
type FqAdUserInfoExportReq struct {
	g.Meta `path:"/export" tags:"用户注册信息表" method:"get" summary:"用户注册信息表导出"`
	commonApi.Author
	model.FqAdUserInfoSearchReq
}

// FqAdUserInfoExportRes 导出响应
type FqAdUserInfoExportRes struct {
	commonApi.EmptyRes
}

// FqAdUserInfoAddReq 添加操作请求参数
type FqAdUserInfoAddReq struct {
	g.Meta `path:"/add" tags:"用户注册信息表" method:"post" summary:"用户注册信息表添加"`
	commonApi.Author
	*model.FqAdUserInfoAddReq
}

// FqAdUserInfoAddRes 添加操作返回结果
type FqAdUserInfoAddRes struct {
	commonApi.EmptyRes
}

// FqAdUserInfoEditReq 修改操作请求参数
type FqAdUserInfoEditReq struct {
	g.Meta `path:"/edit" tags:"用户注册信息表" method:"put" summary:"用户注册信息表修改"`
	commonApi.Author
	*model.FqAdUserInfoEditReq
}

// FqAdUserInfoEditRes 修改操作返回结果
type FqAdUserInfoEditRes struct {
	commonApi.EmptyRes
}

// FqAdUserInfoGetReq 获取一条数据请求
type FqAdUserInfoGetReq struct {
	g.Meta `path:"/get" tags:"用户注册信息表" method:"get" summary:"获取用户注册信息表信息"`
	commonApi.Author
	DistributorId int64 `p:"distributorId" v:"required#主键必须"` //通过主键获取
}

// FqAdUserInfoGetRes 获取一条数据结果
type FqAdUserInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdUserInfoInfoRes
}

// FqAdUserInfoDeleteReq 删除数据请求
type FqAdUserInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"用户注册信息表" method:"delete" summary:"删除用户注册信息表"`
	commonApi.Author
	DistributorIds []int64 `p:"distributorIds" v:"required#主键必须"` //通过主键删除
}

// FqAdUserInfoDeleteRes 删除数据返回
type FqAdUserInfoDeleteRes struct {
	commonApi.EmptyRes
}
