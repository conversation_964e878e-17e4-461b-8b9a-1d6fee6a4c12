/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// DmpCustomAudiencePushV2ApiService DmpCustomAudiencePushV2Api service
type DmpCustomAudiencePushV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.DmpCustomAudiencePushV2V2Request
}

func (r *DmpCustomAudiencePushV2ApiService) SetCfg(cfg *conf.Configuration) *DmpCustomAudiencePushV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DmpCustomAudiencePushV2ApiService) DmpCustomAudiencePushV2V2Request(dmpCustomAudiencePushV2V2Request models.DmpCustomAudiencePushV2V2Request) *DmpCustomAudiencePushV2ApiService {
	r.Request = &dmpCustomAudiencePushV2V2Request
	return r
}

func (r *DmpCustomAudiencePushV2ApiService) AccessToken(accessToken string) *DmpCustomAudiencePushV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DmpCustomAudiencePushV2ApiService) Do() (data *models.DmpCustomAudiencePushV2V2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dmp/custom_audience/push_v2/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.DmpCustomAudiencePushV2V2Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DmpCustomAudiencePushV2V2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dmp/custom_audience/push_v2/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
