// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: api/v1/ad/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdSalerCopyRightSearchReq 分页请求参数
type KsAdSalerCopyRightSearchReq struct {
	g.Meta `path:"/list" tags:"快手版权商短剧分销数据" method:"post" summary:"快手版权商短剧分销数据列表"`
	commonApi.Author
	model.KsAdSalerCopyRightSearchReq
}

// KsAdSalerCopyRightSearchRes 列表返回结果
type KsAdSalerCopyRightSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdSalerCopyRightSearchRes
}

// KsAdSalerCopyRightExportReq 导出请求
type KsAdSalerCopyRightExportReq struct {
	g.Meta `path:"/export" tags:"快手版权商短剧分销数据" method:"post" summary:"快手版权商短剧分销数据导出"`
	commonApi.Author
	model.KsAdSalerCopyRightSearchReq
}

// KsAdSalerCopyRightExportRes 导出响应
type KsAdSalerCopyRightExportRes struct {
	commonApi.EmptyRes
}

// KsAdSalerCopyRightAddReq 添加操作请求参数
type KsAdSalerCopyRightAddReq struct {
	g.Meta `path:"/add" tags:"快手版权商短剧分销数据" method:"post" summary:"快手版权商短剧分销数据添加"`
	commonApi.Author
	*model.KsAdSalerCopyRightAddReq
}

// KsAdSalerCopyRightAddRes 添加操作返回结果
type KsAdSalerCopyRightAddRes struct {
	commonApi.EmptyRes
}

// KsAdSalerCopyRightEditReq 修改操作请求参数
type KsAdSalerCopyRightEditReq struct {
	g.Meta `path:"/edit" tags:"快手版权商短剧分销数据" method:"put" summary:"快手版权商短剧分销数据修改"`
	commonApi.Author
	*model.KsAdSalerCopyRightEditReq
}

// KsAdSalerCopyRightEditRes 修改操作返回结果
type KsAdSalerCopyRightEditRes struct {
	commonApi.EmptyRes
}

// KsAdSalerCopyRightGetReq 获取一条数据请求
type KsAdSalerCopyRightGetReq struct {
	g.Meta `path:"/get" tags:"快手版权商短剧分销数据" method:"get" summary:"获取快手版权商短剧分销数据信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdSalerCopyRightGetRes 获取一条数据结果
type KsAdSalerCopyRightGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdSalerCopyRightInfoRes
}

// KsAdSalerCopyRightDeleteReq 删除数据请求
type KsAdSalerCopyRightDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手版权商短剧分销数据" method:"post" summary:"删除快手版权商短剧分销数据"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdSalerCopyRightDeleteRes 删除数据返回
type KsAdSalerCopyRightDeleteRes struct {
	commonApi.EmptyRes
}

// KsAdSalerCopyRightTaskReq 分页请求参数
type KsAdSalerCopyRightTaskReq struct {
	g.Meta `path:"/task" tags:"快手版权商短剧分销数据" method:"post" summary:"快手版权商短剧分销数据任务"`
	commonApi.Author
	model.KsAdSalerCopyRightSearchReq
}

// KsAdSalerCopyRightTaskRes 列表返回结果
type KsAdSalerCopyRightTaskRes struct {
	g.Meta `mime:"application/json"`
}
