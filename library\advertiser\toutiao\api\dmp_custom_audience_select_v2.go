/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DmpCustomAudienceSelectV2ApiService DmpCustomAudienceSelectV2Api service
type DmpCustomAudienceSelectV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DmpCustomAudienceSelectV2Request
}

type DmpCustomAudienceSelectV2Request struct {
	AdvertiserId *int64
	SelectType   *int64
	Offset       *int64
	Limit        *int64
}

func (r *DmpCustomAudienceSelectV2ApiService) SetCfg(cfg *conf.Configuration) *DmpCustomAudienceSelectV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DmpCustomAudienceSelectV2ApiService) DmpCustomAudienceSelectV2Request(dmpCustomAudienceSelectV2Request DmpCustomAudienceSelectV2Request) *DmpCustomAudienceSelectV2ApiService {
	r.Request = &dmpCustomAudienceSelectV2Request
	return r
}

func (r *DmpCustomAudienceSelectV2ApiService) AccessToken(accessToken string) *DmpCustomAudienceSelectV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DmpCustomAudienceSelectV2ApiService) Do() (data *models.DmpCustomAudienceReadV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dmp/custom_audience/select/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.SelectType == nil {
		return nil, errors.New("selectType is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "select_type", r.Request.SelectType)
	if r.Request.Offset != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "offset", r.Request.Offset)
	}
	if r.Request.Limit != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "limit", r.Request.Limit)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.DmpCustomAudienceReadV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DmpCustomAudienceReadV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dmp/custom_audience/select/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
