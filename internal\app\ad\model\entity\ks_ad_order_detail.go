// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: internal/app/ad/model/entity/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// KsAdOrderDetail is the golang structure for table ks_ad_order_detail.
type KsAdOrderDetail struct {
	gmeta.Meta      `orm:"table:ks_ad_order_detail"`
	Id              int64       `orm:"id,primary" json:"id"`                    // id
	AdvertiserId    int64       `orm:"advertiser_id" json:"advertiserId"`       // 用户快手号id
	PayDate         string      `orm:"pay_date" json:"payDate"`                 // 购买时间
	SettleDate      string      `orm:"settle_date" json:"settleDate"`           // 结算时间
	OrderId         string      `orm:"order_id" json:"orderId"`                 // 订单ID
	OrderType       string      `orm:"order_type" json:"orderType"`             // 订单类型 本账号售卖、子账户售卖、分销售卖
	CopyrightUid    string      `orm:"copyright_uid" json:"copyrightUid"`       // 版权商UID
	CopyrightName   string      `orm:"copyright_name" json:"copyrightName"`     // 版权商名称
	SeriesName      string      `orm:"series_name" json:"seriesName"`           // 短剧名称
	SubAccountUid   string      `orm:"sub_account_uid" json:"subAccountUid"`    // 子账号UID
	SubAccountName  string      `orm:"sub_account_name" json:"subAccountName"`  // 子账号名称
	PayProvider     string      `orm:"pay_provider" json:"payProvider"`         // 支付渠道
	PayAmt          float64     `orm:"pay_amt" json:"payAmt"`                   // 结算订单总金额（元）
	RedundPrice     float64     `orm:"redund_price" json:"redundPrice"`         // 退款金额（元）
	CommissionPrice float64     `orm:"commission_price" json:"commissionPrice"` // 佣金（元）
	SettlePrice     float64     `orm:"settle_price" json:"settlePrice"`         // 可提现金额（元）
	SettleAmt       float64     `orm:"settle_amt" json:"settleAmt"`             // 分成金额（元）
	SalerRateStr    string      `orm:"saler_rate_str" json:"salerRateStr"`      // 分成比例
	Expenditure     float64     `orm:"expenditure" json:"expenditure"`          // 总支出（元）
	Income          float64     `orm:"income" json:"income"`                    // 总收入（元）
	SettleRate      string      `orm:"settle_rate" json:"settleRate"`           // 分成比例 仅分销售卖该字段有值
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt"`             // 创建时间
	UpdatedAt       *gtime.Time `orm:"updated_at" json:"updatedAt"`             // 更新时间
}
