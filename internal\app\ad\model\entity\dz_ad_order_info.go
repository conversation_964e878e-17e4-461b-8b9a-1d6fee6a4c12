// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:38
// 生成路径: internal/app/ad/model/entity/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdOrderInfo is the golang structure for table dz_ad_order_info.
type DzAdOrderInfo struct {
	gmeta.Meta    `orm:"table:dz_ad_order_info"`
	Id            uint64      `orm:"id,primary" json:"id"`                 // 商户单号
	Ver           string      `orm:"ver" json:"ver"`                       // 小程序版本号
	OutTradeNo    string      `orm:"out_trade_no" json:"outTradeNo"`       // 交易单号
	Discount      float64     `orm:"discount" json:"discount"`             // 订单金额（单位元）
	Type          int         `orm:"type" json:"type"`                     // 订单类型（1.看点充值 2.VIP）
	StatusNotify  int         `orm:"status_notify" json:"statusNotify"`    // 订单状态（0.待支付 1.支付成功）
	Ctime         int64       `orm:"ctime" json:"ctime"`                   // 下单时间（秒）
	FinishTime    int64       `orm:"finish_time" json:"finishTime"`        // 完成时间（秒）
	UserId        int64       `orm:"user_id" json:"userId"`                // 用户ID
	ChannelId     string      `orm:"channel_id" json:"channelId"`          // 渠道ID
	Domain        int         `orm:"domain" json:"domain"`                 // 业务线（14抖音 16微信 17快手）
	SourceInfo    string      `orm:"source_info" json:"sourceInfo"`        // 短剧ID
	ChapterId     string      `orm:"chapter_id" json:"chapterId"`          // 剧集ID
	SourceDesc    string      `orm:"source_desc" json:"sourceDesc"`        // 短剧名称
	RegisterDate  string      `orm:"register_date" json:"registerDate"`    // 注册时间（秒）
	OpenId        string      `orm:"open_id" json:"openId"`                // 小程序openId
	Os            string      `orm:"os" json:"os"`                         // 机型（0:Android 1:iOS）
	ReferralId    string      `orm:"referral_id" json:"referralId"`        // 推广链接ID
	Adid          string      `orm:"adid" json:"adid"`                     // 计划ID
	FromDrId      string      `orm:"from_dr_id" json:"fromDrId"`           // 达人ID
	Platform      string      `orm:"platform" json:"platform"`             // 达人平台
	Scene         string      `orm:"scene" json:"scene"`                   // 进入场景
	ThirdCorpId   string      `orm:"third_corp_id" json:"thirdCorpId"`     // 三方来源企微主体
	ThirdWxId     string      `orm:"third_wx_id" json:"thirdWxId"`         // 三方企微唯一标识
	KdrId         string      `orm:"kdr_id" json:"kdrId"`                  // 挂载达人ID
	SelfReturn    string      `orm:"self_return" json:"selfReturn"`        // 自归因
	ProjectId     string      `orm:"project_id" json:"projectId"`          // 头条2.0参数 - 项目ID
	PromotionId   string      `orm:"promotion_id" json:"promotionId"`      // 头条2.0参数 - 推广ID
	SchannelTime  string      `orm:"schannel_time" json:"schannelTime"`    // 抖音快手注册时间
	DyeTime       string      `orm:"dye_time" json:"dyeTime"`              // 抖音快手推广链接染色时间
	XuniPay       string      `orm:"xuni_pay" json:"xuniPay"`              // 虚拟支付订单（1=是）
	MoneyBenefit  string      `orm:"money_benefit" json:"moneyBenefit"`    // 渠道分成金额
	Mid1          string      `orm:"mid1" json:"mid1"`                     // 素材ID - 图片
	Mid2          string      `orm:"mid2" json:"mid2"`                     // 素材ID - 标题
	Mid3          string      `orm:"mid3" json:"mid3"`                     // 素材ID - 视频
	UnionId       string      `orm:"union_id" json:"unionId"`              // 小程序unionId
	From          string      `orm:"from" json:"from"`                     // 投放媒体（如bd/dy/ks等）
	OrderSubType  string      `orm:"order_sub_type" json:"orderSubType"`   // 订单业务类型（如整本购）
	WxFinderId    string      `orm:"wx_finder_id" json:"wxFinderId"`       // 视频号ID
	WxExportId    string      `orm:"wx_export_id" json:"wxExportId"`       // 视频ID
	WxPromotionId string      `orm:"wx_promotion_id" json:"wxPromotionId"` // 加热订单ID
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt"`          // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt"`          // 更新时间
}
