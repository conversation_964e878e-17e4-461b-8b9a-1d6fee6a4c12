// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-07 14:26:18
// 生成路径: internal/app/ad/logic/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdReportStats(New())
}

func New() service.IKsAdReportStats {
	return &sKsAdReportStats{}
}

type sKsAdReportStats struct{}

// GetAdData
func (s *sKsAdReportStats) GetAdData(ctx context.Context, req api.QueryAdDataReq) (res *api.SeriesAdPageDataSnake, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 根据aid 获取token
		token := api.GetAccessTokenByAppIdCache(req.AdvertiserID)
		// 首次请求第一页数据
		resp, innerError := api.GetKSApiClient().QueryAdDataService.AccessToken(token).SetReq(req).Do()
		liberr.ErrIsNil(ctx, innerError, fmt.Sprintf("获取统计数据失败: %v", innerError))
		if resp.Code != 0 {
			err = fmt.Errorf("获取统计数据失败: %s", resp.Message)
			return
		}
		res = resp.Data
	})
	return
}

// KsAdReportStatsSync 刷快手的数据
func (s *sKsAdReportStats) KsAdReportStatsSync(ctx context.Context, startTime, endTime string) (count int, err error) {
	if startTime > endTime {
		return
	}
	innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()
	for {
		if startTime > endTime {
			break
		}
		err = s.DayAdDataTask(innerContext, startTime)
		if err != nil {
			g.Log().Error(ctx, err)
		}
		err = service.KsSeriesReportCoreData().DayAdDataTask(innerContext, startTime)
		if err != nil {
			g.Log().Error(ctx, err)
		}
		g.Log().Info(ctx, fmt.Sprintf("刷快手数据成功, 统计日期: %s", startTime))
		startTime = libUtils.PlusDays(startTime, 1)
	}
	return
}

func (s *sKsAdReportStats) GetKsReportStats(ctx context.Context, aId, appId int64, time string) (res int, err error) {
	res = 0
	err = g.Try(ctx, func(ctx context.Context) {
		// 定义分页参数
		pageNo := 1
		pageSize := 100
		startTime, endTime := "", ""
		startTime, endTime, err = libUtils.GetDayTimestampsStr(time)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("传入的时间转换失败: %v", err))
		// 根据aid 获取token
		token := api.GetAccessTokenByAppIdCache(aId)
		// 首次请求第一页数据
		resp, innerError := api.GetKSApiClient().QueryAdDetailService.AccessToken(token).SetReq(api.QueryAdDetailReq{
			AdvertiserId:   aId,
			StartTime:      startTime,
			EndTime:        endTime,
			PageNo:         pageNo,
			PageSize:       pageSize,
			ExtraDimension: []string{"seriesId", "accountId"},
		}).Do()
		liberr.ErrIsNil(ctx, innerError, fmt.Sprintf("获取统计数据失败: %v", innerError))
		if resp.Code != 0 {
			err = fmt.Errorf("获取统计数据失败: %s", resp.Message)
			return
		}

		// 保存第一页数据
		if len(resp.Data.DataList) > 0 {
			for _, item := range resp.Data.DataList {
				item.AdvertiserId = aId
				y, m, d := item.Date.Date()
				item.CreateTime = fmt.Sprintf("%d-%02d-%02d", y, m, d)
			}
			_, err = dao.KsAdReportStats.Ctx(ctx).Save(resp.Data.DataList)
			res += len(resp.Data.DataList)
			//append(res, resp.Data.DataList...)
			if err != nil {
				return
			}
		}

		// 判断是否存在分页数据：如果总记录数大于当前返回的数量，则需要进行分页查询
		totalCount := resp.Data.TotalCount
		// 如果总记录数大于第一页返回的数量，则需要处理剩余的分页数据
		if totalCount > len(resp.Data.DataList) {
			// 计算总页数（注意最后一页可能不足 pageSize 条数据）
			totalPages := (totalCount + pageSize - 1) / pageSize
			// 从第二页开始循环获取剩余数据
			for page := 2; page <= totalPages; page++ {
				moreResp, moreErr := api.GetKSApiClient().QueryAdDetailService.SetReq(api.QueryAdDetailReq{
					AdvertiserId:   aId,
					StartTime:      startTime,
					EndTime:        endTime,
					PageNo:         page,
					PageSize:       pageSize,
					ExtraDimension: []string{"seriesId", "accountId"}}).Do()
				liberr.ErrIsNil(ctx, moreErr, fmt.Sprintf("获取统计数据失败, 页码 %d: %v", page, moreErr))
				if moreResp.Code != 0 {
					err = fmt.Errorf("获取统计数据失败, 页码 %d: %s", page, moreResp.Message)
					return
				}
				// 保存当前页数据
				if len(moreResp.Data.DataList) > 0 {
					for _, item := range moreResp.Data.DataList {
						item.AdvertiserId = aId
						y, m, d := item.Date.Date()
						item.CreateTime = fmt.Sprintf("%d-%02d-%02d", y, m, d)
					}
					_, err = dao.KsAdReportStats.Ctx(ctx).Save(moreResp.Data.DataList)
					res += len(moreResp.Data.DataList)
					//res = append(res, resp.Data.DataList...)
					if err != nil {
						return
					}
				}
			}
		}
	})
	return
}

// DayAdDataTask 短剧广告报表 每日统计任务
func (s *sKsAdReportStats) DayAdDataTask(ctx context.Context, statDate string) (err error) {
	if len(statDate) == 0 {
		statDate = libUtils.GetYesterdayDate()
	}
	channelRechargeStatKey := model.KSAdDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :ChannelStatTask ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = g.Try(ctx, func(ctx context.Context) {
		list, _ := service.KsAdInfo().List(ctx, &model.KsAdInfoSearchReq{
			Status: "authorized",
		})
		if len(list.List) > 0 {
			for _, item := range list.List {
				_, err = s.GetKsReportStats(innerCtx, item.AdvertiserId, int64(item.AppId), statDate)
				if err != nil {
					g.Log().Error(innerCtx, err)
				}
			}
		}
	})
	return
}

func (s *sKsAdReportStats) List(ctx context.Context, req *model.KsAdReportStatsSearchReq) (listRes *model.KsAdReportStatsSearchRes, err error) {
	listRes = new(model.KsAdReportStatsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdReportStats.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.Id != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Id+" = ?", req.Id)
		}
		if req.Likes != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Likes+" = ?", gconv.Int(req.Likes))
		}
		if req.Share != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Share+" = ?", gconv.Int(req.Share))
		}
		if req.PhotoClick != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PhotoClick+" = ?", gconv.Int(req.PhotoClick))
		}
		if req.Impression != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Impression+" = ?", gconv.Int(req.Impression))
		}
		if req.EventPay != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventPay+" = ?", gconv.Int(req.EventPay))
		}
		if req.T0DirectPaiedCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T0DirectPaiedCnt+" = ?", gconv.Int(req.T0DirectPaiedCnt))
		}
		if req.AdShow != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdShow+" = ?", gconv.Int(req.AdShow))
		}
		if req.EventAppInvoked != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventAppInvoked+" = ?", gconv.Int(req.EventAppInvoked))
		}
		if req.Conversion != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Conversion+" = ?", gconv.Int(req.Conversion))
		}
		if req.T0DirectConversionCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T0DirectConversionCnt+" = ?", gconv.Int(req.T0DirectConversionCnt))
		}
		if req.Negative != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Negative+" = ?", gconv.Int(req.Negative))
		}
		if req.Report != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Report+" = ?", gconv.Int(req.Report))
		}
		if req.Block != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Block+" = ?", gconv.Int(req.Block))
		}
		if req.Comment != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Comment+" = ?", gconv.Int(req.Comment))
		}
		if req.EventPayFirstDay != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventPayFirstDay+" = ?", gconv.Int(req.EventPayFirstDay))
		}
		if req.PlayedNum != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PlayedNum+" = ?", gconv.Int(req.PlayedNum))
		}
		if req.PlayedThreeSeconds != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PlayedThreeSeconds+" = ?", gconv.Int(req.PlayedThreeSeconds))
		}
		if req.AdPhotoPlayed75Percent != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdPhotoPlayed75Percent+" = ?", gconv.Int(req.AdPhotoPlayed75Percent))
		}
		if req.PlayedEnd != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PlayedEnd+" = ?", gconv.Int(req.PlayedEnd))
		}
		if req.Follow != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Follow+" = ?", gconv.Int(req.Follow))
		}
		if req.EventNewUserPay != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventNewUserPay+" = ?", gconv.Int(req.EventNewUserPay))
		}
		if req.AdItemClick != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdItemClick+" = ?", gconv.Int(req.AdItemClick))
		}
		if req.T7PaiedCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T7PaiedCnt+" = ?", gconv.Int(req.T7PaiedCnt))
		}
		if req.ConversionNumByImpression7D != "" {
			m = m.Where(dao.KsAdReportStats.Columns().ConversionNumByImpression7D+" = ?", gconv.Int(req.ConversionNumByImpression7D))
		}
		if req.DeepConversionNumByImpression7D != "" {
			m = m.Where(dao.KsAdReportStats.Columns().DeepConversionNumByImpression7D+" = ?", gconv.Int(req.DeepConversionNumByImpression7D))
		}
		if req.ConversionNum != "" {
			m = m.Where(dao.KsAdReportStats.Columns().ConversionNum+" = ?", gconv.Int(req.ConversionNum))
		}
		if req.DeepConversionNum != "" {
			m = m.Where(dao.KsAdReportStats.Columns().DeepConversionNum+" = ?", gconv.Int(req.DeepConversionNum))
		}
		if req.T0PaiedCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T0PaiedCnt+" = ?", gconv.Int(req.T0PaiedCnt))
		}
		if req.Date != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Date+" = ?", gconv.Time(req.Date))
		}
		if req.KeyAction != "" {
			m = m.Where(dao.KsAdReportStats.Columns().KeyAction+" = ?", gconv.Int64(req.KeyAction))
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if len(req.AdvertiserIds) > 0 {
			m = m.WhereIn(dao.KsAdReportStats.Columns().AdvertiserId, req.AdvertiserIds)
		}
		if len(req.SeriesIds) > 0 {
			m = m.WhereIn(dao.KsAdReportStats.Columns().SeriesId, req.SeriesIds)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.KsAdReportStats.Columns().CreateTime+" = ?", req.CreateTime)
		}
		if len(req.StartDate) > 0 && len(req.EndDate) > 0 {
			m = m.Where(dao.KsAdReportStats.Columns().Date+" >= ?", req.StartDate)
			m = m.Where(dao.KsAdReportStats.Columns().Date+" <= ?", req.EndDate)
		}

		var summary = new(model.KsAdReportSummaryDataRes)
		m.FieldSum(dao.KsAdReportStats.Columns().TotalCharge, "totalCharge").
			FieldSum(dao.KsAdReportStats.Columns().Likes, "likes").
			FieldSum(dao.KsAdReportStats.Columns().Share, "share").
			FieldSum(dao.KsAdReportStats.Columns().PhotoClick, "photoClick").
			FieldSum(dao.KsAdReportStats.Columns().Impression, "impression").
			FieldSum(dao.KsAdReportStats.Columns().EventPay, "eventPay").
			FieldSum(dao.KsAdReportStats.Columns().T0DirectPaiedCnt, "t0DirectPaiedCnt").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmount, "eventPayPurchaseAmount").
			FieldSum(dao.KsAdReportStats.Columns().T0DirectPaiedAmt, "t0DirectPaiedAmt").
			FieldSum(dao.KsAdReportStats.Columns().AdShow, "adShow").
			FieldSum(dao.KsAdReportStats.Columns().TotalCharge, "totalCharge").
			FieldSum(dao.KsAdReportStats.Columns().EventAppInvoked, "eventAppInvoked").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountFirstDay, "eventPayPurchaseAmountFirstDay").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountOneDayByConversion, "eventPayPurchaseAmountOneDayByConversion").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountWeekByConversion, "eventPayPurchaseAmountWeekByConversion").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountThreeDayByConversion, "eventPayPurchaseAmountThreeDayByConversion").
			FieldSum(dao.KsAdReportStats.Columns().Conversion, "conversion").
			FieldSum(dao.KsAdReportStats.Columns().T0DirectConversionCnt, "t0DirectConversionCnt").
			FieldSum(dao.KsAdReportStats.Columns().Negative, "negative").
			FieldSum(dao.KsAdReportStats.Columns().Report, "report").
			FieldSum(dao.KsAdReportStats.Columns().Block, "block").
			FieldSum(dao.KsAdReportStats.Columns().Comment, "comment").
			FieldSum(dao.KsAdReportStats.Columns().EventPayFirstDay, "eventPayFirstDay").
			FieldSum(dao.KsAdReportStats.Columns().PlayedNum, "playedNum").
			FieldSum(dao.KsAdReportStats.Columns().PlayedThreeSeconds, "playedThreeSeconds").
			FieldSum(dao.KsAdReportStats.Columns().AdPhotoPlayed75Percent, "adPhotoPlayed75Percent").
			FieldSum(dao.KsAdReportStats.Columns().PlayedEnd, "playedEnd").
			FieldSum(dao.KsAdReportStats.Columns().Follow, "follow").
			FieldSum(dao.KsAdReportStats.Columns().EventNewUserPay, "eventNewUserPay").
			FieldSum(dao.KsAdReportStats.Columns().AdItemClick, "adItemClick").
			FieldSum(dao.KsAdReportStats.Columns().T7PaiedCnt, "t7PaiedCnt").
			FieldSum(dao.KsAdReportStats.Columns().T7PaiedAmt, "t7PaiedAmt").
			FieldSum(dao.KsAdReportStats.Columns().ConversionNumByImpression7D, "conversionNumByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().DeepConversionNumByImpression7D, "deepConversionNumByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().ConversionNum, "conversionNum").
			FieldSum(dao.KsAdReportStats.Columns().DeepConversionNum, "deepConversionNum").
			FieldSum(dao.KsAdReportStats.Columns().T0PaiedCnt, "t0PaiedCnt").
			FieldSum(dao.KsAdReportStats.Columns().T0PaiedAmt, "t0PaiedAmt").
			FieldSum(dao.KsAdReportStats.Columns().EventPayCost, "eventPayCost").
			FieldSum(dao.KsAdReportStats.Columns().EventAppInvokedCost, "eventAppInvokedCost").
			FieldSum(dao.KsAdReportStats.Columns().ConversionCost, "conversionCost").
			FieldSum(dao.KsAdReportStats.Columns().DeepConversionCostByImpression7D, "deepConversionCostByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().EventPayFirstDayCost, "eventPayFirstDayCost").
			FieldSum(dao.KsAdReportStats.Columns().EventNewUserPayCost, "eventNewUserPayCost").
			FieldSum(dao.KsAdReportStats.Columns().ConversionCostByImpression7D, "conversionCostByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().AdPhotoPlayed75PercentRatio2, "adPhotoPlayed75PercentRatio2").
			FieldSum(dao.KsAdReportStats.Columns().MiniGameIaaPurchaseAmount, "miniGameIaaPurchaseAmount").
			FieldSum(dao.KsAdReportStats.Columns().MiniGameIaaPurchaseAmount, "miniGameIaaPurchaseAmount").
			Scan(&summary)
		listRes.Summary = summary
		summary.EventPayPurchaseAmount = libUtils.ToRound(summary.EventPayPurchaseAmount, 2, libUtils.RoundHalfEven)
		summary.T0DirectPaiedAmt = libUtils.ToRound(summary.T0DirectPaiedAmt, 2, libUtils.RoundHalfEven)
		summary.TotalCharge = libUtils.ToRound(summary.TotalCharge, 2, libUtils.RoundHalfEven)
		summary.EventPayPurchaseAmountFirstDay = libUtils.ToRound(summary.EventPayPurchaseAmountFirstDay, 2, libUtils.RoundHalfEven)
		summary.EventPayPurchaseAmountOneDayByConversion = libUtils.ToRound(summary.EventPayPurchaseAmountOneDayByConversion, 2, libUtils.RoundHalfEven)
		summary.EventPayPurchaseAmountWeekByConversion = libUtils.ToRound(summary.EventPayPurchaseAmountWeekByConversion, 2, libUtils.RoundHalfEven)
		summary.EventPayPurchaseAmountThreeDayByConversion = libUtils.ToRound(summary.EventPayPurchaseAmountThreeDayByConversion, 2, libUtils.RoundHalfEven)
		summary.T0PaiedAmt = libUtils.ToRound(summary.T0PaiedAmt, 2, libUtils.RoundHalfEven)
		summary.DeepConversionCostByImpression7D = libUtils.ToRound(summary.DeepConversionCostByImpression7D, 2, libUtils.RoundHalfEven)
		//summary.EventPayCost = libUtils.ToRound(summary.EventPayCost, 2, libUtils.RoundHalfEven)
		//summary.EventAppInvokedCost = libUtils.ToRound(summary.EventAppInvokedCost, 2, libUtils.RoundHalfEven)
		//summary.ConversionCost = libUtils.ToRound(summary.ConversionCost, 2, libUtils.RoundHalfEven)
		//summary.EventPayFirstDayCost = libUtils.ToRound(summary.EventPayFirstDayCost, 2, libUtils.RoundHalfEven)
		//summary.EventNewUserPayCost = libUtils.ToRound(summary.EventNewUserPayCost, 2, libUtils.RoundHalfEven)
		summary.ConversionCostByImpression7D = libUtils.ToRound(summary.ConversionCostByImpression7D, 2, libUtils.RoundHalfEven)
		summary.RechargeRate = libUtils.ToRound(summary.RechargeRate, 2, libUtils.RoundHalfEven)
		summary.MiniGameIaaPurchaseAmount = libUtils.ToRound(summary.MiniGameIaaPurchaseAmount, 2, libUtils.RoundHalfEven)
		summary.AdPhotoPlayed75PercentRatio2 = libUtils.ToRound(summary.AdPhotoPlayed75PercentRatio2, 2, libUtils.RoundHalfEven)
		summary.T7PaiedAmt = libUtils.ToRound(summary.T7PaiedAmt, 2, libUtils.RoundHalfEven)
		if req.AdvertiserId == "" && len(req.AdvertiserIds) == 0 && len(req.SeriesIds) == 0 {
			// 按照日期汇总
			m = m.Group(dao.KsAdReportStats.Columns().Date).Group(dao.KsAdReportStats.Columns().AdvertiserId)
		} else if req.AdvertiserId != "" || len(req.AdvertiserIds) > 0 {
			m = m.Group(dao.KsAdReportStats.Columns().Date).Group(dao.KsAdReportStats.Columns().AdvertiserId)
		} else {
			m = m.Group(dao.KsAdReportStats.Columns().Date).Group(dao.KsAdReportStats.Columns().AdvertiserId).Group(dao.KsAdReportStats.Columns().SeriesId)
		}

		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := dao.KsAdReportStats.Columns().Date + " desc"
		if req.OrderBy != "" {
			order = req.OrderBy + " " + req.OrderType
		}
		var res []*model.KsAdReportStatsListRes
		m = m.FieldSum(dao.KsAdReportStats.Columns().TotalCharge, "totalCharge").
			FieldSum(dao.KsAdReportStats.Columns().Likes, "likes").
			FieldSum(dao.KsAdReportStats.Columns().Share, "share").
			FieldSum(dao.KsAdReportStats.Columns().PhotoClick, "photoClick").
			FieldSum(dao.KsAdReportStats.Columns().Impression, "impression").
			FieldSum(dao.KsAdReportStats.Columns().EventPay, "eventPay").
			FieldSum(dao.KsAdReportStats.Columns().T0DirectPaiedCnt, "t0DirectPaiedCnt").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmount, "eventPayPurchaseAmount").
			FieldSum(dao.KsAdReportStats.Columns().T0DirectPaiedAmt, "t0DirectPaiedAmt").
			FieldSum(dao.KsAdReportStats.Columns().AdShow, "adShow").
			FieldSum(dao.KsAdReportStats.Columns().TotalCharge, "totalCharge").
			FieldSum(dao.KsAdReportStats.Columns().EventAppInvoked, "eventAppInvoked").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountFirstDay, "eventPayPurchaseAmountFirstDay").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountOneDayByConversion, "eventPayPurchaseAmountOneDayByConversion").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountWeekByConversion, "eventPayPurchaseAmountWeekByConversion").
			FieldSum(dao.KsAdReportStats.Columns().EventPayPurchaseAmountThreeDayByConversion, "eventPayPurchaseAmountThreeDayByConversion").
			FieldSum(dao.KsAdReportStats.Columns().Conversion, "conversion").
			FieldSum(dao.KsAdReportStats.Columns().T0DirectConversionCnt, "t0DirectConversionCnt").
			FieldSum(dao.KsAdReportStats.Columns().Negative, "negative").
			FieldSum(dao.KsAdReportStats.Columns().Report, "report").
			FieldSum(dao.KsAdReportStats.Columns().Block, "block").
			FieldSum(dao.KsAdReportStats.Columns().Comment, "comment").
			FieldSum(dao.KsAdReportStats.Columns().EventPayFirstDay, "eventPayFirstDay").
			FieldSum(dao.KsAdReportStats.Columns().PlayedNum, "playedNum").
			FieldSum(dao.KsAdReportStats.Columns().PlayedThreeSeconds, "playedThreeSeconds").
			FieldSum(dao.KsAdReportStats.Columns().AdPhotoPlayed75Percent, "adPhotoPlayed75Percent").
			FieldSum(dao.KsAdReportStats.Columns().PlayedEnd, "playedEnd").
			FieldSum(dao.KsAdReportStats.Columns().Follow, "follow").
			FieldSum(dao.KsAdReportStats.Columns().EventNewUserPay, "eventNewUserPay").
			FieldSum(dao.KsAdReportStats.Columns().AdItemClick, "adItemClick").
			FieldSum(dao.KsAdReportStats.Columns().T7PaiedCnt, "t7PaiedCnt").
			FieldSum(dao.KsAdReportStats.Columns().T7PaiedAmt, "t7PaiedAmt").
			FieldSum(dao.KsAdReportStats.Columns().ConversionNumByImpression7D, "conversionNumByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().DeepConversionNumByImpression7D, "deepConversionNumByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().ConversionNum, "conversionNum").
			FieldSum(dao.KsAdReportStats.Columns().DeepConversionNum, "deepConversionNum").
			FieldSum(dao.KsAdReportStats.Columns().T0PaiedCnt, "t0PaiedCnt").
			FieldSum(dao.KsAdReportStats.Columns().T0PaiedAmt, "t0PaiedAmt").
			FieldSum(dao.KsAdReportStats.Columns().EventPayCost, "eventPayCost").
			FieldSum(dao.KsAdReportStats.Columns().EventAppInvokedCost, "eventAppInvokedCost").
			FieldSum(dao.KsAdReportStats.Columns().ConversionCost, "conversionCost").
			FieldSum(dao.KsAdReportStats.Columns().DeepConversionCostByImpression7D, "deepConversionCostByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().EventPayFirstDayCost, "eventPayFirstDayCost").
			FieldSum(dao.KsAdReportStats.Columns().EventNewUserPayCost, "eventNewUserPayCost").
			FieldSum(dao.KsAdReportStats.Columns().ConversionCostByImpression7D, "conversionCostByImpression7D").
			FieldSum(dao.KsAdReportStats.Columns().AdPhotoPlayed75PercentRatio2, "adPhotoPlayed75PercentRatio2").
			FieldSum(dao.KsAdReportStats.Columns().MiniGameIaaPurchaseAmount, "miniGameIaaPurchaseAmount").
			FieldSum(dao.KsAdReportStats.Columns().MiniGameIaaPurchaseAmount, "miniGameIaaPurchaseAmount")
		if req.AdvertiserId == "" && len(req.AdvertiserIds) == 0 && len(req.SeriesIds) == 0 {
			// 按照日期汇总
			m = m.Fields(dao.KsAdReportStats.Columns().Date).
				Fields(dao.KsAdReportStats.Columns().AdvertiserId)
		} else if req.AdvertiserId != "" || len(req.AdvertiserIds) > 0 {
			m = m.Fields(dao.KsAdReportStats.Columns().Date).
				Fields(dao.KsAdReportStats.Columns().AdvertiserId)
		} else {
			m = m.Fields(dao.KsAdReportStats.Columns().Date).
				Fields(dao.KsAdReportStats.Columns().AdvertiserId).
				Fields(dao.KsAdReportStats.Columns().SeriesId)

		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdReportStatsListRes, len(res))
		aidList := make([]int64, 0)
		for k, v := range res {
			aidList = append(aidList, v.AdvertiserId)
			ent := &model.KsAdReportStatsListRes{
				AdvertiserId:                             v.AdvertiserId,
				Id:                                       v.Id,
				Likes:                                    v.Likes,
				Share:                                    v.Share,
				PhotoClick:                               v.PhotoClick,
				Impression:                               v.Impression,
				EventPay:                                 v.EventPay,
				T0DirectPaiedCnt:                         v.T0DirectPaiedCnt,
				EventPayPurchaseAmount:                   libUtils.ToRound(v.EventPayPurchaseAmount, 2, libUtils.RoundHalfEven),
				T0DirectPaiedAmt:                         libUtils.ToRound(v.T0DirectPaiedAmt, 2, libUtils.RoundHalfEven),
				AdShow:                                   v.AdShow,
				TotalCharge:                              libUtils.ToRound(v.TotalCharge, 2, libUtils.RoundHalfEven),
				EventAppInvoked:                          v.EventAppInvoked,
				EventPayPurchaseAmountFirstDay:           libUtils.ToRound(v.EventPayPurchaseAmountFirstDay, 2, libUtils.RoundHalfEven),
				EventPayPurchaseAmountOneDayByConversion: libUtils.ToRound(v.EventPayPurchaseAmountOneDayByConversion, 2, libUtils.RoundHalfEven),
				EventPayPurchaseAmountWeekByConversion:   libUtils.ToRound(v.EventPayPurchaseAmountWeekByConversion, 2, libUtils.RoundHalfEven),
				EventPayPurchaseAmountThreeDayByConversion: libUtils.ToRound(v.EventPayPurchaseAmountThreeDayByConversion, 2, libUtils.RoundHalfEven),
				Conversion:                      v.Conversion,
				T0DirectConversionCnt:           v.T0DirectConversionCnt,
				Negative:                        v.Negative,
				Report:                          v.Report,
				Block:                           v.Block,
				Comment:                         v.Comment,
				EventPayFirstDay:                v.EventPayFirstDay,
				PlayedNum:                       v.PlayedNum,
				PlayedThreeSeconds:              v.PlayedThreeSeconds,
				AdPhotoPlayed75Percent:          v.AdPhotoPlayed75Percent,
				PlayedEnd:                       v.PlayedEnd,
				Follow:                          v.Follow,
				EventNewUserPay:                 v.EventNewUserPay,
				AdItemClick:                     v.AdItemClick,
				T7PaiedCnt:                      v.T7PaiedCnt,
				T7PaiedAmt:                      libUtils.ToRound(v.T7PaiedAmt, 2, libUtils.RoundHalfEven),
				ConversionNumByImpression7D:     v.ConversionNumByImpression7D,
				DeepConversionNumByImpression7D: v.DeepConversionNumByImpression7D,
				ConversionNum:                   v.ConversionNum,
				DeepConversionNum:               v.DeepConversionNum,
				T0PaiedCnt:                      v.T0PaiedCnt,
				T0PaiedAmt:                      libUtils.ToRound(v.T0PaiedAmt, 2, libUtils.RoundHalfEven),
				Play3SRatio:                     libUtils.ToRound(v.Play3SRatio, 2, libUtils.RoundHalfEven),
				AdPhotoPlayed75PercentRatio:     libUtils.ToRound(v.AdPhotoPlayed75PercentRatio, 2, libUtils.RoundHalfEven),
				T7PaiedRoi:                      libUtils.ToRound(v.T7PaiedRoi, 2, libUtils.RoundHalfEven),
				T0PaiedRoi:                      libUtils.ToRound(v.T0PaiedRoi, 2, libUtils.RoundHalfEven),
				PhotoClickRatio:                 libUtils.ToRound(v.PhotoClickRatio, 2, libUtils.RoundHalfEven),

				EventPayRoi:          libUtils.ToRound(v.EventPayRoi, 2, libUtils.RoundHalfEven),
				EventAppInvokedCost:  libUtils.ToRound(v.EventAppInvokedCost, 2, libUtils.RoundHalfEven),
				EventAppInvokedRatio: libUtils.ToRound(v.EventAppInvokedRatio, 2, libUtils.RoundHalfEven),

				EventPayFirstDayRoi:                           libUtils.ToRound(v.EventPayFirstDayRoi, 2, libUtils.RoundHalfEven),
				EventPayPurchaseAmountOneDayByConversionRoi:   libUtils.ToRound(v.EventPayPurchaseAmountOneDayByConversionRoi, 2, libUtils.RoundHalfEven),
				EventPayPurchaseAmountThreeDayByConversionRoi: libUtils.ToRound(v.EventPayPurchaseAmountThreeDayByConversionRoi, 2, libUtils.RoundHalfEven),
				EventPayPurchaseAmountWeekByConversionRoi:     libUtils.ToRound(v.EventPayPurchaseAmountWeekByConversionRoi, 2, libUtils.RoundHalfEven),
				PhotoClickCost:                    libUtils.ToRound(v.PhotoClickCost, 2, libUtils.RoundHalfEven),
				Impression1KCost:                  libUtils.ToRound(v.Impression1KCost, 2, libUtils.RoundHalfEven),
				Click1KCost:                       libUtils.ToRound(v.Click1KCost, 2, libUtils.RoundHalfEven),
				ActionCost:                        libUtils.ToRound(v.ActionCost, 2, libUtils.RoundHalfEven),
				DeepConversionCostByImpression7D:  libUtils.ToRound(v.DeepConversionCostByImpression7D, 2, libUtils.RoundHalfEven),
				DeepConversionRatioByImpression7D: libUtils.ToRound(v.DeepConversionRatioByImpression7D, 2, libUtils.RoundHalfEven),

				ActionRatio:                   libUtils.ToRound(v.ActionRatio, 2, libUtils.RoundHalfEven),
				PlayEndRatio:                  libUtils.ToRound(v.PlayEndRatio, 2, libUtils.RoundHalfEven),
				EventNewUserPayCost:           libUtils.ToRound(v.EventNewUserPayCost, 2, libUtils.RoundHalfEven),
				EventNewUserPayRatio:          libUtils.ToRound(v.EventNewUserPayRatio, 2, libUtils.RoundHalfEven),
				ActionNewRatio:                libUtils.ToRound(v.ActionNewRatio, 2, libUtils.RoundHalfEven),
				ConversionCostByImpression7D:  libUtils.ToRound(v.ConversionCostByImpression7D, 2, libUtils.RoundHalfEven),
				ConversionRatioByImpression7D: libUtils.ToRound(v.ConversionRatioByImpression7D, 2, libUtils.RoundHalfEven),
				Date:                          v.Date,
				KeyAction:                     v.KeyAction,
				AdPhotoPlayed75PercentRatio2:  libUtils.ToRound(v.AdPhotoPlayed75PercentRatio2, 2, libUtils.RoundHalfEven),
				AccountId:                     v.AccountId,
				SeriesId:                      v.SeriesId,
				RechargeRate:                  libUtils.ToRound(v.RechargeRate, 2, libUtils.RoundHalfEven),
				MiniGameIaaRoi:                libUtils.ToRound(v.MiniGameIaaRoi, 2, libUtils.RoundHalfEven),
				MiniGameIaaPurchaseAmount:     libUtils.ToRound(v.MiniGameIaaPurchaseAmount, 2, libUtils.RoundHalfEven),
				CreateTime:                    v.CreateTime,
			}
			if ent.TotalCharge > 0 {
				// 7 日 累计roi
				ent.T7PaiedRoi = libUtils.DivideAndRound(ent.T7PaiedAmt, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				// 付费 roi
				ent.EventPayRoi = libUtils.DivideAndRound(ent.EventPayPurchaseAmount, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				// IAA广告变现ROI
				ent.MiniGameIaaRoi = libUtils.DivideAndRound(ent.MiniGameIaaPurchaseAmount, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				// 当日roi
				ent.T0PaiedRoi = libUtils.DivideAndRound(ent.T0PaiedAmt, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				ent.EventPayFirstDayRoi = libUtils.DivideAndRound(ent.EventPayPurchaseAmountFirstDay, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				ent.EventPayPurchaseAmountOneDayByConversionRoi = libUtils.DivideAndRound(ent.EventPayPurchaseAmountOneDayByConversion, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				ent.EventPayPurchaseAmountThreeDayByConversionRoi = libUtils.DivideAndRound(ent.EventPayPurchaseAmountWeekByConversion, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				ent.EventPayPurchaseAmountWeekByConversionRoi = libUtils.DivideAndRound(ent.EventPayPurchaseAmountThreeDayByConversion, ent.TotalCharge/1000, 2, libUtils.RoundHalfEven)
				ent.EventPayCost = libUtils.DivideAndRound(ent.TotalCharge/1000, float64(v.EventPay), 2, libUtils.RoundHalfEven)
				ent.ConversionCost = libUtils.DivideAndRound(ent.TotalCharge/1000, float64(v.Conversion), 2, libUtils.RoundHalfEven)
				ent.EventPayFirstDayCost = libUtils.DivideAndRound(ent.TotalCharge/1000, float64(v.EventPayFirstDay), 2, libUtils.RoundHalfEven)
				ent.EventAppInvokedCost = libUtils.DivideAndRound(ent.TotalCharge/1000, float64(v.EventAppInvoked), 2, libUtils.RoundHalfEven)
				ent.EventNewUserPayCost = libUtils.DivideAndRound(ent.TotalCharge/1000, float64(v.EventNewUserPay), 2, libUtils.RoundHalfEven)
			}

			listRes.List[k] = ent
		}
		aList, _ := service.KsAdInfo().GetByAIds(ctx, aidList)
		for _, item := range listRes.List {
			for _, adInfo := range aList {
				if item.AdvertiserId == adInfo.AdvertiserId {
					item.AdAccountName = adInfo.AdAccountName
					item.AccountMainName = adInfo.AccountMainName
				}
			}
		}
	})
	return
}

func (s *sKsAdReportStats) GetExportData(ctx context.Context, req *model.KsAdReportStatsSearchReq) (listRes []*model.KsAdReportStatsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdReportStats.Ctx(ctx).WithAll()
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.Id != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Id+" = ?", req.Id)
		}
		if req.Likes != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Likes+" = ?", gconv.Int(req.Likes))
		}
		if req.Share != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Share+" = ?", gconv.Int(req.Share))
		}
		if req.PhotoClick != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PhotoClick+" = ?", gconv.Int(req.PhotoClick))
		}
		if req.Impression != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Impression+" = ?", gconv.Int(req.Impression))
		}
		if req.EventPay != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventPay+" = ?", gconv.Int(req.EventPay))
		}
		if req.T0DirectPaiedCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T0DirectPaiedCnt+" = ?", gconv.Int(req.T0DirectPaiedCnt))
		}
		if req.AdShow != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdShow+" = ?", gconv.Int(req.AdShow))
		}
		if req.EventAppInvoked != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventAppInvoked+" = ?", gconv.Int(req.EventAppInvoked))
		}
		if req.Conversion != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Conversion+" = ?", gconv.Int(req.Conversion))
		}
		if req.T0DirectConversionCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T0DirectConversionCnt+" = ?", gconv.Int(req.T0DirectConversionCnt))
		}
		if req.Negative != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Negative+" = ?", gconv.Int(req.Negative))
		}
		if req.Report != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Report+" = ?", gconv.Int(req.Report))
		}
		if req.Block != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Block+" = ?", gconv.Int(req.Block))
		}
		if req.Comment != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Comment+" = ?", gconv.Int(req.Comment))
		}
		if req.EventPayFirstDay != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventPayFirstDay+" = ?", gconv.Int(req.EventPayFirstDay))
		}
		if req.PlayedNum != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PlayedNum+" = ?", gconv.Int(req.PlayedNum))
		}
		if req.PlayedThreeSeconds != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PlayedThreeSeconds+" = ?", gconv.Int(req.PlayedThreeSeconds))
		}
		if req.AdPhotoPlayed75Percent != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdPhotoPlayed75Percent+" = ?", gconv.Int(req.AdPhotoPlayed75Percent))
		}
		if req.PlayedEnd != "" {
			m = m.Where(dao.KsAdReportStats.Columns().PlayedEnd+" = ?", gconv.Int(req.PlayedEnd))
		}
		if req.Follow != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Follow+" = ?", gconv.Int(req.Follow))
		}
		if req.EventNewUserPay != "" {
			m = m.Where(dao.KsAdReportStats.Columns().EventNewUserPay+" = ?", gconv.Int(req.EventNewUserPay))
		}
		if req.AdItemClick != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AdItemClick+" = ?", gconv.Int(req.AdItemClick))
		}
		if req.T7PaiedCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T7PaiedCnt+" = ?", gconv.Int(req.T7PaiedCnt))
		}
		if req.ConversionNumByImpression7D != "" {
			m = m.Where(dao.KsAdReportStats.Columns().ConversionNumByImpression7D+" = ?", gconv.Int(req.ConversionNumByImpression7D))
		}
		if req.DeepConversionNumByImpression7D != "" {
			m = m.Where(dao.KsAdReportStats.Columns().DeepConversionNumByImpression7D+" = ?", gconv.Int(req.DeepConversionNumByImpression7D))
		}
		if req.ConversionNum != "" {
			m = m.Where(dao.KsAdReportStats.Columns().ConversionNum+" = ?", gconv.Int(req.ConversionNum))
		}
		if req.DeepConversionNum != "" {
			m = m.Where(dao.KsAdReportStats.Columns().DeepConversionNum+" = ?", gconv.Int(req.DeepConversionNum))
		}
		if req.T0PaiedCnt != "" {
			m = m.Where(dao.KsAdReportStats.Columns().T0PaiedCnt+" = ?", gconv.Int(req.T0PaiedCnt))
		}
		if req.Date != "" {
			m = m.Where(dao.KsAdReportStats.Columns().Date+" = ?", gconv.Time(req.Date))
		}
		if req.KeyAction != "" {
			m = m.Where(dao.KsAdReportStats.Columns().KeyAction+" = ?", gconv.Int64(req.KeyAction))
		}
		if req.AccountId != "" {
			m = m.Where(dao.KsAdReportStats.Columns().AccountId+" = ?", gconv.Int64(req.AccountId))
		}
		if len(req.SeriesIds) > 0 {
			m = m.WhereIn(dao.KsAdReportStats.Columns().SeriesId, req.SeriesIds)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.KsAdReportStats.Columns().CreateTime+" = ?", req.CreateTime)
		}
		if len(req.StartDate) > 0 && len(req.EndDate) > 0 {
			m = m.Where(dao.KsAdReportStats.Columns().Date+" >= ?", req.StartDate)
			m = m.Where(dao.KsAdReportStats.Columns().Date+" <= ?", req.EndDate)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "advertiser_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sKsAdReportStats) GetById(ctx context.Context, advertiserId int64) (res *model.KsAdReportStatsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdReportStats.Ctx(ctx).WithAll().Where(dao.KsAdReportStats.Columns().Id, advertiserId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdReportStats) Add(ctx context.Context, req *model.KsAdReportStatsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdReportStats.Ctx(ctx).Insert(do.KsAdReportStats{
			AdvertiserId:                             req.AdvertiserId,
			Likes:                                    req.Likes,
			Share:                                    req.Share,
			PhotoClick:                               req.PhotoClick,
			Impression:                               req.Impression,
			EventPay:                                 req.EventPay,
			T0DirectPaiedCnt:                         req.T0DirectPaiedCnt,
			EventPayPurchaseAmount:                   req.EventPayPurchaseAmount,
			T0DirectPaiedAmt:                         req.T0DirectPaiedAmt,
			AdShow:                                   req.AdShow,
			TotalCharge:                              req.TotalCharge,
			EventAppInvoked:                          req.EventAppInvoked,
			EventPayPurchaseAmountFirstDay:           req.EventPayPurchaseAmountFirstDay,
			EventPayPurchaseAmountOneDayByConversion: req.EventPayPurchaseAmountOneDayByConversion,
			EventPayPurchaseAmountWeekByConversion:   req.EventPayPurchaseAmountWeekByConversion,
			EventPayPurchaseAmountThreeDayByConversion: req.EventPayPurchaseAmountThreeDayByConversion,
			Conversion:                      req.Conversion,
			T0DirectConversionCnt:           req.T0DirectConversionCnt,
			Negative:                        req.Negative,
			Report:                          req.Report,
			Block:                           req.Block,
			Comment:                         req.Comment,
			EventPayFirstDay:                req.EventPayFirstDay,
			PlayedNum:                       req.PlayedNum,
			PlayedThreeSeconds:              req.PlayedThreeSeconds,
			AdPhotoPlayed75Percent:          req.AdPhotoPlayed75Percent,
			PlayedEnd:                       req.PlayedEnd,
			Follow:                          req.Follow,
			EventNewUserPay:                 req.EventNewUserPay,
			AdItemClick:                     req.AdItemClick,
			T7PaiedCnt:                      req.T7PaiedCnt,
			T7PaiedAmt:                      req.T7PaiedAmt,
			ConversionNumByImpression7D:     req.ConversionNumByImpression7D,
			DeepConversionNumByImpression7D: req.DeepConversionNumByImpression7D,
			ConversionNum:                   req.ConversionNum,
			DeepConversionNum:               req.DeepConversionNum,
			T0PaiedCnt:                      req.T0PaiedCnt,
			T0PaiedAmt:                      req.T0PaiedAmt,
			Play3SRatio:                     req.Play3SRatio,
			AdPhotoPlayed75PercentRatio:     req.AdPhotoPlayed75PercentRatio,
			T7PaiedRoi:                      req.T7PaiedRoi,
			T0PaiedRoi:                      req.T0PaiedRoi,
			PhotoClickRatio:                 req.PhotoClickRatio,
			EventPayCost:                    req.EventPayCost,
			EventPayRoi:                     req.EventPayRoi,
			EventAppInvokedCost:             req.EventAppInvokedCost,
			EventAppInvokedRatio:            req.EventAppInvokedRatio,
			ConversionCost:                  req.ConversionCost,
			EventPayFirstDayRoi:             req.EventPayFirstDayRoi,
			EventPayPurchaseAmountOneDayByConversionRoi:   req.EventPayPurchaseAmountOneDayByConversionRoi,
			EventPayPurchaseAmountThreeDayByConversionRoi: req.EventPayPurchaseAmountThreeDayByConversionRoi,
			EventPayPurchaseAmountWeekByConversionRoi:     req.EventPayPurchaseAmountWeekByConversionRoi,
			PhotoClickCost:                    req.PhotoClickCost,
			Impression1KCost:                  req.Impression1KCost,
			Click1KCost:                       req.Click1KCost,
			ActionCost:                        req.ActionCost,
			DeepConversionCostByImpression7D:  req.DeepConversionCostByImpression7D,
			DeepConversionRatioByImpression7D: req.DeepConversionRatioByImpression7D,
			EventPayFirstDayCost:              req.EventPayFirstDayCost,
			ActionRatio:                       req.ActionRatio,
			PlayEndRatio:                      req.PlayEndRatio,
			EventNewUserPayCost:               req.EventNewUserPayCost,
			EventNewUserPayRatio:              req.EventNewUserPayRatio,
			ActionNewRatio:                    req.ActionNewRatio,
			ConversionCostByImpression7D:      req.ConversionCostByImpression7D,
			ConversionRatioByImpression7D:     req.ConversionRatioByImpression7D,
			Date:                              req.Date,
			KeyAction:                         req.KeyAction,
			AdPhotoPlayed75PercentRatio2:      req.AdPhotoPlayed75PercentRatio2,
			AccountId:                         req.AccountId,
			SeriesId:                          req.SeriesId,
			RechargeRate:                      req.RechargeRate,
			MiniGameIaaRoi:                    req.MiniGameIaaRoi,
			MiniGameIaaPurchaseAmount:         req.MiniGameIaaPurchaseAmount,
			CreateTime:                        req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdReportStats) Edit(ctx context.Context, req *model.KsAdReportStatsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdReportStats.Ctx(ctx).WherePri(req.AdvertiserId).Update(do.KsAdReportStats{
			Likes:                                    req.Likes,
			Share:                                    req.Share,
			PhotoClick:                               req.PhotoClick,
			Impression:                               req.Impression,
			EventPay:                                 req.EventPay,
			T0DirectPaiedCnt:                         req.T0DirectPaiedCnt,
			EventPayPurchaseAmount:                   req.EventPayPurchaseAmount,
			T0DirectPaiedAmt:                         req.T0DirectPaiedAmt,
			AdShow:                                   req.AdShow,
			TotalCharge:                              req.TotalCharge,
			EventAppInvoked:                          req.EventAppInvoked,
			EventPayPurchaseAmountFirstDay:           req.EventPayPurchaseAmountFirstDay,
			EventPayPurchaseAmountOneDayByConversion: req.EventPayPurchaseAmountOneDayByConversion,
			EventPayPurchaseAmountWeekByConversion:   req.EventPayPurchaseAmountWeekByConversion,
			EventPayPurchaseAmountThreeDayByConversion: req.EventPayPurchaseAmountThreeDayByConversion,
			Conversion:                      req.Conversion,
			T0DirectConversionCnt:           req.T0DirectConversionCnt,
			Negative:                        req.Negative,
			Report:                          req.Report,
			Block:                           req.Block,
			Comment:                         req.Comment,
			EventPayFirstDay:                req.EventPayFirstDay,
			PlayedNum:                       req.PlayedNum,
			PlayedThreeSeconds:              req.PlayedThreeSeconds,
			AdPhotoPlayed75Percent:          req.AdPhotoPlayed75Percent,
			PlayedEnd:                       req.PlayedEnd,
			Follow:                          req.Follow,
			EventNewUserPay:                 req.EventNewUserPay,
			AdItemClick:                     req.AdItemClick,
			T7PaiedCnt:                      req.T7PaiedCnt,
			T7PaiedAmt:                      req.T7PaiedAmt,
			ConversionNumByImpression7D:     req.ConversionNumByImpression7D,
			DeepConversionNumByImpression7D: req.DeepConversionNumByImpression7D,
			ConversionNum:                   req.ConversionNum,
			DeepConversionNum:               req.DeepConversionNum,
			T0PaiedCnt:                      req.T0PaiedCnt,
			T0PaiedAmt:                      req.T0PaiedAmt,
			Play3SRatio:                     req.Play3SRatio,
			AdPhotoPlayed75PercentRatio:     req.AdPhotoPlayed75PercentRatio,
			T7PaiedRoi:                      req.T7PaiedRoi,
			T0PaiedRoi:                      req.T0PaiedRoi,
			PhotoClickRatio:                 req.PhotoClickRatio,
			EventPayCost:                    req.EventPayCost,
			EventPayRoi:                     req.EventPayRoi,
			EventAppInvokedCost:             req.EventAppInvokedCost,
			EventAppInvokedRatio:            req.EventAppInvokedRatio,
			ConversionCost:                  req.ConversionCost,
			EventPayFirstDayRoi:             req.EventPayFirstDayRoi,
			EventPayPurchaseAmountOneDayByConversionRoi:   req.EventPayPurchaseAmountOneDayByConversionRoi,
			EventPayPurchaseAmountThreeDayByConversionRoi: req.EventPayPurchaseAmountThreeDayByConversionRoi,
			EventPayPurchaseAmountWeekByConversionRoi:     req.EventPayPurchaseAmountWeekByConversionRoi,
			PhotoClickCost:                    req.PhotoClickCost,
			Impression1KCost:                  req.Impression1KCost,
			Click1KCost:                       req.Click1KCost,
			ActionCost:                        req.ActionCost,
			DeepConversionCostByImpression7D:  req.DeepConversionCostByImpression7D,
			DeepConversionRatioByImpression7D: req.DeepConversionRatioByImpression7D,
			EventPayFirstDayCost:              req.EventPayFirstDayCost,
			ActionRatio:                       req.ActionRatio,
			PlayEndRatio:                      req.PlayEndRatio,
			EventNewUserPayCost:               req.EventNewUserPayCost,
			EventNewUserPayRatio:              req.EventNewUserPayRatio,
			ActionNewRatio:                    req.ActionNewRatio,
			ConversionCostByImpression7D:      req.ConversionCostByImpression7D,
			ConversionRatioByImpression7D:     req.ConversionRatioByImpression7D,
			Date:                              req.Date,
			KeyAction:                         req.KeyAction,
			AdPhotoPlayed75PercentRatio2:      req.AdPhotoPlayed75PercentRatio2,
			AccountId:                         req.AccountId,
			SeriesId:                          req.SeriesId,
			RechargeRate:                      req.RechargeRate,
			MiniGameIaaRoi:                    req.MiniGameIaaRoi,
			MiniGameIaaPurchaseAmount:         req.MiniGameIaaPurchaseAmount,
			CreateTime:                        req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdReportStats) Delete(ctx context.Context, advertiserIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdReportStats.Ctx(ctx).Delete(dao.KsAdReportStats.Columns().Id+" in (?)", advertiserIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
