// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:37
// 生成路径: internal/app/adx/model/entity/adx_playlet.go
// 生成人：cq
// desc:ADX短剧基本信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxPlaylet is the golang structure for table adx_playlet.
type AdxPlaylet struct {
	gmeta.Meta          `orm:"table:adx_playlet, do:true"`
	PlayletId           interface{} `orm:"playlet_id,primary" json:"playletId"`              // 短剧ID
	PlayletName         interface{} `orm:"playlet_name" json:"playletName"`                  // 短剧名称
	CoverOss            interface{} `orm:"cover_oss" json:"coverOss"`                        // 封面OSS地址
	Description         interface{} `orm:"description" json:"description"`                   // 短剧描述
	ReleaseStartDate    interface{} `orm:"release_start_date" json:"releaseStartDate"`       // 首次投放日期
	TotalEpisode        interface{} `orm:"total_episode" json:"totalEpisode"`                // 总集数
	ContractorList      interface{} `orm:"contractor_list" json:"contractorList"`            // 承制方列表
	CopyrightHolderList interface{} `orm:"copyright_holder_list" json:"copyrightHolderList"` // 版权方列表
	RelatedPartyList    interface{} `orm:"related_party_list" json:"relatedPartyList"`       // 关联方列表
	Tags                interface{} `orm:"tags" json:"tags"`                                 // 短剧标签
}
