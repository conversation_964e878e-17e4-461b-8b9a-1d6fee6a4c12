// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-16 11:09:45
// 生成路径: api/v1/system/sys_user_wx_ad_config.go
// 生成人：cq
// desc:投手微信广告位ID配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// SysUserWxAdConfigSearchReq 分页请求参数
type SysUserWxAdConfigSearchReq struct {
	g.Meta `path:"/list" tags:"投手微信广告位ID配置" method:"post" summary:"投手微信广告位ID配置列表"`
	commonApi.Author
	model.SysUserWxAdConfigSearchReq
}

// SysUserWxAdConfigSearchRes 列表返回结果
type SysUserWxAdConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SysUserWxAdConfigSearchRes
}

// SysUserWxAdConfigAddReq 添加操作请求参数
type SysUserWxAdConfigAddReq struct {
	g.Meta `path:"/add" tags:"投手微信广告位ID配置" method:"post" summary:"投手微信广告位ID配置添加"`
	commonApi.Author
	*model.SysUserWxAdConfigAddReq
}

// SysUserWxAdConfigAddRes 添加操作返回结果
type SysUserWxAdConfigAddRes struct {
	commonApi.EmptyRes
	Id int64 `json:"id"`
}

// SysUserWxAdConfigEditReq 修改操作请求参数
type SysUserWxAdConfigEditReq struct {
	g.Meta `path:"/edit" tags:"投手微信广告位ID配置" method:"put" summary:"投手微信广告位ID配置修改"`
	commonApi.Author
	*model.SysUserWxAdConfigEditReq
}

// SysUserWxAdConfigEditRes 修改操作返回结果
type SysUserWxAdConfigEditRes struct {
	commonApi.EmptyRes
}

// SysUserWxAdConfigGetReq 获取一条数据请求
type SysUserWxAdConfigGetReq struct {
	g.Meta `path:"/get" tags:"投手微信广告位ID配置" method:"get" summary:"获取投手微信广告位ID配置信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// SysUserWxAdConfigGetRes 获取一条数据结果
type SysUserWxAdConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SysUserWxAdConfigInfoRes
}

// SysUserWxAdConfigGetByAdUnitIdReq 获取一条数据请求
type SysUserWxAdConfigGetByAdUnitIdReq struct {
	g.Meta `path:"/getByAdUnitId" tags:"投手微信广告位ID配置" method:"get" summary:"校验广告位ID是否已绑定"`
	commonApi.Author
	AdUnitId string `p:"adUnitId" v:"required#广告位ID必须"`
}

// SysUserWxAdConfigGetByAdUnitIdRes 获取一条数据结果
type SysUserWxAdConfigGetByAdUnitIdRes struct {
	g.Meta `mime:"application/json"`
	*model.SysUserWxAdConfigInfoRes
}

// SysUserWxAdConfigDeleteReq 删除数据请求
type SysUserWxAdConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"投手微信广告位ID配置" method:"post" summary:"删除投手微信广告位ID配置"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SysUserWxAdConfigDeleteRes 删除数据返回
type SysUserWxAdConfigDeleteRes struct {
	commonApi.EmptyRes
}
