/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletCreateV30MaxPaymentTierRange
type ToolsWechatAppletCreateV30MaxPaymentTierRange string

// List of tools_wechat_applet_create_v3.0_max_payment_tier_range
const (
	ABOVE_1000_ToolsWechatAppletCreateV30MaxPaymentTierRange       ToolsWechatAppletCreateV30MaxPaymentTierRange = "ABOVE_1000"
	BELOW_500_ToolsWechatAppletCreateV30MaxPaymentTierRange        ToolsWechatAppletCreateV30MaxPaymentTierRange = "BELOW_500"
	FROM_500_TO_1000_ToolsWechatAppletCreateV30MaxPaymentTierRange ToolsWechatAppletCreateV30MaxPaymentTierRange = "FROM_500_TO_1000"
)

// Ptr returns reference to tools_wechat_applet_create_v3.0_max_payment_tier_range value
func (v ToolsWechatAppletCreateV30MaxPaymentTierRange) Ptr() *ToolsWechatAppletCreateV30MaxPaymentTierRange {
	return &v
}
