// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-08-28 15:47:10
// 生成路径: internal/app/applet/controller/product_detail.go
// 生成人：cq
// desc:商品详情表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type productDetailController struct {
	systemController.BaseController
}

var ProductDetail = new(productDetailController)

// List 列表
func (c *productDetailController) List(ctx context.Context, req *applet.ProductDetailSearchReq) (res *applet.ProductDetailSearchRes, err error) {
	res = new(applet.ProductDetailSearchRes)
	res.ProductDetailSearchRes, err = service.ProductDetail().List(ctx, &req.ProductDetailSearchReq)
	return
}

// Get 获取商品详情表
func (c *productDetailController) Get(ctx context.Context, req *applet.ProductDetailGetReq) (res *applet.ProductDetailGetRes, err error) {
	res = new(applet.ProductDetailGetRes)
	res.ProductDetailInfoRes, err = service.ProductDetail().GetById(ctx, req.Id)
	return
}

// Add 添加商品详情表
func (c *productDetailController) Add(ctx context.Context, req *applet.ProductDetailAddReq) (res *applet.ProductDetailAddRes, err error) {
	err = service.ProductDetail().Add(ctx, req.ProductDetailAddReq)
	return
}

// Edit 修改商品详情表
func (c *productDetailController) Edit(ctx context.Context, req *applet.ProductDetailEditReq) (res *applet.ProductDetailEditRes, err error) {
	err = service.ProductDetail().Edit(ctx, req.ProductDetailEditReq)
	return
}

// Delete 删除商品详情表
func (c *productDetailController) Delete(ctx context.Context, req *applet.ProductDetailDeleteReq) (res *applet.ProductDetailDeleteRes, err error) {
	err = service.ProductDetail().Delete(ctx, req.Ids)
	return
}
