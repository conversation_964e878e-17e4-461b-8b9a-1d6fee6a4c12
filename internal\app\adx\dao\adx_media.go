// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-06-05 11:33:33
// 生成路径: internal/app/adx/dao/adx_media.go
// 生成人：cq
// desc:ADX媒体信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/adx/dao/internal"
)

// adxMediaDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adxMediaDao struct {
	*internal.AdxMediaDao
}

var (
	// AdxMedia is globally public accessible object for table tools_gen_table operations.
	AdxMedia = adxMediaDao{
		internal.NewAdxMediaDao(),
	}
)

// Fill with you ideas below.
