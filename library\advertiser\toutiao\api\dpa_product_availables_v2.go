/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DpaProductAvailablesV2ApiService DpaProductAvailablesV2Api service
type DpaProductAvailablesV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DpaProductAvailablesV2Request
}

type DpaProductAvailablesV2Request struct {
	AdvertiserId *int64
}

func (r *DpaProductAvailablesV2ApiService) SetCfg(cfg *conf.Configuration) *DpaProductAvailablesV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DpaProductAvailablesV2ApiService) DpaProductAvailablesV2Request(dpaProductAvailablesV2Request DpaProductAvailablesV2Request) *DpaProductAvailablesV2ApiService {
	r.Request = &dpaProductAvailablesV2Request
	return r
}

func (r *DpaProductAvailablesV2ApiService) AccessToken(accessToken string) *DpaProductAvailablesV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DpaProductAvailablesV2ApiService) Do() (data *models.DpaProductAvailablesV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dpa/product/availables/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.DpaProductAvailablesV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DpaProductAvailablesV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dpa/product/availables/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
