// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-16 15:34:40
// 生成路径: internal/app/ad/model/entity/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAnchorPointImages is the golang structure for table ad_anchor_point_images.
type AdAnchorPointImages struct {
	gmeta.Meta     `orm:"table:ad_anchor_point_images, do:true"`
	Id             interface{} `orm:"id,primary" json:"id"`                   //
	AnchorPointId  interface{} `orm:"anchor_point_id" json:"anchorPointId"`   // 锚点id
	Uri            interface{} `orm:"uri" json:"uri"`                         // url 地址
	Width          interface{} `orm:"width" json:"width"`                     // 宽
	Height         interface{} `orm:"height" json:"height"`                   // 高
	Loading        interface{} `orm:"loading" json:"loading"`                 // 0 ， 1 暂时不知道干嘛的
	ClMaterialId   interface{} `orm:"cl_material_id" json:"clMaterialId"`     // 关联的素材id
	ClThumbnailUri interface{} `orm:"cl_thumbnail_uri" json:"clThumbnailUri"` // 关联饿素材url
	ImageType      interface{} `orm:"image_type" json:"imageType"`            // 图片类型，分别对应头图、图标、应用图像'head_image', 'icon_image', 'app_image'
}
