// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/dao/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// dzAdUserInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzAdUserInfoDao struct {
	*internal.DzAdUserInfoDao
}

var (
	// DzAdUserInfo is globally public accessible object for table tools_gen_table operations.
	DzAdUserInfo = dzAdUserInfoDao{
		internal.NewDzAdUserInfoDao(),
	}
)

// Fill with you ideas below.
