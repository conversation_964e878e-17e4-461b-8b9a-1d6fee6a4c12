// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/model/entity/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdBatchTask is the golang structure for table ad_batch_task.
type AdBatchTask struct {
	gmeta.Meta `orm:"table:ad_batch_task, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`          //
	TaskId     interface{} `orm:"task_id" json:"taskId"`         // 任务ID
	TaskName   interface{} `orm:"task_name" json:"taskName"`     // 任务名称
	MediaType  interface{} `orm:"media_type" json:"mediaType"`   // 媒体类型 1：巨量
	OptObject  interface{} `orm:"opt_object" json:"optObject"`   // 操作对象类型 1：账户 2：项目 3：广告
	OptType    interface{} `orm:"opt_type" json:"optType"`       // 操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像
	OptNum     interface{} `orm:"opt_num" json:"optNum"`         // 操作数量
	OptStatus  interface{} `orm:"opt_status" json:"optStatus"`   // 执行状态：EXECUTING：执行中  COMPLETED：执行完成
	SuccessNum interface{} `orm:"success_num" json:"successNum"` // 成功数量
	FailNum    interface{} `orm:"fail_num" json:"failNum"`       // 失败数量
	UserId     interface{} `orm:"user_id" json:"userId"`         // 归属人员
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"`   // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt"`   // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt"`   // 删除时间
}
