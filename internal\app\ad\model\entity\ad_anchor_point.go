// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/model/entity/ad_anchor_point.go
// 生成人：cyao
// desc:锚点表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdAnchorPoint is the golang structure for table ad_anchor_point.
type AdAnchorPoint struct {
	gmeta.Meta         `orm:"table:ad_anchor_point"`
	Id                 int         `orm:"id,primary" json:"id"`                           // ID
	AnchorPointName    string      `orm:"anchor_point_name" json:"anchorPointName"`       // Anchor Point 名称
	ToolTitle          string      `orm:"tool_title" json:"toolTitle"`                    // 工具标题
	AnchorType         string      `orm:"anchor_type" json:"anchorType"`                  // Anchor 类型
	AndroidAnchorTitle string      `orm:"android_anchor_title" json:"androidAnchorTitle"` // Android Anchor 标题
	IosAnchorTitle     string      `orm:"ios_anchor_title" json:"iosAnchorTitle"`         // iOS Anchor 标题
	AppTags            string      `orm:"app_tags" json:"appTags"`                        // APP标签
	GuideText          string      `orm:"guide_text" json:"guideText"`                    // 引导文案
	AnchorImageMode    int         `orm:"anchor_image_mode" json:"anchorImageMode"`       // Anchor 图像模式  1 横图 2竖图
	GameDescription    string      `orm:"game_description" json:"gameDescription"`        // 游戏描述
	AppDescription     string      `orm:"app_description" json:"appDescription"`          // 应用描述
	GameCharatoristic  string      `orm:"game_charatoristic" json:"gameCharatoristic"`    // 游戏特点
	OtherDescription   string      `orm:"other_description" json:"otherDescription"`      // 其他描述
	MainUserId         int64       `orm:"main_user_id" json:"mainUserId"`                 // 主用户ID
	CreateTime         *gtime.Time `orm:"create_time" json:"createTime"`                  // 创建时间
}
