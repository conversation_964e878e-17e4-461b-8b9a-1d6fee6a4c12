// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/dao/internal/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DzAdUserInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type DzAdUserInfoDao struct {
	table   string              // Table is the underlying table name of the DAO.
	group   string              // Group is the database configuration group name of current DAO.
	columns DzAdUserInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// DzAdUserInfoColumns defines and stores column names for table dz_ad_user_info.
type DzAdUserInfoColumns struct {
	UserId       string // 用户ID
	Time         string // 请求时间戳，单位毫秒
	ChannelId    string // 渠道ID
	AppId        string // 小程序ID
	PromotionId  string // 推广班组ID
	OpenId       string // openID
	AdId         string // 广告ID
	BookId       string // 书籍ID
	ProjectId    string // 广告计划ID
	ClickId      string // 广告点击ID
	UnionId      string // unionID
	RegisterTime string // 用户注册时间戳，单位毫秒
	DyeTime      string // 用户染色时间戳，单位毫秒
	CreatedAt    string // 创建时间
}

var dzAdUserInfoColumns = DzAdUserInfoColumns{
	UserId:       "user_id",
	Time:         "time",
	ChannelId:    "channel_id",
	AppId:        "app_id",
	PromotionId:  "promotion_id",
	OpenId:       "open_id",
	AdId:         "ad_id",
	BookId:       "book_id",
	ProjectId:    "project_id",
	ClickId:      "click_id",
	UnionId:      "union_id",
	RegisterTime: "register_time",
	DyeTime:      "dye_time",
	CreatedAt:    "created_at",
}

// NewDzAdUserInfoDao creates and returns a new DAO object for table data access.
func NewDzAdUserInfoDao() *DzAdUserInfoDao {
	return &DzAdUserInfoDao{
		group:   "default",
		table:   "dz_ad_user_info",
		columns: dzAdUserInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *DzAdUserInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *DzAdUserInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *DzAdUserInfoDao) Columns() DzAdUserInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *DzAdUserInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *DzAdUserInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *DzAdUserInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
