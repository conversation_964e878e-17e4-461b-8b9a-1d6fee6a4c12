// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: internal/app/ad/logic/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdCommonAssetCategory(New())
}

func New() service.IAdCommonAssetCategory {
	return &sAdCommonAssetCategory{}
}

type sAdCommonAssetCategory struct{}

func (s *sAdCommonAssetCategory) List(ctx context.Context, req *model.AdCommonAssetCategorySearchReq) (listRes *model.AdCommonAssetCategorySearchRes, err error) {
	listRes = new(model.AdCommonAssetCategorySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdCommonAssetCategory.Ctx(ctx).WithAll()
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdPlanSetting.Columns().UserId, userIds)
		}
		if req.Category != "" {
			m = m.WhereLike(dao.AdCommonAssetCategory.Columns().Category, "%"+req.Category+"%")
		}
		if req.CategoryIds != nil {
			m = m.WhereIn(dao.AdCommonAssetCategory.Columns().Id, req.CategoryIds)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdCommonAssetCategoryListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdCommonAssetCategoryListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdCommonAssetCategoryListRes{
				Id:        v.Id,
				Category:  v.Category,
				UserId:    v.UserId,
				CreatedAt: v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdCommonAssetCategory) GetById(ctx context.Context, id int) (res *model.AdCommonAssetCategoryInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdCommonAssetCategory.Ctx(ctx).WithAll().Where(dao.AdCommonAssetCategory.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdCommonAssetCategory) Add(ctx context.Context, req *model.AdCommonAssetCategoryAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		user := sysService.Context().GetLoginUser(ctx)
		_, err = dao.AdCommonAssetCategory.Ctx(ctx).Insert(do.AdCommonAssetCategory{
			Category: req.Category,
			UserId:   user.Id,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdCommonAssetCategory) Edit(ctx context.Context, req *model.AdCommonAssetCategoryEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdCommonAssetCategory.Ctx(ctx).WherePri(req.Id).Update(do.AdCommonAssetCategory{
			Category: req.Category,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdCommonAssetCategory) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdCommonAssetCategory.Ctx(ctx).Delete(dao.AdCommonAssetCategory.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
