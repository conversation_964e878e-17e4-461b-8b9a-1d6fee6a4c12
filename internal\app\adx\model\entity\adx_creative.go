// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-06-05 11:33:13
// 生成路径: internal/app/adx/model/entity/adx_creative.go
// 生成人：cq
// desc:ADX短剧广告计划表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdxCreative is the golang structure for table adx_creative.
type AdxCreative struct {
	gmeta.Meta    `orm:"table:adx_creative"`
	CreativeId    uint64           `orm:"creative_id,primary" json:"creativeId"` // 计划ID
	FirstSeen     string           `orm:"first_seen" json:"firstSeen"`           // 首次出现日期
	LastSeen      string           `orm:"last_seen" json:"lastSeen"`             // 最后出现日期
	MaterialId    uint64           `orm:"material_id" json:"materialId"`         // 素材ID
	MediaId       uint             `orm:"media_id" json:"mediaId"`               // 媒体ID
	MobileType    uint             `orm:"mobile_type" json:"mobileType"`         // 投放平台1.安卓2.苹果
	NumDate       map[string]int64 `orm:"num_date" json:"numDate"`               // 投放日期及次数
	PositionId    uint             `orm:"position_id" json:"positionId"`         // 广告位ID
	ProductId     uint64           `orm:"product_id" json:"productId"`           // 投放产品ID
	PublisherId   uint64           `orm:"publisher_id" json:"publisherId"`       // 公司ID
	TalentAccount string           `orm:"talent_account" json:"talentAccount"`   // 达人账号
	TalentName    string           `orm:"talent_name" json:"talentName"`         // 达人名称
	TargetUrl     string           `orm:"target_url" json:"targetUrl"`           // 落地页
	TargetUrlId   uint64           `orm:"target_url_id" json:"targetUrlId"`      // 落地页ID
	Title1        string           `orm:"title1" json:"title1"`                  // 使用的标题
	Title2        string           `orm:"title2" json:"title2"`                  // 使用的文案
}
