/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// NativeAnchorGetV30ApiService NativeAnchorGetV30Api service
type NativeAnchorGetV30ApiService struct {
	ctx          context.Context
	cfg          *conf.Configuration
	token        string
	advertiserId *int64
	page         *int32
	pageSize     *int32
	filtering    *models.NativeAnchorGetV30Filtering
}

func (r *NativeAnchorGetV30ApiService) SetCfg(cfg *conf.Configuration) *NativeAnchorGetV30ApiService {
	r.cfg = cfg
	return r
}

func (r *NativeAnchorGetV30ApiService) AccessToken(accessToken string) *NativeAnchorGetV30ApiService {
	r.token = accessToken
	return r
}

func (r *NativeAnchorGetV30ApiService) AdvertiserId(advertiserId int64) *NativeAnchorGetV30ApiService {
	r.advertiserId = &advertiserId
	return r
}

func (r *NativeAnchorGetV30ApiService) Page(page int32) *NativeAnchorGetV30ApiService {
	r.page = &page
	return r
}

func (r *NativeAnchorGetV30ApiService) PageSize(pageSize int32) *NativeAnchorGetV30ApiService {
	r.pageSize = &pageSize
	return r
}

func (r *NativeAnchorGetV30ApiService) Filtering(filtering models.NativeAnchorGetV30Filtering) *NativeAnchorGetV30ApiService {
	r.filtering = &filtering
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *NativeAnchorGetV30ApiService) Do() (data *models.NativeAnchorGetV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/native_anchor/get/"
	localVarQueryParams := map[string]string{}
	if r.advertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)

	if r.page != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.page)
	}
	if r.pageSize != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.pageSize)
	}
	if r.filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.filtering)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.NativeAnchorGetV30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.NativeAnchorGetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/native_anchor/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}

// Execute executes the request
//
//	@return NativeAnchorGetV30Response
//func (a *NativeAnchorGetV30ApiService) getExecute(r *ApiOpenApiV30NativeAnchorGetGetRequest) (*NativeAnchorGetV30Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodGet
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *NativeAnchorGetV30Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/v3.0/native_anchor/get/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//	if r.advertiserId == nil {
//		return localVarReturnValue, nil, ReportError("advertiserId is required and must be specified")
//	}
//
//	parameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
//	if r.page != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "page", r.page)
//	}
//	if r.pageSize != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.pageSize)
//	}
//	if r.filtering != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.filtering)
//	}
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
