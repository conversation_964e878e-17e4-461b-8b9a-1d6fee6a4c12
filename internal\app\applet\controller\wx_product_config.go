// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-03-08 18:20:26
// 生成路径: internal/app/applet/controller/wx_product_config.go
// 生成人：lx
// desc:产品配置（充值）
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type wxProductConfigController struct {
	systemController.BaseController
}

var WxProductConfig = new(wxProductConfigController)

// List 列表
func (c *wxProductConfigController) List(ctx context.Context, req *applet.WxProductConfigSearchReq) (res *applet.WxProductConfigSearchRes, err error) {
	res = new(applet.WxProductConfigSearchRes)
	res.WxProductConfigSearchRes, err = service.WxProductConfig().List(ctx, &req.WxProductConfigSearchReq)
	return
}

// Get 获取产品配置（充值）
func (c *wxProductConfigController) Get(ctx context.Context, req *applet.WxProductConfigGetReq) (res *applet.WxProductConfigGetRes, err error) {
	res = new(applet.WxProductConfigGetRes)
	res.WxProductConfigInfoRes, err = service.WxProductConfig().GetById(ctx, req.Id)
	return
}

// Add 添加产品配置（充值）
func (c *wxProductConfigController) Add(ctx context.Context, req *applet.WxProductConfigAddReq) (res *applet.WxProductConfigAddRes, err error) {
	err = service.WxProductConfig().Add(ctx, req.WxProductConfigAddReq)
	return
}

// Publish 发布道具
func (c *wxProductConfigController) Publish(ctx context.Context, req *applet.WxProductConfigPublishReq) (res *applet.WxProductConfigPublishRes, err error) {
	err = service.WxProductConfig().Publish(ctx, req.Ids)
	return
}

// SetStatus 启用禁用道具
func (c *wxProductConfigController) SetStatus(ctx context.Context, req *applet.WxProductConfigSetStatusReq) (res *applet.WxProductConfigSetStatusRes, err error) {
	err = service.WxProductConfig().SetStatus(ctx, req.Ids, req.Status)
	return
}

// Edit 修改产品配置（充值）
func (c *wxProductConfigController) Edit(ctx context.Context, req *applet.WxProductConfigEditReq) (res *applet.WxProductConfigEditRes, err error) {
	err = service.WxProductConfig().Edit(ctx, req.WxProductConfigEditReq)
	return
}

// Delete 删除产品配置（充值）
func (c *wxProductConfigController) Delete(ctx context.Context, req *applet.WxProductConfigDeleteReq) (res *applet.WxProductConfigDeleteRes, err error) {
	err = service.WxProductConfig().Delete(ctx, req.Ids)
	return
}
func (c *wxProductConfigController) GetPriceList(ctx context.Context, req *applet.WxProductConfigGetPriceReq) (res []*model.WxProductConfigListRes, err error) {

	res, err = service.WxProductConfig().GetPriceList(ctx, req)
	return
}
