// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-16 11:16:19
// 生成路径: internal/app/ad/model/fq_ad_account_depts.go
// 生成人：gfast
// desc:番茄账号权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdAccountDeptsInfoRes is the golang structure for table fq_ad_account_depts.
type FqAdAccountDeptsInfoRes struct {
	gmeta.Meta    `orm:"table:fq_ad_account_depts"`
	DistributorId int64 `orm:"distributor_id,primary" json:"distributorId" dc:"渠道ID"` // 渠道ID
	DetpId        int   `orm:"detp_id,primary" json:"detpId" dc:"部门ID"`               // 部门ID
}

type FqAdAccountDeptsListRes struct {
	DistributorId int64 `json:"distributorId" dc:"渠道ID"`
	DetpId        int   `json:"detpId" dc:"部门ID"`
}

// FqAdAccountDeptsSearchReq 分页请求参数
type FqAdAccountDeptsSearchReq struct {
	comModel.PageReq
	DistributorId  string  `p:"distributorId" dc:"渠道ID"`  //渠道ID
	DistributorIds []int64 `p:"distributorId" dc:"渠道IDs"` //渠道IDs
	DetpId         string  `p:"detpId" dc:"部门ID"`         //部门ID
}

// FqAdAccountDeptsSearchRes 列表返回结果
type FqAdAccountDeptsSearchRes struct {
	comModel.ListRes
	List []*FqAdAccountDeptsListRes `json:"list"`
}

// FqAdAccountDeptsAddReq 添加操作请求参数
type FqAdAccountDeptsAddReq struct {
	DistributorId int64 `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
}

// FqAdAccountDeptsEditReq 修改操作请求参数
type FqAdAccountDeptsEditReq struct {
	DistributorId int64 `p:"distributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
}
