/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// ToolsOrangeSiteGetV3ApiService ToolsOrangeSiteGetV3Api service
type ToolsOrangeSiteGetV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi3ToolsOrangeSiteGetGetRequest
}

type ApiOpenApi3ToolsOrangeSiteGetGetRequest struct {
	AdvertiserId *int64 `p:"advertiserId" v:"required#广告主ID必须"`
	Page         *int32 `p:"page" v:"required#页码必须"`
	PageSize     *int32 `p:"page_size" v:"required#每页数量必须"`
	OptimizeGoal *models.ToolsOrangeSiteGetV30OptimizeGoal
	Status       *models.ToolsOrangeSiteGetV30Status
	Filtering    *models.ToolsOrangeSiteGetV30Filtering
}

func (r *ToolsOrangeSiteGetV3ApiService) SetCfg(cfg *conf.Configuration) *ToolsOrangeSiteGetV3ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsOrangeSiteGetV3ApiService) SetRequest(req ApiOpenApi3ToolsOrangeSiteGetGetRequest) *ToolsOrangeSiteGetV3ApiService {
	r.Request = &req
	return r
}

func (r *ToolsOrangeSiteGetV3ApiService) AccessToken(accessToken string) *ToolsOrangeSiteGetV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsOrangeSiteGetV3ApiService) Do() (data *models.ToolsOrangeSiteGetV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/tools/orange_site/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if *r.Request.AdvertiserId < 1 {
		return nil, errors.New("advertiserId must be greater than 1")
	}
	if r.Request.Page == nil {
		return nil, errors.New("page is required and must be specified")
	}
	if *r.Request.Page < 1 {
		return nil, errors.New("page must be greater than 1")
	}
	if r.Request.PageSize == nil {
		return nil, errors.New("pageSize is required and must be specified")
	}
	if *r.Request.PageSize < 1 {
		return nil, errors.New("pageSize must be greater than 1")
	}
	if r.Request.OptimizeGoal == nil {
		return nil, errors.New("optimizeGoal is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	if r.Request.Status != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "status", r.Request.Status)
	}
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.Request.Filtering)
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "optimize_goal", r.Request.OptimizeGoal)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.ToolsOrangeSiteGetV30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsOrangeSiteGetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/tools/orange_site/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
