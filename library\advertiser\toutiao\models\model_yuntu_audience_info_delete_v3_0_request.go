/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceInfoDeleteV30Request struct for YuntuAudienceInfoDeleteV30Request
type YuntuAudienceInfoDeleteV30Request struct {
	// 广告主ID，集品牌虚拟adv_id
	AdvertiserId int64 `json:"advertiser_id"`
	// 人群包ID
	CustomAudienceId int64 `json:"custom_audience_id"`
	// 服务商id
	ServiceProviderId int64 `json:"service_provider_id"`
	// 品牌id
	YuntuBrandId int64 `json:"yuntu_brand_id"`
}
