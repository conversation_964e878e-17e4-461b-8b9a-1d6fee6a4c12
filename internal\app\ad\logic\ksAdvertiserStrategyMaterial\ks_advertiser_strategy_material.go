// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-22 11:52:28
// 生成路径: internal/app/ad/logic/ks_advertiser_strategy_material.go
// 生成人：cq
// desc:快手策略组-素材
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterKsAdvertiserStrategyMaterial(New())
}

func New() service.IKsAdvertiserStrategyMaterial {
	return &sKsAdvertiserStrategyMaterial{}
}

type sKsAdvertiserStrategyMaterial struct{}

func (s *sKsAdvertiserStrategyMaterial) List(ctx context.Context, req *model.KsAdvertiserStrategyMaterialSearchReq) (listRes *model.KsAdvertiserStrategyMaterialSearchRes, err error) {
	listRes = new(model.KsAdvertiserStrategyMaterialSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyMaterial.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().Id+" = ?", req.Id)
		}
		if req.StrategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().StrategyId+" = ?", req.StrategyId)
		}
		if req.TaskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.MaterialAllocationMethod != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().MaterialAllocationMethod+" = ?", req.MaterialAllocationMethod)
		}
		if req.VideoCount != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().VideoCount+" = ?", gconv.Int(req.VideoCount))
		}
		if req.VideoLocked != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().VideoLocked+" = ?", gconv.Int(req.VideoLocked))
		}
		if req.ImageCount != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().ImageCount+" = ?", gconv.Int(req.ImageCount))
		}
		if req.ImageLocked != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().ImageLocked+" = ?", gconv.Int(req.ImageLocked))
		}
		if req.DescriptionMatchMethod != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().DescriptionMatchMethod+" = ?", req.DescriptionMatchMethod)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().CreatedAt+" >=? AND "+dao.KsAdvertiserStrategyMaterial.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.KsAdvertiserStrategyMaterialListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.KsAdvertiserStrategyMaterialListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.KsAdvertiserStrategyMaterialListRes{
				Id:                       v.Id,
				StrategyId:               v.StrategyId,
				TaskId:                   v.TaskId,
				MaterialAllocationMethod: v.MaterialAllocationMethod,
				VideoCount:               v.VideoCount,
				VideoLocked:              v.VideoLocked,
				ImageCount:               v.ImageCount,
				ImageLocked:              v.ImageLocked,
				DescriptionMatchMethod:   v.DescriptionMatchMethod,
				CreativeMaterialData:     v.CreativeMaterialData,
				CreatedAt:                v.CreatedAt,
			}
		}
	})
	return
}

func (s *sKsAdvertiserStrategyMaterial) GetById(ctx context.Context, id uint64) (res *model.KsAdvertiserStrategyMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdvertiserStrategyMaterial.Ctx(ctx).WithAll().Where(dao.KsAdvertiserStrategyMaterial.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyMaterial) GetInfoById(ctx context.Context, strategyId string, taskId string) (res *model.KsAdvertiserStrategyMaterialInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdvertiserStrategyMaterial.Ctx(ctx).WithAll()
		if strategyId != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().StrategyId+" = ?", strategyId)
		}
		if taskId != "" {
			m = m.Where(dao.KsAdvertiserStrategyMaterial.Columns().TaskId+" = ?", taskId)
		}
		err = m.Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdvertiserStrategyMaterial) Add(ctx context.Context, req *model.KsAdvertiserStrategyMaterialAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyMaterial.Ctx(ctx).Insert(do.KsAdvertiserStrategyMaterial{
			StrategyId:               req.StrategyId,
			TaskId:                   req.TaskId,
			MaterialAllocationMethod: req.MaterialAllocationMethod,
			VideoCount:               req.VideoCount,
			VideoLocked:              req.VideoLocked,
			ImageCount:               req.ImageCount,
			ImageLocked:              req.ImageLocked,
			DescriptionMatchMethod:   req.DescriptionMatchMethod,
			CreativeMaterialData:     req.CreativeMaterialData,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdvertiserStrategyMaterial) Edit(ctx context.Context, req *model.KsAdvertiserStrategyMaterialEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyMaterial.Ctx(ctx).
			Where(dao.KsAdvertiserStrategyMaterial.Columns().StrategyId, req.StrategyId).
			Update(do.KsAdvertiserStrategyMaterial{
				TaskId:                   req.TaskId,
				MaterialAllocationMethod: req.MaterialAllocationMethod,
				VideoCount:               req.VideoCount,
				VideoLocked:              req.VideoLocked,
				ImageCount:               req.ImageCount,
				ImageLocked:              req.ImageLocked,
				DescriptionMatchMethod:   req.DescriptionMatchMethod,
				CreativeMaterialData:     req.CreativeMaterialData,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdvertiserStrategyMaterial) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdvertiserStrategyMaterial.Ctx(ctx).Delete(dao.KsAdvertiserStrategyMaterial.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdvertiserStrategyMaterial) UpLoad(ctx context.Context, aId string, fileId int) (result *model.UpLoadVideoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		result = new(model.UpLoadVideoRes)
		if exist, mediaId, _ := service.AdMaterialUpload().IsExist(ctx, aId, fileId); exist {
			result.PhotoId = mediaId
			return
		}
		file := new(model.AdMaterialInfoRes)
		err = dao.AdMaterial.Ctx(ctx).Where(dao.AdMaterial.Columns().MaterialId, fileId).Scan(&file)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		if file.MaterialType == consts.VideoMaterialType {
			result, err = service.KsAdvertiserAccountInfo().UpLoadVideo(ctx, &model.UpLoadVideoReq{
				AdvertiserId: gconv.Int64(aId),
				FileUrl:      file.FileUri,
			})
			// 存储到 ad_material_upload 表格
			_, err = dao.AdMaterialUpload.Ctx(ctx).Insert(do.AdMaterialUpload{
				MediaType:    2,
				AdMaterialId: file.MaterialId,
				AdvertiserId: gconv.Int(aId),
				MediaId:      &result.PhotoId,
				MaterialName: file.MaterialName,
				Size:         file.FileSize,
				Width:        file.Width,
				Height:       file.Height,
				AdType:       1,
				Url:          file.FileUri,
				Format:       file.FileFormat,
				MaterialId:   &result.PhotoId,
				Signature:    &result.Signature,
				Duration:     file.VideoDuration,
				CreatedAt:    gtime.Now(),
			})
			liberr.ErrIsNil(ctx, err, "插入表格AdMaterialUpload失败！")

			return
		}
		// 6. 发起上传
		accessToken, err := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, gconv.Int64(aId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		data, uploadErr := ksApi.GetKSApiClient().AdImageUploadService.
			AccessToken(accessToken).
			SetReq(ksApi.AdImageUploadReq{
				AdvertiserId: gconv.Int64(aId),
				UploadType:   2,
				Url:          file.FileUri,
			}).
			Do()
		if uploadErr != nil || data == nil || data.Data == nil {
			errMsg := fmt.Sprintf("上传图片失败！err:%v, req:%+v, token:%s, result:%+v", uploadErr, file.FileUri, accessToken, result)
			g.Log().Error(ctx, errMsg)
			liberr.ErrIsNil(ctx, errors.New("上传图片失败"), errMsg)
		}

		// 7. 入库
		insertReq := do.AdMaterialUpload{
			MediaType:    1,
			AdMaterialId: file.MaterialId,
			AdvertiserId: gconv.Int64(aId),
			MediaId:      data.Data.PicId,
			MaterialName: file.MaterialName,
			Size:         data.Data.Size,
			Width:        data.Data.Width,
			Height:       data.Data.Height,
			Url:          file.FileUri,
			AdType:       2,
			Format:       data.Data.Format,
			MaterialId:   gconv.Int64(data.Data.PicId),
			CreatedAt:    gtime.Now(),
		}
		_, err = dao.AdMaterialUpload.Ctx(ctx).Insert(insertReq)
		liberr.ErrIsNil(ctx, err, "插入 AdMaterialUpload 失败")
		// 8. 返回结果
		result.PicId = data.Data.PicId
	})
	return
}

//UpLoadByUrl

func (s *sKsAdvertiserStrategyMaterial) UpLoadByUrl(ctx context.Context, aId, fileUrl string, isVideo bool) (result *model.UpLoadVideoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		result = new(model.UpLoadVideoRes)

		if isVideo {
			result, err = service.KsAdvertiserAccountInfo().UpLoadVideo(ctx, &model.UpLoadVideoReq{
				AdvertiserId: gconv.Int64(aId),
				FileUrl:      fileUrl,
			})
			return
		}
		// 6. 发起上传
		accessToken, err := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, gconv.Int64(aId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		data, uploadErr := ksApi.GetKSApiClient().AdImageUploadService.
			AccessToken(accessToken).
			SetReq(ksApi.AdImageUploadReq{
				AdvertiserId: gconv.Int64(aId),
				UploadType:   2,
				Url:          fileUrl,
			}).
			Do()
		if uploadErr != nil || data == nil || data.Data == nil {
			errMsg := fmt.Sprintf("上传图片失败！err:%v, req:%+v, token:%s, result:%+v", uploadErr, fileUrl, accessToken, result)
			g.Log().Error(ctx, errMsg)
			liberr.ErrIsNil(ctx, errors.New("上传图片失败"), errMsg)
		}

		// 8. 返回结果
		result.PicId = data.Data.PicId
		result.Signature = data.Data.Signature
	})
	return
}
