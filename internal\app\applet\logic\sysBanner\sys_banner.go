// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-04-03 11:14:04
// 生成路径: internal/app/applet/logic/sys_banner.go
// 生成人：cyao
// desc:新banner表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	linkedSPlatRulesDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	linkedTheaterInfoDao "github.com/tiger1103/gfast/v3/internal/app/theater/dao"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterSysBanner(New())
}

func New() service.ISysBanner {
	return &sSysBanner{}
}

type sSysBanner struct{}

func (s *sSysBanner) List(ctx context.Context, req *model.SysBannerSearchReq) (listRes *model.SysBannerSearchRes, err error) {
	listRes = new(model.SysBannerSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SysBanner.Ctx(ctx).WithAll()
		if req.AppId != "" {
			m = m.Where(dao.SysBanner.Columns().AppId+" = ?", req.AppId)
		}
		if req.LinkType != "" {
			m = m.Where(dao.SysBanner.Columns().LinkType+" = ?", gconv.Int(req.LinkType))
		}
		if req.ParentId != "" {
			m = m.Where(dao.SysBanner.Columns().ParentId+" = ?", gconv.Int(req.ParentId))
		}
		if req.Status != "" {
			m = m.Where(dao.SysBanner.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.StartTime != "" {
			m = m.Where(dao.SysBanner.Columns().CreateTime+" >= ?", gconv.Time(req.StartTime))
		}
		if req.EndTime != "" {
			m = m.Where(dao.SysBanner.Columns().CreateTime+" <=> ?", gconv.Time(req.EndTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "create_time desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SysBannerListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SysBannerListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.SysBannerListRes{
				Id:             v.Id,
				ImgUrl:         v.ImgUrl,
				AppId:          v.AppId,
				LinkedAppId:    v.LinkedAppId,
				LinkType:       v.LinkType,
				LinkUrl:        v.LinkUrl,
				ParentId:       v.ParentId,
				LinkedParentId: v.LinkedParentId,
				Remark:         v.Remark,
				Sort:           v.Sort,
				Status:         v.Status,
				UpdateTime:     v.UpdateTime,
				CreateTime:     v.CreateTime,
			}
		}
	})
	return
}

// LinkedDataSearch 相关连表查询数据
func (s *sSysBanner) LinkedSysBannerDataSearch(ctx context.Context) (res *model.LinkedSysBannerDataSearchRes, err error) {
	res = new(model.LinkedSysBannerDataSearchRes)
	res.LinkedSysBannerSPlatRules, err = s.ListSysBannerSPlatRules(ctx)
	liberr.ErrIsNil(ctx, err, "获取关联表信息失败")
	res.LinkedSysBannerTheaterInfo, err = s.ListSysBannerTheaterInfo(ctx)
	liberr.ErrIsNil(ctx, err, "获取关联表信息失败")
	return
}

func (s *sSysBanner) GetById(ctx context.Context, id int) (res *model.SysBannerInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SysBanner.Ctx(ctx).WithAll().Where(dao.SysBanner.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSysBanner) Add(ctx context.Context, req *model.SysBannerAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SysBanner.Ctx(ctx).Insert(do.SysBanner{
			ImgUrl:   req.ImgUrl,
			AppId:    req.AppId,
			LinkType: req.LinkType,
			LinkUrl:  req.LinkUrl,
			ParentId: req.ParentId,
			Remark:   req.Remark,
			Sort:     req.Sort,
			Status:   req.Status,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSysBanner) Edit(ctx context.Context, req *model.SysBannerEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SysBanner.Ctx(ctx).WherePri(req.Id).Update(do.SysBanner{
			ImgUrl:     req.ImgUrl,
			AppId:      req.AppId,
			LinkType:   req.LinkType,
			LinkUrl:    req.LinkUrl,
			ParentId:   req.ParentId,
			Remark:     req.Remark,
			Sort:       req.Sort,
			Status:     req.Status,
			UpdateTime: gtime.Now(),
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSysBanner) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SysBanner.Ctx(ctx).Delete(dao.SysBanner.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sSysBanner) ListSysBannerSPlatRules(ctx context.Context) (linkedSysBannerSPlatRules []*model.LinkedSysBannerSPlatRules, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = linkedSPlatRulesDao.SPlatRules.
			Ctx(ctx).
			Fields(model.LinkedSysBannerSPlatRules{}).Scan(&linkedSysBannerSPlatRules)
		liberr.ErrIsNil(ctx, err)
	})
	return
}

func (s *sSysBanner) ListSysBannerTheaterInfo(ctx context.Context) (linkedSysBannerTheaterInfo []*model.LinkedSysBannerTheaterInfo, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = linkedTheaterInfoDao.TheaterInfo.
			Ctx(ctx).Where(linkedTheaterInfoDao.TheaterInfo.Columns().Status, 0).
			Fields(model.LinkedSysBannerTheaterInfo{}).Scan(&linkedSysBannerTheaterInfo)
		liberr.ErrIsNil(ctx, err)
	})
	return
}
