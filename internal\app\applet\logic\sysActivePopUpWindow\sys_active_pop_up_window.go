// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-09-02 14:44:06
// 生成路径: internal/app/applet/logic/sys_active_pop_up_window.go
// 生成人：cyao
// desc:活动弹出窗口
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/entity"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemDao "github.com/tiger1103/gfast/v3/internal/app/system/dao"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
)

func init() {
	service.RegisterSysActivePopUpWindow(New())
}

func New() service.ISysActivePopUpWindow {
	return &sSysActivePopUpWindow{}
}

type sSysActivePopUpWindow struct{}

func (s *sSysActivePopUpWindow) List(ctx context.Context, req *model.SysActivePopUpWindowSearchReq) (listRes *model.SysActivePopUpWindowSearchRes, err error) {
	listRes = new(model.SysActivePopUpWindowSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SysActivePopUpWindow.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().Id+" = ?", req.Id)
		}
		if req.ImgUrl != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().ImgUrl+" = ?", req.ImgUrl)
		}
		if req.AppId != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().AppId+" = ? or app_id = 'all'", req.AppId)
		} else if len(req.AppIds) > 0 {
			m = m.Where(dao.SysActivePopUpWindow.Columns().AppId+" in (?) or app_id = 'all'", strings.Join(req.AppIds, ","))
		}

		if req.LinkType != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().LinkType+" = ?", gconv.Int(req.LinkType))
		}
		if req.LinkUrl != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().LinkUrl+" = ?", req.LinkUrl)
		}
		if req.ParentId != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().ParentId+" = ?", gconv.Int(req.ParentId))
		}
		if req.Sort != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().Sort+" = ?", gconv.Int(req.Sort))
		}
		if req.Status != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		if req.UpdateTime != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().UpdateTime+" = ?", gconv.Time(req.UpdateTime))
		}
		if req.CreateTime != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		if req.ExpiredTime != "" {
			m = m.Where(dao.SysActivePopUpWindow.Columns().ExpiredTime+" = ?", gconv.Time(req.ExpiredTime))
		}
		m = m.Group(dao.SysActivePopUpWindow.Columns().LinkType, dao.SysActivePopUpWindow.Columns().LinkUrl, dao.SysActivePopUpWindow.Columns().ImgUrl, dao.SysActivePopUpWindow.Columns().Sort, dao.SysActivePopUpWindow.Columns().ExpiredTime, dao.SysActivePopUpWindow.Columns().ParentId)
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "sort desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.SysActivePopUpWindowListRes
		err = m.Fields("img_url as imgUrl").
			Fields("link_type as linkType").
			Fields("link_url as linkUrl").
			Fields("MIN(id) as id").
			Fields("GROUP_CONCAT(id) as idList").
			Fields("GROUP_CONCAT(app_id) as appId").
			Fields("ANY_VALUE(sort) as sort").
			Fields("ANY_VALUE(parent_id) as parentId").
			Fields("ANY_VALUE(status) as status").
			Fields("ANY_VALUE(remark) as remark").
			Fields("ANY_VALUE(expired_time) as expiredTime").
			Fields("ANY_VALUE(update_time) as updateTime").
			Fields("ANY_VALUE(create_time) as createTime").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.SysActivePopUpWindowListRes, len(res))

		//查询小程序列表
		var appletList []systemModel.SPlatRulesInfoRes
		systemDao.SPlatRules.Ctx(ctx).WithAll().Scan(&appletList)
		for k, v := range res {
			appNames := make([]string, 0)
			if len(v.AppId) > 0 {
				appIds := strings.Split(v.AppId, ",")
				for _, appId := range appIds {
					for _, item := range appletList {
						if item.AppId == appId {
							appNames = append(appNames, item.AppName)
							break
						}
					}
				}

			}
			sumUa, sumUc := 0, 0
			if len(v.IdList) > 0 {
				ids := strings.Split(v.IdList, ",")
				for _, v := range ids {
					ua, _ := commonService.GetGoRedis().PFCount(ctx, fmt.Sprintf("PLAT:ACTIVE:UA:%v", v)).Uint64()
					uc, _ := commonService.GetGoRedis().Get(ctx, fmt.Sprintf("PLAT:ACTIVE:UC:%v", v)).Int()
					sumUa += int(ua)
					sumUc += uc
				}

			}
			//for _, item := range appletList {
			//	if item.AppId == v.AppId {
			//		appName = item.AppName
			//		break
			//	}
			//}
			// 活动弹框 key
			//ua, _ := commonService.GetGoRedis().PFCount(ctx, fmt.Sprintf("PLAT:ACTIVE:UA:%v", v.Id)).Uint64()
			//uc, _ := commonService.GetGoRedis().Get(ctx, fmt.Sprintf("PLAT:ACTIVE:UC:%v", v.Id)).Int()
			listRes.List[k] = &model.SysActivePopUpWindowListRes{
				Id:          v.Id,
				IdList:      v.IdList,
				Ids:         libUtils.StringArrayToIntArray(strings.Split(v.IdList, ",")),
				ImgUrl:      v.ImgUrl,
				AppId:       v.AppId,
				AppIds:      strings.Split(v.AppId, ","),
				AppName:     strings.Join(appNames, ","),
				LinkType:    v.LinkType,
				LinkUrl:     v.LinkUrl,
				ParentId:    v.ParentId,
				Remark:      v.Remark,
				Sort:        v.Sort,
				Status:      v.Status,
				UpdateTime:  v.UpdateTime,
				CreateTime:  v.CreateTime,
				ExpiredTime: v.ExpiredTime,
				UA:          sumUa,
				UC:          sumUc,
			}
		}
	})
	return
}

func (s *sSysActivePopUpWindow) GetById(ctx context.Context, id int) (res *model.SysActivePopUpWindowInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SysActivePopUpWindow.Ctx(ctx).WithAll().Where(dao.SysActivePopUpWindow.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSysActivePopUpWindow) Add(ctx context.Context, req *model.SysActivePopUpWindowAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if len(req.AppIds) > 0 {
			for _, id := range req.AppIds {
				_, err = dao.SysActivePopUpWindow.Ctx(ctx).Insert(do.SysActivePopUpWindow{
					ImgUrl:      req.ImgUrl,
					AppId:       id,
					LinkType:    req.LinkType,
					LinkUrl:     req.LinkUrl,
					ParentId:    req.ParentId,
					Remark:      req.Remark,
					Sort:        req.Sort,
					Status:      req.Status,
					UpdateTime:  req.UpdateTime,
					CreateTime:  req.CreateTime,
					ExpiredTime: req.ExpiredTime,
				})
			}
			return
		}

		_, err = dao.SysActivePopUpWindow.Ctx(ctx).Insert(do.SysActivePopUpWindow{
			ImgUrl:      req.ImgUrl,
			AppId:       req.AppId,
			LinkType:    req.LinkType,
			LinkUrl:     req.LinkUrl,
			ParentId:    req.ParentId,
			Remark:      req.Remark,
			Sort:        req.Sort,
			Status:      req.Status,
			UpdateTime:  req.UpdateTime,
			CreateTime:  req.CreateTime,
			ExpiredTime: req.ExpiredTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sSysActivePopUpWindow) Edit(ctx context.Context, req *model.SysActivePopUpWindowEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		scanList := make([]*entity.SysActivePopUpWindow, 0)
		err = dao.SysActivePopUpWindow.Ctx(ctx).WhereIn(dao.SysActivePopUpWindow.Columns().Id, req.Ids).Scan(&scanList)
		for i, _ := range scanList {
			scanList[i].ImgUrl = req.ImgUrl
			scanList[i].LinkType = req.LinkType
			scanList[i].LinkUrl = req.LinkUrl
			scanList[i].ParentId = req.ParentId
			scanList[i].Remark = req.Remark
			scanList[i].Sort = req.Sort
			scanList[i].Status = req.Status
			scanList[i].ExpiredTime = req.ExpiredTime
		}
		updateList := make([]*entity.SysActivePopUpWindow, 0)
		addList := make([]*entity.SysActivePopUpWindow, 0)
		deleteList := make([]*entity.SysActivePopUpWindow, 0)
		// 判断哪些要新增哪些要删除 哪些要修改
		for _, appId := range req.AppIds {
			updated := false
			for _, item := range scanList {
				if appId == item.AppId {
					updateList = append(updateList, item)
					updated = true
					break
				}
			}
			if !updated {
				addList = append(addList, &entity.SysActivePopUpWindow{
					ImgUrl:      req.ImgUrl,
					AppId:       appId,
					LinkType:    req.LinkType,
					LinkUrl:     req.LinkUrl,
					ParentId:    req.ParentId,
					Remark:      req.Remark,
					Sort:        req.Sort,
					Status:      req.Status,
					UpdateTime:  gtime.Now(),
					CreateTime:  gtime.Now(),
					ExpiredTime: req.ExpiredTime,
				})
			}
		}

		// 删除
		for _, item := range scanList {
			updated := false
			for _, appId := range req.AppIds {
				if appId == item.AppId {
					updated = true
					break
				}
			}
			if !updated {
				deleteList = append(deleteList, item)
			}
		}
		if len(addList) > 0 {
			_, err = dao.SysActivePopUpWindow.Ctx(ctx).Insert(addList)
		}
		if len(deleteList) > 0 {
			delIds := make([]int, 0)
			for _, item := range deleteList {
				delIds = append(delIds, item.Id)
			}
			_, err = dao.SysActivePopUpWindow.Ctx(ctx).WhereIn(dao.SysActivePopUpWindow.Columns().Id, delIds).Delete()
		}
		if len(updateList) > 0 {
			_, err = dao.SysActivePopUpWindow.Ctx(ctx).Save(updateList)
		}
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sSysActivePopUpWindow) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.SysActivePopUpWindow.Ctx(ctx).Delete(dao.SysActivePopUpWindow.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
