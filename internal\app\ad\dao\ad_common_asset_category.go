// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-12-11 13:50:30
// 生成路径: internal/app/ad/dao/ad_common_asset_category.go
// 生成人：cq
// desc:通用资产-标题分类
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adCommonAssetCategoryDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adCommonAssetCategoryDao struct {
	*internal.AdCommonAssetCategoryDao
}

var (
	// AdCommonAssetCategory is globally public accessible object for table tools_gen_table operations.
	AdCommonAssetCategory = adCommonAssetCategoryDao{
		internal.NewAdCommonAssetCategoryDao(),
	}
)

// Fill with you ideas below.
