// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-04-03 11:14:04
// 生成路径: internal/app/applet/controller/sys_banner.go
// 生成人：cyao
// desc:新banner表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"errors"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
)

type sysBannerController struct {
	systemController.BaseController
}

var SysBanner = new(sysBannerController)

// List 列表
func (c *sysBannerController) List(ctx context.Context, req *applet.SysBannerSearchReq) (res *applet.SysBannerSearchRes, err error) {
	res = new(applet.SysBannerSearchRes)
	res.SysBannerSearchRes, err = service.SysBanner().List(ctx, &req.SysBannerSearchReq)
	return
}

// LinkedSysBannerDataSearch 相关连表查询数据
func (c *sysBannerController) LinkedSysBannerDataSearch(ctx context.Context, req *applet.LinkedSysBannerDataSearchReq) (res *applet.LinkedSysBannerDataSearchRes, err error) {
	if !systemService.SysUser().AccessRule(ctx, systemService.Context().GetUserId(ctx), "api/v1/applet/sysBanner/list") {
		err = errors.New("没有访问权限")
		return
	}
	res = new(applet.LinkedSysBannerDataSearchRes)
	res.LinkedSysBannerDataSearchRes, err = service.SysBanner().LinkedSysBannerDataSearch(ctx)
	return
}

// Get 获取新banner表
func (c *sysBannerController) Get(ctx context.Context, req *applet.SysBannerGetReq) (res *applet.SysBannerGetRes, err error) {
	res = new(applet.SysBannerGetRes)
	res.SysBannerInfoRes, err = service.SysBanner().GetById(ctx, req.Id)
	return
}

// Add 添加新banner表
func (c *sysBannerController) Add(ctx context.Context, req *applet.SysBannerAddReq) (res *applet.SysBannerAddRes, err error) {
	err = service.SysBanner().Add(ctx, req.SysBannerAddReq)
	return
}

// Edit 修改新banner表
func (c *sysBannerController) Edit(ctx context.Context, req *applet.SysBannerEditReq) (res *applet.SysBannerEditRes, err error) {
	err = service.SysBanner().Edit(ctx, req.SysBannerEditReq)
	return
}

// Delete 删除新banner表
func (c *sysBannerController) Delete(ctx context.Context, req *applet.SysBannerDeleteReq) (res *applet.SysBannerDeleteRes, err error) {
	err = service.SysBanner().Delete(ctx, req.Ids)
	return
}
