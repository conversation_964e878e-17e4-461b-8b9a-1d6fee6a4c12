// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:30
// 生成路径: api/v1/ad/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdAccountDeptsSearchReq 分页请求参数
type DzAdAccountDeptsSearchReq struct {
	g.Meta `path:"/list" tags:"权限和部门关联" method:"get" summary:"权限和部门关联列表"`
	commonApi.Author
	model.DzAdAccountDeptsSearchReq
}

// DzAdAccountDeptsSearchRes 列表返回结果
type DzAdAccountDeptsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountDeptsSearchRes
}

// DzAdAccountDeptsExportReq 导出请求
type DzAdAccountDeptsExportReq struct {
	g.Meta `path:"/export" tags:"权限和部门关联" method:"get" summary:"权限和部门关联导出"`
	commonApi.Author
	model.DzAdAccountDeptsSearchReq
}

// DzAdAccountDeptsExportRes 导出响应
type DzAdAccountDeptsExportRes struct {
	commonApi.EmptyRes
}

// DzAdAccountDeptsAddReq 添加操作请求参数
type DzAdAccountDeptsAddReq struct {
	g.Meta `path:"/add" tags:"权限和部门关联" method:"post" summary:"权限和部门关联添加"`
	commonApi.Author
	*model.DzAdAccountDeptsAddReq
}

// DzAdAccountDeptsAddRes 添加操作返回结果
type DzAdAccountDeptsAddRes struct {
	commonApi.EmptyRes
}

// DzAdAccountDeptsEditReq 修改操作请求参数
type DzAdAccountDeptsEditReq struct {
	g.Meta `path:"/edit" tags:"权限和部门关联" method:"put" summary:"权限和部门关联修改"`
	commonApi.Author
	*model.DzAdAccountDeptsEditReq
}

// DzAdAccountDeptsEditRes 修改操作返回结果
type DzAdAccountDeptsEditRes struct {
	commonApi.EmptyRes
}

// DzAdAccountDeptsGetReq 获取一条数据请求
type DzAdAccountDeptsGetReq struct {
	g.Meta `path:"/get" tags:"权限和部门关联" method:"get" summary:"获取权限和部门关联信息"`
	commonApi.Author
	ChannelId int64 `p:"channelId" v:"required#主键必须"` //通过主键获取
}

// DzAdAccountDeptsGetRes 获取一条数据结果
type DzAdAccountDeptsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdAccountDeptsInfoRes
}

// DzAdAccountDeptsDeleteReq 删除数据请求
type DzAdAccountDeptsDeleteReq struct {
	g.Meta `path:"/delete" tags:"权限和部门关联" method:"delete" summary:"删除权限和部门关联"`
	commonApi.Author
	ChannelIds []int64 `p:"channelIds" v:"required#主键必须"` //通过主键删除
}

// DzAdAccountDeptsDeleteRes 删除数据返回
type DzAdAccountDeptsDeleteRes struct {
	commonApi.EmptyRes
}
