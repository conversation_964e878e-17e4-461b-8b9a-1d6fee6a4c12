/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsSiteTemplateSiteCreateV2BricksButtonEventType
type ToolsSiteTemplateSiteCreateV2BricksButtonEventType string

// List of tools_site_template_site_create_v2_bricks_button_event_type
const (
	APPOINT_EVENT_ToolsSiteTemplateSiteCreateV2BricksButtonEventType   ToolsSiteTemplateSiteCreateV2BricksButtonEventType = "APPOINT_EVENT"
	DOWNLOAD_EVENT_ToolsSiteTemplateSiteCreateV2BricksButtonEventType  ToolsSiteTemplateSiteCreateV2BricksButtonEventType = "DOWNLOAD_EVENT"
	LINK_EVENT_ToolsSiteTemplateSiteCreateV2BricksButtonEventType      ToolsSiteTemplateSiteCreateV2BricksButtonEventType = "LINK_EVENT"
	TELEPHONE_EVENT_ToolsSiteTemplateSiteCreateV2BricksButtonEventType ToolsSiteTemplateSiteCreateV2BricksButtonEventType = "TELEPHONE_EVENT"
)

// Ptr returns reference to tools_site_template_site_create_v2_bricks_button_event_type value
func (v ToolsSiteTemplateSiteCreateV2BricksButtonEventType) Ptr() *ToolsSiteTemplateSiteCreateV2BricksButtonEventType {
	return &v
}
