// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: internal/app/ad/logic/ad_material_album.go
// 生成人：cyao
// desc:广告素材专辑
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	commonModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdMaterialAlbum(New())
}

func New() service.IAdMaterialAlbum {
	return &sAdMaterialAlbum{}
}

type sAdMaterialAlbum struct{}

// List 左侧初始化列表
func (s *sAdMaterialAlbum) List(ctx context.Context, req *model.AdMaterialAlbumSearchReq) (listRes *model.AdMaterialAlbumSearchRes, err error) {
	listRes = new(model.AdMaterialAlbumSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetMaterialContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdMaterialAlbum.Ctx(ctx)
		if req.AlbumId > 0 {
			m = m.Where(dao.AdMaterialAlbum.Columns().AlbumId+" = ?", req.AlbumId)
		}
		if req.AlbumName != "" {
			m = m.Where(dao.AdMaterialAlbum.Columns().AlbumName+" like ?", "%"+req.AlbumName+"%")
		}
		if !admin && len(userIds) > 0 {
			m = m.As("a").
				LeftJoin(dao.AdMaterialAlbumDepts.Table(), "d", " a.album_id = d.album_id").
				LeftJoin(dao.AdMaterialAlbumUsers.Table(), "u", " a.album_id = u.album_id").
				Fields("a.album_id").
				Fields(dao.AdMaterialAlbum.Columns().Remark).
				Fields(dao.AdMaterialAlbum.Columns().AlbumName, "titleName").
				WhereOr("a.user_id in (?)", userIds).
				WhereOr("d.detp_id = ?", userInfo.DeptId).
				WhereOr("u.specify_user_id = ?", userInfo.Id).
				WhereOr("a.scope_authority = 4 ").
				Distinct()

			listRes.Total, err = m.Count()
			liberr.ErrIsNil(ctx, err, "获取总行数失败")
			//Scan(&listRes.List)
			liberr.ErrIsNil(ctx, err, "获取数据失败")

		} else {
			listRes.Total, err = m.Count()
			liberr.ErrIsNil(ctx, err, "获取总行数失败")
			// admin 看所有的
			m = m.WithAll().As("a").
				Fields(dao.AdMaterialAlbum.Columns().AlbumId).
				Fields(dao.AdMaterialAlbum.Columns().Remark).
				Fields(dao.AdMaterialAlbum.Columns().AlbumName, "titleName")
			//Scan(&listRes.List)
		}

		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "a.album_id asc"
		if req.OrderBy != "" {
			order = "a." + req.OrderBy + " " + req.OrderType
		}
		var res []*model.AdMaterialAlbumListRes
		if req.SkipNum > 0 {
			err = m.Limit(req.SkipNum, req.PageSize).Order(order).Scan(&res)
		} else {
			err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdMaterialAlbumListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdMaterialAlbumListRes{
				AlbumId:        v.AlbumId,
				AlbumName:      v.AlbumName,
				UserId:         v.UserId,
				ScopeAuthority: v.ScopeAuthority,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdMaterialAlbum) SpecialProductList(ctx context.Context, req *model.SpecialProductListReq) (listRes *model.SpecialProductListRes, err error) {
	listRes = new(model.SpecialProductListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		if req.KeywordType > 0 {
			listRes, err = s.SpecialProductList2(ctx, req)
			return
		}
		if req.AlbumId > 0 || req.FileId > 0 {
			if req.FileId > 0 { // 如果是具体文件夹可能下面挂了文件夹和素材
				// 查询文件夹
				fileList, err := service.AdMaterialFile().List(ctx, &model.AdMaterialFileSearchReq{
					AlbumId:  req.AlbumId,
					ParentId: req.FileId,
					PageReq:  req.PageReq,
				})
				// 计算要跳过的记录数
				offset := (req.PageNum-1)*req.PageSize - fileList.Total.(int)
				// 剩余需要取的数量
				remainingSize := req.PageReq.PageSize - len(fileList.List)
				req.SkipNum = offset
				req.PageSize = remainingSize
				liberr.ErrIsNil(ctx, err, "获取数据失败")
				listRes.List = getSpecialProductList(fileList.List)
				// 判断当前文件夹下面是否还有文件夹文件夹放前面
				// 判断当前文件夹下面是否还有素材
				if remainingSize > 0 {
					// 素材分页条件
					aList, _ := s.GetSpecialProductMaterialList(ctx, &model.SpecialProductListReq{
						FileId: req.FileId,
						PageReq: commonModel.PageReq{
							//PageNum:   materialPageNum,
							PageSize:  remainingSize, // 剩余需要的条数
							SkipNum:   offset,
							OrderBy:   req.PageReq.OrderBy,
							OrderType: req.PageReq.OrderType,
						},
					})
					if aList != nil {
						listRes.List = append(listRes.List, aList.List...)
						listRes.ListRes.Total = fileList.ListRes.Total.(int) + aList.ListRes.Total.(int)
						listRes.ListRes.CurrentPage = req.PageNum
					} else {
						listRes.ListRes.Total = fileList.ListRes.Total.(int)
						listRes.ListRes.CurrentPage = req.PageNum
					}

				}
			} else { // 专辑下面只能挂载文件夹 直接返回
				fileList, err := service.AdMaterialFile().List(ctx, &model.AdMaterialFileSearchReq{
					AlbumId:  req.AlbumId,
					ParentId: req.FileId,
					PageReq:  req.PageReq,
				})
				//remainingSize -= len(listRes.List)
				liberr.ErrIsNil(ctx, err, "获取数据失败")
				listRes.ListRes = fileList.ListRes
				listRes.List = getSpecialProductList(fileList.List)
			}
		} else {
			listRes, err = s.GetSpecialProductAlbumList(ctx, req)
			return
			// 查询专辑列表根据ids
		}
	})
	return
}

func (s *sAdMaterialAlbum) SpecialProductList2(ctx context.Context, req *model.SpecialProductListReq) (listRes *model.SpecialProductListRes, err error) {
	listRes = new(model.SpecialProductListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		switch req.KeywordType {
		case 1:
			// 根据count 计算查询逻辑
			// 获取各个表格的总记录数
			albumCount, fileCount, materialCount, innerError := GetAllCount(ctx, req.KeyWord)
			liberr.ErrIsNil(ctx, innerError, "获取数据失败")

			// 计算要跳过的记录数
			offset := (req.PageNum - 1) * req.PageSize
			// 剩余需要取的数量
			remainingSize := req.PageReq.PageSize
			listRes.Total = albumCount + fileCount + materialCount

			if offset < albumCount && remainingSize > 0 && albumCount > 0 {
				req.SkipNum = offset
				if req.OrderBy == "name" || req.OrderBy == "album_name" {
					req.OrderBy = "file_name"
				}
				//全部 先查询专辑 查询文件夹 然后查询素材
				listRes, err = s.GetSpecialProductAlbumList(ctx, req)
				if offset > 0 {
					offset -= len(listRes.List)
				}
				remainingSize -= len(listRes.List)
			} else {
				if offset > 0 {
					offset -= albumCount
				}
				remainingSize -= albumCount
			}

			// 2. 查询 AdMaterialFile 表
			if offset < fileCount && remainingSize > 0 && fileCount > 0 {
				// 文件夹
				fileList := new(model.AdMaterialFileSearchRes)
				if req.OrderBy == "name" || req.OrderBy == "album_name" {
					req.OrderBy = "file_name"
				}
				req.SkipNum = offset
				req.PageSize = remainingSize
				fileList, err = service.AdMaterialFile().List(ctx, &model.AdMaterialFileSearchReq{
					AlbumId:  req.AlbumId,
					FileName: req.KeyWord,
					ParentId: req.FileId,
					PageReq:  req.PageReq,
				})
				listRes.List = append(listRes.List, getSpecialProductList(fileList.List)...)
				if offset > 0 {
					offset -= len(fileList.List) // 计算跳过的记录数
				}
				remainingSize -= len(fileList.List)
			} else {
				if offset > 0 {
					offset -= fileCount
				}
				remainingSize -= fileCount
			}

			if offset < materialCount && remainingSize > 0 && materialCount > 0 {
				if req.OrderBy == "name" || req.OrderBy == "album_name" || req.OrderBy == "file_name" {
					req.OrderBy = "name"
				}
				req.SkipNum = offset
				req.PageSize = remainingSize
				aList, _ := s.GetSpecialProductMaterialList(ctx, req)
				listRes.List = append(listRes.List, aList.List...)
			}
			return

		case 2:
			// 专辑
			listRes, err = s.GetSpecialProductAlbumList(ctx, req)
			return
		case 3:
			// 文件夹
			fileList := new(model.AdMaterialFileSearchRes)
			if req.OrderBy == "name" {
				req.OrderBy = "file_name"
			}
			fileList, err = service.AdMaterialFile().List(ctx, &model.AdMaterialFileSearchReq{
				AlbumId:  req.AlbumId,
				FileName: req.KeyWord,
				ParentId: req.FileId,
				PageReq:  req.PageReq,
			})
			listRes.List = getSpecialProductList(fileList.List)
			listRes.Total = fileList.Total
			return
		}
	})
	return
}

func (s *sAdMaterialAlbum) GetSpecialProductMaterialList(ctx context.Context, req *model.SpecialProductListReq) (listRes *model.SpecialProductListRes, err error) {
	listRes = new(model.SpecialProductListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		materialListResp, err := service.AdMaterial().List(ctx, &model.AdMaterialSearchReq{
			FileId:       req.FileId,
			MaterialName: req.KeyWord,
			PageReq:      req.PageReq,
		})
		liberr.ErrIsNil(ctx, err, "获取素材列表失败")
		for _, item := range materialListResp.List {
			listRes.List = append(listRes.List, &model.SpecialProduct{
				Type: 3,
				Material: model.SpecialProductMaterial{
					AlbumID:       item.AlbumId,
					FileID:        item.FileId,
					MaterialID:    item.MaterialId,
					MaterialName:  item.MaterialName,
					MaterialType:  item.MaterialType,
					UserId:        item.UserId,
					FileUri:       item.FileUri,
					ThumbnailUri:  item.ThumbnailUri,
					Labels:        item.Labels,
					FileFormat:    item.FileFormat,
					FileSize:      item.FileSize,
					Width:         item.Width,
					Height:        item.Height,
					Remark:        item.Remark,
					VideoDuration: item.VideoDuration,
					CreatedAt:     item.CreatedAt,
				},
			})
		}
		listRes.ListRes.Total = materialListResp.ListRes.Total
		listRes.ListRes.CurrentPage = materialListResp.ListRes.CurrentPage
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}
func (s *sAdMaterialAlbum) GetSpecialProductAlbumList(ctx context.Context, req *model.SpecialProductListReq) (listRes *model.SpecialProductListRes, err error) {
	listRes = new(model.SpecialProductListRes)
	err = g.Try(ctx, func(ctx context.Context) {
		SearchRes, err := s.List(ctx, &model.AdMaterialAlbumSearchReq{
			PageReq:   req.PageReq,
			AlbumId:   req.AlbumId,
			AlbumName: req.KeyWord,
		})
		listRes.List = make([]*model.SpecialProduct, 0)
		listRes.ListRes = SearchRes.ListRes
		ids := make([]int, 0)
		for _, item := range SearchRes.List {
			ids = append(ids, item.AlbumId)
		}
		materialList, err := service.AdMaterialFile().GetMaterialNumByAId(ctx, ids)
		previewList, err := service.AdMaterialFile().GetPreviewByAId(ctx, ids)
		for _, item := range SearchRes.List {
			listRes.List = append(listRes.List, &model.SpecialProduct{
				Type: 1,
				Album: model.SpecialProductAlbum{
					AlbumID:      item.AlbumId,
					AlbumName:    item.AlbumName,
					Remark:       item.Remark,
					CreateTime:   item.CreatedAt.String(),
					CreateUserID: item.UserId,
					MaterialNum:  getMaterial(materialList, item.AlbumId),
					Preview:      getPreview(previewList, item.AlbumId),
				},
			})
		}
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}
func GetAllCount(ctx context.Context, keyWord string) (albumCount, fileCount, materialCount int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdMaterialAlbum.Ctx(ctx)
		if keyWord != "" {
			m = m.Where("album_name like ?", "%"+keyWord+"%")
		}
		albumCount, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		fileCount, err = dao.AdMaterialFile.Ctx(ctx).Where("file_name like ?", "%"+keyWord+"%").Count()
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		materialCount, err = dao.AdMaterial.Ctx(ctx).Where("material_name like ?", "%"+keyWord+"%").Count()
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

// getSpecialProductList 实体映射
func getSpecialProductList(fileList []*model.AdMaterialFileListRes) (list []*model.SpecialProduct) {
	list = make([]*model.SpecialProduct, 0)
	for _, item := range fileList {
		list = append(list, &model.SpecialProduct{
			Type: 2,
			File: model.SpecialProductFile{
				FileID:       item.FileId,
				FileName:     item.FileName,
				AlbumID:      item.AlbumId,
				Remark:       item.Remark,
				CreateTime:   item.CreatedAt.String(),
				CreateUserID: item.UserId,
				ParentID:     item.ParentId,
				MaterialNum:  item.MaterialNum,
				Preview:      getPreArray(item.Preview),
			},
		})
	}
	return
}

func getPreArray(pr string) []string {
	if len(pr) > 0 {
		return strings.Split(pr, "|")
	}
	return nil
}

func getPreview(list []*model.MaterialNumByAlbumId, albumId int) []string {
	if list == nil || len(list) == 0 {
		return []string{}
	}
	for _, item := range list {
		if item.AlbumId == albumId {
			if len(item.Preview) > 0 {
				return strings.Split(item.Preview, "|")
			}
		}
	}
	return []string{}
}

// 获取素材数量
func getMaterial(list []*model.MaterialNumByAlbumId, albumId int) int {
	for _, item := range list {
		if item.AlbumId == albumId {
			return item.MaterialNum
		}
	}
	return 0
}

func (s *sAdMaterialAlbum) GetByIds(ctx context.Context, albumIds []int) (list []model.SpecialProductAlbum, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialAlbum.Ctx(ctx).WithAll().WhereIn(dao.AdMaterialAlbum.Columns().AlbumId, albumIds).Scan(&list)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialAlbum) GetByAlbumId(ctx context.Context, albumId int) (res *model.AdMaterialAlbumInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialAlbum.Ctx(ctx).WithAll().Where(dao.AdMaterialAlbum.Columns().AlbumId, albumId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		//获取当前部门权限
		res.DeptIds = service.AdMaterialAlbumDepts().GetScopeAuthority(ctx, albumId)
		// 获取当前用户权限
		res.SpecifyUserIds = service.AdMaterialAlbumUsers().GetSpecifyUserIds(ctx, albumId)
	})
	return
}

func (s *sAdMaterialAlbum) GetByAlbumIds(ctx context.Context, albumIds []int) (res []*model.AdMaterialAlbumInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdMaterialAlbum.Ctx(ctx).WithAll().WhereIn(dao.AdMaterialAlbum.Columns().AlbumId, albumIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdMaterialAlbum) Add(ctx context.Context, req *model.AdMaterialAlbumAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		result, err := dao.AdMaterialAlbum.Ctx(ctx).Insert(do.AdMaterialAlbum{
			AlbumName:      req.AlbumName,
			UserId:         req.UserId,
			ScopeAuthority: req.ScopeAuthority,
			Remark:         req.Remark,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
		albumId, _ := result.LastInsertId()
		if len(req.DeptIds) > 0 {
			list := make([]do.AdMaterialAlbumDepts, 0)
			for _, id := range req.DeptIds {
				list = append(list, do.AdMaterialAlbumDepts{
					AlbumId: albumId,
					DetpId:  id,
				})
			}
			dao.AdMaterialAlbumDepts.Ctx(ctx).Save(list)
		}
		if len(req.SpecifyUserIds) > 0 {
			list := make([]do.AdMaterialAlbumUsers, 0)
			for _, id := range req.SpecifyUserIds {
				list = append(list, do.AdMaterialAlbumUsers{
					AlbumId:       albumId,
					SpecifyUserId: id,
				})
			}
			dao.AdMaterialAlbumUsers.Ctx(ctx).Save(list)
		}
	})
	return
}

func (s *sAdMaterialAlbum) Edit(ctx context.Context, req *model.AdMaterialAlbumEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbum.Ctx(ctx).WherePri(req.AlbumId).Update(do.AdMaterialAlbum{
			AlbumName:      req.AlbumName,
			UserId:         req.UserId,
			ScopeAuthority: req.ScopeAuthority,
			Remark:         req.Remark,
		})
		dao.AdMaterialAlbumUsers.Ctx(ctx).Delete(dao.AdMaterialAlbumUsers.Columns().AlbumId, req.AlbumId)
		dao.AdMaterialAlbumDepts.Ctx(ctx).Delete(dao.AdMaterialAlbumDepts.Columns().AlbumId, req.AlbumId)
		if len(req.DeptIds) > 0 {
			list := make([]do.AdMaterialAlbumDepts, 0)
			for _, id := range req.DeptIds {
				list = append(list, do.AdMaterialAlbumDepts{
					AlbumId: req.AlbumId,
					DetpId:  id,
				})
			}
			dao.AdMaterialAlbumDepts.Ctx(ctx).Save(list)
		}
		if len(req.SpecifyUserIds) > 0 {
			list := make([]do.AdMaterialAlbumUsers, 0)
			for _, id := range req.SpecifyUserIds {
				list = append(list, do.AdMaterialAlbumUsers{
					AlbumId:       req.AlbumId,
					SpecifyUserId: id,
				})
			}
			dao.AdMaterialAlbumUsers.Ctx(ctx).Save(list)
		}
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdMaterialAlbum) Delete(ctx context.Context, albumIds []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdMaterialAlbum.Ctx(ctx).Delete(dao.AdMaterialAlbum.Columns().AlbumId+" in (?)", albumIds)
		dao.AdMaterialAlbumUsers.Ctx(ctx).Delete(dao.AdMaterialAlbumUsers.Columns().AlbumId+" in (?)", albumIds)
		dao.AdMaterialAlbumDepts.Ctx(ctx).Delete(dao.AdMaterialAlbumDepts.Columns().AlbumId+" in (?)", albumIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
