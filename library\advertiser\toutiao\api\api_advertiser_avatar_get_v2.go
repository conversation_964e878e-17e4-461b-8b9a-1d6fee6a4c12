/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AdvertiserPublicInfoV2ApiService 获取同主体下广告主图片素材 AdvertiserPublicInfoV2Api service
type AdvertiserAvatarGetV2ApiService struct {
	ctx          context.Context
	cfg          *conf.Configuration
	token        string
	advertiserId *int64
}

func (r *AdvertiserAvatarGetV2ApiService) SetCfg(cfg *conf.Configuration) *AdvertiserAvatarGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AdvertiserAvatarGetV2ApiService) SetRequest(advertiserId int64) *AdvertiserAvatarGetV2ApiService {
	r.advertiserId = &advertiserId
	return r
}

func (r *AdvertiserAvatarGetV2ApiService) AccessToken(accessToken string) *AdvertiserAvatarGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AdvertiserAvatarGetV2ApiService) Do() (data *models.AdvertiserAvatarGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/advertiser/avatar/get/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AdvertiserAvatarGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AdvertiserAvatarGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/advertiser/avatar/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
