/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatAppletListV30DataListPaymentForm
type ToolsWechatAppletListV30DataListPaymentForm string

// List of tools_wechat_applet_list_v3.0_data_list_payment_form
const (
	BOTH_OPTIONS_AVAILABLE_ToolsWechatAppletListV30DataListPaymentForm ToolsWechatAppletListV30DataListPaymentForm = "BOTH_OPTIONS_AVAILABLE"
	CONTENT_OR_SERVICES_ToolsWechatAppletListV30DataListPaymentForm    ToolsWechatAppletListV30DataListPaymentForm = "CONTENT_OR_SERVICES"
	UNLOCK_FULL_FEATURES_ToolsWechatAppletListV30DataListPaymentForm   ToolsWechatAppletListV30DataListPaymentForm = "UNLOCK_FULL_FEATURES"
)

// Ptr returns reference to tools_wechat_applet_list_v3.0_data_list_payment_form value
func (v ToolsWechatAppletListV30DataListPaymentForm) Ptr() *ToolsWechatAppletListV30DataListPaymentForm {
	return &v
}
