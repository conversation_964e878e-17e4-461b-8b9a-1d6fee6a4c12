// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: internal/app/ad/logic/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	channelService "github.com/tiger1103/gfast/v3/internal/app/channel/service"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/libDy/api"
	dyModel "github.com/tiger1103/gfast/v3/library/libDy/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
	"time"
)

func init() {
	service.RegisterMpAdEvent(New())
}

func New() service.IMpAdEvent {
	return &sMpAdEvent{}
}

type sMpAdEvent struct{}

func (s *sMpAdEvent) List(ctx context.Context, req *model.MpAdEventSearchReq) (listRes *model.MpAdEventSearchRes, err error) {
	listRes = new(model.MpAdEventSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.MpAdEvent.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.MpAdEvent.Columns().Id+" = ?", req.Id)
		}
		if req.MpId != "" {
			m = m.Where(dao.MpAdEvent.Columns().MpId+" = ?", req.MpId)
		}
		if req.Cost != "" {
			m = m.Where(dao.MpAdEvent.Columns().Cost+" = ?", req.Cost)
		}
		if req.OpenId != "" {
			m = m.Where(dao.MpAdEvent.Columns().OpenId+" = ?", req.OpenId)
		}
		if req.EventTime != "" {
			m = m.Where(dao.MpAdEvent.Columns().EventTime+" = ?", req.EventTime)
		}
		if req.AdType != "" {
			m = m.Where(dao.MpAdEvent.Columns().AdType+" = ?", req.AdType)
		}
		if req.CreateDate != "" {
			m = m.Where(dao.MpAdEvent.Columns().CreateDate+" = ?", req.CreateDate)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.MpAdEvent.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.MpAdEventListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.MpAdEventListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.MpAdEventListRes{
				Id:         v.Id,
				MpId:       v.MpId,
				Cost:       v.Cost,
				OpenId:     v.OpenId,
				EventTime:  v.EventTime,
				AdType:     v.AdType,
				CreateDate: v.CreateDate,
				CreateTime: v.CreateTime,
			}
		}
	})
	return
}

func (s *sMpAdEvent) GetById(ctx context.Context, id int64) (res *model.MpAdEventInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.MpAdEvent.Ctx(ctx).WithAll().Where(dao.MpAdEvent.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// Pull Data
// innerContext, cancel := context.WithCancel(context.Background())
// defer cancel()
func (s *sMpAdEvent) PullData(ctx context.Context, req *model.MpAdEventPullDataReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for {
			if req.StartTime > req.EndTime {
				break
			}
			// 添加日志
			g.Log().Infof(ctx, "sMpAdEvent开始同步数据：%s", req.StartTime)
			appList, innError := channelService.MMemberAdCallback().GetAppId(ctx, req.StartTime)
			if innError != nil {
				g.Log().Error(ctx, innError)
			}
			for _, item := range appList {
				if len(req.AppId) > 0 && item.AppId != req.AppId {
					continue
				}
				err = s.AddECPMQuery(ctx, item.AppId, req.StartTime)
				if err != nil {
					// AddECPMQuery 添加错误日志
					g.Log().Error(ctx, fmt.Sprintf("-------------  执行 AddECPMQuery err:%v -------------------", err))
				}
			}
			req.StartTime = libUtils.PlusDays(req.StartTime, 1)
		}
	})
	if err != nil {
		g.Log().Error(ctx, fmt.Sprintf("-------------  执行 AddECPMQuery 完成 err: %v-------------------", err.Error()))
	}
	// 执行完成添加日志
	g.Log().Info(ctx, "-------------  执行 AddECPMQuery 完成 -------------------")
	return
}

func (s *sMpAdEvent) SyncMpAdEventTask(ctx context.Context, statDate string) (err error) {
	channelRechargeStatKey := model.MpAdEventDataTask + ":" + statDate
	pool := goredis.NewPool(commonService.GetGoRedis())
	rs := redsync.New(pool)
	mutex := rs.NewMutex(channelRechargeStatKey, redsync.WithTries(1), redsync.WithExpiry(time.Second*20), redsync.WithRetryDelay(50*time.Millisecond))
	if err = mutex.TryLockContext(ctx); err != nil {
		g.Log().Info(ctx, "Redisson没有获取到分布式锁："+channelRechargeStatKey+", TaskName :SyncMpAdEventTask ")
		return err
	}
	defer mutex.UnlockContext(ctx)
	innerCtx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = s.PullData(innerCtx, &model.MpAdEventPullDataReq{
		StartTime: statDate,
		EndTime:   statDate,
	})
	if err != nil {
		g.Log().Error(ctx, "SyncMpAdEventTask err：", err)
	}
	return
}

func (s *sMpAdEvent) AddECPMQuery(ctx context.Context, appId, date string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for hour := 0; hour < 24; hour++ {
			var cursor = "0"
			newDateHour := fmt.Sprintf("%s %02d", date, hour)
			InnerAddECPMQuery(ctx, appId, newDateHour, cursor)

		}
	})
	return
}

func InnerAddECPMQuery(ctx context.Context, appId, date, cursor string) {
	for {
		adRes, err2 := api.DyAd().ECPMQuery2(ctx, appId, &dyModel.ECPMQuery{
			DateHour: date,
			PageSize: 500,
			Cursor:   cursor,
		})
		if err2 != nil {
			if strings.Contains(err2.Error(), "Too many requests") {
				g.Log().Info(ctx, fmt.Sprintf("-------------  执行 AddECPMQuery Too many requests  err:%v -------------------", err2))
				time.Sleep(1 * time.Second)
				adRes, err2 = api.DyAd().ECPMQuery2(ctx, appId, &dyModel.ECPMQuery{
					DateHour: date,
					PageSize: 500,
					Cursor:   cursor,
				})
			} else {
				break
			}
		}

		if adRes.ErrNo == 0 && &adRes.Data != nil {
			// 入库操作
			list := make([]*do.MpAdEvent, 0)
			for _, record := range adRes.Data.Records {
				if record.Cost == "0" {
					continue
				}
				list = append(list, &do.MpAdEvent{
					MpId:       record.MpID,
					Cost:       libUtils.StringParsFloat(record.Cost),
					OpenId:     record.OpenID,
					EventTime:  record.EventTime,
					AdType:     record.AdType,
					CreateDate: libUtils.GetDateBySec(gconv.Int64(record.EventTime)),
					CreateTime: gtime.NewFromStrFormat(libUtils.GetDateBySec2(gconv.Int64(record.EventTime)), time.DateTime),
				})
			}
			if len(list) > 0 {
				_, err := dao.MpAdEvent.Ctx(ctx).InsertIgnore(list)
				if err != nil {
					g.Log().Error(ctx, fmt.Sprintf("-------------  执行 dao.MpAdEvent.Ctx(ctx).Insert err:%v -------------------", err))
				}
			}
			// 添加
			if len(adRes.Data.Records) >= 500 {
				if libUtils.StringParsInt(adRes.Data.NextCursor) > 0 {
					// 继续查下一页
					cursor = adRes.Data.NextCursor
				}
			} else {
				break
			}

		} else {
			break
		}

	}
}

// 比较 数据不一致的进行重新刷新处理 todo 暂时数据没找到问题
func (s *sMpAdEvent) CompareData(ctx context.Context, req *model.MpAdEventPullDataReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 首先查询出当天两个库不同的数据
		//select sum(cost) from m_member_dy_ad_record where  event_time >= UNIX_TIMESTAMP( '2025-06-27' ) AND event_time < UNIX_TIMESTAMP( '2025-06-28' );
		//select create_date, sum(cost) From mp_ad_event GROUP BY create_date order by create_date ;
		// 如果当天金额不一致 则进行数据的同步
		// 修复数据的时候 先补充  m_member_dy_ad_record 的数据 根据 login_account_change 查询当前用户归属
		// 然后比较  m_member_ad_callback
	})
	return
}
func (s *sMpAdEvent) Add(ctx context.Context, req *model.MpAdEventAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.MpAdEvent.Ctx(ctx).Insert(do.MpAdEvent{
			MpId:       req.MpId,
			Cost:       req.Cost,
			OpenId:     req.OpenId,
			EventTime:  req.EventTime,
			AdType:     req.AdType,
			CreateDate: req.CreateDate,
			CreateTime: req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sMpAdEvent) Edit(ctx context.Context, req *model.MpAdEventEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.MpAdEvent.Ctx(ctx).WherePri(req.Id).Update(do.MpAdEvent{
			MpId:       req.MpId,
			Cost:       req.Cost,
			OpenId:     req.OpenId,
			EventTime:  req.EventTime,
			AdType:     req.AdType,
			CreateDate: req.CreateDate,
			CreateTime: req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sMpAdEvent) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.MpAdEvent.Ctx(ctx).Delete(dao.MpAdEvent.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
