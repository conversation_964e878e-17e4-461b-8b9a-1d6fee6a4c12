// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: internal/app/ad/model/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// DzAdAccountChannelInfoRes is the golang structure for table dz_ad_account_channel.
type DzAdAccountChannelInfoRes struct {
	gmeta.Meta `orm:"table:dz_ad_account_channel"`
	ChannelId  int64       `orm:"channel_id,primary" json:"channelId" dc:"渠道ID"` // 渠道ID
	AccountId  int64       `orm:"account_id" json:"accountId" dc:"主账号ID"`        // 主账号ID
	NickName   string      `orm:"nick_name" json:"nickName" dc:"渠道昵称"`           // 渠道昵称
	UserName   string      `orm:"user_name" json:"userName" dc:"渠道用户名称"`         // 渠道用户名称
	JumpType   int         `orm:"jump_type" json:"jumpType" dc:"1单端，2双端"`        // 1单端，2双端
	OfAppId    string      `orm:"of_app_id" json:"ofAppId" dc:"小程序ID"`
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"` // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"` // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"` // 删除时间
}

type DzAdAccountChannelListRes struct {
	ChannelId int64  `json:"channelId" dc:"渠道ID"`
	AccountId int64  `json:"accountId" dc:"主账号ID"`
	NickName  string `json:"nickName" dc:"渠道昵称"`
	UserName  string `json:"userName" dc:"渠道用户名称"`
	JumpType  int    `json:"jumpType" dc:"1单端，2双端"`
	// app_id
	AppName string `json:"appName" dc:"小程序名称"`
	// app_name
	OfAppId   string      `json:"ofAppId" dc:"小程序ID"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	UserList  []int64     `json:"userList" dc:"用户列表"`
	DeptList  []int64     `json:"deptList" dc:"部门列表"`
}

// DzAdAccountChannelSearchReq 分页请求参数
type DzAdAccountChannelSearchReq struct {
	comModel.PageReq
	ChannelId string `p:"channelId" dc:"渠道ID"`                                                    //渠道ID
	AccountId string `p:"accountId"  dc:"主账号ID"`                                                  //主账号ID
	NickName  string `p:"nickName" dc:"渠道昵称"`                                                     //渠道昵称
	UserName  string `p:"userName" dc:"渠道用户名称"`                                                   //渠道用户名称
	JumpType  string `p:"jumpType" v:"jumpType@integer#1单端，2双端需为整数" dc:"1单端，2双端"`                 //1单端，2双端
	CreatedAt string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

// DzAdAccountChannelSearchRes 列表返回结果
type DzAdAccountChannelSearchRes struct {
	comModel.ListRes
	List []*DzAdAccountChannelListRes `json:"list"`
}

// DzAdAccountChannelAddReq 添加操作请求参数
type DzAdAccountChannelAddReq struct {
	ChannelId int64  `p:"channelId" v:"required#主键ID不能为空" dc:"渠道ID"`
	AccountId int64  `p:"accountId" v:"required#主账号ID不能为空" dc:"主账号ID"`
	NickName  string `p:"nickName" v:"required#渠道昵称不能为空" dc:"渠道昵称"`
	UserName  string `p:"userName" v:"required#渠道用户名称不能为空" dc:"渠道用户名称"`
	JumpType  int    `p:"jumpType"  dc:"1单端，2双端"`
}

// DzAdAccountChannelEditReq 修改操作请求参数
type DzAdAccountChannelEditReq struct {
	ChannelId int64  `p:"channelId" v:"required#主键ID不能为空" dc:"渠道ID"`
	AccountId int64  `p:"accountId" v:"required#主账号ID不能为空" dc:"主账号ID"`
	NickName  string `p:"nickName" v:"required#渠道昵称不能为空" dc:"渠道昵称"`
	UserName  string `p:"userName" v:"required#渠道用户名称不能为空" dc:"渠道用户名称"`
	JumpType  int    `p:"jumpType"  dc:"1单端，2双端"`
	OfAppId   string `p:"ofAppId" dc:"小程序ID"`
}

type DzAdAccountChannelSetAppIdReq struct {
	ChannelIds []int64 `p:"channelIds"   dc:"渠道IDs"`
	AppId      string  `p:"appId"  dc:"小程序ID"`
}

type DZAdAddAuthReq struct {
	ChannelId      int64   `p:"channelId"   dc:"渠道ID"`
	ChannelIds     []int64 `p:"channelIds"   dc:"渠道ID"`
	DeptIds        []int   `p:"deptIds"  dc:"指定部门id "`
	SpecifyUserIds []int   `p:"specifyUserIds"  dc:"指定用户id"`
}

type DzAdAccountChannelRes struct {
	ChannelId int64  `json:"channelId" dc:"渠道ID"`
	AccountId int64  `json:"accountId" dc:"账号ID"`
	Token     string `json:"token" dc:"密钥"`
}
