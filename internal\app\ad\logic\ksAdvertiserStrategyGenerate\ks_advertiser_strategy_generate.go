// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-21 00:00:00
// 生成路径: internal/app/ad/logic/ksAdvertiserStrategyGenerate/ks_advertiser_strategy_generate.go
// 生成人：gfast
// desc:快手广告策略生成
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	ksGenerate "github.com/tiger1103/gfast/v3/internal/app/ad/ksGenerate"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/generate"
	oceanengineModel "github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
)

func init() {
	service.RegisterKsAdvertiserStrategyGenerate(New())
}

func New() service.IKsAdvertiserStrategyGenerate {
	return &sKsAdvertiserStrategyGenerate{}
}

type sKsAdvertiserStrategyGenerate struct{}

func (s *sKsAdvertiserStrategyGenerate) GenerateAdPreview(ctx context.Context, req *model.KsAdvertiserStrategyGenerateReq) (res *model.KsAdvertiserStrategyGenerateRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		generateImpl := ksGenerate.GetGenerate(req.KsAdvertiserStrategyRuleReq.AdGroupRule)
		if generateImpl == nil {
			liberr.ErrIsNil(ctx, errors.New("暂不支持该生成方式"))
		}
		estimateInfoMap, err1 := generateImpl.GetEstimateInfo(ctx, req)
		liberr.ErrIsNil(ctx, err1)
		res = &model.KsAdvertiserStrategyGenerateRes{
			EstimateInfoMap: estimateInfoMap,
		}
		res.EstimateUnitNum = ksGenerate.CalcEstimateUnitNum(estimateInfoMap)
		if req.KsAdvertiserStrategyRuleReq.DataType == commonConsts.DataTypePreview {
			previewBaseInfoList, err2 := generateImpl.GeneratePreviewBaseInfo(ctx, req, estimateInfoMap)
			liberr.ErrIsNil(ctx, err2)
			advertiserList, err3 := ksGenerate.BuildAdvertiserList(req, estimateInfoMap, previewBaseInfoList)
			liberr.ErrIsNil(ctx, err3)
			res.AdvertiserList = advertiserList
			ksGenerate.BuildCampaignAndUnitName(res.AdvertiserList)
		}
	})
	return
}

func (s *sKsAdvertiserStrategyGenerate) ExecuteTask(ctx context.Context, req *model.AdExecuteTaskReq) (err error) {
	// 根据参数构建
	var taskId = libUtils.GenerateID()
	err = g.Try(ctx, func(ctx context.Context) {
		req.StrategyConfig.KsAdvertiserStrategyRuleReq.TaskId = taskId
		// 生成配置表格数据
		err = service.KsAdvertiserStrategyConfig().Add(ctx, &model.KsAdvertiserStrategyAddReq{
			RuleConfig:     req.StrategyConfig.KsAdvertiserStrategyRuleReq,
			CampaignConfig: req.StrategyConfig.KsAdvertiserStrategyCampaignConfig,
			UnitConfig:     req.StrategyConfig.KsAdvertiserStrategyUnitConfig,
			CreativeConfig: req.StrategyConfig.KsAdvertiserStrategyCreativeConfig,
			MaterialConfig: req.StrategyConfig.KsAdvertiserStrategyMaterialConfig,
			TitleConfig:    req.StrategyConfig.KsAdvertiserStrategyTitleConfig,
		})
		liberr.ErrIsNil(ctx, err, "添加配置表格数据失败")
		// 添加task 数据
		err = service.KsAdvertiserStrategyTask().AddTask(ctx, req)
		liberr.ErrIsNil(ctx, err, "添加配置表格数据失败")
		var campaignTaskList = make([]*do.KsAdvertiserStrategyTaskCampaign, 0)
		var unitTaskList = make([]*do.KsAdvertiserStrategyTaskUnit, 0)
		var creativeTaskList = make([]*do.KsAdvertiserStrategyTaskCreative, 0)
		//  异步上传素材
		err = s.UpLoadMaterials(ctx, req.Generate)
		if err != nil {
			liberr.ErrIsNil(ctx, err, "上传素材出错 err:"+err.Error())
		}
		// 构造广告计划
		for _, info := range req.Generate.AdvertiserList {
			var campaignNum = len(info.CampaignList)
			var unitNum int
			for _, campaignInfo := range info.CampaignList {
				unitNum += len(campaignInfo.UnitList)
				campaignInfo.TaskId = libUtils.GenerateID()
				campaignId, jsonStr, err1 := CreateCampaign(ctx, info, campaignInfo)
				liberr.ErrIsNil(ctx, err1, "创建广告计划失败")
				if campaignId == 0 || err1 != nil {
					err = errors.New("创建广告计划失败")
					campaignTaskList = append(campaignTaskList, &do.KsAdvertiserStrategyTaskCampaign{
						TaskCampaignId: campaignInfo.TaskId,
						CampaignId:     campaignId,
						CampaignName:   campaignInfo.CampaignName,
						TaskId:         taskId,
						AdvertiserId:   info.AdvertiserId,
						AdvertiserNick: info.AdvertiserName,
						CampaignData:   jsonStr,
						Status:         oceanengineModel.TaskProjectStatusError,
					})
					return
				} else {
					campaignInfo.CampaignId = gconv.String(campaignId)
					campaignTaskList = append(campaignTaskList, &do.KsAdvertiserStrategyTaskCampaign{
						TaskCampaignId: campaignInfo.TaskId,
						CampaignId:     campaignId,
						CampaignName:   campaignInfo.CampaignName,
						TaskId:         taskId,
						AdvertiserId:   info.AdvertiserId,
						AdvertiserNick: info.AdvertiserName,
						CampaignData:   jsonStr,
						Status:         oceanengineModel.TaskProjectStatusSuccess,
					})
					for _, unitInfo := range campaignInfo.UnitList {
						unitInfo.TaskId = libUtils.GenerateID()
						unitId, jsonStr2, err2 := CreateUnit(ctx, campaignId, info, campaignInfo, unitInfo)
						liberr.ErrIsNil(ctx, err2, "创建广告组失败")
						if unitId == 0 || err2 != nil {
							err = errors.New("创建广告组失败")
							unitTaskList = append(unitTaskList, &do.KsAdvertiserStrategyTaskUnit{
								TaskUnitId:     unitInfo.TaskId,
								UnitId:         unitId,
								UnitName:       unitInfo.UnitName,
								TaskId:         taskId,
								TaskCampaignId: campaignInfo.TaskId,
								AdvertiserId:   info.AdvertiserId,
								AdvertiserNick: info.AdvertiserName,
								ExternalAction: unitInfo.OcpxActionType,
								ErrMsg:         err2.Error(),
								Status:         oceanengineModel.TaskProjectStatusError,
								UnitData:       jsonStr2,
							})
							return
						} else {
							unitTaskList = append(unitTaskList, &do.KsAdvertiserStrategyTaskUnit{
								TaskUnitId:     unitInfo.TaskId,
								UnitId:         unitId,
								UnitName:       unitInfo.UnitName,
								TaskId:         taskId,
								TaskCampaignId: campaignInfo.TaskId,
								AdvertiserId:   info.AdvertiserId,
								AdvertiserNick: info.AdvertiserName,
								ExternalAction: unitInfo.OcpxActionType,
								ErrMsg:         nil,
								Status:         oceanengineModel.TaskProjectStatusSuccess,
								UnitData:       jsonStr2,
							})
							for _, creativeInfo := range unitInfo.CreativeList {
								creativeInfo.TaskId = libUtils.GenerateID()
								// 自动化创意
								if req.StrategyConfig.KsAdvertiserStrategyCreativeConfig.CreateMode == 1 {
									creativeId, jsonStr3, err3 := CreateCreative(ctx, unitId, info, campaignInfo, creativeInfo)
									liberr.ErrIsNil(ctx, err3, "创建广告创意失败")
									if creativeId == 0 {
										err = errors.New("创建广告创意失败")
										creativeTaskList = append(creativeTaskList, &do.KsAdvertiserStrategyTaskCreative{
											TaskCreativeId: creativeInfo.TaskId,
											CreativeId:     creativeId,
											CreativeName:   creativeInfo.CreativeConfig.CreativeName,
											TaskId:         taskId,
											TaskCampaignId: campaignInfo.TaskId,
											TaskUnitId:     unitInfo.TaskId,
											AdvertiserId:   info.AdvertiserId,
											AdvertiserNick: info.AdvertiserName,
											ErrMsg:         err3.Error(),
											Status:         oceanengineModel.TaskProjectStatusError,
											CreativeData:   jsonStr3,
										})
										return
									}
									creativeTaskList = append(creativeTaskList, &do.KsAdvertiserStrategyTaskCreative{
										TaskCreativeId: creativeInfo.TaskId,
										CreativeId:     creativeId,
										CreativeName:   creativeInfo.CreativeConfig.CreativeName,
										TaskId:         taskId,
										TaskCampaignId: campaignInfo.TaskId,
										TaskUnitId:     unitInfo.TaskId,
										AdvertiserId:   info.AdvertiserId,
										AdvertiserNick: info.AdvertiserName,
										ErrMsg:         nil,
										Status:         oceanengineModel.TaskProjectStatusSuccess,
										CreativeData:   jsonStr3,
									})

								} else if req.StrategyConfig.KsAdvertiserStrategyCreativeConfig.CreateMode == 2 { // 程序化3.0
									creativeId, jsonStr3, err3 := CreateCreative2(ctx, unitId, info, campaignInfo, creativeInfo)
									liberr.ErrIsNil(ctx, err3, "创建广告创意2失败")
									if creativeId == 0 {
										err = errors.New("创建广告创意2失败")
										creativeTaskList = append(creativeTaskList, &do.KsAdvertiserStrategyTaskCreative{
											TaskCreativeId: creativeInfo.TaskId,
											CreativeId:     creativeId,
											CreativeName:   creativeInfo.CreativeConfig.CreativeName,
											TaskId:         taskId,
											TaskCampaignId: campaignInfo.TaskId,
											TaskUnitId:     unitInfo.TaskId,
											AdvertiserId:   info.AdvertiserId,
											AdvertiserNick: info.AdvertiserName,
											ErrMsg:         err3.Error(),
											Status:         oceanengineModel.TaskProjectStatusError,
											CreativeData:   jsonStr3,
										})
										return
									}
									creativeTaskList = append(creativeTaskList, &do.KsAdvertiserStrategyTaskCreative{
										TaskCreativeId: creativeInfo.TaskId,
										CreativeId:     creativeId,
										CreativeName:   creativeInfo.CreativeConfig.CreativeName,
										TaskId:         taskId,
										TaskCampaignId: campaignInfo.TaskId,
										TaskUnitId:     unitInfo.TaskId,
										AdvertiserId:   info.AdvertiserId,
										AdvertiserNick: info.AdvertiserName,
										ErrMsg:         nil,
										Status:         oceanengineModel.TaskProjectStatusSuccess,
										CreativeData:   jsonStr3,
									})

								}
							}
						}
					}
				}
			}
			if strings.Contains(req.StrategyConfig.KsAdvertiserStrategyCampaignConfig.CampaignName, generate.DailyNum) {
				ksGenerate.IncrCampaignDailyNum(info.AdvertiserId, campaignNum)
			}
			if strings.Contains(req.StrategyConfig.KsAdvertiserStrategyUnitConfig.UnitName, generate.DailyNum) {
				ksGenerate.IncrUnitDailyNum(info.AdvertiserId, unitNum)
			}
		}

		if err == nil {
			// 更新任务状态
			_ = service.KsAdvertiserStrategyTask().UpdateStatus(ctx, taskId, string(oceanengineModel.TaskStatusFINISH))
		}

	})
	return
}

// CreateCreative 创建自定义创意
func CreateCreative(ctx context.Context, unitId uint64, adReq *model.AdvertiserInfo, campaignInfo *model.CampaignInfo, creativeInfo *model.CreativeInfo) (creativeId uint64, jsonStr string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, err := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, gconv.Int64(adReq.AdvertiserId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		createCreativeRes := ksApi.CreateCreativeRequest{
			AdvertiserID: gconv.Uint64(adReq.AdvertiserId),
			UnitID:       unitId,
			CreativeName: creativeInfo.CreativeMaterials.Name,
			PhotoID:      creativeInfo.CreativeMaterials.Video[0].MediaId,
			ImageToken:   creativeInfo.CreativeMaterials.Video[0].VideoThumbnailId,
			//DpaTemplateID:               0,
			CreativeMaterialType: 1, //  todo  根据素材类型判断
			//ImageTokens:          nil,
			ActionBarText: creativeInfo.CreativeConfig.ActionBarText,
			//Description:   "",
			NewExposeTag: ksApi.GetNewExposeTagByStringArray(creativeInfo.CreativeConfig.NewExposeTag),
			//ClickTrackURL:               "",
			//ImpressionURL: "",
			//AdPhotoPlayedT3sURL: "",
			//ActionbarClickURL:   "",
			//CreativeCategory:    0,
			CreativeTag: creativeInfo.CreativeConfig.CreativeTag,
			//LiveCreativeTag:             0,
			//LiveTrackURL:                "",
			//DpaStyleTypes:               nil,
			OuterLoopNative: creativeInfo.CreativeConfig.OuterLoopNative,

			//Recommendation:              "",
			HighLightFlash: 0,
			//SplashPhotoIDs:              nil,
			//SplashImageTokens:           nil,
			MaterialIntelligentOptimize: 0,
		}

		if campaignInfo.CampaignType == 30 {
			//当 campaignType=30只能填写1普通快手号
			createCreativeRes.KOLUserType = 1 // 默认1
			// 计划 campaignType=30 短剧推广时，值为短剧作者ID
			createCreativeRes.KOLUserID = creativeInfo.CreativeConfig.KolUserId
		}

		// 调用接口创建unit
		campaignResp, err1 := ksApi.GetKSApiClient().CreateCreativeService.
			AccessToken(accessToken).
			SetReq(createCreativeRes).
			Do()
		if err1 != nil {
			g.Log().Errorf(ctx, fmt.Sprintf("-------------  创建创意失败 error:%v -------------------", err1.Error()))
		}
		if campaignResp.Data != nil {
			creativeId = campaignResp.Data.CreativeID
			data, _ := json.Marshal(createCreativeRes)
			jsonStr = string(data)
			return
		} else {
			err = errors.New("创建创意失败")
			data, _ := json.Marshal(createCreativeRes)
			jsonStr = string(data)
			return
		}

	})
	return
}

// CreateCreative2 创建程序化创意
func CreateCreative2(ctx context.Context, unitId uint64, adReq *model.AdvertiserInfo, campaignInfo *model.CampaignInfo, creativeInfo *model.CreativeInfo) (creativeId uint64, jsonStr string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, err := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, gconv.Int64(adReq.AdvertiserId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		createCreativeRes := ksApi.AdvancedCreativeCreateRequest{
			AdvertiserID: gconv.Uint64(adReq.AdvertiserId),
			UnitID:       unitId,
			PackageName:  creativeInfo.CreativeConfig.CreativeName,
			//StickerStyles: nil, 仅搜索广告支持
			//CoverSlogans:  nil, 仅搜索广告支持
			ActionBar: creativeInfo.CreativeConfig.ActionBarText,
			Captions:  []string{creativeInfo.Title.Title},
			//ClickURL:      "",
			//ImpressionURL: "",
			//ActionbarClickURL:           "",
			//AdPhotoPlayedT3sURL:         "",
			CreativeCategory:            creativeInfo.CreativeConfig.CreativeCategory,
			CreativeTag:                 creativeInfo.CreativeConfig.CreativeTag,
			PhotoList:                   nil,
			PicList:                     nil,
			NewExposeTag:                ksApi.GetNewExposeTagByStringArray(creativeInfo.CreativeConfig.NewExposeTag),
			MaterialIntelligentOptimize: 0,
			OuterLoopNative:             creativeInfo.CreativeConfig.OuterLoopNative,
			//Recommendation:              "",
		}

		if campaignInfo.CampaignType == 30 {
			//当 campaignType=30只能填写1普通快手号
			createCreativeRes.KOLUserType = 1 // 默认1
			// 计划 campaignType=30 短剧推广时，值为短剧作者ID
			createCreativeRes.KOLUserID = creativeInfo.CreativeConfig.KolUserId
		}

		// 调用接口创建unit
		campaignResp, err1 := ksApi.GetKSApiClient().AdvancedCreativeCreateService.
			AccessToken(accessToken).
			SetReq(createCreativeRes).
			Do()
		if err1 != nil {
			g.Log().Errorf(ctx, fmt.Sprintf("-------------  创建创意失败 error:%v -------------------", err1.Error()))
		}
		if campaignResp.Data != nil {
			creativeId = campaignResp.Data.CampaignID
			data, _ := json.Marshal(createCreativeRes)
			jsonStr = string(data)
			return
		} else {
			err = errors.New("创建创意失败")
			data, _ := json.Marshal(createCreativeRes)
			jsonStr = string(data)
			return
		}

	})
	return
}

func CreateUnit(ctx context.Context, campaignId uint64, adReq *model.AdvertiserInfo, campaignInfo *model.CampaignInfo, req *model.UnitInfo) (unitId uint64, jsonStr string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, err := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, gconv.Int64(adReq.AdvertiserId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		createUnitRes := ksApi.CreateUnitRequest{
			AdvertiserID: gconv.Uint64(adReq.AdvertiserId),
			CampaignID:   campaignId,
			UnitName:     req.UnitName,
			PutStatus:    req.PutStatus,
			BidType:      req.BidType,
			//UnitType: req.CreativeList[0].CreativeMaterials,
			//CpaBid:       req.BidData,
			//DayBudget:         req.DayBudget,
			DayBudgetSchedule: gconv.Int64(req.DayBudget),
			OcpxActionType:    req.OcpxActionType,
			RoiRatio:          req.RoiRatio,
			//EnhanceConversionType: req.EnhanceConversionType,
			//DeepConversionType:    req.DeepConversionType,
			//DeepConversionBid:     req.DeepConversionBid,
			SceneID:      req.SceneId,
			BeginTime:    req.BeginTime,
			EndTime:      req.EndTime,
			ScheduleTime: req.ScheduleTime,
			//ConvertID:             req.ConvertId,
			//URLType:               req.URLType,
			//WebURLType:            req.WebURLType,
			//电商
			//URL:                   req.KsAdvertiserStrategyUnitAddReq,
			//UnitType:              req.Uni,
			//SiteID:    req.SceneId,
			//GroupID:   req.grou,
			//SchemaURI: req.sche,
			//SchemaID:  req.sc,
			//AppID:                 req.app,
			//AppDownloadType: req.app,
			//DownloadPageURL: req.do,
			//UseAppMarcket:   req.us,
			//AppStore:        req.app,
			//ShowMode:      req.sho, 默认
			//SiteType:      req.sit,
			SmartCover:  false,
			AssetMining: false,
			//ConsultID:     req.con,
			//AdvCardOption: req.adv,
			//AdvCardList:   req.adv,
			//PlayableID:    req.pl,
			//PlayButton:    req.play,
			//DpaUnitParam:    req.dp, 当计划类型为商品库推广时必填。 type = 9
			//JingleBellID:   req.ji,  // 计划 campaignType=16 粉丝直播推广时必填写
			//ConversionType: req.con,  计划 campaignType=16 粉丝直播推广时必填写 6
			//ExtendSearch: req.ex,
			//CustomMiniAppData:   req.cu, 计划 campaignType=19 推广快手小程序时必填，具体见下方表格。 具体见下方表格
			Target: ksApi.DefaultTarget(),
			//TemplateID:      req.tem,
			//OuterLoopNative: req.outer,

			//PackageID:         req.pack,  todo 暂时是空的
			//SeriesCardType:      req.card, todo 卡片相关的配置
			//SeriesCardInfo:      req.ser, todo 卡片相关的配置
			//ULink:               req.ul, 仅在计划 campaignType=7 提升应用活跃时使用，输入后IOS将优先调起该链接，不超过2000字符
		}

		// req.StrategyConfig.KsAdvertiserStrategyCreativeConfig

		// CpaBid 出价
		// bid_type 是 OCPM 时该字段必填，单位：厘，ocpx_action_type 为 2 时，不得低于 0.1 元，不得高于 10 元；ocpx_action_type 为 180 时，不得低于 1 元，不得高于 3000 元，ocpx_action_type 为 53 时，不得低于 5 元，不得高于 3000 元；不得高于组预算
		if req.BidType == 10 {
			createUnitRes.CpaBid = req.Bid
		} else if req.BidType == 2 {
			createUnitRes.Bid = req.Bid
		}
		// 短剧推广
		if campaignInfo.CampaignType == 30 {
			//req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].Children[0]. 判空
			if req.KsAdvertiserStrategyUnitAddReq != nil && len(req.KsAdvertiserStrategyUnitAddReq.ShortPlayData) > 0 && len(req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList) > 0 && len(req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].Children) > 0 {
				createUnitRes.SeriesID = gconv.Uint64(req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].Children[0].SeriesId)
				createUnitRes.EpisodeID = gconv.Uint64(req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].Children[0].EpisodeId)
				// 付费模式
				if req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].SeriesPayMode > 0 {
					createUnitRes.SeriesPayTemplateID = req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].SeriesPayTemplateId
					createUnitRes.SeriesPayMode = req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].SeriesPayMode
				}
				createUnitRes.LiveUserID = req.KsAdvertiserStrategyUnitAddReq.ShortPlayData[0].ShortPlayInfoList[0].Children[0].KsUserId
			} else {
				liberr.ErrIsNil(ctx, errors.New("短剧推广计划数据为空"), "创建短剧推广计划失败")
			}
		}

		// 搜索块投
		if req.QuickSearch == 1 {
			createUnitRes.QuickSearch = req.QuickSearch
			createUnitRes.TargetExplore = req.TargetExplore
			createUnitRes.NegativeWordParam = &ksApi.NegativeWordParam{
				ExactWords:  req.NegativeWordParam.ExactWords,
				PhraseWords: req.NegativeWordParam.PhraseWords,
			}
		}
		if campaignInfo.BidType == 1 {
			//当计划bid_type=1时，组层级bid_type必须传12
			createUnitRes.BidType = 12
		}
		// 调用接口创建unit
		campaignResp, err1 := ksApi.GetKSApiClient().CreateUnitService.
			AccessToken(accessToken).
			SetReq(createUnitRes).
			Do()

		if err1 != nil {
			g.Log().Errorf(ctx, fmt.Sprintf("-------------  创建unit失败 error:%v -------------------", err1.Error()))
		}
		if campaignResp.Data != nil {
			unitId = campaignResp.Data.UnitID
			data, _ := json.Marshal(createUnitRes)
			jsonStr = string(data)
			return
		} else {
			err = errors.New(" 创建unit失败")
			data, _ := json.Marshal(createUnitRes)
			jsonStr = string(data)
			return
		}

	})
	return

}

func CreateCampaign(ctx context.Context, adReq *model.AdvertiserInfo, req *model.CampaignInfo) (campaignId uint64, jsonStr string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, err := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, gconv.Int64(adReq.AdvertiserId))
		liberr.ErrIsNil(ctx, err, "获取accessToken失败")
		createCampaignRes := ksApi.CreateCampaignRequest{
			AdvertiserID: gconv.Uint64(adReq.AdvertiserId),
			CampaignName: req.CampaignName,
			Type:         req.CampaignType,
			DayBudget:    gconv.Int64(req.DayBudget),
			//DayBudgetSchedule: req.DayBudget,
			AdType:     req.AdType,
			BidType:    req.BidType,
			AutoAdjust: req.AutoAdjust,
			AutoBuild:  req.AutoBuild,
			AutoBuildNameRule: &ksApi.AutoBuildNameRule{
				CreativeNameRule: req.CreativeNameRule,
				UnitNameRule:     req.UnitNameRule,
			},
			//CapRoiRatio:      0,
			//CapBid:           0,
			//ConstraintCpa:    0,
			AutoManage: req.AutoManage,
			//PhotoPackageInfo: nil,
		}
		//if createCampaignRes.AutoPhotoScope == 1 {
		//	 todo 素材包情况
		//}
		campaignResp, err1 := ksApi.GetKSApiClient().CreateCampaignService.
			AccessToken(accessToken).
			SetReq(createCampaignRes).
			Do()

		if err1 != nil {
			g.Log().Errorf(ctx, fmt.Sprintf("-------------  Campaign失败 error:%v -------------------", err1.Error()))
		}
		if campaignResp.Data != nil {
			campaignId = campaignResp.Data.CampaignID
			data, _ := json.Marshal(createCampaignRes)
			jsonStr = string(data)
			return
		} else {
			err = errors.New("创建 Campaign失败")
			data, _ := json.Marshal(createCampaignRes)
			jsonStr = string(data)
			return
		}
	})
	return
}

// UpLoadMaterials 创建项目之前的组装素材的操作
func (s *sKsAdvertiserStrategyGenerate) UpLoadMaterials(ctx context.Context, req *model.KsAdvertiserStrategyGenerateRes) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		for _, advertiserInfo := range req.AdvertiserList {
			for _, campaignInfo := range advertiserInfo.CampaignList {
				for _, unitInfo := range campaignInfo.UnitList {
					for _, creative := range unitInfo.CreativeList {
						materials := creative.CreativeMaterials
						if len(materials.Image) > 0 {
							for _, img := range materials.Image {
								res, innerError := service.KsAdvertiserStrategyMaterial().UpLoad(ctx, advertiserInfo.AdvertiserId, img.MaterialId)
								if innerError != nil {
									g.Log().Error(ctx, innerError, "上传Image图片失败！")
								}
								img.MediaId = res.PicId
							}
						}
						// 视频
						if len(materials.Video) > 0 {
							for _, video := range materials.Video {
								//allVideo = append(allVideo, video)
								res, innerError := service.KsAdvertiserStrategyMaterial().UpLoad(ctx, advertiserInfo.AdvertiserId, video.MaterialId)
								if innerError != nil {
									g.Log().Error(ctx, innerError, "上传视频失败！")
								}
								video.MediaId = res.PhotoId

								// 如果是选择的缩略图
								if len(video.VideoThumbnailId) > 0 {
									res, err = service.KsAdvertiserStrategyMaterial().UpLoad(ctx, advertiserInfo.AdvertiserId, gconv.Int(video.VideoThumbnailId))
									if err != nil {
										g.Log().Error(ctx, err, "上传视频缩略图失败！")
									}
									video.VideoThumbnailId = res.PicId
								} else { // 如果是使用的缩略图url
									res, err = service.KsAdvertiserStrategyMaterial().UpLoadByUrl(ctx, advertiserInfo.AdvertiserId, video.ThumbnailUri, false)
									if err != nil {
										g.Log().Error(ctx, err, "上传视频缩略图失败！")
									}
									video.VideoThumbnailId = res.PicId
								}
							}
						}
					}
				}
			}
		}
	})
	return
}

// QuerySeriesAuthUserList 获取授权的短剧作者列表
func (s *sKsAdvertiserStrategyGenerate) QuerySeriesAuthUserList(ctx context.Context, advertiserId int64) (res []ksApi.QuerySeriesAuthUserListData, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, advertiserId)
		seriesAuthUserListRes, err1 := ksApi.GetKSApiClient().QuerySeriesAuthUserListService.
			AccessToken(accessToken).
			SetReq(ksApi.QuerySeriesAuthUserListReq{AdvertiserId: advertiserId}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		res = *seriesAuthUserListRes.Data
	})
	return
}

// QuerySeriesList 查询授权短剧列表
func (s *sKsAdvertiserStrategyGenerate) QuerySeriesList(ctx context.Context, req *model.QuerySeriesListReq) (res []ksApi.MapiSeriesInfoSnake, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		cursor := ""
		pageSize := 20
		for {
			seriesListRes, err1 := ksApi.GetKSApiClient().QuerySeriesListService.
				AccessToken(accessToken).
				SetReq(ksApi.QuerySeriesListReq{
					AdvertiserId: req.AdvertiserId,
					UserId:       req.UserId,
					PageSize:     pageSize,
					Cursor:       cursor,
					SeriesTitle:  req.SeriesTitle,
				}).
				Do()
			liberr.ErrIsNil(ctx, err1)
			if seriesListRes.Data == nil || len(seriesListRes.Data.Series) == 0 {
				break
			}
			for _, series := range seriesListRes.Data.Series {
				series.SeriesId = series.Id
				res = append(res, series)
			}
			cursor = seriesListRes.Data.Cursor
			if cursor == "-1" {
				break
			}
		}
	})
	return
}

// QuerySeriesEpisodeList 查询短剧剧集列表
func (s *sKsAdvertiserStrategyGenerate) QuerySeriesEpisodeList(ctx context.Context, req *model.QuerySeriesEpisodeListReq) (res []ksApi.MapiEpisodeInfoSnake, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		cursor := ""
		for {
			episodeListRes, err1 := ksApi.GetKSApiClient().QuerySeriesEpisodeListService.
				AccessToken(accessToken).
				SetReq(ksApi.QuerySeriesEpisodeListReq{
					AdvertiserId: req.AdvertiserId,
					UserId:       req.UserId,
					SeriesId:     req.SeriesId,
					EpisodeName:  req.EpisodeName,
					Cursor:       cursor,
				}).
				Do()
			liberr.ErrIsNil(ctx, err1)
			if episodeListRes.Data == nil || len(episodeListRes.Data.Episodes) == 0 {
				break
			}
			for _, episode := range episodeListRes.Data.Episodes {
				episode.SeriesId = req.SeriesId
				episode.EpisodeId = episode.Id
				episode.KsUserId = req.UserId
				res = append(res, episode)
			}
			cursor = episodeListRes.Data.Cursor
			if cursor == "no_more" {
				break
			}
		}
	})
	return
}

// QuerySeriesPayModeType 查询短剧付费模式
func (s *sKsAdvertiserStrategyGenerate) QuerySeriesPayModeType(ctx context.Context, req *model.QuerySeriesPayModeTypeReq) (res []ksApi.MapiSeriesPayModeInfoSnake, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		payModeTypeRes, err1 := ksApi.GetKSApiClient().QuerySeriesPayModeTypeService.
			AccessToken(accessToken).
			SetReq(ksApi.QuerySeriesPayModeTypeReq{
				AdvertiserId: req.AdvertiserId,
				UserId:       req.UserId,
				SeriesId:     req.SeriesId,
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if payModeTypeRes.Data != nil {
			res = *payModeTypeRes.Data
		}
	})
	return
}

// QuerySeriesPayModeTemplate 查询短剧付费模板
func (s *sKsAdvertiserStrategyGenerate) QuerySeriesPayModeTemplate(ctx context.Context, req *model.QuerySeriesPayModeTemplateReq) (res []ksApi.MapiSeriesPayModeTemplateInfoSnake, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		payModeTemplateRes, err1 := ksApi.GetKSApiClient().QuerySeriesPayModeTemplateService.
			AccessToken(accessToken).
			SetReq(ksApi.QuerySeriesPayModeTemplateReq{
				AdvertiserId:        req.AdvertiserId,
				UserId:              req.UserId,
				SeriesId:            req.SeriesId,
				SeriesPayMode:       req.SeriesPayMode,
				SeriesPayTemplateId: req.SeriesPayTemplateId,
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if payModeTemplateRes.Data != nil {
			res = *payModeTemplateRes.Data
		}
	})
	return
}

// QueryProductList 查询商品列表
func (s *sKsAdvertiserStrategyGenerate) QueryProductList(ctx context.Context, req *model.QueryProductListReq) (res *ksApi.ProductBatchQueryResponse, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		// 构建查询参数
		queryParam := &ksApi.AdDpaProductBatchQueryParamSneak{
			LibraryID: req.LibraryId,
		}
		if req.Name != "" {
			queryParam.Name = req.Name
		}
		productListRes, err1 := ksApi.GetKSApiClient().QueryProductService.
			AccessToken(accessToken).
			SetReq(ksApi.ProductBatchQueryRequest{
				AdvertiserID:                     uint64(req.AdvertiserId),
				AdDpaProductBatchQueryParamSneak: queryParam,
				PageInfo: &ksApi.PageInfo{
					PageSize:    req.PageSize,
					CurrentPage: req.CurrentPage,
				},
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if productListRes.Data != nil {
			res = productListRes.Data
		}
	})
	return
}

// QueryProductLibraryList 查询商品库列表
func (s *sKsAdvertiserStrategyGenerate) QueryProductLibraryList(ctx context.Context, req *model.QueryProductLibraryListReq) (res *ksApi.LibraryListResponse, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		libraryListRes, err1 := ksApi.GetKSApiClient().QueryProductLibraryListService.
			AccessToken(accessToken).
			SetReq(ksApi.LibraryListRequest{
				AdvertiserID: uint64(req.AdvertiserId),
				Name:         req.Name,
				LibraryID:    req.LibraryId,
				Status:       req.Status,
				QueryType:    req.QueryType,
				PageInfo: &ksApi.PageInfo{
					PageSize:    req.PageSize,
					CurrentPage: req.CurrentPage,
				},
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if libraryListRes.Data != nil {
			res = libraryListRes.Data
		}
	})
	return
}

// QueryCreativeActionBarText 查询行动号召按钮
func (s *sKsAdvertiserStrategyGenerate) QueryCreativeActionBarText(ctx context.Context, req *model.QueryCreativeActionBarTextReq) (res []string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		actionBarTextRes, err1 := ksApi.GetKSApiClient().QueryCreativeActionBarTextService.
			AccessToken(accessToken).
			SetReq(ksApi.ActionBarTextListRequest{
				AdvertiserID: uint64(req.AdvertiserId),
				CampaignType: req.CampaignType,
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if actionBarTextRes.Data != nil {
			res = actionBarTextRes.Data.ActionBarText
		}
	})
	return
}

// QueryToolExposeTags 查询创意推荐理由
func (s *sKsAdvertiserStrategyGenerate) QueryToolExposeTags(ctx context.Context, req *model.QueryToolExposeTagsReq) (res []string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		exposeTagsRes, err1 := ksApi.GetKSApiClient().QueryToolExposeTagsService.
			AccessToken(accessToken).
			SetReq(ksApi.ActionBarTextListRequest{
				AdvertiserID: uint64(req.AdvertiserId),
				CampaignType: req.CampaignType,
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if exposeTagsRes.Data != nil && exposeTagsRes.Data.ExposeTagView != nil {
			for _, exposeTagView := range exposeTagsRes.Data.ExposeTagView {
				res = append(res, exposeTagView.Text)
			}
		}
	})
	return
}

// QueryCreativeCategory 查询创意分类
func (s *sKsAdvertiserStrategyGenerate) QueryCreativeCategory(ctx context.Context, req *model.QueryCreativeCategoryReq) (res []*model.QueryCreativeCategoryRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accessToken, _ := service.KsAdvertiserAccountInfo().GetAccessToken(ctx, req.AdvertiserId)
		categoryListRes, err1 := ksApi.GetKSApiClient().QueryCreativeCategoryListService.
			AccessToken(accessToken).
			SetReq(ksApi.ActionBarTextListRequest{
				AdvertiserID: uint64(req.AdvertiserId),
			}).
			Do()
		liberr.ErrIsNil(ctx, err1)
		if categoryListRes.Data != nil && len(categoryListRes.Data.AdMarketCreativeCategoryView) > 0 {
			// 构建树形结构
			res = s.buildCategoryTree(categoryListRes.Data.AdMarketCreativeCategoryView)
		}
	})
	return
}

// buildCategoryTree 构建创意分类树形结构
func (s *sKsAdvertiserStrategyGenerate) buildCategoryTree(categories []ksApi.AdMarketCreativeCategoryView) []*model.QueryCreativeCategoryRes {
	// 创建映射表，用于存储所有节点
	categoryMap := make(map[int]*model.QueryCreativeCategoryRes)

	// 第一遍遍历，创建所有节点
	for _, category := range categories {
		categoryRes := &model.QueryCreativeCategoryRes{
			Label:    category.CategoryName,
			Value:    category.CategoryID,
			Level:    category.Level,
			Children: make([]model.QueryCreativeCategoryRes, 0),
		}
		categoryMap[category.CategoryID] = categoryRes
	}

	// 第二遍遍历，建立父子关系
	var rootCategories []*model.QueryCreativeCategoryRes
	for _, category := range categories {
		currentNode := categoryMap[category.CategoryID]
		if category.ParentID == 0 {
			// 根节点
			rootCategories = append(rootCategories, currentNode)
		} else {
			// 子节点，添加到父节点的children中
			if parent, exists := categoryMap[category.ParentID]; exists {
				// 递归构建子树
				childNode := s.buildChildNode(category.CategoryID, categoryMap, categories)
				parent.Children = append(parent.Children, childNode)
			}
		}
	}

	return rootCategories
}

// buildChildNode 递归构建子节点
func (s *sKsAdvertiserStrategyGenerate) buildChildNode(categoryID int, categoryMap map[int]*model.QueryCreativeCategoryRes, allCategories []ksApi.AdMarketCreativeCategoryView) model.QueryCreativeCategoryRes {
	node := categoryMap[categoryID]
	result := model.QueryCreativeCategoryRes{
		Label:    node.Label,
		Value:    node.Value,
		Level:    node.Level,
		Children: make([]model.QueryCreativeCategoryRes, 0),
	}

	// 查找所有以当前节点为父节点的子节点
	for _, category := range allCategories {
		if category.ParentID == categoryID {
			childNode := s.buildChildNode(category.CategoryID, categoryMap, allCategories)
			result.Children = append(result.Children, childNode)
		}
	}

	return result
}
