// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/model/entity/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdUserInfo is the golang structure for table dz_ad_user_info.
type DzAdUserInfo struct {
	gmeta.Meta   `orm:"table:dz_ad_user_info, do:true"`
	UserId       interface{} `orm:"user_id,primary" json:"userId"`     // 用户ID
	Time         interface{} `orm:"time" json:"time"`                  // 请求时间戳，单位毫秒
	ChannelId    interface{} `orm:"channel_id" json:"channelId"`       // 渠道ID
	AppId        interface{} `orm:"app_id" json:"appId"`               // 小程序ID
	PromotionId  interface{} `orm:"promotion_id" json:"promotionId"`   // 推广班组ID
	OpenId       interface{} `orm:"open_id" json:"openId"`             // openID
	AdId         interface{} `orm:"ad_id" json:"adId"`                 // 广告ID
	BookId       interface{} `orm:"book_id" json:"bookId"`             // 书籍ID
	ProjectId    interface{} `orm:"project_id" json:"projectId"`       // 广告计划ID
	ClickId      interface{} `orm:"click_id" json:"clickId"`           // 广告点击ID
	UnionId      interface{} `orm:"union_id" json:"unionId"`           // unionID
	RegisterTime interface{} `orm:"register_time" json:"registerTime"` // 用户注册时间戳，单位毫秒
	DyeTime      interface{} `orm:"dye_time" json:"dyeTime"`           // 用户染色时间戳，单位毫秒
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt"`       // 创建时间
}
