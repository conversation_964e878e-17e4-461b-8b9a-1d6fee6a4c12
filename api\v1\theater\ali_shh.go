package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	aliPayModel "github.com/tiger1103/gfast/v3/internal/app/aliPay/model"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// UploadMaterialReq 上传素材
type UploadMaterialReq struct {
	g.Meta `path:"/upload/material" tags:"短剧推广 - 生活号内容管理 - 上传素材" method:"post" summary:"上传素材"`
	commonApi.Author
	model.UploadMaterialReq
}

// UploadMaterialRes 上传素材返回结果
type UploadMaterialRes struct {
	g.Meta `mime:"application/json"`
	*model.UploadMaterialRes
}

// PublishContentReq 发布内容
type PublishContentReq struct {
	g.Meta `path:"/publish/content" tags:"短剧推广 - 生活号内容管理 - 发布内容" method:"post" summary:"发布内容"`
	commonApi.Author
	model.PublishContentReq
}

// PublishContentRes 发布内容
type PublishContentRes struct {
	g.Meta `mime:"application/json"`
	*model.PublishContentRes
}

// GetMaterialReq  获取素材列表
type GetMaterialReq struct {
	g.Meta `path:"/get/material" tags:"短剧推广 - 生活号内容管理 - 获取素材列表" method:"get" summary:"获取素材列表"`
	commonApi.Author
	model.SAliShhMaterialSearchReq
}

type GetMaterialRes struct {
	g.Meta `mime:"application/json"`
	*model.SAliShhMaterialSearchRes
}

// GetPublishReq  获取发布列表
type GetPublishReq struct {
	g.Meta `path:"/get/publish" tags:"短剧推广 - 生活号内容管理 - 获取发布列表" method:"get" summary:"获取素材列表"`
	commonApi.Author
	model.GetPublishReq
}

type GetPublishRes struct {
	g.Meta `mime:"application/json"`
	*model.GetPublishRes
}

type GetPublishInfoReq struct {
	g.Meta `path:"/get/publish/info" tags:"短剧推广 - 生活号内容管理 - 获取发布详情" method:"get" summary:"获取发布详情"`
	commonApi.Author
	AppId     string `p:"appId" `
	ContentId string `json:"contentId" dc:"内容发布后生成的ID（用于关联查询内容详情、删除内容等其他开放接口）"`
}

type GetPublishInfoRes struct {
	g.Meta `mime:"application/json"`
	*model.GetPublishInfoRes
}

type GetPublishStatusReq struct {
	g.Meta `path:"/get/publish/status" tags:"短剧推广 - 生活号内容管理 - 获取发布详情" method:"get" summary:"单条发布内容状态查询接口"`
	commonApi.Author
	AppId     string `p:"appId" `
	ContentId string `json:"contentId" dc:"内容发布后生成的ID（用于关联查询内容详情、删除内容等其他开放接口）"`
}

type GetPublishStatusRes struct {
	QueryRes *aliPayModel.AliContentStatusQueryRes
}

type DelPublishReq struct {
	g.Meta `path:"/del/publish" tags:"短剧推广 - 生活号内容管理 - 删除发布内容" method:"post" summary:"删除发布内容"`
	commonApi.Author
	AppId     string `p:"appId" `
	ContentId string `json:"contentId" dc:"内容发布后生成的ID（用于关联查询内容详情、删除内容等其他开放接口）"`
}

// DelPublishRes 删除操作返回结果
type DelPublishRes struct {
	commonApi.EmptyRes
}
