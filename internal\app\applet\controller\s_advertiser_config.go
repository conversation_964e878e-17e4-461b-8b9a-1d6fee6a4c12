// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-03-06 17:53:48
// 生成路径: internal/app/applet/controller/s_advertiser_config.go
// 生成人：cq
// desc:广告配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sAdvertiserConfigController struct {
	systemController.BaseController
}

var SAdvertiserConfig = new(sAdvertiserConfigController)

// List 列表
func (c *sAdvertiserConfigController) List(ctx context.Context, req *applet.SAdvertiserConfigSearchReq) (res *applet.SAdvertiserConfigSearchRes, err error) {
	res = new(applet.SAdvertiserConfigSearchRes)
	res.SAdvertiserConfigSearchRes, err = service.SAdvertiserConfig().List(ctx, &req.SAdvertiserConfigSearchReq)
	return
}

// Get 获取广告配置表
func (c *sAdvertiserConfigController) Get(ctx context.Context, req *applet.SAdvertiserConfigGetReq) (res *applet.SAdvertiserConfigGetRes, err error) {
	res = new(applet.SAdvertiserConfigGetRes)
	res.SAdvertiserConfigInfoRes, err = service.SAdvertiserConfig().GetById(ctx, req.Id)
	return
}

// Add 添加广告配置表
func (c *sAdvertiserConfigController) Add(ctx context.Context, req *applet.SAdvertiserConfigAddReq) (res *applet.SAdvertiserConfigAddRes, err error) {
	err = service.SAdvertiserConfig().Add(ctx, req.SAdvertiserConfigAddReq)
	return
}

// Edit 修改广告配置表
func (c *sAdvertiserConfigController) Edit(ctx context.Context, req *applet.SAdvertiserConfigEditReq) (res *applet.SAdvertiserConfigEditRes, err error) {
	err = service.SAdvertiserConfig().Edit(ctx, req.SAdvertiserConfigEditReq)
	return
}

// Delete 删除广告配置表
func (c *sAdvertiserConfigController) Delete(ctx context.Context, req *applet.SAdvertiserConfigDeleteReq) (res *applet.SAdvertiserConfigDeleteRes, err error) {
	err = service.SAdvertiserConfig().Delete(ctx, req.Ids)
	return
}
