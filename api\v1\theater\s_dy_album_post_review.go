// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-05-06 18:47:11
// 生成路径: api/v1/theater/s_dy_album_post_review.go
// 生成人：lx
// desc:抖音剧 后置审核表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyAlbumPostReviewSearchReq 分页请求参数
type SDyAlbumPostReviewSearchReq struct {
	g.Meta `path:"/list" tags:"抖音剧 后置审核表" method:"get" summary:"抖音剧 后置审核表列表"`
	commonApi.Author
	model.SDyAlbumPostReviewSearchReq
}

// SDyAlbumPostReviewSearchRes 列表返回结果
type SDyAlbumPostReviewSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumPostReviewSearchRes
}

// SDyAlbumPostReviewAddReq 添加操作请求参数
type SDyAlbumPostReviewAddReq struct {
	g.Meta `path:"/add" tags:"抖音剧 后置审核表" method:"post" summary:"抖音剧 后置审核表添加"`
	commonApi.Author
	*model.SDyAlbumPostReviewAddReq
}

// SDyAlbumPostReviewAddRes 添加操作返回结果
type SDyAlbumPostReviewAddRes struct {
	commonApi.EmptyRes
}

// SDyAlbumPostReviewEditReq 修改操作请求参数
type SDyAlbumPostReviewEditReq struct {
	g.Meta `path:"/edit" tags:"抖音剧 后置审核表" method:"put" summary:"抖音剧 后置审核表修改"`
	commonApi.Author
	*model.SDyAlbumPostReviewEditReq
}

// SDyAlbumPostReviewEditRes 修改操作返回结果
type SDyAlbumPostReviewEditRes struct {
	commonApi.EmptyRes
}

// SDyAlbumPostReviewGetReq 获取一条数据请求
type SDyAlbumPostReviewGetReq struct {
	g.Meta `path:"/get" tags:"抖音剧 后置审核表" method:"get" summary:"获取抖音剧 后置审核表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SDyAlbumPostReviewGetRes 获取一条数据结果
type SDyAlbumPostReviewGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumPostReviewInfoRes
}

// SDyAlbumPostReviewDeleteReq 删除数据请求
type SDyAlbumPostReviewDeleteReq struct {
	g.Meta `path:"/delete" tags:"抖音剧 后置审核表" method:"delete" summary:"删除抖音剧 后置审核表"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SDyAlbumPostReviewDeleteRes 删除数据返回
type SDyAlbumPostReviewDeleteRes struct {
	commonApi.EmptyRes
}
