// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-12-16 15:34:40
// 生成路径: internal/app/ad/logic/ad_anchor_point_images.go
// 生成人：cyao
// desc:锚点图片表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAnchorPointImages(New())
}

func New() service.IAdAnchorPointImages {
	return &sAdAnchorPointImages{}
}

type sAdAnchorPointImages struct{}

func (s *sAdAnchorPointImages) List(ctx context.Context, req *model.AdAnchorPointImagesSearchReq) (listRes *model.AdAnchorPointImagesSearchRes, err error) {
	listRes = new(model.AdAnchorPointImagesSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAnchorPointImages.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().Id+" = ?", req.Id)
		}
		if req.AnchorPointId != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().AnchorPointId+" = ?", gconv.Int(req.AnchorPointId))
		}
		if req.Uri != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().Uri+" = ?", req.Uri)
		}
		if req.Width != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().Width+" = ?", gconv.Int(req.Width))
		}
		if req.Height != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().Height+" = ?", gconv.Int(req.Height))
		}
		if req.Loading != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().Loading+" = ?", gconv.Int(req.Loading))
		}
		if req.ClMaterialId != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().ClMaterialId+" = ?", gconv.Int64(req.ClMaterialId))
		}
		if req.ClThumbnailUri != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().ClThumbnailUri+" = ?", req.ClThumbnailUri)
		}
		if req.ImageType != "" {
			m = m.Where(dao.AdAnchorPointImages.Columns().ImageType+" = ?", req.ImageType)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAnchorPointImagesListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAnchorPointImagesListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdAnchorPointImagesListRes{
				Id:             v.Id,
				AnchorPointId:  v.AnchorPointId,
				Uri:            v.Uri,
				Width:          v.Width,
				Height:         v.Height,
				Loading:        v.Loading,
				ClMaterialId:   v.ClMaterialId,
				ClThumbnailUri: v.ClThumbnailUri,
				ImageType:      v.ImageType,
			}
		}
	})
	return
}

func (s *sAdAnchorPointImages) GetById(ctx context.Context, id int) (res *model.AdAnchorPointImagesInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAnchorPointImages.Ctx(ctx).WithAll().Where(dao.AdAnchorPointImages.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAnchorPointImages) Add(ctx context.Context, req *model.AdAnchorPointImagesAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointImages.Ctx(ctx).Insert(do.AdAnchorPointImages{
			AnchorPointId:  req.AnchorPointId,
			Uri:            req.Uri,
			Width:          req.Width,
			Height:         req.Height,
			Loading:        req.Loading,
			ClMaterialId:   req.ClMaterialId,
			ClThumbnailUri: req.ClThumbnailUri,
			ImageType:      req.ImageType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAnchorPointImages) Adds(ctx context.Context, req []*model.AdAnchorPointImagesAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointImages.Ctx(ctx).Save(req)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAnchorPointImages) Edits(ctx context.Context, req []*model.AdAnchorPointImagesEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointImages.Ctx(ctx).Save(req)
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAnchorPointImages) Edit(ctx context.Context, req *model.AdAnchorPointImagesEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointImages.Ctx(ctx).WherePri(req.Id).Update(do.AdAnchorPointImages{
			AnchorPointId:  req.AnchorPointId,
			Uri:            req.Uri,
			Width:          req.Width,
			Height:         req.Height,
			Loading:        req.Loading,
			ClMaterialId:   req.ClMaterialId,
			ClThumbnailUri: req.ClThumbnailUri,
			ImageType:      req.ImageType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAnchorPointImages) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointImages.Ctx(ctx).Delete(dao.AdAnchorPointImages.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdAnchorPointImages) DeleteByAPId(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointImages.Ctx(ctx).Delete(dao.AdAnchorPointImages.Columns().AnchorPointId+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
