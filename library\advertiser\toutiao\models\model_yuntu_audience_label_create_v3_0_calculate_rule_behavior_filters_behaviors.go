/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors
type YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors string

// List of yuntu_audience_label_create_v3.0_calculate_rule_behavior_filters_behaviors
const (
	ADD_CART_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors     YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "add_cart"
	BROWSE_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors       YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "browse"
	CLICK_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors        YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "click"
	COMMENT_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors      YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "comment"
	ENTER_DETAIL_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "enter_detail"
	FAVORITES_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors    YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "favorites"
	FORWARD_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors      YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "forward"
	LIKE_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors         YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "like"
	ORDER_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors        YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "order"
	QUERY_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors        YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "query"
	SHOW_YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors         YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors = "show"
)

// Ptr returns reference to yuntu_audience_label_create_v3.0_calculate_rule_behavior_filters_behaviors value
func (v YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors) Ptr() *YuntuAudienceLabelCreateV30CalculateRuleBehaviorFiltersBehaviors {
	return &v
}
