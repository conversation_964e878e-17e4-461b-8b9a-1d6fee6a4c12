// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-01-03 11:02:30
// 生成路径: api/v1/oceanengine/ad_strategy_config.go
// 生成人：gfast
// desc:巨量广告搭建-策略配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdStrategyConfigSearchReq 分页请求参数
type AdStrategyConfigSearchReq struct {
	g.Meta `path:"/list" tags:"巨量广告搭建-策略配置" method:"post" summary:"巨量广告搭建-策略配置列表"`
	commonApi.Author
	model.AdStrategyConfigSearchReq
}

// AdStrategyConfigSearchRes 列表返回结果
type AdStrategyConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyConfigSearchRes
}

// AdStrategyConfigAddReq 添加操作请求参数
type AdStrategyConfigAddReq struct {
	g.Meta `path:"/add" tags:"巨量广告搭建-策略配置" method:"post" summary:"巨量广告搭建-策略配置添加"`
	commonApi.Author
	*model.AdStrategyConfigAddReq
}

// AdStrategyConfigAddRes 添加操作返回结果
type AdStrategyConfigAddRes struct {
	commonApi.EmptyRes
}

// AdStrategyConfigEditReq 修改操作请求参数
type AdStrategyConfigEditReq struct {
	g.Meta `path:"/edit" tags:"巨量广告搭建-策略配置" method:"put" summary:"巨量广告搭建-策略配置修改"`
	commonApi.Author
	*model.AdStrategyConfigEditReq
}

// AdStrategyConfigEditRes 修改操作返回结果
type AdStrategyConfigEditRes struct {
	commonApi.EmptyRes
}

// AdStrategyConfigGetReq 获取一条数据请求
type AdStrategyConfigGetReq struct {
	g.Meta `path:"/get" tags:"巨量广告搭建-策略配置" method:"get" summary:"获取巨量广告搭建-策略配置信息"`
	commonApi.Author
	StrategyId string `p:"strategyId" dc:"策略组ID"`
	TaskId     string `p:"taskId" dc:"任务ID"`
}

// AdStrategyConfigGetRes 获取一条数据结果
type AdStrategyConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdStrategyConfigDetailInfoRes
}

// AdStrategyConfigDeleteReq 删除数据请求
type AdStrategyConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"巨量广告搭建-策略配置" method:"post" summary:"删除巨量广告搭建-策略配置"`
	commonApi.Author
	StrategyIds []string `p:"strategyIds" v:"required#strategyIds必须"`
}

// AdStrategyConfigDeleteRes 删除数据返回
type AdStrategyConfigDeleteRes struct {
	commonApi.EmptyRes
}

// AdStrategyConfigCopyReq 复制策略组请求
type AdStrategyConfigCopyReq struct {
	g.Meta `path:"/copy" tags:"巨量广告搭建-策略配置" method:"post" summary:"获取巨量广告搭建-复制策略组"`
	commonApi.Author
	StrategyId string `p:"strategyId" v:"required#strategyId必须"`
}

// AdStrategyConfigCopyRes 复制策略组结果
type AdStrategyConfigCopyRes struct {
	g.Meta `mime:"application/json"`
}
