// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: internal/app/ad/logic/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysDo "github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"time"
)

func init() {
	service.RegisterKsAdOrderSettle(New())
}

func New() service.IKsAdOrderSettle {
	return &sKsAdOrderSettle{}
}

type sKsAdOrderSettle struct{}

func (s *sKsAdOrderSettle) List(ctx context.Context, req *model.KsAdOrderSettleSearchReq) (listRes *model.KsAdOrderSettleSearchRes, err error) {
	listRes = new(model.KsAdOrderSettleSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.KsAdOrderSettle.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().Id+" = ?", req.Id)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().AdvertiserId+" = ?", gconv.Int64(req.AdvertiserId))
		}
		if len(req.SeriesIds) > 0 {
			m = m.WhereIn(dao.KsAdOrderSettle.Columns().SeriesId, req.SeriesIds)
		}
		if len(req.SeriesNames) > 0 {
			m = m.WhereIn(dao.KsAdOrderSettle.Columns().SeriesName, req.SeriesNames)
		}
		if req.CopyrightUid != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().CopyrightUid+" = ?", req.CopyrightUid)
		}
		if req.CopyrightName != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().CopyrightName+" like ?", "%"+req.CopyrightName+"%")
		}
		if req.SalerUid != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().SalerUid+" = ?", req.SalerUid)
		}
		if req.PayProvider != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().PayProvider+" = ?", req.PayProvider)
		}
		if req.OrderType != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().OrderType+" = ?", req.OrderType)
		}
		if req.StartPayDate != "" && req.EndPayDate != "" {
			m = m.Where(dao.KsAdOrderSettle.Columns().PayDate+" >= ?", req.StartPayDate)
			m = m.Where(dao.KsAdOrderSettle.Columns().PayDate+" <= ?", req.EndPayDate)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "pay_date desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var groupBy string
		if req.OrderType == "" || req.OrderType == commonConsts.AccountSale {
			groupBy = "advertiser_id, pay_date, pay_provider"
		} else {
			groupBy = "advertiser_id, pay_date, pay_provider, copyright_uid"
		}
		listRes.Total, err = m.Group(groupBy).Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		summaryM := m.Clone()
		var res []*model.KsAdOrderSettleListRes
		m = m.FieldSum("pay_amt", "payAmt").
			FieldSum("redund_price", "redundPrice").
			FieldSum("commission_price", "commissionPrice").
			FieldSum("settle_price", "settlePrice").
			FieldSum("sub_account_order_pay_amt", "subAccountOrderPayAmt").
			FieldSum("cur_account_order_pay_amt", "curAccountOrderPayAmt").
			FieldSum("copyright_distribution_order_pay_amt", "copyrightDistributionOrderPayAmt").
			FieldSum("saler_distribution_order_pay_amt", "salerDistributionOrderPayAmt").
			FieldSum("expenditure", "expenditure")
		if req.OrderType == "" || req.OrderType == commonConsts.AccountSale {
			m = m.Fields("advertiser_id as advertiserId").
				Fields("pay_date as payDate").
				Fields("pay_provider as payProvider")
		} else {
			m = m.Fields("advertiser_id as advertiserId").
				Fields("pay_date as payDate").
				Fields("pay_provider as payProvider").
				Fields("copyright_uid as copyrightUid").
				Fields("ANY_VALUE(copyright_name) as copyrightName")
		}
		err = m.Page(req.PageNum, req.PageSize).Group(groupBy).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		// 查询账户名称
		var advertiserIds = make([]int64, 0)
		for _, v := range res {
			advertiserIds = append(advertiserIds, v.AdvertiserId)
		}
		ksAdInfos, _ := service.KsAdInfo().GetByAdvertiserIds(ctx, advertiserIds)
		listRes.List = make([]*model.KsAdOrderSettleListRes, len(res))
		for k, v := range res {
			for _, adInfo := range ksAdInfos {
				if adInfo.AdvertiserId == v.AdvertiserId {
					v.AdvertiserName = adInfo.AdAccountName
					break
				}
			}
			listRes.List[k] = v
		}
		// 计算汇总数据
		var summary *model.KsAdOrderSettleListRes
		err = summaryM.FieldSum("pay_amt", "payAmt").
			FieldSum("redund_price", "redundPrice").
			FieldSum("commission_price", "commissionPrice").
			FieldSum("settle_price", "settlePrice").
			FieldSum("sub_account_order_pay_amt", "subAccountOrderPayAmt").
			FieldSum("cur_account_order_pay_amt", "curAccountOrderPayAmt").
			FieldSum("copyright_distribution_order_pay_amt", "copyrightDistributionOrderPayAmt").
			FieldSum("saler_distribution_order_pay_amt", "salerDistributionOrderPayAmt").
			FieldSum("expenditure", "expenditure").
			Scan(&summary)
		liberr.ErrIsNil(ctx, err, "获取汇总数据失败")
		listRes.Summary = summary
	})
	return
}

func (s *sKsAdOrderSettle) GetExportData(ctx context.Context, req *model.KsAdOrderSettleSearchReq) (listRes []*model.KsAdOrderSettleListRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res, err1 := s.List(ctx, req)
		liberr.ErrIsNil(ctx, err1, "获取数据失败")
		listRes = res.List
	})
	return
}

func (s *sKsAdOrderSettle) GetById(ctx context.Context, id int64) (res *model.KsAdOrderSettleInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.KsAdOrderSettle.Ctx(ctx).WithAll().Where(dao.KsAdOrderSettle.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sKsAdOrderSettle) Add(ctx context.Context, req *model.KsAdOrderSettleAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdOrderSettle.Ctx(ctx).Insert(do.KsAdOrderSettle{
			AdvertiserId:                     req.AdvertiserId,
			SettleDate:                       req.SettleDate,
			SeriesId:                         req.SeriesId,
			SeriesName:                       req.SeriesName,
			CopyrightUid:                     req.CopyrightUid,
			CopyrightName:                    req.CopyrightName,
			SalerUid:                         req.SalerUid,
			SalerName:                        req.SalerName,
			SubAccountUid:                    req.SubAccountUid,
			SubAccountName:                   req.SubAccountName,
			PayProvider:                      req.PayProvider,
			PayAmt:                           req.PayAmt,
			RedundPrice:                      req.RedundPrice,
			CommissionPrice:                  req.CommissionPrice,
			SettlePrice:                      req.SettlePrice,
			SubAccountOrderPayAmt:            req.SubAccountOrderPayAmt,
			CurAccountOrderPayAmt:            req.CurAccountOrderPayAmt,
			CopyrightDistributionOrderPayAmt: req.CopyrightDistributionOrderPayAmt,
			SalerDistributionOrderPayAmt:     req.SalerDistributionOrderPayAmt,
			Expenditure:                      req.Expenditure,
			PayDate:                          req.PayDate,
			OrderType:                        req.OrderType,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdOrderSettle) BatchAdd(ctx context.Context, batchAddReq []*model.KsAdOrderSettleAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		list := make([]do.KsAdOrderSettle, 0)
		for _, req := range batchAddReq {
			list = append(list, do.KsAdOrderSettle{
				AdvertiserId:                     req.AdvertiserId,
				SettleDate:                       req.SettleDate,
				SeriesId:                         req.SeriesId,
				SeriesName:                       req.SeriesName,
				CopyrightUid:                     req.CopyrightUid,
				CopyrightName:                    req.CopyrightName,
				SalerUid:                         req.SalerUid,
				SalerName:                        req.SalerName,
				SubAccountUid:                    req.SubAccountUid,
				SubAccountName:                   req.SubAccountName,
				PayProvider:                      req.PayProvider,
				PayAmt:                           req.PayAmt,
				RedundPrice:                      req.RedundPrice,
				CommissionPrice:                  req.CommissionPrice,
				SettlePrice:                      req.SettlePrice,
				SubAccountOrderPayAmt:            req.SubAccountOrderPayAmt,
				CurAccountOrderPayAmt:            req.CurAccountOrderPayAmt,
				CopyrightDistributionOrderPayAmt: req.CopyrightDistributionOrderPayAmt,
				SalerDistributionOrderPayAmt:     req.SalerDistributionOrderPayAmt,
				Expenditure:                      req.Expenditure,
				PayDate:                          req.PayDate,
				OrderType:                        req.OrderType,
			})
		}
		_, err = dao.KsAdOrderSettle.Ctx(ctx).Save(list)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sKsAdOrderSettle) Edit(ctx context.Context, req *model.KsAdOrderSettleEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdOrderSettle.Ctx(ctx).WherePri(req.Id).Update(do.KsAdOrderSettle{
			AdvertiserId:                     req.AdvertiserId,
			SettleDate:                       req.SettleDate,
			SeriesId:                         req.SeriesId,
			SeriesName:                       req.SeriesName,
			CopyrightUid:                     req.CopyrightUid,
			CopyrightName:                    req.CopyrightName,
			SalerUid:                         req.SalerUid,
			SalerName:                        req.SalerName,
			SubAccountUid:                    req.SubAccountUid,
			SubAccountName:                   req.SubAccountName,
			PayProvider:                      req.PayProvider,
			PayAmt:                           req.PayAmt,
			RedundPrice:                      req.RedundPrice,
			CommissionPrice:                  req.CommissionPrice,
			SettlePrice:                      req.SettlePrice,
			SubAccountOrderPayAmt:            req.SubAccountOrderPayAmt,
			CurAccountOrderPayAmt:            req.CurAccountOrderPayAmt,
			CopyrightDistributionOrderPayAmt: req.CopyrightDistributionOrderPayAmt,
			SalerDistributionOrderPayAmt:     req.SalerDistributionOrderPayAmt,
			Expenditure:                      req.Expenditure,
			PayDate:                          req.PayDate,
			OrderType:                        req.OrderType,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sKsAdOrderSettle) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.KsAdOrderSettle.Ctx(ctx).Delete(dao.KsAdOrderSettle.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sKsAdOrderSettle) RunCalcAdOrderSettleStat(ctx context.Context, req *model.KsAdOrderSettleSearchReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		innerContext, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()
		startTime := req.StartPayDate
		endTime := req.EndPayDate
		for {
			if startTime > endTime {
				break
			}
			errors := s.CalcAdOrderSettleStat(innerContext, startTime)
			if errors != nil {
				g.Log().Error(innerContext, errors)
			}
			startTime = libUtils.PlusDays(startTime, 1)
		}
	})
	return
}

func (s *sKsAdOrderSettle) CalcAdOrderSettleStatTask(ctx context.Context) {
	err := g.Try(ctx, func(ctx context.Context) {
		pool := goredis.NewPool(commonService.GetGoRedis())
		rs := redsync.New(pool)
		mutex := rs.NewMutex(commonConsts.PlatKsAdOrderSettleStatLock, redsync.WithRetryDelay(50*time.Millisecond))
		// TryLockContext只尝试锁定一次，无论成功或失败立即返回，无需重试
		err := mutex.TryLockContext(ctx)
		liberr.ErrIsNil(ctx, err, fmt.Sprintf("Redisson没有获取到分布式锁：%s", commonConsts.PlatKsAdOrderSettleStatLock))
		// 释放锁
		defer mutex.UnlockContext(ctx)
		yesterday := gtime.Now().AddDate(0, 0, -5).Format("Y-m-d")
		err = s.CalcAdOrderSettleStat(ctx, yesterday)
		liberr.ErrIsNil(ctx, err, "快手订单日结算汇总统计失败")
		sysService.SysJobLog().Add(ctx, &sysDo.SysJobLog{
			TargetName: "CalcAdOrderSettleStatTask",
			CreatedAt:  gtime.Now(),
			Result:     "快手订单日结算汇总统计，执行成功",
		})
	})
	if err != nil {
		g.Log().Error(ctx, err)
	}
}

func (s *sKsAdOrderSettle) CalcAdOrderSettleStat(ctx context.Context, statDate string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 查询所有快手账号
		advertiserList, err1 := service.KsAdAccountInfo().GetAllAdvertiserIds(ctx)
		liberr.ErrIsNil(ctx, err1, "获取快手账号失败")
		for _, advertiser := range advertiserList {
			advertiserId := advertiser.AdvertiserId
			accessToken := api.GetAccessTokenByAppIdCache(advertiserId)
			if accessToken == "" {
				g.Log().Error(ctx, fmt.Sprintf("------------- 获取快手token失败, advertiserId: %v --------------------", advertiserId))
				continue
			}
			startTimestamps, endTimestamps, _ := libUtils.GetDayTimestamps(statDate)
			s.QueryAdOrderSettle(ctx, advertiserId, accessToken, startTimestamps, endTimestamps)
		}
	})
	return
}

func (s *sKsAdOrderSettle) QueryAdOrderSettle(ctx context.Context, advertiserId int64, accessToken string, payBeginDate int64, payEndDate int64) {
	var financeQueryTypes = []int64{2, 4}
	var orderTypeMap = map[int64]string{
		2: commonConsts.AccountSale,
		3: commonConsts.SubAccountSale,
		4: commonConsts.DistributionSale,
	}
	for _, financeQueryType := range financeQueryTypes {
		var pageNum = 1
		var pageSize = 500
		for {
			querySettleDataRes, err1 := api.GetKSApiClient().QuerySettleDataService.
				AccessToken(accessToken).
				SetReq(api.QuerySettleDataReq{
					AdvertiserID:     advertiserId,
					PageNum:          pageNum,
					PageSize:         pageSize,
					FinanceQueryType: financeQueryType,
					ExtraDimensions:  []string{"seriesId"},
					PayBeginDate:     payBeginDate,
					PayEndDate:       payEndDate,
				}).Do()
			if err1 != nil {
				g.Log().Errorf(ctx, "advertiserId: %v查询订单日结算汇总失败: %v", advertiserId, err1)
				break
			}
			if querySettleDataRes.Data == nil || len(querySettleDataRes.Data.CoreDataList) == 0 {
				break
			}
			batchAddReq := make([]*model.KsAdOrderSettleAddReq, 0)
			for _, item := range querySettleDataRes.Data.CoreDataList {
				batchAddReq = append(batchAddReq, &model.KsAdOrderSettleAddReq{
					AdvertiserId:                     advertiserId,
					SettleDate:                       item.SettleDate,
					SeriesId:                         item.SeriesID,
					SeriesName:                       item.SeriesName,
					CopyrightUid:                     item.CopyrightUID,
					CopyrightName:                    item.CopyrightName,
					SalerUid:                         item.SalerUID,
					SalerName:                        item.SalerName,
					SubAccountUid:                    item.SubAccountUID,
					SubAccountName:                   item.SubAccountName,
					PayProvider:                      item.PayProvider,
					PayAmt:                           item.PayAmt,
					RedundPrice:                      item.RedundPrice,
					CommissionPrice:                  item.CommissionPrice,
					SettlePrice:                      item.SettlePrice,
					SubAccountOrderPayAmt:            item.SubAccountOrderPayAmt,
					CurAccountOrderPayAmt:            item.CurAccountOrderPayAmt,
					CopyrightDistributionOrderPayAmt: item.CopyrightDistributionOrderPayAmt,
					SalerDistributionOrderPayAmt:     item.SalerDistributionOrderPayAmt,
					Expenditure:                      item.Expenditure,
					PayDate:                          item.PayDate,
					OrderType:                        orderTypeMap[financeQueryType],
				})
			}
			_ = s.BatchAdd(ctx, batchAddReq)
			if pageNum*pageSize >= querySettleDataRes.Data.TotalCount {
				break
			}
			pageNum++
		}
	}
}
