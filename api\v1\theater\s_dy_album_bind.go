// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-22 17:26:21
// 生成路径: api/v1/theater/s_dy_album_bind.go
// 生成人：cq
// desc:抖音页面绑定表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package theater

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/theater/model"
)

// SDyAlbumBindSearchReq 分页请求参数
type SDyAlbumBindSearchReq struct {
	g.Meta `path:"/list" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"get" summary:"页面绑定列表"`
	commonApi.Author
	model.SDyAlbumBindSearchReq
}

// SDyAlbumBindSearchRes 列表返回结果
type SDyAlbumBindSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SDyAlbumBindSearchRes
}

// SDyAlbumBindAddReq 添加操作请求参数
type SDyAlbumBindAddReq struct {
	g.Meta `path:"/add" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"添加页面绑定"`
	commonApi.Author
	*model.SDyAlbumBindAddReq
}

// SDyAlbumBindAddRes 添加操作返回结果
type SDyAlbumBindAddRes struct {
	commonApi.EmptyRes
}

// SDyAlbumBindBatchAddReq 批量添加操作请求参数
type SDyAlbumBindBatchAddReq struct {
	g.Meta `path:"/batchAdd" tags:"短剧推广 - 抖音内容库 - 短剧库" method:"post" summary:"批量添加页面绑定"`
	commonApi.Author
	*model.SDyAlbumBindBatchAddReq
}

// SDyAlbumBindBatchAddRes 批量添加操作返回结果
type SDyAlbumBindBatchAddRes struct {
	commonApi.EmptyRes
}
