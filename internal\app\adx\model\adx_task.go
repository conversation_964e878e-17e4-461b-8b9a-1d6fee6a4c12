// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-09 11:03:44
// 生成路径: internal/app/adx/model/adx_task.go
// 生成人：cyao
// desc:ADX任务主表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxTaskInfoRes is the golang structure for table adx_task.
type AdxTaskInfoRes struct {
	gmeta.Meta     `orm:"table:adx_task"`
	Id             int64       `orm:"id,primary" json:"id" dc:"主键ID"`                           // 主键ID
	TaskName       string      `orm:"task_name" json:"taskName" dc:"任务名称"`                      // 任务名称
	OperationType  string      `orm:"operation_type" json:"operationType" dc:"操作类型（批量下载、单条下载）"` // 操作类型（批量下载、单条下载）
	OperationCount int         `orm:"operation_count" json:"operationCount" dc:"操作数量（下载视频数量）"`  // 操作数量（下载视频数量）
	OperationTime  *gtime.Time `orm:"operation_time" json:"operationTime" dc:"操作时间"`            // 操作时间
	Status         string      `orm:"status" json:"status" dc:"执行状态（执行中、执行完成、执行失败）"`            // 执行状态（执行中、执行完成、执行失败）
	Operator       string      `orm:"operator" json:"operator" dc:"提交任务的用户名"`                   // 提交任务的用户名
	DownloadUrl    string      `orm:"download_url" json:"downloadUrl" dc:"打包下载地址（OSS压缩包地址）"`    // 打包下载地址（OSS压缩包地址）
	FailLog        string      `orm:"fail_log" json:"failLog" dc:"任务执行失败日志"`                    // 任务执行失败日志
	TotalFileSize  int64       `orm:"total_file_size" json:"totalFileSize" dc:"总文件大小，单位：字节"`    // 总文件大小，单位：字节
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                    // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                    // 更新时间
}

type AdxTaskListRes struct {
	Id             int64           `json:"id" dc:"主键ID"`
	TaskName       string          `json:"taskName" dc:"任务名称"`
	OperationType  string          `json:"operationType" dc:"操作类型（批量下载、单条下载）"`
	OperationCount int             `json:"operationCount" dc:"操作数量（下载视频数量）"`
	OperationTime  *gtime.Time     `json:"operationTime" dc:"操作时间"`
	Status         string          `json:"status" dc:"执行状态（执行中、执行完成、执行失败）"`
	Operator       string          `json:"operator" dc:"提交任务的用户名"`
	DownloadUrl    []*DownloadInfo `json:"downloadUrl" dc:"打包下载地址（OSS压缩包地址）"`
	FailLog        string          `json:"failLog" dc:"任务执行失败日志"`
	TotalFileSize  int64           `json:"totalFileSize" dc:"总文件大小，单位：字节"`
	CreatedAt      *gtime.Time     `json:"createdAt" dc:"创建时间"`
}

type DownloadInfo struct {
	ItemId int64  `json:"ItemId" dc:"主键ID"`
	Url    string `json:"url" dc:"下载地址"`
}

// AdxTaskSearchReq 分页请求参数
type AdxTaskSearchReq struct {
	comModel.PageReq
	Id             string `p:"id" dc:"主键ID"`                                                                   //主键ID
	TaskName       string `p:"taskName" dc:"任务名称"`                                                             //任务名称
	OperationType  string `p:"operationType" dc:"操作类型（批量下载、单条下载）"`                                             //操作类型（批量下载、单条下载）
	OperationCount string `p:"operationCount" v:"operationCount@integer#操作数量（下载视频数量）需为整数" dc:"操作数量（下载视频数量）"`   //操作数量（下载视频数量）
	OperationTime  string `p:"operationTime" v:"operationTime@datetime#操作时间需为YYYY-MM-DD hh:mm:ss格式" dc:"操作时间"` //操作时间
	Status         string `p:"status" dc:"执行状态（执行中、执行完成、执行失败）"`                                                //执行状态（执行中、执行完成、执行失败）
	Operator       string `p:"operator" dc:"提交任务的用户名"`                                                         //提交任务的用户名
	DownloadUrl    string `p:"downloadUrl" dc:"打包下载地址（OSS压缩包地址）"`                                              //打包下载地址（OSS压缩包地址）
	FailLog        string `p:"failLog" dc:"任务执行失败日志"`                                                          //任务执行失败日志
	TotalFileSize  string `p:"totalFileSize" v:"totalFileSize@integer#总文件大小，单位：字节需为整数" dc:"总文件大小，单位：字节"`       //总文件大小，单位：字节
	CreatedAt      string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`         //创建时间
	StartTime      string `p:"startTime" v:"startTime@datetime#开始时间需为YYYY-MM-DD hh:mm:ss格式" dc:"开始时间"`
	EndTime        string `p:"endTime" v:"endTime@datetime#结束时间需为YYYY-MM-DD hh:mm:ss格式" dc:"结束时间"`
}

// AdxTaskSearchRes 列表返回结果
type AdxTaskSearchRes struct {
	comModel.ListRes
	List []*AdxTaskListRes `json:"list"`
}

type CreateByMaterialReq struct {
	MaterialIds []uint64 `p:"materialIds" v:"required#素材ID不能为空" dc:"素材ID"`
}
type CreateByMaterialRes struct {
	TaskId       int64             `json:"taskId" dc:"任务ID"`
	TaskName     string            `json:"taskName" dc:"任务名称"`
	MaterialList []MaterialListRes `json:"materialList" dc:"素材列表"`
}

type MaterialListRes struct {
	ItemTaskId int64  `json:"itemTaskId" dc:"子任务id"`
	MaterialId uint64 `json:"materialId" dc:"素材ID"`
}

type UpdateStatusReq struct {
	Id         int64  `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	Status     string `p:"status" v:"required#执行状态（执行中、执行完成、执行失败）不能为空" dc:"执行状态（执行中、执行完成、执行失败）"`
	FailReason string `p:"failReason" dc:"失败原因"`
}

// AdxTaskAddReq 添加操作请求参数
type AdxTaskAddReq struct {
	TaskName       string      `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	OperationType  string      `p:"operationType" v:"required#操作类型（批量下载、单条下载）不能为空" dc:"操作类型（批量下载、单条下载）"`
	OperationCount int         `p:"operationCount"  dc:"操作数量（下载视频数量）"`
	OperationTime  *gtime.Time `p:"operationTime"  dc:"操作时间"`
	Status         string      `p:"status" v:"required#执行状态（执行中、执行完成、执行失败）不能为空" dc:"执行状态（执行中、执行完成、执行失败）"`
	Operator       string      `p:"operator"  dc:"提交任务的用户名"`
	DownloadUrl    string      `p:"downloadUrl"  dc:"打包下载地址（OSS压缩包地址）"`
	FailLog        string      `p:"failLog"  dc:"任务执行失败日志"`
	TotalFileSize  int64       `p:"totalFileSize"  dc:"总文件大小，单位：字节"`
}

// AdxTaskEditReq 修改操作请求参数
type AdxTaskEditReq struct {
	Id             int64       `p:"id" v:"required#主键ID不能为空" dc:"主键ID"`
	TaskName       string      `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	OperationType  string      `p:"operationType" v:"required#操作类型（批量下载、单条下载）不能为空" dc:"操作类型（批量下载、单条下载）"`
	OperationCount int         `p:"operationCount"  dc:"操作数量（下载视频数量）"`
	OperationTime  *gtime.Time `p:"operationTime"  dc:"操作时间"`
	Status         string      `p:"status" v:"required#执行状态（执行中、执行完成、执行失败）不能为空" dc:"执行状态（执行中、执行完成、执行失败）"`
	Operator       string      `p:"operator"  dc:"提交任务的用户名"`
	DownloadUrl    string      `p:"downloadUrl"  dc:"打包下载地址（OSS压缩包地址）"`
	FailLog        string      `p:"failLog"  dc:"任务执行失败日志"`
	TotalFileSize  int64       `p:"totalFileSize"  dc:"总文件大小，单位：字节"`
}
