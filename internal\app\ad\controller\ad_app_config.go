// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-13 10:42:39
// 生成路径: internal/app/ad/controller/ad_app_config.go
// 生成人：cq
// desc:广告应用配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAppConfigController struct {
	systemController.BaseController
}

var AdAppConfig = new(adAppConfigController)

// List 列表
func (c *adAppConfigController) List(ctx context.Context, req *ad.AdAppConfigSearchReq) (res *ad.AdAppConfigSearchRes, err error) {
	res = new(ad.AdAppConfigSearchRes)
	res.AdAppConfigSearchRes, err = service.AdAppConfig().List(ctx, &req.AdAppConfigSearchReq)
	return
}

// Get 获取广告应用配置表
func (c *adAppConfigController) Get(ctx context.Context, req *ad.AdAppConfigGetReq) (res *ad.AdAppConfigGetRes, err error) {
	res = new(ad.AdAppConfigGetRes)
	res.AdAppConfigInfoRes, err = service.AdAppConfig().GetById(ctx, req.Id)
	return
}

// Add 添加广告应用配置表
func (c *adAppConfigController) Add(ctx context.Context, req *ad.AdAppConfigAddReq) (res *ad.AdAppConfigAddRes, err error) {
	err = service.AdAppConfig().Add(ctx, req.AdAppConfigAddReq)
	return
}

// Edit 修改广告应用配置表
func (c *adAppConfigController) Edit(ctx context.Context, req *ad.AdAppConfigEditReq) (res *ad.AdAppConfigEditRes, err error) {
	err = service.AdAppConfig().Edit(ctx, req.AdAppConfigEditReq)
	return
}

// Delete 删除广告应用配置表
func (c *adAppConfigController) Delete(ctx context.Context, req *ad.AdAppConfigDeleteReq) (res *ad.AdAppConfigDeleteRes, err error) {
	err = service.AdAppConfig().Delete(ctx, req.Ids)
	return
}

// GetAuthUrl 获取授权链接
func (c *adAppConfigController) GetAuthUrl(ctx context.Context, req *ad.GetAuthUrlReq) (res *ad.GetAuthUrlRes, err error) {
	res = new(ad.GetAuthUrlRes)
	res.AuthUrl, err = service.AdAppConfig().GetAuthUrl(ctx, req.EvnType, req.AppType, req.AuthUserType, req.MajordomoUserName)
	return
}
