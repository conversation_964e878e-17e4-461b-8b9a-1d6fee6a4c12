// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-16 11:16:21
// 生成路径: api/v1/ad/fq_ad_account_users.go
// 生成人：gfast
// desc:番茄账号权限用户关联相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// FqAdAccountUsersSearchReq 分页请求参数
type FqAdAccountUsersSearchReq struct {
	g.Meta `path:"/list" tags:"番茄账号权限用户关联" method:"get" summary:"番茄账号权限用户关联列表"`
	commonApi.Author
	model.FqAdAccountUsersSearchReq
}

// FqAdAccountUsersSearchRes 列表返回结果
type FqAdAccountUsersSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountUsersSearchRes
}

// FqAdAccountUsersExportReq 导出请求
type FqAdAccountUsersExportReq struct {
	g.Meta `path:"/export" tags:"番茄账号权限用户关联" method:"get" summary:"番茄账号权限用户关联导出"`
	commonApi.Author
	model.FqAdAccountUsersSearchReq
}

// FqAdAccountUsersExportRes 导出响应
type FqAdAccountUsersExportRes struct {
	commonApi.EmptyRes
}

// FqAdAccountUsersAddReq 添加操作请求参数
type FqAdAccountUsersAddReq struct {
	g.Meta `path:"/add" tags:"番茄账号权限用户关联" method:"post" summary:"番茄账号权限用户关联添加"`
	commonApi.Author
	*model.FqAdAccountUsersAddReq
}

// FqAdAccountUsersAddRes 添加操作返回结果
type FqAdAccountUsersAddRes struct {
	commonApi.EmptyRes
}

// FqAdAccountUsersEditReq 修改操作请求参数
type FqAdAccountUsersEditReq struct {
	g.Meta `path:"/edit" tags:"番茄账号权限用户关联" method:"put" summary:"番茄账号权限用户关联修改"`
	commonApi.Author
	*model.FqAdAccountUsersEditReq
}

// FqAdAccountUsersEditRes 修改操作返回结果
type FqAdAccountUsersEditRes struct {
	commonApi.EmptyRes
}

// FqAdAccountUsersGetReq 获取一条数据请求
type FqAdAccountUsersGetReq struct {
	g.Meta `path:"/get" tags:"番茄账号权限用户关联" method:"get" summary:"获取番茄账号权限用户关联信息"`
	commonApi.Author
	DistributorId int64 `p:"distributorId" v:"required#主键必须"` //通过主键获取
}

// FqAdAccountUsersGetRes 获取一条数据结果
type FqAdAccountUsersGetRes struct {
	g.Meta `mime:"application/json"`
	*model.FqAdAccountUsersInfoRes
}

// FqAdAccountUsersDeleteReq 删除数据请求
type FqAdAccountUsersDeleteReq struct {
	g.Meta `path:"/delete" tags:"番茄账号权限用户关联" method:"delete" summary:"删除番茄账号权限用户关联"`
	commonApi.Author
	DistributorIds []int64 `p:"distributorIds" v:"required#主键必须"` //通过主键删除
}

// FqAdAccountUsersDeleteRes 删除数据返回
type FqAdAccountUsersDeleteRes struct {
	commonApi.EmptyRes
}
