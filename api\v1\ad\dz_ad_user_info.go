// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: api/v1/ad/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdUserInfoSearchReq 分页请求参数
type DzAdUserInfoSearchReq struct {
	g.Meta `path:"/list" tags:"广告注册用户信息表" method:"get" summary:"广告注册用户信息表列表"`
	commonApi.Author
	model.DzAdUserInfoSearchReq
}

// DzAdUserInfoSearchRes 列表返回结果
type DzAdUserInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdUserInfoSearchRes
}

// DzAdUserInfoExportReq 导出请求
type DzAdUserInfoExportReq struct {
	g.Meta `path:"/export" tags:"广告注册用户信息表" method:"get" summary:"广告注册用户信息表导出"`
	commonApi.Author
	model.DzAdUserInfoSearchReq
}

// DzAdUserInfoExportRes 导出响应
type DzAdUserInfoExportRes struct {
	commonApi.EmptyRes
}

// DzAdUserInfoAddReq 添加操作请求参数
type DzAdUserInfoAddReq struct {
	g.Meta `path:"/add" tags:"广告注册用户信息表" method:"post" summary:"广告注册用户信息表添加"`
	commonApi.Author
	*model.DzAdUserInfoAddReq
}

// DzAdUserInfoAddRes 添加操作返回结果
type DzAdUserInfoAddRes struct {
	commonApi.EmptyRes
}

// DzAdUserInfoEditReq 修改操作请求参数
type DzAdUserInfoEditReq struct {
	g.Meta `path:"/edit" tags:"广告注册用户信息表" method:"put" summary:"广告注册用户信息表修改"`
	commonApi.Author
	*model.DzAdUserInfoEditReq
}

// DzAdUserInfoEditRes 修改操作返回结果
type DzAdUserInfoEditRes struct {
	commonApi.EmptyRes
}

// DzAdUserInfoGetReq 获取一条数据请求
type DzAdUserInfoGetReq struct {
	g.Meta `path:"/get" tags:"广告注册用户信息表" method:"get" summary:"获取广告注册用户信息表信息"`
	commonApi.Author
	UserId string `p:"userId" v:"required#主键必须"` //通过主键获取
}

// DzAdUserInfoGetRes 获取一条数据结果
type DzAdUserInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdUserInfoInfoRes
}

// DzAdUserInfoDeleteReq 删除数据请求
type DzAdUserInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告注册用户信息表" method:"delete" summary:"删除广告注册用户信息表"`
	commonApi.Author
	UserIds []string `p:"userIds" v:"required#主键必须"` //通过主键删除
}

// DzAdUserInfoDeleteRes 删除数据返回
type DzAdUserInfoDeleteRes struct {
	commonApi.EmptyRes
}
