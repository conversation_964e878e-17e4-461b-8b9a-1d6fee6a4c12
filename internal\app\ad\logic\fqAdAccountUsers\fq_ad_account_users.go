// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-04-16 11:16:21
// 生成路径: internal/app/ad/logic/fq_ad_account_users.go
// 生成人：gfast
// desc:番茄账号权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterFqAdAccountUsers(New())
}

func New() service.IFqAdAccountUsers {
	return &sFqAdAccountUsers{}
}

type sFqAdAccountUsers struct{}

func (s *sFqAdAccountUsers) List(ctx context.Context, req *model.FqAdAccountUsersSearchReq) (listRes *model.FqAdAccountUsersSearchRes, err error) {
	listRes = new(model.FqAdAccountUsersSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAccountUsers.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdAccountUsers.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if len(req.DistributorIds) > 0 {
			m = m.WhereIn(dao.FqAdAccountUsers.Columns().DistributorId, req.DistributorIds)
		}
		if req.SpecifyUserId != "" {
			m = m.Where(dao.FqAdAccountUsers.Columns().SpecifyUserId+" = ?", req.SpecifyUserId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.FqAdAccountUsersListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.FqAdAccountUsersListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.FqAdAccountUsersListRes{
				DistributorId: v.DistributorId,
				SpecifyUserId: v.SpecifyUserId,
			}
		}
	})
	return
}

func (s *sFqAdAccountUsers) GetExportData(ctx context.Context, req *model.FqAdAccountUsersSearchReq) (listRes []*model.FqAdAccountUsersInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.FqAdAccountUsers.Ctx(ctx).WithAll()
		if req.DistributorId != "" {
			m = m.Where(dao.FqAdAccountUsers.Columns().DistributorId+" = ?", req.DistributorId)
		}
		if req.SpecifyUserId != "" {
			m = m.Where(dao.FqAdAccountUsers.Columns().SpecifyUserId+" = ?", req.SpecifyUserId)
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "distributor_id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sFqAdAccountUsers) GetBySpecifyUserId(ctx context.Context, distributorId int64) (res *model.FqAdAccountUsersInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.FqAdAccountUsers.Ctx(ctx).WithAll().Where(dao.FqAdAccountUsers.Columns().SpecifyUserId, distributorId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sFqAdAccountUsers) Add(ctx context.Context, req *model.FqAdAccountUsersAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountUsers.Ctx(ctx).Insert(do.FqAdAccountUsers{
			DistributorId: req.DistributorId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sFqAdAccountUsers) Edit(ctx context.Context, req *model.FqAdAccountUsersEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountUsers.Ctx(ctx).WherePri(req.DistributorId).Update(do.FqAdAccountUsers{})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sFqAdAccountUsers) Delete(ctx context.Context, distributorIds []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.FqAdAccountUsers.Ctx(ctx).Delete(dao.FqAdAccountUsers.Columns().SpecifyUserId+" in (?)", distributorIds)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
