// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-02-13 16:19:20
// 生成路径: internal/app/ad/logic/ad_anchor_point_upload.go
// 生成人：cyao
// desc:推送到巨量的原生锚点
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdAnchorPointUpload(New())
}

func New() service.IAdAnchorPointUpload {
	return &sAdAnchorPointUpload{}
}

type sAdAnchorPointUpload struct{}

func (s *sAdAnchorPointUpload) List(ctx context.Context, req *model.AdAnchorPointUploadSearchReq) (listRes *model.AdAnchorPointUploadSearchRes, err error) {
	listRes = new(model.AdAnchorPointUploadSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdAnchorPointUpload.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdAnchorPointUpload.Columns().Id+" = ?", req.Id)
		}
		if req.AnchorType != "" {
			m = m.Where(dao.AdAnchorPointUpload.Columns().AnchorType+" = ?", req.AnchorType)
		}
		if req.AnchorPointId != "" {
			m = m.Where(dao.AdAnchorPointUpload.Columns().AnchorPointId+" = ?", gconv.Int(req.AnchorPointId))
		}
		if req.AnchorId != "" {
			m = m.Where(dao.AdAnchorPointUpload.Columns().AnchorId+" = ?", req.AnchorId)
		}
		if req.CreateTime != "" {
			m = m.Where(dao.AdAnchorPointUpload.Columns().CreateTime+" = ?", gconv.Time(req.CreateTime))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdAnchorPointUploadListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAnchorPointUploadListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdAnchorPointUploadListRes{
				Id:            v.Id,
				AnchorType:    v.AnchorType,
				AnchorPointId: v.AnchorPointId,
				AnchorId:      v.AnchorId,
				CreateTime:    v.CreateTime,
			}
		}
	})
	return
}

func (s *sAdAnchorPointUpload) GetById(ctx context.Context, id int) (res *model.AdAnchorPointUploadInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdAnchorPointUpload.Ctx(ctx).WithAll().Where(dao.AdAnchorPointUpload.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdAnchorPointUpload) Add(ctx context.Context, req *model.AdAnchorPointUploadAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointUpload.Ctx(ctx).Insert(do.AdAnchorPointUpload{
			AnchorType:    req.AnchorType,
			AnchorPointId: req.AnchorPointId,
			AnchorId:      req.AnchorId,
			CreateTime:    req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdAnchorPointUpload) Edit(ctx context.Context, req *model.AdAnchorPointUploadEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointUpload.Ctx(ctx).WherePri(req.Id).Update(do.AdAnchorPointUpload{
			AnchorType:    req.AnchorType,
			AnchorPointId: req.AnchorPointId,
			AnchorId:      req.AnchorId,
			CreateTime:    req.CreateTime,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdAnchorPointUpload) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdAnchorPointUpload.Ctx(ctx).Delete(dao.AdAnchorPointUpload.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
