/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// AgentInfoGetV2ApiService AgentInfoGetV2Api service
type AgentInfoGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *AgentInfoGetV2Request
}

type AgentInfoGetV2Request struct {
	AdvertiserIds *[]int64
	Fields        *[]*models.AgentInfoV2Fields
}

func (r *AgentInfoGetV2ApiService) SetCfg(cfg *conf.Configuration) *AgentInfoGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *AgentInfoGetV2ApiService) AgentInfoGetV2Request(agentInfoGetV2Request AgentInfoGetV2Request) *AgentInfoGetV2ApiService {
	r.Request = &agentInfoGetV2Request
	return r
}

func (r *AgentInfoGetV2ApiService) AccessToken(accessToken string) *AgentInfoGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *AgentInfoGetV2ApiService) Do() (data *models.AgentInfoV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/agent/info/"
	localVarQueryParams := map[string]string{}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_ids", r.Request.AdvertiserIds)
	if r.Request.Fields != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "fields", r.Request.Fields)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.AgentInfoV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.AgentInfoV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/agent/info/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
