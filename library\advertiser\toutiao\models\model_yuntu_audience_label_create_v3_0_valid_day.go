/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceLabelCreateV30ValidDay
type YuntuAudienceLabelCreateV30ValidDay int64

// List of yuntu_audience_label_create_v3.0_valid_day
const (
	Enum_120_YuntuAudienceLabelCreateV30ValidDay YuntuAudienceLabelCreateV30ValidDay = 120
	Enum_150_YuntuAudienceLabelCreateV30ValidDay YuntuAudienceLabelCreateV30ValidDay = 150
	Enum_180_YuntuAudienceLabelCreateV30ValidDay YuntuAudienceLabelCreateV30ValidDay = 180
	Enum_30_YuntuAudienceLabelCreateV30ValidDay  YuntuAudienceLabelCreateV30ValidDay = 30
	Enum_60_YuntuAudienceLabelCreateV30ValidDay  YuntuAudienceLabelCreateV30ValidDay = 60
	Enum_90_YuntuAudienceLabelCreateV30ValidDay  YuntuAudienceLabelCreateV30ValidDay = 90
)

// Ptr returns reference to yuntu_audience_label_create_v3.0_valid_day value
func (v YuntuAudienceLabelCreateV30ValidDay) Ptr() *YuntuAudienceLabelCreateV30ValidDay {
	return &v
}
