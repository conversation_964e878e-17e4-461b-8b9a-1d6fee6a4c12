// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-16 15:34:39
// 生成路径: internal/app/ad/controller/ad_anchor_point.go
// 生成人：cyao
// desc:锚点表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adAnchorPointController struct {
	systemController.BaseController
}

var AdAnchorPoint = new(adAnchorPointController)

// List 列表
func (c *adAnchorPointController) List(ctx context.Context, req *ad.AdAnchorPointSearchReq) (res *ad.AdAnchorPointSearchRes, err error) {
	res = new(ad.AdAnchorPointSearchRes)
	res.AdAnchorPointSearchRes, err = service.AdAnchorPoint().List(ctx, &req.AdAnchorPointSearchReq)
	return
}

// Get 获取锚点表
func (c *adAnchorPointController) Get(ctx context.Context, req *ad.AdAnchorPointGetReq) (res *ad.AdAnchorPointGetRes, err error) {
	res = new(ad.AdAnchorPointGetRes)
	res.AdAnchorPointInfoRes, err = service.AdAnchorPoint().GetById(ctx, req.Id)
	return
}

// Add 添加锚点表
func (c *adAnchorPointController) Add(ctx context.Context, req *ad.AdAnchorPointAddReq) (res *ad.AdAnchorPointAddRes, err error) {
	err = service.AdAnchorPoint().Add(ctx, req.AdAnchorPointAddReq)
	return
}

// Edit 修改锚点表
func (c *adAnchorPointController) Edit(ctx context.Context, req *ad.AdAnchorPointEditReq) (res *ad.AdAnchorPointEditRes, err error) {
	err = service.AdAnchorPoint().Edit(ctx, req.AdAnchorPointEditReq)
	return
}

// Delete 删除锚点表
func (c *adAnchorPointController) Delete(ctx context.Context, req *ad.AdAnchorPointDeleteReq) (res *ad.AdAnchorPointDeleteRes, err error) {
	err = service.AdAnchorPoint().Delete(ctx, req.Ids)
	return
}
