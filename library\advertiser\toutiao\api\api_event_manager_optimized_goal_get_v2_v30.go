/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// EventManagerOptimizedGoalGetV2V30ApiService EventManagerOptimizedGoalGetV2V30Api service
type EventManagerOptimizedGoalGetV2V30ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApiV30EventManagerOptimizedGoalGetV2GetRequest
}

type ApiOpenApiV30EventManagerOptimizedGoalGetV2GetRequest struct {
	AdvertiserId       int64                                                       `json:"advertiserId"`
	LandingType        *models.EventManagerOptimizedGoalGetV2V30LandingType        `json:"landingType"`
	AdType             *models.EventManagerOptimizedGoalGetV2V30AdType             `json:"adType"`
	AssetType          *models.EventManagerOptimizedGoalGetV2V30AssetType          `json:"assetType"`
	AssetId            *int64                                                      `json:"assetId"`
	PackageName        *string                                                     `json:"packageName"`
	AppType            *models.EventManagerOptimizedGoalGetV2V30AppType            `json:"appType"`
	AppPromotionType   *models.EventManagerOptimizedGoalGetV2V30AppPromotionType   `json:"appPromotionType"`
	MarketingGoal      *models.EventManagerOptimizedGoalGetV2V30MarketingGoal      `json:"marketingGoal"`
	QuickAppId         *int64                                                      `json:"quickAppId"`
	DeliveryMode       *models.EventManagerOptimizedGoalGetV2V30DeliveryMode       `json:"deliveryMode"`
	MiniProgramId      *string                                                     `json:"miniProgramId"`
	DpaAdtype          *models.EventManagerOptimizedGoalGetV2V30DpaAdtype          `json:"dpaAdtype"`
	MicroPromotionType *models.EventManagerOptimizedGoalGetV2V30MicroPromotionType `json:"microPromotionType"`
	MicroAppInstanceId *int64                                                      `json:"microAppInstanceId"`
	DeliveryType       *models.EventManagerOptimizedGoalGetV2V30DeliveryType       `json:"deliveryType"`
}

func (r *EventManagerOptimizedGoalGetV2V30ApiService) SetCfg(cfg *conf.Configuration) *EventManagerOptimizedGoalGetV2V30ApiService {
	r.cfg = cfg
	return r
}

func (r *EventManagerOptimizedGoalGetV2V30ApiService) SetRequest(req ApiOpenApiV30EventManagerOptimizedGoalGetV2GetRequest) *EventManagerOptimizedGoalGetV2V30ApiService {
	r.Request = &req
	return r
}

func (r *EventManagerOptimizedGoalGetV2V30ApiService) AccessToken(accessToken string) *EventManagerOptimizedGoalGetV2V30ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *EventManagerOptimizedGoalGetV2V30ApiService) Do() (data *models.EventManagerOptimizedGoalGetV2V30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/event_manager/optimized_goal/get_v2/"
	localVarQueryParams := map[string]string{}

	if r.Request.AdvertiserId < 1 {
		return nil, errors.New("advertiserId must be greater than 1")
	}
	if r.Request.LandingType == nil {
		return nil, errors.New("landingType is required and must be specified")
	}
	if r.Request.AdType == nil {
		return nil, errors.New("adType is required and must be specified")
	}
	if r.Request.AssetType == nil {
		return nil, errors.New("assetType is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)

	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "landing_type", r.Request.LandingType)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "ad_type", r.Request.AdType)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "asset_type", r.Request.AssetType)
	if r.Request.AssetId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "asset_id", r.Request.AssetId)
	}
	if r.Request.PackageName != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "package_name", r.Request.PackageName)
	}
	if r.Request.AppType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "app_type", r.Request.AppType)
	}
	if r.Request.AppPromotionType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "app_promotion_type", r.Request.AppPromotionType)
	}
	if r.Request.MarketingGoal != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "marketing_goal", r.Request.MarketingGoal)
	}
	if r.Request.QuickAppId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "quick_app_id", r.Request.QuickAppId)
	}
	if r.Request.DeliveryMode != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "delivery_mode", r.Request.DeliveryMode)
	}
	if r.Request.MiniProgramId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "mini_program_id", r.Request.MiniProgramId)
	}
	if r.Request.DpaAdtype != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "dpa_adtype", r.Request.DpaAdtype)
	}
	if r.Request.MicroPromotionType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "micro_promotion_type", r.Request.MicroPromotionType)
	}
	if r.Request.MicroAppInstanceId != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "micro_app_instance_id", r.Request.MicroAppInstanceId)
	}
	if r.Request.DeliveryType != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "delivery_type", r.Request.DeliveryType)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.EventManagerOptimizedGoalGetV2V30Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.EventManagerOptimizedGoalGetV2V30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/event_manager/optimized_goal/get_v2解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}

// Execute executes the request
//
//	@return EventManagerOptimizedGoalGetV2V30Response
//func (a *EventManagerOptimizedGoalGetV2V30ApiService) getExecute(r *ApiOpenApiV30EventManagerOptimizedGoalGetV2GetRequest) (*EventManagerOptimizedGoalGetV2V30Response, *http.Response, error) {
//	var (
//		localVarHTTPMethod  = http.MethodGet
//		localVarPostBody    interface{}
//		formFiles           map[string]*FormFileInfo
//		localVarReturnValue *EventManagerOptimizedGoalGetV2V30Response
//	)
//
//	r.ctx = a.client.prepareCtx(r.ctx)
//
//	localBasePath := a.client.Cfg.GetBasePath()
//
//	localVarPath := localBasePath + "/open_api/v3.0/event_manager/optimized_goal/get_v2/"
//
//	localVarHeaderParams := make(map[string]string)
//	formFiles = make(map[string]*FormFileInfo)
//	localVarQueryParams := url.Values{}
//	localVarFormParams := url.Values{}
//	if r.advertiserId == nil {
//		return localVarReturnValue, nil, ReportError("advertiserId is required and must be specified")
//	}
//	if *r.advertiserId < 1 {
//		return localVarReturnValue, nil, ReportError("advertiserId must be greater than 1")
//	}
//	if r.landingType == nil {
//		return localVarReturnValue, nil, ReportError("landingType is required and must be specified")
//	}
//	if r.adType == nil {
//		return localVarReturnValue, nil, ReportError("adType is required and must be specified")
//	}
//	if r.assetType == nil {
//		return localVarReturnValue, nil, ReportError("assetType is required and must be specified")
//	}
//
//	parameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.advertiserId)
//	parameterAddToHeaderOrQuery(localVarQueryParams, "landing_type", r.landingType)
//	parameterAddToHeaderOrQuery(localVarQueryParams, "ad_type", r.adType)
//	parameterAddToHeaderOrQuery(localVarQueryParams, "asset_type", r.assetType)
//	if r.assetId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "asset_id", r.assetId)
//	}
//	if r.packageName != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "package_name", r.packageName)
//	}
//	if r.appType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "app_type", r.appType)
//	}
//	if r.appPromotionType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "app_promotion_type", r.appPromotionType)
//	}
//	if r.marketingGoal != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "marketing_goal", r.marketingGoal)
//	}
//	if r.quickAppId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "quick_app_id", r.quickAppId)
//	}
//	if r.deliveryMode != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "delivery_mode", r.deliveryMode)
//	}
//	if r.miniProgramId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "mini_program_id", r.miniProgramId)
//	}
//	if r.dpaAdtype != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "dpa_adtype", r.dpaAdtype)
//	}
//	if r.microPromotionType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "micro_promotion_type", r.microPromotionType)
//	}
//	if r.microAppInstanceId != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "micro_app_instance_id", r.microAppInstanceId)
//	}
//	if r.deliveryType != nil {
//		parameterAddToHeaderOrQuery(localVarQueryParams, "delivery_type", r.deliveryType)
//	}
//	// to determine the Content-Type header
//	localVarHTTPContentTypes := []string{}
//
//	// set Content-Type header
//	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
//	if localVarHTTPContentType != "" {
//		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
//	}
//
//	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
//	if err != nil {
//		return localVarReturnValue, nil, err
//	}
//
//	localVarHTTPResponse, err := a.client.call(r.ctx, req, &localVarReturnValue)
//	if err != nil || localVarHTTPResponse == nil {
//		return localVarReturnValue, localVarHTTPResponse, err
//	}
//	return localVarReturnValue, localVarHTTPResponse, nil
//}
