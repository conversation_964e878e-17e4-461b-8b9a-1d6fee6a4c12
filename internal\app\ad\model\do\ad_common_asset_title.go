// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-11 13:50:11
// 生成路径: internal/app/ad/model/entity/ad_common_asset_title.go
// 生成人：cq
// desc:通用资产-标题库
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdCommonAssetTitle is the golang structure for table ad_common_asset_title.
type AdCommonAssetTitle struct {
	gmeta.Meta        `orm:"table:ad_common_asset_title, do:true"`
	Id                interface{} `orm:"id,primary" json:"id"`                           //
	Title             interface{} `orm:"title" json:"title"`                             // 标题
	CategoryIds       interface{} `orm:"category_ids" json:"categoryIds"`                // 标题分类ID列表，以|分隔
	UserId            interface{} `orm:"user_id" json:"userId"`                          // 创建者
	Last3DayClickRate interface{} `orm:"last_3_day_click_rate" json:"last3DayClickRate"` // 近3日点击率
	Last3DayCost      interface{} `orm:"last_3_day_cost" json:"last3DayCost"`            // 近3日消耗
	HistoryClickRate  interface{} `orm:"history_click_rate" json:"historyClickRate"`     // 历史点击率
	HistoryCost       interface{} `orm:"history_cost" json:"historyCost"`                // 历史消耗
	AdCount           interface{} `orm:"ad_count" json:"adCount"`                        // 关联广告数
	CreatedAt         *gtime.Time `orm:"created_at" json:"createdAt"`                    // 创建时间
	UpdatedAt         *gtime.Time `orm:"updated_at" json:"updatedAt"`                    // 更新时间
	DeletedAt         *gtime.Time `orm:"deleted_at" json:"deletedAt"`                    // 删除时间
}
