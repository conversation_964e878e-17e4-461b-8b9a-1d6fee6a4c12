// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: internal/app/ad/controller/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type adXtAccountController struct {
	systemController.BaseController
}

var AdXtAccount = new(adXtAccountController)

// List 列表
func (c *adXtAccountController) List(ctx context.Context, req *ad.AdXtAccountSearchReq) (res *ad.AdXtAccountSearchRes, err error) {
	res = new(ad.AdXtAccountSearchRes)
	res.AdXtAccountSearchRes, err = service.AdXtAccount().List(ctx, &req.AdXtAccountSearchReq)
	return
}

// Export 导出excel
func (c *adXtAccountController) Export(ctx context.Context, req *ad.AdXtAccountExportReq) (res *ad.AdXtAccountExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.AdXtAccountInfoRes
		//表头
		tableHead = []interface{}{"ID", "账户ID 对应 star_id ", "账户名称", "角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图", "授权时间", "授权的app_id", "用于验证权限的token", "刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间", "unauthorized未授权 authorized 已经授权", "创建时间", "更新时间", "删除时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.AdXtAccount().GetExportData(ctx, &req.AdXtAccountSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.Id,
				v.AdvertiserId,
				v.AdvertiserNick,
				v.RoleName,
				v.AuthTime.Format("Y-m-d H:i:s"),
				v.AppId,
				v.AccessToken,
				v.RefreshToken,
				v.Status,
				v.CreatedAt.Format("Y-m-d H:i:s"),
				v.UpdatedAt.Format("Y-m-d H:i:s"),
				v.DeletedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("广告星图账户表格")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取广告星图账户表格
func (c *adXtAccountController) Get(ctx context.Context, req *ad.AdXtAccountGetReq) (res *ad.AdXtAccountGetRes, err error) {
	res = new(ad.AdXtAccountGetRes)
	res.AdXtAccountInfoRes, err = service.AdXtAccount().GetById(ctx, req.Id)
	return
}

// Add 添加广告星图账户表格
func (c *adXtAccountController) Add(ctx context.Context, req *ad.AdXtAccountAddReq) (res *ad.AdXtAccountAddRes, err error) {
	err = service.AdXtAccount().Add(ctx, req.AdXtAccountAddReq)
	return
}

// Edit 修改广告星图账户表格
func (c *adXtAccountController) Edit(ctx context.Context, req *ad.AdXtAccountEditReq) (res *ad.AdXtAccountEditRes, err error) {
	err = service.AdXtAccount().Edit(ctx, req.AdXtAccountEditReq)
	return
}

// Delete 删除广告星图账户表格
func (c *adXtAccountController) Delete(ctx context.Context, req *ad.AdXtAccountDeleteReq) (res *ad.AdXtAccountDeleteRes, err error) {
	err = service.AdXtAccount().Delete(ctx, req.Ids)
	return
}
