// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-08-02 18:25:27
// 生成路径: internal/app/applet/controller/s_applet_rechare_statistics.go
// 生成人：len
// desc:小程序充值统计
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/theater"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sAppletRechargeStatisticsController struct {
	systemController.BaseController
}

var SAppletRechargeStatistics = new(sAppletRechargeStatisticsController)

// List 列表
func (c *sAppletRechargeStatisticsController) List(ctx context.Context, req *applet.SAppletRechargeStatisticsSearchReq) (res *applet.SAppletRechargeStatisticsSearchRes, err error) {
	res = new(applet.SAppletRechargeStatisticsSearchRes)
	res.SAppletRechargeStatisticsSearchRes, err = service.SAppletRechargeStatistics().List(ctx, &req.SAppletRechargeStatisticsSearchReq)
	return
}

// Get 获取小程序充值统计
func (c *sAppletRechargeStatisticsController) Get(ctx context.Context, req *applet.SAppletRechargeStatisticsGetReq) (res *applet.SAppletRechargeStatisticsGetRes, err error) {
	res = new(applet.SAppletRechargeStatisticsGetRes)
	res.SAppletRechargeStatisticsInfoRes, err = service.SAppletRechargeStatistics().GetById(ctx, req.Id)
	return
}

// Add 添加小程序充值统计
func (c *sAppletRechargeStatisticsController) Add(ctx context.Context, req *applet.SAppletRechargeStatisticsAddReq) (res *applet.SAppletRechargeStatisticsAddRes, err error) {
	err = service.SAppletRechargeStatistics().Add(ctx, req.SAppletRechargeStatisticsAddReq)
	return
}

// Edit 修改小程序充值统计
func (c *sAppletRechargeStatisticsController) Edit(ctx context.Context, req *applet.SAppletRechargeStatisticsEditReq) (res *applet.SAppletRechargeStatisticsEditRes, err error) {
	err = service.SAppletRechargeStatistics().Edit(ctx, req.SAppletRechargeStatisticsEditReq)
	return
}

// Delete 删除小程序充值统计
func (c *sAppletRechargeStatisticsController) Delete(ctx context.Context, req *applet.SAppletRechargeStatisticsDeleteReq) (res *applet.SAppletRechargeStatisticsDeleteRes, err error) {
	err = service.SAppletRechargeStatistics().Delete(ctx, req.Ids)
	return
}

// Export 导出excel
func (c *sAppletRechargeStatisticsController) Export(ctx context.Context, req *applet.SAppletRechargeStatisticsExportReq) (res *theater.SVideoStatisticsExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.SAppletRechargeStatisticsListRes
		//表头
		tableHead = []interface{}{"日期", "小程序名称", "注册渠道", "当日新增用户充值", "总充值金额", "抖小总充值", "微小安卓总充值", "微小IOS总充值", "广告收入"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		resp, _ := service.SAppletRechargeStatistics().List(ctx, &req.SAppletRechargeStatisticsSearchReq)
		listData = resp.List
		if err != nil {
			return
		}
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.CreateDate,
				v.AppName,
				v.AppType,
				v.NewUserAmount,
				v.TotalAmount,
				v.DyAndroidRechargeAmount + v.DyIosRechargeAmount,
				v.WechatAndroidRechargeAmount,
				v.WechatIosRechargeAmount,
				v.TotalAdUp,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("小程序充值统计表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}
