// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: internal/app/ad/model/entity/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdXtAccount is the golang structure for table ad_xt_account.
type AdXtAccount struct {
	gmeta.Meta            `orm:"table:ad_xt_account"`
	Id                    int64       `orm:"id,primary" json:"id"`                  // ID
	AdvertiserId          string      `orm:"advertiser_id" json:"advertiserId"`     // 账户ID 对应 star_id
	AdvertiserNick        string      `orm:"advertiser_nick" json:"advertiserNick"` // 账户名称
	RoleName              string      `orm:"role_name" json:"roleName"`             // 角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图
	AuthTime              *gtime.Time `orm:"auth_time" json:"authTime"`             // 授权时间
	AppId                 int         `orm:"app_id" json:"appId"`                   // 授权的app_id
	AccessToken           string      `orm:"access_token" json:"accessToken"`       // 用于验证权限的token
	RefreshToken          string      `orm:"refresh_token" json:"refreshToken"`     // 刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间
	Status                string      `orm:"status" json:"status"`                  // unauthorized未授权 authorized 已经授权
	RefreshTokenExpiresIn *gtime.Time `orm:"refresh_token_expires_in" json:"refreshTokenExpiresIn"  dc:"刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间"`
	//expires_in
	ExpiresIn *gtime.Time `orm:"expires_in" json:"expiresIn" dc:"expires_in"`
	CreatedAt *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
	DeletedAt *gtime.Time `orm:"deleted_at" json:"deletedAt"` // 删除时间
}
