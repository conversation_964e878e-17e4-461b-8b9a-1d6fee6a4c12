// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-08-28 15:16:41
// 生成路径: internal/app/applet/controller/s_applet_deposit_retention_statistics.go
// 生成人：cyao
// desc:小程序维度用户/充值留存数据
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/api/v1/order"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type sAppletDepositRetentionStatisticsController struct {
	systemController.BaseController
}

var SAppletDepositRetentionStatistics = new(sAppletDepositRetentionStatisticsController)

// List 列表
func (c *sAppletDepositRetentionStatisticsController) List(ctx context.Context, req *applet.SAppletDepositRetentionStatisticsSearchReq) (res *applet.SAppletDepositRetentionStatisticsSearchRes, err error) {
	res = new(applet.SAppletDepositRetentionStatisticsSearchRes)
	res.SAppletDepositRetentionStatisticsSearchRes, err = service.SAppletDepositRetentionStatistics().List(ctx, &req.SAppletDepositRetentionStatisticsSearchReq)
	return
}

// RechargeStatExport 导出小程序充值统计excel
func (c *sAppletDepositRetentionStatisticsController) RechargeStatExport(ctx context.Context, req *applet.RechargeStatExportReq) (res *order.OrderInfoExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listRes  *model.SAppletDepositRetentionStatisticsSearchRes
		listData []*model.SAppletDepositRetentionStatisticsListRes
		//表头
		tableHead = []interface{}{"日期", "小程序名称", "平台用户", "新增用户", "用户增长率", "活跃人数", "付费人数", "新增付费人数", "付费金额", "新增付费金额", "当天付费率", "新增付费率", "用户次留", "次日留存", "次日付费", "用户3日留存", "用户3日留存率", "用户3日付费", "用户7日留存", "用户7日留存率", "用户7日付费", "用户30日留存", "用户30日留存率", "用户30日付费"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.SAppletDepositRetentionStatistics().List(ctx, &req.SAppletDepositRetentionStatisticsSearchReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.CreateTime,
				v.AppName,
				v.TotalNumberUsers,
				v.NewUserNums,
				v.UserGrowthRate,
				v.ActiveUserNums,
				v.RechargeNums,
				v.NewUserRechargeNums,
				v.TotalAmount,
				v.NewUserAmount,
				v.SameDayRate,
				v.IncrementalFeeRate,
				v.UserRetention,
				v.RetentionRate,
				v.NextDayPayment,
				v.UserRetention3Day,
				v.RetentionRate3Day,
				v.NextDayPayment3Day,
				v.UserRetention7Day,
				v.RetentionRate7Day,
				v.NextDayPayment7Day,
				v.UserRetention30Day,
				v.RetentionRate30Day,
				v.NextDayPayment30Day,
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("小程序维度用户充值留存数据")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

func (c *sAppletDepositRetentionStatisticsController) Statistics(ctx context.Context, req *applet.StatisticsReq) (res *applet.StatisticsRes, err error) {
	err = service.SAppletDepositRetentionStatistics().Statistics(ctx, req.StatDate, req.StartTime, req.BeforeDay)
	return
}

// Get 获取小程序维度用户/充值留存数据
func (c *sAppletDepositRetentionStatisticsController) Get(ctx context.Context, req *applet.SAppletDepositRetentionStatisticsGetReq) (res *applet.SAppletDepositRetentionStatisticsGetRes, err error) {
	res = new(applet.SAppletDepositRetentionStatisticsGetRes)
	res.SAppletDepositRetentionStatisticsInfoRes, err = service.SAppletDepositRetentionStatistics().GetById(ctx, req.Id)
	return
}

// Add 添加小程序维度用户/充值留存数据
func (c *sAppletDepositRetentionStatisticsController) Add(ctx context.Context, req *applet.SAppletDepositRetentionStatisticsAddReq) (res *applet.SAppletDepositRetentionStatisticsAddRes, err error) {
	err = service.SAppletDepositRetentionStatistics().Add(ctx, req.SAppletDepositRetentionStatisticsAddReq)
	return
}

// Edit 修改小程序维度用户/充值留存数据
func (c *sAppletDepositRetentionStatisticsController) Edit(ctx context.Context, req *applet.SAppletDepositRetentionStatisticsEditReq) (res *applet.SAppletDepositRetentionStatisticsEditRes, err error) {
	err = service.SAppletDepositRetentionStatistics().Edit(ctx, req.SAppletDepositRetentionStatisticsEditReq)
	return
}

// Delete 删除小程序维度用户/充值留存数据
func (c *sAppletDepositRetentionStatisticsController) Delete(ctx context.Context, req *applet.SAppletDepositRetentionStatisticsDeleteReq) (res *applet.SAppletDepositRetentionStatisticsDeleteRes, err error) {
	err = service.SAppletDepositRetentionStatistics().Delete(ctx, req.Ids)
	return
}
