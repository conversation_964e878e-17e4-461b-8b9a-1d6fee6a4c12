// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-12-11 11:34:18
// 生成路径: internal/app/ad/controller/ad_material.go
// 生成人：cyao
// desc:素材主表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adMaterialController struct {
	systemController.BaseController
}

var AdMaterial = new(adMaterialController)

// List 列表
func (c *adMaterialController) List(ctx context.Context, req *ad.AdMaterialSearchReq) (res *ad.AdMaterialSearchRes, err error) {
	res = new(ad.AdMaterialSearchRes)
	res.AdMaterialSearchRes, err = service.AdMaterial().List(ctx, &req.AdMaterialSearchReq)
	return
}

// Get 获取素材主表
func (c *adMaterialController) Get(ctx context.Context, req *ad.AdMaterialGetReq) (res *ad.AdMaterialGetRes, err error) {
	res = new(ad.AdMaterialGetRes)
	res.AdMaterialInfoRes, err = service.AdMaterial().GetByMaterialId(ctx, req.MaterialId)
	return
}

// Add 添加素材主表
func (c *adMaterialController) Add(ctx context.Context, req *ad.AdMaterialAddReq) (res *ad.AdMaterialAddRes, err error) {
	err = service.AdMaterial().Add(ctx, req.AdMaterialAddReq)
	return
}

// Edit 修改素材主表
func (c *adMaterialController) Edit(ctx context.Context, req *ad.AdMaterialEditReq) (res *ad.AdMaterialEditRes, err error) {
	err = service.AdMaterial().Edit(ctx, req.AdMaterialEditReq)
	return
}

// Delete 删除素材主表
func (c *adMaterialController) Delete(ctx context.Context, req *ad.AdMaterialDeleteReq) (res *ad.AdMaterialDeleteRes, err error) {
	err = service.AdMaterial().Delete(ctx, req.MaterialIds)
	return
}
