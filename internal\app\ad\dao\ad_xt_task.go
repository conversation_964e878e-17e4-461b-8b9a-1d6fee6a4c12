// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-20 15:16:51
// 生成路径: internal/app/ad/dao/ad_xt_task.go
// 生成人：cyao
// desc:星图任务列表和任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adXtTaskDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adXtTaskDao struct {
	*internal.AdXtTaskDao
}

var (
	// AdXtTask is globally public accessible object for table tools_gen_table operations.
	AdXtTask = adXtTaskDao{
		internal.NewAdXtTaskDao(),
	}
)

// Fill with you ideas below.
