// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/dao/internal/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdxMaterialDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdxMaterialDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns AdxMaterialColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdxMaterialColumns defines and stores column names for table adx_material.
type AdxMaterialColumns struct {
	MaterialId     string // 素材ID
	AdType         string // 广告类型列表
	CommentNum     string // 评论数
	CreateMonth    string // 创建年月(格式:YYYY-MM)
	DownloadNum    string // 预估转化量
	DurationMillis string // 时长(毫秒)
	ExposureNum    string // 预估曝光量
	ForwardNum     string // 分享数
	LikeNum        string // 点赞数
	MaterialHeight string // 素材高度(像素)
	MaterialType   string // 素材类型1图片2视频
	MaterialWidth  string // 素材宽度(像素)
	PicList        string // 素材封面链接列表
	PlayNum        string // 播放数
	PlayletId      string // 投放短剧ID
	VideoList      string // 素材视频链接列表
}

var adxMaterialColumns = AdxMaterialColumns{
	MaterialId:     "material_id",
	AdType:         "ad_type",
	CommentNum:     "comment_num",
	CreateMonth:    "create_month",
	DownloadNum:    "download_num",
	DurationMillis: "duration_millis",
	ExposureNum:    "exposure_num",
	ForwardNum:     "forward_num",
	LikeNum:        "like_num",
	MaterialHeight: "material_height",
	MaterialType:   "material_type",
	MaterialWidth:  "material_width",
	PicList:        "pic_list",
	PlayNum:        "play_num",
	PlayletId:      "playlet_id",
	VideoList:      "video_list",
}

// NewAdxMaterialDao creates and returns a new DAO object for table data access.
func NewAdxMaterialDao() *AdxMaterialDao {
	return &AdxMaterialDao{
		group:   "default",
		table:   "adx_material",
		columns: adxMaterialColumns,
	}
}

func NewAdxMaterialAnalyticDao() *AdxMaterialDao {
	return &AdxMaterialDao{
		group:   "analyticDB",
		table:   "adx_material",
		columns: adxMaterialColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdxMaterialDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdxMaterialDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdxMaterialDao) Columns() AdxMaterialColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdxMaterialDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdxMaterialDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdxMaterialDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
