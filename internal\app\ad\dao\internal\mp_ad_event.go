// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-07-02 16:29:27
// 生成路径: internal/app/ad/dao/internal/mp_ad_event.go
// 生成人：cyao
// desc:dy小程序广告事件记录
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MpAdEventDao is the manager for logic model data accessing and custom defined data operations functions management.
type MpAdEventDao struct {
	table   string           // Table is the underlying table name of the DAO.
	group   string           // Group is the database configuration group name of current DAO.
	columns MpAdEventColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// MpAdEventColumns defines and stores column names for table mp_ad_event.
type MpAdEventColumns struct {
	Id         string // 记录唯一标识
	MpId       string // 小程序ID
	Cost       string // 广告消耗，单位为十万分之一元
	OpenId     string // 用户OpenID
	EventTime  string // 广告计费发生时间戳，单位秒
	AdType     string // 小程序广告类型
	CreateDate string // 创建日期，按照event_time来
	CreateTime string // 根据event_time来的时间
}

var mpAdEventColumns = MpAdEventColumns{
	Id:         "id",
	MpId:       "mp_id",
	Cost:       "cost",
	OpenId:     "open_id",
	EventTime:  "event_time",
	AdType:     "ad_type",
	CreateDate: "create_date",
	CreateTime: "create_time",
}

// NewMpAdEventDao creates and returns a new DAO object for table data access.
func NewMpAdEventDao() *MpAdEventDao {
	return &MpAdEventDao{
		group:   "default",
		table:   "mp_ad_event",
		columns: mpAdEventColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *MpAdEventDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *MpAdEventDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *MpAdEventDao) Columns() MpAdEventColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *MpAdEventDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *MpAdEventDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *MpAdEventDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
