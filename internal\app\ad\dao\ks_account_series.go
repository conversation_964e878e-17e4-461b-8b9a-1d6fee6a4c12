// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-10 14:40:34
// 生成路径: internal/app/ad/dao/ks_account_series.go
// 生成人：cyao
// desc:短剧信息列表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAccountSeriesDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAccountSeriesDao struct {
	*internal.KsAccountSeriesDao
}

var (
	// KsAccountSeries is globally public accessible object for table tools_gen_table operations.
	KsAccountSeries = ksAccountSeriesDao{
		internal.NewKsAccountSeriesDao(),
	}
)

// Fill with you ideas below.
