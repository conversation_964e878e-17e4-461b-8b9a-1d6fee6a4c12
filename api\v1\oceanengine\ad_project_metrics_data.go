// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-19 10:47:03
// 生成路径: api/v1/oceanengine/ad_project_metrics_data.go
// 生成人：cyao
// desc:广告账户下的项目的指标数据相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdProjectMetricsDataSearchReq 分页请求参数
type AdProjectMetricsDataSearchReq struct {
	g.Meta `path:"/list" tags:"广告账户下的项目的指标数据" method:"get" summary:"广告账户下的项目的指标数据列表"`
	commonApi.Author
	model.AdProjectMetricsDataSearchReq
}

type AdProjectReportDataSearchReq struct {
	g.Meta `path:"/getReport" tags:"广告账户下的项目的指标数据" method:"get" summary:"广告账户的指标数据列表"`
	commonApi.Author
	model.AdProjectReportDataSearch
}

type AdProjectReportDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectReportDataSearchRes
}

// AdProjectReportReportSubTaskReq 订阅账户的报表数据
type AdProjectReportReportSubTaskReq struct {
	g.Meta `path:"/getReport/sub/task" tags:"广告账户下的项目的指标数据" method:"post" summary:"订阅"`
	commonApi.Author
	StartTime string   `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string   `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	PIds      []string `p:"pIds" v:"required#项目id必须"`
}

// AdProjectReportReportSubTaskRes 返回
type AdProjectReportReportSubTaskRes struct {
	g.Meta `mime:"application/json"`
}

// AdProjectReportReportStatTaskReq 刷账户的报表数据
type AdProjectReportReportStatTaskReq struct {
	g.Meta `path:"/getReport/stat/task" tags:"广告账户下的项目的指标数据" method:"post" summary:"AdProjectReportReport"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

// AdProjectReportReportStatTaskRes 返回
type AdProjectReportReportStatTaskRes struct {
	g.Meta `mime:"application/json"`
}

// AdProjectMetricsDataSearchRes 列表返回结果
type AdProjectMetricsDataSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectMetricsDataSearchRes
}

// AdProjectMetricsDataAddReq 添加操作请求参数
type AdProjectMetricsDataAddReq struct {
	g.Meta `path:"/add" tags:"广告账户下的项目的指标数据" method:"post" summary:"广告账户下的项目的指标数据添加"`
	commonApi.Author
	*model.AdProjectMetricsDataAddReq
}

// AdProjectMetricsDataAddRes 添加操作返回结果
type AdProjectMetricsDataAddRes struct {
	commonApi.EmptyRes
}

// AdProjectMetricsDataEditReq 修改操作请求参数
type AdProjectMetricsDataEditReq struct {
	g.Meta `path:"/edit" tags:"广告账户下的项目的指标数据" method:"put" summary:"广告账户下的项目的指标数据修改"`
	commonApi.Author
	*model.AdProjectMetricsDataEditReq
}

// AdProjectMetricsDataEditRes 修改操作返回结果
type AdProjectMetricsDataEditRes struct {
	commonApi.EmptyRes
}

// AdProjectMetricsDataGetReq 获取一条数据请求
type AdProjectMetricsDataGetReq struct {
	g.Meta `path:"/get" tags:"广告账户下的项目的指标数据" method:"get" summary:"获取广告账户下的项目的指标数据信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdProjectMetricsDataGetRes 获取一条数据结果
type AdProjectMetricsDataGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdProjectMetricsDataInfoRes
}

// AdProjectMetricsDataDeleteReq 删除数据请求
type AdProjectMetricsDataDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告账户下的项目的指标数据" method:"delete" summary:"删除广告账户下的项目的指标数据"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdProjectMetricsDataDeleteRes 删除数据返回
type AdProjectMetricsDataDeleteRes struct {
	commonApi.EmptyRes
}
