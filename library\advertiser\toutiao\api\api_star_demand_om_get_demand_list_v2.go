/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// StarDemandOmGetChallengeItemsDataV2ApiService 查询任务列表
type StarDemandOmGetDemandListV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2StarDemandOmGetDemandListGetRequest
}

func (r *StarDemandOmGetDemandListV2ApiService) SetCfg(cfg *conf.Configuration) *StarDemandOmGetDemandListV2ApiService {
	r.cfg = cfg
	return r
}

func (r *StarDemandOmGetDemandListV2ApiService) SetRequest(accountFundGetV3Request ApiOpenApi2StarDemandOmGetDemandListGetRequest) *StarDemandOmGetDemandListV2ApiService {
	r.Request = &accountFundGetV3Request
	return r
}

func (r *StarDemandOmGetDemandListV2ApiService) AccessToken(accessToken string) *StarDemandOmGetDemandListV2ApiService {
	r.token = accessToken
	return r
}

type ApiOpenApi2StarDemandOmGetDemandListGetRequest struct {
	StarId                  int64
	PageNo                  int
	PageSize                int
	UniversalSettlementType models.StarDemandOmGetDemandListV2UniversalSettlementType
	MicroAppId              string
	CreateStartTime         int64
	CreateEndTime           int64
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *StarDemandOmGetDemandListV2ApiService) Do() (data *models.StarDemandOmGetDemandListV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/star/demand/om_get_demand_list/"
	localVarQueryParams := map[string]string{}

	if r.Request.StarId == 0 {
		return nil, errors.New("starId is required and must be specified")
	}
	if r.Request.PageNo == 0 {
		return nil, errors.New("pageNo is required and must be specified")
	}
	if r.Request.PageSize == 0 {
		return nil, errors.New("pageSize is required and must be specified")
	}

	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "star_id", r.Request.StarId)
	if r.Request.UniversalSettlementType != 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "universal_settlement_type", r.Request.UniversalSettlementType)
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_no", r.Request.PageNo)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	if len(r.Request.MicroAppId) != 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "micro_app_id", r.Request.MicroAppId)
	}
	if r.Request.CreateStartTime != 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "create_start_time", r.Request.CreateStartTime)
	}
	if r.Request.CreateEndTime != 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "create_end_time", r.Request.CreateEndTime)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.StarDemandOmGetDemandListV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.StarDemandOmGetDemandListV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/star/demand/om_get_demand_list/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
