// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-23 15:51:08
// 生成路径: api/v1/ad/ad_material_upload.go
// 生成人：cyao
// desc:素材上传之后的表格和广告挂钩相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// AdMaterialUploadSearchReq 分页请求参数
type AdMaterialUploadSearchReq struct {
	g.Meta `path:"/list" tags:"素材上传之后的表格和广告挂钩" method:"get" summary:"素材上传之后的表格和广告挂钩列表"`
	commonApi.Author
	model.AdMaterialUploadSearchReq
}

// AdMaterialUploadSearchRes 列表返回结果
type AdMaterialUploadSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialUploadSearchRes
}

type SyncMediaMaterialToLocalReq struct {
	g.Meta `path:"/syncMediaMaterialToLocal" tags:"素材上传之后的表格和广告挂钩" method:"get" summary:"获取账户素材"`
	commonApi.Author
	*model.SyncMediaMaterialToLocalReq
}

type SyncMediaMaterialToLocalRes struct {
	commonApi.EmptyRes
}

// AdMaterialUploadPullReq 拉取广告图片
type AdMaterialUploadPullReq struct {
	g.Meta `path:"/pull" tags:"素材上传之后的表格和广告挂钩" method:"get" summary:"获取账户素材"`
	commonApi.Author
	AdvertiserId string `p:"advertiserId" v:"required#广告账户id不能为空" dc:"广告账户id"`
	//material_type
	MaterialType string `p:"material_type" v:"required#素材类型不能为空" dc:"素材类型 video/image"`
}

type AdMaterialUploadPullRes struct {
	commonApi.EmptyRes
}

// AdMaterialUploadAddReq 添加操作请求参数
type AdMaterialUploadAddReq struct {
	g.Meta `path:"/add" tags:"素材上传之后的表格和广告挂钩" method:"post" summary:"素材上传之后的表格和广告挂钩添加"`
	commonApi.Author
	*model.AdMaterialUploadAddReq
}

// AdMaterialUploadAddRes 添加操作返回结果
type AdMaterialUploadAddRes struct {
	commonApi.EmptyRes
}

// AdMaterialUploadEditReq 修改操作请求参数
type AdMaterialUploadEditReq struct {
	g.Meta `path:"/edit" tags:"素材上传之后的表格和广告挂钩" method:"put" summary:"素材上传之后的表格和广告挂钩修改"`
	commonApi.Author
	*model.AdMaterialUploadEditReq
}

// AdMaterialUploadEditRes 修改操作返回结果
type AdMaterialUploadEditRes struct {
	commonApi.EmptyRes
}

// AdMaterialUploadGetReq 获取一条数据请求
type AdMaterialUploadGetReq struct {
	g.Meta `path:"/get" tags:"素材上传之后的表格和广告挂钩" method:"get" summary:"获取素材上传之后的表格和广告挂钩信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdMaterialUploadGetRes 获取一条数据结果
type AdMaterialUploadGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialUploadInfoRes
}

type AdGetByMaterialIdReq struct {
	g.Meta `path:"/get/by/materialId" tags:"素材上传之后的表格和广告挂钩" method:"get" summary:"获取素材上传之后的表格和广告挂钩信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdGetByMaterialIdRes 获取一条数据结果
type AdGetByMaterialIdRes struct {
	g.Meta `mime:"application/json"`
	*model.AdMaterialInfoRes
}

// AdMaterialUploadDeleteReq 删除数据请求
type AdMaterialUploadDeleteReq struct {
	g.Meta `path:"/delete" tags:"素材上传之后的表格和广告挂钩" method:"delete" summary:"删除素材上传之后的表格和广告挂钩"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdMaterialUploadDeleteRes 删除数据返回
type AdMaterialUploadDeleteRes struct {
	commonApi.EmptyRes
}
