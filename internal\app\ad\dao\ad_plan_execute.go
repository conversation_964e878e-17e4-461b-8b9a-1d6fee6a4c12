// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: internal/app/ad/dao/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adPlanExecuteDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adPlanExecuteDao struct {
	*internal.AdPlanExecuteDao
}

var (
	// AdPlanExecute is globally public accessible object for table tools_gen_table operations.
	AdPlanExecute = adPlanExecuteDao{
		internal.NewAdPlanExecuteDao(),
	}
)

// Fill with you ideas below.
