package order

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

type OrderStatTaskReq struct {
	g.Meta `path:"/order/stat/task" tags:"Task" method:"post" summary:"orderStatTask"`
	commonApi.Author
	StatDate string
}

type ChannelStatTaskReq struct {
	g.Meta `path:"/channel/stat/task" tags:"Task" method:"post" summary:"ChannelStatTaskReq"`
	commonApi.Author
	StatDate string
}

// ChannelRechargeStatTaskReq 渠道充值统计请求参数
type ChannelRechargeStatTaskReq struct {
	g.Meta `path:"/channel/rechargeStat/task" tags:"Task" method:"post" summary:"channelRechargeStatTask"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

// ChannelRechargeStatTaskRes 渠道充值统计返回结果
type ChannelRechargeStatTaskRes struct {
	g.Meta `mime:"application/json"`
}

type NoRechargeHaveCoinConsumeChannelStatTaskReq struct {
	g.Meta `path:"/recharge/haveCoinConsume/task" tags:"Task" method:"post" summary:"noRechargeHaveCoinConsumeChannelStatTask"`
	commonApi.Author
	StatDate string
}

// RunNoRechargeHaveCoinConsumeChannelStatTaskReq 渠道充值统计请求参数
type RunNoRechargeHaveCoinConsumeChannelStatTaskReq struct {
	g.Meta `path:"/channel/rechargeStat/task2" tags:"Task" method:"post" summary:"noRechargeHaveCoinConsumeChannelStatTask"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

type NoRechargeWxHaveCoinConsumeChannelStatTaskReq struct {
	g.Meta `path:"/recharge/wxHaveCoinConsume/task" tags:"Task" method:"post" summary:"noRechargeWxHaveCoinConsumeChannelStatTaskReq"`
	commonApi.Author
	StatDate string
}

type VideoStatTaskReq struct {
	g.Meta `path:"/video/stat/task" tags:"Task" method:"post" summary:"videoStatTask"`
	commonApi.Author
	StatDate string
}

type ChannelDetailStatTaskReq struct {
	g.Meta `path:"/channel/detailStat/task" tags:"Task" method:"post" summary:"ChannelDetailStat"`
	commonApi.Author
	StatDate string
}
type ChannelHourStatTaskReq struct {
	g.Meta `path:"/channel/hour/stat/task" tags:"Task" method:"post" summary:"ChannelHourStatTaskReq"`
	commonApi.Author
	StatDate string
}

type VideoRechargeStatisticsReq struct {
	g.Meta `path:"/video/recharge/task" tags:"Task" method:"post" summary:"VideoRechargeStatistics"`
	commonApi.Author
	StatDate  string
	StartTime string
	BeforeDay bool
}

// OrderTaskEmptyRes
type OrderTaskEmptyRes struct {
	commonApi.EmptyRes
}
type AppletRechargeStatTaskReq struct {
	g.Meta `path:"/applet/recharge/stat/task" tags:"Task" method:"post" summary:"AppletRechargeStatTaskReq"`
	commonApi.Author
	StatDate  string `p:"statDate" dc:""`
	StartTime string `p:"startTime" dc:""`
	BeforeDay bool   `p:"beforeDay" dc:""`
}
type RefreshChannelRechargeParentIdReq struct {
	g.Meta `path:"/refresh/channel/recharge/parentId" tags:"Task" method:"post" summary:"RefreshChannelRechargeParentIdReq"`
	commonApi.Author
}
type RefreshChannelRechargeAdReq struct {
	g.Meta `path:"/channel/recharge/refresh/ad/task" tags:"Task" method:"post" summary:"AppletRechargeStatTaskReq"`
	commonApi.Author
	StatDate  string `p:"statDate" dc:""`
	StartTime string `p:"startTime" dc:""`
}

type RefreshChannelRechargeOtherTypeReq struct {
	g.Meta `path:"/channel/recharge/refresh/other/task" tags:"Task" method:"post" summary:"刷新其他token"`
	commonApi.Author
	StatDate  string `p:"statDate" dc:""`
	StartTime string `p:"startTime" dc:""`
}
type PitcherVideoRechargeAdReq struct {
	g.Meta `path:"/pitcher/video/recharge/task" tags:"Task" method:"post" summary:"PitcherVideoRechargeAdReq"`
	commonApi.Author
	StatDate  string `p:"statDate" dc:""`
	StartTime string `p:"startTime" dc:""`
	BeforeDay bool   `p:"beforeDay" dc:""`
}

type RefreshTokenTaskReq struct {
	g.Meta `path:"/refresh/token/task" tags:"Task" method:"post" summary:"刷新token"`
	commonApi.Author
}

type AllDataStatReq struct {
	g.Meta    `path:"/all/stat" tags:"定时任务" method:"post" summary:"刷新所有数据"`
	EndTime   string
	StartTime string
}

type AllDataStatRes struct {
	commonApi.EmptyRes
}

type FqRechargeStatTaskReq struct {
	g.Meta `path:"/fqRechargeStatTask" tags:"定时任务" method:"post" summary:"番茄渠道充值统计"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

type FqRechargeStatTaskRes struct {
	commonApi.EmptyRes
}

type DzRechargeStatTaskReq struct {
	g.Meta `path:"/dzRechargeStatTask" tags:"定时任务" method:"post" summary:"点众渠道充值统计"`
	commonApi.Author
	StartTime string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime   string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}
