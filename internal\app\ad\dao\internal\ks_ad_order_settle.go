// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-08 15:57:39
// 生成路径: internal/app/ad/dao/internal/ks_ad_order_settle.go
// 生成人：cq
// desc:快手订单日结算汇总
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdOrderSettleDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdOrderSettleDao struct {
	table   string                 // Table is the underlying table name of the DAO.
	group   string                 // Group is the database configuration group name of current DAO.
	columns KsAdOrderSettleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdOrderSettleColumns defines and stores column names for table ks_ad_order_settle.
type KsAdOrderSettleColumns struct {
	Id                               string // id
	AdvertiserId                     string // 用户快手号id
	SettleDate                       string // 结算时间
	SeriesId                         string // 短剧ID
	SeriesName                       string // 短剧名称
	CopyrightUid                     string // 版权商UID
	CopyrightName                    string // 版权商名称
	SalerUid                         string // 分销商UID
	SalerName                        string // 分销商名称
	SubAccountUid                    string // 子账号UID
	SubAccountName                   string // 子账号名称
	PayProvider                      string // 支付渠道
	PayAmt                           string // 结算订单总金额（元）
	RedundPrice                      string // 退款金额（元）
	CommissionPrice                  string // 佣金（元）
	SettlePrice                      string // 可提现金额（元）
	SubAccountOrderPayAmt            string // 子账号结算（元）
	CurAccountOrderPayAmt            string // 本账号结算（元）
	CopyrightDistributionOrderPayAmt string // 分销分成前结算（元）（版权方视角）
	SalerDistributionOrderPayAmt     string // 分销分成前结算（元）（分销视角）
	Expenditure                      string // 总支出（元）
	PayDate                          string // 支付时间
	OrderType                        string // 订单类型 本账号售卖、子账户售卖、分销售卖
	CreatedAt                        string // 创建时间
	UpdatedAt                        string // 更新时间
}

var ksAdOrderSettleColumns = KsAdOrderSettleColumns{
	Id:                               "id",
	AdvertiserId:                     "advertiser_id",
	SettleDate:                       "settle_date",
	SeriesId:                         "series_id",
	SeriesName:                       "series_name",
	CopyrightUid:                     "copyright_uid",
	CopyrightName:                    "copyright_name",
	SalerUid:                         "saler_uid",
	SalerName:                        "saler_name",
	SubAccountUid:                    "sub_account_uid",
	SubAccountName:                   "sub_account_name",
	PayProvider:                      "pay_provider",
	PayAmt:                           "pay_amt",
	RedundPrice:                      "redund_price",
	CommissionPrice:                  "commission_price",
	SettlePrice:                      "settle_price",
	SubAccountOrderPayAmt:            "sub_account_order_pay_amt",
	CurAccountOrderPayAmt:            "cur_account_order_pay_amt",
	CopyrightDistributionOrderPayAmt: "copyright_distribution_order_pay_amt",
	SalerDistributionOrderPayAmt:     "saler_distribution_order_pay_amt",
	Expenditure:                      "expenditure",
	PayDate:                          "pay_date",
	OrderType:                        "order_type",
	CreatedAt:                        "created_at",
	UpdatedAt:                        "updated_at",
}

// NewKsAdOrderSettleDao creates and returns a new DAO object for table data access.
func NewKsAdOrderSettleDao() *KsAdOrderSettleDao {
	return &KsAdOrderSettleDao{
		group:   "default",
		table:   "ks_ad_order_settle",
		columns: ksAdOrderSettleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdOrderSettleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdOrderSettleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdOrderSettleDao) Columns() KsAdOrderSettleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdOrderSettleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdOrderSettleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdOrderSettleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
