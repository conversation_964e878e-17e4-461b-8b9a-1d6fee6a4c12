/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsWechatGameListV30DataListMinPaymentTierRange
type ToolsWechatGameListV30DataListMinPaymentTierRange string

// List of tools_wechat_game_list_v3.0_data_list_min_payment_tier_range
const (
	SIX_TEN_ToolsWechatGameListV30DataListMinPaymentTierRange      ToolsWechatGameListV30DataListMinPaymentTierRange = "SIX_TEN"
	TEN_TWENTY_ToolsWechatGameListV30DataListMinPaymentTierRange   ToolsWechatGameListV30DataListMinPaymentTierRange = "TEN_TWENTY"
	TWENTY_FIFTY_ToolsWechatGameListV30DataListMinPaymentTierRange ToolsWechatGameListV30DataListMinPaymentTierRange = "TWENTY_FIFTY"
	ZERO_FIVE_ToolsWechatGameListV30DataListMinPaymentTierRange    ToolsWechatGameListV30DataListMinPaymentTierRange = "ZERO_FIVE"
)

// Ptr returns reference to tools_wechat_game_list_v3.0_data_list_min_payment_tier_range value
func (v ToolsWechatGameListV30DataListMinPaymentTierRange) Ptr() *ToolsWechatGameListV30DataListMinPaymentTierRange {
	return &v
}
