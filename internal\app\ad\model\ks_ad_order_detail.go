// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-08 15:56:31
// 生成路径: internal/app/ad/model/ks_ad_order_detail.go
// 生成人：cq
// desc:快手订单结算明细
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdOrderDetailInfoRes is the golang structure for table ks_ad_order_detail.
type KsAdOrderDetailInfoRes struct {
	gmeta.Meta      `orm:"table:ks_ad_order_detail"`
	Id              int64       `orm:"id,primary" json:"id" dc:"id"`                           // id
	AdvertiserId    int64       `orm:"advertiser_id" json:"advertiserId" dc:"用户快手号id"`         // 用户快手号id
	PayDate         string      `orm:"pay_date" json:"payDate" dc:"购买时间"`                      // 购买时间
	SettleDate      string      `orm:"settle_date" json:"settleDate" dc:"结算时间"`                // 结算时间
	OrderId         string      `orm:"order_id" json:"orderId" dc:"订单ID"`                      // 订单ID
	OrderType       string      `orm:"order_type" json:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"` // 订单类型 本账号售卖、子账户售卖、分销售卖
	CopyrightUid    string      `orm:"copyright_uid" json:"copyrightUid" dc:"版权商UID"`          // 版权商UID
	CopyrightName   string      `orm:"copyright_name" json:"copyrightName" dc:"版权商名称"`         // 版权商名称
	SeriesName      string      `orm:"series_name" json:"seriesName" dc:"短剧名称"`                // 短剧名称
	SubAccountUid   string      `orm:"sub_account_uid" json:"subAccountUid" dc:"子账号UID"`       // 子账号UID
	SubAccountName  string      `orm:"sub_account_name" json:"subAccountName" dc:"子账号名称"`      // 子账号名称
	PayProvider     string      `orm:"pay_provider" json:"payProvider" dc:"支付渠道"`              // 支付渠道
	PayAmt          float64     `orm:"pay_amt" json:"payAmt" dc:"结算订单总金额（元）"`                  // 结算订单总金额（元）
	RedundPrice     float64     `orm:"redund_price" json:"redundPrice" dc:"退款金额（元）"`           // 退款金额（元）
	CommissionPrice float64     `orm:"commission_price" json:"commissionPrice" dc:"佣金（元）"`     // 佣金（元）
	SettlePrice     float64     `orm:"settle_price" json:"settlePrice" dc:"可提现金额（元）"`          // 可提现金额（元）
	SettleAmt       float64     `orm:"settle_amt" json:"settleAmt" dc:"分成金额（元）"`               // 分成金额（元）
	SalerRateStr    string      `orm:"saler_rate_str" json:"salerRateStr" dc:"分成比例"`           // 分成比例
	Expenditure     float64     `orm:"expenditure" json:"expenditure" dc:"总支出（元）"`             // 总支出（元）
	Income          float64     `orm:"income" json:"income" dc:"总收入（元）"`                       // 总收入（元）
	SettleRate      string      `orm:"settle_rate" json:"settleRate" dc:"分成比例 仅分销售卖该字段有值"`     // 分成比例 仅分销售卖该字段有值
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                  // 创建时间
	UpdatedAt       *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                  // 更新时间
}

type KsAdOrderDetailListRes struct {
	Id              int64       `json:"id" dc:"id"`
	AdvertiserId    int64       `json:"advertiserId" dc:"用户快手号id"`
	AdvertiserName  string      `json:"advertiserName" dc:"用户快手号名称"`
	PayDate         string      `json:"payDate" dc:"购买时间"`
	SettleDate      string      `json:"settleDate" dc:"结算时间"`
	OrderId         string      `json:"orderId" dc:"订单ID"`
	OrderType       string      `json:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
	CopyrightUid    string      `json:"copyrightUid" dc:"版权商UID"`
	CopyrightName   string      `json:"copyrightName" dc:"版权商名称"`
	SeriesName      string      `json:"seriesName" dc:"短剧名称"`
	SubAccountUid   string      `json:"subAccountUid" dc:"子账号UID"`
	SubAccountName  string      `json:"subAccountName" dc:"子账号名称"`
	PayProvider     string      `json:"payProvider" dc:"支付渠道"`
	PayAmt          float64     `json:"payAmt" dc:"结算订单总金额（元）"`
	RedundPrice     float64     `json:"redundPrice" dc:"退款金额（元）"`
	CommissionPrice float64     `json:"commissionPrice" dc:"佣金（元）"`
	SettlePrice     float64     `json:"settlePrice" dc:"可提现金额（元）"`
	SettleAmt       float64     `json:"settleAmt" dc:"分成金额（元）"`
	SalerRateStr    string      `json:"salerRateStr" dc:"分成比例"`
	Expenditure     float64     `json:"expenditure" dc:"总支出（元）"`
	Income          float64     `json:"income" dc:"总收入（元）"`
	SettleRate      string      `json:"settleRate" dc:"分成比例 仅分销售卖该字段有值"`
	CreatedAt       *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdOrderDetailSearchReq 分页请求参数
type KsAdOrderDetailSearchReq struct {
	comModel.PageReq
	AdvertiserId    string              `p:"advertiserId" v:"advertiserId@integer#用户快手号id需为整数" dc:"用户快手号id"`
	StartPayDate    string              `p:"startPayDate" dc:"开始购买时间"`
	EndPayDate      string              `p:"endPayDate" dc:"结束购买时间"`
	StartSettleDate string              `p:"startSettleDate" dc:"开始结算时间"`
	EndSettleDate   string              `p:"endSettleDate" dc:"结束结算时间"`
	OrderId         string              `p:"orderId" dc:"订单ID"`
	OrderType       string              `p:"orderType" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
	CopyrightUid    string              `p:"copyrightUid" dc:"版权商UID"`
	CopyrightName   string              `p:"copyrightName" dc:"版权商名称"`
	SeriesNameMap   map[string][]string `p:"seriesNameMap" dc:"短剧名称列表"`
	PayProvider     string              `p:"payProvider" dc:"支付渠道"`
	RefundStatus    int                 `p:"refundStatus" dc:"退款类型 1：非退款 2：退款"`
}

// KsAdOrderDetailSearchRes 列表返回结果
type KsAdOrderDetailSearchRes struct {
	comModel.ListRes
	List    []*KsAdOrderDetailListRes `json:"list"`
	Summary *KsAdOrderDetailListRes   `json:"summary"`
}

// KsAdOrderDetailAddReq 添加操作请求参数
type KsAdOrderDetailAddReq struct {
	AdvertiserId    int64   `p:"advertiserId" v:"required#用户快手号id不能为空" dc:"用户快手号id"`
	PayDate         string  `p:"payDate" v:"required#购买时间不能为空" dc:"购买时间"`
	SettleDate      string  `p:"settleDate" v:"required#结算时间不能为空" dc:"结算时间"`
	OrderId         string  `p:"orderId" v:"required#订单ID不能为空" dc:"订单ID"`
	OrderType       string  `p:"orderType" v:"required#订单类型 本账号售卖、子账户售卖、分销售卖不能为空" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
	CopyrightUid    string  `p:"copyrightUid" v:"required#版权商UID不能为空" dc:"版权商UID"`
	CopyrightName   string  `p:"copyrightName" v:"required#版权商名称不能为空" dc:"版权商名称"`
	SeriesName      string  `p:"seriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	SubAccountUid   string  `p:"subAccountUid" v:"required#子账号UID不能为空" dc:"子账号UID"`
	SubAccountName  string  `p:"subAccountName" v:"required#子账号名称不能为空" dc:"子账号名称"`
	PayProvider     string  `p:"payProvider" v:"required#支付渠道不能为空" dc:"支付渠道"`
	PayAmt          float64 `p:"payAmt"  dc:"结算订单总金额（元）"`
	RedundPrice     float64 `p:"redundPrice"  dc:"退款金额（元）"`
	CommissionPrice float64 `p:"commissionPrice"  dc:"佣金（元）"`
	SettlePrice     float64 `p:"settlePrice"  dc:"可提现金额（元）"`
	SettleAmt       float64 `p:"settleAmt"  dc:"分成金额（元）"`
	SalerRateStr    string  `p:"salerRateStr"  dc:"分成比例"`
	Expenditure     float64 `p:"expenditure"  dc:"总支出（元）"`
	Income          float64 `p:"income"  dc:"总收入（元）"`
	SettleRate      string  `p:"settleRate"  dc:"分成比例 仅分销售卖该字段有值"`
}

// KsAdOrderDetailEditReq 修改操作请求参数
type KsAdOrderDetailEditReq struct {
	Id              int64   `p:"id" v:"required#主键ID不能为空" dc:"id"`
	AdvertiserId    int64   `p:"advertiserId" v:"required#用户快手号id不能为空" dc:"用户快手号id"`
	PayDate         string  `p:"payDate" v:"required#购买时间不能为空" dc:"购买时间"`
	SettleDate      string  `p:"settleDate" v:"required#结算时间不能为空" dc:"结算时间"`
	OrderId         string  `p:"orderId" v:"required#订单ID不能为空" dc:"订单ID"`
	OrderType       string  `p:"orderType" v:"required#订单类型 本账号售卖、子账户售卖、分销售卖不能为空" dc:"订单类型 本账号售卖、子账户售卖、分销售卖"`
	CopyrightUid    string  `p:"copyrightUid" v:"required#版权商UID不能为空" dc:"版权商UID"`
	CopyrightName   string  `p:"copyrightName" v:"required#版权商名称不能为空" dc:"版权商名称"`
	SeriesName      string  `p:"seriesName" v:"required#短剧名称不能为空" dc:"短剧名称"`
	SubAccountUid   string  `p:"subAccountUid" v:"required#子账号UID不能为空" dc:"子账号UID"`
	SubAccountName  string  `p:"subAccountName" v:"required#子账号名称不能为空" dc:"子账号名称"`
	PayProvider     string  `p:"payProvider" v:"required#支付渠道不能为空" dc:"支付渠道"`
	PayAmt          float64 `p:"payAmt"  dc:"结算订单总金额（元）"`
	RedundPrice     float64 `p:"redundPrice"  dc:"退款金额（元）"`
	CommissionPrice float64 `p:"commissionPrice"  dc:"佣金（元）"`
	SettlePrice     float64 `p:"settlePrice"  dc:"可提现金额（元）"`
	SettleAmt       float64 `p:"settleAmt"  dc:"分成金额（元）"`
	SalerRateStr    string  `p:"salerRateStr"  dc:"分成比例"`
	Expenditure     float64 `p:"expenditure"  dc:"总支出（元）"`
	Income          float64 `p:"income"  dc:"总收入（元）"`
	SettleRate      string  `p:"settleRate"  dc:"分成比例 仅分销售卖该字段有值"`
}
