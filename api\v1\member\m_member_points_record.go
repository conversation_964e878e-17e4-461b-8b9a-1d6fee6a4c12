// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-17 11:36:36
// 生成路径: api/v1/member/m_member_points_record.go
// 生成人：cq
// desc:用户积分记录表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberPointsRecordSearchReq 分页请求参数
type MMemberPointsRecordSearchReq struct {
	g.Meta `path:"/list" tags:"用户积分记录表" method:"post" summary:"任务管理-用户积分记录列表"`
	commonApi.Author
	model.MMemberPointsRecordSearchReq
}

// MMemberPointsRecordSearchRes 列表返回结果
type MMemberPointsRecordSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberPointsRecordSearchRes
}

// MMemberPointsRecordExportReq 导出请求
type MMemberPointsRecordExportReq struct {
	g.Meta `path:"/export" tags:"用户积分记录表" method:"post" summary:"任务管理-用户积分记录导出"`
	commonApi.Author
	model.MMemberPointsRecordSearchReq
}

// MMemberPointsRecordExportRes 导出响应
type MMemberPointsRecordExportRes struct {
	commonApi.EmptyRes
}

// MMemberPointsRecordAddReq 添加操作请求参数
type MMemberPointsRecordAddReq struct {
	g.Meta `path:"/add" tags:"用户积分记录表" method:"post" summary:"用户积分记录表添加"`
	commonApi.Author
	*model.MMemberPointsRecordAddReq
}

// MMemberPointsRecordAddRes 添加操作返回结果
type MMemberPointsRecordAddRes struct {
	commonApi.EmptyRes
}

// MMemberPointsRecordEditReq 修改操作请求参数
type MMemberPointsRecordEditReq struct {
	g.Meta `path:"/edit" tags:"用户积分记录表" method:"put" summary:"用户积分记录表修改"`
	commonApi.Author
	*model.MMemberPointsRecordEditReq
}

// MMemberPointsRecordEditRes 修改操作返回结果
type MMemberPointsRecordEditRes struct {
	commonApi.EmptyRes
}

// MMemberPointsRecordGetReq 获取一条数据请求
type MMemberPointsRecordGetReq struct {
	g.Meta `path:"/get" tags:"用户积分记录表" method:"get" summary:"获取用户积分记录表信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// MMemberPointsRecordGetRes 获取一条数据结果
type MMemberPointsRecordGetRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberPointsRecordInfoRes
}

// MMemberPointsRecordUserReq 用户积分明细请求参数
type MMemberPointsRecordUserReq struct {
	g.Meta `path:"/user/list" tags:"用户积分记录表" method:"post" summary:"用户管理-用户积分明细列表"`
	commonApi.Author
	model.MMemberPointsRecordUserReq
}

// MMemberPointsRecordUserRes 用户积分明细列表返回结果
type MMemberPointsRecordUserRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberPointsRecordUserRes
}
