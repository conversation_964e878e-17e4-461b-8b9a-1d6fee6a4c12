// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/dao/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// adBatchTaskDetailDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adBatchTaskDetailDao struct {
	*internal.AdBatchTaskDetailDao
}

var (
	// AdBatchTaskDetail is globally public accessible object for table tools_gen_table operations.
	AdBatchTaskDetail = adBatchTaskDetailDao{
		internal.NewAdBatchTaskDetailDao(),
	}
)

// Fill with you ideas below.
