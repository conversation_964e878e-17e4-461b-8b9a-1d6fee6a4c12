// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: internal/app/ad/dao/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdUserRewardClickDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdUserRewardClickDao struct {
	*internal.FqAdUserRewardClickDao
}

var (
	// FqAdUserRewardClick is globally public accessible object for table tools_gen_table operations.
	FqAdUserRewardClick = fqAdUserRewardClickDao{
		internal.NewFqAdUserRewardClickDao(),
	}

	FqAdUserRewardClickAnalytic = fqAdUserRewardClickDao{
		internal.NewFqAdUserRewardClickAnalyticDao(),
	}
)

// Fill with you ideas below.
