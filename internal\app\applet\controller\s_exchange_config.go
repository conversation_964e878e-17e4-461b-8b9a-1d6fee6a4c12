// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-07-16 17:18:05
// 生成路径: internal/app/applet/controller/s_exchange_config.go
// 生成人：cq
// desc:兑换配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/applet"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type sExchangeConfigController struct {
	systemController.BaseController
}

var SExchangeConfig = new(sExchangeConfigController)

// List 列表
func (c *sExchangeConfigController) List(ctx context.Context, req *applet.SExchangeConfigSearchReq) (res *applet.SExchangeConfigSearchRes, err error) {
	res = new(applet.SExchangeConfigSearchRes)
	res.SExchangeConfigSearchRes, err = service.SExchangeConfig().List(ctx, &req.SExchangeConfigSearchReq)
	return
}

// Get 获取兑换配置表
func (c *sExchangeConfigController) Get(ctx context.Context, req *applet.SExchangeConfigGetReq) (res *applet.SExchangeConfigGetRes, err error) {
	res = new(applet.SExchangeConfigGetRes)
	res.SExchangeConfigInfoRes, err = service.SExchangeConfig().GetById(ctx, req.Id)
	return
}

// Add 添加兑换配置表
func (c *sExchangeConfigController) Add(ctx context.Context, req *applet.SExchangeConfigAddReq) (res *applet.SExchangeConfigAddRes, err error) {
	err = service.SExchangeConfig().Add(ctx, req.SExchangeConfigAddReq)
	return
}

// Edit 修改兑换配置表
func (c *sExchangeConfigController) Edit(ctx context.Context, req *applet.SExchangeConfigEditReq) (res *applet.SExchangeConfigEditRes, err error) {
	err = service.SExchangeConfig().Edit(ctx, req.SExchangeConfigEditReq)
	return
}

// Delete 删除兑换配置表
func (c *sExchangeConfigController) Delete(ctx context.Context, req *applet.SExchangeConfigDeleteReq) (res *applet.SExchangeConfigDeleteRes, err error) {
	err = service.SExchangeConfig().Delete(ctx, req.Ids)
	return
}
