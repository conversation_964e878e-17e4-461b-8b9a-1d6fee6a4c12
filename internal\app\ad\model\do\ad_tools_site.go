// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-12-17 14:20:26
// 生成路径: internal/app/ad/model/entity/ad_tools_site.go
// 生成人：cyao
// desc:广告落地页（工具站点）表
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdToolsSite is the golang structure for table ad_tools_site.
type AdToolsSite struct {
	gmeta.Meta   `orm:"table:ad_tools_site, do:true"`
	Id           interface{} `orm:"id,primary" json:"id"`              // ID
	AdvertiserId interface{} `orm:"advertiser_id" json:"advertiserId"` // 广告主ID
	SiteId       interface{} `orm:"site_id" json:"siteId"`             // 站点ID
	SiteName     interface{} `orm:"site_name" json:"siteName"`         // 站点名称
	Status       interface{} `orm:"status" json:"status"`              // 状态
	SiteType     interface{} `orm:"site_type" json:"siteType"`         // 站点类型
	FunctionType interface{} `orm:"function_type" json:"functionType"` // 功能类型
	Thumbnail    interface{} `orm:"thumbnail" json:"thumbnail"`        // 缩略图
	MainUserId   interface{} `orm:"main_user_id" json:"mainUserId"`    // 主用户ID
	CreateTime   *gtime.Time `orm:"create_time" json:"createTime"`     // 创建时间
	UpdateTime   *gtime.Time `orm:"update_time" json:"updateTime"`     // 更新时间
}
