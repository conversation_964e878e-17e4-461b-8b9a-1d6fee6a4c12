// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-07-07 15:25:37
// 生成路径: api/v1/ad/dz_ad_order_info.go
// 生成人：cyao
// desc:广告订单信息表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// DzAdOrderInfoSearchReq 分页请求参数
type DzAdOrderInfoSearchReq struct {
	g.Meta `path:"/list" tags:"广告订单信息表" method:"get" summary:"广告订单信息表列表"`
	commonApi.Author
	model.DzAdOrderInfoSearchReq
}

// DzAdOrderInfoSearchRes 列表返回结果
type DzAdOrderInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdOrderInfoSearchRes
}

// Callback
type DzAdOrderInfoCallbackReq struct {
	g.Meta `path:"/callback" tags:"广告订单信息表" method:"get" summary:"广告订单信息表回调"`
	*model.DzAdOrderInfoCallbackReq
}

type DzAdOrderInfoCallbackRes struct {
	commonApi.EmptyRes
}

// DzAdOrderInfoExportReq 导出请求
type DzAdOrderInfoExportReq struct {
	g.Meta `path:"/export" tags:"广告订单信息表" method:"get" summary:"广告订单信息表导出"`
	commonApi.Author
	model.DzAdOrderInfoSearchReq
}

// DzAdOrderInfoExportRes 导出响应
type DzAdOrderInfoExportRes struct {
	commonApi.EmptyRes
}

// DzAdOrderInfoAddReq 添加操作请求参数
type DzAdOrderInfoAddReq struct {
	g.Meta `path:"/add" tags:"广告订单信息表" method:"post" summary:"广告订单信息表添加"`
	commonApi.Author
	*model.DzAdOrderInfoAddReq
}

// DzAdOrderInfoAddRes 添加操作返回结果
type DzAdOrderInfoAddRes struct {
	commonApi.EmptyRes
}

// DzAdOrderInfoEditReq 修改操作请求参数
type DzAdOrderInfoEditReq struct {
	g.Meta `path:"/edit" tags:"广告订单信息表" method:"put" summary:"广告订单信息表修改"`
	commonApi.Author
	*model.DzAdOrderInfoEditReq
}

// DzAdOrderInfoEditRes 修改操作返回结果
type DzAdOrderInfoEditRes struct {
	commonApi.EmptyRes
}

// DzAdOrderInfoGetReq 获取一条数据请求
type DzAdOrderInfoGetReq struct {
	g.Meta `path:"/get" tags:"广告订单信息表" method:"get" summary:"获取广告订单信息表信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// DzAdOrderInfoGetRes 获取一条数据结果
type DzAdOrderInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.DzAdOrderInfoInfoRes
}

// DzAdOrderInfoDeleteReq 删除数据请求
type DzAdOrderInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告订单信息表" method:"delete" summary:"删除广告订单信息表"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// DzAdOrderInfoDeleteRes 删除数据返回
type DzAdOrderInfoDeleteRes struct {
	commonApi.EmptyRes
}

type DzAdOrderInfoPullOrderDataReq struct {
	g.Meta `path:"/pullOrderData" tags:"广告订单信息表" method:"get" summary:"拉取订单数据"`
	commonApi.Author
	StartTime string `p:"startTime" v:"required#开始时间不能为空"`
	EndTime   string `p:"endTime" v:"required#结束时间不能为空"`
}

type DzAdOrderInfoPullOrderDataRes struct {
	commonApi.EmptyRes
}
