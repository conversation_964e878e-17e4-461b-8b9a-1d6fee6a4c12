/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// ProjectBudgetUpdateV3ApiService ProjectBudgetUpdateV3Api service
type ProjectBudgetUpdateV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.ProjectBudgetUpdateV30Request
}

func (r *ProjectBudgetUpdateV3ApiService) SetCfg(cfg *conf.Configuration) *ProjectBudgetUpdateV3ApiService {
	r.cfg = cfg
	return r
}

func (r *ProjectBudgetUpdateV3ApiService) ProjectBudgetUpdateV30Request(projectBudgetUpdateV30Request models.ProjectBudgetUpdateV30Request) *ProjectBudgetUpdateV3ApiService {
	r.Request = &projectBudgetUpdateV30Request
	return r
}

func (r *ProjectBudgetUpdateV3ApiService) AccessToken(accessToken string) *ProjectBudgetUpdateV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ProjectBudgetUpdateV3ApiService) Do() (data *models.ProjectBudgetUpdateV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/project/budget/update/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.ProjectBudgetUpdateV30Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ProjectBudgetUpdateV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/project/budget/update/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
