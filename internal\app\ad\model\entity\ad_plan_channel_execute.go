// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2024-11-27 11:19:17
// 生成路径: internal/app/ad/model/entity/ad_plan_channel_execute.go
// 生成人：cq
// desc:广告渠道执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdPlanChannelExecute is the golang structure for table ad_plan_channel_execute.
type AdPlanChannelExecute struct {
	gmeta.Meta  `orm:"table:ad_plan_channel_execute"`
	Id          int         `orm:"id,primary" json:"id"`             //
	PlanId      int         `orm:"plan_id" json:"planId"`            // 计划ID
	ChannelList string      `orm:"channel_list" json:"channelList"`  // 渠道列表，用|分隔
	TemplateId  int         `orm:"template_id" json:"templateId"`    // 充值模板ID
	SinglePrice float64     `orm:"single_price" json:"singlePrice"`  // 单集价格
	LockNum     int         `orm:"lock_num" json:"lockNum"`          // 付费集数
	RewardId    int         `orm:"reward_id" json:"rewardId"`        // 激励广告配置ID
	AdSettingId int         `orm:"ad_setting_id" json:"adSettingId"` // 广告回传配置ID
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt"`      // 创建时间
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt"`      // 更新时间
}
