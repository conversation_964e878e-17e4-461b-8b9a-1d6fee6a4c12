// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-04-02 17:53:42
// 生成路径: api/v1/system/sys_user_page_table_config.go
// 生成人：lx
// desc:后台用户页面表格项配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// SysUserPageTableConfigSearchReq 分页请求参数
type SysUserPageTableConfigSearchReq struct {
	g.Meta `path:"/list" tags:"后台用户页面表格项配置" method:"post" summary:"后台用户页面表格项配置列表"`
	commonApi.Author
	model.SysUserPageTableConfigSearchReq
}

// SysUserPageTableConfigSearchRes 列表返回结果
type SysUserPageTableConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.SysUserPageTableConfigSearchRes
}

// SysUserPageTableConfigAddReq 添加操作请求参数
type SysUserPageTableConfigAddReq struct {
	g.Meta `path:"/add" tags:"后台用户页面表格项配置" method:"post" summary:"后台用户页面表格项配置添加"`
	commonApi.Author
	*model.SysUserPageTableConfigAddReq
}

// SysUserPageTableConfigAddRes 添加操作返回结果
type SysUserPageTableConfigAddRes struct {
	commonApi.EmptyRes
}

// SysUserPageTableConfigEditReq 修改操作请求参数
type SysUserPageTableConfigEditReq struct {
	g.Meta `path:"/edit" tags:"后台用户页面表格项配置" method:"post" summary:"后台用户页面表格项配置修改"`
	commonApi.Author
	*model.SysUserPageTableConfigEditReq
}

// SysUserPageTableConfigEditRes 修改操作返回结果
type SysUserPageTableConfigEditRes struct {
	commonApi.EmptyRes
}

// SysUserPageTableConfigGetReq 获取一条数据请求
type SysUserPageTableConfigGetReq struct {
	g.Meta `path:"/post" tags:"后台用户页面表格项配置" method:"get" summary:"获取后台用户页面表格项配置信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// SysUserPageTableConfigGetRes 获取一条数据结果
type SysUserPageTableConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.SysUserPageTableConfigInfoRes
}

// SysUserPageTableConfigDeleteReq 删除数据请求
type SysUserPageTableConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"后台用户页面表格项配置" method:"post" summary:"删除后台用户页面表格项配置"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// SysUserPageTableConfigDeleteRes 删除数据返回
type SysUserPageTableConfigDeleteRes struct {
	commonApi.EmptyRes
}
type SysUserPageTableConfigBatchSettingReq struct {
	g.Meta `path:"/batchSettingTableConfig" tags:"分销统计" method:"post" summary:"设置用户列"`
	commonApi.Author
	EditList []*model.SysUserPageTableConfigAddReq `p:"editList" `
}
