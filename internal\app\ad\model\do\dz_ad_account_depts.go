// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:30
// 生成路径: internal/app/ad/model/entity/dz_ad_account_depts.go
// 生成人：cyao
// desc:权限和部门关联
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdAccountDepts is the golang structure for table dz_ad_account_depts.
type DzAdAccountDepts struct {
	gmeta.Meta `orm:"table:dz_ad_account_depts, do:true"`
	ChannelId  interface{} `orm:"channel_id,primary" json:"channelId"` // 渠道id
	DetpId     interface{} `orm:"detp_id,primary" json:"detpId"`       // 部门ID
}
