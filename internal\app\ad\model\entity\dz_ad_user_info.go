// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:41
// 生成路径: internal/app/ad/model/entity/dz_ad_user_info.go
// 生成人：cyao
// desc:广告注册用户信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdUserInfo is the golang structure for table dz_ad_user_info.
type DzAdUserInfo struct {
	gmeta.Meta   `orm:"table:dz_ad_user_info"`
	UserId       string      `orm:"user_id,primary" json:"userId"`     // 用户ID
	Time         int64       `orm:"time" json:"time"`                  // 请求时间戳，单位毫秒
	ChannelId    string      `orm:"channel_id" json:"channelId"`       // 渠道ID
	AppId        string      `orm:"app_id" json:"appId"`               // 小程序ID
	PromotionId  string      `orm:"promotion_id" json:"promotionId"`   // 推广班组ID
	OpenId       string      `orm:"open_id" json:"openId"`             // openID
	AdId         string      `orm:"ad_id" json:"adId"`                 // 广告ID
	BookId       string      `orm:"book_id" json:"bookId"`             // 书籍ID
	ProjectId    string      `orm:"project_id" json:"projectId"`       // 广告计划ID
	ClickId      string      `orm:"click_id" json:"clickId"`           // 广告点击ID
	UnionId      string      `orm:"union_id" json:"unionId"`           // unionID
	RegisterTime int64       `orm:"register_time" json:"registerTime"` // 用户注册时间戳，单位毫秒
	DyeTime      int64       `orm:"dye_time" json:"dyeTime"`           // 用户染色时间戳，单位毫秒
	CreatedAt    *gtime.Time `orm:"created_at" json:"createdAt"`       // 创建时间
}
