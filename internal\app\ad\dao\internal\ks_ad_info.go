// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: internal/app/ad/dao/internal/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KsAdInfoDao is the manager for logic model data accessing and custom defined data operations functions management.
type KsAdInfoDao struct {
	table   string          // Table is the underlying table name of the DAO.
	group   string          // Group is the database configuration group name of current DAO.
	columns KsAdInfoColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// KsAdInfoColumns defines and stores column names for table ks_ad_info.
type KsAdInfoColumns struct {
	Id              string // ID
	AdvertiserId    string // 用户快手号id/广告主id
	AppId           string // 授权的app_id
	AdAccountName   string // 快手经营者账号名称
	AccountMainName string // 账号主体名称
	Status          string //
	AuthTime        string // 授权时间
	AuthUrl         string // 授权链接地址
	CreatedAt       string // 创建时间
}

var ksAdInfoColumns = KsAdInfoColumns{
	Id:              "id",
	AdvertiserId:    "advertiser_id",
	AppId:           "app_id",
	AdAccountName:   "ad_account_name",
	AccountMainName: "account_main_name",
	Status:          "status",
	AuthTime:        "auth_time",
	AuthUrl:         "auth_url",
	CreatedAt:       "created_at",
}

// NewKsAdInfoDao creates and returns a new DAO object for table data access.
func NewKsAdInfoDao() *KsAdInfoDao {
	return &KsAdInfoDao{
		group:   "default",
		table:   "ks_ad_info",
		columns: ksAdInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *KsAdInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *KsAdInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *KsAdInfoDao) Columns() KsAdInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *KsAdInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *KsAdInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *KsAdInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
