/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// YuntuAudienceInfoCreateV30Request struct for YuntuAudienceInfoCreateV30Request
type YuntuAudienceInfoCreateV30Request struct {
	// 广告主ID，即品牌虚拟adv_id
	AdvertiserId int64 `json:"advertiser_id"`
	// 运算池列表
	CalculatePools []*YuntuAudienceInfoCreateV30RequestCalculatePoolsInner `json:"calculate_pools"`
	// 人群包过期日期 YYYY-MM-DD
	ExpireDate string `json:"expire_date"`
	// 人群包名称
	Name string `json:"name"`
	// 服务商id
	ServiceProviderId int64 `json:"service_provider_id"`
	//
	YuntuBrandId int64 `json:"yuntu_brand_id"`
}
