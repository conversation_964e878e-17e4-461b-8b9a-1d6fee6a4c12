/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package models

// ToolsVideoCheckAvailableAnchorV2ExternalAction
type ToolsVideoCheckAvailableAnchorV2ExternalAction string

// List of tools_video_check_available_anchor_v2_external_action
const (
	AD_CONVERT_TYPE_ACTIVE_ToolsVideoCheckAvailableAnchorV2ExternalAction          ToolsVideoCheckAvailableAnchorV2ExternalAction = "AD_CONVERT_TYPE_ACTIVE"
	AD_CONVERT_TYPE_ACTIVE_REGISTER_ToolsVideoCheckAvailableAnchorV2ExternalAction ToolsVideoCheckAvailableAnchorV2ExternalAction = "AD_CONVERT_TYPE_ACTIVE_REGISTER"
	AD_CONVERT_TYPE_APP_ORDER_ToolsVideoCheckAvailableAnchorV2ExternalAction       ToolsVideoCheckAvailableAnchorV2ExternalAction = "AD_CONVERT_TYPE_APP_ORDER"
	AD_CONVERT_TYPE_APP_PAY_ToolsVideoCheckAvailableAnchorV2ExternalAction         ToolsVideoCheckAvailableAnchorV2ExternalAction = "AD_CONVERT_TYPE_APP_PAY"
	AD_CONVERT_TYPE_APP_UV_ToolsVideoCheckAvailableAnchorV2ExternalAction          ToolsVideoCheckAvailableAnchorV2ExternalAction = "AD_CONVERT_TYPE_APP_UV"
	AD_CONVERT_TYPE_PAY_ToolsVideoCheckAvailableAnchorV2ExternalAction             ToolsVideoCheckAvailableAnchorV2ExternalAction = "AD_CONVERT_TYPE_PAY"
)

// Ptr returns reference to tools_video_check_available_anchor_v2_external_action value
func (v ToolsVideoCheckAvailableAnchorV2ExternalAction) Ptr() *ToolsVideoCheckAvailableAnchorV2ExternalAction {
	return &v
}
