/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

// Oauth2AdvertiserGetResponse struct for Oauth2AdvertiserGetResponse
type Oauth2AdvertiserGetResponse struct {
	//
	Code *int64                           `json:"code,omitempty"`
	Data *Oauth2AdvertiserGetResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// Oauth2AdvertiserGetResponseData
type Oauth2AdvertiserGetResponseData struct {
	//
	List []*Oauth2AdvertiserGetResponseDataListInner `json:"list,omitempty"`
}

// Oauth2AdvertiserGetResponseDataListInner struct for Oauth2AdvertiserGetResponseDataListInner
type Oauth2AdvertiserGetResponseDataListInner struct {
	// 新版账号角色
	AccountRole *string `json:"account_role,omitempty"`
	// 账号id（字符串型） 当advertiser_role=10有效，即抖音号类型时，即为aweme_sec_uid，可用于Dou+接口调用
	AccountStringId *string `json:"account_string_id,omitempty"`
	// 账号id
	AdvertiserId *int64 `json:"advertiser_id,omitempty"`
	// 账号名称
	AdvertiserName *string `json:"advertiser_name,omitempty"`
	// 旧版账号角色，1-普通广告主，2-纵横组织账户，3-一级代理商，4-二级代理商，6-星图账号，10-抖音号（用于Dou+接口调用）
	AdvertiserRole *int64 `json:"advertiser_role,omitempty"`
	//
	CompanyList []*Oauth2AdvertiserGetResponseDataListInnerCompanyListInner `json:"company_list,omitempty"`
	// 授权有效性，允许值：true/false；false表示对应的user在客户中心/一站式平台代理商平台变更了对此账号的权限,需要到对应平台进行调整过来；
	IsValid *bool `json:"is_valid,omitempty"`
}

// Oauth2AdvertiserGetResponseDataListInnerCompanyListInner struct for Oauth2AdvertiserGetResponseDataListInnerCompanyListInner
type Oauth2AdvertiserGetResponseDataListInnerCompanyListInner struct {
	//
	CustomerCompanyId *int64 `json:"customer_company_id,omitempty"`
	//
	CustomerCompanyName *string `json:"customer_company_name,omitempty"`
}
