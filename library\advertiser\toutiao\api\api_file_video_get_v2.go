/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// FileVideoGetV2ApiService 获取视频素材
type FileVideoGetV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2FileVideoGetGetRequest
}
type ApiOpenApi2FileVideoGetGetRequest struct {
	AdvertiserId int64
	Filtering    *models.FileVideoGetV2Filtering
	Page         *int64
	PageSize     *int64
}

func (r *FileVideoGetV2ApiService) SetCfg(cfg *conf.Configuration) *FileVideoGetV2ApiService {
	r.cfg = cfg
	return r
}

func (r *FileVideoGetV2ApiService) SetRequest(req ApiOpenApi2FileVideoGetGetRequest) *FileVideoGetV2ApiService {
	r.Request = &req
	return r
}

func (r *FileVideoGetV2ApiService) AccessToken(accessToken string) *FileVideoGetV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *FileVideoGetV2ApiService) Do() (data *models.FileVideoGetV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/file/video/get/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == 0 {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "filtering", r.Request.Filtering)
	}
	if r.Request.Page != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.FileVideoGetV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.FileVideoGetV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/file/video/get/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
