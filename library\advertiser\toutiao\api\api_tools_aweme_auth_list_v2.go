/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// ToolsAwemeAuthListV2ApiService ToolsAwemeAuthListV2Api service
type ToolsAwemeAuthListV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *ApiOpenApi2ToolsAwemeAuthListGetRequest
}

type ApiOpenApi2ToolsAwemeAuthListGetRequest struct {
	AdvertiserId int64
	Filtering    *models.ToolsAwemeAuthListV2Filtering
	Page         int64
	PageSize     int64
}

func (r *ToolsAwemeAuthListV2ApiService) SetCfg(cfg *conf.Configuration) *ToolsAwemeAuthListV2ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsAwemeAuthListV2ApiService) SetRequest(req ApiOpenApi2ToolsAwemeAuthListGetRequest) *ToolsAwemeAuthListV2ApiService {
	r.Request = &req
	return r
}

func (r *ToolsAwemeAuthListV2ApiService) AccessToken(accessToken string) *ToolsAwemeAuthListV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsAwemeAuthListV2ApiService) Do() (data *models.ToolsAwemeAuthListV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/tools/aweme_auth_list/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId < 1 {
		return nil, errors.New("advertiserId must be greater than 1")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	if r.Request.Page > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page", r.Request.Page)
	}
	if r.Request.PageSize > 0 {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.PageSize)
	}
	if r.Request.Filtering != nil {
		libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "page_size", r.Request.Filtering)
	}

	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.ToolsAwemeAuthListV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsAwemeAuthListV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/tools/aweme_auth_list/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
