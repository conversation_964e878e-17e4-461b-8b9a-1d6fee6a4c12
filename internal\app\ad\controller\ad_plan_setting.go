// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2024-11-27 11:18:05
// 生成路径: internal/app/ad/controller/ad_plan_setting.go
// 生成人：cq
// desc:广告计划设置表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adPlanSettingController struct {
	systemController.BaseController
}

var AdPlanSetting = new(adPlanSettingController)

// List 列表
func (c *adPlanSettingController) List(ctx context.Context, req *ad.AdPlanSettingSearchReq) (res *ad.AdPlanSettingSearchRes, err error) {
	res = new(ad.AdPlanSettingSearchRes)
	res.AdPlanSettingSearchRes, err = service.AdPlanSetting().List(ctx, &req.AdPlanSettingSearchReq)
	return
}

// Get 获取广告计划设置表
func (c *adPlanSettingController) Get(ctx context.Context, req *ad.AdPlanSettingGetReq) (res *ad.AdPlanSettingGetRes, err error) {
	res = new(ad.AdPlanSettingGetRes)
	res.AdPlanSettingListRes, err = service.AdPlanSetting().GetById(ctx, req.Id)
	return
}

func (c *adPlanSettingController) ExecutePlan(ctx context.Context, req *ad.AdExecutePlanReq) (res *ad.AdExecutePlanRes, err error) {
	err = service.AdPlanSetting().ExecutePlanByPlanId(ctx, req.Id)
	return
}

// Add 添加广告计划设置表
func (c *adPlanSettingController) Add(ctx context.Context, req *ad.AdPlanSettingAddReq) (res *ad.AdPlanSettingAddRes, err error) {
	err = service.AdPlanSetting().Add(ctx, req.AdPlanSettingAddReq)
	return
}

// Edit 修改广告计划设置表
func (c *adPlanSettingController) Edit(ctx context.Context, req *ad.AdPlanSettingEditReq) (res *ad.AdPlanSettingEditRes, err error) {
	err = service.AdPlanSetting().Edit(ctx, req.AdPlanSettingEditReq)
	return
}

// Delete 删除广告计划设置表
func (c *adPlanSettingController) Delete(ctx context.Context, req *ad.AdPlanSettingDeleteReq) (res *ad.AdPlanSettingDeleteRes, err error) {
	err = service.AdPlanSetting().Delete(ctx, req.Ids)
	return
}

// UpdateStatus 更新状态
func (c *adPlanSettingController) UpdateStatus(ctx context.Context, req *ad.AdPlanSettingStatusReq) (res *ad.AdPlanSettingStatusRes, err error) {
	err = service.AdPlanSetting().UpdateStatus(ctx, req.Ids, req.Status)
	return
}

// GetManagedObjectList 获取托管对象列表
func (c *adPlanSettingController) GetManagedObjectList(ctx context.Context, req *ad.GetManagedObjectListReq) (res *ad.GetManagedObjectListRes, err error) {
	res = new(ad.GetManagedObjectListRes)
	res.GetManagedObjectListRes, err = service.AdPlanSetting().GetManagedObjectList(ctx, &req.GetManagedObjectListReq)
	return
}

// PushNotifyTest 推送测试通知
func (c *adPlanSettingController) PushNotifyTest(ctx context.Context, req *ad.PushNotifyTestReq) (res *ad.PushNotifyTestRes, err error) {
	err = service.AdPlanSetting().PushNotifyTest(ctx, &req.PushNotifyTestReq)
	return
}

// SendNotify 发送通知
func (c *adPlanSettingController) SendNotify(ctx context.Context, req *ad.SendNotifyReq) (res *ad.SendNotifyRes, err error) {
	err = service.AdPlanSetting().SendNotify(ctx, req.RuleId)
	return
}
