// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: internal/app/ad/model/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdAccountChannelInfoRes is the golang structure for table fq_ad_account_channel.
type FqAdAccountChannelInfoRes struct {
	gmeta.Meta           `orm:"table:fq_ad_account_channel"`
	ChannelDistributorId int64       `orm:"channel_distributor_id,primary" json:"channelDistributorId" dc:"渠道ID"` // 渠道ID
	DistributorId        int64       `orm:"distributor_id" json:"distributorId" dc:"父级渠道ID"`                      // 父级渠道ID
	NickName             string      `orm:"nick_name" json:"nickName" dc:"渠道名称"`                                  // 渠道名称
	AppId                int64       `orm:"app_id" json:"appId" dc:"小程序ID"`                                       // 小程序ID
	AppName              string      `orm:"app_name" json:"appName" dc:"小程序名称"`                                   // 小程序名称
	AppType              int         `orm:"app_type" json:"appType" dc:"业务类型"`                                    // 业务类型
	CreatedAt            *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                // 创建时间
	UpdatedAt            *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                // 更新时间
	DeletedAt            *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                // 删除时间
}

type FqAdAccountChannelListRes struct {
	ChannelDistributorId int64       `json:"channelDistributorId" dc:"渠道ID"`
	DistributorId        int64       `json:"distributorId" dc:"父级渠道ID"`
	NickName             string      `json:"nickName" dc:"渠道名称"`
	AppId                int64       `json:"appId" dc:"小程序ID"`
	AppName              string      `json:"appName" dc:"小程序名称"`
	AppType              int         `json:"appType" dc:"业务类型"`
	UserList             []int64     `json:"userList" dc:"用户列表"`
	DeptList             []int64     `json:"deptList" dc:"部门列表"`
	CreatedAt            *gtime.Time `json:"createdAt" dc:"创建时间"`
}

type FqAdAccountChannelRes struct {
	ChannelDistributorId int64  `json:"channelDistributorId" dc:"渠道ID"`
	DistributorId        int64  `json:"distributorId" dc:"父级渠道ID"`
	SecretKey            string `json:"secretKey" dc:"密钥"`
}

// FqAdAccountChannelSearchReq 分页请求参数
type FqAdAccountChannelSearchReq struct {
	comModel.PageReq
	ChannelDistributorId string `p:"channelDistributorId" dc:"渠道ID"`                                 //渠道ID
	DistributorId        string `p:"distributorId" v:"distributorId@integer#父级渠道ID需为整数" dc:"父级渠道ID"` //父级渠道ID
	NickName             string `p:"nickName" dc:"渠道名称"`                                             //渠道名称
	AppId                string `p:"appId" dc:"小程序ID"`                                               //小程序ID
	AppName              string `p:"appName" dc:"小程序名称"`                                             //小程序名称
	AppType              int    `p:"appType" dc:"业务类型"`
	CreatedAt            string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
}

type FqChannelStaticReq struct {
	comModel.PageReq
	FqDistributorId         string   `p:"fqDistributorId" dc:"番茄账号ID"`           //快应用/公众号distributor_id
	FqDistributorIds        []string `p:"fqDistributorIds" dc:"番茄账号IDs"`         //快应用/公众号distributor_id
	FqChannelDistributorId  string   `p:"fqChannelDistributorId"   dc:"番茄渠道ID"`  //渠道Id
	FqChannelDistributorIds []string `p:"fqChannelDistributorIds"   dc:"番茄渠道ID"` //渠道Id
	StartTime               string   `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime                 string   `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}
type FqChannelStaticRes struct {
	comModel.ListRes
	List    []*FqChannelStaticListRes `json:"list"`
	Summary *FqChannelStaticListRes
}

type FqChannelStaticListRes struct {
	//fq_distributor_id
	//create_time
	CreateTime            string  `json:"createTime" dc:"日期"`
	FqDistributorId       int64   `json:"fqDistributorId" dc:"番茄账号ID"`
	NickName              string  `json:"nickName"   dc:"番茄账号de渠道名称"`
	NewUserAmount         float64 `json:"newUserAmount"   dc:"当日新增用户充值"`
	NewUserRechargeNums   int     `json:"newUserRechargeNums"  dc:"当日新增付费人数"`
	AccountCoinConsume    float64 `json:"accountCoinConsume"   dc:"消耗"`
	TotalAmount           float64 `json:"totalAmount"  dc:"总充值金额"`
	TotalAdUp             float64 `json:"totalAdUp" dc:"广告总充值"`
	NewUserAdUp           float64 `json:"newUserAdUp" dc:"新增广告收入"`
	DayRoi                float64 `json:"dayRoi"   dc:"当日ROI"`
	WechatAndroidRecharge float64 `json:"wechatAndroidRecharge"  dc:"微小安卓充值"`
	WechatIosRecharge     float64 `json:"wechatIosRecharge"  dc:"微小iOS充值"`
	WechatRechargeAmount  float64 `json:"wechatRechargeAmount" dc:"微小总充值"`
	DyAndroidRecharge     float64 `json:"dyAndroidRecharge"  dc:"抖音安卓充值"`
	DyIosRecharge         float64 `json:"dyIosRecharge"  dc:"抖音iOS充值"`
	DyRechargeAmount      float64 `json:"dyRechargeAmount"   dc:"抖小总充值"`
	RechargeNums          int     `json:"rechargeNums"   dc:"充值人数"`
	CustomerPrice         float64 `json:"customerPrice"  dc:"客单价"`
	AvgRechargeTimes      float64 `json:"avgRechargeTimes"   dc:"人均充值次数"`
	RechargeAndroidAmount float64 `json:"rechargeAndroidAmount"  dc:"安卓充值金额"`
	RechargeIosAmount     float64 `json:"rechargeIosAmount"  dc:"iOS充值金额"`
	TotalRechargeTimes    int     `json:"totalRechargeTimes"   dc:"总充值次数"`
}

// FqAdAccountChannelSearchRes 列表返回结果
type FqAdAccountChannelSearchRes struct {
	comModel.ListRes
	List []*FqAdAccountChannelListRes `json:"list"`
}

// FqAdAccountChannelAddReq 添加操作请求参数
type FqAdAccountChannelAddReq struct {
	ChannelDistributorId int64  `p:"channelDistributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
	DistributorId        int64  `p:"distributorId" v:"required#父级渠道ID不能为空" dc:"父级渠道ID"`
	NickName             string `p:"nickName" v:"required#渠道名称不能为空" dc:"渠道名称"`
	AppId                int64  `p:"appId"  dc:"小程序ID"`
	AppName              string `p:"appName" v:"required#小程序名称不能为空" dc:"小程序名称"`
	AppType              int    `p:"appType" dc:"业务类型"`
}

// FqAdAccountChannelEditReq 修改操作请求参数
type FqAdAccountChannelEditReq struct {
	ChannelDistributorId int64  `p:"channelDistributorId" v:"required#主键ID不能为空" dc:"渠道ID"`
	DistributorId        int64  `p:"distributorId" v:"required#父级渠道ID不能为空" dc:"父级渠道ID"`
	NickName             string `p:"nickName" v:"required#渠道名称不能为空" dc:"渠道名称"`
	AppId                int64  `p:"appId"  dc:"小程序ID"`
	AppName              string `p:"appName" v:"required#小程序名称不能为空" dc:"小程序名称"`
	AppType              int    `p:"appType" dc:"业务类型"`
}
