// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-06-05 11:33:24
// 生成路径: internal/app/adx/dao/internal/adx_hot_ranking.go
// 生成人：cq
// desc:ADX热力榜数据表
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdxHotRankingDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdxHotRankingDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns AdxHotRankingColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdxHotRankingColumns defines and stores column names for table adx_hot_ranking.
type AdxHotRankingColumns struct {
	PlayletId           string // 短剧ID
	PlayletName         string // 短剧名称
	Ranking             string // 排名
	ConsumeNum          string // 当日热力值
	TotalConsumeNum     string // 累计热力值
	NewFlag             string // 是否有new标识
	PlayletTags         string // 短剧标签
	ContractorList      string // 承制方列表
	RelatedPartyList    string // 关联方列表
	CopyrightHolderList string
	BusinessDate        string
}

var adxHotRankingColumns = AdxHotRankingColumns{
	PlayletId: "playlet_id",
	//business_date
	BusinessDate:     "business_date",
	PlayletName:      "playlet_name",
	Ranking:          "ranking",
	ConsumeNum:       "consume_num",
	TotalConsumeNum:  "total_consume_num",
	NewFlag:          "new_flag",
	PlayletTags:      "playlet_tags",
	ContractorList:   "contractor_list",
	RelatedPartyList: "related_party_list",
	//CopyrightHolderList
	CopyrightHolderList: "copyright_holder_list",
}

// NewAdxHotRankingDao creates and returns a new DAO object for table data access.
func NewAdxHotRankingDao() *AdxHotRankingDao {
	return &AdxHotRankingDao{
		group:   "default",
		table:   "adx_hot_ranking",
		columns: adxHotRankingColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdxHotRankingDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdxHotRankingDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdxHotRankingDao) Columns() AdxHotRankingColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdxHotRankingDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdxHotRankingDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdxHotRankingDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
