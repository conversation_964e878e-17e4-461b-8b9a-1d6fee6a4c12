// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-03-06 17:53:48
// 生成路径: internal/app/applet/logic/s_advertiser_config.go
// 生成人：cq
// desc:广告配置表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ahmetb/go-linq"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model"
	"github.com/tiger1103/gfast/v3/internal/app/applet/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/applet/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	sysModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
)

func init() {
	service.RegisterSAdvertiserConfig(New())
}

func New() service.ISAdvertiserConfig {
	return &sSAdvertiserConfig{}
}

type sSAdvertiserConfig struct{}

func (s *sSAdvertiserConfig) List(ctx context.Context, req *model.SAdvertiserConfigSearchReq) (listRes *model.SAdvertiserConfigSearchRes, err error) {
	listRes = new(model.SAdvertiserConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.SAdvertiserConfig.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.SAdvertiserConfig.Columns().Id+" = ?", req.Id)
		}
		if req.AppId != "" {
			m = m.Where(dao.SAdvertiserConfig.Columns().AppId+" = ?", req.AppId)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		orderBy := "createtime desc"
		if req.OrderBy != "" {
			orderBy = req.OrderBy
		}
		var res []*model.SAdvertiserConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(orderBy).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")

		appIds := make([]string, 0)
		linq.From(res).SelectT(func(item *model.SAdvertiserConfigListRes) string {
			return item.AppId
		}).ToSlice(&appIds)

		// 获取小程序名称
		miniInfos, err := sysService.SPlatRules().GetMiniInfoByAppIds(ctx, appIds)
		liberr.ErrIsNil(ctx, err, "获取小程序名称失败")

		listRes.List = make([]*model.SAdvertiserConfigListRes, len(res))
		for k, v := range res {
			config := &model.SAdvertiserConfigListRes{
				Id:                      v.Id,
				AppId:                   v.AppId,
				OpenScreenAd:            v.OpenScreenAd,
				OpenInterstitialAd:      v.OpenInterstitialAd,
				DayPopUpNum:             v.DayPopUpNum,
				Intervals:               v.Intervals,
				OpenRewardedAd:          v.OpenRewardedAd,
				DayViewsNum:             v.DayViewsNum,
				DayUnlockEpisodesNum:    v.DayUnlockEpisodesNum,
				CreateTime:              v.CreateTime,
				UpdateTime:              v.UpdateTime,
				ScreenAdPositionId:      v.ScreenAdPositionId,
				RewardedAdPositionId:    v.RewardedAdPositionId,
				AdPositionId:            v.AdPositionId,
				OpenRetainWindow:        v.OpenRetainWindow,
				RetainAdPositionId:      v.RetainAdPositionId,
				RetainTitle:             v.RetainTitle,
				RetainUnlockEpisodesNum: v.RetainUnlockEpisodesNum,
				RetainDayViewsNum:       v.RetainDayViewsNum,
				RetainWindowSeconds:     v.RetainWindowSeconds,
			}
			appInterface := linq.From(miniInfos).WhereT(func(item *sysModel.GetMiniInfoListRes) bool {
				return item.AppId == v.AppId
			}).First()
			if miniInfo, ok := appInterface.(*sysModel.GetMiniInfoListRes); ok {
				config.AppName = miniInfo.AppName
			}
			listRes.List[k] = config
		}
	})
	return
}

func (s *sSAdvertiserConfig) GetById(ctx context.Context, id int64) (res *model.SAdvertiserConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SAdvertiserConfig.Ctx(ctx).WithAll().Where(dao.SAdvertiserConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sSAdvertiserConfig) Add(ctx context.Context, req *model.SAdvertiserConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		existConfig, err := s.GetByAppId(ctx, req.AppId)
		if existConfig != nil {
			err = errors.New("该小程序已存在广告配置！")
			liberr.ErrIsNil(ctx, err)
		}
		config := &do.SAdvertiserConfig{
			AppId:                   req.AppId,
			OpenScreenAd:            req.OpenScreenAd,
			OpenInterstitialAd:      req.OpenInterstitialAd,
			DayPopUpNum:             req.DayPopUpNum,
			Intervals:               req.Intervals,
			OpenRewardedAd:          req.OpenRewardedAd,
			DayViewsNum:             req.DayViewsNum,
			DayUnlockEpisodesNum:    req.DayUnlockEpisodesNum,
			CreateTime:              gtime.Now(),
			UpdateTime:              gtime.Now(),
			ScreenAdPositionId:      req.ScreenAdPositionId,
			RewardedAdPositionId:    req.RewardedAdPositionId,
			AdPositionId:            req.AdPositionId,
			OpenRetainWindow:        req.OpenRetainWindow,
			RetainAdPositionId:      req.RetainAdPositionId,
			RetainTitle:             req.RetainTitle,
			RetainUnlockEpisodesNum: req.RetainUnlockEpisodesNum,
			RetainDayViewsNum:       req.RetainDayViewsNum,
			RetainWindowSeconds:     req.RetainWindowSeconds,
		}
		_, err = dao.SAdvertiserConfig.Ctx(ctx).Insert(config)
		liberr.ErrIsNil(ctx, err, "添加广告配置失败")
		s.SetAdConfigToRedis(ctx, config, req.AppId)
	})
	return
}

func (s *sSAdvertiserConfig) Edit(ctx context.Context, req *model.SAdvertiserConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		existConfig, err := s.GetByAppId(ctx, req.AppId)
		if existConfig != nil && existConfig.Id != req.Id {
			err = errors.New("该小程序已存在广告配置！")
			liberr.ErrIsNil(ctx, err)
		}

		config := &do.SAdvertiserConfig{
			AppId:      req.AppId,
			UpdateTime: gtime.Now(),
		}
		if req.OpenScreenAd != nil {
			config.OpenScreenAd = req.OpenScreenAd
		}
		if req.OpenInterstitialAd != nil {
			config.OpenInterstitialAd = req.OpenInterstitialAd
		}
		if req.DayPopUpNum != nil {
			config.DayPopUpNum = req.DayPopUpNum
		}
		if req.Intervals != nil {
			config.Intervals = req.Intervals
		}
		if req.OpenRewardedAd != nil {
			config.OpenRewardedAd = req.OpenRewardedAd
		}
		if req.DayViewsNum != nil {
			config.DayViewsNum = req.DayViewsNum
		}
		if req.DayUnlockEpisodesNum != nil {
			config.DayUnlockEpisodesNum = req.DayUnlockEpisodesNum
		}
		if req.ScreenAdPositionId != nil {
			config.ScreenAdPositionId = req.ScreenAdPositionId
		}
		if req.RewardedAdPositionId != nil {
			config.RewardedAdPositionId = req.RewardedAdPositionId
		}
		if req.AdPositionId != nil {
			config.AdPositionId = req.AdPositionId
		}
		if req.OpenRetainWindow != nil {
			config.OpenRetainWindow = req.OpenRetainWindow
		}
		if req.RetainAdPositionId != nil {
			config.RetainAdPositionId = req.RetainAdPositionId
		}
		if req.RetainTitle != nil {
			config.RetainTitle = req.RetainTitle
		}
		if req.RetainUnlockEpisodesNum != nil {
			config.RetainUnlockEpisodesNum = req.RetainUnlockEpisodesNum
		}
		if req.RetainDayViewsNum != nil {
			config.RetainDayViewsNum = req.RetainDayViewsNum
		}
		if req.RetainWindowSeconds != nil {
			config.RetainWindowSeconds = req.RetainWindowSeconds
		}
		_, err = dao.SAdvertiserConfig.Ctx(ctx).WherePri(req.Id).Update(config)
		liberr.ErrIsNil(ctx, err, "修改广告配置失败")
		err = dao.SAdvertiserConfig.Ctx(ctx).WherePri(req.Id).Scan(config)
		liberr.ErrIsNil(ctx, err, "查询广告配置失败")
		s.SetAdConfigToRedis(ctx, config, req.AppId)
	})
	return
}

func (s *sSAdvertiserConfig) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res := make([]*model.SAdvertiserConfigInfoRes, 0)
		_ = dao.SAdvertiserConfig.Ctx(ctx).WhereIn(dao.SAdvertiserConfig.Columns().Id, ids).Scan(&res)
		_, err = dao.SAdvertiserConfig.Ctx(ctx).Delete(dao.SAdvertiserConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
		for _, adConfig := range res {
			commonService.RedisCache().Del(ctx, fmt.Sprintf("%s%s", commonConsts.PlatAdConfig, adConfig.AppId))
		}
	})
	return
}

func (s *sSAdvertiserConfig) GetByAppId(ctx context.Context, appId string) (config *model.SAdvertiserConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.SAdvertiserConfig.Ctx(ctx).WherePri(dao.SAdvertiserConfig.Columns().AppId, appId).Scan(&config)
		liberr.ErrIsNil(ctx, err, "获取广告配置失败")
	})
	return
}

func (s *sSAdvertiserConfig) SetAdConfigToRedis(ctx context.Context, config *do.SAdvertiserConfig, appId string) {
	bytes, err := json.Marshal(config)
	liberr.ErrIsNil(ctx, err, "Marshal Ad Config Error")
	// 存入redis缓存，这样处理是为了兼容java
	cacheStr := "\"" + strings.ReplaceAll(string(bytes), `"`, `\"`) + "\""
	_, err = commonService.RedisCache().Set(ctx, fmt.Sprintf("%s%s", commonConsts.PlatAdConfig, appId), cacheStr)
	liberr.ErrIsNil(ctx, err, "Ad Config Set Redis Error")
}
