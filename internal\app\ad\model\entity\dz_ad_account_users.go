// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-07-07 15:25:33
// 生成路径: internal/app/ad/model/entity/dz_ad_account_users.go
// 生成人：cyao
// desc:权限用户关联
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// DzAdAccountUsers is the golang structure for table dz_ad_account_users.
type DzAdAccountUsers struct {
	gmeta.Meta    `orm:"table:dz_ad_account_users"`
	ChannelId     int64 `orm:"channel_id,primary" json:"channelId"`          // 渠道id
	SpecifyUserId int   `orm:"specify_user_id,primary" json:"specifyUserId"` // 用户ID
}
