// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-07-22 16:55:53
// 生成路径: api/v1/member/m_member_sign_statistics.go
// 生成人：cq
// desc:签到数据统计相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package member

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/member/model"
)

// MMemberSignStatisticsSearchReq 分页请求参数
type MMemberSignStatisticsSearchReq struct {
	g.Meta `path:"/list" tags:"签到数据统计" method:"post" summary:"签到数据统计列表"`
	commonApi.Author
	model.MMemberSignStatisticsSearchReq
}

// MMemberSignStatisticsSearchRes 列表返回结果
type MMemberSignStatisticsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.MMemberSignStatisticsSearchRes
}

// MMemberSignStatisticsExportReq 导出请求
type MMemberSignStatisticsExportReq struct {
	g.Meta `path:"/export" tags:"签到数据统计" method:"post" summary:"签到数据统计导出"`
	commonApi.Author
	model.MMemberSignStatisticsSearchReq
}

// MMemberSignStatisticsExportRes 导出响应
type MMemberSignStatisticsExportRes struct {
	commonApi.EmptyRes
}
