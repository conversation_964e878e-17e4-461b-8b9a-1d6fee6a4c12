// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-17 16:11:51
// 生成路径: internal/app/ad/model/fq_ad_user_payment_record.go
// 生成人：gfast
// desc:用户买入行为- 对应番茄用户买入接口
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// FqAdUserPaymentRecordInfoRes is the golang structure for table fq_ad_user_payment_record.
type FqAdUserPaymentRecordInfoRes struct {
	gmeta.Meta       `orm:"table:fq_ad_user_payment_record"`
	TradeNo          string `orm:"trade_no,primary" json:"tradeNo" dc:"常读订单id"`                                    // 常读订单id
	DistributorId    string `orm:"distributor_id" json:"distributorId" dc:"快应用/公众号distributor_id"`                 // 快应用/公众号distributor_id
	AppId            string `orm:"app_id" json:"appId" dc:"公众号/快应用id（分销平台id）"`                                     // 公众号/快应用id（分销平台id）
	AppName          string `orm:"app_name" json:"appName" dc:"快应用/公众号名称"`                                         // 快应用/公众号名称
	PromotionId      string `orm:"promotion_id" json:"promotionId" dc:"付费推广链id（用户染色归属推广链）"`                        // 付费推广链id（用户染色归属推广链）
	OutTradeNo       string `orm:"out_trade_no" json:"outTradeNo" dc:"第三方订单ID"`                                    // 第三方订单ID
	DeviceId         string `orm:"device_id" json:"deviceId" dc:"设备ID"`                                            // 设备ID
	OpenId           string `orm:"open_id" json:"openId" dc:"用户openid（H5书城、微信小程序、抖音小程序）"`                          // 用户openid（H5书城、微信小程序、抖音小程序）
	WxOaOpenId       string `orm:"wx_oa_open_id" json:"wxOaOpenId" dc:"微信公众号 open_id（仅适用网文微小复访的公众号用户）"`            // 微信公众号 open_id（仅适用网文微小复访的公众号用户）
	WxOaName         string `orm:"wx_oa_name" json:"wxOaName" dc:"公众号名称（微小的小程序维度返回）"`                              // 公众号名称（微小的小程序维度返回）
	PayAmount        int64  `orm:"pay_amount" json:"payAmount" dc:"付费金额，单位分"`                                      // 付费金额，单位分
	Ip               string `orm:"ip" json:"ip" dc:"用户最近一次点击推广链时的IP"`                                              // 用户最近一次点击推广链时的IP
	UserAgent        string `orm:"user_agent" json:"userAgent" dc:"用户最近一次点击推广链时的UA"`                               // 用户最近一次点击推广链时的UA
	Oaid             string `orm:"oaid" json:"oaid" dc:"付费时用户OAID（仅支持快应用）"`                                        // 付费时用户OAID（仅支持快应用）
	AndroidId        string `orm:"android_id" json:"androidId" dc:"付费时用户android_id（仅支持快应用）"`                       // 付费时用户android_id（仅支持快应用）
	RegisterTime     string `orm:"register_time" json:"registerTime" dc:"用户染色时间戳"`                                 // 用户染色时间戳
	WxPlatformAppKey string `orm:"wx_platform_app_key" json:"wxPlatformAppKey" dc:"微信开发者id(仅支持微信H5)【v1.2】"`        // 微信开发者id(仅支持微信H5)【v1.2】
	BookId           string `orm:"book_id" json:"bookId" dc:"染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）"` // 染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）
	BookName         string `orm:"book_name" json:"bookName" dc:"染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称"`                  // 染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称
	BookGender       string `orm:"book_gender" json:"bookGender" dc:"染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别"`               // 染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别
	BookCategory     string `orm:"book_category" json:"bookCategory" dc:"染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型"`          // 染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型
	Activity         string `orm:"activity" json:"activity" dc:"是否是充值活动(仅支持微信H5)【v1.2】"`                           // 是否是充值活动(仅支持微信H5)【v1.2】
	RecentReadBookId string `orm:"recent_read_book_id" json:"recentReadBookId" dc:"H5书城用户订单最近阅读书籍（小程序不返回）"`        // H5书城用户订单最近阅读书籍（小程序不返回）
	ExternalId       string `orm:"external_id" json:"externalId" dc:"企微用户企微id（公众号返回）"`                             // 企微用户企微id（公众号返回）
	OrderType        int64  `orm:"order_type" json:"orderType" dc:"订单类型：1 拟支付，2 非虚拟支付"`                            // 订单类型：1 拟支付，2 非虚拟支付
	AdvertiserId     string `orm:"advertiser_id" json:"advertiserId" dc:"腾讯广告主id"`                                 // 腾讯广告主id
	AdgroupId        string `orm:"adgroup_id" json:"adgroupId" dc:"腾讯广告id"`                                        // 腾讯广告id
	AdId             string `orm:"ad_id" json:"adId" dc:"腾讯广告创意id"`                                                // 腾讯广告创意id
	UnionId          string `orm:"union_id" json:"unionId" dc:"用户在微信/抖音开放平台下的唯一id"`                                // 用户在微信/抖音开放平台下的唯一id
	WxVideoId        string `orm:"wx_video_id" json:"wxVideoId" dc:"视频ID（仅视频号场景）"`                                 // 视频ID（仅视频号场景）
	WxVcSourceType   int    `orm:"wx_vc_source_type" json:"wxVcSourceType" dc:"视频号订单类型（仅视频号场景）1.自然流量 2.加热流量"`      // 视频号订单类型（仅视频号场景）1.自然流量 2.加热流量
	WxPromotionId    string `orm:"wx_promotion_id" json:"wxPromotionId" dc:"视频号加热订单ID（仅视频号场景）"`                    // 视频号加热订单ID（仅视频号场景）
	WxSourceType     string `orm:"wx_source_type" json:"wxSourceType" dc:"场景参数，用于区分分销自挂载和CPS达人模式"`                 // 场景参数，用于区分分销自挂载和CPS达人模式
	WxVideoChannelId string `orm:"wx_video_channel_id" json:"wxVideoChannelId" dc:"视频号ID（仅视频号场景）"`                 // 视频号ID（仅视频号场景）
	Status           int    `orm:"status" json:"status" dc:"0-已支付-1-未支付"`                                          // 0-已支付-1-未支付
	PayWay           int    `orm:"pay_way" json:"payWay" dc:"1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成"`                  // 1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成
	PayTimestamp     string `orm:"pay_timestamp" json:"payTimestamp" dc:"付费时间戳"`                                   // 付费时间戳
	CreateTime       string `orm:"create_time" json:"createTime" dc:"订单创建时间"`                                      // 订单创建时间
}

type FqAdUserPaymentRecordListRes struct {
	DistributorId          string  `json:"distributorId" dc:"快应用/公众号distributor_id"`
	CreateTime             string  `json:"createTime" dc:"订单创建时间"`
	CreateDate             string  `json:"createDate" dc:"订单创建日期"`
	TradeNo                string  `json:"tradeNo" dc:"常读订单id"`
	OutTradeNo             string  `json:"outTradeNo" dc:"第三方订单ID"`
	PayWay                 int     `json:"payWay" dc:"1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成"`
	PayAmount              float64 `json:"payAmount" dc:"付费金额，单位分"`
	Status                 int     `json:"status" dc:"0-已支付-1-未支付"`
	PayTimestamp           string  `json:"payTimestamp" dc:"付费时间戳"`
	OrderType              int64   `json:"orderType" dc:"订单类型：1 拟支付，2 非虚拟支付"`
	Activity               string  `json:"activity" dc:"是否是充值活动(仅支持微信H5)【v1.2】"`
	BookId                 string  `json:"bookId" dc:"染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）"`
	BookName               string  `json:"bookName" dc:"染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称"`
	BookGender             string  `json:"bookGender" dc:"染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别"`
	BookCategory           string  `json:"bookCategory" dc:"染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型"`
	UserId                 uint64  `json:"userId" dc:"投手id"`
	DistributorName        string  `json:"distributorName" dc:"投手上级分销"`
	FqAccount              string  `json:"fqAccount" dc:"番茄账号"`
	FqChannel              string  `json:"fqChannel" dc:"渠道"`
	FqChannelDistributorId string  `json:"fqChannelDistributorId" dc:"番茄渠道ID"`
	UserName               string  `json:"userName" dc:"投手名"`
	ChannelCode            string  `orm:"channel_code" json:"channelCode" dc:"渠道号"`
	RegisterTime           string  `json:"registerTime" dc:"用户染色时间戳"`
	DeviceId               string  `json:"deviceId" dc:"设备ID"`
	OpenId                 string  `json:"openId" dc:"用户openid（H5书城、微信小程序、抖音小程序）"`
	Ip                     string  `json:"ip" dc:"用户最近一次点击推广链时的IP"`
	WxOaOpenId             string  `json:"wxOaOpenId" dc:"微信公众号 open_id（仅适用网文微小复访的公众号用户）"`
	WxOaName               string  `json:"wxOaName" dc:"公众号名称（微小的小程序维度返回）"`
	AppId                  string  `json:"appId" dc:"公众号/快应用id（分销平台id）"`
	AppName                string  `json:"appName" dc:"快应用/公众号名称"`
	PromotionId            string  `json:"promotionId" dc:"付费推广链id（用户染色归属推广链）"`
	UserAgent              string  `json:"userAgent" dc:"用户最近一次点击推广链时的UA"`
	Oaid                   string  `json:"oaid" dc:"付费时用户OAID（仅支持快应用）"`
	AndroidId              string  `json:"androidId" dc:"付费时用户android_id（仅支持快应用）"`
	WxPlatformAppKey       string  `json:"wxPlatformAppKey" dc:"微信开发者id(仅支持微信H5)【v1.2】"`
	RecentReadBookId       string  `json:"recentReadBookId" dc:"H5书城用户订单最近阅读书籍（小程序不返回）"`
	ExternalId             string  `json:"externalId" dc:"企微用户企微id（公众号返回）"`
	AdvertiserId           string  `json:"advertiserId" dc:"腾讯广告主id"`
	AdgroupId              string  `json:"adgroupId" dc:"腾讯广告id"`
	AdId                   string  `json:"adId" dc:"腾讯广告创意id"`
	UnionId                string  `json:"unionId" dc:"用户在微信/抖音开放平台下的唯一id"`
	WxVideoId              string  `json:"wxVideoId" dc:"视频ID（仅视频号场景）"`
	WxVcSourceType         int     `json:"wxVcSourceType" dc:"视频号订单类型（仅视频号场景）1.自然流量 2.加热流量"`
	WxPromotionId          string  `json:"wxPromotionId" dc:"视频号加热订单ID（仅视频号场景）"`
	WxSourceType           string  `json:"wxSourceType" dc:"场景参数，用于区分分销自挂载和CPS达人模式"`
	WxVideoChannelId       string  `json:"wxVideoChannelId" dc:"视频号ID（仅视频号场景）"`
}

// FqAdUserPaymentRecordSearchReq 分页请求参数
type FqAdUserPaymentRecordSearchReq struct {
	comModel.PageReq
	StartTime         string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime           string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
	PayStartTime      string `p:"payStartTime" dc:"付费开始时间"`
	PayEndTime        string `p:"payEndTime" dc:"付费结束时间"`
	RegisterStartTime string `p:"registerStartTime" dc:"用户染色时间戳"`
	RegisterEndTime   string `p:"registerEndTime" dc:"用户染色时间戳"`
	BookId            string `p:"bookId" dc:"染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）"` //染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）
	//部门id
	DeptIds                 []int    `p:"deptIds"`
	DistributorId           int      `p:"distributorId" dc:"分销id"`
	FqDistributorId         string   `p:"fqDistributorId" dc:"快应用/公众号distributor_id"`  //快应用/公众号distributor_id
	FqDistributorIds        []string `p:"fqDistributorIds" dc:"快应用/公众号distributor_id"` //快应用/公众号distributor_id
	FqChannelDistributorId  string   `p:"fqChannelDistributorId"   dc:"番茄渠道ID"`        //渠道Id
	FqChannelDistributorIds []string `p:"fqChannelDistributorIds"   dc:"番茄渠道ID"`       //渠道Id
	PitcherId               int      `p:"pitcherId" dc:"投手id"`
	ChannelCode             string   `orm:"channel_code" p:"channelCode" dc:"渠道号"`
	ChannelCodes            []string `orm:"channel_codes" p:"channelCodes" dc:"渠道号"`
	DeviceId                string   `p:"deviceId" dc:"设备ID"`                                                                                       //设备ID
	TradeNo                 string   `p:"tradeNo" dc:"常读订单id"`                                                                                      //常读订单id
	OutTradeNo              string   `p:"outTradeNo" dc:"第三方订单ID"`                                                                                  //第三方订单ID
	PayWay                  string   `p:"payWay" v:"payWay@integer#1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成需为整数" dc:"1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成"` //1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成
	Status                  string   `p:"status" v:"status@integer#0-已支付-1-未支付需为整数" dc:"0-已支付-1-未支付"`                                               //0-已支付-1-未支付

	OrderType string `p:"orderType"  dc:"订单类型：1 拟支付，2 非虚拟支付"`   //订单类型：1 拟支付，2 非虚拟支付
	Activity  string `p:"activity" dc:"是否是充值活动(仅支持微信H5)【v1.2】"` //是否是充值活动(仅支持微信H5)【v1.2】

	WxVideoId        string `p:"wxVideoId" dc:"视频ID（仅视频号场景）"`                                                                                  //视频ID（仅视频号场景）
	WxVcSourceType   string `p:"wxVcSourceType" v:"wxVcSourceType@integer#视频号订单类型（仅视频号场景）1.自然流量 2.加热流量需为整数" dc:"视频号订单类型（仅视频号场景）1.自然流量 2.加热流量"` //视频号订单类型（仅视频号场景）1.自然流量 2.加热流量
	WxPromotionId    string `p:"wxPromotionId" dc:"视频号加热订单ID（仅视频号场景）"`                                                                         //视频号加热订单ID（仅视频号场景）
	WxSourceType     string `p:"wxSourceType" dc:"场景参数，用于区分分销自挂载和CPS达人模式"`                                                                     //场景参数，用于区分分销自挂载和CPS达人模式
	WxVideoChannelId string `p:"wxVideoChannelId" dc:"视频号ID（仅视频号场景）"`
	AppId            string `p:"appId" dc:"公众号/快应用id（分销平台id）"`                               //公众号/快应用id（分销平台id）
	AppName          string `p:"appName" dc:"快应用/公众号名称"`                                     //快应用/公众号名称
	PromotionId      string `p:"promotionId" dc:"付费推广链id（用户染色归属推广链）"`                        //付费推广链id（用户染色归属推广链）
	OpenId           string `p:"openId" dc:"用户openid（H5书城、微信小程序、抖音小程序）"`                     //用户openid（H5书城、微信小程序、抖音小程序）
	WxOaOpenId       string `p:"wxOaOpenId" dc:"微信公众号 open_id（仅适用网文微小复访的公众号用户）"`             //微信公众号 open_id（仅适用网文微小复访的公众号用户）
	WxOaName         string `p:"wxOaName" dc:"公众号名称（微小的小程序维度返回）"`                            //公众号名称（微小的小程序维度返回）
	PayAmount        string `p:"payAmount" v:"payAmount@integer#付费金额，单位分需为整数" dc:"付费金额，单位分"` //付费金额，单位分
	Ip               string `p:"ip" dc:"用户最近一次点击推广链时的IP"`                                    //用户最近一次点击推广链时的IP
	UserAgent        string `p:"userAgent" dc:"用户最近一次点击推广链时的UA"`                             //用户最近一次点击推广链时的UA
	Oaid             string `p:"oaid" dc:"付费时用户OAID（仅支持快应用）"`                                //付费时用户OAID（仅支持快应用）
	AndroidId        string `p:"androidId" dc:"付费时用户android_id（仅支持快应用）"`                     //付费时用户android_id（仅支持快应用）
	//RegisterTime     string `p:"registerTime" dc:"用户染色时间戳"`                                                                                    //用户染色时间戳
	WxPlatformAppKey string `p:"wxPlatformAppKey" dc:"微信开发者id(仅支持微信H5)【v1.2】"`     //微信开发者id(仅支持微信H5)【v1.2】
	BookName         string `p:"bookName" dc:"染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称"`     //染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称
	BookGender       string `p:"bookGender" dc:"染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别"`    //染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别
	BookCategory     string `p:"bookCategory" dc:"染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型"` //染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型
	RecentReadBookId string `p:"recentReadBookId" dc:"H5书城用户订单最近阅读书籍（小程序不返回）"`     //H5书城用户订单最近阅读书籍（小程序不返回）
	ExternalId       string `p:"externalId" dc:"企微用户企微id（公众号返回）"`                  //企微用户企微id（公众号返回）
	AdvertiserId     string `p:"advertiserId" dc:"腾讯广告主id"`                        //腾讯广告主id
	AdgroupId        string `p:"adgroupId" dc:"腾讯广告id"`                            //腾讯广告id
	AdId             string `p:"adId" dc:"腾讯广告创意id"`                               //腾讯广告创意id
	UnionId          string `p:"unionId" dc:"用户在微信/抖音开放平台下的唯一id"`                  //用户在微信/抖音开放平台下的唯一id

	//PayTimestamp     string `p:"payTimestamp" dc:"付费时间戳"`                                                                                      //付费时间戳
	//CreateTime       string `p:"createTime" dc:"订单创建时间"`                                                                                       //订单创建时间

}

// FqAdUserPaymentRecordSearchRes 列表返回结果
type FqAdUserPaymentRecordSearchRes struct {
	comModel.ListRes
	List    []*FqAdUserPaymentRecordListRes `json:"list"`
	Summary FqAdUserPaymentRecordSummary    `json:"summary"`
}

type FqAdUserPaymentRecordSummary struct {
	PayAmount      float64 `p:"payAmount"  dc:"付费金额，单位分"`
	PayAmountCount int64   `p:"payAmountCount"  dc:"总笔数"`
}

// FqAdUserPaymentRecordAddReq 添加操作请求参数
type FqAdUserPaymentRecordAddReq struct {
	TradeNo          string `p:"tradeNo" v:"required#主键ID不能为空" dc:"常读订单id"`
	DistributorId    string `p:"distributorId"  dc:"快应用/公众号distributor_id"`
	AppId            string `p:"appId"  dc:"公众号/快应用id（分销平台id）"`
	AppName          string `p:"appName" v:"required#快应用/公众号名称不能为空" dc:"快应用/公众号名称"`
	PromotionId      string `p:"promotionId"  dc:"付费推广链id（用户染色归属推广链）"`
	OutTradeNo       string `p:"outTradeNo"  dc:"第三方订单ID"`
	DeviceId         string `p:"deviceId"  dc:"设备ID"`
	OpenId           string `p:"openId"  dc:"用户openid（H5书城、微信小程序、抖音小程序）"`
	WxOaOpenId       string `p:"wxOaOpenId"  dc:"微信公众号 open_id（仅适用网文微小复访的公众号用户）"`
	WxOaName         string `p:"wxOaName" v:"required#公众号名称（微小的小程序维度返回）不能为空" dc:"公众号名称（微小的小程序维度返回）"`
	PayAmount        int64  `p:"payAmount"  dc:"付费金额，单位分"`
	Ip               string `p:"ip"  dc:"用户最近一次点击推广链时的IP"`
	UserAgent        string `p:"userAgent"  dc:"用户最近一次点击推广链时的UA"`
	Oaid             string `p:"oaid"  dc:"付费时用户OAID（仅支持快应用）"`
	AndroidId        string `p:"androidId"  dc:"付费时用户android_id（仅支持快应用）"`
	RegisterTime     string `p:"registerTime"  dc:"用户染色时间戳"`
	WxPlatformAppKey string `p:"wxPlatformAppKey"  dc:"微信开发者id(仅支持微信H5)【v1.2】"`
	BookId           string `p:"bookId"  dc:"染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）"`
	BookName         string `p:"bookName" v:"required#染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称不能为空" dc:"染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称"`
	BookGender       string `p:"bookGender"  dc:"染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别"`
	BookCategory     string `p:"bookCategory"  dc:"染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型"`
	Activity         string `p:"activity"  dc:"是否是充值活动(仅支持微信H5)【v1.2】"`
	RecentReadBookId string `p:"recentReadBookId"  dc:"H5书城用户订单最近阅读书籍（小程序不返回）"`
	ExternalId       string `p:"externalId"  dc:"企微用户企微id（公众号返回）"`
	OrderType        int64  `p:"orderType"  dc:"订单类型：1 拟支付，2 非虚拟支付"`
	AdvertiserId     string `p:"advertiserId"  dc:"腾讯广告主id"`
	AdgroupId        string `p:"adgroupId"  dc:"腾讯广告id"`
	AdId             string `p:"adId"  dc:"腾讯广告创意id"`
	UnionId          string `p:"unionId"  dc:"用户在微信/抖音开放平台下的唯一id"`
	WxVideoId        string `p:"wxVideoId"  dc:"视频ID（仅视频号场景）"`
	WxVcSourceType   int    `p:"wxVcSourceType"  dc:"视频号订单类型（仅视频号场景）1.自然流量 2.加热流量"`
	WxPromotionId    string `p:"wxPromotionId"  dc:"视频号加热订单ID（仅视频号场景）"`
	WxSourceType     string `p:"wxSourceType"  dc:"场景参数，用于区分分销自挂载和CPS达人模式"`
	WxVideoChannelId string `p:"wxVideoChannelId"  dc:"视频号ID（仅视频号场景）"`
	Status           int    `p:"status" v:"required#0-已支付-1-未支付不能为空" dc:"0-已支付-1-未支付"`
	PayWay           int    `p:"payWay"  dc:"1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成"`
	PayTimestamp     string `p:"payTimestamp"  dc:"付费时间戳"`
	CreateTime       string `p:"createTime"  dc:"订单创建时间"`
}

type FqAdUserPaymentRecordSaveReq struct {
	DistributorId    string       `p:"distributor_id" json:"distributorId" dc:"快应用/公众号distributor_id"`                 // 快应用/公众号distributor_id
	AppId            string       `p:"app_id" json:"appId" dc:"公众号/快应用id（分销平台id）"`                                     // 公众号/快应用id（分销平台id）
	AppName          string       `p:"app_name" json:"appName" dc:"快应用/公众号名称"`                                         // 快应用/公众号名称
	TradeNo          string       `p:"trade_no_raw" json:"tradeNo" dc:"常读订单id"`                                        // 常读订单id
	OutTradeNo       string       `p:"out_trade_no" json:"outTradeNo" dc:"第三方订单ID"`                                    // 第三方订单ID
	DeviceId         string       `p:"device_id" json:"deviceId" dc:"设备ID"`                                            // 设备ID
	OpenId           string       `p:"open_id" json:"openId" dc:"用户openid（H5书城、微信小程序、抖音小程序）"`                          // 用户openid（H5书城、微信小程序、抖音小程序）
	WxOaOpenId       string       `p:"wx_oa_open_id" json:"wxOaOpenId" dc:"微信公众号 open_id（仅适用网文微小复访的公众号用户）"`            // 微信公众号 open_id（仅适用网文微小复访的公众号用户）
	WxOaName         string       `p:"wx_oa_name" json:"wxOaName" dc:"公众号名称（微小的小程序维度返回）"`                              // 公众号名称（微小的小程序维度返回）
	PayAmount        int64        `p:"pay_amount" json:"payAmount" dc:"付费金额，单位分"`                                      // 付费金额，单位分
	PromotionId      string       `p:"promotion_id" json:"promotionId" dc:"付费推广链id（用户染色归属推广链）"`                        // 付费推广链id（用户染色归属推广链）
	PayTimestamp     string       `p:"pay_timestamp" json:"payTimestamp" dc:"付费时间戳"`                                   // 付费时间戳
	Ip               string       `p:"ip" json:"ip" dc:"用户最近一次点击推广链时的IP"`                                              // 用户最近一次点击推广链时的IP
	UserAgent        string       `p:"user_agent" json:"userAgent" dc:"用户最近一次点击推广链时的UA"`                               // 用户最近一次点击推广链时的UA
	Oaid             string       `p:"oaid" json:"oaid" dc:"付费时用户OAID（仅支持快应用）"`                                        // 付费时用户OAID（仅支持快应用）
	AndroidId        string       `p:"android_id" json:"androidId" dc:"付费时用户android_id（仅支持快应用）"`                       // 付费时用户android_id（仅支持快应用）
	RegisterTime     string       `p:"register_time" json:"registerTime" dc:"用户染色时间戳"`                                 // 用户染色时间戳
	WxPlatformAppKey string       `p:"wx_platform_app_key" json:"wxPlatformAppKey" dc:"微信开发者id(仅支持微信H5)【v1.2】"`        // 微信开发者id(仅支持微信H5)【v1.2】
	BookId           string       `p:"book_id" json:"bookId" dc:"染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）"` // 染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）
	BookName         string       `p:"book_name" json:"bookName" dc:"染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称"`                  // 染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称
	BookGender       string       `p:"book_gender" json:"bookGender" dc:"染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别"`               // 染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别
	BookCategory     string       `p:"book_category" json:"bookCategory" dc:"染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型"`          // 染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型
	Activity         string       `p:"activity" json:"activity" dc:"是否是充值活动(仅支持微信H5)【v1.2】"`                           // 是否是充值活动(仅支持微信H5)【v1.2】
	RecentReadBookId string       `p:"recent_read_book_id" json:"recentReadBookId" dc:"H5书城用户订单最近阅读书籍（小程序不返回）"`        // H5书城用户订单最近阅读书籍（小程序不返回）
	ExternalId       string       `p:"external_id" json:"externalId" dc:"企微用户企微id（公众号返回）"`                             // 企微用户企微id（公众号返回）
	OrderType        int64        `p:"order_type" json:"orderType" dc:"订单类型：1 拟支付，2 非虚拟支付"`                            // 订单类型：1 拟支付，2 非虚拟支付
	AdvertiserId     string       `p:"advertiser_id" json:"advertiserId" dc:"腾讯广告主id"`                                 // 腾讯广告主id
	AdgroupId        string       `p:"adgroup_id" json:"adgroupId" dc:"腾讯广告id"`                                        // 腾讯广告id
	AdId             string       `p:"ad_id" json:"adId" dc:"腾讯广告创意id"`                                                // 腾讯广告创意id
	UnionId          string       `p:"union_id" json:"unionId" dc:"用户在微信/抖音开放平台下的唯一id"`                                // 用户在微信/抖音开放平台下的唯一id
	WxVideoInfo      *WxVideoInfo `p:"wx_video_info" json:"wxVideoInfo" dc:"视频号信息"`
	Status           int          `p:"status" json:"status" dc:"0-已支付-1-未支付"`                         // 0-已支付-1-未支付
	PayWay           int          `p:"pay_way" json:"payWay" dc:"1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成"` // 1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成
	CreateTime       string       `p:"create_time" json:"createTime" dc:"订单创建时间"`                     // 订单创建时间
}

type WxVideoInfo struct {
	WxVideoId        string `p:"wx_video_id"  dc:"视频ID（仅视频号场景）"`
	WxVideoChannelId string `p:"wx_video_channel_id"  dc:"视频号ID（仅视频号场景）"` // 视频号ID（仅视频号场景）
	WxPromotionId    string `p:"wx_promotion_id"  dc:"视频号加热订单ID（仅视频号场景）"`
	WxSourceType     string `p:"wx_source_type" dc:"场景参数，用于区分分销自挂载和CPS达人模式"`
}

// FqAdUserPaymentRecordEditReq 修改操作请求参数
type FqAdUserPaymentRecordEditReq struct {
	TradeNo          string `p:"tradeNo" v:"required#主键ID不能为空" dc:"常读订单id"`
	DistributorId    string `p:"distributorId"  dc:"快应用/公众号distributor_id"`
	AppId            string `p:"appId"  dc:"公众号/快应用id（分销平台id）"`
	AppName          string `p:"appName" v:"required#快应用/公众号名称不能为空" dc:"快应用/公众号名称"`
	PromotionId      string `p:"promotionId"  dc:"付费推广链id（用户染色归属推广链）"`
	OutTradeNo       string `p:"outTradeNo"  dc:"第三方订单ID"`
	DeviceId         string `p:"deviceId"  dc:"设备ID"`
	OpenId           string `p:"openId"  dc:"用户openid（H5书城、微信小程序、抖音小程序）"`
	WxOaOpenId       string `p:"wxOaOpenId"  dc:"微信公众号 open_id（仅适用网文微小复访的公众号用户）"`
	WxOaName         string `p:"wxOaName" v:"required#公众号名称（微小的小程序维度返回）不能为空" dc:"公众号名称（微小的小程序维度返回）"`
	PayAmount        int64  `p:"payAmount"  dc:"付费金额，单位分"`
	Ip               string `p:"ip"  dc:"用户最近一次点击推广链时的IP"`
	UserAgent        string `p:"userAgent"  dc:"用户最近一次点击推广链时的UA"`
	Oaid             string `p:"oaid"  dc:"付费时用户OAID（仅支持快应用）"`
	AndroidId        string `p:"androidId"  dc:"付费时用户android_id（仅支持快应用）"`
	RegisterTime     string `p:"registerTime"  dc:"用户染色时间戳"`
	WxPlatformAppKey string `p:"wxPlatformAppKey"  dc:"微信开发者id(仅支持微信H5)【v1.2】"`
	BookId           string `p:"bookId"  dc:"染色推广链的书籍ID【v1.2】，H5书城：最近阅读书籍ID（recent_read_book_id）"`
	BookName         string `p:"bookName" v:"required#染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称不能为空" dc:"染色推广链的书籍名称【v1.2】，H5书城：最近阅读书籍名称"`
	BookGender       string `p:"bookGender"  dc:"染色推广链书籍性别【v1.2】，H5书城：最近阅读书籍性别"`
	BookCategory     string `p:"bookCategory"  dc:"染色推广链的书籍类型【v1.2】，H5书城：最近阅读书籍类型"`
	Activity         string `p:"activity"  dc:"是否是充值活动(仅支持微信H5)【v1.2】"`
	RecentReadBookId string `p:"recentReadBookId"  dc:"H5书城用户订单最近阅读书籍（小程序不返回）"`
	ExternalId       string `p:"externalId"  dc:"企微用户企微id（公众号返回）"`
	OrderType        int64  `p:"orderType"  dc:"订单类型：1 拟支付，2 非虚拟支付"`
	AdvertiserId     string `p:"advertiserId"  dc:"腾讯广告主id"`
	AdgroupId        string `p:"adgroupId"  dc:"腾讯广告id"`
	AdId             string `p:"adId"  dc:"腾讯广告创意id"`
	UnionId          string `p:"unionId"  dc:"用户在微信/抖音开放平台下的唯一id"`
	WxVideoId        string `p:"wxVideoId"  dc:"视频ID（仅视频号场景）"`
	WxVcSourceType   int    `p:"wxVcSourceType"  dc:"视频号订单类型（仅视频号场景）1.自然流量 2.加热流量"`
	WxPromotionId    string `p:"wxPromotionId"  dc:"视频号加热订单ID（仅视频号场景）"`
	WxSourceType     string `p:"wxSourceType"  dc:"场景参数，用于区分分销自挂载和CPS达人模式"`
	WxVideoChannelId string `p:"wxVideoChannelId"  dc:"视频号ID（仅视频号场景）"`
	Status           int    `p:"status" v:"required#0-已支付-1-未支付不能为空" dc:"0-已支付-1-未支付"`
	PayWay           int    `p:"payWay"  dc:"1-微信-2-支付-5-抖音支付-6-抖音钻石付-200-未支付完成"`
	PayTimestamp     string `p:"payTimestamp"  dc:"付费时间戳"`
	CreateTime       string `p:"createTime"  dc:"订单创建时间"`
}
