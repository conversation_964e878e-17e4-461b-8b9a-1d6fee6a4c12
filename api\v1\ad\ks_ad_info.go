// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-13 16:04:03
// 生成路径: api/v1/ad/ks_ad_info.go
// 生成人：cyao
// desc:快手账号管理相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdInfoSearchReq 分页请求参数
type KsAdInfoSearchReq struct {
	g.Meta `path:"/list" tags:"快手账号管理" method:"get" summary:"快手账号管理列表"`
	commonApi.Author
	model.KsAdInfoSearchReq
}

// KsAdInfoSearchRes 列表返回结果
type KsAdInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdInfoSearchRes
}

// KsAdInfoAddReq 添加操作请求参数
type KsAdInfoAddReq struct {
	g.Meta `path:"/add" tags:"快手账号管理" method:"post" summary:"快手账号管理添加"`
	commonApi.Author
	*model.KsAdInfoAddReq
}

// KsAdInfoAddRes 添加操作返回结果
type KsAdInfoAddRes struct {
	commonApi.EmptyRes
}

// KsAdInfoEditReq 修改操作请求参数
type KsAdInfoEditReq struct {
	g.Meta `path:"/edit" tags:"快手账号管理" method:"put" summary:"快手账号管理修改"`
	commonApi.Author
	*model.KsAdInfoEditReq
}

// KsAdInfoEditRes 修改操作返回结果
type KsAdInfoEditRes struct {
	commonApi.EmptyRes
}

// KsAdInfoGetReq 获取一条数据请求
type KsAdInfoGetReq struct {
	g.Meta `path:"/get" tags:"快手账号管理" method:"get" summary:"获取快手账号管理信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// KsAdInfoGetRes 获取一条数据结果
type KsAdInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdInfoInfoRes
}

// KsAdInfoDeleteReq 删除数据请求
type KsAdInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手账号管理" method:"delete" summary:"删除快手账号管理"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// KsAdInfoDeleteRes 删除数据返回
type KsAdInfoDeleteRes struct {
	commonApi.EmptyRes
}
