// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-11-13 10:02:38
// 生成路径: api/v1/oceanengine/ad_advertiser_account.go
// 生成人：cq
// desc:广告主账户表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAdvertiserAccountSearchReq 分页请求参数
type AdAdvertiserAccountSearchReq struct {
	g.Meta `path:"/list" tags:"广告主账户表" method:"post" summary:"广告主账户表列表"`
	commonApi.Author
	model.AdAdvertiserAccountSearchReq
}

// AdAdvertiserAccountSearchRes 列表返回结果
type AdAdvertiserAccountSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountSearchRes
}

type CheckAdvertiserAccountAvatarReq struct {
	g.Meta `path:"/check/avatar" tags:"广告主账户表" method:"post" summary:"检测广告账户头像"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds"  dc:"账户IDs"`
}

type CheckAdvertiserAccountRes struct {
	g.Meta                `mime:"application/json"`
	NoPassAdvertiserIds   []string                          `p:"noPassAdvertiserIds"  dc:"没有通过检测的广告账户IDs"`
	AdvertiserAccountList []model.CheckAdvertiserAccountRes `p:"advertiserAccountList"  dc:"通过的头像"`
}

type UpLoadAdvertiserAccountAvatarReq struct {
	g.Meta `path:"/upload/avatar" tags:"广告主账户表" method:"post" summary:"上传广告账户头像"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds"  dc:"账户IDs"`
	ImgUrl        string   `p:"imgUrl"  dc:"imgUrl"`
	//	fileName
	FileName string `p:"fileName"  dc:"fileName"`
}

type UpLoadAdvertiserAccountAvatarRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountAddReq 添加操作请求参数
type AdAdvertiserAccountAddReq struct {
	g.Meta `path:"/add" tags:"广告主账户表" method:"post" summary:"广告主账户表添加"`
	commonApi.Author
	*model.AdAdvertiserAccountAddReq
}

// AdAdvertiserAccountAddRes 添加操作返回结果
type AdAdvertiserAccountAddRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountEditReq 修改操作请求参数
type AdAdvertiserAccountEditReq struct {
	g.Meta `path:"/edit" tags:"广告主账户表" method:"put" summary:"广告主账户表修改"`
	commonApi.Author
	*model.AdAdvertiserAccountEditReq
}

// AdAdvertiserAccountEditRes 修改操作返回结果
type AdAdvertiserAccountEditRes struct {
	commonApi.EmptyRes
}

// AdAdvertiserAccountGetReq 获取一条数据请求
type AdAdvertiserAccountGetReq struct {
	g.Meta `path:"/get" tags:"广告主账户表" method:"get" summary:"获取广告主账户表信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAdvertiserAccountGetRes 获取一条数据结果
type AdAdvertiserAccountGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAdvertiserAccountInfoRes
}

// AdAdvertiserAccountDeleteReq 删除数据请求
type AdAdvertiserAccountDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告主账户表" method:"delete" summary:"删除广告主账户表"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAdvertiserAccountDeleteRes 删除数据返回
type AdAdvertiserAccountDeleteRes struct {
	commonApi.EmptyRes
}

// GetAdCompanyListReq 获取账户主体请求
type GetAdCompanyListReq struct {
	g.Meta `path:"/getCompanyList" tags:"广告主账户表" method:"post" summary:"获取账户主体"`
	commonApi.Author
	comModel.PageReq
	Company string `p:"company"`
}

// GetAdCompanyListRes 获取账户主体返回
type GetAdCompanyListRes struct {
	commonApi.EmptyRes
	*model.GetCompanyListRes
}

// RunSyncAllAdvertiserInfoTaskReq 同步巨量项目和广告计划请求
type RunSyncAllAdvertiserInfoTaskReq struct {
	g.Meta `path:"/sync/task" tags:"广告主账户表" method:"post" summary:"同步巨量广告主/项目/计划"`
	commonApi.Author
	*model.SyncAllAdvertiserInfoReq
}

// RunSyncAllAdvertiserInfoTaskRes 同步巨量项目和广告计划返回
type RunSyncAllAdvertiserInfoTaskRes struct {
	commonApi.EmptyRes
}
