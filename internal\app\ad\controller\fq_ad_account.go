// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-16 11:16:17
// 生成路径: internal/app/ad/controller/fq_ad_account.go
// 生成人：gfast
// desc:番茄账号
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdAccountController struct {
	systemController.BaseController
}

var FqAdAccount = new(fqAdAccountController)

// List 列表
func (c *fqAdAccountController) List(ctx context.Context, req *ad.FqAdAccountSearchReq) (res *ad.FqAdAccountSearchRes, err error) {
	res = new(ad.FqAdAccountSearchRes)
	res.FqAdAccountSearchRes, err = service.FqAdAccount().List(ctx, &req.FqAdAccountSearchReq)
	return
}

// AddAuth
func (c *fqAdAccountController) AddAuth(ctx context.Context, req *ad.AddAuthReq) (res *ad.FqAdAccountAddRes, err error) {
	err = service.FqAdAccount().AddAuth(ctx, &req.FqAdAddAuthReq)
	return
}

// SelectList 获取当前账号下番茄账号列表
func (c *fqAdAccountController) SelectList(ctx context.Context, req *ad.FqAdAccountReq) (res *ad.FqAdAccountRes, err error) {
	res = new(ad.FqAdAccountRes)
	res.FqAdAccountSearchRes, err = service.FqAdAccount().List2(ctx)
	return
}

// Export 导出excel
func (c *fqAdAccountController) Export(ctx context.Context, req *ad.FqAdAccountExportReq) (res *ad.FqAdAccountExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdAccountInfoRes
		//表头
		tableHead = []interface{}{"渠道ID", "秘钥", "账号名", "短剧类型", "创建时间", "更新时间", "删除时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listData, err = service.FqAdAccount().GetExportData(ctx, &req.FqAdAccountSearchReq)
		if err != nil {
			return
		}
		if listData == nil {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.DistributorId,
				v.SecretKey,
				v.AccountName,
				v.DramaType,
				v.CreatedAt.Format("Y-m-d H:i:s"),
				v.UpdatedAt.Format("Y-m-d H:i:s"),
				v.DeletedAt.Format("Y-m-d H:i:s"),
			}
			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("番茄账号")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取番茄账号
func (c *fqAdAccountController) Get(ctx context.Context, req *ad.FqAdAccountGetReq) (res *ad.FqAdAccountGetRes, err error) {
	res = new(ad.FqAdAccountGetRes)
	res.FqAdAccountInfoRes, err = service.FqAdAccount().GetByDistributorId(ctx, req.DistributorId)
	return
}

// Add 添加番茄账号
func (c *fqAdAccountController) Add(ctx context.Context, req *ad.FqAdAccountAddReq) (res *ad.FqAdAccountAddRes, err error) {
	err = service.FqAdAccount().Add(ctx, req.FqAdAccountAddReq)
	return
}

// Edit 修改番茄账号
func (c *fqAdAccountController) Edit(ctx context.Context, req *ad.FqAdAccountEditReq) (res *ad.FqAdAccountEditRes, err error) {
	err = service.FqAdAccount().Edit(ctx, req.FqAdAccountEditReq)
	return
}

// Delete 删除番茄账号
func (c *fqAdAccountController) Delete(ctx context.Context, req *ad.FqAdAccountDeleteReq) (res *ad.FqAdAccountDeleteRes, err error) {
	err = service.FqAdAccount().Delete(ctx, req.DistributorIds)
	return
}
