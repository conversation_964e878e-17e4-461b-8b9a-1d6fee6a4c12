// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-03-06 11:01:29
// 生成路径: api/v1/ad/ks_ad_report_stats.go
// 生成人：cyao
// desc:短剧广告报表明细表相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

type GetAdDataReq struct {
	g.Meta `path:"/get/ad/data" tags:"短剧广告报表明细表" method:"get" summary:"获取短剧广告报表信息"`
	commonApi.Author
	api.QueryAdDataReq
}

type GetAdDataRes struct {
	g.Meta `mime:"application/json"`
	Data   *api.SeriesAdPageDataSnake `json:"data"`
}

type GetKsReportStatsReq struct {
	g.Meta `path:"/get/report/stats" tags:"短剧广告报表明细表" method:"get" summary:"获取短剧广告报表明细表信息"`
	commonApi.Author
	AdvertiserId int64  `p:"advertiserId" v:"required#主键必须"` //通过主键获取
	AppId        int64  `p:"appId" v:"required#主键必须"`
	StatTime     string `p:"statTime" v:"required#日期必须"`
}

type GetKsReportStatsRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// 刷数据
type KsAdReportStatsSyncReq struct {
	g.Meta `path:"/sync" tags:"短剧广告报表明细表" method:"get" summary:"短剧广告报表明细表同步"`
	commonApi.Author
	StartTime string `p:"startTime" v:"required#日期必须"`
	EndTime   string `p:"endTime" v:"required#日期必须"`
}

type KsAdReportStatsSyncRes struct {
	g.Meta `mime:"application/json"`
	Count  int `json:"count"` // 同步数据数量
}

// KsAdReportStatsExportReq 导出请求
type KsAdReportStatsExportReq struct {
	g.Meta `path:"/export" tags:"短剧广告报表明细表" method:"get" summary:"短剧广告报表明细表导出"`
	commonApi.Author
	model.KsAdReportStatsSearchReq
}

// KsAdReportStatsExportRes 导出响应
type KsAdReportStatsExportRes struct {
	commonApi.EmptyRes
}

// KsAdReportStatsSearchReq 分页请求参数
type KsAdReportStatsSearchReq struct {
	g.Meta `path:"/list" tags:"短剧广告报表明细表" method:"post" summary:"短剧广告报表明细表列表"`
	commonApi.Author
	model.KsAdReportStatsSearchReq
}

// KsAdReportStatsSearchRes 列表返回结果
type KsAdReportStatsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdReportStatsSearchRes
}

// KsAdReportStatsAddReq 添加操作请求参数
type KsAdReportStatsAddReq struct {
	g.Meta `path:"/add" tags:"短剧广告报表明细表" method:"post" summary:"短剧广告报表明细表添加"`
	commonApi.Author
	*model.KsAdReportStatsAddReq
}

// KsAdReportStatsAddRes 添加操作返回结果
type KsAdReportStatsAddRes struct {
	commonApi.EmptyRes
}

// KsAdReportStatsEditReq 修改操作请求参数
type KsAdReportStatsEditReq struct {
	g.Meta `path:"/edit" tags:"短剧广告报表明细表" method:"put" summary:"短剧广告报表明细表修改"`
	commonApi.Author
	*model.KsAdReportStatsEditReq
}

// KsAdReportStatsEditRes 修改操作返回结果
type KsAdReportStatsEditRes struct {
	commonApi.EmptyRes
}

// KsAdReportStatsGetReq 获取一条数据请求
type KsAdReportStatsGetReq struct {
	g.Meta `path:"/get" tags:"短剧广告报表明细表" method:"get" summary:"获取短剧广告报表明细表信息"`
	commonApi.Author
	AdvertiserId int64 `p:"advertiserId" v:"required#主键必须"` //通过主键获取
}

// KsAdReportStatsGetRes 获取一条数据结果
type KsAdReportStatsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdReportStatsInfoRes
}

// KsAdReportStatsDeleteReq 删除数据请求
type KsAdReportStatsDeleteReq struct {
	g.Meta `path:"/delete" tags:"短剧广告报表明细表" method:"delete" summary:"删除短剧广告报表明细表"`
	commonApi.Author
	AdvertiserIds []int64 `p:"advertiserIds" v:"required#主键必须"` //通过主键删除
}

// KsAdReportStatsDeleteRes 删除数据返回
type KsAdReportStatsDeleteRes struct {
	commonApi.EmptyRes
}
