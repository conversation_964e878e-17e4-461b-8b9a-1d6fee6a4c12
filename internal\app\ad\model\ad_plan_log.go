// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2024-12-06 10:32:54
// 生成路径: internal/app/ad/model/ad_plan_log.go
// 生成人：cyao
// desc:广告计划执行日志
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdPlanLogInfoRes is the golang structure for table ad_plan_log.
type AdPlanLogInfoRes struct {
	gmeta.Meta      `orm:"table:ad_plan_log"`
	Id              int         `orm:"id,primary" json:"id" dc:"ID"` // ID
	RuleExecuteId   int         `orm:"rule_execute_id" json:"ruleExecuteId"   dc:"计划执行ID"`
	PlanId          int         `orm:"plan_id" json:"planId" dc:"计划ID"`                                                                         // 计划ID
	RuleName        string      `orm:"rule_name" json:"ruleName" dc:"计划规则名称"`                                                                   // 计划规则名称
	MediaType       int         `orm:"media_type" json:"mediaType" dc:"媒体类型，1 巨量，2"`                                                            // 媒体类型，1 巨量，2
	ObjectType      int         `orm:"object_type" json:"objectType" dc:"对象类型，1 账户，2 项目，3 广告"`                                                  // 对象类型，1 账户，2 项目，3 广告
	ScopeType       int         `orm:"scope_type" json:"scopeType" dc:"范围，1 所有，2 指定范围"`                                                         // 范围，1 所有，2 指定范围
	ScopeObjectType int         `orm:"scope_object_type" json:"scopeObjectType" dc:"指定范围类型，仅当 scope_type 为 2 时才不为空，1 账户，2 项目，3 广告"`             // 指定范围类型，仅当 scope_type 为 2 时才不为空，1 账户，2 项目，3 广告
	ScopeEntityId   int64       `orm:"scope_entity_id" json:"scopeEntityId" dc:"（和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空"` // （和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空
	Conditions      string      `orm:"conditions" json:"conditions" dc:"满足条件"`                                                                  // 满足条件
	Content         string      `orm:"content" json:"content" dc:"执行操作的内容"`                                                                     // 执行操作的内容
	ExecutionState  int         `orm:"execution_state" json:"executionState" dc:"执行状态，1 成功，2 失败"`                                               // 执行状态，1 成功，2 失败
	CreatedAt       *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                   // 创建时间
	UpdatedAt       *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                   // 更新时间
}

type AdPlanLogListRes struct {
	Id              int         `json:"id" dc:"ID"`
	PlanId          int         `json:"planId" dc:"计划ID"`
	RuleExecuteId   int         `p:"ruleExecuteId"   dc:"计划执行ID"`
	RuleName        string      `json:"ruleName" dc:"计划规则名称"`
	MediaType       int         `json:"mediaType" dc:"媒体类型，1 巨量，2"`
	ObjectType      int         `json:"objectType" dc:"对象类型，1 账户，2 项目，3 广告"`
	ScopeType       int         `json:"scopeType" dc:"范围，1 所有，2 指定范围"`
	ScopeEntityId   int64       `json:"scopeEntityId" dc:"运行对象Id"`
	ScopeEntityName string      `json:"scopeEntityName" dc:"运行对象的名称"`
	Conditions      string      `json:"conditions" dc:"满足条件"`
	Content         string      `json:"content" dc:"执行操作的内容"`
	ExecutionState  int         `json:"executionState" dc:"执行状态，1 成功，2 失败"`
	CreatedAt       *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdPlanLogSearchReq 分页请求参数
type AdPlanLogSearchReq struct {
	comModel.PageReq
	RuleExecuteId  string `p:"ruleExecuteId"   dc:"计划执行ID"`
	PlanId         string `p:"planId" v:"planId@integer#计划ID需为整数" dc:"计划ID"`                                       //计划ID
	RuleName       string `p:"ruleName" dc:"计划规则名称"`                                                               //计划规则名称
	MediaType      string `p:"mediaType" v:"mediaType@integer#媒体类型，1 巨量，2需为整数" dc:"媒体类型，1 巨量，2"`                   //媒体类型，1 巨量，2
	ObjectType     string `p:"objectType" v:"objectType@integer#对象类型，1 账户，2 项目，3 广告需为整数" dc:"对象类型，1 账户，2 项目，3 广告"` //对象类型，1 账户，2 项目，3 广告
	ScopeType      string `p:"scopeType" v:"scopeType@integer#范围，1 所有，2 指定范围需为整数" dc:"范围，1 所有，2 指定范围"`             //范围，1 所有，2 指定范围
	ScopeEntityId  string `p:"scopeEntityId" v:"scopeEntityId@integer# 运行对象Id" dc:"运行对象Id"`                        //（和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空
	ExecutionState string `p:"executionState" v:"executionState@integer#执行状态，1 成功，2 失败需为整数" dc:"执行状态，1 成功，2 失败"`   //执行状态，1 成功，2 失败
	StartTime      string `p:"startTime" dc:"开始时间 YYYY-MM-DD格式"`
	EndTime        string `p:"endTime" dc:"结束时间 YYYY-MM-DD格式"`
}

// AdPlanLogSearchRes 列表返回结果
type AdPlanLogSearchRes struct {
	comModel.ListRes
	List []*AdPlanLogListRes `json:"list"`
}

// AdPlanLogAddReq 添加操作请求参数
type AdPlanLogAddReq struct {
	PlanId         int    `p:"planId" v:"required#计划ID不能为空" dc:"计划ID"`
	RuleExecuteId  int    `p:"ruleExecuteId"   dc:"计划执行ID"`
	RuleName       string `p:"ruleName" v:"required#计划规则名称不能为空" dc:"计划规则名称"`
	MediaType      int    `p:"mediaType" v:"required#媒体类型，1 巨量，2不能为空" dc:"媒体类型，1 巨量，2"`
	ObjectType     int    `p:"objectType" v:"required#对象类型，1 账户，2 项目，3 广告不能为空" dc:"对象类型，1 账户，2 项目，3 广告"`
	ScopeType      int    `p:"scopeType" v:"required#范围，1 所有，2 指定范围不能为空" dc:"范围，1 所有，2 指定范围"`
	ScopeEntityId  int64  `p:"scopeEntityId" v:"required#（和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空不能为空" dc:"（和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空"`
	Conditions     string `p:"conditions"  dc:"满足条件"`
	Content        string `p:"content"  dc:"执行操作的内容"`
	ExecutionState int    `p:"executionState" v:"required#执行状态，1 成功，2 失败不能为空" dc:"执行状态，1 成功，2 失败"`
}

// AdPlanLogEditReq 修改操作请求参数
type AdPlanLogEditReq struct {
	Id             int    `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	RuleExecuteId  int    ` p:"ruleExecuteId"   dc:"计划执行ID"`
	PlanId         int    `p:"planId" v:"required#计划ID不能为空" dc:"计划ID"`
	RuleName       string `p:"ruleName" v:"required#计划规则名称不能为空" dc:"计划规则名称"`
	MediaType      int    `p:"mediaType" v:"required#媒体类型，1 巨量，2不能为空" dc:"媒体类型，1 巨量，2"`
	ObjectType     int    `p:"objectType" v:"required#对象类型，1 账户，2 项目，3 广告不能为空" dc:"对象类型，1 账户，2 项目，3 广告"`
	ScopeType      int    `p:"scopeType" v:"required#范围，1 所有，2 指定范围不能为空" dc:"范围，1 所有，2 指定范围"`
	ScopeEntityId  int64  `p:"scopeEntityId" v:"required#（和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空不能为空" dc:"（和 object_type 匹配，不同对象对应不同的 ID）指定范围类型，仅当 scope_type 为 2 时才不为空"`
	Conditions     string `p:"conditions"  dc:"满足条件"`
	Content        string `p:"content"  dc:"执行操作的内容"`
	ExecutionState int    `p:"executionState" v:"required#执行状态，1 成功，2 失败不能为空" dc:"执行状态，1 成功，2 失败"`
}
