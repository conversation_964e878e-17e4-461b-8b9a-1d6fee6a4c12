/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
)

// DpaClueProductDetailV2ApiService DpaClueProductDetailV2Api service
type DpaClueProductDetailV2ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *DpaClueProductDetailV2Request
}

type DpaClueProductDetailV2Request struct {
	AdvertiserId *int64
	ProductIds   []int64
}

func (r *DpaClueProductDetailV2ApiService) SetCfg(cfg *conf.Configuration) *DpaClueProductDetailV2ApiService {
	r.cfg = cfg
	return r
}

func (r *DpaClueProductDetailV2ApiService) DpaClueProductDetailV2Request(dpaClueProductDetailV2Request DpaClueProductDetailV2Request) *DpaClueProductDetailV2ApiService {
	r.Request = &dpaClueProductDetailV2Request
	return r
}

func (r *DpaClueProductDetailV2ApiService) AccessToken(accessToken string) *DpaClueProductDetailV2ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *DpaClueProductDetailV2ApiService) Do() (data *models.DpaClueProductDetailV2Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/2/dpa/clue_product/detail/"
	localVarQueryParams := map[string]string{}
	if r.Request.AdvertiserId == nil {
		return nil, errors.New("advertiserId is required and must be specified")
	}
	if r.Request.ProductIds == nil {
		return nil, errors.New("productIds is required and must be specified")
	}
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "advertiser_id", r.Request.AdvertiserId)
	libUtils.ParameterAddToHeaderOrQuery(localVarQueryParams, "product_ids", r.Request.ProductIds)
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetQueryParams(localVarQueryParams).
		SetResult(&models.DpaClueProductDetailV2Response{}).
		Get(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.DpaClueProductDetailV2Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/2/dpa/clue_product/detail/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
