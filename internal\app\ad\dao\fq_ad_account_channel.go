// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-05-07 18:02:16
// 生成路径: internal/app/ad/dao/fq_ad_account_channel.go
// 生成人：cq
// desc:番茄渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// fqAdAccountChannelDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type fqAdAccountChannelDao struct {
	*internal.FqAdAccountChannelDao
}

var (
	// FqAdAccountChannel is globally public accessible object for table tools_gen_table operations.
	FqAdAccountChannel = fqAdAccountChannelDao{
		internal.NewFqAdAccountChannelDao(),
	}
)

// Fill with you ideas below.
