// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2024-03-07 15:30:59
// 生成路径: internal/app/applet/dao/s_customer_qr_code_config.go
// 生成人：cyao
// desc:微信二维码配置
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/applet/dao/internal"
)

// sCustomerQrCodeConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type sCustomerQrCodeConfigDao struct {
	*internal.SCustomerQrCodeConfigDao
}

var (
	// SCustomerQrCodeConfig is globally public accessible object for table tools_gen_table operations.
	SCustomerQrCodeConfig = sCustomerQrCodeConfigDao{
		internal.NewSCustomerQrCodeConfigDao(),
	}
)

// Fill with you ideas below.
