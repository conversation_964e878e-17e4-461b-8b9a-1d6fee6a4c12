// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-03-11 14:20:45
// 生成路径: internal/app/ad/dao/ks_ad_saler_copy_right.go
// 生成人：cq
// desc:快手版权商短剧分销数据
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// ksAdSalerCopyRightDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ksAdSalerCopyRightDao struct {
	*internal.KsAdSalerCopyRightDao
}

var (
	// KsAdSalerCopyRight is globally public accessible object for table tools_gen_table operations.
	KsAdSalerCopyRight = ksAdSalerCopyRightDao{
		internal.NewKsAdSalerCopyRightDao(),
	}
)

// Fill with you ideas below.
