// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: internal/app/ad/logic/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdPlanExecute(New())
}

func New() service.IAdPlanExecute {
	return &sAdPlanExecute{}
}

type sAdPlanExecute struct{}

func (s *sAdPlanExecute) List(ctx context.Context, req *model.AdPlanExecuteSearchReq) (listRes *model.AdPlanExecuteSearchRes, err error) {
	listRes = new(model.AdPlanExecuteSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdPlanExecute.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdPlanExecute.Columns().Id+" = ?", req.Id)
		}
		if req.PlanId != "" {
			m = m.Where(dao.AdPlanExecute.Columns().PlanId+" = ?", gconv.Int(req.PlanId))
		}
		if req.ExecuteType != "" {
			m = m.Where(dao.AdPlanExecute.Columns().ExecuteType+" = ?", gconv.Int(req.ExecuteType))
		}
		if req.AdjustmentType != "" {
			m = m.Where(dao.AdPlanExecute.Columns().AdjustmentType+" = ?", gconv.Int(req.AdjustmentType))
		}
		if req.ExecuteValue != "" {
			m = m.Where(dao.AdPlanExecute.Columns().ExecuteValue+" = ?", gconv.Int(req.ExecuteValue))
		}
		if req.LimitValue != "" {
			m = m.Where(dao.AdPlanExecute.Columns().LimitValue+" = ?", gconv.Int(req.LimitValue))
		}
		if req.TimeDimension != "" {
			m = m.Where(dao.AdPlanExecute.Columns().TimeDimension+" = ?", gconv.Int(req.TimeDimension))
		}
		if req.ExecuteTimes != "" {
			m = m.Where(dao.AdPlanExecute.Columns().ExecuteTimes+" = ?", gconv.Int(req.ExecuteTimes))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdPlanExecute.Columns().CreatedAt+" >=? AND "+dao.AdPlanExecute.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdPlanExecuteListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdPlanExecuteListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdPlanExecuteListRes{
				Id:             v.Id,
				PlanId:         v.PlanId,
				ExecuteType:    v.ExecuteType,
				AdjustmentType: v.AdjustmentType,
				ExecuteValue:   v.ExecuteValue,
				ValueType:      v.ValueType,
				LimitValue:     v.LimitValue,
				TimeDimension:  v.TimeDimension,
				ExecuteTimes:   v.ExecuteTimes,
				CreatedAt:      v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdPlanExecute) GetById(ctx context.Context, id int) (res *model.AdPlanExecuteInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanExecute.Ctx(ctx).WithAll().Where(dao.AdPlanExecute.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanExecute) GetByPlanId(ctx context.Context, planId int) (res *model.AdPlanExecuteInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanExecute.Ctx(ctx).WithAll().Where(dao.AdPlanExecute.Columns().PlanId, planId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanExecute) GetByPlanIds(ctx context.Context, planIds []int) (res []*model.AdPlanExecuteInfoRes, err error) {
	res = make([]*model.AdPlanExecuteInfoRes, 0)
	if planIds == nil || len(planIds) == 0 {
		return
	}
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdPlanExecute.Ctx(ctx).WithAll().WhereIn(dao.AdPlanExecute.Columns().PlanId, planIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdPlanExecute) Add(ctx context.Context, req *model.AdPlanExecuteAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanExecute.Ctx(ctx).Insert(do.AdPlanExecute{
			PlanId:         req.PlanId,
			ExecuteType:    req.ExecuteType,
			AdjustmentType: req.AdjustmentType,
			ExecuteValue:   req.ExecuteValue,
			ValueType:      req.ValueType,
			LimitValue:     req.LimitValue,
			TimeDimension:  req.TimeDimension,
			ExecuteTimes:   req.ExecuteTimes,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdPlanExecute) Edit(ctx context.Context, req *model.AdPlanExecuteEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanExecute.Ctx(ctx).WherePri(req.Id).Update(do.AdPlanExecute{
			PlanId:         req.PlanId,
			ExecuteType:    req.ExecuteType,
			AdjustmentType: req.AdjustmentType,
			ExecuteValue:   req.ExecuteValue,
			ValueType:      req.ValueType,
			LimitValue:     req.LimitValue,
			TimeDimension:  req.TimeDimension,
			ExecuteTimes:   req.ExecuteTimes,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdPlanExecute) Delete(ctx context.Context, ids []int) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdPlanExecute.Ctx(ctx).Delete(dao.AdPlanExecute.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
