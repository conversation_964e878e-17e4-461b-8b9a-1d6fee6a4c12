// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-07-08 14:56:11
// 生成路径: internal/app/ad/controller/dz_task.go
// 生成人：cyao
// desc:记录任务日志
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type dzTaskController struct {
	systemController.BaseController
}

var DzTask = new(dzTaskController)

// List 列表
func (c *dzTaskController) List(ctx context.Context, req *ad.DzTaskSearchReq) (res *ad.DzTaskSearchRes, err error) {
	res = new(ad.DzTaskSearchRes)
	res.DzTaskSearchRes, err = service.DzTask().List(ctx, &req.DzTaskSearchReq)
	return
}

// Get 获取记录任务日志
func (c *dzTaskController) Get(ctx context.Context, req *ad.DzTaskGetReq) (res *ad.DzTaskGetRes, err error) {
	res = new(ad.DzTaskGetRes)
	res.DzTaskInfoRes, err = service.DzTask().GetByTaskId(ctx, req.TaskId)
	return
}

// Add 添加记录任务日志
func (c *dzTaskController) Add(ctx context.Context, req *ad.DzTaskAddReq) (res *ad.DzTaskAddRes, err error) {
	err = service.DzTask().Add(ctx, req.DzTaskAddReq)
	return
}

// Edit 修改记录任务日志
func (c *dzTaskController) Edit(ctx context.Context, req *ad.DzTaskEditReq) (res *ad.DzTaskEditRes, err error) {
	err = service.DzTask().Edit(ctx, req.DzTaskEditReq)
	return
}

// Delete 删除记录任务日志
func (c *dzTaskController) Delete(ctx context.Context, req *ad.DzTaskDeleteReq) (res *ad.DzTaskDeleteRes, err error) {
	err = service.DzTask().Delete(ctx, req.TaskIds)
	return
}
