// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2024-11-27 11:18:54
// 生成路径: internal/app/ad/dao/internal/ad_plan_execute.go
// 生成人：cq
// desc:广告计划执行配置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdPlanExecuteDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdPlanExecuteDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns AdPlanExecuteColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdPlanExecuteColumns defines and stores column names for table ad_plan_execute.
type AdPlanExecuteColumns struct {
	Id             string // ID
	PlanId         string // 计划ID
	ExecuteType    string // 执行类型，1 更新预算，2 更新出价（广告），3 暂停计划
	AdjustmentType string // 调整方式，1 调整至目标值，2 增加，3 减少
	ExecuteValue   string // 调整的目标值，增加或者减少的值
	LimitValue     string // 最低/最高调整至
	TimeDimension  string // 时间维度，1 天
	ExecuteTimes   string // 执行次数，1、2、3、4 等
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
}

var adPlanExecuteColumns = AdPlanExecuteColumns{
	Id:             "id",
	PlanId:         "plan_id",
	ExecuteType:    "execute_type",
	AdjustmentType: "adjustment_type",
	ExecuteValue:   "execute_value",
	LimitValue:     "limit_value",
	TimeDimension:  "time_dimension",
	ExecuteTimes:   "execute_times",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
}

// NewAdPlanExecuteDao creates and returns a new DAO object for table data access.
func NewAdPlanExecuteDao() *AdPlanExecuteDao {
	return &AdPlanExecuteDao{
		group:   "default",
		table:   "ad_plan_execute",
		columns: adPlanExecuteColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdPlanExecuteDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdPlanExecuteDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdPlanExecuteDao) Columns() AdPlanExecuteColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdPlanExecuteDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdPlanExecuteDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdPlanExecuteDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
