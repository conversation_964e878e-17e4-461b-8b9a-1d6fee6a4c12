// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-03-11 15:06:51
// 生成路径: api/v1/channel/recharge_template.go
// 生成人：lx
// desc:充值模板相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package channel

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/channel/model"
)

// RechargeTemplateSearchReq 分页请求参数
type RechargeTemplateSearchReq struct {
	g.Meta `path:"/list" tags:"小程序设置" method:"post" summary:"充值模板列表"`
	commonApi.Author
	model.RechargeTemplateSearchReq
}

// RechargeTemplateSearchRes 列表返回结果
type RechargeTemplateSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.RechargeTemplateSearchRes
}

// RechargeTemplateAddReq 添加操作请求参数
type RechargeTemplateAddReq struct {
	g.Meta `path:"/add" tags:"充值模板" method:"post" summary:"充值模板添加"`
	commonApi.Author
	*model.RechargeTemplateAddReq
}

// RechargeTemplateAddRes 添加操作返回结果
type RechargeTemplateAddRes struct {
	commonApi.EmptyRes
}

// RechargeTemplateEditReq 修改操作请求参数
type RechargeTemplateEditReq struct {
	g.Meta `path:"/edit" tags:"充值模板" method:"put" summary:"充值模板修改"`
	commonApi.Author
	*model.RechargeTemplateEditReq
}

// RechargeTemplateEditRes 修改操作返回结果
type RechargeTemplateEditRes struct {
	commonApi.EmptyRes
}

// RechargeTemplateGetReq 获取一条数据请求
type RechargeTemplateGetReq struct {
	g.Meta `path:"/get" tags:"充值模板" method:"get" summary:"获取充值模板信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// RechargeTemplateGetRes 获取一条数据结果
type RechargeTemplateGetRes struct {
	g.Meta `mime:"application/json"`
	*model.RechargeTemplateInfoRes
}

// RechargeTemplateDeleteReq 删除数据请求
type RechargeTemplateDeleteReq struct {
	g.Meta `path:"/delete" tags:"小程序设置" method:"post" summary:"删除充值模板"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键删除
}

// RechargeTemplateDeleteRes 删除数据返回
type RechargeTemplateDeleteRes struct {
	commonApi.EmptyRes
}

// RechargeTemplateCopyReq 复制充值模板请求
type RechargeTemplateCopyReq struct {
	g.Meta `path:"/copy" tags:"小程序设置" method:"post" summary:"复制充值模板"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"`
}

// RechargeTemplateCopyRes 复制充值模板返回
type RechargeTemplateCopyRes struct {
	commonApi.EmptyRes
}

// RechargeTemplateShareReq 共享充值模板请求
type RechargeTemplateShareReq struct {
	g.Meta `path:"/share" tags:"小程序设置" method:"post" summary:"共享充值模板"`
	commonApi.Author
	Id      int   `p:"id" v:"required#主键必须"`
	UserIds []int `p:"userIds" dc:"共享用户ID列表"`
}

// RechargeTemplateShareRes 共享充值模板返回
type RechargeTemplateShareRes struct {
	commonApi.EmptyRes
}

// RechargeTemplateSetDefaultReq 设置充值模板是否默认请求
type RechargeTemplateSetDefaultReq struct {
	g.Meta `path:"/setDefault" tags:"小程序设置" method:"post" summary:"设置充值模板是否默认"`
	commonApi.Author
	Id        int `p:"id" v:"required#主键必须"`
	IsDefault int `p:"isDefault" v:"required#是否默认必须" dc:"是否默认 0：否 1：是"`
}

// RechargeTemplateSetDefaultRes 设置充值模板是否默认返回
type RechargeTemplateSetDefaultRes struct {
	commonApi.EmptyRes
}

// RechargeTemplateGetDefaultReq 获取默认模板请求
type RechargeTemplateGetDefaultReq struct {
	g.Meta `path:"/getDefault" tags:"小程序设置" method:"post" summary:"获取默认模板"`
	commonApi.Author
}

// RechargeTemplateGetDefaultRes 获取默认模板返回
type RechargeTemplateGetDefaultRes struct {
	commonApi.EmptyRes
	*model.RechargeTemplateInfoRes
}
