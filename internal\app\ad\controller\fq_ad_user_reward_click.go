// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-18 15:23:37
// 生成路径: internal/app/ad/controller/fq_ad_user_reward_click.go
// 生成人：gfast
// desc:番茄用户激励点击记录表
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"

	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/xuri/excelize/v2"
)

type fqAdUserRewardClickController struct {
	systemController.BaseController
}

var FqAdUserRewardClick = new(fqAdUserRewardClickController)

// List 列表
func (c *fqAdUserRewardClickController) List(ctx context.Context, req *ad.FqAdUserRewardClickSearchReq) (res *ad.FqAdUserRewardClickSearchRes, err error) {
	res = new(ad.FqAdUserRewardClickSearchRes)
	res.FqAdUserRewardClickSearchRes, err = service.FqAdUserRewardClick().List(ctx, &req.FqAdUserRewardClickSearchReq)
	return
}

// Export 导出excel
func (c *fqAdUserRewardClickController) Export(ctx context.Context, req *ad.FqAdUserRewardClickExportReq) (res *ad.FqAdUserRewardClickExportRes, err error) {
	var (
		r        = ghttp.RequestFromCtx(ctx)
		listData []*model.FqAdUserRewardClickListRes
		listRes  *model.FqAdUserRewardClickSearchRes
		//表头
		//fu.ip , fu.clickid, fu.oaid,fu.adid, fu.caid ,fu.optimizer_account,fu.external_id,fu.project_id,fu.ad_id_v2,fu.mid
		tableHead = []interface{}{"日期", "投手上级分销", "番茄账号", "投手名", "渠道号", "快应用/公众号对应distributor_id", "公众号/快应用/小程序id（分销平台id）", "快应用/公众号/小程序名称", "脱敏后的用户设备ID", "推广链id", "激励点击金额", "激励点击的时间戳", "用户染色时间戳", "染色推广链的短剧ID", "染色推广链的短剧名称", "染色推广链短剧性别(0女生、1男生、2无性别)", "染色推广链的短剧类型", "ip", "clickid", "oaid", "caid", "adid", "优化师返回优化师账户邮箱", "企微用户企微id", "巨量2.0广告计划组ID", "巨量2.0广告计划ID", "素材id", "创建时间"}
		excelData [][]interface{}
		//字典选项处理
	)
	req.PageNum = 1
	req.PageSize = 500
	//获取字典数据
	excelData = append(excelData, tableHead)
	for {
		listRes, err = service.FqAdUserRewardClick().List(ctx, &req.FqAdUserRewardClickSearchReq)
		if err != nil {
			return
		}
		listData = listRes.List
		if listData == nil || len(listData) == 0 {
			break
		}
		for _, v := range listData {
			var ()
			dt := []interface{}{
				v.EventTimeDate,
				gconv.String(v.DistributorName),
				gconv.String(v.FqAccount),
				gconv.String(v.UserName),
				gconv.String(v.ChannelCode),
				v.DistributorId,
				v.AppId,
				v.AppName,
				v.DeviceId,
				v.PromotionId,
				v.EcpmCost,
				v.EventTime,
				v.RegisterTime,
				v.BookId,
				v.BookName,
				v.BookGender,
				v.BookCategory,
				v.Ip,
				v.Clickid,
				v.Oaid,
				v.Caid,
				gconv.String(v.Adid),
				v.OptimizerAccount,
				v.ExternalId,
				v.ProjectId,
				v.AdIdV2,
				v.Mid,
				v.CreatedAt.Format("Y-m-d H:i:s"),
			}

			excelData = append(excelData, dt)
		}
		req.PageNum++
	}
	//创建excel处理对象
	excel := new(libUtils.ExcelHelper).CreateFile()
	excel.ArrToExcel("Sheet1", "A1", excelData)
	col, _ := excelize.ColumnNumberToName(len(tableHead))
	row := len(excelData)
	cr, _ := excelize.JoinCellName(col, row)
	excel.SetCellBorder("Sheet1", "A1", cr)
	_, err = excel.WriteTo(r.Response.Writer)
	if err != nil {
		return
	}
	r.Response.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	r.Response.Header().Set("Accept-Ranges", "bytes")
	r.Response.Header().Set("Access-Control-Expose-Headers", "*")
	r.Response.Header().Set("Content-Disposition", "attachment; filename="+gurl.Encode("番茄用户激励点击记录表")+".xlsx")
	r.Response.Buffer()
	r.Exit()
	return
}

// Get 获取番茄用户激励点击记录表
func (c *fqAdUserRewardClickController) Get(ctx context.Context, req *ad.FqAdUserRewardClickGetReq) (res *ad.FqAdUserRewardClickGetRes, err error) {
	res = new(ad.FqAdUserRewardClickGetRes)
	res.FqAdUserRewardClickInfoRes, err = service.FqAdUserRewardClick().GetByEcpmNo(ctx, req.EcpmNo)
	return
}

// Add 添加番茄用户激励点击记录表
func (c *fqAdUserRewardClickController) Add(ctx context.Context, req *ad.FqAdUserRewardClickAddReq) (res *ad.FqAdUserRewardClickAddRes, err error) {
	err = service.FqAdUserRewardClick().Add(ctx, req.FqAdUserRewardClickInfoSaveRes)
	return
}

// Edit 修改番茄用户激励点击记录表
func (c *fqAdUserRewardClickController) Edit(ctx context.Context, req *ad.FqAdUserRewardClickEditReq) (res *ad.FqAdUserRewardClickEditRes, err error) {
	err = service.FqAdUserRewardClick().Edit(ctx, req.FqAdUserRewardClickEditReq)
	return
}

// Delete 删除番茄用户激励点击记录表
func (c *fqAdUserRewardClickController) Delete(ctx context.Context, req *ad.FqAdUserRewardClickDeleteReq) (res *ad.FqAdUserRewardClickDeleteRes, err error) {
	err = service.FqAdUserRewardClick().Delete(ctx, req.EcpmNos)
	return
}
