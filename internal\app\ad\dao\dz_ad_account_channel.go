// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-07-07 15:25:28
// 生成路径: internal/app/ad/dao/dz_ad_account_channel.go
// 生成人：cyao
// desc:点众渠道
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao/internal"
)

// dzAdAccountChannelDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type dzAdAccountChannelDao struct {
	*internal.DzAdAccountChannelDao
}

var (
	// DzAdAccountChannel is globally public accessible object for table tools_gen_table operations.
	DzAdAccountChannel = dzAdAccountChannelDao{
		internal.NewDzAdAccountChannelDao(),
	}
)

// Fill with you ideas below.
