// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-03-20 15:16:47
// 生成路径: internal/app/ad/dao/internal/ad_xt_account.go
// 生成人：cyao
// desc:广告星图账户表格
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdXtAccountDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdXtAccountDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns AdXtAccountColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdXtAccountColumns defines and stores column names for table ad_xt_account.
type AdXtAccountColumns struct {
	Id             string // ID
	AdvertiserId   string // 账户ID 对应 star_id
	AdvertiserNick string // 账户名称
	RoleName       string // 角色枚举 账户角色  PLATFORM_ROLE_STAR 对应星图
	AuthTime       string // 授权时间
	AppId          string // 授权的app_id
	AccessToken    string // 用于验证权限的token
	RefreshToken   string // 刷新access_token，用于获取新的access_token和refresh_token，并且刷新过期时间
	Status         string // unauthorized未授权 authorized 已经授权
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
}

var adXtAccountColumns = AdXtAccountColumns{
	Id:             "id",
	AdvertiserId:   "advertiser_id",
	AdvertiserNick: "advertiser_nick",
	RoleName:       "role_name",
	AuthTime:       "auth_time",
	AppId:          "app_id",
	AccessToken:    "access_token",
	RefreshToken:   "refresh_token",
	Status:         "status",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewAdXtAccountDao creates and returns a new DAO object for table data access.
func NewAdXtAccountDao() *AdXtAccountDao {
	return &AdXtAccountDao{
		group:   "default",
		table:   "ad_xt_account",
		columns: adXtAccountColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdXtAccountDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdXtAccountDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdXtAccountDao) Columns() AdXtAccountColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdXtAccountDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdXtAccountDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdXtAccountDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
