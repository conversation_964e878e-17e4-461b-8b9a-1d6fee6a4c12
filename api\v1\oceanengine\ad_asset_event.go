// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2024-12-16 15:35:59
// 生成路径: api/v1/oceanengine/ad_asset_event.go
// 生成人：cq
// desc:资产-事件相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdAssetEventSearchReq 分页请求参数
type AdAssetEventSearchReq struct {
	g.Meta `path:"/list" tags:"资产-事件" method:"post" summary:"资产-事件列表"`
	commonApi.Author
	model.AdAssetEventSearchReq
}

// AdAssetEventSearchRes 列表返回结果
type AdAssetEventSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetEventSearchRes
}

// AdAssetEventAddReq 添加操作请求参数
type AdAssetEventAddReq struct {
	g.Meta `path:"/add" tags:"资产-事件" method:"post" summary:"资产-事件添加"`
	commonApi.Author
	*model.AdAssetEventAddReq
}

// AdAssetEventAddRes 添加操作返回结果
type AdAssetEventAddRes struct {
	commonApi.EmptyRes
}

// AdAssetEventSyncReq 同步事件请求参数
type AdAssetEventSyncReq struct {
	g.Meta `path:"/sync" tags:"资产-事件" method:"post" summary:"资产-同步事件"`
	commonApi.Author
	AdvertiserIds []string `p:"advertiserIds" dc:"广告主ID列表"`
}

// AdAssetEventSyncRes 同步事件返回结果
type AdAssetEventSyncRes struct {
	commonApi.EmptyRes
}

// AdAssetEventEditReq 修改操作请求参数
type AdAssetEventEditReq struct {
	g.Meta `path:"/edit" tags:"资产-事件" method:"put" summary:"资产-事件修改"`
	commonApi.Author
	*model.AdAssetEventEditReq
}

// AdAssetEventEditRes 修改操作返回结果
type AdAssetEventEditRes struct {
	commonApi.EmptyRes
}

// AdAssetEventGetReq 获取一条数据请求
type AdAssetEventGetReq struct {
	g.Meta `path:"/get" tags:"资产-事件" method:"get" summary:"获取资产-事件信息"`
	commonApi.Author
	Id int `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdAssetEventGetRes 获取一条数据结果
type AdAssetEventGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdAssetEventInfoRes
}

// AdAssetEventDeleteReq 删除数据请求
type AdAssetEventDeleteReq struct {
	g.Meta `path:"/delete" tags:"资产-事件" method:"post" summary:"删除资产-事件"`
	commonApi.Author
	Ids []int `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdAssetEventDeleteRes 删除数据返回
type AdAssetEventDeleteRes struct {
	commonApi.EmptyRes
}
