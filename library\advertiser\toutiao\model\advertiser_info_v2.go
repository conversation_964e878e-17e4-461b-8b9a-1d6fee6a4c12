/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package model

type AdvertiserInfoV2Request struct {
	AdvertiserIds *[]int64
	fields        *[]string
}

// AdvertiserInfoV2Response struct for AdvertiserInfoV2Response
type AdvertiserInfoV2Response struct {
	//
	Code *int64 `json:"code,omitempty"`
	//
	Data []*AdvertiserInfoV2ResponseData `json:"data,omitempty"`
	//
	Message *string `json:"message,omitempty"`
	//
	RequestId *string `json:"request_id,omitempty"`
}

// AdvertiserInfoV2ResponseData struct for AdvertiserInfoV2ResponseData
type AdvertiserInfoV2ResponseData struct {
	//
	Address *string `json:"address,omitempty"`
	//
	Brand *string `json:"brand,omitempty"`
	//
	Company *string `json:"company,omitempty"`
	//
	CreateTime *string `json:"create_time,omitempty"`
	//
	FirstIndustryName *string `json:"first_industry_name,omitempty"`
	//
	Id *int64 `json:"id,omitempty"`
	//
	Industry *string `json:"industry,omitempty"`
	//
	LicenseCity *string `json:"license_city,omitempty"`
	//
	LicenseNo *string `json:"license_no,omitempty"`
	//
	LicenseProvince *string `json:"license_province,omitempty"`
	//
	LicenseUrl *string `json:"license_url,omitempty"`
	//
	Name *string `json:"name,omitempty"`
	//
	Note *string `json:"note,omitempty"`
	//
	PromotionArea *string `json:"promotion_area,omitempty"`
	//
	PromotionCenterCity *string `json:"promotion_center_city,omitempty"`
	//
	PromotionCenterProvince *string `json:"promotion_center_province,omitempty"`
	//
	Reason *string                   `json:"reason,omitempty"`
	Role   *AdvertiserInfoV2DataRole `json:"role,omitempty"`
	//
	SecondIndustryName *string                     `json:"second_industry_name,omitempty"`
	Status             *AdvertiserInfoV2DataStatus `json:"status,omitempty"`
}

// AdvertiserInfoV2DataRole
type AdvertiserInfoV2DataRole string

// OceanEngineCallback of advertiser_info_v2_data_role
const (
	ROLE_DOUDIAN_ADVERTISER_AdvertiserInfoV2DataRole             AdvertiserInfoV2DataRole = "ROLE_DOUDIAN_ADVERTISER"
	ROLE_LOCAL_LIFE_ADVERTISER_AdvertiserInfoV2DataRole          AdvertiserInfoV2DataRole = "ROLE_LOCAL_LIFE_ADVERTISER"
	ROLE_LITE_ADVERTISER_AdvertiserInfoV2DataRole                AdvertiserInfoV2DataRole = "ROLE_LITE_ADVERTISER"
	ROLE_ECP_ADVERTISER_AdvertiserInfoV2DataRole                 AdvertiserInfoV2DataRole = "ROLE_ECP_ADVERTISER"
	ROLE_ECP_INTERNAL_ADVERTISER_AdvertiserInfoV2DataRole        AdvertiserInfoV2DataRole = "ROLE_ECP_INTERNAL_ADVERTISER"
	ROLE_HOTSOON_ADVERTISER_AdvertiserInfoV2DataRole             AdvertiserInfoV2DataRole = "ROLE_HOTSOON_ADVERTISER"
	ROLE_AWEME_PROMOTION_ADVERTISER_AdvertiserInfoV2DataRole     AdvertiserInfoV2DataRole = "ROLE_AWEME_PROMOTION_ADVERTISER"
	ROLE_AGENT_AdvertiserInfoV2DataRole                          AdvertiserInfoV2DataRole = "ROLE_AGENT"
	ROLE_INTERNAL_ADV_AdvertiserInfoV2DataRole                   AdvertiserInfoV2DataRole = "ROLE_INTERNAL_ADV"
	ROLE_ADVERTISER_ABSTRACT_AdvertiserInfoV2DataRole            AdvertiserInfoV2DataRole = "ROLE_ADVERTISER_ABSTRACT"
	ROLE_AGENT_ABSTRACT_AdvertiserInfoV2DataRole                 AdvertiserInfoV2DataRole = "ROLE_AGENT_ABSTRACT"
	ROLE_ECP_CHILD_ADVERTISER_AdvertiserInfoV2DataRole           AdvertiserInfoV2DataRole = "ROLE_ECP_CHILD_ADVERTISER"
	ROLE_LOCAL_LIFE_VIRTUAL_ADVERTISER_AdvertiserInfoV2DataRole  AdvertiserInfoV2DataRole = "ROLE_LOCAL_LIFE_VIRTUAL_ADVERTISER"
	ROLE_PGC_ADVERTISER_AdvertiserInfoV2DataRole                 AdvertiserInfoV2DataRole = "ROLE_PGC_ADVERTISER"
	ROLE_MAJORDOMO_AdvertiserInfoV2DataRole                      AdvertiserInfoV2DataRole = "ROLE_MAJORDOMO"
	ROLE_ADVERTISER_AdvertiserInfoV2DataRole                     AdvertiserInfoV2DataRole = "ROLE_ADVERTISER"
	ROLE_CHILD_AGENT_AdvertiserInfoV2DataRole                    AdvertiserInfoV2DataRole = "ROLE_CHILD_AGENT"
	ROLE_LOCAL_LIFE_INTERNAL_ADVERTISER_AdvertiserInfoV2DataRole AdvertiserInfoV2DataRole = "ROLE_LOCAL_LIFE_INTERNAL_ADVERTISER"
	ROLE_DSP_ADVERTISER_AdvertiserInfoV2DataRole                 AdvertiserInfoV2DataRole = "ROLE_DSP_ADVERTISER"
	ROLE_AGENT_SYSTEM_ACCOUNT_AdvertiserInfoV2DataRole           AdvertiserInfoV2DataRole = "ROLE_AGENT_SYSTEM_ACCOUNT"
	ROLE_ADMIN_AdvertiserInfoV2DataRole                          AdvertiserInfoV2DataRole = "ROLE_ADMIN"
	ROLE_ADVERTISER_SYSTEM_ACCOUNT_AdvertiserInfoV2DataRole      AdvertiserInfoV2DataRole = "ROLE_ADVERTISER_SYSTEM_ACCOUNT"
	ROLE_CHILD_ADVERTISER_AdvertiserInfoV2DataRole               AdvertiserInfoV2DataRole = "ROLE_CHILD_ADVERTISER"
	ROLE_HOTSOON_PROMOTION_ADVERTISER_AdvertiserInfoV2DataRole   AdvertiserInfoV2DataRole = "ROLE_HOTSOON_PROMOTION_ADVERTISER"
)

// Ptr returns reference to advertiser_info_v2_data_role value
func (v AdvertiserInfoV2DataRole) Ptr() *AdvertiserInfoV2DataRole {
	return &v
}

// AdvertiserInfoV2DataStatus
type AdvertiserInfoV2DataStatus string

// List of advertiser_info_v2_data_status
const (
	STATUS_PENDING_CONFIRM_MODIFY_AdvertiserInfoV2DataStatus AdvertiserInfoV2DataStatus = "STATUS_PENDING_CONFIRM_MODIFY"
	STATUS_DISABLE_AdvertiserInfoV2DataStatus                AdvertiserInfoV2DataStatus = "STATUS_DISABLE"
	STATUS_CONFIRM_FAIL_END_AdvertiserInfoV2DataStatus       AdvertiserInfoV2DataStatus = "STATUS_CONFIRM_FAIL_END"
	STATUS_CONFIRM_FAIL_AdvertiserInfoV2DataStatus           AdvertiserInfoV2DataStatus = "STATUS_CONFIRM_FAIL"
	STATUS_PENDING_CONFIRM_AdvertiserInfoV2DataStatus        AdvertiserInfoV2DataStatus = "STATUS_PENDING_CONFIRM"
	STATUS_LIMIT_AdvertiserInfoV2DataStatus                  AdvertiserInfoV2DataStatus = "STATUS_LIMIT"
	STATUS_WAIT_FOR_BPM_AUDIT_AdvertiserInfoV2DataStatus     AdvertiserInfoV2DataStatus = "STATUS_WAIT_FOR_BPM_AUDIT"
	STATUS_SELF_SERVICE_UNAUDITED_AdvertiserInfoV2DataStatus AdvertiserInfoV2DataStatus = "STATUS_SELF_SERVICE_UNAUDITED"
	STATUS_WAIT_FOR_PUBLIC_AUTH_AdvertiserInfoV2DataStatus   AdvertiserInfoV2DataStatus = "STATUS_WAIT_FOR_PUBLIC_AUTH"
	STATUS_PENDING_VERIFIED_AdvertiserInfoV2DataStatus       AdvertiserInfoV2DataStatus = "STATUS_PENDING_VERIFIED"
	STATUS_CONFIRM_MODIFY_FAIL_AdvertiserInfoV2DataStatus    AdvertiserInfoV2DataStatus = "STATUS_CONFIRM_MODIFY_FAIL"
	STATUS_ENABLE_AdvertiserInfoV2DataStatus                 AdvertiserInfoV2DataStatus = "STATUS_ENABLE"
)

// Ptr returns reference to advertiser_info_v2_data_status value
func (v AdvertiserInfoV2DataStatus) Ptr() *AdvertiserInfoV2DataStatus {
	return &v
}
